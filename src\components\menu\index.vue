<script lang="tsx">
    import { defineComponent, ref, h, compile, computed, watch } from 'vue';
    import { useI18n } from 'vue-i18n';
    import { useRoute, useRouter, RouteRecordRaw } from 'vue-router';
    import type { RouteMeta } from 'vue-router';
    import { useAppStore } from '@/store';
    import { listenerRouteChange } from '@/utils/route-listener';
    import { openWindow, regexUrl } from '@/utils';
    import usePermissionStore from '@/store/modules/permission/index'
    import SvgIcon from '@/components/SvgIcon/index.vue'

    export default defineComponent({
        emit: ['collapse'],
        setup() {
            const appStore = useAppStore();
            const router = useRouter();
            const route = useRoute();
            const collapsed = computed({
                get() {
                    if (appStore.device === 'desktop')
                        return appStore.menuCollapse;
                    return false;
                },
                set(value: boolean) {
                    appStore.updateSettings({ menuCollapse: value });
                },
            });
            const activeMenuPath = computed(() => {
                const { activeMenu } = route.meta;
                return activeMenu || route.name;
            });

            const permissionStore = usePermissionStore()
            const sidebarRouters = computed(() => permissionStore.sidebarRouters)

            const topMenu = computed(() => appStore.topMenu);
            const openKeys = ref<string[]>([]);
            const selectedKey = ref<string[]>([]);

            const goto = (item: RouteRecordRaw) => {
                console.log(item)
                //"/workplace"
                // if  (item.path.indexOf("/workplace") > -1) {
                //     router.push({
                //         name: 'WorkplaceIndex',
                //     });
                //     return;
                // }
                // Open external link
                if (regexUrl.test(item.path)) {
                    openWindow(item.path);
                    selectedKey.value = [item.name as string];
                    return;
                }
                // Eliminate external link side effects
                const { hideInMenu, activeMenu } = item.meta as RouteMeta;
                if (route.name === item.name && !hideInMenu && !activeMenu) {
                    selectedKey.value = [item.name as string];
                    return;
                }
                // Trigger router change
                router.push({
                    name: item.name,
                });
            };
            const findMenuOpenKeys = (target: string): string[] => {
                const result: string[] = [];
                let isFind = false;
                const backtrack = (item: RouteRecordRaw, keys: string[]) => {
                    if (item.name === target) {
                        isFind = true;
                        result.push(...keys);
                        return;
                    }
                    if (item.children?.length) {
                        item.children.forEach((el) => {
                            backtrack(el, [...keys, el.name as string]);
                        });
                    }
                };
                sidebarRouters.value.forEach((el: any) => {
                    if (isFind) return; // Performance optimization
                    backtrack(el, [el.name as string]);
                });
                return result;
            };
            listenerRouteChange((newRoute) => {
                const { activeMenu } = newRoute.meta;
                const menuOpenKeys = findMenuOpenKeys(
                    (activeMenu || newRoute.name) as string
                );

                const keySet = new Set([
                    ...menuOpenKeys,
                    ...openKeys.value,
                ]);
                openKeys.value = [...keySet];

                selectedKey.value = [
                    (activeMenu || menuOpenKeys[menuOpenKeys.length - 1]) as string,
                ];
            }, true);
            const setCollapse = (val: boolean) => {
                if (appStore.device === 'desktop')
                    appStore.updateSettings({ menuCollapse: val });
            };

            const renderSubMenu = () => {
                function travel(_route: any[], nodes = []) {
                    if (_route) {
                        _route.forEach((element) => {
                            if(element.meta.hidden) return
                            // This is demo, modify nodes as needed
                            const icon = element?.meta?.icon && element?.meta?.icon !== '#' && element.meta.level === 1
                                ? element?.meta?.icon
                                : null;
                            const node =
                                element?.children &&
                                element?.children.length !== 0 ? (
                                    <a-sub-menu
                                        key={element?.name}
                                        v-slots={{
                                            icon: element.meta.level === 1?()=> {
                                                return (
                                                    <SvgIcon iconClass={icon} style="height: 32px;width: 16px;" />
                                                )
                                            } : null,
                                            title: () =>
                                                h(
                                                    compile(element?.meta?.title || '')
                                                ),
                                        }}
                                    >
                                        {travel(element?.children)}
                                    </a-sub-menu>
                                ) : (
                                    <a-menu-item
                                        key={element?.name}
                                        v-slots={{ icon: element.meta.level === 1? ()=> {
                                                return (
                                                    <SvgIcon iconClass={icon} style="height: 32px;width: 16px;" />
                                                )
                                            }: null }}
                                        onClick={() => goto(element)}
                                    >
                                        {element?.meta?.title || ''}
                                    </a-menu-item>
                                );
                            nodes.push(node as never);
                        });
                    }
                    return nodes;
                }
                return travel(sidebarRouters.value);
            };

            return () => (
                <a-menu
                    mode={topMenu.value ? 'horizontal' : 'vertical'}
                    v-model:collapsed={collapsed.value}
                    v-model:open-keys={openKeys.value}
                    show-collapse-button={appStore.device !== 'mobile'}
                    auto-open={false}
                    selected-keys={[activeMenuPath.value as string]}
                    auto-open-selected={true}
                    level-indent={34}
                    style="height: 100%;width:100%;"
                    onCollapse={setCollapse}
                    theme={appStore.navbarTheme}
                    trigger-props={{
                        theme: appStore.navbarTheme
                    }}
                >
                    {renderSubMenu()}
                </a-menu>
            );
        },
    });
</script>

<style lang="less" scoped>
:deep(.arco-menu-inner) {
    .arco-menu-inline-header {
        display: flex;
        align-items: center;
    }

    .arco-icon {
        &:not(.arco-icon-down) {
            font-size: 18px;
        }
    }
}
</style>
