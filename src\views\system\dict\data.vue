<template>
    <div class="container">
        <a-card class="general-card" :title="'字典数据'">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form :model="searchForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="dictType" label="字典名称">
                                    <a-select v-model="searchForm.dictType" allow-clear placeholder="请选择字典名称">
                                        <a-option v-for="item in typeOptions" :key="item.dictType" :value="item.dictType">
                                            {{ item.dictName }}
                                        </a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="dictLabel" label="字典标签">
                                    <a-input v-model="searchForm.dictLabel" placeholder="请输入字典标签" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="status" label="状态">
                                    <a-select v-model="searchForm.status" placeholder="数据状态" allow-clear>
                                        <a-option value="">全部</a-option>
                                        <a-option value="1">停用</a-option>
                                        <a-option value="0">正常</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="handleSearch">
                            <template #icon><icon-search /></template>
                            搜索
                        </a-button>
                        <a-button @click="handleReset">
                            <template #icon><icon-refresh /></template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin-top: 0" />

            <!-- 操作按钮区域 -->
            <a-row style="margin-bottom: 16px">
                <a-col :span="16">
                    <a-space>
                        <a-button type="primary" @click="handleAdd">
                            <template #icon><icon-plus /></template>
                            新增
                        </a-button>
                        <a-button type="primary" status="warning" @click="handleModify" :disabled="selectedKeys.length !== 1">
                            <template #icon><icon-edit /></template>
                            修改
                        </a-button>
                        <a-button type="primary" status="danger" @click="handleDelete" :disabled="!selectedKeys.length">
                            <template #icon><icon-delete /></template>
                            删除
                        </a-button>
                        <a-button type="primary" @click="handleClose">
                            <template #icon><icon-close /></template>
                            关闭
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>

            <!-- 表格区域 -->
            <a-table
                :columns="columns"
                :data="tableData"
                :pagination="{
                    total: pagination.total,
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    showTotal: true,
                    showPageSize: true
                }"
                :loading="loading"
                :scroll="{x: 1}"
                :row-selection="{
                    type: 'checkbox',
                    showCheckedAll: true,
                    onlyCurrent: false
                }"
                @page-change="onPageChange"
                @page-size-change="onPageSizeChange"
                @selection-change="onSelectionChange"
                row-key="dictCode"
            >
                <template #status="{ record }">
                    <a-tag :color="record.status === '1' ? 'red' : 'green'">
                        {{ record.status === '1' ? '停用' : '正常' }}
                    </a-tag>
                </template>
            </a-table>

            <!-- 新增/修改弹窗 -->
            <a-modal v-model:visible="dialogVisible" :title="dialogTitle" @ok="handleDialogConfirm"
                @cancel="handleDialogCancel">
                <a-form :model="formData" :rules="rules" ref="formRef">
                    <a-form-item field="dictType" label="字典类型">
                        <a-input v-model="formData.dictType" disabled />
                    </a-form-item>
                    <a-form-item field="dictLabel" label="数据标签">
                        <a-input v-model="formData.dictLabel" placeholder="请输入数据标签" />
                    </a-form-item>
                    <a-form-item field="dictValue" label="数据键值">
                        <a-input v-model="formData.dictValue" placeholder="请输入数据键值" />
                    </a-form-item>
                    <a-form-item field="dictSort" label="显示排序">
                        <a-input-number v-model="formData.dictSort" :min="0" />
                    </a-form-item>
                    <a-form-item field="status" label="状态">
                        <a-radio-group v-model="formData.status">
                            <a-radio value="0">正常</a-radio>
                            <a-radio value="1">停用</a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item field="remark" label="备注">
                        <a-textarea v-model="formData.remark" placeholder="请输入备注" />
                    </a-form-item>
                </a-form>
            </a-modal>
        </a-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useRoute, useRouter } from 'vue-router'
import type { TableColumnData } from '@arco-design/web-vue'
import { listData, getData, delData, addData, updateData } from '@/api/system/dict'
import { optionselect as getDictOptionselect, DictType } from '@/api/system/dict'

const route = useRoute()
const router = useRouter()

// 表格列定义
const columns: TableColumnData[] = [
    { title: '字典编码', dataIndex: 'dictCode', width: 100 },
    { title: '字典标签', dataIndex: 'dictLabel', width: 100 },
    { title: '字典键值', dataIndex: 'dictValue', width: 100 },
    { title: '字典排序', dataIndex: 'dictSort', width: 100 },
    { title: '状态', dataIndex: 'status', slotName: 'status', width: 100 },
    { title: '备注', dataIndex: 'remark', width: 100 },
    { title: '创建时间', dataIndex: 'createTime', width: 150 }
]

// 搜索表单
const searchForm = reactive({
    dictType: route.query.dictType as string,
    dictLabel: '',
    status: '',
    pageNum: 1,
    pageSize: 10
})

// 表格数据
const tableData = ref<DictType[]>([])
const loading = ref(false)
const selectedKeys = ref<string[]>([])
const typeOptions = ref<DictType[]>([])
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10
})

// 弹窗相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增字典数据')
const formRef = ref()
const formData = reactive({
    dictCode: undefined,
    dictType: '',
    dictLabel: '',
    dictValue: '',
    dictSort: 0,
    status: '1',
    remark: ''
})

// 表单校验规则
const rules = {
    dictLabel: [{ required: true, message: '请输入字典标签' }],
    dictValue: [{ required: true, message: '请输入字典键值' }]
}

// 获取字典类型选项
const getTypeOptions = async () => {
    const res = await getDictOptionselect()
    typeOptions.value = res.data
}

// 获取数据列表
const fetchData = async () => {
    loading.value = true
    try {
        const res = await listData(searchForm)
        if (res.code === 200) {
            tableData.value = res.rows
            pagination.total = res.total
        }
    } finally {
        loading.value = false
    }
}

// 页面初始化
onMounted(() => {
    console.log("dictType",route.query.dictType)
    getTypeOptions()
    fetchData()
})

// 搜索方法
const handleSearch = () => {
    searchForm.pageNum = 1
    fetchData()
}

// 重置搜索
const handleReset = () => {
    searchForm.dictLabel = ''
    searchForm.status = ''
    handleSearch()
}

// 新增数据
const handleAdd = () => {
    dialogTitle.value = '新增字典数据'
    dialogVisible.value = true
    Object.assign(formData, {
        dictCode: undefined,
        dictType: searchForm.dictType,
        dictLabel: '',
        dictValue: '',
        dictSort: 0,
        status: '0',
        remark: ''
    })
}

// 修改数据
const handleModify = async () => {
    if (selectedKeys.value.length !== 1) {
        Message.warning('请选择一条记录')
        return
    }
    console.log(selectedKeys.value[0])
    const res = await getData(selectedKeys.value[0])
    if (res.code === 200) {
        dialogTitle.value = '修改字典数据'
        dialogVisible.value = true
        Object.assign(formData, res.data)
    }
}

// 删除数据
const handleDelete = async () => {
    if (!selectedKeys.value.length) {
        Message.warning('请选择要删除的记录')
        return
    }
    await delData(Number(selectedKeys.value[0]))
    Message.success('删除成功')
    fetchData()
}

// 关闭页面
const handleClose = () => {
    router.push('/system/dict')
}

// 弹窗确认
const handleDialogConfirm = async () => {
    await formRef.value?.validate()
    if (formData.dictCode) {
        await updateData(formData)
    } else {
        await addData(formData)
    }
    Message.success('保存成功')
    dialogVisible.value = false
    fetchData()
}

// 弹窗取消
const handleDialogCancel = () => {
    dialogVisible.value = false
}

// 表格选择改变
const onSelectionChange = (rowKeys: string[]) => {
    selectedKeys.value = rowKeys
}

// 分页改变
const onPageChange = (current: number) => {
    searchForm.pageNum = current
    fetchData()
}

// 每页条数改变
const onPageSizeChange = (pageSize: number) => {
    searchForm.pageSize = pageSize
    fetchData()
}
</script>
<script lang="ts">
export default { name: 'dictDataRouter' }
</script>

<style scoped>
.container {
    padding: 0 16px 16px 16px;
}
.general-card {
    border-radius: 4px;
}
</style>