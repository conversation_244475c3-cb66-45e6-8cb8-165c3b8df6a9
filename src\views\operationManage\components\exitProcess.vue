<template>
    <div class="exit-process">
        <div class="exit-tips">
            <div class="tip-title">出场提示：</div>
            <div class="tip-item">1、房源可分批次做出场物业交割，但必须所有退租房源统一结算</div>
            <div class="tip-item">2、物业交割单必须商服和物业全都确认，结算单才能发给客户签字</div>
        </div>
        <div class="action-buttons">
            <a-space>
                <a-button 
                    type="primary" 
                    :loading="loading" 
                    @click="handlePropertyOnly"
                >
                    先物业交割，后续结算
                </a-button>
                <a-button 
                    type="primary" 
                    :loading="loading" 
                    @click="handlePropertyAndSettlement"
                >
                    物业交割并结算
                </a-button>
                <a-button @click="handleCancel">取消</a-button>
            </a-space>
        </div>
    </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import { chooseExitProcessType } from '@/api/operationManage'

const props = defineProps<{
    data: {
        id?: string
        [key: string]: any
    }
}>()

const emit = defineEmits(['cancel', 'property-only', 'property-and-settlement', 'refresh'])

const loading = ref(false)

// 调用选择办理类型接口
const handleChooseProcessType = async (processType: number) => {
    if (!props.data?.id) {
        Message.error('出场单ID不能为空')
        return false
    }

    try {
        loading.value = true
        await chooseExitProcessType({
            exitId: props.data.id,
            processType
        })
        // Message.success('办理类型选择成功')
        // 触发刷新列表
        emit('refresh')
        return true
    } catch (error) {
        console.error('选择办理类型失败:', error)
        // Message.error('选择办理类型失败')
        return false
    } finally {
        loading.value = false
    }
}

// 先物业交割，后续结算
const handlePropertyOnly = async () => {
    const success = await handleChooseProcessType(1) // 1-先交割后结算
    if (success) {
        emit('property-only')
    }
}

// 物业交割并结算
const handlePropertyAndSettlement = async () => {
    const success = await handleChooseProcessType(2) // 2-交割并结算
    if (success) {
        emit('property-and-settlement')
    }
}

// 取消
const handleCancel = () => {
    emit('cancel')
}
</script>

<style scoped>
.exit-process {
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 32px;
}

.exit-tips {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.tip-title {
    color: #f53f3f;
    font-weight: bold;
    font-size: 16px;
}

.tip-item {
    font-size: 14px;
    line-height: 1.5;
}

.action-buttons {
    margin-top: 16px;
}
</style> 