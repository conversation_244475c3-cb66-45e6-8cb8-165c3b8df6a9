<template>
  <div class="active-properties">
    <!-- 数据表格 -->
    <a-table 
      :columns="columns" 
      :scroll="{x: 1}" 
      :data="tableData" 
      :pagination="pagination" 
      :bordered="{ cell: true }" 
      :stripe="true"
      :loading="props.loading"
      :row-selection="rowSelection"
      v-model:selectedKeys="selectedKeys"
      row-key="id"
      @page-change="onPageChange" 
      @page-size-change="onPageSizeChange"
      @selection-change="handleSelectionChange"
    >
      <template #name="{ record }">
        <PermissionLink v-permission="['rent:room:query']" @click="handleViewDetail(record)">{{ record.roomName }}</PermissionLink>
      </template>
      <template #operation="{ record }">
        <a-space>
          <a-button v-permission="['rent:room:edit']" type="text" size="mini" @click="() => handleEdit(record)">编辑</a-button>
          <a-button v-permission="['rent:room:split:add']" type="text" size="mini" @click="() => handleSplit(record)">拆分</a-button>
          <a-button v-permission="['rent:room:merge:add']" type="text" size="mini" @click="() => handleMerge(record)">合并</a-button>
          <a-button v-permission="['rent:room:effectCancel']" type="text" size="mini" @click="() => handleInvalidate(record)">作废</a-button>
        </a-space>
      </template>
    </a-table>

    <!-- 资产构成分析弹框 -->
    <a-modal
      v-model:visible="assetDrawerVisible"
      :title="`资产构成 - ${currentBuilding}`"
      :footer="false"
      :width="800"
    >
      <!-- 顶部统计数据 -->
      <div class="asset-summary">
        <div class="summary-item">
          <div>自持面积</div>
          <div>{{ assetSummary.selfHoldArea.toFixed(2) }}㎡</div>
        </div>
        <div class="summary-item">
          <div>已接收资产</div>
          <div>{{ assetSummary.receivedAssetArea.toFixed(2) }}㎡</div>
        </div>
        <div class="summary-item">
          <div style="color: #f00;">房源总面积</div>
          <div style="color: #f00;">{{ assetSummary.totalRoomArea.toFixed(2) }}㎡</div>
        </div>
        <div class="summary-item">
          <div>房源数</div>
          <div>{{ assetSummary.roomCount }}个</div>
        </div>
      </div>
      
      <!-- 表格1：接收资产构成 -->
      <div style="margin-top: 16px;">
        <div style="font-weight: bold;">接收资产构成</div>
        <a-table
          :columns="assetColumns"
          :data="receivedAssetData"
          :pagination="false"
          :bordered="{ cell: true }"
          size="small"
          style="margin-top: 8px;"
        >
          <template #empty>
            <a-empty description="暂无数据" />
          </template>
        </a-table>
      </div>
      
      <!-- 表格2：房源业态构成 -->
      <div style="margin-top: 16px;">
        <div style="font-weight: bold;">房源业态构成</div>
        <a-table
          :columns="businessColumns"
          :data="roomBizTypeData"
          :pagination="false"
          :bordered="{ cell: true }"
          size="small"
          style="margin-top: 8px;"
        >
          <template #empty>
            <a-empty description="暂无数据" />
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { 
  getRoomList,
  getAssetComposition,
  getAssetCompositionStatistics,
  type RoomQueryDTO, 
  type RoomVo,
  type AssetCompositionVo,
  type AssetCompositionQueryDTO,
  type RoomStatisticsQueryDTO,
  type RoomStatisticsVo,
  type AssetCompositionStatisticsVo,
  RoomStatus,
  PaymentStatus,
  RoomType
} from '@/api/room'

// 定义props
const props = defineProps<{
  selection: {
    projectId: string
    projectName: string
    blockId: string
    blockName: string
    buildingId: string
    buildingName: string
  }
  filterForm: any
  loading: boolean
}>()

// 定义emits
const emit = defineEmits<{
  'update:loading': [value: boolean]
  'selection-change': [keys: string[]]
  'view-detail': [id: string]
  'edit-room': [id: string]
  'split-room': [id: string]
  'merge-room': [room: RoomVo]
  'invalidate-room': [id: string, name: string]
  'update-room-data-cache': [data: RoomVo[]]
}>()

// 表格数据
const tableData = ref<RoomVo[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
})

// 选中的行
const selectedKeys = ref<string[]>([])
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
})

// 资产构成数据
const assetData = ref<AssetCompositionStatisticsVo[]>([])
const receivedAssetData = ref<AssetCompositionStatisticsVo[]>([])
const roomBizTypeData = ref<AssetCompositionStatisticsVo[]>([])
const assetDrawerVisible = ref(false)
const assetStatistics = ref<RoomStatisticsVo>({})
const currentBuilding = ref('')
const assetSummary = reactive({
  selfHoldArea: 0,
  receivedAssetArea: 0,
  totalRoomArea: 0,
  roomCount: 0,
  avgArea: 0,
  avgOccupancyRate: 0
})

// 表格列定义
const columns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '房源名称', dataIndex: 'roomName', slotName: 'name', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼栋', dataIndex: 'buildingName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼层', dataIndex: 'floorName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '计租面积(㎡)', dataIndex: 'rentArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '物业类型', dataIndex: 'propertyTypeName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { 
    title: '交付状态', 
    dataIndex: 'paymentStatus', 
    width: 120, 
    ellipsis: true, 
    tooltip: true, 
    align: 'center',
    render: (data: any) => {
      return data.record.paymentStatus === PaymentStatus.DELIVERED ? '已交付' : '未交付'
    }
  },
  { title: '户型', dataIndex: 'houseTypeName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '可招租日期', dataIndex: 'rentalStartDate', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '对外出租起始日期', dataIndex: 'externalRentStartDate', width: 140, ellipsis: true, tooltip: true, align: 'center' },
  { title: '操作', slotName: 'operation', width: 280, ellipsis: false, tooltip: false, align: 'center', fixed: 'right' }
]

// 资产构成表格列
const assetColumns = [
  { title: '业态', dataIndex: 'typeName', align: 'center', width: 150 },
  { title: '数量', dataIndex: 'count', align: 'center', width: 100 },
  { 
    title: '建筑面积(㎡)', 
    dataIndex: 'buildArea', 
    align: 'center', 
    width: 150,
    render: (data: any) => {
      return Number(data.record.buildArea || 0).toFixed(2)
    }
  }
]
const businessColumns = [
  { title: '业态', dataIndex: 'typeName', align: 'center', width: 150 },
  { title: '数量', dataIndex: 'count', align: 'center', width: 100 },
  { 
    title: '建筑面积(㎡)', 
    dataIndex: 'buildArea', 
    align: 'center', 
    width: 150,
    render: (data: any) => {
      return Number(data.record.buildArea || 0).toFixed(2)
    }
  }
]

// 查询房源列表
const fetchRoomList = async () => {
  // 如果没有项目ID，不调用接口
  if (!props.filterForm.projectId) {
    console.log('没有项目ID，跳过接口调用')
    emit('update:loading', false)
    return
  }

  try {
    emit('update:loading', true)
    const queryParams: RoomQueryDTO = {
      ...props.filterForm,
      status: RoomStatus.ACTIVE, // 只查询生效中的房源
      type: RoomType.NORMAL, // 默认为普通房源
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    
    console.log('ActiveProperties 调用接口，参数:', queryParams)
    const response = await getRoomList(queryParams)
    if (response.rows) {
      tableData.value = response.rows || []
      pagination.total = response.total || 0
      
      // 添加序号
      tableData.value.forEach((item: any, index) => {
        item.index = (pagination.current - 1) * pagination.pageSize + index + 1
      })
      
      // 通过自定义事件将数据传递给父组件的缓存
      emit('update-room-data-cache', tableData.value)
    }
  } catch (error) {
    console.error('查询房源列表失败:', error)
  } finally {
    emit('update:loading', false)
  }
}

// 分页变化
const onPageChange = (page: number) => {
  pagination.current = page
  props.filterForm.pageNum = page
  fetchRoomList()
}

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  props.filterForm.pageSize = pageSize
  props.filterForm.pageNum = 1
  fetchRoomList()
}

// 处理选择变化
const handleSelectionChange = (rowKeys: string[]) => {
  selectedKeys.value = rowKeys
  emit('selection-change', rowKeys)
}

// 查看详情
const handleViewDetail = (record: RoomVo) => {
  console.log('查看详情:', record)
  if (record.id) {
    emit('view-detail', record.id)
  } else {
    Message.error('房源ID不存在')
  }
}

// 编辑
const handleEdit = (record: RoomVo) => {
  console.log('编辑房源:', record)
  if (record.id) {
    emit('edit-room', record.id)
  } else {
    Message.error('房源ID不存在')
  }
}

// 拆分
const handleSplit = (record: RoomVo) => {
  console.log('拆分房源:', record)
  if (!record.id) {
    Message.error('房源ID不存在')
    return
  }
  
  // 使用emit通知父组件处理拆分，只传递房源ID
  emit('split-room', record.id)
}

// 合并
const handleMerge = (record: RoomVo) => {
  console.log('合并房源:', record)
  if (!record.id) {
    Message.error('房源ID不存在')
    return
  }
  
  // 使用emit通知父组件处理合并，传递完整的房源对象
  emit('merge-room', record)
}

// 作废
const handleInvalidate = (record: RoomVo) => {
  console.log('作废房源:', record)
  if (!record.id) {
    Message.error('房源ID不存在')
    return
  }
  
  // 使用emit通知父组件作废单个房源
  emit('invalidate-room', record.id, record.roomName || '')
}

// 显示资产构成分析
const showAssetAnalysis = async () => {
  // 验证必要参数
  if (!props.filterForm.projectId) {
    Message.warning('请先选择项目')
    return
  }

  try {
    const queryParams: RoomStatisticsQueryDTO = {
      projectId: props.filterForm.projectId,
      parcelId: props.filterForm.parcelId,
      buildingId: props.filterForm.buildingId
    }
    
    console.log('调用资产构成统计接口，参数:', queryParams)
    
    const response = await getAssetCompositionStatistics(queryParams)
    console.log('资产构成统计接口响应:', response)
    
    if (response && response.data) {
      assetStatistics.value = response.data
      
      // 设置汇总数据
      assetSummary.selfHoldArea = Number(assetStatistics.value.selfHoldArea) || 0
      assetSummary.receivedAssetArea = Number(assetStatistics.value.receivedAssetArea) || 0
      assetSummary.totalRoomArea = Number(assetStatistics.value.totalRoomArea) || 0
      assetSummary.roomCount = Number(assetStatistics.value.roomCount) || 0
      assetSummary.avgArea = assetSummary.roomCount > 0 ? assetSummary.totalRoomArea / assetSummary.roomCount : 0
      assetSummary.avgOccupancyRate = assetSummary.totalRoomArea > 0 ? assetSummary.receivedAssetArea / assetSummary.totalRoomArea : 0
      
      // 分别设置接收资产构成和房源业态构成数据
      receivedAssetData.value = assetStatistics.value.receivedAssetComposition || []
      roomBizTypeData.value = assetStatistics.value.roomBizTypeComposition || []
      assetData.value = [...receivedAssetData.value, ...roomBizTypeData.value]
      
      // 设置当前楼栋名称
      if (props.selection.buildingId && props.selection.buildingName) {
        currentBuilding.value = props.selection.buildingName
      } else if (props.selection.blockId && props.selection.blockName) {
        currentBuilding.value = props.selection.blockName
      } else if (props.selection.projectName) {
        currentBuilding.value = props.selection.projectName
      } else {
        currentBuilding.value = '当前项目'
      }
      
    } else {
      console.log('接口返回数据为空')
      assetStatistics.value = {}
      assetData.value = []
      // 重置汇总数据
      Object.keys(assetSummary).forEach(key => {
        assetSummary[key as keyof typeof assetSummary] = 0
      })
    }
    
    assetDrawerVisible.value = true
  } catch (error) {
    console.error('获取资产构成数据失败:', error)
    console.error('错误详情:', {
      message: (error as any)?.message,
      response: (error as any)?.response,
      status: (error as any)?.response?.status,
      data: (error as any)?.response?.data
    })
  }
}

// 暴露给父组件的方法
defineExpose({
  showAssetAnalysis,
  handleSelectionChange
})

// 监听父组件的刷新事件
const handleRefresh = () => {
  console.log('ActiveProperties 收到刷新事件')
  selectedKeys.value = []
  fetchRoomList()
}

// 移除对selection的监听，避免重复调用接口
// 改为只通过全局事件触发刷新

// 监听特定的刷新事件
onMounted(() => {
  document.addEventListener('refresh-property-active-data', handleRefresh)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('refresh-property-active-data', handleRefresh)
})
</script>

<style scoped lang="less">
.active-properties {
  :deep(.arco-table-th) {
    background-color: var(--color-fill-2);
  }

  :deep(.arco-table-td) {
    background-color: var(--color-bg-2);
  }

  :deep(.arco-pagination) {
    margin-top: 16px;
    justify-content: flex-end;
  }
}

.asset-summary {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 16px;

  .summary-item {
    text-align: center;
    min-width: 120px;
    padding: 0 8px;

    div:first-child {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    div:last-child {
      font-size: 16px;
      font-weight: bold;
    }
  }
}
</style>