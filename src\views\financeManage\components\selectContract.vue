<template>
    <a-drawer :visible="drawerVisible" :width="1200" :footer="true" :title="drawerTitle" unmount-on-close @cancel="handleCancel">
        <!-- 筛选条件 -->
        <div class="filter-container">
            <a-form :model="searchForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" label-align="right">
                <a-row :gutter="16">
                    <a-col :span="8">
                        <a-form-item field="projectId" label="项目">
                            <a-select v-model="searchForm.projectId" placeholder="请选择项目" allow-clear @change="handleProjectChange">
                                <a-option v-for="item in projectOptions" :key="item.id" :value="item.id">
                                    {{ item.name }}
                                </a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="plotId" label="地块">
                            <a-select v-model="searchForm.plotId" placeholder="请选择地块" allow-clear @change="handlePlotChange">
                                <a-option v-for="item in plotOptions" :key="item.id" :value="item.id">
                                    {{ item.name }}
                                </a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="buildingId" label="楼栋">
                            <a-select v-model="searchForm.buildingId" placeholder="请选择楼栋" allow-clear @change="handleBuildingChange">
                                <a-option v-for="item in buildingOptions" :key="item.id" :value="item.id">
                                    {{ item.name }}
                                </a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="roomId" label="房间">
                            <a-select v-model="searchForm.roomId" placeholder="请选择房间" allow-clear>
                                <a-option v-for="item in roomOptions" :key="item.id" :value="item.id">
                                    {{ item.name }}
                                </a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="tenantName" label="承租人">
                            <a-input v-model="searchForm.tenantName" placeholder="请输入承租人" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="contractNo" label="合同号">
                            <a-input v-model="searchForm.contractNo" placeholder="请输入合同号" allow-clear />
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
            <div class="filter-actions">
                <a-button type="primary" @click="handleSearch">查询</a-button>
            </div>
        </div>

        <!-- 表格 -->
        <a-table 
            row-key="id" 
            :loading="loading" 
            :pagination="pagination" 
            :columns="columns" 
            :data="tableData"
            :row-selection="rowSelection"
            :bordered="{ cell: true }"
            @page-change="onPageChange" 
            @page-size-change="onPageSizeChange"
            @row-click="handleRowClick"
        />
        
        <!-- 底部操作按钮 -->
        <template #footer>
            <div class="drawer-footer">
                <a-space>
                    <a-button @click="handleCancel">取消</a-button>
                    <a-button type="primary" @click="handleConfirm">确认</a-button>
                </a-space>
            </div>
        </template>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, PropType, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import type { TableColumnData } from '@arco-design/web-vue'

// 定义props
const props = defineProps({
    title: {
        type: String,
        default: '选择合同'
    },
    multiple: {
        type: Boolean,
        default: false
    }
})

// 定义emit
const emit = defineEmits(['cancel', 'confirm'])

const drawerVisible = ref(false)

// 计算属性：抽屉标题
const drawerTitle = computed(() => props.title || '选择合同')

// 搜索表单
const searchForm = reactive({
    projectId: '',
    plotId: '',
    buildingId: '',
    roomId: '',
    tenantName: '',
    contractNo: ''
})

// 加载状态
const loading = ref(false)

// 分页
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
})

// 表格列定义
const columns = ref<TableColumnData[]>([
    {
        title: '项目',
        dataIndex: 'projectName',
        width: 150,
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '合同号',
        dataIndex: 'contractNo',
        width: 150,
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '承租人',
        dataIndex: 'tenantName',
        width: 120,
    },
    {
        title: '合同周期',
        dataIndex: 'contractPeriod',
        width: 200,
    },
    {
        title: '合同状态',
        dataIndex: 'contractStatus',
        width: 100,
    }
])

// 表格数据
const tableData = ref<any[]>([])

// 选中的行数据
const selectedRow = ref<any>(null)
const selectedRows = ref<any[]>([])

// 行选择配置
const rowSelection = computed(() => ({
    type: props.multiple ? 'checkbox' : 'radio',
    showCheckedAll: props.multiple,
    onlyCurrent: false,
    onChange: (selectedRowKeys: (string | number)[], selectedRowsData: any[]) => {
        if (props.multiple) {
            selectedRows.value = selectedRowsData
        } else {
            selectedRow.value = selectedRowsData.length > 0 ? selectedRowsData[0] : null
        }
    }
}))

// 点击行选中
const handleRowClick = (record: any) => {
    if (props.multiple) {
        // 多选模式下，点击行切换选中状态
        const index = selectedRows.value.findIndex(item => item.id === record.id)
        if (index >= 0) {
            selectedRows.value.splice(index, 1)
        } else {
            selectedRows.value.push(record)
        }
    } else {
        // 单选模式下，点击行直接选中
        selectedRow.value = record
    }
}

// 选项数据
const projectOptions = ref<{ id: number | string, name: string }[]>([
    { id: 1, name: '项目A' },
    { id: 2, name: '项目B' }
])
const plotOptions = ref<{ id: number | string, name: string }[]>([])
const buildingOptions = ref<{ id: number | string, name: string }[]>([])
const roomOptions = ref<{ id: number | string, name: string }[]>([])

// 处理项目变更
const handleProjectChange = (value: number | string) => {
    if (value) {
        // 根据选择的项目加载地块列表
        plotOptions.value = [
            { id: 1, name: '地块A-1' },
            { id: 2, name: '地块A-2' }
        ]
    } else {
        // 清空地块和下级选项
        plotOptions.value = []
        buildingOptions.value = []
        roomOptions.value = []
        searchForm.plotId = ''
        searchForm.buildingId = ''
        searchForm.roomId = ''
    }
}

// 处理地块变更
const handlePlotChange = (value: number | string) => {
    if (value) {
        // 根据选择的地块加载楼栋列表
        buildingOptions.value = [
            { id: 1, name: '1栋' },
            { id: 2, name: '2栋' }
        ]
    } else {
        // 清空楼栋和房间选项
        buildingOptions.value = []
        roomOptions.value = []
        searchForm.buildingId = ''
        searchForm.roomId = ''
    }
}

// 处理楼栋变更
const handleBuildingChange = (value: number | string) => {
    if (value) {
        // 根据选择的楼栋加载房间列表
        roomOptions.value = [
            { id: 1, name: '101' },
            { id: 2, name: '102' }
        ]
    } else {
        // 清空房间选项
        roomOptions.value = []
        searchForm.roomId = ''
    }
}

// 查询
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 模拟数据
        tableData.value = [
            {
                id: 1,
                projectName: '项目A',
                contractNo: '**********',
                tenantName: '张三',
                contractPeriod: '2024-01-01 至 2025-01-01',
                contractStatus: '正常'
            },
            {
                id: 2,
                projectName: '项目A',
                contractNo: '**********',
                tenantName: '李四',
                contractPeriod: '2024-02-01 至 2025-02-01',
                contractStatus: '正常'
            },
            {
                id: 3,
                projectName: '项目B',
                contractNo: '**********',
                tenantName: '王五',
                contractPeriod: '2024-03-01 至 2025-03-01',
                contractStatus: '正常'
            }
        ]
        pagination.total = 3
        loading.value = false
    } catch (error) {
        console.error('获取合同列表失败:', error)
        loading.value = false
    }
}

// 分页
const onPageChange = (current: number) => {
    pagination.current = current
    fetchData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    fetchData()
}

// 取消
const handleCancel = () => {
    // emit('update:visible', false)
    drawerVisible.value = false
    emit('cancel')
}

// 确认
const handleConfirm = () => {
    if (props.multiple) {
        if (selectedRows.value.length === 0) {
            Message.warning('请选择至少一个合同')
            return
        }
        emit('confirm', selectedRows.value)
    } else {
        if (!selectedRow.value) {
            Message.warning('请选择一个合同')
            return
        }
        emit('confirm', selectedRow.value)
    }
    // emit('update:visible', false)
    drawerVisible.value = false
}

// 初始化
const init = () => {
    // 重置选中状态
    selectedRow.value = null
    selectedRows.value = []
    
    // 获取数据
    fetchData()
}
// 暴露方法给父组件
defineExpose({
    open() {
        // isEdit.value = true
        drawerVisible.value = true
        // initFormData({})
    },
    // view(record: any) {
    //     isEdit.value = false
    //     drawerVisible.value = true
    //     initFormData(record)
    // },
    // edit(record: any) {
    //     isEdit.value = true
    //     drawerVisible.value = true
    //     initFormData(record)
    // },
    // async save() {
    //     return handleSave()
    // },
    // async submit() {
    //     return handleSubmit()
    // },
    // print() {
    //     Message.info('打印预览')
    // },
    // initFormData(record: any) {
    //     initFormData(record)
    // }
})

// // 监听抽屉可见性变化
// watch(() => props.visible, (newVal) => {
//     if (newVal) {
//         init()
//     }
// })
</script>

<style scoped lang="less">
.filter-container {
    margin-bottom: 16px;
    
    .filter-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
    }
}

.drawer-footer {
    display: flex;
    justify-content: flex-end;
}
</style> 