<template>
    <div class="company-edit-container">
        <a-form ref="formRef" label-align="right" :model="formModel" :rules="rules" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item field="orgCompanyName" label="商业公司名称" required>
                        <a-input v-model="formModel.orgCompanyName" placeholder="请输入商业公司名称" :disabled="true"/>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="merchantNo" label="商户号" required>
                        <a-input v-model="formModel.merchantNo" placeholder="请输入商户号" :disabled="true"/>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="bankName" label="开户银行" required>
                        <a-input v-model="formModel.bankName" placeholder="请输入开户银行" :disabled="true" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="bankAccount" label="银行账号" required>
                        <a-input v-model="formModel.bankAccount" placeholder="请输入银行账号" :disabled="true" />
                    </a-form-item>
                </a-col>
            </a-row>

            <sectionTitle title="企业信用信息" />
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item field="unifiedSocialCreditCode" label="统一社会信用代码">
                        <a-input v-model="formModel.unifiedSocialCreditCode" placeholder="请输入统一社会信用代码" :disabled="isViewMode" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="phoneNumber" label="电话号码">
                        <a-input v-model="formModel.phoneNumber" placeholder="请输入电话号码" :disabled="isViewMode" />
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item field="registeredAddress" label="注册地址">
                        <a-input v-model="formModel.registeredAddress" placeholder="请输入注册地址" :disabled="isViewMode"/>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="legalPersonName" label="法人名称">
                        <a-input v-model="formModel.legalPersonName" placeholder="请输入法人姓名" :disabled="true"/>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="legalPersonIdCard" label="法人身份证">
                        <a-input v-model="formModel.legalPersonIdCard" placeholder="请输入法人身份证号码" :disabled="true"/>
                    </a-form-item>
                </a-col>
            </a-row>
            
            <sectionTitle title="电子签章相关信息" />
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item field="agentName" label="经办人姓名">
                        <a-input v-model="formModel.agentName" placeholder="请输入经办人姓名" :disabled="isViewMode" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="agentPhoneNumber" label="经办人电话号码">
                        <a-input v-model="formModel.agentPhoneNumber" placeholder="请输入经办人电话号码" :disabled="isViewMode" />
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item field="agentIdCard" label="经办人身份证">
                        <a-input v-model="formModel.agentIdCard" placeholder="请输入经办人身份证号码" :disabled="isViewMode" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="sealId" label="印章ID">
                        <a-input v-model="formModel.sealId" placeholder="请输入印章ID" :disabled="isViewMode" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="processId" label="流程ID">
                        <a-input v-model="formModel.processId" placeholder="请输入流程ID" :disabled="isViewMode" />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { getMerchantDetail, editMerchant, type MerchantAddDTO } from '@/api/company'

const props = defineProps({
    id: {
        type: [Number, String],
        required: true
    },
    mode: {
        type: String,
        default: 'edit', // 'edit' 或 'view'
        validator: (value: string) => ['edit', 'view'].includes(value)
    }
})

const emit = defineEmits(['save', 'loading'])

const formRef = ref()
const formModel = ref<MerchantAddDTO>({
    id: '',
    orgCompanyName: '',
    merchantNo: '',
    bankName: '',
    bankAccount: '',
    unifiedSocialCreditCode: '',
    phoneNumber: '',
    registeredAddress: '',
    legalPersonName: '',
    legalPersonIdCard: '',
    agentName: '',
    agentPhoneNumber: '',
    agentIdCard: '',
    sealId: '',
    processId: ''
})

// 是否为查看模式
const isViewMode = computed(() => props.mode === 'view')

// 表单验证规则
const rules = computed(() => {
    if (isViewMode.value) return {}
    
    return {
        // 可编辑且可为空的字段
        phoneNumber: [
            { 
                validator: (value: string, callback: (error?: string) => void) => {
                    if (value && !/^1[3-9]\d{9}$/.test(value)) {
                        callback('请输入正确的手机号码')
                    } else {
                        callback()
                    }
                }
            }
        ]
    }
})

// 获取商业公司详情
const fetchMerchantDetail = async () => {
    if (!props.id) return
    
    try {
        emit('loading', true)
        const response = await getMerchantDetail(String(props.id))
        
        if (response && response.data) {
            // 处理null值，转换为空字符串
            const data = response.data
            formModel.value = {
                id: data.id || '',
                orgCompanyName: data.orgCompanyName || '',
                merchantNo: data.merchantNo || '',
                bankName: data.bankName || '',
                bankAccount: data.bankAccount || '',
                unifiedSocialCreditCode: data.unifiedSocialCreditCode || '',
                phoneNumber: data.phoneNumber || '',
                registeredAddress: data.registeredAddress || '',
                legalPersonName: data.legalPersonName || '',
                legalPersonIdCard: data.legalPersonIdCard || '',
                agentName: data.agentName || '',
                agentPhoneNumber: data.agentPhoneNumber || '',
                agentIdCard: data.agentIdCard || '',
                sealId: data.sealId || '',
                processId: data.processId || ''
            }
        }
    } catch (error) {
        console.error('获取商业公司详情失败:', error)
    } finally {
        emit('loading', false)
    }
}

// 提交表单
const handleSubmit = async () => {
    if (isViewMode.value) return Promise.resolve()
    
    return new Promise(async (resolve, reject) => {
        try {
            // 只进行表单验证（针对可编辑字段）
            const errors = await formRef.value.validate()
            if (errors) {
                reject(new Error('表单验证失败'))
                return
            }
            
            emit('loading', true)
            await editMerchant(formModel.value)
            
            Message.success('保存成功')
            emit('save')
            resolve(true)
        } catch (error) {
            console.error('保存失败:', error)
            reject(error)
        } finally {
            emit('loading', false)
        }
    })
}

// 表单验证
const validate = async () => {
    if (isViewMode.value) return true
    
    try {
        const errors = await formRef.value.validate()
        return !errors
    } catch (error) {
        return false
    }
}

onMounted(() => {
    fetchMerchantDetail()
})

// 暴露方法给父组件
defineExpose({
    handleSubmit,
    formModel,
    validate
})
</script>

<style scoped lang="less">
.company-edit-container {
    :deep(.section-title) {
        margin: 16px 0;
    }
}
</style>