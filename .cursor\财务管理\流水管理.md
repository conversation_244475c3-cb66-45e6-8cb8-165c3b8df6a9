# 其它收入
 - 弹出 modal 提示
 - 提示：是否将此流水标记其他收入
 - 输入说明，必填
 - 取消和确认
 - 直接写在流水管理页面

# 选择合同
 - 在财务管理文件夹 创建 组件， a-drawer 也都写在组件里面
 - 使用 a-drawer
    ## 筛选条件
        - 项目 select
        - 地块 select
        - 楼栋 select
        - 房间 select
        - 承租人 select
        - 合同号 input
        ### 操作
            - 查询
    ## 表格
        - 选择
        - 项目
        - 合同号
        - 承租人
        - 合同周期
        - 合同状态
        ### 操作
            单选，点击行自动选定
    ## 操作
        - 取消
        - 确认

# 流水记账 （a-drawer）
    ## 流水记账
        - table 展示
        - 字段：
            - 流水订单号
            - 支付方向
            - 支付方式
            - 对方姓名
            - 入账时间
            - 支付金额
            - 已使用金额
            - 剩余金额
    ## 账单信息
        - 一个选择合同的输入框
        - 展示信息：合同号，合同周期，租赁单元，合同状态
        - 操作按钮：自动核销
        ### table （根据合同查询未收齐账单//financialFlow/getBillByContractId）
            - 字段：承租人，账单周期，账单类型，应收日期，账单总额(元），优惠金额（元），账单应收总额（元），账单已收金额（元）
            - 操作：记账（逻辑和账单管理的记账一样的）
    ## 记账信息
        - table 展示
        - 字段：项目，合同号，承租人，租赁单元，合同状态，账单类型，应收日期，账单待收金额，本次记账金额，账单待收金额
        - 操作：移除
        - 合计
    ## 历史记账信息
        - 参考定单管理的 历史记账信息
