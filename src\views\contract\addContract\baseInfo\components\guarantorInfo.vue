<template>
    <section>
        <sectionTitle title="担保人信息" />
        <div class="content">
            <a-form ref="formRef" :model="contractData.customer" :rules="isDetailMode ? {} : rules"
                :disabled="isDetailMode" layout="vertical" auto-label-width>
                <a-form-item label="姓名" field="guarantorName">
                    <a-input v-model="contractData.customer.guarantorName" placeholder="请输入姓名" allow-clear />
                </a-form-item>
                <a-form-item label="手机号" field="guarantorPhone">
                    <a-input v-model="contractData.customer.guarantorPhone" placeholder="请输入手机号" maxlength="11"
                        allow-clear />
                </a-form-item>
                <a-form-item label="证件类型" field="guarantorIdType">
                    <a-select v-model="contractData.customer.guarantorIdType" placeholder="请选择证件类型">
                        <a-option v-for="item in idTypeOptions" :key="item.value" :value="item.value">
                            {{ item.label }}
                        </a-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="证件号码" field="guarantorIdNumber">
                    <a-input v-model="contractData.customer.guarantorIdNumber" placeholder="请输入证件号码" allow-clear />
                </a-form-item>
                <a-form-item label="通讯地址" field="guarantorAddress">
                    <a-input v-model="contractData.customer.guarantorAddress" placeholder="请输入通讯地址" allow-clear />
                </a-form-item>
                <a-card>
                    <a-form-item label="请上传本人身份证正反面" hide-label>
                        <a-grid :cols="2" :col-gap="16" style="flex: 1">

                            <a-upload :disabled="isDetailMode" action="/" :show-file-list="false"
                                :custom-request="customFrontRequest">
                                <template #upload-button>
                                    <div class="card-item">
                                        <img class="card-bg"
                                            :src="contractData.customer.guarantorIdFront || idCardFront" />
                                        <span class="card-text"
                                            v-if="!contractData.customer.guarantorIdFront">上传身份证人像面</span>
                                    </div>
                                </template>
                            </a-upload>
                            <a-upload :disabled="isDetailMode" action="/" :show-file-list="false"
                                :custom-request="customBackRequest">
                                <template #upload-button>
                                    <div class="card-item">
                                        <img class="card-bg"
                                            :src="contractData.customer.guarantorIdBack || idCardBack" />
                                        <span class="card-text"
                                            v-if="!contractData.customer.guarantorIdBack">上传身份证国徽面</span>
                                    </div>
                                </template>
                            </a-upload>
                        </a-grid>
                    </a-form-item>
                    <div class="button-bar" v-if="!isDetailMode && !(editType === 'change' && !changeType.includes(1))">
                        <a-button type="primary" @click="handleIdCardRecognition">开始识别</a-button>
                    </div>
                </a-card>
            </a-form>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { inject, computed, ref, watch, toRef } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { FileItem, FormInstance, Message } from '@arco-design/web-vue';
import idCardFront from '@/assets/images/contract/idCard-front.png';
import idCardBack from '@/assets/images/contract/idCard-back.png';
import { useContractStore } from '@/store/modules/contract'
import { uploadFile, analyzeImage } from '@/api/common';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

/**
 * @description 编辑类型
 * 1. 详情
 *    - 所有字段不可修改，且展示成文本
 */
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => (editType.value === 'change' && !changeType.value.includes(1)) || editType.value === 'detail' || editType.value === 'changeDetail' || editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo')
const formItemMargin = computed(() => isDetailMode.value ? '16px' : '16px')


// 表单验证规则
const rules = computed(() => {
    const isRequired = Boolean(contractData.value.customer?.guarantorName)
    if (!isRequired) {
        formRef.value?.resetFields()
    }
    return {
        guarantorName: [{ required: isRequired, message: '请输入姓名' }],
        guarantorPhone: [{ required: isRequired, message: '请输入手机号' }],
        guarantorIdType: [{ required: isRequired, message: '请选择证件类型' }],
        guarantorIdNumber: [{ required: isRequired, message: '请输入证件号码' }],
        guarantorAddress: [{ required: isRequired, message: '请输入通讯地址' }]
    }
})

// 证件类型选项
const idTypeOptions = [
    { label: '身份证', value: '1' },
    { label: '护照', value: '2' },
    { label: '军官证', value: '3' }
]

const idFrontFile = ref<FileItem>()
const idBackFile = ref<FileItem>()
// 身份证正面照片上传
const customFrontRequest = async (options: any) => {
    const { fileItem } = options
    const formData = new FormData()
    formData.append('file', fileItem.file)
    try {
        const res = await uploadFile(formData)
        contractData.value.customer.guarantorIdFront = res.data.fileUrl
        idFrontFile.value = fileItem
    } catch (error) {

    }
}

// 身份证正面照片上传
const customBackRequest = async (options: any) => {
    const { fileItem } = options
    const formData = new FormData()
    formData.append('file', fileItem.file)
    try {
        const res = await uploadFile(formData)
        contractData.value.customer.guarantorIdBack = res.data.fileUrl
        idBackFile.value = fileItem
    } catch (error) {

    }
}
// 身份证反面照片
const fileBack = ref<FileItem>()

// 身份证反面照片上传成功
const fileBackChangeSuccess = (file: FileItem) => {
    fileBack.value = file
}

// 身份证识别
const handleIdCardRecognition = async () => {
    // 检查是否至少上传了一张身份证图片
    if (!contractData.value.customer.guarantorIdFront && !contractData.value.customer.guarantorIdBack) {
        Message.warning('请先上传身份证正面或反面')
        return
    }

    try {

        let frontResults = null
        let backResults = null

        // 生成当前日期时间戳
        const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '')

        // 如果上传了正面，则识别正面 (type: 1)
        if (idFrontFile.value) {
            const frontFormData = new FormData()
            idFrontFile
            const frontFile = new File([idFrontFile.value.file], `idcard_front_${timestamp}.${idFrontFile.value.file.name.split('.').pop()}`, {
                type: idFrontFile.value.file.type
            })
            frontFormData.append('file', frontFile)
            frontResults = await analyzeImage(frontFormData, 1)
        }

        // 如果上传了反面，则识别反面 (type: 2)
        if (idBackFile.value) {
            const backFormData = new FormData()
            const backFile = new File([idBackFile.value.file], `idcard_back_${timestamp}.${idBackFile.value.file.name.split('.').pop()}`, {
                type: idBackFile.value.file.type
            })
            backFormData.append('file', backFile)
            backResults = await analyzeImage(backFormData, 2)
        }

        // 处理正面识别结果
        if (frontResults?.data) {
            const frontData = frontResults.data
            if (frontData.name) contractData.value.customer.guarantorName = frontData.name
            if (frontData.idCard) contractData.value.customer.guarantorIdNumber = frontData.idCard
            if (frontData.address) contractData.value.customer.guarantorAddress = frontData.address
        }

        // // 处理反面识别结果
        // if (backResults?.data) {
        //     const backData = backResults.data
        //     if (backData.validatePeriod) {
        //         // 解析有效期格式 "2004.10.27-2024.10.26"
        //         const periods = backData.validatePeriod.split('-')
        //         if (periods.length === 2) {
        //             // 转换日期格式从 "2004.10.27" 到 "2004-10-27"
        //             const startDate = periods[0].replace(/\./g, '-')
        //             const endDate = periods[1].replace(/\./g, '-')
        //             contractData.vaue.customer.guarantorIdValidPeriod = [startDate, endDate]
        //         }
        //     }
        // }

        // 根据识别的内容显示不同的成功消息
        let successMessage = '身份证识别成功'
        if (frontResults && backResults) {
            successMessage = '身份证正反面识别成功'
        } else if (frontResults) {
            successMessage = '身份证正面识别成功'
        } else if (backResults) {
            successMessage = '身份证反面识别成功'
        }

        Message.success(successMessage)
    } catch (error) {
        console.error('身份证识别失败:', error)
    } finally {
    }
}

const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('担保人信息', errors);
            reject()
        } else {
            resolve(true)
        }
    })
}

defineExpose({
    validate
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;

        .card-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;

            .card-bg {
                width: 100%;
                height: 140px;
                display: block;
                object-fit: fill;
            }

            .card-text {
                position: absolute;
                left: 50%;
                bottom: 0;
                transform: translateX(-50%);
                width: 100%;
                height: 38px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
            }
        }

        .button-bar {
            display: flex;
            justify-content: center;
        }
    }
}

.detail-text {
    padding: 4px 0;
    color: #333;
}

.arco-form-item {
    margin-bottom: v-bind(formItemMargin);
}
</style>