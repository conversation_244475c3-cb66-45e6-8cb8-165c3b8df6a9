<template>
    <div class="contract-termination">
        <!-- <div class="contract-termination-content"> -->
        <!-- 承租方信息 -->
        <section-title title="承租方信息" />
        <a-form :model="tenantForm" layout="horizontal" :label-col-props="{ span: 7 }"
            :wrapper-col-props="{ span: 17 }">
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="承租方名称">
                        <a-input v-model="tenantForm.name" disabled />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="个人/企业">
                        <a-input v-model="tenantForm.type" disabled />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="合同用途">
                        <a-input v-model="tenantForm.purpose" disabled />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>

        <!-- 租赁房源 -->
        <section-title title="租赁房源" />
        <!-- 到期退租提示 -->
        <!-- <div v-if="terminationForm.terminateType === 0" class="room-selection-tip">
            <a-alert type="info" :show-icon="false" :closable="false">
                <template #default>
                    到期退租默认选择全部房源，不允许修改
                </template>
            </a-alert>
        </div> -->
        <!-- {{ selectedRoomIds }} -->
        <a-table 
            :columns="houseColumns" 
            :data="houseList" 
            :pagination="false"
            row-key="roomId"
            :row-selection="props.viewMode ? undefined : {
                type: 'checkbox'
            }"
            v-model:selectedKeys="selectedRoomIds"
            @selection-change="handleRoomSelectionChange"
        >
            <template #area="{ record }">
                {{ record.area }} m²
            </template>
        </a-table>

        <!-- 基础信息 -->
        <section-title title="基础信息" />
        <a-form :model="baseForm" layout="horizontal" :label-col-props="{ span: 7 }" :wrapper-col-props="{ span: 17 }">
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="合同起止日期">
                        <a-input v-model="baseForm.contractPeriod" disabled />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="合同总金额">
                        <a-input v-model="baseForm.totalAmount" disabled append="元"/>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="免租期">
                        <a-input v-model="baseForm.freePeriod" disabled />
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="已收保证金">
                        <a-input v-model="baseForm.deposit" disabled append="元"/>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="已收租金">
                        <a-input v-model="baseForm.paidRent" disabled append="元"/>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="已收款账期">
                        <a-input v-model="baseForm.paidPeriod" disabled ellipsis tooltip/>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="逾期未收租金" class="highlight-red">
                        <a-input v-model="baseForm.unpaidRent" disabled append="元"/>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="逾期账期" class="highlight-red">
                        <a-input v-model="baseForm.terminationPeriod" disabled />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>

        <!-- 退租信息 -->
        <section-title title="退租信息" />
        <a-form :model="terminationForm" layout="horizontal" :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }">
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="退租类型" required>
                        <a-radio-group v-model="terminationForm.terminateType" @change="handleTerminateTypeChange" :disabled="props.viewMode || true">
                            <a-radio :value="0">到期退租</a-radio>
                            <a-radio :value="1">提前退租</a-radio>
                        </a-radio-group>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="退租日期" required>
                        <a-date-picker 
                            v-model="terminationForm.terminateDate" 
                            style="width: 100%" 
                            :disabled="props.viewMode || terminationForm.terminateType === 0"
                            @change="handleTerminateDateChange"
                        />
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item label="退租原因" required :label-col-props="{ span: 2 }">
                        <a-checkbox-group v-model="terminationForm.terminateReason" :disabled="props.viewMode">
                            <a-checkbox v-for="item in terminateReasonOptions" :key="item.dictValue" :value="item.dictValue">
                                {{ item.dictLabel }}
                            </a-checkbox>
                        </a-checkbox-group>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item label="其他原因说明" :label-col-props="{ span: 2.5 }">
                        <a-textarea v-model="terminationForm.otherReasonDesc" placeholder="请输入其他原因说明" :disabled="props.viewMode" />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>

        <!-- 免租期是否收费 -->
        <section-title title="免租期是否收费" />
        <a-table :columns="freeColumns" :data="freeList" :pagination="false">
            <template #operation="{ record }">
                <!-- 到期退租时，免租期不可选择收费，固定为"否" -->
                <a-radio-group 
                    v-model="record.isCharge" 
                    @change="() => handleFreeRentChargeChange(record)"
                    :disabled="props.viewMode || terminationForm.terminateType === 0 || record.isDisabled"
                >
                    <a-radio :value="true">是</a-radio>
                    <a-radio :value="false">否</a-radio>
                </a-radio-group>
            </template>
        </a-table>

        <!-- 预计退款信息 -->
        <section-title title="预计退款信息" />
        <a-table 
            :columns="refundColumns" 
            :data="refundList" 
            :pagination="false"
            :row-class="getRowClass"
        >
            <template #penaltyAmount="{ record }">
                <a-input-number 
                    v-model="record.penaltyAmount" 
                    :min="0" 
                    :max="record.maxPenaltyAmount"
                    :precision="2"
                    :format="true"
                    :disabled="props.viewMode || !record.canEditPenalty"
                    placeholder="请输入罚没金额(0-1000000)"
                    @change="() => calculateTotal(record)"
                />
            </template>
            <template #totalAmount="{ record }">
                {{ formatAmount((Number(record.penaltyAmount) || 0) + (Number(record.refundAmount) || 0)) }}
            </template>
        </a-table>
        
        <!-- 合计信息 -->
        <div class="total-info">
            <a-row :gutter="16">
                <a-col :span="8">
                    <span>总罚没金额：<strong>{{ formatAmount(totalPenaltyAmount) }}</strong></span>
                </a-col>
                <a-col :span="8">
                    <span>总预计退款金额：<strong>{{ formatAmount(totalRefundAmount) }}</strong></span>
                </a-col>
                <!-- <a-col :span="8">
                    <span>合计：<strong>{{ formatAmount(grandTotal) }}</strong></span>
                </a-col> -->
            </a-row>
        </div>

        <!-- 其他扣款 -->
        <section-title title="其他扣款" />
        <a-form :model="deductionForm" layout="horizontal" :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }">
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="其他扣款" required>
                        <a-radio-group v-model="deductionForm.hasOtherDeduction" :disabled="props.viewMode">
                            <a-radio :value="true">是</a-radio>
                            <a-radio :value="false">否</a-radio>
                        </a-radio-group>
                    </a-form-item>
                </a-col>
                <a-col :span="8" v-if="deductionForm.hasOtherDeduction">
                    <a-form-item label="扣款金额" :label-col-props="{ span: 0 }">
                        <a-input-number v-model="deductionForm.deductionAmount" :min="0" :max="1000000" :precision="2" :format="true" placeholder="请输入扣款金额(0-1000000)" :disabled="props.viewMode" />
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="16" v-if="deductionForm.hasOtherDeduction">
                <a-col :span="24">
                    <a-form-item label="说明" :label-col-props="{ span: 2 }">
                        <a-textarea v-model="deductionForm.otherDeductionDesc" placeholder="请输入说明" :disabled="props.viewMode" />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
                <!-- 备注 -->
                <section-title title="备注" />
        <a-form :model="terminationForm" layout="horizontal" :label-col-props="{ span: 2 }" :wrapper-col-props="{ span: 22 }">
            <a-row>
                <a-col :span="24">
                    <a-form-item label="备注">
                        <a-textarea v-model="terminationForm.terminateRemark" placeholder="请输入备注信息" :disabled="props.viewMode" />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>

        <!-- 附件 -->
        <section-title title="附件" v-if="!props.viewMode" />
        <a-upload 
            v-if="!props.viewMode"
            :file-list="fileList" 
            :custom-request="handleUpload" 
            :before-upload="beforeUpload"
            multiple
            @change="handleFileChange"
        >
            <a-button type="primary">点击上传</a-button>
            <template #tip>
                <div class="ant-upload-hint">
                    支持 word/ppt/excel/txt等格式
                </div>
            </template>
        </a-upload>
        
        <!-- 查看模式下显示附件列表 -->
        <div v-if="props.viewMode && fileList.length > 0">
            <section-title title="附件" />
            <div class="attachment-list">
                <div v-for="(file, index) in fileList" :key="index" class="attachment-item">
                    <span class="attachment-name">{{ file.name || file.fileName }}</span>
                    <a-button type="text" size="small" @click="downloadFile(file)">下载</a-button>
                </div>
            </div>
        </div>



    </div>


    <!-- </div> -->
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, toRefs } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { 
    getContractTerminateDetail, 
    getTerminateBill, 
    getTerminateFreeRent, 
    saveContractTerminate,
    type ContractTerminateAddDTO,
    type ContractTerminateVo,
    type ContractTerminateCostVo,
    type ContractTerminateInitDto
} from '@/api/contractTerminate'
import { getDictLabel } from '@/dict'
import { useDictSync } from '@/utils/dict'

// Props
interface Props {
    contractId?: string
    terminateId?: string
    unionId?: string
    mode?: 'apply-only' | 'apply-and-exit' // 新增模式：仅申请退租 | 申请退租并出场
    refundType?: 0 | 1
    viewMode?: boolean // 新增查看模式：true-只读模式，false-编辑模式
}

const props = withDefaults(defineProps<Props>(), {
    contractId: '',
    terminateId: '',
    unionId: '',
    mode: 'apply-only',
    refundType: 0 as 0 | 1,
    viewMode: false
})

// Emits
const emit = defineEmits<{
    save: [data: ContractTerminateAddDTO]
    submit: [data: ContractTerminateAddDTO]
    next: [data: ContractTerminateAddDTO] // 新增next事件，用于进入出场流程
}>()

// 响应式数据
const loading = ref(false)
const terminateDetail = ref<ContractTerminateVo>()
const terminateReasonOptions = ref<any[]>([])

// 加载字典数据
const loadDictData = async () => {
    try {
        const dictData = await useDictSync('terminate_reason')
        if (dictData.terminate_reason) {
            terminateReasonOptions.value = dictData.terminate_reason
        }
    } catch (error) {
        console.error('加载退租原因字典数据失败:', error)
    }
}

// 承租方表单数据
const tenantForm = reactive({
    name: '',
    type: '',
    purpose: ''
})

// 房源列表数据
const houseList = ref<any[]>([])
const selectedRoomIds = ref<string[]>([])

// 房源表格列配置
const houseColumns = [
    { 
        title: '房源信息', 
        dataIndex: 'roomName',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    { 
        title: '租赁面积(㎡)', 
        dataIndex: 'area', 
        slotName: 'area',
        align: 'center',
        ellipsis: true,
        tooltip: true
    }
]

// 基础信息表单数据
const baseForm = reactive({
    contractPeriod: '',
    totalAmount: '',
    freePeriod: '',
    deposit: '',
    paidRent: '',
    paidPeriod: '',
    unpaidRent: '',
    terminationPeriod: ''
})

// 退租表单数据
const terminationForm = reactive({
    terminateType: 0,
    terminateDate: '',
    terminateReason: [] as string[],
    otherReasonDesc: '',
    terminateRemark: '',
})

// 免租期表格列配置
const freeColumns = [
    { 
        title: '免租类型', 
        dataIndex: 'freeType',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => {
            const freeTypeMap: Record<number, string> = {
                0: '装修免租',
                1: '经营免租',
                2: '合同免租'
            }
            return freeTypeMap[record.freeType] || '未知'
        }
    },
    { 
        title: '开始日期', 
        dataIndex: 'startDate',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    { 
        title: '免租天数', 
        dataIndex: 'freeRentDay',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => {
            const months = record.freeRentMonth || 0
            const days = record.freeRentDay || 0
            return `${months}月 ${days}天`
        }
    },
    { 
        title: '结束日期', 
        dataIndex: 'endDate',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    { 
        title: '备注', 
        dataIndex: 'remark',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    { 
        title: '是否收费', 
        dataIndex: 'operation', 
        slotName: 'operation',
        align: 'center',
        ellipsis: true,
        tooltip: true
    }
]

// 免租期列表数据
const freeList = ref<any[]>([])

// 退款信息表格列配置
const refundColumns = [
    { 
        title: '费项', 
        dataIndex: 'subjectName',
        align: 'center',
        width: 90,
        ellipsis: true,
        tooltip: true
    },
    { 
        title: '期限', 
        dataIndex: 'period',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => {
            if (record.startDate && record.endDate) {
                return `${record.startDate} 至 ${record.endDate}`
            }
            return record.period || '-'
        }
    },
    { 
        title: '应收日期', 
        dataIndex: 'receivableDate',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    { 
        title: '账单应收金额(元)', 
        dataIndex: 'actualReceivable',
        align: 'center',
        width: 150,
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => formatAmount(record.actualReceivable)
    },
    { 
        title: '截至退款日应收(元)', 
        dataIndex: 'terminateReceivable;',
        width: 160,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => formatAmount(record.terminateReceivable)
    },
    { 
        title: '账单已收金额(元)', 
        dataIndex: 'receivedAmount',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => formatAmount(record.receivedAmount)
    },
    //欠款金额(元)
// 截至退款日应收 - 账单已收金额
    { 
        title: '欠款金额(元)', 
        dataIndex: '',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => formatAmount(record.terminateReceivable - record.receivedAmount)
    },
    { 
        title: '罚没金额(元)', 
        dataIndex: 'penaltyAmount', 
        slotName: 'penaltyAmount',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    //预计退款金额(元)
// 账单已收金额 - 截至退款日应收
    { 
        title: '预计退款金额(元)', 
        dataIndex: 'refundAmount',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => formatAmount(record.refundAmount)
    },
    // { 
    //     title: '合计(元)', 
    //     slotName: 'totalAmount',  // 只使用插槽，不使用dataIndex
    //     align: 'center',
    //     ellipsis: true,
    //     tooltip: true
    // }
]

// 退款信息列表数据
const refundList = ref<ContractTerminateCostVo[]>([])

// 其他扣款表单数据
const deductionForm = reactive({
    hasOtherDeduction: false,
    deductionAmount: 0,
    otherDeductionDesc: ''
})

// 文件列表
const fileList = ref<any[]>([])

// 计算属性
const totalPenaltyAmount = computed(() => {
    return refundList.value.reduce((sum, item) => {
        const penaltyAmount = Number(item.penaltyAmount) || 0
        return sum + penaltyAmount
    }, 0)
})

const totalRefundAmount = computed(() => {
    return refundList.value.reduce((sum, item) => {
        const refundAmount = Number(item.refundAmount) || 0
        return sum + refundAmount
    }, 0)
})

const grandTotal = computed(() => {
    return totalPenaltyAmount.value + totalRefundAmount.value
})



// 方法
const formatAmount = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined || isNaN(Number(amount))) {
        return '0.00'
    }
    // 添加千位分隔符
    return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 判断行是否需要标红
const getRowClass = (record: any) => {
    // 判断 receivableDate 是否在 startDate 和 endDate 范围内
    if (record.receivableDate && record.startDate && record.endDate) {
        const receivableDate = new Date(record.receivableDate)
        const startDate = new Date(record.startDate)
        const endDate = new Date(record.endDate)
        
        // 如果应收日期不在期限范围内，标红
        if (receivableDate < startDate || receivableDate > endDate) {
            return 'red-row'
        }
    }
    return ''
}

const calculateTotal = (record: ContractTerminateCostVo) => {
    // 获取预计退款金额（已在数据源中计算好）
    const refundAmount = Number(record.maxPenaltyAmount) || 0
    
    // 确保罚没金额在合理范围内
    if (refundAmount < 0) {
        record.penaltyAmount = 0
    } else {
        record.penaltyAmount = Math.min(Number(record.penaltyAmount) || 0, refundAmount)
    }
    
    // 更新最终的预计退款金额
    record.refundAmount = refundAmount - (Number(record.penaltyAmount) || 0)
}

// 计算最大可输入的罚没金额
const calculateMaxPenaltyAmount = (record: ContractTerminateCostVo) => {
    const receivedAmount = Number(record.receivedAmount) || 0  // 账单已收金额
    const terminateReceivable = Number(record.terminateReceivable) || 0  // 欠款金额
    
    // 计算预计退款金额
    let refundAmount = 0
    
    // 如果是保证金类型
    if (record.subjectName === '保证金') {
        // 预计退款金额等于账单已收金额
        refundAmount = receivedAmount
    } else {
        // 其他费项的计算逻辑保持不变
        refundAmount = receivedAmount - terminateReceivable
    }

    // 当预计退款金额大于等于0时，罚没金额不能超过预计退款金额
    return refundAmount >= 0 ? refundAmount : 0
}

// 判断是否可以编辑罚没金额
const canEditPenaltyAmount = (record: ContractTerminateCostVo) => {
    const receivedAmount = Number(record.receivedAmount) || 0  // 账单已收金额
    const terminateReceivable = Number(record.terminateReceivable) || 0  // 欠款金额
    
    // 计算预计退款金额
    let refundAmount = 0
    
    // 如果是保证金类型
    if (record.subjectName === '保证金') {
        // 预计退款金额等于账单已收金额
        refundAmount = receivedAmount
    } else {
        // 其他费项的计算逻辑保持不变
        refundAmount = receivedAmount - terminateReceivable
    }

    // 只有当预计退款金额大于等于0时才可以编辑
    return refundAmount >= 0
}

// 加载退租详情
const loadTerminateDetail = async () => {
    try {
        loading.value = true
        const params: any = {}
        if (props.terminateId) {
            params.id = props.terminateId
        } else if (props.contractId) {
            params.contractId = props.contractId
        } else {
            Message.error('缺少必要参数')
            return
        }

        const { data } = await getContractTerminateDetail(params)
        //【合同退租】- 退租申请页面， 退租类型 默认为  到期退租，退租日期为合同结束日期不可修改。 terminateType
        if (!data.terminateType) {
            data.terminateType = 0
            // terminationForm.terminateDate = data.contract?.endDate || ''
        }
        terminateDetail.value = data

        // 填充数据
        fillFormData(data)
    } catch (error) {
        console.error('加载退租详情失败:', error)
        // Message.error('加载数据失败')
    } finally {
        loading.value = false
    }
}

// 填充表单数据
const fillFormData = async (data: ContractTerminateVo) => {
    // 承租方信息
    if (data.contract?.customer) {
        tenantForm.name = data.contract.customer.customerName || ''
        tenantForm.type = data.contract.customer.customerType === 1 ? '个人' : '企业'
        
        // 合同用途转义 - 使用字典
        tenantForm.purpose = getDictLabel('diversification_purpose', data.contract.contractPurpose?.toString() || '') || ''
    }

    // 房源列表 - 从 contract.rooms 获取，并设置禁用状态
    houseList.value = (data.contract?.rooms || []).map(room => ({
        ...room,
        disabled: false // 初始状态不禁用，后续根据退租类型动态更新
    }))
    console.log('房源数据:', data.contract?.rooms)
    console.log('roomList 数据:', data.roomList)
    
    // 退租信息 - 先设置退租类型，这样后续的逻辑才能正确判断
    console.log('fillFormData - data.terminateType:', data.terminateType, 'props.refundType:', props.refundType, 'refundType.value:', refundType.value)
    
    // 优先使用父组件传入的 refundType，如果没有则使用服务器数据
    if (props.refundType !== undefined && props.refundType !== null) {
        terminationForm.terminateType = props.refundType
        console.log('使用父组件 refundType:', props.refundType)
    } else if (data.terminateType !== undefined && data.terminateType !== null) {
        terminationForm.terminateType = data.terminateType
        console.log('使用服务器 terminateType:', data.terminateType)
    } else {
        terminationForm.terminateType = 0
        console.log('使用默认值: 0')
    }
    
    await handleTerminateTypeChange()
    
    // 设置默认选中的房源
    if (data.roomList && data.roomList.length > 0) {
        // 如果 roomList 有值，匹配 roomId 相等的房源
        const roomListIds = data.roomList.map(room => room.roomId?.toString()).filter(id => id)
        selectedRoomIds.value = (data.contract?.rooms || [])
            .filter(room => {
                const roomId = room.roomId?.toString()
                return roomId && roomListIds.includes(roomId)
            })
            .map(room => room.roomId?.toString())
            .filter((id): id is string => id !== null && id !== undefined && id !== '')

            console.log('selectedRoomIds---', selectedRoomIds)
    } else {
        // 如果 roomList 没有值，默认全部选中
        selectedRoomIds.value = (data.contract?.rooms || [])
            .map(room => room.roomId?.toString())
            .filter((id): id is string => id !== null && id !== undefined && id !== '')
    }
    
    // 如果是到期退租，确保选中全部房源
    if (terminationForm.terminateType === 0) {
        selectAllRooms()
    }
    
    // 基础信息
    if (data.contract) {
        baseForm.contractPeriod = `${data.contract.startDate} 至 ${data.contract.endDate}`
        baseForm.totalAmount = data.contract.totalPrice ? formatAmount(data.contract.totalPrice) : ''
        
        // 使用 contract.fees 列表来拼接免租期
        if (data.contract.fees && data.contract.fees.length > 0) {
            const firstFee = data.contract.fees[0]
            const lastFee = data.contract.fees[data.contract.fees.length - 1]
            if (firstFee.startDate && lastFee.endDate) {
                baseForm.freePeriod = `${firstFee.startDate} 至 ${lastFee.endDate}`
            } else {
                baseForm.freePeriod = ''
            }
        } else {
            baseForm.freePeriod = ''
        }
        
        baseForm.deposit = data.bondReceivedAmount ? formatAmount(data.bondReceivedAmount) : ''
        baseForm.paidRent = data.rentReceivedAmount ? formatAmount(data.rentReceivedAmount) : ''
        baseForm.paidPeriod = data.receivedPeriod || ''
        baseForm.unpaidRent = data.rentOverdueAmount ? formatAmount(data.rentOverdueAmount) : ''
        baseForm.terminationPeriod = data.overduePeriod || ''
    }

    // 根据退租类型设置退租日期
    if (terminationForm.terminateType === 0) {
        // 到期退租：使用合同结束日期，优先使用已保存的日期
        terminationForm.terminateDate = data.terminateDate || data.contract?.endDate || ''
    } else {
        // 提前退租：使用已保存的日期
        terminationForm.terminateDate = data.terminateDate || ''
    }
    
    // 退租原因解析 - 处理字符串形式的多选值 "6,2,5"
    if (data.terminateReason) {
        const reasonValue = data.terminateReason as any
        if (typeof reasonValue === 'string') {
            // 先去除前后空格，然后分割，过滤空值
            const trimmedValue = reasonValue.trim()
            if (trimmedValue) {
                terminationForm.terminateReason = trimmedValue.split(',').filter((item: string) => item.trim() !== '')
            } else {
                // 如果是空字符串，根据退租类型设置默认值
                terminationForm.terminateReason = terminationForm.terminateType === 0 ? ['1'] : []
            }
        } else if (Array.isArray(reasonValue)) {
            const validReasons = reasonValue.map((item: any) => item.toString()).filter((item: string) => item.trim() !== '')
            terminationForm.terminateReason = validReasons.length > 0 ? validReasons : (terminationForm.terminateType === 0 ? ['1'] : [])
        } else {
            const reasonStr = reasonValue.toString().trim()
            terminationForm.terminateReason = reasonStr ? [reasonStr] : (terminationForm.terminateType === 0 ? ['1'] : [])
        }
    } else {
        // 如果没有保存的退租原因，且是到期退租，则默认选择"到期退租"
        if (terminationForm.terminateType === 0) {
            terminationForm.terminateReason = ['1']
        } else {
            terminationForm.terminateReason = []
        }
    }
    
    terminationForm.otherReasonDesc = data.otherReasonDesc || ''
    terminationForm.terminateRemark = data.terminateRemark || ''

    // 免租期信息
    freeList.value = data.contract?.fees || []
    
    // 如果是到期退租，将所有免租期的收费选项设置为"否"
    if (terminationForm.terminateType === 0 && freeList.value.length > 0) {
        freeList.value.forEach(item => {
            item.isCharge = false
        })
        console.log('到期退租：初始化时将所有免租期收费设置为"否"')
    }

    // 退款信息 - 重新计算预计退款金额
    if (data.costList && data.costList.length > 0) {
        refundList.value = data.costList.map(item => {
            const receivedAmount = Number(item.receivedAmount) || 0  // 账单已收金额
            const terminateReceivable = Number(item.terminateReceivable) || 0  // 欠款金额
            
            // 计算预计退款金额
            let refundAmount: number = 0
            
            // 如果是保证金类型
            console.log('item.subjectName', item.subjectName)
            if (item.subjectName === '保证金') {
                console.log('保证金',  Math.min(terminateReceivable, receivedAmount))
                // 预计退款金额等于账单已收金额
                refundAmount = receivedAmount
            } else {
                // 其他费项的计算逻辑保持不变
                refundAmount = receivedAmount - terminateReceivable
            }
            
            // 根据预计退款金额的正负来设置罚没金额
            const penaltyAmount: number = refundAmount < 0 ? 0 : (Number(item.penaltyAmount) || 0)
            
            return {
                ...item,
                billAmount: Number(item.totalAmount) || 0,  // 账单应收金额使用原来的totalAmount
                penaltyAmount: penaltyAmount,
                refundAmount: refundAmount - penaltyAmount,  // 使用计算出的值
                terminateReceivable: terminateReceivable,
                receivedAmount: receivedAmount,
                actualReceivable: Number(item.actualReceivable) || 0,
                // 添加一个标记，用于前端判断是否可编辑罚没金额
                canEditPenalty: refundAmount >= 0,
                // 添加最大可输入的罚没金额
                maxPenaltyAmount: refundAmount >= 0 ? refundAmount : 0
            }
        })
    } else {
        // refundList.value = []
    }

    // 其他扣款
    deductionForm.hasOtherDeduction = data.hasOtherDeduction || false
    deductionForm.otherDeductionDesc = data.otherDeductionDesc || ''

    // 附件
    if (data.terminateAttachments) {
        try {
            fileList.value = JSON.parse(data.terminateAttachments)
        } catch (e) {
            console.warn('解析附件失败:', e)
        }
    }


    
    // 最后更新房源禁用状态，确保基于正确的退租类型
    updateRoomDisabledStatus()
    
    console.log('初始化完成 - 退租类型:', terminationForm.terminateType, '选中房源:', selectedRoomIds.value)
}

// 更新房源禁用状态
const updateRoomDisabledStatus = () => {
    houseList.value = houseList.value.map(room => ({
        ...room,
        disabled: terminationForm.terminateType === 0
    }))
}

// 选择全部房源
const selectAllRooms = () => {
    selectedRoomIds.value = (houseList.value || [])
        .map(room => room.roomId?.toString())
        .filter((id): id is string => id !== null && id !== undefined && id !== '')
    console.log('自动选择全部房源:', selectedRoomIds.value)
}

// 房源选择变更
const handleRoomSelectionChange = (selectedKeys: string[]) => {
    // 如果是到期退租，不允许修改房源选择
    if (terminationForm.terminateType === 0) {
        return
    }
    
    // 过滤掉 null、undefined 和空字符串
    const validKeys = selectedKeys.filter(key => key !== null && key !== undefined && key !== '')
    selectedRoomIds.value = validKeys
    console.log('选中的房源ID:', validKeys)
    
    // 选中房源后查询预计退款信息
    if (validKeys.length > 0) {
        getBillInfo()
    }
}

// 退租类型变更
const handleTerminateTypeChange = async () => {
    console.log('退租类型变更:', terminationForm.terminateType, '当前退租原因:', terminationForm.terminateReason)
    
    // 更新房源禁用状态
    updateRoomDisabledStatus()
    
    // 如果选择到期退租，设置退租日期为合同结束日期，且设置默认退租原因
    if (terminationForm.terminateType === 0) {
        // 设置退租日期为合同结束日期
        if (terminateDetail.value?.contract?.endDate) {
            terminationForm.terminateDate = terminateDetail.value.contract.endDate
        }
        // 设置默认退租原因为"到期退租"，但只有在当前没有有效退租原因时才设置
        const hasValidReasons = terminationForm.terminateReason && terminationForm.terminateReason.length > 0 && 
                               terminationForm.terminateReason.some(reason => reason && reason.trim() !== '')
        if (!hasValidReasons || !terminationForm.terminateReason.includes('1')) {
            terminationForm.terminateReason = ['1']
            console.log('到期退租：设置默认退租原因为到期退租')
        }
        // 到期退租时自动选择全部房源且不允许修改
        selectAllRooms()
        
        // 到期退租时，将所有免租期的收费选项设置为"否"
        if (freeList.value && freeList.value.length > 0) {
            freeList.value.forEach(item => {
                item.isCharge = false
            })
            console.log('到期退租：将所有免租期收费设置为"否"')
        }
    } else {
        // 提前退租时清空退租日期，让用户手动选择
        terminationForm.terminateDate = ''
        // 如果当前退租原因只包含"到期退租"，则清空让用户重新选择
        // 否则保留现有选择（用户可能是从到期退租切换过来）
        if (terminationForm.terminateReason.length === 1 && terminationForm.terminateReason[0] === '1') {
            terminationForm.terminateReason = []
            console.log('提前退租：清空默认的到期退租原因')
        } else {
            console.log('提前退租：保留现有退租原因')
        }
    }
    
    console.log('退租类型变更后退租原因:', terminationForm.terminateReason)
    getBillInfo()
}

// 退租日期变更
const handleTerminateDateChange = () => {
        // freeList 【合同退租】- 提前退租的情况，当退租日期在 免租期周期内，则不允许 免租期收费。按钮默认否，置灰。
        if (terminationForm.terminateType === 1) {
        freeList.value.forEach(item => {
            if (item.startDate && item.endDate && item.freeType === 0) {
                if (terminationForm.terminateDate && terminationForm.terminateDate >= item.startDate && terminationForm.terminateDate <= item.endDate) {
                    item.isCharge = false
                    // item.isDisabled = true
                }else{
                    item.isCharge = false
                    // item.isDisabled = false
                }
            }
        })
    }
    getBillInfo()

}

// 免租期收费变更
const handleFreeRentChargeChange = (record: any) => {
    // 如果选择是收费，调用免租期租金接口
    getFreeRentInfo()

/*     if (record.isCharge) {
        getFreeRentInfo()
    } else {
        // 重新获取账单信息
        // getBillInfo()
    } */
}

// 获取账单信息
const getBillInfo = async () => {
    console.log('获取账单信息', props.contractId, selectedRoomIds.value, terminationForm.terminateDate)
    
    // 如果存在 terminateId，则不需要 contractId；如果不存在 terminateId，则需要 contractId
    const contractId = props.terminateId ? terminateDetail.value?.contract?.id : props.contractId
    
    if (!contractId || selectedRoomIds.value.length === 0 || !terminationForm.terminateDate) {
        // 清空退款列表，避免数据残留
        refundList.value = []
        return
    }

    try {
        const params: ContractTerminateInitDto = {
            contractId: contractId,
            roomIds: selectedRoomIds.value,
            terminateType: terminationForm.terminateType,
            terminateDate: terminationForm.terminateDate
        }

        // 这里去除一次 roomids 的 null 数据
        params.roomIds = params.roomIds?.filter(id => id !== null && id !== undefined && id !== '') || []
        console.log('获取账单信息', params)
        const { data } = await getTerminateBill(params)
        console.log('getTerminateBill data', data)
        
        // 处理数据，根据不同类型计算预计退款金额
        refundList.value = (data || []).map(item => {
            const receivedAmount = Number(item.receivedAmount) || 0  // 账单已收金额
            const terminateReceivable = Number(item.terminateReceivable) || 0  // 欠款金额
            
            // 根据费项类型计算预计退款金额
            let refundAmount = 0
            if (item.subjectName === '保证金') {
                // 保证金类型：预计退款金额等于账单已收金额
                refundAmount = receivedAmount
            } else {
                // 其他费项：预计退款金额 = 账单已收金额 - 截至退款日应收
                refundAmount = receivedAmount - terminateReceivable
            }

            // 根据预计退款金额的正负来设置罚没金额
            const penaltyAmount = refundAmount < 0 ? 0 : (Number(item.penaltyAmount) || 0)

            return {
                ...item,
                billAmount: Number(item.totalAmount) || 0,  // 账单应收金额使用原来的totalAmount
                penaltyAmount: penaltyAmount,  // 设置罚没金额
                refundAmount: refundAmount - penaltyAmount,  // 计算最终的预计退款金额
                terminateReceivable: terminateReceivable,
                receivedAmount: receivedAmount,
                actualReceivable: Number(item.actualReceivable) || 0,
                // 添加一个标记，用于前端判断是否可编辑罚没金额
                canEditPenalty: refundAmount >= 0,
                // 添加最大可输入的罚没金额
                maxPenaltyAmount: refundAmount >= 0 ? refundAmount : 0
            }
        })
        console.log('refundList.value', refundList.value)
    } catch (error) {
        console.error('获取账单信息失败:', error)
        // 出错时清空列表
        refundList.value = []
        // Message.error('获取账单信息失败')
    }
}

// 获取免租期租金信息
const getFreeRentInfo = async () => {
    // 如果存在 terminateId，则不需要 contractId；如果不存在 terminateId，则需要 contractId
    const contractId = props.terminateId ? terminateDetail.value?.contract?.id : props.contractId
    
    if (!contractId) {
        return
    }

    try {
        // 获取选中收费的免租期ID
        const freeRentIds = freeList.value
            .filter(item => item.isCharge)
            .map(item => item.id)

        // if (freeRentIds.length === 0) {
        //     return
        // }

        const params: ContractTerminateInitDto = {
            contractId: contractId,
            freeRentIds,
            roomIds: selectedRoomIds.value,
            terminateType: terminationForm.terminateType,
            terminateDate: terminationForm.terminateDate
        }
        if (!params.terminateDate) {
            return
        }

        const { data } = await getTerminateFreeRent(params)
        
        // 将免租期租金信息与现有退款信息合并
        if (data && data.length > 0) {
            // 注意字段区别，根据实际API返回调整
            // 每次都先 清空 费项 是 免租期的
            refundList.value = refundList.value.filter(item => !item.subjectName?.includes('免租期'))


            refundList.value = [...refundList.value, ...data]
        }else{
            refundList.value = refundList.value.filter(item => !item.subjectName?.includes('免租期'))
        }
    } catch (error) {
        console.error('获取免租期租金失败:', error)
        // Message.error('获取免租期租金失败')
    }
}

// 上传文件前校验
const beforeUpload = (file: File) => {
    const allowedTypes = [
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain'
    ]
    
    if (!allowedTypes.includes(file.type)) {
        Message.error('只支持 word/ppt/excel/txt 格式的文件')
        return false
    }
    
    if (file.size > 10 * 1024 * 1024) {
        Message.error('文件大小不能超过 10MB')
        return false
    }
    
    return true
}

// 处理文件上传
const handleUpload = (options: any) => {
    // 这里应该调用实际的文件上传API
    // 暂时模拟上传成功
    setTimeout(() => {
        options.onSuccess()
    }, 1000)
}

// 文件变更
const handleFileChange = (fileList: any[]) => {
    // 处理文件列表变更
}

// 下载文件
const downloadFile = (file: any) => {
    // 这里实现文件下载逻辑
    if (file.url) {
        window.open(file.url, '_blank')
    } else {
        console.log('下载文件:', file)
        Message.info('文件下载功能待实现')
    }
}

// 基础验证方法（用于暂存）
const validateBasic = () => {
    // 暂存时只做最基础的验证
    if (terminationForm.terminateType === null || terminationForm.terminateType === undefined) {
        Message.error('请先选择退租类型')
        return false
    }
    
    if (selectedRoomIds.value.length === 0) {
        Message.error('请先选择要退租的房源')
        return false
    }
    
    return true
}

// 暂存
const handleSave = async (noSaveContractTerminate: boolean) => {
    // 暂存时进行基础验证
    if (!validateBasic()) {
        return
    }
    
    try {
        const data = buildSaveData(false)
        let res = await saveContractTerminate(data)
//         {
//     "msg": "保存成功",
//     "code": 200,
//     "data": "7125d2479cb2bee69be46ad95294601c"
// }
        terminateDetail.value.id = res.data
        // Message.success('暂存成功')
        emit('save', data)
    } catch (error) {
        console.error('暂存失败:', error)
        // Message.error('暂存失败')
    }
}

// 提交申请
const handleSubmit = async () => {
    // 先验证表单
    const isValid = await validate()
    if (!isValid) {
        return
    }
    
    try {
        const data = buildSaveData(true)
        let res =   await saveContractTerminate(data)
        terminateDetail.value.id = res.data
        // Message.success('提交成功')
        emit('submit', data)
    } catch (error) {
        console.error('提交失败:', error)
        // Message.error('提交失败')
    }
}

// 下一步 - 进入出场流程
const handleNext = async () => {
    // 先验证表单
    const isValid = await validate()
    if (!isValid) {
        return
    }
    
    try {
        // 暂存数据，isSubmit 为 false
        const saveData = buildSaveData(false)
        let res = await saveContractTerminate(saveData)
        saveData.id = res.data
        console.log('handleNext', res)
        
        // Message.success('暂存成功，即将进入出场流程')
        emit('next', saveData)
    } catch (error) {
        console.error('暂存失败:', error)
        // Message.error('暂存失败')
    }
}

// 构建保存数据
const buildSaveData = (isSubmit: boolean): ContractTerminateAddDTO => {
    // 如果存在 terminateId，则使用 terminateDetail 中的 contractId；否则使用 props.contractId
    const contractId = props.terminateId ? terminateDetail.value?.contract?.id : props.contractId
    
    // 确保退租原因数据的有效性
    const validTerminateReasons = terminationForm.terminateReason.filter(reason => reason && reason.trim() !== '')
    
    return {
        id: terminateDetail.value?.id,
        contractId: contractId,
        unionId: props.unionId || terminateDetail.value?.contract?.unionId || terminateDetail.value?.unionId,
        terminateType: terminationForm.terminateType,
        terminateDate: terminationForm.terminateDate,
        terminateReason: validTerminateReasons.join(','),
        otherReasonDesc: terminationForm.otherReasonDesc,
        hasOtherDeduction: deductionForm.hasOtherDeduction,
        otherDeductionDesc: deductionForm.otherDeductionDesc,
        terminateRemark: terminationForm.terminateRemark,
        terminateAttachments: JSON.stringify(fileList.value),
        bondReceivedAmount: terminateDetail.value?.bondReceivedAmount,
        rentReceivedAmount: terminateDetail.value?.rentReceivedAmount,
        rentOverdueAmount: terminateDetail.value?.rentOverdueAmount,
        receivedPeriod: terminateDetail.value?.receivedPeriod,
        overduePeriod: terminateDetail.value?.overduePeriod,
        roomList: selectedRoomIds.value.map(roomId => {
            const room = houseList.value.find(item => (item.roomId || item.id) === roomId)
            return {
                roomId,
                area: room?.area || '',
                roomName: room?.roomName || room?.name || ''
            }
        }),
        costList: refundList.value,
        feeList: freeList.value,
        isSubmit,
        // isExit: true
        isExit: props.mode === 'apply-and-exit' ? true : false
    }
}

// 表单验证方法
const validate = async () => {
    console.log('开始验证退租表单，当前退租原因:', terminationForm.terminateReason)
    
    // 基础验证
    if (terminationForm.terminateType === null || terminationForm.terminateType === undefined) {
        Message.error('请选择退租类型')
        return false
    }
    
    if (!terminationForm.terminateDate) {
        Message.error('请选择退租日期')
        return false
    }
    
    // 改进退租原因验证
    if (!terminationForm.terminateReason || terminationForm.terminateReason.length === 0) {
        console.log('验证失败：退租原因为空')
        Message.error('请选择退租原因')
        return false
    }
    
    // 验证退租原因是否包含有效值（非空字符串）
    const validReasons = terminationForm.terminateReason.filter(reason => reason && reason.trim() !== '')
    if (validReasons.length === 0) {
        console.log('验证失败：退租原因无有效值', terminationForm.terminateReason)
        Message.error('请选择有效的退租原因')
        return false
    }
    
    // 如果选择了"其他原因"，验证是否填写了说明
    if (terminationForm.terminateReason.includes('8') && (!terminationForm.otherReasonDesc || terminationForm.otherReasonDesc.trim() === '')) {
        Message.error('选择其他原因时，请填写其他原因说明')
        return false
    }
    
    if (selectedRoomIds.value.length === 0) {
        Message.error('请选择要退租的房源')
        return false
    }
    
    console.log('退租表单验证通过')
    return true
}

// 获取表单数据
const getFormData = () => {
    return buildSaveData(false)
}

// 重新加载数据
const reload = () => {
    if (props.contractId || props.terminateId) {
        loadTerminateDetail()
    }
}

// 暴露方法给父组件
defineExpose({
    handleSave,
    handleSubmit,
    handleNext,
    validate,
    validateBasic,
    getFormData,
    reload
})

// 监听 props 变化，重新加载数据
const { contractId, terminateId, refundType } = toRefs(props)
console.log('toRefs 解构后的 refundType:', refundType)

watch([contractId, terminateId], ([newContractId, newTerminateId]) => {
    if (newContractId || newTerminateId) {
        loadTerminateDetail()
    }
}, { immediate: false })

// 监听 refundType 变化，更新退租类型
watch(refundType, (newRefundType, oldRefundType) => {
    console.log('watch refundType 触发 - 新值:', newRefundType, '旧值:', oldRefundType, '当前terminateType:', terminationForm.terminateType)
    
    if (newRefundType !== undefined && newRefundType !== null && newRefundType !== oldRefundType) {
        console.log('refundType 变化:', oldRefundType, '->', newRefundType)
        
        // 直接更新退租类型，不再检查是否有服务器数据
        // 父组件传入的 refundType 应该具有最高优先级
        terminationForm.terminateType = newRefundType
        console.log('强制更新退租类型为:', newRefundType)
        
        // 根据新的退租类型更新相关状态
        handleTerminateTypeChange()
    } else {
        console.log('refundType 值无效或未发生实际变化，不更新')
        handleTerminateTypeChange()

    }
}, { immediate: false })

// 生命周期
onMounted(async () => {
    // 加载字典数据
    await loadDictData()

    if (props.contractId || props.terminateId) {
        loadTerminateDetail()
    } else if (props.refundType !== undefined && props.refundType !== null) {
        // 如果没有合同ID或退租ID，但有 refundType，直接设置退租类型
        terminationForm.terminateType = props.refundType
        console.log('初始化时设置退租类型为:', props.refundType)
        handleTerminateTypeChange()
    }
})
</script>

<style scoped lang="less">
.contract-termination {
    width: 100%;
    // height: 100%;
    box-sizing: border-box;
    padding: 0 12px;

    .section-title {
        margin: 12px 0;
    }

    .room-selection-tip {
        margin-bottom: 16px;
    }

    .highlight-red {
        :deep(.arco-input) {
            color: var(--color-danger);
        }
    }

    .total-info {
        margin: 16px 0;
        padding: 12px;
        background-color: #f7f8fa;
        border-radius: 4px;
        
        span {
            font-size: 14px;
            
            strong {
                color: #165dff;
                font-weight: 600;
            }
        }
    }

    // 红色行样式
    :deep(.red-row) {
        color: #ff4d4f !important;
        
        .arco-table-cell {
            color: #ff4d4f !important;
        }
        
        .arco-input-number {
            color: #ff4d4f !important;
        }
        
        .arco-input-number .arco-input-inner {
            color: #ff4d4f !important;
        }
    }

    // 附件列表样式
    .attachment-list {
        .attachment-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 8px;
            background-color: #f7f8fa;
            border-radius: 4px;
            
            .attachment-name {
                flex: 1;
                font-size: 14px;
                color: #1d2129;
            }
        }
    }

}
</style>