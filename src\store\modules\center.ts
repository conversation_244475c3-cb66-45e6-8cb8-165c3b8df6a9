import { defineStore } from 'pinia';
import usePermissionStore from '@/store/modules/permission/index';
import router from '@/router';
import { isHttp } from '@/utils/validate';
import piniaPersistConfig from '@/store/helper/persist';
import useTabBarStore from '@/store/modules/tab-bar';

export type CenterType = 1 | 2 | 3 | 4;

export const centerMap = new Map([
    [1, '资管平台'],
    [2, '资产中心'],
    [3, '租赁中心'],
    [4, '佣金中心'],
]);

interface CenterState {
    isInit: boolean;
    currentCenter: number;
    centerList: CenterItem[];
}

interface CenterItem {
    label: string;
    value: number;
}

const useCenterStore = defineStore('center', {
    state: (): CenterState => ({
        isInit: false,
        currentCenter: 1,
        centerList: [],
    }),

    getters: {
        getCurrentCenter: (state) => state.currentCenter,
        getCenterName: (state) => {
            return centerMap.get(state.currentCenter);
        },
    },

    actions: {
        setCurrentCenter(center: CenterType) {
            this.currentCenter = center;
            usePermissionStore()
                .generateRoutes()
                .then((accessRoutes) => {
                    // 根据roles权限生成可访问的路由表
                    accessRoutes.forEach((route: any) => {
                        if (!isHttp(route.path)) {
                            router.addRoute(route); // 动态添加可访问路由表
                        }
                    });
                    // 清空tabbar
                    useTabBarStore().resetTabList();
                    router.push('/');
                });
        },
        setCenterOptions(systemPermissions: number[]) {
            if (this.isInit) {
                return;
            }
            if (!systemPermissions || systemPermissions.length === 0) {
                this.centerList = [];
                this.currentCenter = 1;
                return;
            }
            this.centerList = systemPermissions.map((item: number) => {
                return {
                    label: centerMap.get(item)!,
                    value: item,
                };
            });
            this.currentCenter = systemPermissions[0];
            this.isInit = true;
        },
    },

    persist: piniaPersistConfig('center'),
});

export default useCenterStore;
