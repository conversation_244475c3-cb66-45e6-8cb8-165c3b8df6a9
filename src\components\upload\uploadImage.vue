<template>
    <div class="upload-image-container">
        <div class="multi-upload-container">
            <div class="upload-area">
                <a-upload v-model:file-list="fileList" :limit="limit" :accept="accept || 'image/*'"
                    :max-size="maxSize * 1024 * 1024" :disabled="disabled || readonly" list-type="picture-card"
                    image-preview :custom-request="customRequest" @success="handleSuccess" @change="handleChange"
                    @exceed-limit="handleExceedLimit" @exceed-size="handleExceedSize">
                    <template #upload-button>
                        <div v-if="!readonly" class="upload-trigger">
                            <icon-plus />
                            <div class="upload-text">上传图片</div>
                        </div>
                    </template>
                </a-upload>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import type { FileItem } from '@arco-design/web-vue/es/upload/interfaces'
import { uploadFile } from '@/api/common';

interface Props {
    // 图片列表，用于v-model双向绑定
    modelValue?: string
    // 上传接口地址
    uploadApi?: (params: any) => Promise<any>
    // 接受的图片类型，例如'.jpg,.png'，默认为'image/*'
    accept?: string
    // 单个图片大小限制，单位MB
    maxSize?: number
    // 最大上传图片数量，0表示不限制
    limit?: number
    // 是否为只读模式
    readonly?: boolean
    // 是否禁用上传功能
    disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    accept: '',
    maxSize: 5,
    limit: 1,
    readonly: false,
    disabled: false,
})

const emit = defineEmits(['update:modelValue', 'success', 'error', 'exceed-limit', 'exceed-size'])

// 图片列表
const fileList = ref<FileItem[]>([])

// 防止循环更新的标志
let isUpdatingFileList = false
let isEmittingUpdate = false

// 监听传入的值变化
watch(() => props.modelValue, (val) => {
    try {
        if (isEmittingUpdate) return; // 避免递归触发

        isUpdatingFileList = true;

        if (val) {
            // 字符串转数组
            const list = JSON.parse(val);
            fileList.value = list.map((item: any, index: number) => {
                // 为每个文件生成唯一的uid，用于后续的文件项匹配
                const uid = `file-${Date.now()}-${index}-${Math.random().toString(36).substr(2, 9)}`;
                return {
                    uid,
                    status: 'done',
                    name: item.fileName || '',
                    url: item.fileUrl || ''
                } as FileItem;
            });
        } else {
            fileList.value = [];
        }

        nextTick(() => {
            isUpdatingFileList = false;
        });
    } catch (error) {

    }
}, { deep: true, immediate: true })

// 自定义上传请求
const customRequest = async (options: any) => {
    const { onProgress, onSuccess, onError, fileItem } = options
    const formData = new FormData()
    formData.append('file', fileItem.file)
    try {
        // 如果提供了上传API，则使用它
        let res
        if (typeof props.uploadApi === 'function') {
            res = await props.uploadApi(formData)
        } else {
            // 使用通用上传接口
            res = await uploadFile(formData);
        }

        onProgress({ percent: 100 });
        onSuccess(res);
        Message.success('上传成功');
    } catch (error) {
        onError(error)
        emit('error', error, fileItem)
    }
}

// 处理成功
const handleSuccess = (file: FileItem) => {
    file.name = file.response?.data.fileName
    file.url = file.response?.data.fileUrl
    emit('success', file)
}

// 处理图片变化
const handleChange = (files: FileItem[], file: FileItem) => {
    if (file.status === 'done') {
        const list = files.map((item: any) => {
            return {
                fileName: item.name,
                fileUrl: item.url
            }
        })
        emit('update:modelValue', list.length > 0 ? JSON.stringify(list) : '');
    }
}

// 处理超出数量限制
const handleExceedLimit = (files: FileItem[]) => {
    Message.warning(`最多只能上传${props.limit}个图片`)
    emit('exceed-limit', files)
}

// 处理超出大小限制
const handleExceedSize = (file: FileItem) => {
    Message.warning(`图片大小不能超过${props.maxSize}MB`)
    emit('exceed-size', file)
}
</script>

<style lang="less" scoped>
.upload-image-container {
    width: 100%;

    .upload-trigger {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 80px;
        width: 80px;
        border: 1px dashed #c9cdd4;
        border-radius: 2px;
        cursor: pointer;

        &:hover {
            border-color: #1677ff;
        }

        .upload-text {
            margin-top: 8px;
            color: #4e5969;
            font-size: 12px;
        }
    }
}
</style>