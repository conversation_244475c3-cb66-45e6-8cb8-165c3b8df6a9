<template>
    <div class="group-control-settings">


        <!-- 即将到期提醒 -->
        <div class="form-section">
            <SectionTitle title="即将到期提醒" />
            <div class="expiry-reminder">
                <a-form
                    ref="combinedFormRef"
                    :model="combinedData"
                    :rules="combinedRules"
                    label-align="right"
                    layout="horizontal"
                    auto-label-width
                    :label-col-props="{ span: 6 }"
                    :wrapper-col-props="{ span: 18 }"
                >
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="配置状态">
                                <a-switch v-model="combinedData.expireStatus" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item field="expireDays" label="即将到期：距离合同结束前">
                                <a-input-number
                                    v-model="combinedData.expireDays"
                                    placeholder="请输入天数"
                                    :min="1"
                                    :precision="0"
                                    style="width: 100%"
                                >
                                    <template #append>天</template>
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </div>

        <!-- 空置房源用水电异常 -->
        <div class="form-section">
            <SectionTitle title="空置房源用水电异常" />
            <div class="vacant-utility-alert">
                <a-form
                    :model="combinedData"
                    :rules="combinedRules"
                    label-align="right"
                    layout="horizontal"
                    auto-label-width
                    :label-col-props="{ span: 6 }"
                    :wrapper-col-props="{ span: 18 }"
                >
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="配置状态">
                                <a-switch v-model="combinedData.waterElectricityStatus" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item field="electricityNum" label="每天用电超过">
                                <a-input-number
                                    v-model="combinedData.electricityNum"
                                    placeholder="多少"
                                    :min="0"
                                    :precision="0"
                                    style="width: 100%"
                                >
                                    <template #append>度</template>
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item field="coldWaterNum" label="每天用冷水超过">
                                <a-input-number
                                    v-model="combinedData.coldWaterNum"
                                    placeholder="多少"
                                    :min="0"
                                    :precision="0"
                                    style="width: 100%"
                                >
                                    <template #append>吨</template>
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="hotWaterNum" label="每天用热水超过">
                                <a-input-number
                                    v-model="combinedData.hotWaterNum"
                                    placeholder="多少"
                                    :min="0"
                                    :precision="0"
                                    style="width: 100%"
                                >
                                    <template #append>吨</template>
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
                <!-- 共用保存按钮 -->
                <div class="section-actions">
                    <a-button type="primary" @click="handleSaveCombined" :loading="combinedSaveLoading">
                        保存
                    </a-button>
                </div>
            </div>
        </div>

        <!-- 催缴规则 -->
        <div class="form-section">
            <SectionTitle title="催缴规则" />
            <div class="collection-rules">
                <!-- 统一配置状态 -->
                <div class="rule-header">
                    <a-form-item label="配置状态">
                        <a-switch v-model="globalStatus" @change="handleGlobalStatusChange" />
                    </a-form-item>
                </div>

                <!-- 营收抽点类（固定） -->
                <div class="rule-item">
                    <div class="rule-title">营收抽点类</div>
                    <a-form
                        :model="revenueRule"
                        label-align="right"
                        layout="horizontal"
                        auto-label-width
                        :label-col-props="{ span: 4 }"
                        :wrapper-col-props="{ span: 20 }"
                    >
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item label="符合条件：">
                                    <span>营收提报后且完成审批且月付</span>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="16">
                            <a-col :span="20">
                                <a-form-item label="催缴日期：">
                                    <div class="rule-config">
                                        <div class="config-item" v-for="(config, configIndex) in revenueRule.ruleConfigList" :key="configIndex">
                                            <span>在应付日期</span>
                                            <a-select v-model="config.type" style="width: 70px; margin: 0 8px;">
                                                <a-option :value="1">前</a-option>
                                                <a-option :value="2">后</a-option>
                                            </a-select>
                                            <a-input-number
                                                v-model="config.days"
                                                :min="1"
                                                :precision="0"
                                                style="width: 80px; margin-right: 8px;"
                                                placeholder="天数"
                                            />
                                            <span style="margin-right: 8px;">天生成并发送催缴</span>
                                            <a-button
                                                type="text"
                                                size="small"
                                                @click="removeRevenueRuleConfig(configIndex)"
                                                v-if="revenueRule.ruleConfigList.length > 1"
                                            >
                                                <template #icon>
                                                    <icon-delete />
                                                </template>
                                            </a-button>
                                        </div>
                                        <a-button
                                            type="text"
                                            size="small"
                                            @click="addRevenueRuleConfig"
                                            style="margin-top: 8px; color: #1890ff;"
                                        >
                                            +添加催缴频次
                                        </a-button>
                                    </div>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </div>

                <!-- 固定金额类 -->
                <div class="rule-item">
                    <div class="rule-title">固定金额类</div>
                    <div class="fixed-amount-rules">
                        <div class="fixed-rule-item" v-for="(rule, index) in fixedAmountRules" :key="rule.id || index">
                            <a-form
                                :model="rule"
                                label-align="right"
                                layout="horizontal"
                                auto-label-width
                                :label-col-props="{ span: 4 }"
                                :wrapper-col-props="{ span: 20 }"
                            >
                                <a-row :gutter="16">
                                    <a-col :span="24">
                                        <a-form-item label="符合条件：" style="margin-bottom: 0;">
                                            <a-form-item label="业态">
                                                <a-select v-model="rule.propertyType" placeholder="业态">
                                                    <a-option :value="0">不限</a-option>
                                                    <a-option :value="1">宿舍</a-option>
                                                    <a-option :value="2">非宿舍</a-option>
                                                </a-select>
                                            </a-form-item>
                                            <a-form-item label="客户类型">
                                                <a-select v-model="rule.customerType" placeholder="客户类型">
                                                    <a-option :value="0">不限</a-option>
                                                    <a-option :value="1">个人</a-option>
                                                    <a-option :value="2">企业</a-option>
                                                </a-select>
                                            </a-form-item>
                                            <a-form-item label="支付周期">
                                                <a-select v-model="rule.payPeriod" placeholder="支付周期">
                                                    <a-option :value="0">不限</a-option>
                                                    <a-option :value="1">月付</a-option>
                                                    <a-option :value="2">年付</a-option>
                                                </a-select>
                                            </a-form-item>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="16">
                                    <a-col :span="20">
                                        <a-form-item label="催缴日期：">
                                            <div class="rule-config">
                                                <div class="config-item" v-for="(config, configIndex) in rule.ruleConfigList" :key="configIndex">
                                                    <span>在应付日期</span>
                                                    <a-select v-model="config.type" style="width: 70px; margin: 0 8px;">
                                                        <a-option :value="1">前</a-option>
                                                        <a-option :value="2">后</a-option>
                                                    </a-select>
                                                    <a-input-number
                                                        v-model="config.days"
                                                        :min="1"
                                                        :precision="0"
                                                        style="width: 80px; margin-right: 8px;"
                                                        placeholder="天数"
                                                    />
                                                    <span style="margin-right: 8px;">天生成并发送催缴</span>
                                                    <a-button
                                                        type="text"
                                                        size="small"
                                                        @click="removeRuleConfig(rule, configIndex)"
                                                        v-if="rule.ruleConfigList.length > 1"
                                                    >
                                                        <template #icon>
                                                            <icon-delete />
                                                        </template>
                                                    </a-button>
                                                </div>
                                                <a-button
                                                    type="text"
                                                    size="small"
                                                    @click="addRuleConfig(rule)"
                                                    style="margin-top: 8px; color: #1890ff;"
                                                >
                                                    +添加催缴频次
                                                </a-button>
                                            </div>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="4">
                                        <div class="rule-actions">
                                            <a-button
                                                type="text"
                                                size="small"
                                                @click="removeFixedRule(index)"
                                                v-if="fixedAmountRules.length > 1"
                                            >
                                                <template #icon>
                                                    <icon-delete />
                                                </template>
                                            </a-button>
                                        </div>
                                    </a-col>
                                </a-row>
                            </a-form>
                        </div>
                    </div>
                    <a-button
                        type="text"
                        @click="addFixedRule"
                        style="margin-top: 16px; color: #1890ff;"
                    >
                        +添加条件
                    </a-button>
                </div>
                <!-- 保存按钮 -->
                <div class="section-actions">
                    <a-button type="primary" @click="handleSaveCollectionRules" :loading="collectionRulesSaveLoading">
                        保存
                    </a-button>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconDelete } from '@arco-design/web-vue/es/icon'
import SectionTitle from '@/components/sectionTitle/index.vue'
import { getContractBillConfig, saveContractBillConfig, type ContractBillConfigAddDTO } from '@/api/collectionManagement'
import { getWarningRulesDetail, addWarningRules, type WarningRulesAddDTO } from '@/api/warningRules'

// 规则配置项接口
interface RuleConfigItem {
    type: number // 1前 2后
    days: number // 天数
}

// 催缴规则接口
interface CollectionRule extends ContractBillConfigAddDTO {
    ruleConfigList: RuleConfigItem[]
}

// 表单引用
const combinedFormRef = ref()

// 保存加载状态
const combinedSaveLoading = ref(false)
const collectionRulesSaveLoading = ref(false)

// 合并的数据结构（即将到期提醒 + 空置房源用水电异常）- 直接使用API字段
const combinedData = reactive({
    // 即将到期提醒
    expireStatus: false,           // 即将到期配置状态
    expireDays: 30,               // 合同即将到期前多少天
    // 空置房源用水电异常
    waterElectricityStatus: false, // 水电异常配置状态
    electricityNum: 0,            // 电度数
    coldWaterNum: 0,              // 冷水吨数
    hotWaterNum: 0                // 热水吨数
})



// 全局配置状态
const globalStatus = ref(false)

// 营收抽点规则（固定）
const revenueRule = reactive({
    type: 1,
    propertyType: 0,
    customerType: 0,
    payPeriod: 0,
    status: false,
    ruleConfig: '',
    ruleConfigList: [{ type: 1, days: 1 }],
    isDel: false
})

// 固定金额规则列表
const fixedAmountRules = ref<CollectionRule[]>([
    {
        type: 2,
        propertyType: 0,
        customerType: 0,
        payPeriod: 0,
        status: false,
        ruleConfig: '',
        ruleConfigList: [{ type: 1, days: 1 }],
        isDel: false
    }
])



// 合并的验证规则 - 使用API字段名
const combinedRules = {
    expireDays: [
        { required: true, message: '请输入提醒天数' }
    ],
    electricityNum: [
        { required: true, message: '请输入用电阈值' }
    ],
    coldWaterNum: [
        { required: true, message: '请输入冷水阈值' }
    ],
    hotWaterNum: [
        { required: true, message: '请输入热水阈值' }
    ]
}

// 全局状态变更处理
const handleGlobalStatusChange = (value: boolean) => {
    revenueRule.status = value
    fixedAmountRules.value.forEach(rule => {
        rule.status = value
    })
}

// 添加固定金额规则
const addFixedRule = () => {
    fixedAmountRules.value.push({
        type: 2,
        propertyType: 0,
        customerType: 0,
        payPeriod: 0,
        status: globalStatus.value,
        ruleConfig: '',
        ruleConfigList: [{ type: 1, days: 1 }],
        isDel: false
    })
}

// 删除固定金额规则
const removeFixedRule = (index: number) => {
    if (fixedAmountRules.value.length > 1) {
        fixedAmountRules.value.splice(index, 1)
    }
}

// 添加营收抽点规则配置
const addRevenueRuleConfig = () => {
    revenueRule.ruleConfigList.push({ type: 1, days: 1 })
}

// 删除营收抽点规则配置
const removeRevenueRuleConfig = (configIndex: number) => {
    if (revenueRule.ruleConfigList.length > 1) {
        revenueRule.ruleConfigList.splice(configIndex, 1)
    }
}

// 添加固定金额规则配置
const addRuleConfig = (rule: CollectionRule) => {
    rule.ruleConfigList.push({ type: 1, days: 1 })
}

// 删除固定金额规则配置
const removeRuleConfig = (rule: CollectionRule, configIndex: number) => {
    if (rule.ruleConfigList.length > 1) {
        rule.ruleConfigList.splice(configIndex, 1)
    }
}

// 处理合并保存（即将到期提醒 + 空置房源用水电异常）
const handleSaveCombined = async () => {
    try {
        const errors = await combinedFormRef.value?.validate()
        if (errors) return

        combinedSaveLoading.value = true

        // 构造预警规则数据 - 直接使用API字段
        const warningRulesData: WarningRulesAddDTO = {
            expireStatus: combinedData.expireStatus,
            expireDays: combinedData.expireDays,
            waterElectricityStatus: combinedData.waterElectricityStatus,
            electricityNum: combinedData.electricityNum,
            coldWaterNum: combinedData.coldWaterNum,
            hotWaterNum: combinedData.hotWaterNum,
            billStatus: false, // 催缴状态暂时设为false，由催缴规则单独管理
            isDel: false
        }

        // 调用新增预警规则接口
        console.log('保存预警规则:', warningRulesData)
        const response = await addWarningRules(warningRulesData)

        console.log('预警规则保存响应:', response)
        Message.success('预警规则保存成功')
    } catch (error) {
        console.error('保存失败:', error)
        Message.error('保存失败')
    } finally {
        combinedSaveLoading.value = false
    }
}

// 处理催缴规则保存
const handleSaveCollectionRules = async () => {
    try {
        collectionRulesSaveLoading.value = true

        // 合并所有规则数据，只保留接口需要的字段
        const allRules = [
            // 营收抽点规则
            {
                status: revenueRule.status,
                type: revenueRule.type,
                propertyType: revenueRule.propertyType,
                customerType: revenueRule.customerType,
                payPeriod: revenueRule.payPeriod,
                ruleConfig: JSON.stringify(revenueRule.ruleConfigList),
                isDel: revenueRule.isDel
            },
            // 固定金额规则
            ...fixedAmountRules.value.map(rule => ({
                status: rule.status,
                type: rule.type,
                propertyType: rule.propertyType,
                customerType: rule.customerType,
                payPeriod: rule.payPeriod,
                ruleConfig: JSON.stringify(rule.ruleConfigList),
                isDel: rule.isDel
            }))
        ]

        // 保存催缴规则
        await saveContractBillConfig(allRules)

        Message.success('催缴规则保存成功')
    } catch (error) {
        console.error('催缴规则保存失败:', error)
    } finally {
        collectionRulesSaveLoading.value = false
    }
}





// 加载预警规则数据
const loadWarningRules = async () => {
    try {
        // 获取预警规则详情
        const response = await getWarningRulesDetail()

        if (response && response.data) {
            const data = response.data
            // 直接使用API字段赋值 - 使用 ?? 操作符来处理 null/undefined，避免 0 值被覆盖
            combinedData.expireStatus = data.expireStatus ?? false
            combinedData.expireDays = data.expireDays ?? 30
            combinedData.waterElectricityStatus = data.waterElectricityStatus ?? false
            combinedData.electricityNum = data.electricityNum ?? 0
            combinedData.coldWaterNum = data.coldWaterNum ?? 0
            combinedData.hotWaterNum = data.hotWaterNum ?? 0

            console.log('预警规则数据加载成功:', data)
        } else {
            console.log('未找到预警规则数据，使用默认值')
        }
    } catch (error: any) {
        console.error('加载预警规则数据失败:', error)
    }
}

// 加载催缴规则数据
const loadCollectionRules = async () => {
    try {
        const response = await getContractBillConfig()
        if (response.data && Array.isArray(response.data)) {
            const rules = response.data.map((rule: ContractBillConfigAddDTO) => ({
                ...rule,
                ruleConfigList: rule.ruleConfig ? JSON.parse(rule.ruleConfig) : [{ type: 1, days: 1 }]
            }))

            // 分离营收抽点和固定金额规则
            const revenueRules = rules.filter(rule => rule.type === 1)
            const fixedRules = rules.filter(rule => rule.type === 2)

            // 设置营收抽点规则
            if (revenueRules.length > 0) {
                const rule = revenueRules[0]
                Object.assign(revenueRule, {
                    ...rule,
                    ruleConfigList: rule.ruleConfig ? JSON.parse(rule.ruleConfig) : [{ type: 1, days: 1 }]
                })
            }

            // 设置固定金额规则
            if (fixedRules.length > 0) {
                fixedAmountRules.value = fixedRules
            }

            // 设置全局状态（基于第一个规则的状态）
            if (rules.length > 0) {
                globalStatus.value = rules[0].status ?? false
            }
        }

        // 确保至少有一个固定金额规则
        if (fixedAmountRules.value.length === 0) {
            fixedAmountRules.value = [{
                type: 2,
                propertyType: 0,
                customerType: 0,
                payPeriod: 0,
                status: false,
                ruleConfig: '',
                ruleConfigList: [{ type: 1, days: 1 }],
                isDel: false
            }]
        }
    } catch (error) {
        console.error('加载催缴规则数据失败:', error)
    }
}

onMounted(() => {
    loadCollectionRules()
    loadWarningRules()
})
</script>

<style scoped lang="less">
.group-control-settings {
    padding: 12px;
    box-sizing: border-box;

    .form-section {
        margin-bottom: 16px;
    }

    :deep(.section-title){
        margin-bottom: 16px;
    }

    .collection-rules {
        .rule-header {
            background: var(--color-bg-1);
            border-radius: 6px;
        }

        .rule-item {
            padding: 16px;
            border: 1px solid var(--color-border-2);
            border-radius: 6px;
            margin-bottom: 16px;
            background: var(--color-bg-1);

            &:last-child {
                margin-bottom: 0;
            }

            .rule-title {
                font-weight: 600;
                margin-bottom: 16px;
                color: var(--color-text-1);
            }
        }

        .fixed-amount-rules {
            .fixed-rule-item {
                padding: 12px;
                border: 1px solid var(--color-border-3);
                border-radius: 4px;
                margin-bottom: 12px;
                background: var(--color-bg-2);

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .rule-config {
            .config-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .rule-actions {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            height: 32px;
            .arco-btn-icon{
                font-size: 22px;
            }
        }
    }

    .section-actions {
        display: flex;
        justify-content: flex-end;
        padding-top: 16px;
        margin-top: 16px;
    }

    .expiry-reminder,
    .vacant-utility-alert {
        background: var(--color-bg-1);
        border-radius: 6px;
    }
}
</style>
