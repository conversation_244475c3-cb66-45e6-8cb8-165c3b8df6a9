<template>
    <div class="test-container">
        <a-card title="房源选择组件测试 - 支持三种v-model类型">
            <a-space direction="vertical" size="large" style="width: 100%">
                <!-- ID类型 -->
                <a-card title="ID类型 (valueType='id')" size="small">
                    <a-form :model="idForm" layout="vertical">
                        <a-form-item label="选择房源">
                            <room-tree-select 
                                v-model="idForm.roomId" 
                                value-type="id"
                                @change="handleIdChange"
                                style="width: 300px"
                            />
                        </a-form-item>
                        <a-form-item label="选中的值">
                            <a-input :model-value="String(idForm.roomId)" readonly />
                        </a-form-item>
                        <a-form-item label="值类型">
                            <a-tag color="blue">{{ typeof idForm.roomId }}</a-tag>
                        </a-form-item>
                    </a-form>
                </a-card>

                <!-- Name类型 -->
                <a-card title="Name类型 (valueType='name')" size="small">
                    <a-form :model="nameForm" layout="vertical">
                        <a-form-item label="选择房源">
                            <room-tree-select 
                                v-model="nameForm.roomName" 
                                value-type="name"
                                @change="handleNameChange"
                                style="width: 300px"
                            />
                        </a-form-item>
                        <a-form-item label="选中的值">
                            <a-input :model-value="String(nameForm.roomName)" readonly />
                        </a-form-item>
                        <a-form-item label="值类型">
                            <a-tag color="green">{{ typeof nameForm.roomName }}</a-tag>
                        </a-form-item>
                    </a-form>
                </a-card>

                <!-- Object类型 -->
                <a-card title="Object类型 (valueType='object')" size="small">
                    <a-form :model="objectForm" layout="vertical">
                        <a-form-item label="选择房源">
                            <room-tree-select 
                                v-model="objectForm.roomData" 
                                value-type="object"
                                @change="handleObjectChange"
                                style="width: 300px"
                            />
                        </a-form-item>
                        <a-form-item label="选中的值">
                            <a-textarea :model-value="JSON.stringify(objectForm.roomData, null, 2)" readonly :rows="3" />
                        </a-form-item>
                        <a-form-item label="值类型">
                            <a-tag color="purple">{{ typeof objectForm.roomData }}</a-tag>
                        </a-form-item>
                    </a-form>
                </a-card>

                <!-- 带筛选条件的使用 -->
                <a-card title="带筛选条件的使用" size="small">
                    <a-form :model="filterForm" layout="vertical">
                        <a-row :gutter="16">
                            <a-col :span="6">
                                <a-form-item label="返回值类型">
                                    <a-select v-model="filterForm.valueType" placeholder="请选择">
                                        <a-option value="id">ID</a-option>
                                        <a-option value="name">Name</a-option>
                                        <a-option value="object">Object</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item label="是否自持">
                                    <a-select v-model="filterForm.isCompanySelf" placeholder="请选择" allow-clear>
                                        <a-option :value="true">是</a-option>
                                        <a-option :value="false">否</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item label="产品类型">
                                    <a-input v-model="filterForm.productType" placeholder="请输入产品类型" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item label="类型">
                                    <a-select v-model="filterForm.type" placeholder="请选择" allow-clear>
                                        <a-option :value="1">接收</a-option>
                                        <a-option :value="2">处置</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-form-item label="选择房源">
                            <room-tree-select 
                                v-model="filterForm.selectedValue"
                                :value-type="filterForm.valueType"
                                :is-company-self="filterForm.isCompanySelf"
                                :product-type="filterForm.productType"
                                :type="filterForm.type"
                                @change="handleFilterChange"
                                style="width: 300px"
                            />
                        </a-form-item>
                        <a-form-item label="选中的值">
                            <a-textarea 
                                :model-value="typeof filterForm.selectedValue === 'object' ? JSON.stringify(filterForm.selectedValue, null, 2) : String(filterForm.selectedValue)" 
                                readonly 
                                :rows="3" 
                            />
                        </a-form-item>
                        <a-form-item label="值类型">
                            <a-tag :color="getTypeColor(typeof filterForm.selectedValue)">{{ typeof filterForm.selectedValue }}</a-tag>
                        </a-form-item>
                    </a-form>
                </a-card>

                <!-- 性能优化配置测试 -->
                <a-card title="性能优化配置测试" size="small">
                    <a-form :model="performanceForm" layout="vertical">
                        <a-row :gutter="16">
                            <a-col :span="6">
                                <a-form-item label="启用懒加载">
                                    <a-switch v-model="performanceForm.enableLazyLoad" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item label="启用虚拟滚动">
                                    <a-switch v-model="performanceForm.enableVirtualList" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item label="启用缓存">
                                    <a-switch v-model="performanceForm.enableCache" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item label="分页大小">
                                    <a-input-number v-model="performanceForm.pageSize" :min="10" :max="200" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-form-item label="性能优化房源选择">
                            <room-tree-select 
                                ref="performanceRoomSelectRef"
                                v-model="performanceForm.selectedValue"
                                :enable-lazy-load="performanceForm.enableLazyLoad"
                                :enable-virtual-list="performanceForm.enableVirtualList"
                                :enable-cache="performanceForm.enableCache"
                                :page-size="performanceForm.pageSize"
                                @change="handlePerformanceChange"
                                style="width: 300px"
                            />
                        </a-form-item>
                        <a-form-item label="选中的值">
                            <a-input :model-value="String(performanceForm.selectedValue)" readonly />
                        </a-form-item>
                        <a-form-item label="操作">
                            <a-space>
                                <a-button @click="clearPerformanceCache">清理缓存</a-button>
                                <a-button @click="refreshPerformanceData">刷新数据</a-button>
                                <a-button @click="measurePerformance">性能测试</a-button>
                            </a-space>
                        </a-form-item>
                    </a-form>
                </a-card>

                <!-- 搜索过滤功能测试 -->
                <a-card title="搜索过滤功能测试" size="small">
                    <a-form :model="searchForm" layout="vertical">
                        <a-form-item label="搜索过滤说明">
                            <a-alert 
                                type="info" 
                                message="搜索过滤规则"
                                description="1. 房间节点：匹配房间名称；2. 楼层/楼栋节点：匹配自身名称或有匹配的子节点；3. 如果父节点的所有子节点都不匹配，则隐藏父节点"
                                show-icon
                            />
                        </a-form-item>
                        <a-form-item label="搜索过滤房源选择">
                            <room-tree-select 
                                v-model="searchForm.selectedValue"
                                :enable-lazy-load="false"
                                :enable-virtual-list="false"
                                :enable-cache="true"
                                @change="handleSearchChange"
                                style="width: 300px"
                                placeholder="请选择房源（支持搜索过滤）"
                            />
                        </a-form-item>
                        <a-form-item label="选中的值">
                            <a-input :model-value="String(searchForm.selectedValue)" readonly />
                        </a-form-item>
                        <a-form-item label="测试搜索关键词">
                            <a-space wrap>
                                <a-tag 
                                    v-for="keyword in searchKeywords" 
                                    :key="keyword"
                                    color="blue"
                                    style="cursor: pointer"
                                    @click="testSearchKeyword(keyword)"
                                >
                                    {{ keyword }}
                                </a-tag>
                            </a-space>
                        </a-form-item>
                        <a-form-item label="搜索说明">
                            <a-typography-text type="secondary">
                                点击上方标签可以快速测试不同的搜索关键词，观察父节点的显示/隐藏效果。
                                例如搜索"101"会显示包含101的房间及其父级楼层和楼栋，
                                而搜索不存在的关键词会隐藏所有没有匹配子节点的父节点。
                            </a-typography-text>
                        </a-form-item>
                    </a-form>
                </a-card>

                <!-- 选中房源信息展示 -->
                <a-card title="选中房源信息" size="small" v-if="selectedRoomData">
                    <a-descriptions :column="2" bordered>
                        <a-descriptions-item label="房源ID">{{ selectedRoomData.id }}</a-descriptions-item>
                        <a-descriptions-item label="房源名称">{{ selectedRoomData.roomName }}</a-descriptions-item>
                        <a-descriptions-item label="楼栋名称">{{ selectedRoomData.buildingName }}</a-descriptions-item>
                        <a-descriptions-item label="楼层名称">{{ selectedRoomData.floorName }}</a-descriptions-item>
                        <a-descriptions-item label="地块名称">{{ selectedRoomData.parcelName }}</a-descriptions-item>
                        <a-descriptions-item label="产品类型">{{ selectedRoomData.productType }}</a-descriptions-item>
                        <a-descriptions-item label="建筑面积">{{ selectedRoomData.buildArea }}㎡</a-descriptions-item>
                        <a-descriptions-item label="套内面积">{{ selectedRoomData.innerArea }}㎡</a-descriptions-item>
                        <a-descriptions-item label="是否可售">{{ selectedRoomData.isSale ? '是' : '否' }}</a-descriptions-item>
                        <a-descriptions-item label="是否自持">{{ selectedRoomData.isCompanySelf ? '是' : '否' }}</a-descriptions-item>
                        <a-descriptions-item label="房间状态">{{ getRoomStatusName(selectedRoomData.status) }}</a-descriptions-item>
                        <a-descriptions-item label="面积类型">{{ selectedRoomData.areaTypeName }}</a-descriptions-item>
                    </a-descriptions>
                </a-card>

                <!-- 使用说明 -->
                <a-alert 
                    type="info" 
                    title="使用说明"
                >
                    <template #description>
                        <div>
                            <p><strong>房源选择组件支持三种v-model类型：</strong></p>
                            <ul>
                                <li><strong>id</strong>: 返回房源ID (string | number)</li>
                                <li><strong>name</strong>: 返回房源名称 (string)</li>
                                <li><strong>object</strong>: 返回包含id和name的对象 ({ id: string | number, name: string })</li>
                            </ul>
                            <p>通过 <code>value-type</code> 属性指定返回值类型，默认为 'id'。</p>
                        </div>
                    </template>
                </a-alert>

                <!-- 操作按钮 -->
                <a-card title="测试操作" size="small">
                    <a-space>
                        <a-button @click="clearAllSelections">清空所有选择</a-button>
                        <a-button @click="setTestValues">设置测试值</a-button>
                        <a-button @click="logAllValues">打印所有值</a-button>
                    </a-space>
                </a-card>
            </a-space>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import RoomTreeSelect from './components/roomTreeSelect/index.vue'
import type { SysRoomVo } from './api/room'

// ID类型表单
const idForm = reactive({
    roomId: null as string | number | null
})

// Name类型表单
const nameForm = reactive({
    roomName: null as string | null
})

// Object类型表单
const objectForm = reactive({
    roomData: null as { id: string | number; name: string } | null
})

// 筛选表单
const filterForm = reactive({
    selectedValue: null as any,
    valueType: 'id' as 'id' | 'name' | 'object',
    isCompanySelf: undefined as boolean | undefined,
    productType: '',
    type: undefined as number | undefined
})

// 性能优化表单
const performanceForm = reactive({
    selectedValue: null as any,
    enableLazyLoad: false,
    enableVirtualList: false,
    enableCache: false,
    pageSize: 100
})

// 搜索表单
const searchForm = reactive({
    selectedValue: null as any
})

// 选中的房源数据
const selectedRoomData = ref<SysRoomVo | null>(null)

// 测试搜索关键词列表
const searchKeywords = ref([
    '101', '102', '201', '301', // 房间号
    'A栋', 'B栋', 'C栋', // 楼栋名
    '1层', '2层', '3层', // 楼层名
    '住宅', '商业', '办公', // 产品类型
    '不存在的关键词' // 测试无匹配结果
])

// 房间状态名称映射
const getRoomStatusName = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '初始',
        1: '接收',
        2: '处置'
    }
    return statusMap[status || 0] || '未知'
}

// 获取类型颜色
const getTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
        'string': 'green',
        'number': 'blue',
        'object': 'purple',
        'undefined': 'gray'
    }
    return colorMap[type] || 'default'
}

// ID类型选择变化处理
const handleIdChange = (value: string | number | null, roomData: SysRoomVo) => {
    console.log('ID类型选择变化:', { value, roomData, type: typeof value })
    selectedRoomData.value = roomData
    if (value) {
        Message.success(`已选择房源ID: ${value}`)
    } else {
        Message.info('已清空ID选择')
        selectedRoomData.value = null
    }
}

// Name类型选择变化处理
const handleNameChange = (value: string | null, roomData: SysRoomVo) => {
    console.log('Name类型选择变化:', { value, roomData, type: typeof value })
    selectedRoomData.value = roomData
    if (value) {
        Message.success(`已选择房源名称: ${value}`)
    } else {
        Message.info('已清空Name选择')
        selectedRoomData.value = null
    }
}

// Object类型选择变化处理
const handleObjectChange = (value: { id: string | number; name: string } | null, roomData: SysRoomVo) => {
    console.log('Object类型选择变化:', { value, roomData, type: typeof value })
    selectedRoomData.value = roomData
    if (value) {
        Message.success(`已选择房源对象: ${value.name} (${value.id})`)
    } else {
        Message.info('已清空Object选择')
        selectedRoomData.value = null
    }
}

// 筛选选择变化处理
const handleFilterChange = (value: any, roomData: SysRoomVo) => {
    console.log('筛选选择变化:', { value, roomData, type: typeof value, valueType: filterForm.valueType })
    selectedRoomData.value = roomData
    if (value) {
        const displayValue = typeof value === 'object' ? `${value.name} (${value.id})` : value
        Message.success(`已选择房源: ${displayValue}`)
    } else {
        Message.info('已清空筛选选择')
        selectedRoomData.value = null
    }
}

// 性能优化选择变化处理
const handlePerformanceChange = (value: any, roomData: SysRoomVo) => {
    console.log('性能优化选择变化:', { value, roomData, type: typeof value })
    selectedRoomData.value = roomData
    if (value) {
        const displayValue = typeof value === 'object' ? `${value.name} (${value.id})` : value
        Message.success(`已选择房源: ${displayValue}`)
    } else {
        Message.info('已清空性能优化选择')
        selectedRoomData.value = null
    }
}

// 搜索选择变化处理
const handleSearchChange = (value: any, roomData: SysRoomVo) => {
    console.log('搜索选择变化:', { value, roomData, type: typeof value })
    selectedRoomData.value = roomData
    if (value) {
        const displayValue = typeof value === 'object' ? `${value.name} (${value.id})` : value
        Message.success(`已选择房源: ${displayValue}`)
    } else {
        Message.info('已清空搜索选择')
        selectedRoomData.value = null
    }
}

// 清空所有选择
const clearAllSelections = () => {
    idForm.roomId = null
    nameForm.roomName = null
    objectForm.roomData = null
    filterForm.selectedValue = null
    performanceForm.selectedValue = null
    searchForm.selectedValue = null
    selectedRoomData.value = null
    Message.info('已清空所有选择')
}

// 设置测试值
const setTestValues = () => {
    // 这里设置一些测试值，实际使用时需要根据真实数据调整
    idForm.roomId = 'test-room-001'
    nameForm.roomName = '测试房间001'
    objectForm.roomData = { id: 'test-room-002', name: '测试房间002' }
    filterForm.selectedValue = 'test-room-003'
    performanceForm.selectedValue = 'test-room-004'
    searchForm.selectedValue = 'test-room-005'
    Message.success('已设置测试值')
}

// 打印所有值
const logAllValues = () => {
    console.log('所有表单值:', {
        idForm: idForm.roomId,
        nameForm: nameForm.roomName,
        objectForm: objectForm.roomData,
        filterForm: filterForm.selectedValue,
        performanceForm: performanceForm.selectedValue,
        searchForm: searchForm.selectedValue
    })
    Message.info('已在控制台打印所有值')
}

// 清理性能优化缓存
const clearPerformanceCache = () => {
    performanceForm.selectedValue = null
    Message.info('已清理性能优化缓存')
}

// 刷新性能优化数据
const refreshPerformanceData = () => {
    performanceForm.selectedValue = 'test-room-006'
    Message.success('已刷新性能优化数据')
}

// 性能测试
const measurePerformance = () => {
    // 这里实现性能测试逻辑
    Message.info('性能测试逻辑未实现')
}

// 测试搜索关键词
const testSearchKeyword = (keyword: string) => {
    // 这里实现测试搜索关键词的逻辑
    Message.info(`测试搜索关键词: ${keyword}`)
}
</script>

<style scoped lang="less">
.test-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}
</style> 