<template>
  <div class="container">
    <a-card class="general-card">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form :model="formModel" :label-col-props="{ span: 7 }" :wrapper-col-props="{ span: 17 }"
            label-align="right">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="category" label="种类">
                  <a-select v-model="formModel.category" placeholder="请选择种类" allow-clear>
                    <a-option :value="1">家电</a-option>
                    <a-option :value="2">家具</a-option>
                    <a-option :value="3">装饰</a-option>
                    <a-option :value="4">其他</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="name" label="物品名称">
                  <a-input v-model="formModel.name" placeholder="请输入物品名称" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <!-- <a-divider style="height: 84px" direction="vertical" /> -->
        <a-col :flex="'86px'" style="text-align: right">
          <a-space :size="18">
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              查询
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0;" />
      
      <!-- 操作按钮区域 -->
      <a-row style="margin-bottom: 12px">
        <a-col :span="24" style="text-align: right">
          <a-space>
            <a-button v-permission="['asset:assets:add']" type="primary" @click="handleAdd">
              <template #icon>
                <icon-plus />
              </template>
              新增
            </a-button>
            <!-- <a-button @click="handleExport">
              <template #icon>
                <icon-download />
              </template>
              导出
            </a-button> -->
          </a-space>
        </a-col>
      </a-row>
      
      <!-- 表格区域 -->
      <a-table 
        row-key="id" 
        :loading="loading" 
        :pagination="pagination" 
        :columns="columns" 
        :data="tableData"
        :bordered="{ cell: true }" 
        :scroll="{x: 1200}" 
        @page-change="onPageChange" 
        @page-size-change="onPageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
        </template>
        <template #category="{ record }">
          {{ getCategoryText(record.category) }}
        </template>
        <template #usageScope="{ record }">
          {{ getUsageScopeText(record.usageScope) }}
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button v-permission="['asset:assets:edit']" type="text" size="mini" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-button v-permission="['asset:assets:detail']" type="text" size="mini" @click="handleDetail(record)">
              详情
            </a-button>
            <a-button v-permission="['asset:assets:remove']" type="text" size="mini" status="danger" @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>
    
    <!-- 新增/编辑弹窗 -->
    <AddEditModal 
      v-model:visible="addEditVisible" 
      :edit-data="editData"
      :usage-scope-options="usageScopeOptions"
      @success="handleSuccess"
    />
    
    <!-- 详情弹窗 -->
    <DetailModal 
      v-model:visible="detailVisible" 
      :detail-data="detailData"
      :usage-scope-options="usageScopeOptions"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { 
  getFixedAssetsList, 
  deleteFixedAssets, 
  exportFixedAssets,
  getFixedAssetsDetail,
  type FixedAssetsQueryDTO, 
  type FixedAssetsVo 
} from '@/api/fixedAssets'
import { getDicts } from '@/api/system/dict'
import { exportExcel } from '@/utils/exportUtil'
import AddEditModal from './components/AddEditModal.vue'
import DetailModal from './components/DetailModal.vue'

// 字典数据
const usageScopeOptions = ref<any[]>([])

// 表格数据
const loading = ref(false)
const tableData = ref<FixedAssetsVo[]>([])

// 弹窗控制
const addEditVisible = ref(false)
const detailVisible = ref(false)
const editData = ref<FixedAssetsVo | null>(null)
const detailData = ref<FixedAssetsVo | null>(null)

// 表格列定义
const columns = [
  {
    title: '序号',
    slotName: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '种类',
    slotName: 'category',
    width: 100,
    ellipsis: true,
    tooltip: true,
    align: 'center',
  },
  {
    title: '物品名称',
    dataIndex: 'name',
    width: 150,
    ellipsis: true,
    tooltip: true,
    align: 'center',
  },
  {
    title: '规格',
    dataIndex: 'specification',
    width: 150,
    ellipsis: true,
    tooltip: true,
    align: 'center',
  },
  {
    title: '使用范围',
    slotName: 'usageScope',
    width: 150,
    ellipsis: true,
    tooltip: true,
    align: 'center',
  },
  {
    title: '描述',
    dataIndex: 'remark',
    width: 200,
    ellipsis: true,
    tooltip: true,
    align: 'center',
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 180,
    align: 'center',
    ellipsis: false,
    tooltip: false,
    fixed: 'right',
  }
]

// 分页
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showPageSize: true
})

// 表单
const formModel = reactive({
  category: undefined as number | undefined,
  name: ''
})

// 种类映射
const getCategoryText = (category: number) => {
  const categoryMap: Record<number, string> = {
    1: '家电',
    2: '家具', 
    3: '装饰',
    4: '其他'
  }
  return categoryMap[category] || '未知'
}

// 使用范围映射
const getUsageScopeText = (usageScope: string) => {
  if (!usageScope) return ''
  
  // 处理多选情况
  const scopes = usageScope.split(',')
  const scopeLabels: string[] = []
  
  // 在字典数据中查找对应的文本
  const findOption = (options: any[], value: string): string | null => {
    if (!options || !options.length) return null
    
    for (const option of options) {
      if (option.dictValue === value) {
        return option.dictLabel
      }
      if (option.childList && option.childList.length > 0) {
        const childResult = findOption(option.childList, value)
        if (childResult) return childResult
      }
    }
    return null
  }
  
  scopes.forEach(scope => {
    const label = findOption(usageScopeOptions.value, scope)
    if (label) {
      scopeLabels.push(label)
    }
  })
  
  return scopeLabels.join('、')
}

// 查询
const search = async () => {
  pagination.current = 1
  await loadData()
}

// 重置
const reset = () => {
  formModel.category = undefined
  formModel.name = ''
  pagination.current = 1
  loadData()
}

// 加载字典数据
const loadUsageScopeDict = async () => {
  try {
    const response = await getDicts('diversification_purpose')
    if (response.code === 200) {
      const flatData = response.data || []
      
      // 定义数据项接口
      interface DictItem {
        dictCode: number;
        parentCode: number | null;
        dictSort: number;
        dictLabel: string;
        dictValue: string;
        dictType: string;
        cssClass: string | null;
        listClass: string | null;
        isDefault: string | null;
        status: string;
        remark: string;
        createTime: string;
        childList: DictItem[];
      }
      
      // 构建树形结构
      const buildTree = (items: any[], parentCode: number | null = null): DictItem[] => {
        return items
          .filter(item => item.parentCode === parentCode)
          .map(item => ({
            ...item,
            childList: buildTree(items, item.dictCode)
          }))
      }
      
      // 生成树形结构
      usageScopeOptions.value = buildTree(flatData)
    } else {
      console.error('加载使用范围字典失败:', response.msg)
    }
  } catch (error) {
    console.error('加载使用范围字典失败:', error)
  }
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    const params: FixedAssetsQueryDTO = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      category: formModel.category,
      name: formModel.name || undefined
    }
    
    const response = await getFixedAssetsList(params)
    tableData.value = response.rows || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 分页变化
const onPageChange = (page: number) => {
  pagination.current = page
  loadData()
}

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  loadData()
}

// 新增
const handleAdd = () => {
  // 确保清空编辑数据
  editData.value = null
  
  // 延迟打开弹窗，确保数据已清空并且不会被其他逻辑修改
  setTimeout(() => {
    addEditVisible.value = true
  }, 10)
}

// 编辑
const handleEdit = (record: FixedAssetsVo) => {
  // 获取最新数据
  getFixedAssetsDetail(record.id!).then(res => {
    editData.value = res.data
    addEditVisible.value = true
  }).catch(err => {
    console.error('获取详情失败:', err)
    Message.error('获取详情失败')
  })
}

// 详情
const handleDetail = (record: FixedAssetsVo) => {
  getFixedAssetsDetail(record.id!).then(res => {
    detailData.value = res.data
    detailVisible.value = true
  })
}

// 删除
const handleDelete = (record: FixedAssetsVo) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除固定资产"${record.name}"吗？`,
    onOk: async () => {
      try {
        await deleteFixedAssets(record.id!)
        Message.success('删除成功')
        loadData()
      } catch (error) {
        console.error('删除失败:', error)
      }
    }
  })
}

// 导出
const handleExport = async () => {
  try {
    const params = {
      params: {},
      pageNum: 1,
      pageSize: 999999,
      category: formModel.category?.toString(),
      name: formModel.name || undefined
    }
    
    await exportExcel(exportFixedAssets, params, '固定资产列表')
    Message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 操作成功回调
const handleSuccess = () => {
  // 只负责重新加载数据，模态窗口的关闭由子组件控制
  // 确保完全清空编辑数据
  setTimeout(() => {
    editData.value = null
  }, 0)
  loadData()
}

// 初始化
onMounted(() => {
  loadUsageScopeDict()
  loadData()
})
</script>

<style scoped lang="less">
.container {
    padding: 0 16px 0 16px;
    .general-card {
        box-sizing: border-box;
        padding-top: 16px;
    }

}
</style> 