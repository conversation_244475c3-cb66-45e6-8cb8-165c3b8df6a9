<template>
    <section>
        <sectionTitle :title="contractData.contractType === 2 ? '多经点位信息' : '房源信息'" />
        <div class="content">
            <a-form v-if="editType !== 'detail' && editType !== 'change' && editType !== 'changeDetail' && editType !== 'updateBank' && editType !== 'updateContact' && editType !== 'updateSignMethod' && editType !== 'updateRentNo'" :model="formData" auto-label-width
                style="margin-bottom: 16px;">
                <a-space :size="20">
                    <a-form-item :label="contractData.contractType === 2 ? '多经点位' : '房源'" style="margin-bottom: 0"
                        :rules="disabledFields.includes('roomName') ? [] : [{ required: true, message: '请选择' + (contractData.contractType === 2 ? '多经点位' : '房源') }]">
                        <a-input :disabled="disabledFields.includes('roomName')" v-model="roomNames"
                            :placeholder="'请选择' + (contractData.contractType === 2 ? '多经点位' : '房源')"
                            style="width: 300px" readonly @click="handleOpenRoomDrawer"></a-input>
                    </a-form-item>
                    <a-typography-text type="warning" v-if="!disabledFields.includes('roomName')">
                        <a-space>
                            <icon-exclamation-circle-fill size="16" />
                            更改{{ contractData.contractType === 2 ? '多经点位' : '房源' }}后下方需重新维护
                        </a-space>
                    </a-typography-text>
                </a-space>
            </a-form>
            <a-table :data="contractData.rooms" :bordered="{ cell: true }" :pagination="{ showJumper: true }" summary
                summary-text="合计" :scroll="{ x: 1200 }">
                <template #columns>
                    <a-table-column title="房源" data-index="roomName" :width="100" align="center" ellipsis
                        tooltip>
                        <template #cell="{ record }">
                            <span>{{ record.parcelName+'-'+record.buildingName+'-'+record.roomName }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="面积（㎡）" data-index="area" :width="60" align="center"></a-table-column>
                    <a-table-column title="标准租金（单价）" data-index="standardUnitPrice" :width="80" align="center">
                        <template #cell="{ record }">
                            <span>{{ formatAmount(record.standardUnitPrice) }} {{ unitMap.get(record.priceUnit)
                            }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="折扣" data-index="discount" :width="80" align="center">
                        <template #title>
                            <div style="display: flex; align-items: center;gap: 8px">
                                <span style="flex: 0 0 auto;">折扣</span>
                                <a-input-number
                                    v-if="editType !== 'detail' && editType !== 'change' && editType !== 'changeDetail' && editType !== 'updateBank' && editType !== 'updateContact' && editType !== 'updateSignMethod' && editType !== 'updateRentNo'"
                                    v-model="discount" style="background-color: #fff;" @change="applyDiscountToAll">
                                    <template #suffix>%</template>
                                </a-input-number>
                            </div>
                        </template>
                        <template #cell="{ record }">
                            <a-input-number
                                v-if="editType !== 'detail' && editType !== 'change' && editType !== 'changeDetail' && editType !== 'updateBank' && editType !== 'updateContact' && editType !== 'updateSignMethod' && editType !== 'updateRentNo'"
                                v-model="record.discount">
                                <template #suffix>%</template>
                            </a-input-number>
                            <span v-else>{{ record.discount }}%</span>
                        </template>

                    </a-table-column>
                    <a-table-column title="签约单价" data-index="signedUnitPrice" :width="80" align="center">
                        <template #cell="{ record }">
                            <a-input-number
                                v-if="editType !== 'detail' && editType !== 'change'&& editType !== 'changeDetail' && editType !== 'updateBank' && editType !== 'updateContact' && editType !== 'updateSignMethod' && editType !== 'updateRentNo'"
                                v-model="record.signedUnitPrice" :formatter="formatter" :parser="parser"
                                @change="(val: number) => { lastEditType = 'signedUnitPrice'; updateDiscount(record) }" :precision="8">
                                <template #suffix>{{ unitMap.get(record.priceUnit) }}</template>
                            </a-input-number>
                            <span v-else>{{ record.signedUnitPrice.toFixed(8) }}{{ unitMap.get(record.priceUnit) }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="签约月总价（元/月）" data-index="signedMonthlyPrice" :width="80" align="center">
                        <template #cell="{ record }">
                            {{ formatAmount(record.signedMonthlyPrice) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="操作" :width="60" fixed="right" v-if="editType !== 'detail' && editType !== 'change' && editType !== 'changeDetail' && editType !== 'updateBank' && editType !== 'updateContact' && editType !== 'updateSignMethod' && editType !== 'updateRentNo'" align="center">
                        <template #cell="{ record, rowIndex }">
                            <a-button type="text" size="mini" @click="removeRoom(record, rowIndex)">移除</a-button>
                        </template>
                    </a-table-column>
                </template>
                <template #summary-cell="{ column }">
                    <template v-if="column.dataIndex === 'roomName'">合计</template>
                    <template v-if="column.dataIndex === 'area'">{{ calculateAreaTotal }}</template>
                    <template v-if="column.dataIndex === 'signedUnitPrice'">{{ formatAmount8(Number(calculateUnitAvg))
                    }}</template>
                    <template v-if="column.dataIndex === 'signedMonthlyPrice'">{{
                        formatAmount(Number(calculateTotalSum)) }}</template>
                </template>
            </a-table>
        </div>

        <selectRoomsDrawer ref="selectRoomRef" @submit="getRoomList" />
    </section>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import selectRoomsDrawer from './selectRoomsDrawer.vue';
import { useContractStore } from '@/store/modules/contract'
import { ContractRoomDTO } from '@/api/contract';
import { Message } from '@arco-design/web-vue';
import { getOrderList } from '@/api/orderManagement'
import { getRoomTree, type RoomTreeQueryDTO, type RoomTreeVo } from '@/api/room'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)
const changeType = computed(() => contractStore.changeType) // 变更类型

// 在房源树中递归查找指定的房源ID
const findRoomInTree = (treeData: RoomTreeVo[], targetRoomId: string): RoomTreeVo | null => {
    for (const node of treeData) {
        // 检查当前节点是否是目标房源
        if (node.roomId === targetRoomId) {
            return node
        }
        // 递归查找子节点
        if (node.children && node.children.length > 0) {
            const found = findRoomInTree(node.children, targetRoomId)
            if (found) {
                return found
            }
        }
    }
    return null
}

// 检查是否有预选房源ID并自动填充
const checkAndFillPreSelectedRoom = async () => {
    console.log('contractStore.preSelectedRoomId', contractStore.preSelectedRoomId)
    if (contractStore.preSelectedRoomId) {
        try {
            // 使用getRoomTree接口获取房源树数据
            // 这里需要传递必要的查询参数，可能需要项目ID等
            const queryParams: RoomTreeQueryDTO = {
                projectId: contractData.value.projectId || contractStore.currentProjectId,
                // 可以添加其他必要的查询参数
                contractType: contractData.value.contractType?.toString()
            }
            console.log('queryParams', queryParams)
            const response = await getRoomTree(queryParams)
            if (response.code === 200 && response.data) {
                // 在房源树中查找目标房源
                const roomData = findRoomInTree(response.data, contractStore.preSelectedRoomId)
                console.log('roomData', roomData)
                if (roomData) {
                    // 构建房源信息，格式与selectRoomsDrawer的handleCheck函数完全一致
                    const roomInfo: ContractRoomDTO = {
                        id: roomData.id,
                        contractId: undefined,
                        roomId: roomData.roomId || '',
                        roomName: roomData.roomName || '',
                        area: roomData.rentArea || 0,
                        standardUnitPrice: roomData.price || 0,
                        bottomPrice: roomData.bottomPrice || 0,
                        priceUnit: roomData.priceUnit || 0,
                        discount: 100,
                        signedUnitPrice: 0,
                        signedMonthlyPrice: 0,
                        startDate: '', // RoomTreeVo中没有startDate字段
                        endDate: '', // RoomTreeVo中没有endDate字段
                        propertyType: roomData.propertyType || '',
                        bondPriceType: undefined, // RoomTreeVo中没有depositType字段
                        bondPrice: undefined, // RoomTreeVo中没有depositAmount字段
                        buildingName: roomData.buildingName || '',
                        floorName: '', // RoomTreeVo中没有floorName字段
                        parcelName: roomData.parcelName || '',
                        stageName: '', // RoomTreeVo中没有stageName字段
                    }

                    // 设置房源数据，使用与getRoomList相同的逻辑
                    contractData.value.rooms = [roomInfo]
                    contractData.value.contractPurpose = Number(roomData.propertyType)

                    // 设置全局折扣值（与监听函数逻辑一致）
                    const firstDiscount = roomInfo.discount
                    discount.value = firstDiscount

                    // 计算签约金额
                    calcAmount(roomInfo)

                    // 加载有效订单
                    loadValidOrders()

                    // 清除预选房源ID，避免重复加载
                    contractStore.preSelectedRoomId = ''
                } else {
                    console.warn('在房源树中未找到指定的房源ID:', contractStore.preSelectedRoomId)
                }
            }
        } catch (error) {
            console.error('自动填充房源信息失败:', error)
        }
    }
}

const unitMap = new Map([
    [1, '元/平方米/月'],
    [2, '元/月']
])

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}
// 金额格式化
const formatAmount8 = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 8,
        maximumFractionDigits: 8
    })
}

const formatter = (value: string) => {
    const values = value.split('.');
    values[0] = values[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return values.join('.')
};

const parser = (value: string) => {
    return value.replace(/,/g, '')
};

/**
 * @description 编辑类型
 * 1. 续签
 *    - 房源不能修改
 * 2. 详情
 *    - 不展示房源选择，不能修改
 */
const editType = computed(() => contractStore.editType)
const disabledFields = computed(() => {
    if (editType.value === 'renew') {
        return ['roomName']
    }
    return []
})

const formData = ref({})

const roomNames = computed(() => {
    return contractData.value.rooms?.map(item => item.roomName).join(',')
})

const discount = ref()

// 新增：标记最近主动编辑字段
let lastEditType: 'signedUnitPrice' | 'discount' | null = null

// 将标头折扣应用到所有房源
const applyDiscountToAll = (val: number) => {
    if (!contractData.value.rooms?.length) return
    contractData.value.rooms.forEach(room => {
        room.discount = val
        lastEditType = 'discount'
        updateSignedUnitPrice(room)
    })
}

// 计算签约单价和签约总价（只计算总价）
const calcAmount = (room: ContractRoomDTO) => {
    if (room.signedUnitPrice && room.area) {
        if (room.priceUnit === 1) {
            room.signedMonthlyPrice = Number((room.signedUnitPrice * room.area).toFixed(2))
        } else if (room.priceUnit === 2) {
            room.signedMonthlyPrice = room.signedUnitPrice
        }
    } else {
        room.signedMonthlyPrice = 0
    }
}

// 只在签约单价主动输入时反算折扣
const updateDiscount = (room: ContractRoomDTO) => {
    if (lastEditType && lastEditType === 'signedUnitPrice') {
        if (room.standardUnitPrice && room.signedUnitPrice) {
            room.discount = Number(((room.signedUnitPrice / room.standardUnitPrice) * 100).toFixed(2))
        } else {
            room.discount = 0
        }
        calcAmount(room)
    }
}

// 只在折扣主动输入时反算签约单价
const updateSignedUnitPrice = (room: ContractRoomDTO) => {
    if (lastEditType && lastEditType === 'discount') {
        if (room.standardUnitPrice && room.discount) {
            console.log('room.standardUnitPrice && room.discount', room.standardUnitPrice , room.discount)
            room.signedUnitPrice = Number((room.standardUnitPrice * room.discount / 100).toFixed(8))
        } else {
            room.signedUnitPrice = 0
        }
        calcAmount(room)
    }
}

// 监听房源数据变化，初始化折扣值
watch(() => contractData.value.rooms, (newRooms) => {
    if (newRooms?.length > 0) {
        // 如果所有房源折扣相同，则设置全局折扣值
        const firstDiscount = newRooms[0].discount
        const allSameDiscount = newRooms.every(room => room.discount === firstDiscount)
        if (allSameDiscount) {
            discount.value = firstDiscount
        }
        // 确保所有房源都计算了签约月总价
        console.log('newRooms', newRooms)
        newRooms.forEach(room => {
            calcAmount(room)
        })
    }
}, { immediate: true, deep: true })

// 选择房源
const selectRoomRef = useTemplateRef('selectRoomRef')
const handleOpenRoomDrawer = () => {
    if (disabledFields.value.includes('roomName') || editType.value === 'detail') {
        return
    }
    selectRoomRef.value?.openDrawer(contractData.value.rooms || [])
}
const getRoomList = (data: ContractRoomDTO[]) => {
    contractData.value.rooms = data
    contractData.value.contractPurpose = Number(data[0].propertyType)
    // 初始化新选择的房源数据
    data.forEach(room => {
        // 如果有设置全局折扣，应用到新房源
        if (discount.value) {
            room.discount = discount.value
        }
        // 新增：自动初始化签约单价和签约月总价
        console.log('room.standardUnitPrice && room.discount', room.standardUnitPrice , room.discount)
        if (room.standardUnitPrice && room.discount) {
            room.signedUnitPrice = Number((room.standardUnitPrice * room.discount / 100).toFixed(8))
        } else {
            room.signedUnitPrice = 0
        }
        if (room.signedUnitPrice && room.area) {
            if (room.priceUnit === 1) {
                room.signedMonthlyPrice = Number((room.signedUnitPrice * room.area).toFixed(2))
            } else if (room.priceUnit === 2) {
                room.signedMonthlyPrice = room.signedUnitPrice
            }
        } else {
            room.signedMonthlyPrice = 0
        }
    })
    loadValidOrders()
}


// 加载房间有效定单
const loadValidOrders = async () => {
    const { rows } = await getOrderList({
        pageNum: 1,
        pageSize: 10000,
        status: 2,
        roomIds: contractData.value.rooms.map(item => item.roomId).join(','),
        projectId: contractStore.currentProjectId,
    })
    if (rows.length === 0) {
        contractData.value.bookings = []
        contractData.value.bookingRelType = 1
    } else {
        const bookings = rows.map((item: any) => {
            return {
                bookingId: item.id,
                bookedRoom: item.roomName,
                bookerName: item.customerName,
                bookingReceivedAmount: item.receivedAmount,
                bookingPaymentDate: item.receivedDate,
            }
        })

        if (contractStore.editType === 'toSign'&& !!contractData.value.bookings && contractData.value.bookings.length>0) {
            // 转签约需要判断房源获取的定单中是否包含转签约带过来的定单，如果包含，则类型为房间有效定单，否则为其他定单
            const isInclude = bookings.some((item: any) => item.bookingId === contractData.value.bookings?.[0]?.bookingId)
            if (isInclude) {
                contractData.value.bookingRelType = 0
                contractData.value.bookings = bookings
            } else {
                contractData.value.bookingRelType = 2
                contractData.value.bookings = contractData.value.bookings?.concat(bookings)
            }
        } else {
            contractData.value.bookingRelType = 0
            // 格式化定单列表
            contractData.value.bookings = bookings
        }
    }
}

// 计算面积合计
const calculateAreaTotal = computed(() => {
    return contractData.value.rooms.reduce((sum, room) => {
        return sum + (isNaN(room.area || 0) ? 0 : room.area)
    }, 0).toFixed(2)
})

// 计算签约单价合计
const calculateUnitAvg = computed(() => {
    if (contractData.value.rooms.length === 0) return 0
    const priceUnit = contractData.value.rooms[0].priceUnit
    if (priceUnit === 1) {
        // 单位为 元/平方米/月， 签约单价合计 = 签约单价 * 面积 之和 / 面积之和
        const totalArea = contractData.value.rooms.reduce((sum, room) => {
            return sum + (isNaN(room.area || 0) ? 0 : room.area)
        }, 0)
        const totalPrice = contractData.value.rooms.reduce((sum, room) => {
            return sum + (isNaN(room.signedUnitPrice || 0) ? 0 : room.signedUnitPrice * (isNaN(room.area || 0) ? 0 : room.area))
        }, 0)
        return totalArea === 0 ? 0 : (totalPrice / totalArea).toFixed(8)
    } else if (priceUnit === 2) {
        // 单位为 元/月， 签约单价合计 = 签约单价 之和 / 房源数量
        const totalPrice = contractData.value.rooms.reduce((sum, room) => {
            return sum + (isNaN(room.signedUnitPrice || 0) ? 0 : room.signedUnitPrice)
        }, 0)
        return (totalPrice / contractData.value.rooms.length).toFixed(8)
    } else {
        return 0
    }
})

// 计算签约总价合计
const calculateTotalSum = computed(() => {
    return contractData.value.rooms.reduce((sum, room) => {
        return sum + (isNaN(room.signedMonthlyPrice) ? 0 : room.signedMonthlyPrice)
    }, 0).toFixed(2)
})

// 移除房源
const removeRoom = (room: ContractRoomDTO, index: number) => {
    contractData.value.rooms.splice(index, 1)

    // 如果移除后没有房源了，清空全局折扣
    if (contractData.value.rooms.length === 0) {
        discount.value = undefined
    }
}

const validate = async () => {
    return new Promise(async (resolve, reject) => {
        if (contractData.value.rooms.length === 0) {
            Message.warning('请选择房源')
            reject()
        } else {
            // 判断房源信息是否填写完整
            resolve(true)
            // const isComplete = contractData.value.rooms.every(room => room.roomName && room.area && room.standardUnitPrice && room.discount && room.signedUnitPrice && room.signedMonthlyPrice)
            // if(!isComplete) {
            //     Message.warning('请填写完整房源信息')
            //     reject()
            // } else {
            //     resolve(true)
            // }
        }
    })
}

defineExpose({
    validate
})

// 组件挂载时检查预选房源
onMounted(() => {
    checkAndFillPreSelectedRoom()
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}
</style>
