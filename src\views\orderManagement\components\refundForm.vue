<template>
    <a-drawer v-model:visible="refundDrawerVisible" title="退定单" width="800px" :mask-closable="true" @cancel="handleRefundCancel">
        <template #footer>
            <a-space>
                <a-button @click="handleRefundCancel">取消</a-button>
                <a-button type="primary" status="success" @click="handleRefundSave" v-if="!isEffective">保存</a-button>
                <a-button type="primary" status="danger" @click="handleRefundSubmit" v-if="!isEffective">生效</a-button>
                <!-- <a-button type="primary" @click="handleRefundApply" >退款申请</a-button> -->

                <a-button type="primary" @click="handleRefundApply" v-if="isEffective && formData.isRefund === 1">退款申请</a-button>
                <!-- <a-button type="primary" @click="handleRefundPrint">预览&打印</a-button> -->
            </a-space>
        </template>

        <a-form 
        ref="formRef"
        :model="formData"
        :label-col-props="{ span: 4 }"
        :wrapper-col-props="{ span: 20 }"
    >
         <section-title title="基础信息" style="margin-bottom: 16px;"/>
        
        <a-row :gutter="16">
            <a-col :span="24">
                <a-form-item field="customerName" label="客户名称:" required :rules="[{ required: true, message: '请输入客户名称' }]">
                    {{ formData.customerName }}
                </a-form-item>
            </a-col>
        </a-row>

        <a-row :gutter="16">
            <a-col :span="24">
                <a-form-item field="roomName" label="意向房源:" required :rules="[{ required: true, message: '请输入意向房源' }]">
                    {{ formData.roomName }}
                </a-form-item>
            </a-col>
        </a-row>
<!-- 应收：bookingAmount，已收：receivedAmount，实收日期：receivedDate -->
        <a-row :gutter="16">
            <a-col :span="24">
                <a-form-item
                    field="bookingAmount"
                    label="定金应收金额:"
                    required
                    :rules="[{ required: true, message: '请输入定金应收金额' }]"
                >
                    {{ formData.bookingAmount }} 元
                </a-form-item>
            </a-col>
        </a-row>

        <a-row :gutter="16">
            <a-col :span="24">
                <a-form-item
                    field="receivedAmount"
                    label="定金已收金额:"
                    required
                    :rules="[{ required: true, message: '请输入定金已收金额' }]"
                >
                    {{ formData.receivedAmount }} 元
                </a-form-item>
            </a-col>
        </a-row>

        <a-row :gutter="16">
            <a-col :span="24">
                <a-form-item
                    field="receivedDate"
                    label="缴纳日期:"
                    required
                >
                    {{ formData.receivedDate }}
                </a-form-item>
            </a-col>
        </a-row>

        <a-row :gutter="16">
            <a-col :span="24">
                <a-form-item
                    field="isRefund"
                    label="是否退定金:"
                    required
                    :rules="[{ required: true, message: '请选择是否退定金' }]"
                >
                    <a-radio-group v-model="formData.isRefund" :disabled="isEffective">
                        <a-radio :value="1">是</a-radio>
                        <a-radio :value="0">否</a-radio>
                    </a-radio-group>
                    <!-- <a-tag color="warning">默认定单选择</a-tag> -->
                </a-form-item>
            </a-col>
        </a-row>

        <a-row :gutter="16">
            <a-col :span="24">
                <a-form-item
                    field="cancelRemark"
                    label="说明:"
                    :rules="[{ required: true, message: '请输入说明' }]"
                >
                    <a-textarea
                        v-model="formData.cancelRemark"
                        placeholder="请输入说明"
                        :auto-size="{ minRows: 3, maxRows: 5 }"
                        :disabled="isEffective"
                    />
                </a-form-item>
            </a-col>
        </a-row>

        <a-row :gutter="16">
            <a-col :span="24">
                <a-form-item label="附件:">
                    <UploadFile 
                        v-model="formData.cancelEnclosure"
                        :limit="5"
                        accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                        :max-size="10"
                        :disabled="isEffective"
                    />
                </a-form-item>
            </a-col>
        </a-row>
    </a-form>
    
    <!-- 退款申请抽屉 -->
    <order-refund-apply-form ref="refundApplyFormRef" @success="onRefundApplySuccess" @cancel="handleRefundApplyCancel" />
    <!-- <a-drawer v-model:visible="refundApplyDrawerVisible" title="退款申请单" width="800px" :mask-closable="true"
        @cancel="handleRefundApplyCancel">
        <template #footer>
            <a-space>
                <a-button @click="handleRefundApplyCancel">取消</a-button>
                <a-button type="primary" status="success" @click="handleRefundApplySave">暂存</a-button>
                <a-button type="primary" @click="handleRefundApplySubmit">发起审批</a-button>
            </a-space>
        </template>
        <order-refund-apply-form ref="refundApplyFormRef" v-if="refundApplyDrawerVisible"
            @success="onRefundApplySuccess" @cancel="handleRefundApplyCancel" />
    </a-drawer> -->
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits } from 'vue'
import { Message } from '@arco-design/web-vue'
import SectionTitle from '@/components/sectionTitle/index.vue'
import UploadFile from '@/components/upload/uploadFile.vue'
import OrderRefundApplyForm from './orderRefundApplyForm.vue'
import { cancelOrder } from '@/api/orderManagement'

const emit = defineEmits(['success'])

// {
//     "id": "string",
//     "cancelRemark": "string",
//     "cancelEnclosure": "string",
//     "isRefund": 0, //是否退款，0-否，1-是
//     "isSubmit": 0 //使用该字段判断是暂存还是提交，0-暂存，1-提交
// }

const refundDrawerVisible = ref(false)
const formRef = ref()
const isEffective = ref(false) // 是否已生效
const formData = reactive({
    id: '',
    customerName: '',
    roomName: '',
    bookingAmount: 0,
    receivedAmount: 0,
    receivedDate: '',
    cancelRemark: '',
    cancelEnclosure: '',
    isRefund: 0, // 改为数字类型：0-否，1-是
    isSubmit: 0
})

// 退款需要的项目信息
const refundApplyNeedData = ref<any>({})

// 附件列表
const fileList = ref<any[]>([])

// 退款申请相关
const refundApplyDrawerVisible = ref(false)
const refundApplyFormRef = ref()

// 初始化表单数据
const initFormData = (data: any) => {
    Object.assign(formData, {
        id: data.id,
        customerName: data.customerName,
        roomName: data.roomName,
        bookingAmount: data.bookingAmount,
        receivedAmount: data.receivedAmount,
        receivedDate: data.receivedDate,
        // 清除上一次的表单输入数据
        cancelRemark: data.cancelRemark || '',
        cancelEnclosure: data.cancelEnclosure || '',
        isRefund: data.isRefund || 0
    })
    isEffective.value = false // 重置生效状态
}

// 取消
const handleRefundCancel = () => {
    refundDrawerVisible.value = false
    isEffective.value = false // 重置状态
}

// 保存
const handleRefundSave = async () => {
    const errors = await formRef.value.validate()
    if (errors) return
    const submitData = {
        ...formData,
        isSubmit: 0, // 标记为暂存
        cancelEnclosure: formData.cancelEnclosure || null
    }
    const res = await cancelOrder(submitData)
    if (res.code === 200) {
        Message.success('保存成功')
        emit('success')
        refundDrawerVisible.value = false
        isEffective.value = false
    } else {
        // Message.error(res.msg || '保存失败')
    }
}

// 生效
const handleRefundSubmit = async () => {
    const errors = await formRef.value.validate()
    if (errors) return
    
    const submitData = {
        ...formData,
        isSubmit: 1, // 标记为提交生效
        cancelEnclosure: formData.cancelEnclosure || null
    }
    
    try {
        const res = await cancelOrder(submitData)
        if (res.code === 200) {
            Message.success('生效成功')
            isEffective.value = true // 设置为已生效
            emit('success')
            
            // 如果选择退定金，显示退款申请按钮，页面不关闭
            if (formData.isRefund === 1) {
                // 页面不关闭，显示退款申请按钮
                console.log('需要退定金，显示退款申请按钮')
            } else {
                // 不需要退定金，关闭页面
                refundDrawerVisible.value = false
                isEffective.value = false
            }
        } else {
            // Message.error(res.msg || '生效失败')
        }
    } catch (error) {
        console.error('生效失败:', error)
        // Message.error('生效失败')
    }
}

// 退款申请
const handleRefundApply = () => {
    // return console.log('refundApplyNeedData', refundApplyNeedData.value)
    // 初始化退款申请表单数据
    const refundApplyData = {
        projectId: refundApplyNeedData.value.projectId,
        id: refundApplyNeedData.value.id,
        bizId: refundApplyNeedData.value.id, // 订单ID
        customerName: formData.customerName,
        intentRoom: formData.roomName,
        bookingAmount: formData.bookingAmount,
        receivedAmount: formData.receivedAmount,
        receivableDate: formData.receivedDate,
        refundAmount: formData.receivedAmount, // 默认退款金额为已收金额
        isRefundDeposit: formData.isRefund,
        refundDepositRemark: formData.cancelRemark
    }
    
    refundApplyDrawerVisible.value = true
    
    // 等待组件渲染后初始化数据
    // setTimeout(() => {
    //     if (refundApplyFormRef.value && refundApplyFormRef.value.initFormData) {
    //         refundApplyFormRef.value.initFormData(refundApplyData)
    //     }
    // }, 100)

    try {
        refundApplyFormRef.value?.open({
            record: refundApplyData,
            mode: 'create'
        })
    } catch (error) {
        console.error('获取退款申请信息失败:', error)
        // Message.error('获取退款申请信息失败')
    }
}

const handleRefundApplyCancel = () => {
    refundApplyDrawerVisible.value = false
}

const handleRefundApplySave = async () => {
    try {
        if (refundApplyFormRef.value && refundApplyFormRef.value.save) {
            await refundApplyFormRef.value.save()
            Message.success('暂存成功')
            refundApplyDrawerVisible.value = false
        }
    } catch (error) {
        console.error('暂存失败:', error)
    }
}

const handleRefundApplySubmit = async () => {
    try {
        if (refundApplyFormRef.value && refundApplyFormRef.value.submit) {
            await refundApplyFormRef.value.submit()
            Message.success('发起审批成功')
            refundApplyDrawerVisible.value = false
            // 退款申请提交成功后关闭退定单页面
            refundDrawerVisible.value = false
            isEffective.value = false
        }
    } catch (error) {
        console.error('发起审批失败:', error)
    }
}

const onRefundApplySuccess = () => {
    Message.success('退款申请成功')
    refundApplyDrawerVisible.value = false
    // 退款申请成功后关闭退定单页面
    refundDrawerVisible.value = false
    isEffective.value = false
}

// 预览&打印
const handleRefundPrint = () => {
    console.log('预览&打印')
}

// 文件上传处理
const handleFileChange = (fileListData: any[]) => {
    console.log('文件变更:', fileListData)
    fileList.value = fileListData
    
    // 将附件信息更新到表单数据中
    const attachmentUrls = fileListData
        .filter(file => file.status === 'done')
        .map(file => file.response?.url || file.url)
        .join(',')
    
    formData.cancelEnclosure = attachmentUrls
}

// 自定义上传请求
const customUploadRequest = (options: any) => {
    const { onProgress, onSuccess, onError, file } = options
    
    // 模拟上传过程
    const timer = setInterval(() => {
        const percent = Math.floor(Math.random() * 10) + 10
        onProgress(percent)
    }, 300)
    
    // 模拟上传完成
    setTimeout(() => {
        clearInterval(timer)
        onProgress(100)
        onSuccess({
            url: `https://example.com/files/${file.name}`,
            name: file.name
        })
    }, 1000)
}

// 暴露方法给父组件
defineExpose({
    initFormData,
    open(data: any) {
        refundDrawerVisible.value = true
        // 初始化表单数据
        // 退款需要的项目信息
        refundApplyNeedData.value = data
        console.log('refundApplyNeedData', refundApplyNeedData.value)
        initFormData(data)
        // 清除表单验证状态
        formRef.value?.clearValidate()
    },
    async save() {
        await formRef.value.validate()
        return Promise.resolve()
    },
    async submit() {
        await formRef.value.validate()
        return Promise.resolve()
    },
    print() {
        console.log('打印预览')
    }
})
</script>