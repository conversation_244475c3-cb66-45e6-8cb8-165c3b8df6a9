<template>
    <a-drawer v-model:visible="visible" title="选择合同" width="1000px" @cancel="handleCancel">
        <div class="contract-selector">
            <!-- 筛选条件 -->
            <a-form :model="formModel" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }"
                label-align="right">
                <a-row :gutter="16">
                    <a-col :span="8">
                        <a-form-item field="projectId" label="项目">
                            <ProjectSelector v-model="formModel.projectId" @change="handleProjectChange" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="blockName" label="地块">
                            <a-input v-model="formModel.blockName" placeholder="请输入地块" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="buildingName" label="楼栋">
                            <a-input v-model="formModel.buildingName" placeholder="请输入楼栋" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="roomName" label="房间">
                            <a-input v-model="formModel.roomName" placeholder="请输入房间" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="customerName" label="承租人">
                            <a-input v-model="formModel.customerName" placeholder="请输入承租人" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item field="contractNo" label="合同号">
                            <a-input v-model="formModel.contractNo" placeholder="请输入合同号" allow-clear />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="24" style="text-align: right;">
                        <a-space>
                            <a-button type="primary" @click="handleSearch">
                                <template #icon>
                                    <icon-search />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="handleReset">
                                <template #icon>
                                    <icon-refresh />
                                </template>
                                重置
                            </a-button>
                        </a-space>
                    </a-col>
                </a-row>
            </a-form>

            <!-- 表格 -->
            <a-table :columns="columns" :data="tableData" row-key="id" :pagination="pagination" :bordered="true"
                size="small" :loading="loading"
                :row-selection="{ type: 'radio', selectedRowKeys, onChange: handleSelectionChange }"
                @page-change="onPageChange" @page-size-change="onPageSizeChange" @row-click="handleRowClick"
                style="margin-top: 16px; cursor: pointer;">
                <template #contractStatus="{ record }">
                    <a-tag :color="getStatusColor(record.status)">
                        {{ getStatusText(record.status) }}
                    </a-tag>
                </template>
                <template #contractPeriod="{ record }">
                    {{ record.startDate }} 至 {{ record.endDate }}
                </template>
            </a-table>


        </div>
        <!-- 底部操作按钮 -->
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" @click="handleConfirm" :disabled="!selectedContract.contractId">确认</a-button>
            </a-space>
        </template>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconSearch, IconRefresh } from '@arco-design/web-vue/es/icon'
import ProjectSelector from '@/components/projectSelector/index.vue'
import { getContractList, type ContractQueryDTO, type ContractVo } from '@/api/contract'

const emit = defineEmits(['confirm', 'cancel'])

// 抽屉显示控制
const visible = ref(false)
const loading = ref(false)

// 表单数据
const formModel = reactive({
    projectId: '',
    blockName: '',
    buildingName: '',
    roomName: '',
    customerName: '',
    contractNo: '',
    pageNum: 1,
    pageSize: 10
})

// 分页信息
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
})

// 表格数据
const tableData = ref<ContractVo[]>([])

// 选中的行
const selectedRowKeys = ref<string[]>([])
const selectedContract = reactive({
    contractId: '',
    contractNo: '',
    contractPeriod: '',
    roomName: '',
    status: 0,
    customerName: '',
    projectName: ''
})

// 表格列配置
const columns = [
    {
        title: '项目',
        dataIndex: 'projectName',
        width: 120,
        align: 'center',
        render: ({ record }: { record: ContractVo }) => (record as any).projectName || ''
    },
    { title: '合同号', dataIndex: 'contractNo', width: 160, align: 'center' },
    {
        title: '承租人',
        dataIndex: 'customerName',
        width: 120,
        align: 'center',
        render: ({ record }: { record: ContractVo }) => record.customer?.customerName || ''
    },
    {
        title: '租赁资源',
        dataIndex: 'roomName',
        width: 120,
        align: 'center',
        render: ({ record }: { record: ContractVo }) => {
            if (record.rooms && record.rooms.length > 0) {
                return record.rooms.map(room => room.roomName).join('、')
            }
            return ''
        }
    },
    { title: '合同周期', slotName: 'contractPeriod', width: 200, align: 'center' },
    { title: '合同状态', slotName: 'contractStatus', width: 100, align: 'center' }
]

// 打开抽屉
const open = () => {
    visible.value = true
    fetchData()
}

// 关闭抽屉
const close = () => {
    visible.value = false
    resetData()
}

// 暴露方法给父组件调用
defineExpose({
    open,
    close
})

// 重置数据
const resetData = () => {
    Object.assign(formModel, {
        projectId: '',
        blockName: '',
        buildingName: '',
        roomName: '',
        customerName: '',
        contractNo: '',
        pageNum: 1,
        pageSize: 10
    })
    selectedRowKeys.value = []
    Object.assign(selectedContract, {
        contractId: '',
        contractNo: '',
        contractPeriod: '',
        roomName: '',
        status: 0,
        customerName: '',
        projectName: ''
    })
    tableData.value = []
    pagination.current = 1
    pagination.total = 0
}

// 项目变化处理
const handleProjectChange = (value: string) => {
    formModel.projectId = value
}

// 查询
const handleSearch = () => {
    pagination.current = 1
    formModel.pageNum = 1
    fetchData()
}

// 重置
const handleReset = () => {
    Object.assign(formModel, {
        projectId: '',
        blockName: '',
        buildingName: '',
        roomName: '',
        customerName: '',
        contractNo: '',
        pageNum: 1,
        pageSize: 10
    })
    pagination.current = 1
    fetchData()
}

// 分页处理
const onPageChange = (current: number) => {
    pagination.current = current
    formModel.pageNum = current
    fetchData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    formModel.pageSize = pageSize
    pagination.current = 1
    formModel.pageNum = 1
    fetchData()
}

// 行选择处理
const handleSelectionChange = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys
    if (selectedKeys.length > 0) {
        const selected = tableData.value.find(item => item.id === selectedKeys[0])
        if (selected) {
            const contractPeriod = `${selected.startDate || ''} 至 ${selected.endDate || ''}`
            const roomNameStr = selected.rooms && selected.rooms.length > 0
                ? selected.rooms.map(room => room.roomName).join('、')
                : ''

            Object.assign(selectedContract, {
                contractId: selected.id,
                contractNo: selected.contractNo,
                contractPeriod,
                roomName: roomNameStr,
                status: selected.status,
                customerName: selected.customer?.customerName || '',
                projectName: (selected as any).projectName || ''
            })
        }
    }
}

// 行点击处理
const handleRowClick = (record: ContractVo) => {
    selectedRowKeys.value = [record.id!]
    handleSelectionChange([record.id!])
}

// 确认选择
const handleConfirm = () => {
    if (!selectedContract.contractId) {
        Message.warning('请选择一个合同')
        return
    }
    emit('confirm', selectedContract)
    close()
}

// 取消
const handleCancel = () => {
    emit('cancel')
    close()
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建筛选条件
        let roomNameFilter = ''
        const roomFilters = []

        // 添加各种房源相关筛选条件
        if (formModel.blockName) roomFilters.push(formModel.blockName)
        if (formModel.buildingName) roomFilters.push(formModel.buildingName)
        if (formModel.roomName) roomFilters.push(formModel.roomName)

        // 如果有房源筛选条件，组合成搜索字符串
        if (roomFilters.length > 0) {
            roomNameFilter = roomFilters.join(' ')
        }

        const params: ContractQueryDTO = {
            pageNum: formModel.pageNum,
            pageSize: formModel.pageSize,
            projectId: formModel.projectId || undefined,
            roomName: roomNameFilter || undefined,
            customerName: formModel.customerName || undefined,
            contractNo: formModel.contractNo || undefined,
            // 只查询生效中的合同（状态为30）
            statuses: [30]
        }

        const response = await getContractList(params)

        tableData.value = response.rows || []
        pagination.total = response.total || 0
    } catch (error) {
        console.error('获取合同列表失败:', error)
        Message.error('获取合同列表失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 获取状态文本
const getStatusText = (status: number) => {
    const statusMap = new Map([
        [10, '草稿'],
        [20, '待生效'],
        [30, '生效中'],
        [40, '失效']
    ])
    return statusMap.get(status) || '未知'
}

// 获取状态颜色
const getStatusColor = (status: number) => {
    const colorMap = new Map([
        [10, 'gray'],
        [20, 'orange'],
        [30, 'green'],
        [40, 'red']
    ])
    return colorMap.get(status) || 'gray'
}
</script>

<style scoped lang="less">
.contract-selector {
    .footer {
        border-top: 1px solid #e5e6e8;
        padding-top: 16px;
    }

    :deep(.arco-table-tr) {
        cursor: pointer;

        &:hover {
            background-color: #f7f8fa;
        }
    }
}
</style>