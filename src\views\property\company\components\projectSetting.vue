<template>
  <div class="project-setting-container">
    <div class="header">
      <div class="company-name">
        <span class="label">商业公司名称</span>
        <span class="value">{{ formModel.companyName }}</span>
      </div>
    </div>

    <div class="content">
      <section-title title="已关联项目" />
      <div class="search-bar">
        <a-input-search 
          v-model="searchValue" 
          placeholder="请输入项目名称搜索" 
          allow-clear 
          @search="handleSearch"
          @input="handleSearch"
          @clear="handleSearch"
        />
        <div class="actions">
          <a-button v-permission="['rent:merchant:project:add']" type="primary" @click="handleAddProject">
            新增关联项目
          </a-button>
          <a-button v-permission="['rent:merchant:project:cancel']" type="primary" @click="handleBatchUnbind" :disabled="selectedKeys.length === 0">
            批量取消关联
          </a-button>
        </div>
      </div>

      <a-table 
        row-key="id"
        :loading="loading" 
        :columns="columns" 
        :data="tableData" 
        :pagination="pagination"
        :scroll="{x: 1}"
        v-model:selectedKeys="selectedKeys"
        :row-selection="{ type: 'checkbox', showCheckedAll: true }"
        :bordered="{ cell: true }"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
        </template>
        <template #operations="{ record }">
          <a-button v-permission="['rent:merchant:project:cancel']" type="text" size="mini" @click="handleUnbind(record)">
            取消关联
          </a-button>
        </template>
      </a-table>
    </div>

    <!-- 新增关联项目弹窗 -->
    <a-modal
      v-model:visible="addProjectVisible"
      title="新增关联项目"
      width="600px"
      @ok="handleAddProjectOk"
      @cancel="handleAddProjectCancel"
      :ok-button-props="{ disabled: selectedFourthLevelCount === 0 }"
    >
      <div class="add-project-content">
        <div class="filter-wrapper">
          <a-select
            v-model="projectFilter"
            :style="{ width: '140px' }"
            size="large"
            @change="handleFilterChange"
          >
            <a-option value="unlinked">未关联项目</a-option>
            <a-option value="all">全部项目</a-option>
          </a-select>
          <a-input-search
            :style="{ width: '240px', marginLeft: '8px' }"
            v-model="addProjectSearch"
            placeholder="请输入项目名称搜索"
            allow-clear
            @search="handleAddProjectSearch"
          />
        </div>
        <div class="project-tree-content">
          <div class="project-tree-list">
            <a-tree
              :data="filteredAddProjectTreeData"
              v-model:checkedKeys="addProjectSelectedKeys"
              :checkable="true"
              :check-strictly="false"
              :default-expand-all="true"
              :expand-all="!!addProjectSearchKeyword"
              :field-names="{ title: 'name', key: 'id' }"
              class="project-tree"
              show-line
            />
            <div v-if="filteredAddProjectTreeData.length === 0" class="no-data">
              <a-empty description="暂无可关联的项目" />
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { Modal, Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { 
  getMerchantProjectList, 
  addMerchantProject, 
  cancelMerchantProject,
  getOrgTreeWithFilter,
  type MerchantProjectQueryParams, 
  type ProjectInfo,
  type MerchantProjectDTO,
  type OrgTreeQueryParams
} from '@/api/company'

const props = defineProps({
  id: {
    type: [Number, String],
    required: true
  },
  companyName: {
    type: String,
    default: ''
  }
})

interface Project {
  id: string,
  projectId: string,
  projectName: string
  merchantId?: string
  createTime?: string
  updateTime?: string
}

interface TreeNode {
  id: string
  name: string
  children?: TreeNode[]
  [key: string]: any
}

const formModel = ref({
  companyName: ''
})

const searchValue = ref('')
const tableData = ref<Project[]>([])
const loading = ref(false)
const selectedKeys = ref<string[]>([])

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
})

const emit = defineEmits(['refresh'])

// 搜索防抖定时器
let searchTimer: number | null = null
let addProjectSearchTimer: number | null = null

const columns = [
  {
    title: '序号',
    slotName: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '项目',
    dataIndex: 'projectName',
    ellipsis: true,
    tooltip: true,
    align: 'center',
    width: 120
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 100,
    align: 'center',
    ellipsis: false,
    tooltip: false,
    fixed: 'right',
  }
]

onMounted(() => {
  // 设置商业公司名称
  formModel.value.companyName = props.companyName
  loadProjectList()
})

const loadProjectList = async () => {
  try {
    loading.value = true
    const params: MerchantProjectQueryParams = {
      merchantId: props.id as string,
      projectName: searchValue.value.trim() || undefined,
      pageNum: pagination.value.current,
      pageSize: pagination.value.pageSize
    }
    
    const response = await getMerchantProjectList(params)
    if (response) {
      // 将API返回的数据转换为Project格式
      tableData.value = (response.rows || []).map(item => ({
        id: item.projectId, // 使用projectId作为表格的key
        projectId: item.projectId,
        projectName: item.projectName || '',
        merchantId: props.id as string, // 当前商业公司ID
        createTime: item.createTime,
        updateTime: item.updateTime
      }))
      pagination.value.total = response.total || 0
    }
  } catch (error) {
    console.error('获取关联项目列表失败:', error)
    tableData.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索防抖处理
const handleSearch = (value: string) => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    pagination.value.current = 1 // 重置到第一页
    loadProjectList()
  }, 300)
}

const handleUnbind = async (record: Project) => {
  Modal.confirm({
    title: '确认取消关联',
    content: `是否确认取消关联【${record.projectName}】？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        const params: MerchantProjectDTO = {
          merchantId: props.id as string,
          projectIds: [record.projectId]
        }
        await cancelMerchantProject(params)
        Message.success('取消关联成功')
        selectedKeys.value = selectedKeys.value.filter(key => key !== record.id)
        loadProjectList()
        emit('refresh')
      } catch (error) {
        console.error('取消关联失败:', error)
      }
    }
  })
}

const handleBatchUnbind = () => {
  if (selectedKeys.value.length === 0) {
    Message.warning('请选择要取消关联的项目')
    return
  }

  const selectedProjects = tableData.value.filter(item => selectedKeys.value.includes(item.id))
  const projectNames = selectedProjects.map(item => item.projectName).join('】、【')

  Modal.confirm({
    title: '确认取消关联',
    content: `是否确认取消关联【${projectNames}】？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        const params: MerchantProjectDTO = {
          merchantId: props.id as string,
          projectIds: selectedKeys.value
        }
        await cancelMerchantProject(params)
        Message.success('批量取消关联成功')
        selectedKeys.value = []
        loadProjectList()
        emit('refresh')
      } catch (error) {
        console.error('批量取消关联失败:', error)
      }
    }
  })
}

// 新增关联项目相关
const addProjectVisible = ref(false)
const addProjectSearch = ref('')
const addProjectSearchKeyword = ref('')
const addProjectTreeData = ref<TreeNode[]>([])
const addProjectSelectedKeys = ref<string[]>([])
const projectFilter = ref('unlinked') // 'unlinked' | 'all'

const handleAddProjectSearch = (value: string) => {
  addProjectSearch.value = value
  if (addProjectSearchTimer) {
    clearTimeout(addProjectSearchTimer)
  }
  addProjectSearchTimer = setTimeout(() => {
    addProjectSearchKeyword.value = value
  }, 300)
}

// 检查节点是否满足最小层级要求（第四级）
const checkMinLevel = (node: TreeNode, currentLevel: number = 1): boolean => {
  const minLevel = 4 // 要求第四级
  
  // 如果当前层级已经达到要求，返回true
  if (currentLevel >= minLevel) return true
  
  // 如果有子节点，递归检查子节点
  if (node.children && node.children.length > 0) {
    return node.children.some(child => checkMinLevel(child, currentLevel + 1))
  }
  
  // 没有子节点且当前层级不满足要求
  return false
}

// 判断是否为第四级节点（叶子节点且在第四级）
const isFourthLevelNode = (node: TreeNode, treeData: TreeNode[], currentLevel: number = 1): boolean => {
  // 如果当前层级是第四级且没有子节点，则为第四级节点
  if (currentLevel === 4 && (!node.children || node.children.length === 0)) {
    return true
  }
  return false
}

// 获取所有第四级节点的ID
const getFourthLevelNodeIds = (nodes: TreeNode[], currentLevel: number = 1): string[] => {
  const fourthLevelIds: string[] = []
  
  const traverse = (nodeList: TreeNode[], level: number) => {
    for (const node of nodeList) {
      if (level === 4 && (!node.children || node.children.length === 0)) {
        // 第四级叶子节点
        fourthLevelIds.push(node.id)
      } else if (node.children && node.children.length > 0) {
        // 继续遍历子节点
        traverse(node.children, level + 1)
      }
    }
  }
  
  traverse(nodes, currentLevel)
  return fourthLevelIds
}

// 从选中的keys中过滤出第四级节点的ID
const getSelectedFourthLevelIds = (selectedKeys: string[], treeData: TreeNode[]): string[] => {
  const allFourthLevelIds = getFourthLevelNodeIds(treeData)
  return selectedKeys.filter(id => allFourthLevelIds.includes(id))
}

// 计算选中的第四级项目数量
const selectedFourthLevelCount = computed(() => {
  return getSelectedFourthLevelIds(addProjectSelectedKeys.value, addProjectTreeData.value).length
})
// 过滤树数据，移除不满足层级要求的节点
const filterByLevel = (nodes: TreeNode[], currentLevel: number = 1): TreeNode[] => {
  const minLevel = 4 // 要求第四级
  
  return nodes.filter(node => {
    // 检查当前节点是否满足层级要求
    if (!checkMinLevel(node, currentLevel)) {
      return false
    }
    
    return true
  }).map(node => {
    // 创建新的节点对象，避免修改原始数据
    const newNode = { ...node }
    
    // 如果有子节点，递归过滤子节点
    if (node.children && node.children.length > 0) {
      const filteredChildren = filterByLevel(node.children, currentLevel + 1)
      // 如果过滤后没有子节点了，需要重新检查是否满足层级要求
      if (filteredChildren.length === 0) {
        // 如果当前层级不满足要求，则不包含此节点
        if (currentLevel < minLevel) {
          return null
        }
        newNode.children = []
      } else {
        newNode.children = filteredChildren
      }
    }
    
    return newNode
  }).filter(node => node !== null) as TreeNode[]
}

// 获取已关联的项目ID列表
const getLinkedProjectIds = (treeData: TreeNode[]): string[] => {
  const linkedIds: string[] = []
  
  const traverse = (nodes: TreeNode[]) => {
    for (const node of nodes) {
      // 检查当前节点是否已关联（使用projectId比较）
      const isLinked = tableData.value.some(item => item.projectId === node.id)
      if (isLinked) {
        linkedIds.push(node.id)
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    }
  }
  
  traverse(treeData)
  return linkedIds
}

// 过滤未关联的项目
const filterUnlinkedProjects = (treeData: TreeNode[], linkedIds: string[]): TreeNode[] => {
  // 检查一个节点分支下是否还有未关联的第四级项目
  const hasUnlinkedFourthLevel = (node: TreeNode, currentLevel: number = 1): boolean => {
    if (currentLevel === 4) {
      // 第四级节点，检查是否未关联
      const isLinked = linkedIds.includes(node.id)
      return !isLinked
    }
    
    // 非第四级节点，检查子节点
    if (node.children && node.children.length > 0) {
      return node.children.some(child => hasUnlinkedFourthLevel(child, currentLevel + 1))
    }
    
    return false
  }
  
  const filterTree = (nodes: TreeNode[], level: number = 1): TreeNode[] => {
    const result: TreeNode[] = []
    
    for (const node of nodes) {
      const hasUnlinkedProjects = hasUnlinkedFourthLevel(node, level)
      
      if (hasUnlinkedProjects) {
        let filteredChildren: TreeNode[] = []
        
        if (node.children && node.children.length > 0) {
          filteredChildren = filterTree(node.children, level + 1)
        }
        
        result.push({
          ...node,
          children: filteredChildren
        })
      }
    }
    
    return result
  }
  
  return filterTree(treeData)
}

// 根据搜索关键词过滤项目树
const filterTreeBySearch = (treeData: TreeNode[], keyword: string): TreeNode[] => {
  if (!keyword.trim()) {
    return treeData
  }
  
  const searchKeyword = keyword.toLowerCase().trim()
  
  const filterTree = (nodes: TreeNode[]): TreeNode[] => {
    const result: TreeNode[] = []
    
    for (const node of nodes) {
      const matchesSearch = node.name?.toLowerCase().includes(searchKeyword)
      let filteredChildren: TreeNode[] = []
      
      if (node.children && node.children.length > 0) {
        filteredChildren = filterTree(node.children)
      }
      
      // 如果当前节点匹配或有匹配的子节点，则包含此节点
      if (matchesSearch || filteredChildren.length > 0) {
        result.push({
          ...node,
          children: filteredChildren.length > 0 ? filteredChildren : node.children
        })
      }
    }
    
    return result
  }
  
  return filterTree(treeData)
}

// 过滤后的项目树数据
const filteredAddProjectTreeData = computed(() => {
  let data = addProjectTreeData.value
  
  // 根据搜索关键词过滤
  if (addProjectSearchKeyword.value) {
    data = filterTreeBySearch(data, addProjectSearchKeyword.value)
  }
  
  return data
})

// 处理过滤条件变化
const handleFilterChange = async (value: string) => {
  await loadAddProjectTreeData()
}

// 加载项目树数据
const loadAddProjectTreeData = async () => {
  try {
    if (projectFilter.value === 'unlinked') {
      // 使用新API获取未关联项目
      const params: OrgTreeQueryParams = {
        relProjectFlag: 1 // 1:未关联
      }
      
      const response = await getOrgTreeWithFilter(params)
      if (response && response.data) {
        // 首先应用层级过滤，只显示有第四级的项目
        const levelFilteredData = filterByLevel(response.data as TreeNode[])
        addProjectTreeData.value = levelFilteredData
      } else {
        addProjectTreeData.value = []
      }
    } else {
      // 显示全部项目，使用新API
      const params: OrgTreeQueryParams = {
        relProjectFlag: 2 // 2：已关联
      }
      
      const response = await getOrgTreeWithFilter(params)
      if (response && response.data) {
        // 首先应用层级过滤，只显示有第四级的项目
        const levelFilteredData = filterByLevel(response.data as TreeNode[])
        addProjectTreeData.value = levelFilteredData
      } else {
        addProjectTreeData.value = []
      }
    }
  } catch (error) {
    console.error('加载项目树数据失败:', error)
    addProjectTreeData.value = []
  }
}

const handleAddProject = async () => {
  addProjectVisible.value = true
  await loadAddProjectTreeData()
}

const handleAddProjectOk = async () => {
  const fourthLevelIds = getSelectedFourthLevelIds(addProjectSelectedKeys.value, addProjectTreeData.value)
  
  if (fourthLevelIds.length === 0) {
    Message.warning('请选择要关联的项目')
    return
  }
  
  try {
    const params: MerchantProjectDTO = {
      merchantId: props.id as string,
      projectIds: fourthLevelIds
    }
    await addMerchantProject(params)
    Message.success(`成功关联 ${fourthLevelIds.length} 个项目`)
    addProjectVisible.value = false
    addProjectSelectedKeys.value = []
    addProjectSearch.value = ''
    addProjectSearchKeyword.value = ''
    projectFilter.value = 'unlinked'
    loadProjectList()
    emit('refresh')
  } catch (error) {
    console.error('关联项目失败:', error)
  }
}

const handleAddProjectCancel = () => {
  addProjectVisible.value = false
  addProjectSelectedKeys.value = []
  addProjectSearch.value = ''
  addProjectSearchKeyword.value = ''
  projectFilter.value = 'unlinked'
  
  // 清理新增项目搜索防抖定时器
  if (addProjectSearchTimer) {
    clearTimeout(addProjectSearchTimer)
    addProjectSearchTimer = null
  }
}

const onPageChange = (current: number) => {
  pagination.value.current = current
  loadProjectList()
}

const onPageSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.current = 1
  loadProjectList()
}

watch(() => props.companyName, (newValue) => {
  formModel.value.companyName = newValue
})
</script>

<style scoped lang="less">
.project-setting-container {
  height: 100%;
  .section-title {
      margin: 16px 0;
      &.first-child{
        margin-top: 8px;
      }
  }
  .header {
    margin-bottom: 24px;

    .company-name {
      .label {
        color: rgb(var(--gray-6));
        margin-right: 8px;
      }
      .value {
        background-color: rgb(var(--gray-2));
        padding: 4px 8px;
      }
    }
  }

  .content {
    .title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
      position: relative;
      padding-left: 12px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: rgb(var(--primary-6));
      }
    }

    .search-bar {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      :deep(.arco-input-wrapper) {
        width: 240px;
      }

      .actions {
        .arco-btn + .arco-btn {
          margin-left: 16px;
        }
      }
    }
  }
}

.add-project-content {
  height: 500px;
  overflow: hidden;
  
  .filter-wrapper {
    display: flex;
    align-items: center;
  }

  .project-tree-content {
    margin-top: 0;
    height: calc(100% - 70px);
    overflow: auto;
    border-radius: 4px;
    padding: 16px 0;
    box-sizing: border-box;
    
    .project-tree-list {
      height: 100%;
      
      .arco-tree {
        height: 100%;
        overflow-y: auto;
      }
      
      .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
      }
    }
  }
  
  .selected-info {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: var(--color-fill-2);
    border-radius: 4px;
    
    .selected-label {
      color: var(--color-text-2);
      font-size: 14px;
    }
  }
}
</style>