<template>
  <div class="container">
    <a-card class="general-card">
      <!-- 已缴状态切换 -->

      <a-tabs v-model="viewType" size="large" hide-content @change="handleChangeType" style="margin-bottom: 16px;">
        <a-tab-pane v-permission="['asset:ledger:list']" key="nonPayment" title="已接收资产">
          <!-- <received-assets /> -->
        </a-tab-pane>
        <a-tab-pane v-permission="['asset:ledger:historylist']" key="payment" title="历史资产">
          <!-- <history-assets /> -->
        </a-tab-pane>
        <a-tab-pane v-permission="['asset:change:list']" key="temporary" title="面积变更提醒">
          <!-- <area-change-alert /> -->
        </a-tab-pane>
      </a-tabs>

      <!-- 根据viewType动态渲染不同的组件 -->
      <received-assets v-permission="['asset:ledger:list']" v-if="viewType === 'nonPayment'" />
      <history-assets v-permission="['asset:ledger:historylist']" v-if="viewType === 'payment'" />
      <area-change-alert v-permission="['asset:change:list']" v-if="viewType === 'temporary'" />
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ReceivedAssets from './components/receivedAssets.vue'
import HistoryAssets from './components/historyAssets.vue'
import AreaChangeAlert from './components/areaChangeAlert.vue'

// 视图类型
const viewType = ref('nonPayment')

const handleChangeType = (key: string) => {
  viewType.value = key
}
</script>

<style scoped lang="less">
.container {
  padding: 0 16px 16px 16px;
}
</style>