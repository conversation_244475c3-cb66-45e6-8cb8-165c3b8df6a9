---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁后台/客户管理

<a id="opIdupdateOwner"></a>

## POST 更新维护人接口

POST /customer/updateOwner

批量更新客户的维护人信息

> Body 请求参数

```json
{
  "customerIds": [
    "string"
  ],
  "ownerId": "string",
  "ownerName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CustomerUpdateDTO](#schemacustomerupdatedto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdsaveForContract"></a>

## POST 新增客户接口-合同

POST /customer/saveForContract

新增客户信息，包括主表和子表（联系人、担保人、银行账号、开票信息）

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerType": 0,
  "customerName": "string",
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "businessLicense": "string",
  "contactAddress": "string",
  "ownerId": "string",
  "ownerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "isDel": true,
  "contactList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "gender": 0,
      "relationship": 0,
      "idNumber": "string",
      "position": "string",
      "department": "string",
      "isPreferred": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "guarantorList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "idType": 0,
      "idNumber": "string",
      "address": "string",
      "remark": "string",
      "isDel": true
    }
  ],
  "bankAccountList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "bankName": "string",
      "accountNumber": "string",
      "accountRemark": "string",
      "isDel": true
    }
  ],
  "invoiceList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "title": "string",
      "taxNumber": "string",
      "phone": "string",
      "address": "string",
      "bankName": "string",
      "accountNumber": "string",
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CustomerAddDTO](#schemacustomeradddto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdlist_2"></a>

## POST 客户列表查询接口

POST /customer/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "customerType": 0,
  "customerName": "string",
  "ownerName": "string",
  "createByName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CustomerQueryDTO](#schemacustomerquerydto)| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","projectName":"string","customerName":"string","customerType":0,"creditCode":"string","legalName":"string","contactPhone":"string","idType":"string","idNumber":"string","idValidityStart":"2019-08-24T14:15:22Z","idValidityEnd":"2019-08-24T14:15:22Z","idFront":"string","idBack":"string","businessLicense":"string","contactAddress":"string","ownerId":"string","ownerName":"string","attachmentFiles":"string","remark":"string","createByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"contactList":[{"id":"string","customerId":"string","name":"string","phone":"string","gender":0,"relationship":0,"idNumber":"string","position":"string","department":"string","isPreferred":true,"remark":"string","createByName":"string","updateByName":"string","isDel":true}],"guarantorList":[{"id":"string","customerId":"string","name":"string","phone":"string","idType":0,"idNumber":"string","address":"string","remark":"string","createByName":"string","updateByName":"string","isDel":true}],"bankAccountList":[{"id":"string","customerId":"string","bankName":"string","accountNumber":"string","accountRemark":"string","createByName":"string","updateByName":"string","isDel":true}],"invoiceList":[{"id":"string","customerId":"string","title":"string","taxNumber":"string","phone":"string","address":"string","bankName":"string","accountNumber":"string","createByName":"string","updateByName":"string","isDel":true}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[CustomerVo](#schemacustomervo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» projectId|string|false|none|项目id|项目id|
|» projectName|string|false|none|项目名称|项目名称|
|» customerName|string|false|none|客户姓名，支持身份证识别填充|客户姓名，支持身份证识别填充|
|» customerType|integer(int32)|false|none|客户类型，个人：1，企业：2|客户类型，个人：1，企业：2|
|» creditCode|string|false|none|统一社会信用代码，需校验唯一性|统一社会信用代码，需校验唯一性|
|» legalName|string|false|none|法人姓名|法人姓名|
|» contactPhone|string|false|none|联系电话，需校验唯一性|联系电话，需校验唯一性|
|» idType|string|false|none|证件类型，身份证/护照等|证件类型，身份证/护照等|
|» idNumber|string|false|none|证件号码，支持身份证识别填充|证件号码，支持身份证识别填充|
|» idValidityStart|string(date-time)|false|none|证件有效期开始，支持身份证识别填充|证件有效期开始，支持身份证识别填充|
|» idValidityEnd|string(date-time)|false|none|证件有效期结束，支持身份证识别填充|证件有效期结束，支持身份证识别填充|
|» idFront|string|false|none|身份证正面地址|身份证正面地址|
|» idBack|string|false|none|身份证反面地址|身份证反面地址|
|» businessLicense|string|false|none|营业执照地址|营业执照地址|
|» contactAddress|string|false|none|联系地址，支持身份证识别填充|联系地址，支持身份证识别填充|
|» ownerId|string|false|none|维护人id|维护人id|
|» ownerName|string|false|none|维护人姓名|维护人姓名|
|» attachmentFiles|string|false|none|附件|附件|
|» remark|string|false|none|备注|备注|
|» createByName|string|false|none|创建人|创建人|
|» updateTime|string(date-time)|false|none|更新日期|更新日期|
|» isDel|boolean|false|none|0 否 1是|0 否 1是|
|» contactList|[[CustomerContactVo](#schemacustomercontactvo)]|false|none|联系人列表|联系人列表|
|»» 联系人列表|[CustomerContactVo](#schemacustomercontactvo)|false|none|联系人列表|联系人列表|
|»»» id|string|false|none|主键ID|none|
|»»» customerId|string|false|none|客户ID|客户ID|
|»»» name|string|false|none|联系人姓名|联系人姓名|
|»»» phone|string|false|none|联系电话|联系电话|
|»»» gender|integer(int32)|false|none|性别（1男 2女 3未知）|性别（1男 2女 3未知）|
|»»» relationship|integer(int32)|false|none|关系（1夫妻 2合伙人 3父母 4朋友 5子女 6其他）|关系（1夫妻 2合伙人 3父母 4朋友 5子女 6其他）|
|»»» idNumber|string|false|none|证件号码|证件号码|
|»»» position|string|false|none|职务，仅企业客户适用|职务，仅企业客户适用|
|»»» department|string|false|none|部门，仅企业客户适用|部门，仅企业客户适用|
|»»» isPreferred|boolean|false|none|是否首选联系人，默认最新记录为首选|是否首选联系人，默认最新记录为首选|
|»»» remark|string|false|none|补充说明|补充说明|
|»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»» isDel|boolean|false|none|0 否 1是|0 否 1是|
|» guarantorList|[[CustomerGuarantorVo](#schemacustomerguarantorvo)]|false|none|担保人列表|担保人列表|
|»» 担保人列表|[CustomerGuarantorVo](#schemacustomerguarantorvo)|false|none|担保人列表|担保人列表|
|»»» id|string|false|none|主键ID|none|
|»»» customerId|string|false|none|客户ID|客户ID|
|»»» name|string|false|none|担保人姓名|担保人姓名|
|»»» phone|string|false|none|联系电话|联系电话|
|»»» idType|integer(int32)|false|none|证件类型|证件类型|
|»»» idNumber|string|false|none|证件号码|证件号码|
|»»» address|string|false|none|地址|地址|
|»»» remark|string|false|none|备注，200字限制|备注，200字限制|
|»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»» isDel|boolean|false|none|0 否 1是|0 否 1是|
|» bankAccountList|[[CustomerBankAccountVo](#schemacustomerbankaccountvo)]|false|none|银行账号列表|银行账号列表|
|»» 银行账号列表|[CustomerBankAccountVo](#schemacustomerbankaccountvo)|false|none|银行账号列表|银行账号列表|
|»»» id|string|false|none|主键ID|none|
|»»» customerId|string|false|none|客户ID|客户ID|
|»»» bankName|string|false|none|开户银行|开户银行|
|»»» accountNumber|string|false|none|银行账号|银行账号|
|»»» accountRemark|string|false|none|账户备注|账户备注|
|»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»» isDel|boolean|false|none|0 否 1是|0 否 1是|
|» invoiceList|[[CustomerInvoiceVo](#schemacustomerinvoicevo)]|false|none|开票信息列表|开票信息列表|
|»» 开票信息列表|[CustomerInvoiceVo](#schemacustomerinvoicevo)|false|none|开票信息列表|开票信息列表|
|»»» id|string|false|none|主键ID|none|
|»»» customerId|string|false|none|客户ID|客户ID|
|»»» title|string|false|none|抬头名称|抬头名称|
|»»» taxNumber|string|false|none|税号|税号|
|»»» phone|string|false|none|电话号码|电话号码|
|»»» address|string|false|none|单位地址|单位地址|
|»»» bankName|string|false|none|开户银行|开户银行|
|»»» accountNumber|string|false|none|银行账号|银行账号|
|»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»» isDel|boolean|false|none|0 否 1是|0 否 1是|

<a id="opIdlistForContract"></a>

## POST 客户列表查询接口-合同

POST /customer/listForContract

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "customerType": 0,
  "customerName": "string",
  "ownerName": "string",
  "createByName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CustomerQueryDTO](#schemacustomerquerydto)| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","customerName":"string"}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[CustomerSimpleVo](#schemacustomersimplevo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» customerName|string|false|none|客户姓名，支持身份证识别填充|客户姓名，支持身份证识别填充|

<a id="opIdedit_13"></a>

## POST 编辑客户接口

POST /customer/edit

编辑客户信息，包括主表和子表（联系人、担保人、银行账号、开票信息）

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerType": 0,
  "customerName": "string",
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "businessLicense": "string",
  "contactAddress": "string",
  "ownerId": "string",
  "ownerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "isDel": true,
  "contactList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "gender": 0,
      "relationship": 0,
      "idNumber": "string",
      "position": "string",
      "department": "string",
      "isPreferred": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "guarantorList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "idType": 0,
      "idNumber": "string",
      "address": "string",
      "remark": "string",
      "isDel": true
    }
  ],
  "bankAccountList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "bankName": "string",
      "accountNumber": "string",
      "accountRemark": "string",
      "isDel": true
    }
  ],
  "invoiceList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "title": "string",
      "taxNumber": "string",
      "phone": "string",
      "address": "string",
      "bankName": "string",
      "accountNumber": "string",
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CustomerAddDTO](#schemacustomeradddto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdadd_11"></a>

## POST 新增客户接口

POST /customer/add

新增客户信息，包括主表和子表（联系人、担保人、银行账号、开票信息）

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerType": 0,
  "customerName": "string",
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "businessLicense": "string",
  "contactAddress": "string",
  "ownerId": "string",
  "ownerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "isDel": true,
  "contactList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "gender": 0,
      "relationship": 0,
      "idNumber": "string",
      "position": "string",
      "department": "string",
      "isPreferred": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "guarantorList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "idType": 0,
      "idNumber": "string",
      "address": "string",
      "remark": "string",
      "isDel": true
    }
  ],
  "bankAccountList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "bankName": "string",
      "accountNumber": "string",
      "accountRemark": "string",
      "isDel": true
    }
  ],
  "invoiceList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "title": "string",
      "taxNumber": "string",
      "phone": "string",
      "address": "string",
      "bankName": "string",
      "accountNumber": "string",
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CustomerAddDTO](#schemacustomeradddto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIddetail_1"></a>

## GET 客户详情接口

GET /customer/detail

根据客户ID查询客户完整信息，包括主表和子表数据

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |客户ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","projectName":"string","customerName":"string","customerType":0,"creditCode":"string","legalName":"string","contactPhone":"string","idType":"string","idNumber":"string","idValidityStart":"2019-08-24T14:15:22Z","idValidityEnd":"2019-08-24T14:15:22Z","idFront":"string","idBack":"string","businessLicense":"string","contactAddress":"string","ownerId":"string","ownerName":"string","attachmentFiles":"string","remark":"string","createByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"contactList":[{"id":"string","customerId":"string","name":"string","phone":"string","gender":0,"relationship":0,"idNumber":"string","position":"string","department":"string","isPreferred":true,"remark":"string","createByName":"string","updateByName":"string","isDel":true}],"guarantorList":[{"id":"string","customerId":"string","name":"string","phone":"string","idType":0,"idNumber":"string","address":"string","remark":"string","createByName":"string","updateByName":"string","isDel":true}],"bankAccountList":[{"id":"string","customerId":"string","bankName":"string","accountNumber":"string","accountRemark":"string","createByName":"string","updateByName":"string","isDel":true}],"invoiceList":[{"id":"string","customerId":"string","title":"string","taxNumber":"string","phone":"string","address":"string","bankName":"string","accountNumber":"string","createByName":"string","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[CustomerVo](#schemacustomervo)|

# 数据模型

<h2 id="tocS_CustomerQueryDTO">CustomerQueryDTO</h2>

<a id="schemacustomerquerydto"></a>
<a id="schema_CustomerQueryDTO"></a>
<a id="tocScustomerquerydto"></a>
<a id="tocscustomerquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "customerType": 0,
  "customerName": "string",
  "ownerName": "string",
  "createByName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|customerType|integer(int32)|false|none|客户类型，个人：1，企业：2|客户类型，个人：1，企业：2|
|customerName|string|false|none|客户姓名|客户姓名|
|ownerName|string|false|none|维护人姓名|维护人姓名|
|createByName|string|false|none|创建人姓名|创建人姓名|

<h2 id="tocS_CustomerVo">CustomerVo</h2>

<a id="schemacustomervo"></a>
<a id="schema_CustomerVo"></a>
<a id="tocScustomervo"></a>
<a id="tocscustomervo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerName": "string",
  "customerType": 0,
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "businessLicense": "string",
  "contactAddress": "string",
  "ownerId": "string",
  "ownerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "createByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "contactList": [
    {
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "gender": 0,
      "relationship": 0,
      "idNumber": "string",
      "position": "string",
      "department": "string",
      "isPreferred": true,
      "remark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "guarantorList": [
    {
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "idType": 0,
      "idNumber": "string",
      "address": "string",
      "remark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "bankAccountList": [
    {
      "id": "string",
      "customerId": "string",
      "bankName": "string",
      "accountNumber": "string",
      "accountRemark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "invoiceList": [
    {
      "id": "string",
      "customerId": "string",
      "title": "string",
      "taxNumber": "string",
      "phone": "string",
      "address": "string",
      "bankName": "string",
      "accountNumber": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|customerName|string|false|none|客户姓名，支持身份证识别填充|客户姓名，支持身份证识别填充|
|customerType|integer(int32)|false|none|客户类型，个人：1，企业：2|客户类型，个人：1，企业：2|
|creditCode|string|false|none|统一社会信用代码，需校验唯一性|统一社会信用代码，需校验唯一性|
|legalName|string|false|none|法人姓名|法人姓名|
|contactPhone|string|false|none|联系电话，需校验唯一性|联系电话，需校验唯一性|
|idType|string|false|none|证件类型，身份证/护照等|证件类型，身份证/护照等|
|idNumber|string|false|none|证件号码，支持身份证识别填充|证件号码，支持身份证识别填充|
|idValidityStart|string(date-time)|false|none|证件有效期开始，支持身份证识别填充|证件有效期开始，支持身份证识别填充|
|idValidityEnd|string(date-time)|false|none|证件有效期结束，支持身份证识别填充|证件有效期结束，支持身份证识别填充|
|idFront|string|false|none|身份证正面地址|身份证正面地址|
|idBack|string|false|none|身份证反面地址|身份证反面地址|
|businessLicense|string|false|none|营业执照地址|营业执照地址|
|contactAddress|string|false|none|联系地址，支持身份证识别填充|联系地址，支持身份证识别填充|
|ownerId|string|false|none|维护人id|维护人id|
|ownerName|string|false|none|维护人姓名|维护人姓名|
|attachmentFiles|string|false|none|附件|附件|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人|创建人|
|updateTime|string(date-time)|false|none|更新日期|更新日期|
|isDel|boolean|false|none|0 否 1是|0 否 1是|
|contactList|[[CustomerContactVo](#schemacustomercontactvo)]|false|none|联系人列表|联系人列表|
|guarantorList|[[CustomerGuarantorVo](#schemacustomerguarantorvo)]|false|none|担保人列表|担保人列表|
|bankAccountList|[[CustomerBankAccountVo](#schemacustomerbankaccountvo)]|false|none|银行账号列表|银行账号列表|
|invoiceList|[[CustomerInvoiceVo](#schemacustomerinvoicevo)]|false|none|开票信息列表|开票信息列表|

<h2 id="tocS_CustomerInvoiceVo">CustomerInvoiceVo</h2>

<a id="schemacustomerinvoicevo"></a>
<a id="schema_CustomerInvoiceVo"></a>
<a id="tocScustomerinvoicevo"></a>
<a id="tocscustomerinvoicevo"></a>

```json
{
  "id": "string",
  "customerId": "string",
  "title": "string",
  "taxNumber": "string",
  "phone": "string",
  "address": "string",
  "bankName": "string",
  "accountNumber": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

开票信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|customerId|string|false|none|客户ID|客户ID|
|title|string|false|none|抬头名称|抬头名称|
|taxNumber|string|false|none|税号|税号|
|phone|string|false|none|电话号码|电话号码|
|address|string|false|none|单位地址|单位地址|
|bankName|string|false|none|开户银行|开户银行|
|accountNumber|string|false|none|银行账号|银行账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerBankAccountVo">CustomerBankAccountVo</h2>

<a id="schemacustomerbankaccountvo"></a>
<a id="schema_CustomerBankAccountVo"></a>
<a id="tocScustomerbankaccountvo"></a>
<a id="tocscustomerbankaccountvo"></a>

```json
{
  "id": "string",
  "customerId": "string",
  "bankName": "string",
  "accountNumber": "string",
  "accountRemark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

银行账号列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|customerId|string|false|none|客户ID|客户ID|
|bankName|string|false|none|开户银行|开户银行|
|accountNumber|string|false|none|银行账号|银行账号|
|accountRemark|string|false|none|账户备注|账户备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerGuarantorVo">CustomerGuarantorVo</h2>

<a id="schemacustomerguarantorvo"></a>
<a id="schema_CustomerGuarantorVo"></a>
<a id="tocScustomerguarantorvo"></a>
<a id="tocscustomerguarantorvo"></a>

```json
{
  "id": "string",
  "customerId": "string",
  "name": "string",
  "phone": "string",
  "idType": 0,
  "idNumber": "string",
  "address": "string",
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

担保人列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|customerId|string|false|none|客户ID|客户ID|
|name|string|false|none|担保人姓名|担保人姓名|
|phone|string|false|none|联系电话|联系电话|
|idType|integer(int32)|false|none|证件类型|证件类型|
|idNumber|string|false|none|证件号码|证件号码|
|address|string|false|none|地址|地址|
|remark|string|false|none|备注，200字限制|备注，200字限制|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerContactVo">CustomerContactVo</h2>

<a id="schemacustomercontactvo"></a>
<a id="schema_CustomerContactVo"></a>
<a id="tocScustomercontactvo"></a>
<a id="tocscustomercontactvo"></a>

```json
{
  "id": "string",
  "customerId": "string",
  "name": "string",
  "phone": "string",
  "gender": 0,
  "relationship": 0,
  "idNumber": "string",
  "position": "string",
  "department": "string",
  "isPreferred": true,
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

联系人列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|customerId|string|false|none|客户ID|客户ID|
|name|string|false|none|联系人姓名|联系人姓名|
|phone|string|false|none|联系电话|联系电话|
|gender|integer(int32)|false|none|性别（1男 2女 3未知）|性别（1男 2女 3未知）|
|relationship|integer(int32)|false|none|关系（1夫妻 2合伙人 3父母 4朋友 5子女 6其他）|关系（1夫妻 2合伙人 3父母 4朋友 5子女 6其他）|
|idNumber|string|false|none|证件号码|证件号码|
|position|string|false|none|职务，仅企业客户适用|职务，仅企业客户适用|
|department|string|false|none|部门，仅企业客户适用|部门，仅企业客户适用|
|isPreferred|boolean|false|none|是否首选联系人，默认最新记录为首选|是否首选联系人，默认最新记录为首选|
|remark|string|false|none|补充说明|补充说明|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerAddDTO">CustomerAddDTO</h2>

<a id="schemacustomeradddto"></a>
<a id="schema_CustomerAddDTO"></a>
<a id="tocScustomeradddto"></a>
<a id="tocscustomeradddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerType": 0,
  "customerName": "string",
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "businessLicense": "string",
  "contactAddress": "string",
  "ownerId": "string",
  "ownerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "isDel": true,
  "contactList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "gender": 0,
      "relationship": 0,
      "idNumber": "string",
      "position": "string",
      "department": "string",
      "isPreferred": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "guarantorList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "idType": 0,
      "idNumber": "string",
      "address": "string",
      "remark": "string",
      "isDel": true
    }
  ],
  "bankAccountList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "bankName": "string",
      "accountNumber": "string",
      "accountRemark": "string",
      "isDel": true
    }
  ],
  "invoiceList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "title": "string",
      "taxNumber": "string",
      "phone": "string",
      "address": "string",
      "bankName": "string",
      "accountNumber": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|customerType|integer(int32)|false|none|客户类型，个人：1，企业：2|客户类型，个人：1，企业：2|
|customerName|string|false|none|客户姓名，支持身份证识别填充|客户姓名，支持身份证识别填充|
|creditCode|string|false|none|统一社会信用代码，需校验唯一性|统一社会信用代码，需校验唯一性|
|legalName|string|false|none|法人姓名|法人姓名|
|contactPhone|string|false|none|联系电话，需校验唯一性|联系电话，需校验唯一性|
|idType|string|false|none|证件类型，身份证/护照等|证件类型，身份证/护照等|
|idNumber|string|false|none|证件号码，支持身份证识别填充|证件号码，支持身份证识别填充|
|idValidityStart|string(date-time)|false|none|证件有效期开始，支持身份证识别填充|证件有效期开始，支持身份证识别填充|
|idValidityEnd|string(date-time)|false|none|证件有效期结束，支持身份证识别填充|证件有效期结束，支持身份证识别填充|
|idFront|string|false|none|身份证正面地址|身份证正面地址|
|idBack|string|false|none|身份证反面地址|身份证反面地址|
|businessLicense|string|false|none|营业执照地址|营业执照地址|
|contactAddress|string|false|none|联系地址，支持身份证识别填充|联系地址，支持身份证识别填充|
|ownerId|string|false|none|维护人id|维护人id|
|ownerName|string|false|none|维护人姓名|维护人姓名|
|attachmentFiles|string|false|none|附件|附件|
|remark|string|false|none|备注|备注|
|isDel|boolean|false|none|0 否 1是|0 否 1是|
|contactList|[[CustomerContactAddDTO](#schemacustomercontactadddto)]|false|none|联系人列表|联系人列表|
|guarantorList|[[CustomerGuarantorAddDTO](#schemacustomerguarantoradddto)]|false|none|担保人列表|担保人列表|
|bankAccountList|[[CustomerBankAccountAddDTO](#schemacustomerbankaccountadddto)]|false|none|银行账号列表|银行账号列表|
|invoiceList|[[CustomerInvoiceAddDTO](#schemacustomerinvoiceadddto)]|false|none|开票信息列表|开票信息列表|

<h2 id="tocS_CustomerInvoiceAddDTO">CustomerInvoiceAddDTO</h2>

<a id="schemacustomerinvoiceadddto"></a>
<a id="schema_CustomerInvoiceAddDTO"></a>
<a id="tocScustomerinvoiceadddto"></a>
<a id="tocscustomerinvoiceadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "customerId": "string",
  "title": "string",
  "taxNumber": "string",
  "phone": "string",
  "address": "string",
  "bankName": "string",
  "accountNumber": "string",
  "isDel": true
}

```

开票信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|customerId|string|false|none|客户ID|客户ID|
|title|string|false|none|抬头名称|抬头名称|
|taxNumber|string|false|none|税号|税号|
|phone|string|false|none|电话号码|电话号码|
|address|string|false|none|单位地址|单位地址|
|bankName|string|false|none|开户银行|开户银行|
|accountNumber|string|false|none|银行账号|银行账号|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerBankAccountAddDTO">CustomerBankAccountAddDTO</h2>

<a id="schemacustomerbankaccountadddto"></a>
<a id="schema_CustomerBankAccountAddDTO"></a>
<a id="tocScustomerbankaccountadddto"></a>
<a id="tocscustomerbankaccountadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "customerId": "string",
  "bankName": "string",
  "accountNumber": "string",
  "accountRemark": "string",
  "isDel": true
}

```

银行账号列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|customerId|string|false|none|客户ID|客户ID|
|bankName|string|false|none|开户银行|开户银行|
|accountNumber|string|false|none|银行账号|银行账号|
|accountRemark|string|false|none|账户备注|账户备注|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerGuarantorAddDTO">CustomerGuarantorAddDTO</h2>

<a id="schemacustomerguarantoradddto"></a>
<a id="schema_CustomerGuarantorAddDTO"></a>
<a id="tocScustomerguarantoradddto"></a>
<a id="tocscustomerguarantoradddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "customerId": "string",
  "name": "string",
  "phone": "string",
  "idType": 0,
  "idNumber": "string",
  "address": "string",
  "remark": "string",
  "isDel": true
}

```

担保人列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|customerId|string|false|none|客户ID|客户ID|
|name|string|false|none|担保人姓名|担保人姓名|
|phone|string|false|none|联系电话|联系电话|
|idType|integer(int32)|false|none|证件类型|证件类型|
|idNumber|string|false|none|证件号码|证件号码|
|address|string|false|none|地址|地址|
|remark|string|false|none|备注，200字限制|备注，200字限制|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerContactAddDTO">CustomerContactAddDTO</h2>

<a id="schemacustomercontactadddto"></a>
<a id="schema_CustomerContactAddDTO"></a>
<a id="tocScustomercontactadddto"></a>
<a id="tocscustomercontactadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "customerId": "string",
  "name": "string",
  "phone": "string",
  "gender": 0,
  "relationship": 0,
  "idNumber": "string",
  "position": "string",
  "department": "string",
  "isPreferred": true,
  "remark": "string",
  "isDel": true
}

```

联系人列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|customerId|string|false|none|客户ID|客户ID|
|name|string|false|none|联系人姓名|联系人姓名|
|phone|string|false|none|联系电话|联系电话|
|gender|integer(int32)|false|none|性别（1男 2女 3未知）|性别（1男 2女 3未知）|
|relationship|integer(int32)|false|none|关系（1夫妻 2合伙人 3父母 4朋友 5子女 6其他）|关系（1夫妻 2合伙人 3父母 4朋友 5子女 6其他）|
|idNumber|string|false|none|证件号码|证件号码|
|position|string|false|none|职务，仅企业客户适用|职务，仅企业客户适用|
|department|string|false|none|部门，仅企业客户适用|部门，仅企业客户适用|
|isPreferred|boolean|false|none|是否首选联系人，默认最新记录为首选|是否首选联系人，默认最新记录为首选|
|remark|string|false|none|补充说明|补充说明|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerUpdateDTO">CustomerUpdateDTO</h2>

<a id="schemacustomerupdatedto"></a>
<a id="schema_CustomerUpdateDTO"></a>
<a id="tocScustomerupdatedto"></a>
<a id="tocscustomerupdatedto"></a>

```json
{
  "customerIds": [
    "string"
  ],
  "ownerId": "string",
  "ownerName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|customerIds|[string]|true|none|客户ID数组|客户ID数组|
|» 客户ID数组|string|false|none|客户ID数组|客户ID数组|
|ownerId|string|true|none|维护人id|维护人id|
|ownerName|string|true|none|维护人姓名|维护人姓名|

<h2 id="tocS_CustomerSimpleVo">CustomerSimpleVo</h2>

<a id="schemacustomersimplevo"></a>
<a id="schema_CustomerSimpleVo"></a>
<a id="tocScustomersimplevo"></a>
<a id="tocscustomersimplevo"></a>

```json
{
  "id": "string",
  "customerName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|customerName|string|false|none|客户姓名，支持身份证识别填充|客户姓名，支持身份证识别填充|

