<template>
  <div class="container">
    <a-card class="general-card" :title="'登录日志'">
      <a-row>
        <a-col :flex="1">
          <a-form :model="formModel" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
            label-align="right">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="ipaddr" label="登录地址">
                  <a-input v-model="formModel.ipaddr" placeholder="请输入登录地址" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="userName" label="用户名称">
                  <a-input v-model="formModel.userName" placeholder="请输入用户名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" label="状态">
                  <a-select v-model="formModel.status" placeholder="登录状态">
                    <a-option v-for="dict in dictData.sys_common_status" :key="dict.value" :value="dict.value">
                      {{ dict.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="dateRange" label="登录时间">
                  <a-range-picker v-model="dateRange" style="width: 100%" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button status="danger" :disabled="!selectedKeys.length" @click="handleDelete">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
            <a-button status="danger" @click="handleClean">
              <template #icon><icon-delete /></template>
              清空
            </a-button>
            <a-button status="success" :disabled="selectedKeys.length !== 1" @click="handleUnlock">
              <template #icon><icon-unlock /></template>
              解锁
            </a-button>
            <a-button status="warning" @click="handleExport">
              <template #icon><icon-download /></template>
              导出
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-table :row-selection="rowSelection" row-key="infoId" :loading="loading" :data="tableData"
        :pagination="pagination" :bordered="false" @page-change="onPageChange" @page-size-change="onPageSizeChange"
        @selection-change="handleSelectionChange">
        <template #columns>
          <a-table-column type="selection" width="55" align="center" />
          <a-table-column title="访问编号" data-index="infoId" />
          <a-table-column title="用户名称" data-index="userName" :sortable="true" />
          <a-table-column title="地址" data-index="ipaddr" :width="130" />
          <a-table-column title="登录状态" data-index="status">
            <template #cell="{ record }">
              <a-tag :color="record.status === '0' ? 'green' : 'red'">
                {{ record.status === '0' ? '成功' : '失败' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="描述" data-index="msg" />
          <a-table-column title="访问时间" data-index="accessTime" :width="180" :sortable="true">
            <template #cell="{ record }">
              {{ formatDate(record.accessTime) }}
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import useLoading from '@/hooks/loading';
import { formatDate } from '@/utils/index';
import { exportExcel } from '@/utils/exportUtil';
import { exportLoginInfoLog, list, delLogininfor, cleanLogininfor, unlockLogininfor, LoginInfo } from '@/api/system/logininfor'

const { loading, setLoading } = useLoading();
const tableData = ref<LoginInfo[]>([]);
const selectedKeys = ref<string[]>([]);
const dateRange = ref([]);

const formModel = reactive({
  ipaddr: '',
  userName: '',
  status: '',
  pageNum: 1,
  pageSize: 10
});

const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showPageSize: true
});

const dictData = reactive({
  sys_common_status: [
    { label: '成功', value: '0' },
    { label: '失败', value: '1' }
  ]
});

const search = async () => {
  setLoading(true);
  try {
    let params = {}
    if (dateRange.value[0] && dateRange.value[1]) {
      params = { ...formModel, ...{ beginTime: dateRange.value[0], endTime: dateRange.value[1] } }
    } else {
      params = { ...formModel }
    }
    const res = await list(params);
    tableData.value = res.rows;
    pagination.total = res.total;
  } finally {
    setLoading(false);
  }
};

const reset = () => {
  formModel.ipaddr = '';
  formModel.userName = '';
  formModel.status = '';
  dateRange.value = [];
  // 重置分页参数
  pagination.current = 1;
  pagination.pageSize = 10;
  search();
};

// 每页条数改变
const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;  // 切换每页条数时重置为第一页
  formModel.pageSize = pageSize;
  formModel.pageNum = 1;
  search();
};

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});

const handleSelectionChange = (rowKeys: string[]) => {
  selectedKeys.value = rowKeys
};

const handleDelete = async () => {
  if (!selectedKeys.value.length) return;
  try {
    const handleConfirm = async () => {
      try {
        const res = await delLogininfor(selectedKeys.value.join(','));

        if (res.code === 200) {
          Message.success('删除成功')
          search()
        } else {
          Message.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败', error)
        Message.error('删除失败')
      }
    }
    await Modal.confirm({
      title: '确认删除',
      content: `是否确认删除日志编号为"${selectedKeys.value.join(',')}"的数据项？`,
      onOk: handleConfirm
    });
  } catch (err) {
    Message.error('删除失败');
  }
};

const handleClean = async () => {
  try {
    const handleConfirm = async () => {
      try {
        const res = await cleanLogininfor();
        if (res.code === 200) {
          Message.success('清空成功');
          search()
        } else {
          Message.error(res.msg || '清空失败')
        }
      } catch (error) {
        console.error('清空失败', error)
        Message.error('清空失败')
      }
    }
    await Modal.confirm({
      title: '确认清空',
      content: `是否确认清空所有登录日志数据项？`,
      onOk: handleConfirm
    });
  } catch (err) {
    Message.error('清空失败');
  }
};

const handleUnlock = async () => {
  if (selectedKeys.value.length !== 1) return;
  try {
    await unlockLogininfor(selectedKeys.value[0]);
    Message.success('解锁成功');
    search();
  } catch (err) {
    Message.error('解锁失败');
  }
};

const handleExport = () => {
  let params = {}
  if (dateRange.value[0] && dateRange.value[1]) {
    params = { ...formModel, ...{ beginTime: dateRange.value[0], endTime: dateRange.value[1] } }
  } else {
    params = { ...formModel }
  }
  exportExcel(exportLoginInfoLog, params, `loginInfoLog_${new Date().getTime()}`)
};

// script部分
const onPageChange = (current: number) => {
  formModel.pageNum = current;
  pagination.current = current;
  search();
};

search();
</script>

<style scoped>
.container {
  padding: 0 16px 16px 16px;
}

.general-card {
  border-radius: 4px;
}
</style>
