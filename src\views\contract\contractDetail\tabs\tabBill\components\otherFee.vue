<template>
    <section>
        <sectionTitle title="其他费用"></sectionTitle>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table :data="tableData" :pagination="false" :scroll="{ x: 1200 }" :bordered="{ cell: true }">
                <template #columns>
                    <a-table-column title="租期" data-index="rentDate" :width="160" align="center">
                        <template #cell="{ record }">
                            {{ dayjs(record.startDate).format('YYYY-MM-DD') }} 至 {{ dayjs(record.endDate).format('YYYY-MM-DD') }}
                        </template>
                    </a-table-column>
                    <a-table-column title="商户" data-index="customerName" :width="100" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="费项" data-index="subjectName" :width="100" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="应收日期" data-index="receivableDate" :width="100" align="center" />
                    <a-table-column title="账单总额（元）" data-index="totalAmount" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.totalAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="优惠金额（元）" data-index="discountAmount" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.discountAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单应收金额（元）" data-index="actualReceivable" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单已收金额（元）" data-index="receivedAmount" :width="160" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.receivedAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单剩余应收金额（元）" data-index="remainingAmount" :width="180" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable - record.receivedAmount) }}
                        </template>
                    </a-table-column>
                </template>
            </a-table>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import { ContractAddDTO, ContractVo } from '@/api/contract';
import dayjs from 'dayjs';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractDetailCosts as ContractVo)
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

const tableData = ref<any[]>([])

watch(contractCosts, (newVal) => {
    if (newVal.costs && newVal.costs.length > 0) {
        const otherCosts = newVal.costs.filter(item => item.costType === 3)
        tableData.value = otherCosts
    } else {
        tableData.value = []
    }
}, { deep: true, immediate: true })
</script>

<style lang="less" scoped>
.detail-text {
    color: #333;
}
</style>