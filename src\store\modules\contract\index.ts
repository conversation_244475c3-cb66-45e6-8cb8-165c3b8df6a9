// stores/contractStore.ts
import { defineStore } from 'pinia';
import {
    Contract,
    ContractBooking,
    ContractCost,
    ContractCustomer,
    ContractFee,
    ContractRoom,
} from '@/views/contract/type';
import {
    ContractAddDTO,
    ContractPermissionVo,
    ContractRoomDTO,
    ContractCostPlanVo,
    ContractCostDTO,
    ContractVo,
} from '@/api/contract';

export const useContractStore = defineStore('contract', {
    state: () => ({
        // 合同数据（新增、编辑）
        contractData: initContractData(),
        editType: 'create',
        currentProjectId: '',
        contractPermission: {} as ContractPermissionVo,
        // 应收计划
        contractCosts: {} as ContractCostPlanVo,
        // 商业主体公司
        merchantList: [] as any[],
        // 用地性质
        landUsageList: [] as any[],
        // 剩余定金实收金额
        remainingBookingReceivedAmount: 0,
        // 合同详情
        contractDetail: {},
        // 合同详情账单
        contractDetailCosts: {},
        // 合同详情承租人信息
        contractLesseeInfo: {},
        // 历史版本合同详情
        historyVersionContractDetail: {},
        // 历史版本合同详情账单
        historyVersionContractDetailCosts: {},
        // 历史版本合同详情承租人信息
        historyVersionContractLesseeInfo: {},
        // 合同变更类型
        changeType: [] as number[],
        // 预选择的房源ID（用于租客签约时自动填充）
        preSelectedRoomId: '' as string,
        // 是否是审批流程
        isApproval: false,
        // 非标合同是否已经调用过计算金额接口
        hasCalculatedMoney: false,
        // 是否有用户自定义数据（手动添加账单或快速添加）
        hasUserCustomData: false,
        // 变更合同时，变更执行日期之前的租金数据（需要保留展示）
        preChangeRentCosts: [] as ContractCostDTO[]
    }),
    actions: {
        updateCustomer(customer: ContractCustomer) {
            this.contractData.customer = {
                ...this.contractData.customer,
                ...customer,
            };
        },
        resetContract() {
            this.contractData = initContractData();
            this.preSelectedRoomId = '';
            this.changeType = [];
            this.hasCalculatedMoney = false;
            this.hasUserCustomData = false;
            this.preChangeRentCosts = [];
        },
        resetContractDetail() {
            this.contractDetail = initContractDataDetail();
        },
        resetHistoryVersionContractDetail() {
            this.historyVersionContractDetail = initContractDataDetail();
        },
        setCurrentProjectId(projectId: string) {
            this.currentProjectId = projectId;
        },

        /**
         * 保存变更执行日期之前的租金数据
         * 在变更合同调用generateCost时，保存变更执行日期之前的租金数据
         */
        savePreChangeRentCosts() {
            if (this.editType !== 'change' || !this.contractData.changeDate) {
                return;
            }

            const changeDate = this.contractData.changeDate;
            const rentCosts = this.contractCosts.costs?.filter(cost => cost.costType === 2) || [];
            
            // 筛选出变更执行日期之前的租金数据（租期结束日期小于变更执行日期）
            this.preChangeRentCosts = rentCosts.filter(cost => {
                if (!cost.endDate) return false;
                return new Date(cost.endDate) < new Date(changeDate);
            });

            console.log('保存变更执行日期之前的租金数据:', this.preChangeRentCosts);
        },

        /**
         * 定金结转逻辑
         * 将bookings中的定金按顺序结转到保证金和租金的已收金额中
         */
        transferBookingAmount() {
            if (!this.contractCosts.bookings || !this.contractCosts.costs) {
                return;
            }

            // 重置所有已收金额和结转金额
            this.contractCosts.costs.forEach(cost => {
                cost.receivedAmount = 0;
            });

            this.contractCosts.bookings.forEach(booking => {
                booking.transferBondAmount = 0;
                booking.transferRentAmount = 0;
            });

            // 按顺序处理每笔定金
            this.contractCosts.bookings.forEach(booking => {
                let remainingAmount = booking.bookingReceivedAmount || 0;

                if (remainingAmount <= 0) return;

                // 1. 优先结转到保证金（costType = 1）
                const bondCosts = this.contractCosts.costs?.filter(cost => cost.costType === 1) || [];
                // 按期数排序
                bondCosts.sort((a, b) => (a.period || 0) - (b.period || 0));

                for (const bondCost of bondCosts) {
                    if (remainingAmount <= 0) break;

                    const actualReceivable = bondCost.actualReceivable || 0;
                    const currentReceived = bondCost.receivedAmount || 0;
                    const needAmount = actualReceivable - currentReceived;

                    if (needAmount > 0) {
                        const transferAmount = Math.min(remainingAmount, needAmount);
                        bondCost.receivedAmount = currentReceived + transferAmount;
                        booking.transferBondAmount = (booking.transferBondAmount || 0) + transferAmount;
                        remainingAmount -= transferAmount;
                    }
                }

                // 2. 如果保证金结转完还有剩余，结转到租金（costType = 2）
                if (remainingAmount > 0) {
                    const rentCosts = this.contractCosts.costs?.filter(cost => cost.costType === 2) || [];
                    // 按期数排序
                    rentCosts.sort((a, b) => (a.period || 0) - (b.period || 0));

                    for (const rentCost of rentCosts) {
                        if (remainingAmount <= 0) break;

                        const actualReceivable = rentCost.actualReceivable || 0;
                        const currentReceived = rentCost.receivedAmount || 0;
                        const needAmount = actualReceivable - currentReceived;

                        if (needAmount > 0) {
                            const transferAmount = Math.min(remainingAmount, needAmount);
                            rentCost.receivedAmount = currentReceived + transferAmount;
                            booking.transferRentAmount = (booking.transferRentAmount || 0) + transferAmount;
                            remainingAmount -= transferAmount;
                        }
                    }
                }
            });

            // 更新剩余定金实收金额
            this.remainingBookingReceivedAmount = this.contractCosts.bookings?.reduce((acc, booking) => {
                const received = booking.bookingReceivedAmount || 0;
                const transferred = (booking.transferBondAmount || 0) + (booking.transferRentAmount || 0);
                return acc + (received - transferred);
            }, 0) || 0;
        },
    },
});

/**
 * @description 初始化合同数据
 * @returns
 */
function initContractData(): ContractAddDTO {
    return {
        // 基本信息
        id: undefined,
        projectId: undefined,
        projectName: undefined,
        contractNo: undefined,
        unionId: undefined,
        version: undefined,
        isCurrent: false,
        status: 10, // 默认为草稿状态
        statusTwo: undefined,
        approveStatus: 0, // 默认待审核
        operateType: 0, // 默认新签
        contractType: 0,
        ourSigningId: undefined,
        ourSigningParty: undefined,
        signWay: undefined,
        signType: 0,
        originId: undefined,
        changeFromId: undefined,
        landUsage: undefined,
        signerId: undefined,
        signerName: undefined,
        ownerId: undefined,
        ownerName: undefined,
        contractMode: 0,
        paperContractNo: undefined,
        signDate: undefined,
        handoverDate: undefined,
        contractPurpose: undefined,
        dealChannel: undefined,
        assistantId: undefined,
        assistantName: undefined,

        // 租期相关
        rentYear: 0,
        rentMonth: 0,
        rentDay: 0,
        startDate: undefined,
        endDate: undefined,
        effectDate: undefined,

        // 保证金相关
        bondReceivableDate: undefined,
        bondReceivableType: 0,
        bondPriceType: undefined,
        bondPrice: undefined,

        // 租金相关
        chargeWay: 0,
        rentReceivableDate: undefined,
        rentReceivableType: 1,
        rentTicketPeriod: 1,
        rentPayPeriod: 1,
        increaseGap: undefined,
        increaseRate: undefined,
        increaseRule: undefined,
        pmPayPeriod: 0,
        // 营收相关
        estimateRevenue: undefined,
        percentageType: 1,
        fixedPercentage: undefined,
        stepPercentage: undefined,
        revenueType: 1,

        // 物业费相关
        isIncludePm: false,
        pmUnitPrice: undefined,
        pmMonthlyPrice: undefined,

        // 合同总价
        totalPrice: undefined,

        // 业务信息
        bizTypeId: undefined,
        bizTypeName: undefined,
        lesseeBrand: undefined,
        businessCategory: undefined,
        openDate: undefined,
        fireRiskCategory: undefined,
        sprinklerSystem: undefined,
        factoryEngaged: undefined,
        deliverDate: undefined,

        // 车位信息
        parkingSpaceType: undefined,
        hasParkingFee: false,
        parkingFeeAmount: undefined,

        // 附加信息
        otherInfo: undefined,
        contractAttachments: undefined,
        attachmentsPlan: undefined,

        // 确认状态
        isUploadSignature: false,
        isSignatureConfirm: false,
        isPaperConfirm: false,
        isFinish: false,
        isDel: false,
        isSubmit: false,
        createByName: '',
        createTime: '',
        bookingRelType: 1,

        // 相关对象
        customer: {
            id: undefined,
            contractId: undefined,
            customerId: undefined,
            customerType: 1, // 默认个人（修改：原来是2-企业，现在改为1-个人）
            customerName: '',
            address: '',
            phone: '',
            idType: '1',
            idNumber: '',
            isEmployee: undefined,
            creditCode: '',
            contactName: '',
            contactPhone: '',
            contactIdNumber: '',
            legalName: '',
            paymentAccount: '',
            guarantorName: undefined,
            guarantorPhone: undefined,
            guarantorIdType: undefined,
            guarantorIdNumber: undefined,
            guarantorAddress: undefined,
            guarantorIdFront: undefined,
            guarantorIdBack: undefined,
            invoiceTitle: undefined,
            invoiceTaxNumber: undefined,
            invoiceAddress: undefined,
            invoicePhone: undefined,
            invoiceBankName: undefined,
            invoiceAccountNumber: undefined,
        } as ContractCustomer,

        bookings: [] as ContractBooking[],
        fees: [] as ContractFee[],
        costs: [] as ContractCostDTO[],
        rooms: [] as ContractRoomDTO[],

        // 变更相关字段
        changeType: undefined,
        changeDate: undefined,
        changeExplanation: undefined,
        changeAttachments: undefined,
    };
}

/**
 * @description 初始化合同数据
 * @returns
 */
function initContractDataDetail(): ContractVo {
    return {
        id: undefined,
        projectId: undefined,
        contractNo: undefined,
        unionId: undefined,
        version: undefined,
        isCurrent: false,
        status: undefined,
        statusTwo: undefined,
        approveStatus: undefined,
        operateType: undefined,
        contractType: undefined,
        ourSigningParty: undefined,
        signWay: undefined,
        signType: undefined,
        originId: undefined,
        changeFromId: undefined,
        landUsage: undefined,
        signerId: undefined,
        signerName: undefined,
        ownerId: undefined,
        ownerName: undefined,
        contractMode: undefined,
        paperContractNo: undefined,
        signDate: undefined,
        handoverDate: undefined,
        contractPurpose: undefined,
        dealChannel: undefined,
        assistantId: undefined,
        assistantName: undefined,
        rentYear: undefined,
        rentMonth: undefined,
        rentDay: undefined,
        startDate: undefined,
        endDate: undefined,
        effectDate: undefined,
        bondReceivableDate: undefined,
        bondReceivableType: undefined,
        bondPriceType: undefined,
        bondPrice: undefined,
        chargeWay: undefined,
        rentReceivableDate: undefined,
        rentReceivableType: undefined,
        rentTicketPeriod: undefined,
        rentPayPeriod: undefined,
        increaseGap: undefined,
        increaseRate: undefined,
        increaseRule: undefined,
        estimateRevenue: undefined,
        percentageType: undefined,
        fixedPercentage: undefined,
        stepPercentage: undefined,
        revenueType: undefined,
        isIncludePm: undefined,
        pmUnitPrice: undefined,
        pmMonthlyPrice: undefined,
        totalPrice: undefined,
        bizTypeId: undefined,
        bizTypeName: undefined,
        lesseeBrand: undefined,
        businessCategory: undefined,
        openDate: undefined,
        fireRiskCategory: undefined,
        sprinklerSystem: undefined,
        factoryEngaged: undefined,
        deliverDate: undefined,
        parkingSpaceType: undefined,
        hasParkingFee: undefined,
        parkingFeeAmount: undefined,
        otherInfo: undefined,
        signAttachments: undefined,
        attachmentsPlan: undefined,
        isUploadSignature: false,
        isSignatureConfirm: false,
        isPaperConfirm: false,
        isFinish: false,
        isDel: false,
        createByName: '',
        customer: {} as ContractCustomer,
        bookings: [] as ContractBooking[],
        fees: [] as ContractFee[],
        costs: [] as ContractCostDTO[],
        rooms: [] as ContractRoomDTO[],
        pmPayPeriod: 0,
    };
}
