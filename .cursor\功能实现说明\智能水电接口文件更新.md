# 智能水电接口文件更新功能实现说明

## 更新概述
根据更新后的 `.cursor/api/智能水电.md` 文档，清理了 `src/api/smartWaterElectricity.ts` 接口文件中的无效接口，保留了文档中明确定义的接口。

## 主要修改内容

### 1. 移除的无效接口
根据API文档，以下接口已被移除：

#### 水电用量记录管理接口（已移除）
- `addWaterElectricityLog` - 新增房间水电用量
- `updateWaterElectricityLog` - 修改房间水电用量  
- `deleteWaterElectricityLog` - 删除房间水电用量
- `getWaterElectricityDetail` - 获取房间水电用量详细信息

#### 移除的类型定义
- `RoomWaterElectricityLogAddDTO` - 房间水电用量新增/修改参数
- `TableDataInfo` - 分页响应结果（不再使用）

### 2. 保留的有效接口

#### 房源管理接口
1. **getWaterElectricityRoomList** - 查询房源信息列表
   - 路径：`POST /water/electricity/room/list`
   - 参数：`WaterElectricityQueryDTO`
   - 返回：`RoomSimpleForWaterVo[]`

2. **importWaterElectricityRoomList** - 房源信息列表导入
   - 路径：`POST /water/electricity/room/import`
   - 参数：`File`
   - 返回：`AjaxResult`

3. **exportWaterElectricityList** - 导出房间水电用量列表
   - 路径：`POST /water/electricity/room/export`
   - 参数：`WaterElectricityQueryDTO`
   - 返回：`Blob`

#### 智能水电管理接口
4. **getWaterElectricityList** - 智能水电列表接口
   - 路径：`GET /water/electricity/list`
   - 参数：`WaterElectricityQueryDTO`
   - 返回：`RoomVo[]`

5. **getWaterElectricityRoomDetail** - 智能水电详情
   - 路径：`GET /water/electricity/detail`
   - 参数：`roomId: string`
   - 返回：`RoomWaterElectricityDetailVo`

6. **getWaterElectricityLogList** - 智能水电记录
   - 路径：`POST /water/electricity/log/detail`
   - 参数：`RoomWaterElectricityLogQueryDTO`
   - 返回：`RoomWaterElectricityLogVo[]`

#### 绑定关系管理接口
7. **saveWaterElectricityBindRelation** - 保存关联关系接口
   - 路径：`POST /water/electricity/room/bind`
   - 参数：`RoomSimpleForWaterVo[]`
   - 返回：`boolean`

8. **deleteWaterElectricityBindRelation** - 解绑房间与运营系统的关联关系
   - 路径：`POST /water/electricity/room/bind/delete`
   - 参数：`string[]`
   - 返回：`boolean`

### 3. 接口参数和路径更新

#### 参数类型统一
- `getWaterElectricityList` 接口参数类型从内联对象改为使用 `WaterElectricityQueryDTO`
- 所有GET请求的参数都使用 `{ params: ... }` 格式

#### 路径修正
- `exportWaterElectricityList` 路径从 `/water/export` 更新为 `/water/electricity/room/export`
- `getWaterElectricityRoomDetail` 参数格式从直接传递改为 `{ params: { roomId } }`

### 4. 保留的类型定义

#### 基础类型
- `BasePageParams` - 基础分页参数
- `AjaxResult` - 通用响应结果

#### 查询参数类型
- `WaterElectricityQueryDTO` - 房源查询参数
- `RoomWaterElectricityLogQueryDTO` - 房间水电用量查询参数

#### 数据模型类型
- `RoomSimpleForWaterVo` - 房源信息（用于水电）
- `RoomWaterElectricityLogVo` - 房间水电用量日志信息
- `RoomVo` - 房间信息（完整）
- `RoomWaterElectricityDetailVo` - 房间水电详情信息

## 接口功能分类

### 房源管理功能
- 查询房源列表（支持筛选）
- 导入房源信息
- 导出房源列表

### 智能水电设备管理功能
- 查询智能水电设备列表
- 查看设备详情
- 查询水电用量记录

### 绑定关系管理功能
- 保存房源与运营系统的绑定关系
- 解绑房源与运营系统的关联关系

## 技术特点

### 1. 接口规范
- 统一使用 `/business-rent-admin` 作为URL前缀
- POST请求用于复杂查询和数据提交
- GET请求用于简单查询
- 文件上传使用 `multipart/form-data`
- 文件下载使用 `responseType: 'blob'`

### 2. 类型安全
- 完整的TypeScript类型定义
- 严格的参数类型检查
- 明确的返回值类型

### 3. 参数处理
- 分页参数统一继承 `BasePageParams`
- 查询参数支持可选字段
- 文件上传自动处理FormData

## 影响范围

### 需要更新的组件
由于移除了水电用量记录的增删改接口，以下组件可能需要调整：

1. **WaterElectricDetailDrawer.vue**
   - 移除新增/编辑/删除水电用量记录的功能
   - 只保留查看水电用量记录的功能

2. **WaterElectricBatchBindDrawer.vue**
   - 确保使用正确的接口进行房源查询和绑定操作

### 功能调整建议
1. 水电用量记录改为只读模式，不支持前端增删改操作
2. 如需要管理水电用量记录，可能需要通过其他系统或接口实现
3. 专注于房源与运营系统的绑定关系管理

## 注意事项
1. 所有接口都已根据最新API文档进行更新
2. 移除的接口如果在组件中被使用，需要相应调整组件逻辑
3. 导出功能的参数类型已从 `RoomWaterElectricityLogQueryDTO` 改为 `WaterElectricityQueryDTO`
4. 接口调用时需要注意参数格式的变化（特别是GET请求的参数包装）
