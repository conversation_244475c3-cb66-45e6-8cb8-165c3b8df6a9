<template>
    <div class="exit-detail">
        <section-title title="基本信息" />
        <a-row :gutter="16" class="info-section">
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">合同编号:</span>
                    <span class="value">{{ data?.contractNo }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">合同用途:</span>
                    <span class="value">{{ data?.contractPurpose }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">承租方:</span>
                    <span class="value">{{ data?.tenantName }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">退租类型:</span>
                    <span class="value">{{ data?.exitType }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">退租日期:</span>
                    <span class="value">{{ data?.exitDate }}</span>
                </div>
            </a-col>
        </a-row>

        <section-title title="退租房源" />
        <a-table :data="houseData" :columns="columns" :pagination="false" :bordered="{ cell: true }">
            <template #operations="{ record }">
                <a-space>
                    <a-button type="text" size="mini" @click="handleHouseExit(record)">办理退租</a-button>
                    <a-button type="text" size="mini" @click="handlePropertyCheck(record)">验收</a-button>
                </a-space>
            </template>
        </a-table>

        <!-- 验收弹窗 -->
        <a-modal v-model:visible="checkModalVisible" title="房源验收" @cancel="handleCheckCancel" @ok="handleCheckConfirm">
            <a-form :model="checkForm" :label-col-props="{ span: 4 }" :wrapper-col-props="{ span: 20 }">
                <a-form-item field="checkResult" label="验收结果">
                    <a-radio-group v-model="checkForm.checkResult">
                        <a-radio :value="1">通过</a-radio>
                        <a-radio :value="0">不通过</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item field="checkRemark" label="验收说明">
                    <a-textarea v-model="checkForm.checkRemark" placeholder="请输入验收说明" />
                </a-form-item>
            </a-form>
        </a-modal>

        <a-divider />
        <div class="footer-actions">
            <a-space>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" @click="handleSave">保存</a-button>
            </a-space>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineProps, defineEmits } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'

interface HouseItem {
    id: string
    buildingName: string
    houseName: string
    houseArea: number
    houseType: string
    status: number
    checkStatus?: number
    [key: string]: any
}

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
})

const emits = defineEmits(['cancel', 'save'])

// 房源数据
const houseData = ref<HouseItem[]>([])

// 表格列定义
const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 80,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '楼栋名称',
        dataIndex: 'buildingName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '房源名称',
        dataIndex: 'houseName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '房源面积(㎡)',
        dataIndex: 'houseArea',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '房源类型',
        dataIndex: 'houseType',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        render: ({ record }: { record: HouseItem }) => {
            if (record.status === 0) return '待退租'
            if (record.status === 1) return '已退租'
            if (record.status === 2) return '已验收'
            return '未知'
        },
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 160,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 验收弹窗
const checkModalVisible = ref(false)
const currentCheckHouse = ref<HouseItem | null>(null)
const checkForm = reactive({
    checkResult: 1,
    checkRemark: ''
})

// 初始化
onMounted(() => {
    // 模拟获取待退租房源数据
    if (props.data && props.data.id) {
        // 实际项目中应该调用API获取待退租房源列表
        houseData.value = generateMockHouseData()
    }
})

// 模拟待退租房源数据
const generateMockHouseData = () => {
    const mockData: HouseItem[] = []
    for (let i = 1; i <= 5; i++) {
        mockData.push({
            id: `house-${i}`,
            index: i,
            buildingName: `A${i}栋`,
            houseName: `A${i}-${100 + i}室`,
            houseArea: 80 + i * 10,
            houseType: i % 2 === 0 ? '商铺' : '办公室',
            status: 0
        })
    }
    return mockData
}

// 办理单个房源退租
const handleHouseExit = (record: HouseItem) => {
    record.status = 1
}

// 房源验收
const handlePropertyCheck = (record: HouseItem) => {
    if (record.status !== 1) {
        // 只有已退租的房源才能验收
        return
    }
    currentCheckHouse.value = record
    checkForm.checkResult = 1
    checkForm.checkRemark = ''
    checkModalVisible.value = true
}

// 取消验收
const handleCheckCancel = () => {
    checkModalVisible.value = false
    currentCheckHouse.value = null
}

// 确认验收
const handleCheckConfirm = () => {
    if (currentCheckHouse.value) {
        currentCheckHouse.value.status = 2
        currentCheckHouse.value.checkStatus = checkForm.checkResult
        currentCheckHouse.value.checkRemark = checkForm.checkRemark
    }
    checkModalVisible.value = false
}

// 取消
const cancel = () => {
    emits('cancel')
}

// 保存
const handleSave = () => {
    // 检查是否所有房源都已退租且验收
    const allProcessed = houseData.value.every(item => item.status === 2)
    
    // 提交数据
    const formData = {
        contractId: props.data?.contractId,
        houses: houseData.value.map(item => ({
            houseId: item.id,
            status: item.status,
            checkStatus: item.checkStatus || null,
            checkRemark: item.checkRemark || ''
        }))
    }
    
    emits('save', formData)
}
</script>

<style scoped>
.exit-detail {
    padding: 16px;
}

.info-section {
    margin-bottom: 24px;
}

.info-item {
    margin-bottom: 16px;
    display: flex;
}

.label {
    color: #86909c;
    margin-right: 8px;
    min-width: 80px;
}

.value {
    color: #1d2129;
    flex: 1;
}

.footer-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
</style> 