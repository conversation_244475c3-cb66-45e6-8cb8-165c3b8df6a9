<template>
    <div class="data-roles-list">
        <div class="common-flex data-roles-list-header">
            <div class="data-roles-list-header-title">
                <span class="common-header-tag"></span>
                <span>OA组织架构</span>
            </div>
        </div>
        <div class="data-roles-list-search">
            <a-input v-model:value="searchValue" placeholder="请输入内容" />
        </div>
        <div class="data-roles-list-content">
            <div class="data-roles-list-content-list">
                <!-- default-expand-all :selected-key="currentItemId"  -->
                <a-tree :data="list" :field-names="{ title: 'name', key: 'id', children: 'orgList' }" show-line @select="handleSelect" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, nextTick } from 'vue'
import { getOrgTree } from '@/api/org'
import { getEhrOrgList } from '@/api/ehr'

const searchValue = ref('')
const list = ref<any[]>([])
const currentItemId = ref(null)

const emit = defineEmits(['change'])

const handleSelect = (keys: any[]) => {
    console.log(keys, currentItemId.value)
    emit('change', keys)
}


const fetchData = async () => {
    const { data } = await getEhrOrgList({})
    list.value = data as any[]
}

fetchData()
</script>
<script lang="ts">
    export default { name: 'orgTree' }
</script>

<style lang="less" scoped>
    .data-roles-list {
        width: 100%;
        height: 100%;
        background: #fff;
        border-radius: 4px;
        padding: 12px;
        box-sizing: border-box;
        .data-roles-list-header {
            justify-content: space-between;
            .data-roles-list-header-title {
                display: flex;
                align-items: center;
            }
            .data-roles-list-header-right {
                display: flex;
                align-items: center;
                color: rgb(var(--arcoblue-6));
                cursor: pointer;
                .data-roles-list-header-right-button {
                    padding: 0 !important;
                }
            }
        }
        .data-roles-list-search {
            margin-top: 12px;
        }
        .data-roles-list-content {
            margin-top: 12px;

            .data-roles-list-content-list {
                .data-roles-list-content-item {
                    padding: 12px;
                    border-bottom: 1px solid #f0f0f0;
                    cursor: pointer;
                    justify-content: space-between;
                    &:hover {
                        background: var(--color-fill-2);
                    }
                    .data-roles-list-content-item-right-handle {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        cursor: pointer;
                    }
                }
                .data-roles-list-content-item-on {
                    background: rgb(var(--arcoblue-6));
                    color: #fff;
                    &:hover {
                        background: rgb(var(--arcoblue-6));
                    }
                }
            }
        }
    }
</style>
