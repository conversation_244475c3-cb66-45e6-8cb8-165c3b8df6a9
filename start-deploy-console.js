#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 正在启动部署控制台...');
console.log('📋 新功能: 支持选择分支部署！');

// 检查是否安装了express
try {
    require('express');
} catch (error) {
    console.log('📦 正在安装依赖包...');
    const install = spawn('npm', ['install', 'express'], { stdio: 'inherit', shell: true });
    
    install.on('close', (code) => {
        if (code === 0) {
            console.log('✅ 依赖安装完成');
            startServer();
        } else {
            console.error('❌ 依赖安装失败');
            process.exit(1);
        }
    });
    
    return;
}

startServer();

function startServer() {
    console.log('🌿 分支选择功能已启用！');
    console.log('   - 可以选择任意远程分支进行部署');
    console.log('   - 自动切换分支并拉取最新代码');
    console.log('   - 部署前自动同步远程分支信息');
    console.log('');
    
    // 启动服务器
    const server = spawn('node', ['deploy-server.js'], { 
        stdio: 'inherit', 
        shell: true,
        cwd: __dirname 
    });
    
    server.on('close', (code) => {
        console.log(`服务器已关闭，退出码: ${code}`);
    });
    
    server.on('error', (error) => {
        console.error('启动服务器失败:', error);
    });
} 