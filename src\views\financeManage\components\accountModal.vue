<template>
	<!-- 记账抽屉 -->
	<a-drawer v-model:visible="visible" title="记账" width="1200px" @cancel="handleCancel">
		<div class="account-modal">
			<!-- 账单信息 -->
			<section-title title="账单信息" style="margin-bottom: 16px;" />

			<div class="bill-info">
				<a-descriptions :column="3" size="small">
	                <a-descriptions-item label="项目名称">{{ data.projectName }}</a-descriptions-item>
	                <a-descriptions-item label="合同号">{{ data.contractNo }}</a-descriptions-item>
	                <a-descriptions-item label="承租方">{{ data.tenantName }}</a-descriptions-item>
	            </a-descriptions>
				<a-table 
					:columns="billInfoColumns" 
					:data="billInfoData" 
					:pagination="false" 
					:bordered="true"
					size="small"
					:show-header="true"
					:scroll="{ x: 1000 }"
					class="bill-info-table"
				/>
			</div>

			<!-- 流水信息 -->
			<section-title title="流水信息" style="margin: 24px 0 16px 0;" />
			<div class="flow-info">
				<a-form :model="flowForm" v-if="advancedSearchVisible" :label-col-props="{ span: 10 }" :wrapper-col-props="{ span: 14 }" >
					<a-row :gutter="0">
						<a-col :span="6">
							<a-form-item label="项目">
								<a-select v-model="flowForm.project" placeholder="请选择项目" allow-clear></a-select>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付房源名称">
								<a-input v-model="flowForm.payerName" placeholder="请输入支付房源名称" allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付客户名称">
								<a-input v-model="flowForm.customerName" placeholder="请输入支付客户名称" allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付客户手机号">
								<a-input v-model="flowForm.customerPhone" placeholder="请输入支付客户手机号" allow-clear />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="0">
						<a-col :span="6">
							<a-form-item label="支付日期">
								<a-range-picker v-model="flowForm.payDate" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付方式">
								<a-select v-model="flowForm.payType" placeholder="请选择支付方式" allow-clear></a-select>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="收款渠道">
								<a-select v-model="flowForm.channel" placeholder="请选择收款渠道" allow-clear></a-select>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付金额">
								<a-input-number v-model="flowForm.amount" placeholder="请输入支付金额" allow-clear
									style="width: 100%" />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="0">
						<a-col :span="6">
							<a-form-item label="使用情况">
								<a-select v-model="flowForm.payType" placeholder="请选择使用情况" allow-clear></a-select>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="订单号">
								<a-input-number v-model="flowForm.amount" placeholder="请输入支付金额" allow-clear
									style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="6" >
							<a-space style="margin-left: 16px;">
								<a-button type="primary" @click="handleSearch">查询</a-button>
								<a-button @click="handleReset">重置</a-button>
							</a-space>
						</a-col>
					</a-row>
				</a-form>
				<a-row>
					<AdvancedSearch @toggle="handleToggle" />
				</a-row>

				<a-table :columns="flowColumns" :data="flowData" row-key="id" :pagination="false" style="margin-top: 16px">
					<template #operation="{ record }">
						<a-button type="text" @click="handleRecordFlow(record)">记账</a-button>
					</template>
				</a-table>
			</div>

			<!-- 记账信息 -->
			<section-title title="记账信息" style="margin: 24px 0 16px 0;" />
			<div class="account-info">
				<a-table :columns="accountColumns" :data="accountData" row-key="id" :pagination="false">
					<template #operation="{ record }">
						<a-button type="text" @click="handleModify(record)">修改</a-button>
					</template>
				</a-table>
			</div>

			<!-- 历史记账信息 -->
			<section-title title="历史记账信息" style="margin: 24px 0 16px 0;" />
			<div class="history-info">
				<a-table :columns="historyColumns" :data="historyData" row-key="id" :pagination="false"></a-table>
			</div>

			<!-- 底部操作按钮 -->
			<div class="footer">
				<a-space>
					<a-button @click="handleCancel">取消</a-button>
					<a-button type="primary" @click="handleSave">保存</a-button>
				</a-space>
			</div>
		</div>
	</a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import AdvancedSearch from '@/components/advancedSearch/index.vue'

const props = defineProps<{
	data: any
}>()

const emit = defineEmits(['cancel', 'save'])

// 抽屉显示控制
const visible = ref(false)

// 打开抽屉
const open = () => {
	visible.value = true
}

// 关闭抽屉
const close = () => {
	visible.value = false
}

// 暴露方法给父组件调用
defineExpose({
	open,
	close
})

// 账单信息表格列配置 - 横向展示
const billInfoColumns = [
	{ title: '账单类型', dataIndex: 'costType', width: 120 , align: 'center' },
	{ title: '账单周期', dataIndex: 'billPeriod', width: 200, align: 'center'},
	{ title: '应收日期', dataIndex: 'receivableDate', width: 120, align: 'center' },
	{ title: '账单总额', dataIndex: 'totalAmount', width: 120 , align: 'center'},
	{ title: '优惠金额', dataIndex: 'discountAmount', width: 120 , align: 'center'},
	{ title: '实际应收金额', dataIndex: 'actualReceivable', width: 140 , align: 'center'},
	{ title: '实际已收金额', dataIndex: 'receivedAmount', width: 140 , align: 'center'},
	{ title: '未收金额', dataIndex: 'unpaidAmount', width: 120 , align: 'center'}
]

// 账单信息数据 - 单行数据
const billInfoData = computed(() => [{
	costType: props.data.costType || '',
	billPeriod: props.data.billPeriod || '',
	receivableDate: props.data.receivableDate || '',
	totalAmount: props.data.totalAmount ? props.data.totalAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '',
	discountAmount: props.data.discountAmount ? props.data.discountAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '',
	actualReceivable: props.data.actualReceivable ? props.data.actualReceivable.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '',
	receivedAmount: props.data.receivedAmount ? props.data.receivedAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '',
	unpaidAmount: props.data.unpaidAmount ? props.data.unpaidAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : ''
}])

// 流水表单数据
const flowForm = reactive({
	project: '',
	payerName: '',
	customerName: '',
	customerPhone: '',
	payDate: [],
	payType: '',
	channel: '',
	amount: undefined
})

// 流水表格列配置
const flowColumns = [
	{ title: '项目', dataIndex: 'project' },
	{ title: '支付方式', dataIndex: 'payType' },
	{ title: '支付时间', dataIndex: 'payTime' },
	{ title: '支付源', dataIndex: 'paySource' },
	{ title: '订单号', dataIndex: 'orderId' },
	{ title: '客户名称', dataIndex: 'customerName' },
	{ title: '手机号', dataIndex: 'phone' },
	{ title: '支付金额', dataIndex: 'amount' },
	{ title: '已使用金额', dataIndex: 'usedAmount' },
	{ title: '操作', slotName: 'operation', width: 100 }
]

// 记账表格列配置
const accountColumns = [
	{ title: '账单类型', dataIndex: 'billType' },
	{ title: '订单号', dataIndex: 'orderId' },
	{ title: '支付源', dataIndex: 'paySource' },
	{ title: '支付时间', dataIndex: 'payTime' },
	{ title: '客户名称', dataIndex: 'customerName' },
	{ title: '支付金额', dataIndex: 'amount' },
	{ title: '未收已收金额', dataIndex: 'paidAmount' },
	{ title: '剩余金额', dataIndex: 'remainAmount' },
	{ title: '收款单位', dataIndex: 'receiver' },
	{ title: '操作', slotName: 'operation', width: 100 }
]

// 历史记账表格列配置
const historyColumns = [
	{ title: '项目', dataIndex: 'project' },
	{ title: '账单类型', dataIndex: 'billType' },
	{ title: '订单号', dataIndex: 'orderId' },
	{ title: '支付源', dataIndex: 'paySource' },
	{ title: '支付时间', dataIndex: 'payTime' },
	{ title: '客户名称', dataIndex: 'customerName' },
	{ title: '支付金额', dataIndex: 'amount' },
	{ title: '本次已记账金额', dataIndex: 'accountedAmount' },
	{ title: '剩余金额', dataIndex: 'remainAmount' },
	{ title: '收款单位', dataIndex: 'receiver' }
]

const flowData = ref([])
const accountData = ref([])
const historyData = ref([])
const advancedSearchVisible = ref(false)
// 查询
const handleSearch = () => {
	console.log('查询', flowForm)
}

// 重置
const handleReset = () => {
	Object.assign(flowForm, {
		project: '',
		payerName: '',
		customerName: '',
		customerPhone: '',
		payDate: [],
		payType: '',
		channel: '',
		amount: undefined
	})
}

// 记账
const handleRecordFlow = (record: any) => {
	console.log('记账', record)
}

// 修改
const handleModify = (record: any) => {
	console.log('修改', record)
}

// 取消
const handleCancel = () => {
	visible.value = false
	emit('cancel')
}

// 保存
const handleSave = () => {
	// TODO: 这里可以添加保存逻辑
	visible.value = false
	emit('save')
}

// 高级搜索
const handleToggle = (visible: boolean) => {
	console.log('高级搜索', visible)
	advancedSearchVisible.value = visible
}
</script>

<style scoped lang="less">
.account-modal {

	.bill-info,
	.flow-info,
	.account-info,
	.history-info {
		margin-bottom: 24px;
	}

	.footer {
		text-align: right;
		margin-top: 24px;
	}

	.bill-info-table {
		:deep(.arco-table-th) {
			background-color: var(--color-fill-2);
			font-weight: 500;
			text-align: center;
		}
		
		:deep(.arco-table-td) {
			text-align: center;
		}
		
		:deep(.arco-table-tr) {
			&:hover {
				background-color: transparent;
			}
		}
	}
}
</style>