<template>
  <a-modal
    v-model:visible="visible"
    :title="'创建房源--选择楼栋'"
    :width="700"
    @cancel="handleCancel"
  >
    <div>
      <a-form label-align="right" :model="form" layout="inline" style="margin-bottom: 16px;">
        <a-form-item label="所属项目" field="projectId">
          <project-tree-select 
            v-model="form.projectId"
            style="width: 250px;"
            disabled
            @change="handleProjectChange"
          />
        </a-form-item>
        <a-form-item label="楼栋名称" field="buildingName">
          <a-input v-model="form.buildingName" style="width: 180px;" placeholder="请输入" @input="handleBuildingNameInputDebounce" />
        </a-form-item>
      </a-form>
      
      <div class="tree-scroll-container">
        <a-tree
          :data="treeData"
          checkable
          v-model:checked-keys="checkedKeys"
          :field-names="{ key: 'id', title: 'name', children: 'children' }"
          :loading="loading"
          :default-expanded-keys="expandedKeys"
          :expanded-keys="expandedKeys"
          default-expand-all
          @expand="handleExpand"
        />
      </div>
    </div>
    <template #footer>
      <div style="text-align: center;">
        <a-button type="primary" :disabled="checkedKeys.length === 0" @click="handleNext">下一步</a-button>
        <a-button style="margin-left: 16px;" @click="handleCancel">取消</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineEmits, defineExpose, onUnmounted } from 'vue'
import projectTreeSelect from '@/components/projectTreeSelect/index.vue'
import { getBuildingTree, ProjectTreeNodeVo } from '@/api/project'

const visible = ref(false)
const emit = defineEmits(['next', 'cancel'])
const loading = ref(false)

const form = reactive({
  projectId: '',
  buildingName: ''
})

interface SelectedBuilding {
  id: string
  name: string
}

const treeData = ref<ProjectTreeNodeVo[]>([])
const checkedKeys = ref<string[]>([])
// 用于存储需要展开的节点ID
const expandedKeys = ref<string[]>([])

// 定义防抖定时器和延迟时间
let debounceTimer: number | null = null
const DEBOUNCE_DELAY = 300 // 300ms

// 防抖处理函数
const debounce = (fn: Function, delay: number) => {
  return (...args: any[]) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
    debounceTimer = window.setTimeout(() => {
      fn(...args)
      debounceTimer = null
    }, delay)
  }
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
})

// 收集所有节点ID用于展开
const collectNodeIds = (nodes: ProjectTreeNodeVo[]): string[] => {
  const ids: string[] = []
  
  const traverse = (nodeList: ProjectTreeNodeVo[]) => {
    if (!nodeList || !nodeList.length) return
    
    for (const node of nodeList) {
      if (node.id) {
        ids.push(node.id)
      }
      
      if (node.children && node.children.length) {
        traverse(node.children)
      }
    }
  }
  
  traverse(nodes)
  return ids
}

// 加载楼栋树数据
const loadBuildingTree = async (): Promise<void> => {
  if (!form.projectId) {
    console.warn('无法加载楼栋树：项目ID为空')
    return Promise.resolve()
  }
  
  loading.value = true
  try {
    const params = {
      projectId: form.projectId,
      buildingName: form.buildingName || undefined
    }
    
    console.log('请求楼栋树数据，参数:', params)
    const { data } = await getBuildingTree(params)
    
    if (data) {
      // 直接使用API返回的数据，不再需要取children
      // 确保返回的数据是数组形式
      treeData.value = Array.isArray(data) ? data : [data]
      console.log(`获取到楼栋数据，共 ${treeData.value.length} 个地块`)
      
      // 收集所有节点ID用于默认展开
      expandedKeys.value = collectNodeIds(treeData.value)
      console.log(`设置展开节点，共 ${expandedKeys.value.length} 个`)
      
      // 确保树加载完成后默认展开所有地块节点
      setTimeout(() => {
        // 如果expandedKeys为空，则尝试直接获取第一级节点ID
        if (expandedKeys.value.length === 0 && treeData.value.length > 0) {
          expandedKeys.value = treeData.value
            .filter(node => !!node.id)
            .map(node => node.id || '')
          console.log('使用一级节点ID作为展开节点:', expandedKeys.value)
        }
        
        // 检查选中状态是否与树节点匹配
        if (checkedKeys.value.length > 0) {
          // 获取树中所有有效的节点ID
          const allValidIds = collectNodeIds(treeData.value)
          
          // 过滤掉不在树中的节点ID
          const validCheckedKeys = checkedKeys.value.filter(id => allValidIds.includes(id))
          
          if (validCheckedKeys.length !== checkedKeys.value.length) {
            console.warn(`部分选中的楼栋在当前树中不存在，原有 ${checkedKeys.value.length} 个，有效 ${validCheckedKeys.length} 个`)
            checkedKeys.value = validCheckedKeys
          }
        }
      }, 100)
    } else {
      console.warn('获取楼栋数据返回空结果')
      treeData.value = []
      expandedKeys.value = []
    }
  } catch (error) {
    console.error('加载楼栋树失败:', error)
    treeData.value = []
    expandedKeys.value = []
  } finally {
    loading.value = false
  }
  
  return Promise.resolve()
}

// 处理项目变更
const handleProjectChange = (projectId: string) => {
  // 清空已选中的楼栋
  checkedKeys.value = []
  loadBuildingTree()
}

// 楼栋名称输入处理（带防抖）
const handleBuildingNameInputDebounce = debounce(() => {
  loadBuildingTree()
}, DEBOUNCE_DELAY)

const handleNext = () => {
  // 从树数据中获取选中楼栋的名称信息
  const selectedBuildings = getSelectedBuildingInfo(treeData.value, checkedKeys.value)
  
  // 确保传递正确的数据格式给 SelectRoomsModal
  emit('next', {
    projectId: form.projectId,
    buildingName: form.buildingName,
    checkedKeys: checkedKeys.value,
    selectedBuildings,
    // 添加 buildingKeys 属性，与 SelectRoomsModal 期望的格式一致
    buildingKeys: checkedKeys.value
  })
  visible.value = false
}

// 获取选中楼栋的信息
const getSelectedBuildingInfo = (treeNodes: ProjectTreeNodeVo[], checkedKeys: string[]): SelectedBuilding[] => {
  const result: SelectedBuilding[] = []
  
  const traverse = (nodes: ProjectTreeNodeVo[]) => {
    if (!nodes || !nodes.length) return
    
    for (const node of nodes) {
      if (checkedKeys.includes(node.id || '')) {
        result.push({
          id: node.id || '',
          name: node.name || ''
        })
      }
      
      if (node.children && node.children.length) {
        traverse(node.children)
      }
    }
  }
  
  traverse(treeNodes)
  return result
}

const handleCancel = () => {
  emit('cancel')
  visible.value = false
}

// 显示模态框，并处理可能的回显数据
const show = (init?: any) => {
  console.log('SelectBuildingModal show 被调用，传入数据:', init)
  
  // 确保有初始化数据
  if (!init) {
    console.warn('未提供初始化数据，将使用默认值')
    init = {}
  }
  
  // 重置搜索条件，但保留项目信息
  form.buildingName = init.buildingName || ''
  expandedKeys.value = [] // 初始清空展开状态，稍后会根据树数据重新计算
  
  // 处理回显的选中楼栋
  if (init.checkedKeys && Array.isArray(init.checkedKeys)) {
    console.log('设置选中楼栋，数量:', init.checkedKeys.length, '值:', init.checkedKeys)
    checkedKeys.value = [...init.checkedKeys] // 使用展开运算符创建新数组，避免引用问题
  } else {
    console.log('未提供选中楼栋数据或格式不正确，重置选中状态')
    checkedKeys.value = []
  }
  
  // 设置项目ID
  if (init.projectId) {
    console.log('设置项目ID:', init.projectId)
    form.projectId = init.projectId
  } else {
    console.warn('未提供项目ID')
  }
  
  // 检查项目ID是否有效
  if (!form.projectId) {
    console.error('项目ID为空，无法加载楼栋数据')
    return
  }
  
  // 加载楼栋树
  console.log('开始加载楼栋树，项目ID:', form.projectId, '楼栋名称:', form.buildingName)
  loadBuildingTree().then(() => {
    // 树加载完成后，确保选中状态正确
    if (checkedKeys.value.length > 0) {
      console.log('树加载完成，确认选中状态，当前选中:', checkedKeys.value)
      // 可以在这里添加额外的选中状态验证逻辑
    }
  })
  
  // 显示弹窗
  visible.value = true
}

// 监听projectId变化，自动加载楼栋树
watch(() => form.projectId, (newVal) => {
  if (newVal) {
    loadBuildingTree()
  } else {
    treeData.value = []
    expandedKeys.value = []
  }
})

// 处理节点展开
const handleExpand = (keys: string[], { expanded, node }: any) => {
  expandedKeys.value = keys
  
  // 如果是收起操作且当前节点是地块节点（第一级），则重新添加它到展开列表中
  // 确保地块节点始终保持展开状态
  if (!expanded && node && node.parent === undefined) {
    // 这是一个一级节点（地块），确保它始终展开
    setTimeout(() => {
      if (!expandedKeys.value.includes(node.key)) {
        expandedKeys.value = [...expandedKeys.value, node.key]
      }
    }, 10)
  }
}

defineExpose({ show })
</script>

<style scoped>
.tree-scroll-container {
  max-height: 50vh;
  overflow-y: auto;
  padding-right: 8px;
  margin-bottom: 24px;
  border: 1px solid #f2f3f5;
  border-radius: 4px;
}
</style> 