import { App } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts';
import {
    GridComponent,
    TooltipComponent,
    LegendComponent,
    DataZoomComponent,
    GraphicComponent,
} from 'echarts/components';
import Chart from './chart/index.vue';
import Breadcrumb from './breadcrumb/index.vue';
import WordViewer from './WordViewer/index.vue';
import PermissionLink from './PermissionLink/index.vue';

// Manually introduce ECharts modules to reduce packing size

use([
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>hart,
    LineChart,
    PieChart,
    RadarChart,
    Grid<PERSON>omponent,
    TooltipComponent,
    LegendComponent,
    DataZoomComponent,
    GraphicComponent,
]);

export default {
    install(Vue: App) {
        Vue.component('Chart', Chart);
        Vue.component('WordViewer', WordViewer);
        Vue.component('PermissionLink', PermissionLink);
        // Vue.component('Breadcrumb', Breadcrumb);
    },
};
