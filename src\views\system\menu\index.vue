<template>
    <div class="container">
        <a-card class="general-card" :title="'菜单管理'">
            <a-row>
                <a-col :flex="1">
                    <a-form :model="formModel" :label-col-props="{ span: 7 }" :wrapper-col-props="{ span: 17 }"
                        label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="menuName" label="菜单名称">
                                    <a-input v-model="formModel.menuName" placeholder="请输入菜单名称" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="status" label="状态">
                                    <a-select v-model="formModel.status" :options="statusOptions" placeholder="请选择状态" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <!-- <a-divider style="height: 42px" direction="vertical" /> -->
                <a-col :flex="'43px'" style="text-align: right">
                    <a-space direction="horizontal" :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            搜索
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin-top: 0" />
            <a-row style="margin-bottom: 16px">
                <a-col :span="12">
                    <a-space>
                        <a-button type="primary" @click="handleAdd">
                            <template #icon>
                                <icon-plus />
                            </template>
                            新增
                        </a-button>
                    </a-space>
                </a-col>
                <a-col :span="12" style="
                      display: flex;
                      align-items: center;
                      justify-content: end;
                  ">
                    <a-tooltip :content="$t('searchTable.actions.refresh')">
                        <div class="action-icon" @click="search"><icon-refresh size="18" /></div>
                    </a-tooltip>
                    <a-dropdown @select="handleSelectDensity">
                        <a-tooltip :content="$t('searchTable.actions.density')">
                            <div class="action-icon"><icon-line-height size="18" /></div>
                        </a-tooltip>
                        <template #content>
                            <a-doption v-for="item in densityList" :key="item.value" :value="item.value"
                                :class="{ active: item.value === size }">
                                <span>{{ item.name }}</span>
                            </a-doption>
                        </template>
                    </a-dropdown>
                    <a-tooltip :content="$t('searchTable.actions.columnSetting')">
                        <a-popover trigger="click" position="bl" @popup-visible-change="popupVisibleChange">
                            <div class="action-icon"><icon-settings size="18" /></div>
                            <template #content>
                                <div id="tableSetting">
                                    <div v-for="(item, index) in showColumns" :key="item.dataIndex" class="setting">
                                        <div style="
                                              margin-right: 4px;
                                              cursor: move;
                                          ">
                                            <icon-drag-arrow />
                                        </div>
                                        <div>
                                            <a-checkbox v-model="item.checked" @change="
                                                handleChange(
                                                    $event,
                                                    item as TableColumnData,
                                                    index
                                                )
                                                ">
                                            </a-checkbox>
                                        </div>
                                        <div class="title">
                                            {{
                                                item.title === '#'
                                                    ? '序列号'
                                                    : item.title
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </a-popover>
                    </a-tooltip>
                </a-col>
            </a-row>
            <a-table row-key="menuId" :loading="loading" :pagination="false" column-resizable :bordered="{ cell: true }"
                :columns="(cloneColumns as TableColumnData[])" :data="renderData" :size="size"
                :hide-expand-button-on-empty="true">
                <template #operations="{ record }">
                    <a-button type="text" size="small" @click="handleEditMenu(record)">修改</a-button>
                    <a-button type="text" size="small" @click="handleAdd(record)">新增</a-button>
                    <a-popconfirm content="确定删除该菜单吗？" @ok="handleDeleteMenu(record)">
                        <a-button type="text" size="small">删除</a-button>
                    </a-popconfirm>
                </template>
            </a-table>
        </a-card>

        <a-modal v-model:visible="showModal" :title="menuDialogTitle" width="680px" :ok-loading="submitLoading"
            :on-before-ok="handleOk" @cancel="handleCancel">
            <a-form ref="formRef" :model="menuForm" :rules="rules" label-width="100px" auto-label-width>
                <a-row>
                    <a-col :span="24">
                        <a-form-item label="上级菜单">
                            <a-tree-select v-model="menuForm.parentId" :data="menuOptions"
                                :field-names="{ key: 'menuId', title: 'menuName', children: 'children' }"
                                placeholder="请选择上级菜单" :treeProps="menuTreeProps" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="菜单类型">
                            <a-radio-group v-model="menuForm.menuType">
                                <a-radio value="M">目录</a-radio>
                                <a-radio value="C">菜单</a-radio>
                                <a-radio value="F">按钮</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="菜单图标" v-if="menuForm.menuType != 'F'">
                            <a-popover title="" trigger="click" position="bottom">
                                <a-input :readonly="false" v-model="menuForm.icon" placeholder="点击选择图标" allow-clear
                                    @blur="showSelectIcon">
                                    <template #prefix>
                                        <svg-icon v-if="menuForm.icon" :icon-class="menuForm.icon"
                                            class="el-input__icon" style="height: 32px;width: 16px;" />
                                        <icon-search v-else style="height: 32px;width: 16px;" />
                                    </template>
                                </a-input>
                                <template #content>
                                    <div style="width: 540px;">
                                        <IconSelect @selected="selectedIcon" :active-icon="menuForm.icon" />
                                    </div>
                                </template>
                            </a-popover>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="菜单名称" field="menuName">
                            <a-input v-model="menuForm.menuName" placeholder="请输入菜单名称" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="显示排序" field="orderNum">
                            <a-input-number v-model="menuForm.orderNum" :min="0" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-if="menuForm.menuType != 'F'">
                        <a-form-item label="是否外链">
                            <template #label>
                                <span>
                                    <a-tooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
                                        <icon-question-circle-fill />
                                    </a-tooltip>
                                    是否外链
                                </span>
                            </template>
                            <a-radio-group v-model="menuForm.isFrame">
                                <a-radio value="0">是</a-radio>
                                <a-radio value="1">否</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-if="menuForm.menuType != 'F'">
                        <a-form-item label="路由地址" field="path">
                            <template #label>
                                <span>
                                    <a-tooltip content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头" placement="top">
                                        <icon-question-circle-fill />
                                    </a-tooltip>
                                    路由地址
                                </span>
                            </template>
                            <a-input v-model="menuForm.path" placeholder="请输入路由地址" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-if="menuForm.menuType == 'C'">
                        <a-form-item label="组件路径">
                            <template #label>
                                <span>
                                    <a-tooltip content="访问的组件路径，如：`system/user/index`，默认在`views`目录下" placement="top">
                                        <icon-question-circle-fill />
                                    </a-tooltip>
                                    组件路径
                                </span>
                            </template>
                            <a-input v-model="menuForm.component" placeholder="请输入组件路径" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-if="menuForm.menuType !== 'M'">
                        <a-form-item label="权限字符">
                            <template #label>
                                <span>
                                    <a-tooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)"
                                        placement="top">
                                        <icon-question-circle-fill />
                                    </a-tooltip>
                                    权限字符
                                </span>
                            </template>
                            <a-input v-model="menuForm.perms" placeholder="请输入权限标识" maxlength="100" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-if="menuForm.menuType == 'C'">
                        <a-form-item label="路由参数">
                            <template #label>
                                <span>
                                    <a-tooltip content='访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`' placement="top">
                                        <icon-question-circle-fill />
                                    </a-tooltip>
                                    路由参数
                                </span>
                            </template>
                            <a-input v-model="menuForm.query" placeholder="请输入路由参数" maxlength="255" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-if="menuForm.menuType == 'C'">
                        <a-form-item label="是否缓存">
                            <template #label>
                                <span>
                                    <a-tooltip content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致" placement="top">
                                        <icon-question-circle-fill />
                                    </a-tooltip>
                                    是否缓存
                                </span>
                            </template>
                            <a-radio-group v-model="menuForm.isCache">
                                <a-radio value="0">缓存</a-radio>
                                <a-radio value="1">不缓存</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-if="menuForm.menuType !== 'F'">
                        <a-form-item label="显示状态">
                            <template #label>
                                <span>
                                    <a-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                                        <icon-question-circle-fill />
                                    </a-tooltip>
                                    显示状态
                                </span>
                            </template>
                            <a-radio-group v-model="menuForm.visible">
                                <a-radio value="0">显示</a-radio>
                                <a-radio value="1">隐藏</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="菜单状态">
                            <template #label>
                                <span>
                                    <a-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
                                        <icon-question-circle-fill />
                                    </a-tooltip>
                                    菜单状态
                                </span>
                            </template>
                            <a-radio-group v-model="menuForm.status">
                                <a-radio value="0">正常</a-radio>
                                <a-radio value="1">停用</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang="ts" setup>
import { computed, ref, reactive, watch, nextTick, useTemplateRef } from 'vue'
import { getMenuList, addMenu, updateMenu, deleteMenu, getMenuById } from '@/api/system/menu';
import { useI18n } from 'vue-i18n';
import useLoading from '@/hooks/loading';
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
import cloneDeep from 'lodash/cloneDeep';
import Sortable from 'sortablejs';
import { listToTree } from '@/utils/index'
import IconSelect from '@/components/IconSelect/index.vue'
import SvgIcon from '@/components/SvgIcon/index.vue'

type SizeProps = 'mini' | 'small' | 'medium' | 'large';
type Column = TableColumnData & { checked?: true };

const generateFormModel = () => {
    return {
        menuName: '',
        status: '',
    };
};
const { loading, setLoading } = useLoading(true);
const { t } = useI18n();
const renderData = ref<any[]>([]);
const formModel = ref(generateFormModel());
const cloneColumns = ref<Column[]>([]);
const showColumns = ref<Column[]>([]);

const size = ref<SizeProps>('medium');

const densityList = computed(() => [
    {
        name: '迷你',
        value: 'mini',
    },
    {
        name: '偏小',
        value: 'small',
    },
    {
        name: '中等',
        value: 'medium',
    },
    {
        name: '偏大',
        value: 'large',
    },
]);
const columns = computed<TableColumnData[]>(() => [
    {
        title: '菜单名称',
        dataIndex: 'menuName',
    },
    {
        title: '图标',
        dataIndex: 'icon',
    },
    {
        title: '排序',
        dataIndex: 'orderNum',
    },
    {
        title: '权限标识',
        dataIndex: 'perms',
    },
    {
        title: '组件路径',
        dataIndex: 'component',
    },
    {
        title: '状态',
        dataIndex: 'status',
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
    },
    {
        title: '操作',
        dataIndex: 'operations',
        slotName: 'operations',
    },
]);
const statusOptions = computed<SelectOptionData[]>(() => [
    {
        label: t('searchTable.form.status.online'),
        value: 'online',
    },
    {
        label: t('searchTable.form.status.offline'),
        value: 'offline',
    },
]);
const fetchData = async (
    params = {}
) => {
    setLoading(true);
    try {
        const { data } = await getMenuList(params)
        const treeData = listToTree(data, 'menuId', 'parentId')
        renderData.value = treeData as any[]
    } catch (err) {
        // you can report use errorHandler or other
    } finally {
        setLoading(false);
    }
};

const search = () => {
    fetchData({
        ...formModel.value,
    });
};

fetchData();
const reset = () => {
    formModel.value = generateFormModel();
    fetchData()
};

const handleEditMenu = async (row: any) => {
    formReset()
    getTreeSelect()
    const { data } = await getMenuById(row.menuId)
    menuForm.value = data as any
    showModal.value = true
    menuDialogTitle.value = '编辑菜单'
}
const handleDeleteMenu = async (row: any) => {
    await deleteMenu(row.menuId)
    fetchData()
}
const handleSelectDensity = (
    val: string | number | Record<string, any> | undefined,
    e: Event
) => {
    size.value = val as SizeProps;
};

const handleChange = (
    checked: boolean | (string | boolean | number)[],
    column: Column,
    index: number
) => {
    if (!checked) {
        cloneColumns.value = showColumns.value.filter(
            (item) => item.dataIndex !== column.dataIndex
        );
    } else {
        cloneColumns.value.splice(index, 0, column);
    }
};

const exchangeArray = <T extends Array<any>>(
    array: T,
    beforeIdx: number,
    newIdx: number,
    isDeep = false
): T => {
    const newArray = isDeep ? cloneDeep(array) : array;
    if (beforeIdx > -1 && newIdx > -1) {
        // 先替换后面的，然后拿到替换的结果替换前面的
        newArray.splice(
            beforeIdx,
            1,
            newArray.splice(newIdx, 1, newArray[beforeIdx]).pop()
        );
    }
    return newArray;
};

const popupVisibleChange = (val: boolean) => {
    if (val) {
        nextTick(() => {
            const el = document.getElementById(
                'tableSetting'
            ) as HTMLElement;
            const sortable = new Sortable(el, {
                onEnd(e: any) {
                    const { oldIndex, newIndex } = e;
                    exchangeArray(cloneColumns.value, oldIndex, newIndex);
                    exchangeArray(showColumns.value, oldIndex, newIndex);
                },
            });
        });
    }
};

watch(
    () => columns.value,
    (val) => {
        cloneColumns.value = cloneDeep(val);
        cloneColumns.value.forEach((item, index) => {
            item.checked = true;
        });
        showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
);

const showModal = ref(false)

// 菜单数据对象
const menuForm = ref({
    menuId: undefined,
    parentId: 0,
    menuName: '',
    routeName: '',
    icon: '',
    menuType: "M",
    orderNum: undefined,
    path: '',
    component: '',
    perms: '',
    query: '',
    isFrame: "1",
    isCache: "0",
    visible: "0",
    status: "0"
})
const rules = {
    menuName: [
        { required: true, message: '请输入菜单名称', trigger: 'blur' },
    ],
    orderNum: [
        { required: true, message: '请输入排序', trigger: 'blur' },
    ],
    path: [
        { required: true, message: '请输入路由地址', trigger: 'blur' },
    ],
}
const menuDialogTitle = ref('添加菜单')
const formReset = () => {
    menuForm.value = {
        menuId: undefined,
        parentId: 0,
        menuName: '',
        routeName: '',
        icon: '',
        menuType: "M",
        orderNum: undefined,
        path: '',
        component: '',
        perms: '',
        query: '',
        isFrame: "1",
        isCache: "0",
        visible: "0",
        status: "0"
    }
}
// 新增菜单
const handleAdd = (record: any) => {
    formReset()
    getTreeSelect()
    if (!!record && !!record.menuId) {
        menuForm.value.parentId = record.menuId
    }
    menuDialogTitle.value = '添加菜单'
    showModal.value = true
}

// 查询菜单下拉树结构
const menuOptions = ref<any[]>([])
const menuTreeProps = ref({
    'default-expanded-keys': [0],
})
const getTreeSelect = async () => {
    setLoading(true);
    try {
        const { data } = await getMenuList({})
        const menu = { menuId: 0, menuName: "主类目", children: [] as any[] }
        data.forEach((item: any) => {
            item.menuIcon = item.icon
            delete item.icon
        })
        menu.children = listToTree(data, 'menuId', 'parentId') as any[]
        menuOptions.value = [menu] as any[] // 类型断言为any数组避免类型错误
        console.log(menuOptions.value);
    } catch (err) {
        // you can report use errorHandler or other
    } finally {
        setLoading(false);
    }
}

// 选择图标
const iconSelectRef = ref()
const selectedIcon = (name: string) => {
    menuForm.value.icon = name
}
const showSelectIcon = () => {
    iconSelectRef.value?.reset()
}

const formRef = ref()
const submitLoading = ref(false)
const handleOk = async () => {
    submitLoading.value = true
    try {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log(errors, '验证失败')
            return false
        }
        console.log(menuForm.value, '验证通过');
        // 验证通过，继续处理保存逻辑
        if (menuForm.value.menuId) {
            await updateMenu(menuForm.value)
        } else {
            await addMenu(menuForm.value)
        }
        // 刷新列表
        fetchData()
        return true
    } catch (err) {
        console.log(err, '验证失败')
        return false
    } finally {
        submitLoading.value = false
    }
}

const handleCancel = () => {
    showModal.value = false
}
</script>

<script lang="ts">
export default { name: 'menuRouter' };
</script>

<style scoped lang="less">
.container {
    padding: 0 16px;
}

:deep(.arco-table-th) {
    &:last-child {
        .arco-table-th-item-title {
            margin-left: 16px;
        }
    }
}

.action-icon {
    margin-left: 12px;
    cursor: pointer;
}

.active {
    color: #0960bd;
    background-color: #e3f4fc;
}

.setting {
    display: flex;
    align-items: center;
    width: 200px;

    .title {
        margin-left: 12px;
        cursor: pointer;
    }
}
</style>
