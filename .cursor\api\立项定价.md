---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/立项定价申请

<a id="opIdedit_4"></a>

## PUT 修改立项定价申请

PUT /pricing/edit

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "applicationCode": "string",
  "applicationName": "string",
  "pricingType": 0,
  "projectId": "string",
  "projectName": "string",
  "buildingName": "string",
  "roomCount": 0,
  "status": 0,
  "discountRules": "string",
  "pricingDesc": "string",
  "pricingDetails": "string",
  "attachments": "string",
  "roomList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "pricingId": "string",
      "roomId": "string",
      "roomName": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomUsage": "string",
      "roomType": "string",
      "rentArea": 0,
      "planningBusiness": "string",
      "baseRent": 0,
      "additionalFee": 0,
      "calcUnit": 0,
      "isRentIncrease": true,
      "increaseInterval": 0,
      "increaseRate": 0,
      "depositType": 0,
      "depositAmount": 0,
      "paymentMethod": "string",
      "minRentalPeriod": "string",
      "maxRentalPeriod": "string",
      "rentalPeriodUnit": "string",
      "freeRentType": 0,
      "freeRentPeriod": 0,
      "freeRentTerm": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PricingAddDTO](#schemapricingadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdimportRooms_1"></a>

## POST 通过模版导入立项定价

POST /pricing/template/import

通过模版导入立项定价

> Body 请求参数

```json
{
  "file": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdimportRoomsHistory"></a>

## POST 通过模版导入历史立项定价

POST /pricing/template/import/history

通过模版导入立项定价

> Body 请求参数

```json
{
  "file": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdroomDownloadTemplate"></a>

## POST 导出立项定价模版

POST /pricing/template/download

导出立项定价模版

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "string",
  "roomIdList": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PricingTemplateDTO](#schemapricingtemplatedto)| 否 |none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdgetRoomTree_1"></a>

## POST 立项定价选择房源接口

POST /pricing/room/tree

> Body 请求参数

```json
{
  "projectId": "string",
  "buildingIds": [
    "string"
  ],
  "roomUsage": "string",
  "isPriced": 0,
  "roomName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PricingRoomTreeDTO](#schemapricingroomtreedto)| 否 |none|

> 返回示例

> 200 Response

```
[{"treeId":"string","treeName":"string","roomId":"string","roomName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomUsage":"string","roomType":"string","rentArea":0,"children":[{"treeId":"string","treeName":"string","roomId":"string","roomName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomUsage":"string","roomType":"string","rentArea":0,"children":[{"treeId":"string","treeName":"string","roomId":"string","roomName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomUsage":"string","roomType":"string","rentArea":0,"children":[null]}]}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[PricingRoomTreeVo](#schemapricingroomtreevo)]|false|none||[立项定价选择房源接口出参VO]|
|» treeId|string|false|none|节点ID|节点ID|
|» treeName|string|false|none|节点名称|节点名称|
|» roomId|string|false|none|房间ID|房间ID|
|» roomName|string|false|none|房源名称|房源名称|
|» parcelId|string|false|none|地块ID|地块ID|
|» parcelName|string|false|none|地块|地块|
|» buildingId|string|false|none|楼栋ID|楼栋ID|
|» buildingName|string|false|none|楼栋|楼栋|
|» floorId|string|false|none|楼层ID|楼层ID|
|» floorName|string|false|none|楼层|楼层|
|» roomUsage|string|false|none|用途|用途|
|» roomType|string|false|none|户型|户型|
|» rentArea|number|false|none|计租面积|计租面积|
|» children|[[PricingRoomTreeVo](#schemapricingroomtreevo)]|false|none|子节点列表|子节点列表|
|»» treeId|string|false|none|节点ID|节点ID|
|»» treeName|string|false|none|节点名称|节点名称|
|»» roomId|string|false|none|房间ID|房间ID|
|»» roomName|string|false|none|房源名称|房源名称|
|»» parcelId|string|false|none|地块ID|地块ID|
|»» parcelName|string|false|none|地块|地块|
|»» buildingId|string|false|none|楼栋ID|楼栋ID|
|»» buildingName|string|false|none|楼栋|楼栋|
|»» floorId|string|false|none|楼层ID|楼层ID|
|»» floorName|string|false|none|楼层|楼层|
|»» roomUsage|string|false|none|用途|用途|
|»» roomType|string|false|none|户型|户型|
|»» rentArea|number|false|none|计租面积|计租面积|
|»» children|[[PricingRoomTreeVo](#schemapricingroomtreevo)]|false|none|子节点列表|子节点列表|

<a id="opIdlist_1"></a>

## POST 查询立项定价申请列表

POST /pricing/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "applicationCode": "string",
  "applicationName": "string",
  "pricingType": 0,
  "projectId": "string",
  "projectName": "string",
  "buildingName": "string",
  "roomCount": 0,
  "status": 0,
  "discountRules": "string",
  "pricingDesc": "string",
  "pricingDetails": "string",
  "attachments": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PricingQueryDTO](#schemapricingquerydto)| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","applicationCode":"string","applicationName":"string","pricingType":0,"projectId":"string","projectName":"string","buildingName":"string","roomCount":0,"status":0,"discountRules":"string","pricingDesc":"string","pricingDetails":"string","attachments":"string","createByName":"string","updateByName":"string","approveDate":"2019-08-24T14:15:22Z","roomList":[{"id":"string","pricingId":"string","roomId":"string","roomName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomUsage":"string","roomType":"string","rentArea":0,"planningBusiness":"string","baseRent":0,"additionalFee":0,"calcUnit":0,"isRentIncrease":true,"increaseInterval":0,"increaseRate":0,"depositType":0,"depositAmount":0,"paymentMethod":"string","minRentalPeriod":"string","maxRentalPeriod":"string","rentalPeriodUnit":"string","freeRentType":0,"freeRentPeriod":0,"freeRentTerm":"string","createByName":"string","updateByName":"string","isDel":true}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[PricingVo](#schemapricingvo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» applicationCode|string|false|none|立项定价申请编号|立项定价申请编号|
|» applicationName|string|false|none|立项定价申请名称|立项定价申请名称|
|» pricingType|integer(int32)|false|none|定价类型（1首次定价 2过程调价）|定价类型（1首次定价 2过程调价）|
|» projectId|string|false|none|项目ID|项目ID|
|» projectName|string|false|none|项目名称|项目名称|
|» buildingName|string|false|none|定价楼栋|定价楼栋|
|» roomCount|integer(int32)|false|none|定价房源数|定价房源数|
|» status|integer(int32)|false|none|状态(0草稿 1审批中 2已通过 3已驳回)|状态(0草稿 1审批中 2已通过 3已驳回)|
|» discountRules|string|false|none|折扣规则配置|折扣规则配置|
|» pricingDesc|string|false|none|定价说明文档|定价说明文档|
|» pricingDetails|string|false|none|其他定价说明|其他定价说明|
|» attachments|string|false|none|相关附件|相关附件|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» approveDate|string(date-time)|false|none||none|
|» roomList|[[PricingRoomRelVo](#schemapricingroomrelvo)]|false|none|房间定价|房间定价|
|»» 房间定价|[PricingRoomRelVo](#schemapricingroomrelvo)|false|none|房间定价|房间定价|
|»»» id|string|false|none|主键ID|none|
|»»» pricingId|string|false|none|立项定价ID|立项定价ID|
|»»» roomId|string|false|none|房间ID|房间ID|
|»»» roomName|string|false|none|房源名称|房源名称|
|»»» parcelId|string|false|none|地块ID|地块ID|
|»»» parcelName|string|false|none|地块|地块|
|»»» buildingId|string|false|none|楼栋ID|楼栋ID|
|»»» buildingName|string|false|none|楼栋|楼栋|
|»»» floorId|string|false|none|楼层ID|楼层ID|
|»»» floorName|string|false|none|楼层|楼层|
|»»» roomUsage|string|false|none|用途|用途|
|»»» roomType|string|false|none|户型|户型|
|»»» rentArea|number|false|none|计租面积|计租面积|
|»»» planningBusiness|string|false|none|规划业态|规划业态|
|»»» baseRent|number|false|none|基础租金|基础租金|
|»»» additionalFee|number|false|none|附加费用|附加费用|
|»»» calcUnit|integer(int32)|false|none|计租单位(1元/平方米/月 2元/月 3元/日)|计租单位(1元/平方米/月 2元/月 3元/日)|
|»»» isRentIncrease|boolean|false|none|租金是否递增(0否 1是)|租金是否递增(0否 1是)|
|»»» increaseInterval|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|»»» increaseRate|number|false|none|单价递增率(%)|单价递增率(%)|
|»»» depositType|integer(int32)|false|none|保证金类型(固定金额等)|保证金类型(固定金额等)|
|»»» depositAmount|number|false|none|保证金金额|保证金金额|
|»»» paymentMethod|string|false|none|支付方式(1月付 2季付 3半年付 4年付)|支付方式(1月付 2季付 3半年付 4年付)|
|»»» minRentalPeriod|string|false|none|租赁期限(开始)|租赁期限(开始)|
|»»» maxRentalPeriod|string|false|none|租赁期限(结束)|租赁期限(结束)|
|»»» rentalPeriodUnit|string|false|none|租赁期限单位(1月 2年)|租赁期限单位(1月 2年)|
|»»» freeRentType|integer(int32)|false|none|免租期参考因素(1不限 2租赁期限)|免租期参考因素(1不限 2租赁期限)|
|»»» freeRentPeriod|integer(int32)|false|none|免租期(月)|免租期(月)|
|»»» freeRentTerm|string|false|none|免租期租赁期限|免租期租赁期限|
|»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIdcommit"></a>

## POST 提交立项定价申请

POST /pricing/commit/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_9"></a>

## POST 新增立项定价申请

POST /pricing/add

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "applicationCode": "string",
  "applicationName": "string",
  "pricingType": 0,
  "projectId": "string",
  "projectName": "string",
  "buildingName": "string",
  "roomCount": 0,
  "status": 0,
  "discountRules": "string",
  "pricingDesc": "string",
  "pricingDetails": "string",
  "attachments": "string",
  "roomList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "pricingId": "string",
      "roomId": "string",
      "roomName": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomUsage": "string",
      "roomType": "string",
      "rentArea": 0,
      "planningBusiness": "string",
      "baseRent": 0,
      "additionalFee": 0,
      "calcUnit": 0,
      "isRentIncrease": true,
      "increaseInterval": 0,
      "increaseRate": 0,
      "depositType": 0,
      "depositAmount": 0,
      "paymentMethod": "string",
      "minRentalPeriod": "string",
      "maxRentalPeriod": "string",
      "rentalPeriodUnit": "string",
      "freeRentType": 0,
      "freeRentPeriod": 0,
      "freeRentTerm": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PricingAddDTO](#schemapricingadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetDetail"></a>

## GET 获取立项定价申请详细信息

GET /pricing/detail/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"id":"string","applicationCode":"string","applicationName":"string","pricingType":0,"projectId":"string","projectName":"string","buildingName":"string","roomCount":0,"status":0,"discountRules":"string","pricingDesc":"string","pricingDetails":"string","attachments":"string","createByName":"string","updateByName":"string","approveDate":"2019-08-24T14:15:22Z","roomList":[{"id":"string","pricingId":"string","roomId":"string","roomName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomUsage":"string","roomType":"string","rentArea":0,"planningBusiness":"string","baseRent":0,"additionalFee":0,"calcUnit":0,"isRentIncrease":true,"increaseInterval":0,"increaseRate":0,"depositType":0,"depositAmount":0,"paymentMethod":"string","minRentalPeriod":"string","maxRentalPeriod":"string","rentalPeriodUnit":"string","freeRentType":0,"freeRentPeriod":0,"freeRentTerm":"string","createByName":"string","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[PricingVo](#schemapricingvo)|

<a id="opIdremove_4"></a>

## DELETE 删除立项定价申请

DELETE /pricing/delete/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_PricingAddDTO">PricingAddDTO</h2>

<a id="schemapricingadddto"></a>
<a id="schema_PricingAddDTO"></a>
<a id="tocSpricingadddto"></a>
<a id="tocspricingadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "applicationCode": "string",
  "applicationName": "string",
  "pricingType": 0,
  "projectId": "string",
  "projectName": "string",
  "buildingName": "string",
  "roomCount": 0,
  "status": 0,
  "discountRules": "string",
  "pricingDesc": "string",
  "pricingDetails": "string",
  "attachments": "string",
  "roomList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "pricingId": "string",
      "roomId": "string",
      "roomName": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomUsage": "string",
      "roomType": "string",
      "rentArea": 0,
      "planningBusiness": "string",
      "baseRent": 0,
      "additionalFee": 0,
      "calcUnit": 0,
      "isRentIncrease": true,
      "increaseInterval": 0,
      "increaseRate": 0,
      "depositType": 0,
      "depositAmount": 0,
      "paymentMethod": "string",
      "minRentalPeriod": "string",
      "maxRentalPeriod": "string",
      "rentalPeriodUnit": "string",
      "freeRentType": 0,
      "freeRentPeriod": 0,
      "freeRentTerm": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|applicationCode|string|false|none|立项定价申请编号|立项定价申请编号|
|applicationName|string|false|none|立项定价申请名称|立项定价申请名称|
|pricingType|integer(int32)|false|none|定价类型（1首次定价 2过程调价）|定价类型（1首次定价 2过程调价）|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|buildingName|string|false|none|定价楼栋|定价楼栋|
|roomCount|integer(int32)|false|none|定价房源数|定价房源数|
|status|integer(int32)|false|none|状态(0草稿 1审批中 2已通过 3已驳回)|状态(0草稿 1审批中 2已通过 3已驳回)|
|discountRules|string|false|none|折扣规则配置|折扣规则配置|
|pricingDesc|string|false|none|定价说明文档|定价说明文档|
|pricingDetails|string|false|none|其他定价说明|其他定价说明|
|attachments|string|false|none|附件|附件|
|roomList|[[PricingRoomRelAddDTO](#schemapricingroomreladddto)]|false|none|定价房源|定价房源|

<h2 id="tocS_PricingRoomRelAddDTO">PricingRoomRelAddDTO</h2>

<a id="schemapricingroomreladddto"></a>
<a id="schema_PricingRoomRelAddDTO"></a>
<a id="tocSpricingroomreladddto"></a>
<a id="tocspricingroomreladddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "pricingId": "string",
  "roomId": "string",
  "roomName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomUsage": "string",
  "roomType": "string",
  "rentArea": 0,
  "planningBusiness": "string",
  "baseRent": 0,
  "additionalFee": 0,
  "calcUnit": 0,
  "isRentIncrease": true,
  "increaseInterval": 0,
  "increaseRate": 0,
  "depositType": 0,
  "depositAmount": 0,
  "paymentMethod": "string",
  "minRentalPeriod": "string",
  "maxRentalPeriod": "string",
  "rentalPeriodUnit": "string",
  "freeRentType": 0,
  "freeRentPeriod": 0,
  "freeRentTerm": "string"
}

```

定价房源

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|pricingId|string|false|none|立项定价ID|立项定价ID|
|roomId|string|false|none|房间ID|房间ID|
|roomName|string|false|none|房源名称|房源名称|
|parcelId|string|false|none|地块ID|地块ID|
|parcelName|string|false|none|地块|地块|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|buildingName|string|false|none|楼栋|楼栋|
|floorId|string|false|none|楼层ID|楼层ID|
|floorName|string|false|none|楼层|楼层|
|roomUsage|string|false|none|用途|用途|
|roomType|string|false|none|户型|户型|
|rentArea|number|false|none|计租面积|计租面积|
|planningBusiness|string|false|none|规划业态|规划业态|
|baseRent|number|false|none|基础租金|基础租金|
|additionalFee|number|false|none|附加费用|附加费用|
|calcUnit|integer(int32)|false|none|计租单位(1元/平方米/月 2元/月 3元/日)|计租单位(1元/平方米/月 2元/月 3元/日)|
|isRentIncrease|boolean|false|none|租金是否递增(0否 1是)|租金是否递增(0否 1是)|
|increaseInterval|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|increaseRate|number|false|none|单价递增率(%)|单价递增率(%)|
|depositType|integer(int32)|false|none|保证金类型(固定金额等)|保证金类型(固定金额等)|
|depositAmount|number|false|none|保证金金额|保证金金额|
|paymentMethod|string|false|none|支付方式(1月付 2季付 3半年付 4年付)|支付方式(1月付 2季付 3半年付 4年付)|
|minRentalPeriod|string|false|none|租赁期限(开始)|租赁期限(开始)|
|maxRentalPeriod|string|false|none|租赁期限(结束)|租赁期限(结束)|
|rentalPeriodUnit|string|false|none|租赁期限单位(1月 2年)|租赁期限单位(1月 2年)|
|freeRentType|integer(int32)|false|none|免租期参考因素(1不限 2租赁期限)|免租期参考因素(1不限 2租赁期限)|
|freeRentPeriod|integer(int32)|false|none|免租期(月)|免租期(月)|
|freeRentTerm|string|false|none|免租期租赁期限|免租期租赁期限|

<h2 id="tocS_PricingQueryDTO">PricingQueryDTO</h2>

<a id="schemapricingquerydto"></a>
<a id="schema_PricingQueryDTO"></a>
<a id="tocSpricingquerydto"></a>
<a id="tocspricingquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "applicationCode": "string",
  "applicationName": "string",
  "pricingType": 0,
  "projectId": "string",
  "projectName": "string",
  "buildingName": "string",
  "roomCount": 0,
  "status": 0,
  "discountRules": "string",
  "pricingDesc": "string",
  "pricingDetails": "string",
  "attachments": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|applicationCode|string|false|none|立项定价申请编号|立项定价申请编号|
|applicationName|string|false|none|立项定价申请名称|立项定价申请名称|
|pricingType|integer(int32)|false|none|定价类型（1首次定价 2过程调价）|定价类型（1首次定价 2过程调价）|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|buildingName|string|false|none|定价楼栋|定价楼栋|
|roomCount|integer(int32)|false|none|定价房源数|定价房源数|
|status|integer(int32)|false|none|状态(0草稿 1审批中 2已通过 3已驳回)|状态(0草稿 1审批中 2已通过 3已驳回)|
|discountRules|string|false|none|折扣规则配置|折扣规则配置|
|pricingDesc|string|false|none|定价说明文档|定价说明文档|
|pricingDetails|string|false|none|其他定价说明|其他定价说明|
|attachments|string|false|none|相关附件|相关附件|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_PricingVo">PricingVo</h2>

<a id="schemapricingvo"></a>
<a id="schema_PricingVo"></a>
<a id="tocSpricingvo"></a>
<a id="tocspricingvo"></a>

```json
{
  "id": "string",
  "applicationCode": "string",
  "applicationName": "string",
  "pricingType": 0,
  "projectId": "string",
  "projectName": "string",
  "buildingName": "string",
  "roomCount": 0,
  "status": 0,
  "discountRules": "string",
  "pricingDesc": "string",
  "pricingDetails": "string",
  "attachments": "string",
  "createByName": "string",
  "updateByName": "string",
  "approveDate": "2019-08-24T14:15:22Z",
  "roomList": [
    {
      "id": "string",
      "pricingId": "string",
      "roomId": "string",
      "roomName": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomUsage": "string",
      "roomType": "string",
      "rentArea": 0,
      "planningBusiness": "string",
      "baseRent": 0,
      "additionalFee": 0,
      "calcUnit": 0,
      "isRentIncrease": true,
      "increaseInterval": 0,
      "increaseRate": 0,
      "depositType": 0,
      "depositAmount": 0,
      "paymentMethod": "string",
      "minRentalPeriod": "string",
      "maxRentalPeriod": "string",
      "rentalPeriodUnit": "string",
      "freeRentType": 0,
      "freeRentPeriod": 0,
      "freeRentTerm": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|applicationCode|string|false|none|立项定价申请编号|立项定价申请编号|
|applicationName|string|false|none|立项定价申请名称|立项定价申请名称|
|pricingType|integer(int32)|false|none|定价类型（1首次定价 2过程调价）|定价类型（1首次定价 2过程调价）|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|buildingName|string|false|none|定价楼栋|定价楼栋|
|roomCount|integer(int32)|false|none|定价房源数|定价房源数|
|status|integer(int32)|false|none|状态(0草稿 1审批中 2已通过 3已驳回)|状态(0草稿 1审批中 2已通过 3已驳回)|
|discountRules|string|false|none|折扣规则配置|折扣规则配置|
|pricingDesc|string|false|none|定价说明文档|定价说明文档|
|pricingDetails|string|false|none|其他定价说明|其他定价说明|
|attachments|string|false|none|相关附件|相关附件|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|approveDate|string(date-time)|false|none||none|
|roomList|[[PricingRoomRelVo](#schemapricingroomrelvo)]|false|none|房间定价|房间定价|

<h2 id="tocS_PricingTemplateDTO">PricingTemplateDTO</h2>

<a id="schemapricingtemplatedto"></a>
<a id="schema_PricingTemplateDTO"></a>
<a id="tocSpricingtemplatedto"></a>
<a id="tocspricingtemplatedto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "string",
  "roomIdList": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|projectId|string|false|none|项目id|项目id|
|roomIdList|[string]|false|none|房间id|房间id|
|» 房间id|string|false|none|房间id|房间id|

<h2 id="tocS_PricingRoomRelVo">PricingRoomRelVo</h2>

<a id="schemapricingroomrelvo"></a>
<a id="schema_PricingRoomRelVo"></a>
<a id="tocSpricingroomrelvo"></a>
<a id="tocspricingroomrelvo"></a>

```json
{
  "id": "string",
  "pricingId": "string",
  "roomId": "string",
  "roomName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomUsage": "string",
  "roomType": "string",
  "rentArea": 0,
  "planningBusiness": "string",
  "baseRent": 0,
  "additionalFee": 0,
  "calcUnit": 0,
  "isRentIncrease": true,
  "increaseInterval": 0,
  "increaseRate": 0,
  "depositType": 0,
  "depositAmount": 0,
  "paymentMethod": "string",
  "minRentalPeriod": "string",
  "maxRentalPeriod": "string",
  "rentalPeriodUnit": "string",
  "freeRentType": 0,
  "freeRentPeriod": 0,
  "freeRentTerm": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

房间定价

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|pricingId|string|false|none|立项定价ID|立项定价ID|
|roomId|string|false|none|房间ID|房间ID|
|roomName|string|false|none|房源名称|房源名称|
|parcelId|string|false|none|地块ID|地块ID|
|parcelName|string|false|none|地块|地块|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|buildingName|string|false|none|楼栋|楼栋|
|floorId|string|false|none|楼层ID|楼层ID|
|floorName|string|false|none|楼层|楼层|
|roomUsage|string|false|none|用途|用途|
|roomType|string|false|none|户型|户型|
|rentArea|number|false|none|计租面积|计租面积|
|planningBusiness|string|false|none|规划业态|规划业态|
|baseRent|number|false|none|基础租金|基础租金|
|additionalFee|number|false|none|附加费用|附加费用|
|calcUnit|integer(int32)|false|none|计租单位(1元/平方米/月 2元/月 3元/日)|计租单位(1元/平方米/月 2元/月 3元/日)|
|isRentIncrease|boolean|false|none|租金是否递增(0否 1是)|租金是否递增(0否 1是)|
|increaseInterval|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|increaseRate|number|false|none|单价递增率(%)|单价递增率(%)|
|depositType|integer(int32)|false|none|保证金类型(固定金额等)|保证金类型(固定金额等)|
|depositAmount|number|false|none|保证金金额|保证金金额|
|paymentMethod|string|false|none|支付方式(1月付 2季付 3半年付 4年付)|支付方式(1月付 2季付 3半年付 4年付)|
|minRentalPeriod|string|false|none|租赁期限(开始)|租赁期限(开始)|
|maxRentalPeriod|string|false|none|租赁期限(结束)|租赁期限(结束)|
|rentalPeriodUnit|string|false|none|租赁期限单位(1月 2年)|租赁期限单位(1月 2年)|
|freeRentType|integer(int32)|false|none|免租期参考因素(1不限 2租赁期限)|免租期参考因素(1不限 2租赁期限)|
|freeRentPeriod|integer(int32)|false|none|免租期(月)|免租期(月)|
|freeRentTerm|string|false|none|免租期租赁期限|免租期租赁期限|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_PricingRoomTreeDTO">PricingRoomTreeDTO</h2>

<a id="schemapricingroomtreedto"></a>
<a id="schema_PricingRoomTreeDTO"></a>
<a id="tocSpricingroomtreedto"></a>
<a id="tocspricingroomtreedto"></a>

```json
{
  "projectId": "string",
  "buildingIds": [
    "string"
  ],
  "roomUsage": "string",
  "isPriced": 0,
  "roomName": "string"
}

```

立项定价选择房源接口入参DTO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|false|none|项目ID|项目ID|
|buildingIds|[string]|false|none|楼栋ID列表|楼栋ID列表|
|» 楼栋ID列表|string|false|none|楼栋ID列表|楼栋ID列表|
|roomUsage|string|false|none|用途|用途|
|isPriced|integer(int32)|false|none|是否定价|是否定价(0否 1是)|
|roomName|string|false|none|房源名称|房源名称|

<h2 id="tocS_PricingRoomTreeVo">PricingRoomTreeVo</h2>

<a id="schemapricingroomtreevo"></a>
<a id="schema_PricingRoomTreeVo"></a>
<a id="tocSpricingroomtreevo"></a>
<a id="tocspricingroomtreevo"></a>

```json
{
  "treeId": "string",
  "treeName": "string",
  "roomId": "string",
  "roomName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomUsage": "string",
  "roomType": "string",
  "rentArea": 0,
  "children": [
    {
      "treeId": "string",
      "treeName": "string",
      "roomId": "string",
      "roomName": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomUsage": "string",
      "roomType": "string",
      "rentArea": 0,
      "children": [
        {
          "treeId": "string",
          "treeName": "string",
          "roomId": "string",
          "roomName": "string",
          "parcelId": "string",
          "parcelName": "string",
          "buildingId": "string",
          "buildingName": "string",
          "floorId": "string",
          "floorName": "string",
          "roomUsage": "string",
          "roomType": "string",
          "rentArea": 0,
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

立项定价选择房源接口出参VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|treeId|string|false|none|节点ID|节点ID|
|treeName|string|false|none|节点名称|节点名称|
|roomId|string|false|none|房间ID|房间ID|
|roomName|string|false|none|房源名称|房源名称|
|parcelId|string|false|none|地块ID|地块ID|
|parcelName|string|false|none|地块|地块|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|buildingName|string|false|none|楼栋|楼栋|
|floorId|string|false|none|楼层ID|楼层ID|
|floorName|string|false|none|楼层|楼层|
|roomUsage|string|false|none|用途|用途|
|roomType|string|false|none|户型|户型|
|rentArea|number|false|none|计租面积|计租面积|
|children|[[PricingRoomTreeVo](#schemapricingroomtreevo)]|false|none|子节点列表|子节点列表|

