# 🚀 项目部署控制台

一个简单易用的网页界面，用于一键执行项目构建和部署。

## 📁 文件说明

- `deploy-web.html` - 部署控制台网页界面
- `deploy-server.js` - Node.js 服务器
- `start-deploy-console.js` - 启动脚本
- `upload.server.js` - 原有的部署脚本（已集成Git提交信息）

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

```bash
node start-deploy-console.js
```

### 方法二：手动启动

1. 安装依赖：
```bash
npm install express
```

2. 启动服务器：
```bash
node deploy-server.js
```

3. 打开浏览器访问：
```
http://localhost:3000
```

## ✨ 功能特性

### 🎯 主要功能
- **一键部署**：点击按钮执行 `npm run buildAndupload20`
- **实时日志**：部署过程中实时显示输出信息
- **Git信息**：查看今日所有提交记录
- **状态反馈**：清晰的成功/失败状态提示

### 📱 界面特点
- **现代化设计**：美观的渐变背景和卡片式布局
- **响应式布局**：支持不同屏幕尺寸
- **加载动画**：部署过程中显示旋转加载图标
- **状态颜色**：不同状态使用不同颜色区分

### 🔧 技术特性
- **流式输出**：实时显示部署日志，无需等待完成
- **进程管理**：安全的子进程执行和错误处理
- **Git集成**：自动获取今日提交记录
- **防重复执行**：部署进行中时禁用按钮

## 📊 界面预览

### 主界面
- 🚀 项目部署控制台标题
- 🔵 "开始部署" 按钮
- 🟢 "查看今日提交" 按钮
- 📝 实时日志显示区域
- 📋 Git提交信息展示

### 部署过程
- ⏳ 按钮变为"部署中..."状态
- 🔄 显示旋转加载动画
- 📜 实时滚动显示构建日志
- ✅ 完成后显示成功/失败状态

### Git信息展示
- 🌿 当前分支名称
- 📊 今日提交总数
- 📝 每条提交的详细信息：
  - ⏰ 提交时间
  - 💬 提交消息
  - 👤 提交作者
  - 🔗 提交哈希值

## 🛠️ 自定义配置

### 修改端口
在 `deploy-server.js` 中修改：
```javascript
const PORT = 3000; // 改为你想要的端口
```

### 修改部署命令
在 `deploy-server.js` 中修改：
```javascript
const deployProcess = spawn('npm', ['run', 'buildAndupload20'], {
    // 改为你的部署命令
});
```

### 修改样式
直接编辑 `deploy-web.html` 中的 CSS 样式。

## 🔒 安全注意事项

- 此工具仅适用于开发环境
- 不要在生产服务器上运行
- 确保只有授权人员可以访问

## 🐛 故障排除

### 🧪 环境测试工具

运行完整的环境测试：
```bash
node test-deploy-console.js
```

这会检查：
- Node.js 和 npm 版本
- Git 可用性和仓库状态  
- Express 依赖
- 服务器连接状态

### 常见问题

1. **浏览器控制台出现广告脚本错误**
   - 如果看到类似 `ads.914af30a.js` 的错误，这是浏览器扩展或第三方脚本引起的
   - 不会影响部署控制台的功能，我们已经添加了错误过滤机制

2. **端口被占用**
   ```bash
   # 查看占用端口的进程
   lsof -i :3000
   
   # 或者修改 deploy-server.js 中的 PORT 变量
   ```

3. **服务器连接失败**
   - 确保运行了 `node deploy-server.js`
   - 检查防火墙设置
   - 运行测试脚本检查环境

4. **npm 命令不存在**
   - 确保已安装 Node.js 和 npm
   - 检查环境变量配置

5. **权限错误**
   - 确保有执行 npm 脚本的权限
   - 检查文件系统权限

6. **Git 信息获取失败**
   - 确保在 Git 仓库目录中运行
   - 检查 Git 是否正确安装
   - 确保有提交权限

7. **部署命令失败**
   - 确保 `package.json` 中有 `buildAndupload20` 脚本
   - 检查所有依赖是否已安装
   - 确保有网络连接

## 📝 更新日志

- ✅ 支持实时日志输出
- ✅ 集成 Git 提交信息显示
- ✅ 美化界面设计
- ✅ 添加加载状态和错误处理
- ✅ 支持今日提交记录查看

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！ 