<template>
    <div class="flow-edit">
        <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical" @submit="handleSubmit">
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item field="projectId" label="项目" required>
                        <a-select v-model="formData.projectId" placeholder="请选择项目" allow-clear>
                            <a-option v-for="item in projectOptions" :key="item.id" :value="item.id">
                                {{ item.name }}
                            </a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="orderNo" label="订单号" required>
                        <a-input v-model="formData.orderNo" placeholder="请输入订单号" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="payDirection" label="支付方向" required>
                        <a-select v-model="formData.payDirection" placeholder="请选择支付方向" allow-clear>
                            <a-option :value="0">收入</a-option>
                            <a-option :value="1">支出</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="payType" label="支付类型" required>
                        <a-select v-model="formData.payType" placeholder="请选择支付类型" allow-clear>
                            <a-option :value="1">线上</a-option>
                            <a-option :value="2">线下</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="payMethod" label="支付方式" required>
                        <a-select v-model="formData.payMethod" placeholder="请选择支付方式" allow-clear>
                            <a-option :value="1">微信</a-option>
                            <a-option :value="2">支付宝</a-option>
                            <a-option :value="3">银行卡</a-option>
                            <a-option :value="4">现金</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="targetType" label="支付对象类型" required>
                        <a-select v-model="formData.targetType" placeholder="请选择支付对象类型" allow-clear>
                            <a-option :value="1">园区</a-option>
                            <a-option :value="2">地块</a-option>
                            <a-option :value="3">楼栋</a-option>
                            <a-option :value="4">房间</a-option>
                            <a-option :value="5">账单支付</a-option>
                            <a-option :value="6">银行转账</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="target" label="支付对象" required>
                        <a-input v-model="formData.target" placeholder="请输入支付对象" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="entryTime" label="入账时间" required>
                        <a-date-picker v-model="formData.entryTime" style="width: 100%" 
                            value-format="YYYY-MM-DD HH:mm:ss" show-time placeholder="请选择入账时间" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="amount" label="支付金额" required>
                        <a-input-number v-model="formData.amount" placeholder="请输入支付金额" 
                            :precision="2" :min="0" style="width: 100%" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="usedAmount" label="已使用金额">
                        <a-input-number v-model="formData.usedAmount" placeholder="请输入已使用金额" 
                            :precision="2" :min="0" style="width: 100%" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="payerName" label="支付人姓名" required>
                        <a-input v-model="formData.payerName" placeholder="请输入支付人姓名" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="payerPhone" label="支付人手机号">
                        <a-input v-model="formData.payerPhone" placeholder="请输入支付人手机号" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="payerAccount" label="支付人账号">
                        <a-input v-model="formData.payerAccount" placeholder="请输入支付人账号" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="merchant" label="收款商户">
                        <a-input v-model="formData.merchant" placeholder="请输入收款商户" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="payChannel" label="收款渠道">
                        <a-input v-model="formData.payChannel" placeholder="请输入收款渠道" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item field="sourceNo" label="原单号">
                        <a-input v-model="formData.sourceNo" placeholder="请输入原单号" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item field="payRemark" label="支付备注">
                        <a-textarea v-model="formData.payRemark" placeholder="请输入支付备注" 
                            :max-length="200" show-word-limit allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item field="isOtherIncome" label="其他收入">
                        <a-switch v-model="formData.isOtherIncome" />
                    </a-form-item>
                </a-col>
                <a-col v-if="formData.isOtherIncome" :span="24">
                    <a-form-item field="otherIncomeDesc" label="其他收入说明">
                        <a-textarea v-model="formData.otherIncomeDesc" placeholder="请输入其他收入说明" 
                            :max-length="200" show-word-limit allow-clear />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
        
        <div class="form-actions">
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" @click="handleSubmit" :loading="loading">保存</a-button>
            </a-space>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import type { FinancialFlowAddDTO } from '@/api/flowManage'

interface Props {
    data: Partial<FinancialFlowAddDTO>
    mode: 'add' | 'edit'
}

const props = defineProps<Props>()

const emit = defineEmits<{
    cancel: []
    save: [data: FinancialFlowAddDTO]
}>()

const formRef = ref()
const loading = ref(false)

// 项目选项
const projectOptions = ref([
    { id: '1', name: '项目A' },
    { id: '2', name: '项目B' }
])

const formData = reactive<FinancialFlowAddDTO>({
    projectId: '',
    orderNo: '',
    payDirection: 0,
    payType: 1,
    payMethod: 1,
    targetType: 1,
    target: '',
    entryTime: '',
    status: 0,
    amount: 0,
    usedAmount: 0,
    payerName: '',
    payerPhone: '',
    payerAccount: '',
    payRemark: '',
    merchant: '',
    payChannel: '',
    sourceNo: '',
    isOtherIncome: false,
    otherIncomeDesc: '',
    isDel: false
})

const rules = {
    projectId: [{ required: true, message: '请选择项目' }],
    orderNo: [{ required: true, message: '请输入订单号' }],
    payDirection: [{ required: true, message: '请选择支付方向' }],
    payType: [{ required: true, message: '请选择支付类型' }],
    payMethod: [{ required: true, message: '请选择支付方式' }],
    targetType: [{ required: true, message: '请选择支付对象类型' }],
    target: [{ required: true, message: '请输入支付对象' }],
    entryTime: [{ required: true, message: '请选择入账时间' }],
    amount: [{ required: true, message: '请输入支付金额' }],
    payerName: [{ required: true, message: '请输入支付人姓名' }]
}

// 监听props变化，更新表单数据
watch(() => props.data, (newData) => {
    if (newData) {
        Object.assign(formData, newData)
    }
}, { immediate: true, deep: true })

const handleSubmit = async () => {
    try {
        const errors = await formRef.value.validate()
        if (errors) return
        
        loading.value = true
        emit('save', { ...formData })
    } catch (error) {
        console.error('表单验证失败:', error)
    } finally {
        loading.value = false
    }
}

const handleCancel = () => {
    emit('cancel')
}

// 获取项目列表
const getProjects = async () => {
    try {
        // TODO: 调用接口获取项目列表
        // projectOptions.value = await getProjectList()
    } catch (error) {
        console.error('获取项目列表失败:', error)
    }
}

onMounted(() => {
    getProjects()
})
</script>

<style scoped lang="less">
.flow-edit {
    padding: 16px;
    
    .form-actions {
        margin-top: 24px;
        text-align: right;
        border-top: 1px solid var(--color-border-2);
        padding-top: 16px;
    }
}
</style> 