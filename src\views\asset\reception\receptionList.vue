<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form :model="formModel" auto-label-width label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="project" label="项目">
                                    <ProjectSelector v-model="formModel.projectId"
                                        @change="handleProjectSelectorChange" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="receptionCode" label="楼栋名称">
                                    <a-input v-model="formModel.projectName" placeholder="请输入名称" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="status" label="审批状态">
                                    <a-select v-model="formModel.status" placeholder="请选择状态" allow-clear>
                                        <a-option value="0">草稿</a-option>
                                        <a-option value="1">审批中</a-option>
                                        <a-option value="2">已审批</a-option>
                                        <a-option value="3">审批拒绝</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <!-- <a-divider style="height: 84px" direction="vertical" /> -->
                <a-col :flex="'86px'" style="text-align: right; margin-left: 16px;">
                    <a-space :size="18">
                        <a-button v-permission="['asset:receive:list']" type="primary" @click="searchForm">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="resetForm">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin: 0 0 16px 0;" />

            <!-- 操作按钮区域 -->
            <a-row style="margin: 16px 0;text-align: right;">
                <a-col>
                    <a-button v-permission="['asset:receive:add']" type="primary" @click="handleAdd">资产接收</a-button>
                </a-col>
            </a-row>

            <!-- 表格区域 -->
            <a-table row-key="id" :loading="loading" :data="tableData" :columns="columns" :bordered="{ cell: true }"
                :pagination="pagination" @page-change="onPageChange" @page-size-change="onPageSizeChange"
                :scroll="{ x: 1 }">
                <template #index="{ rowIndex }">
                    {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
                </template>
                <template #receiveType="{ record }">
                    {{ record.receiveType === '1' ? '新项目首次接收' : record.receiveType === '2' ? '老项目接收' :
                        record.receiveType === '3' ? '外部项目接收' : record.receiveType }}
                </template>
                <template #receiveDate="{ record }">
                    {{ record.receiveDate ? new Date(record.receiveDate).toLocaleDateString() : '' }}
                </template>
                <template #approvalStatus="{ record }">
                    <a-tag :color="getStatusColor(record.approvalStatus)">
                        {{ getStatusText(record.approvalStatus) }}
                    </a-tag>
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <!-- 草稿状态才显示编辑、删除、提交按钮 -->
                        <template v-if="record.approvalStatus === 0">
                            <a-button v-permission="['asset:receive:edit']" type="text" size="mini" @click="handleEdit(record)">编辑</a-button>
                            <a-button v-permission="['asset:receive:remove']" type="text" size="mini" @click="handleDelete(record)">删除</a-button>
                            <a-button v-permission="['asset:receive:submit']" type="text" size="mini" @click="handleSubmit(record)">提交</a-button>
                        </template>
                        <!-- 所有状态都显示详情按钮 -->
                        <a-button v-permission="['asset:receive:detail']" v-else-if="record.approvalStatus === 2" type="text" size="mini"
                            @click="handleDetail(record)">详情</a-button>
                    </a-space>
                </template>
            </a-table>
        </a-card>
        <!-- 选择楼栋弹窗 -->
        <a-modal v-model:visible="dialogVisible" :width="'700px'" title="资产接收-选择楼栋" :footer="false"
            @cancel="handleCancel" @open="handleDialogOpen" class="building-modal">
            <a-form :model="buildingForm" :label-col-props="{ span: 4 }" :wrapper-col-props="{ span: 20 }"
                auto-label-width>
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item field="projectId" label="所属项目">
                            <a-input v-model="projectData.projectName" disabled></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item field="buildingName" label="楼栋名称">
                            <a-input v-model="buildingForm.buildingName" placeholder="请输入楼栋名称" allow-clear
                                @change="handleBuildingNameChange" />
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>

            <a-card style="position: relative; min-height: 300px;">
                <a-spin :loading="buildingLoading">
                    <div class="empty-container" v-if="treeData.length === 0">
                        <a-empty class="centered-empty" description="暂无楼栋数据">
                            <template #image>
                                <icon-folder style="font-size: 48px; color: var(--color-text-3)" />
                            </template>
                        </a-empty>
                    </div>
                    <a-tree v-else ref="treeRef" blockNode checkable v-model:checked-keys="checkedKeys"
                        checked-strategy="child" :data="treeData" :virtualListProps="{ height: 300 }" :show-line="true"
                        :auto-expand-parent="true" :field-names="{
                            key: 'key',
                            title: 'title',
                            children: 'children'
                        }">
                    </a-tree>
                </a-spin>
            </a-card>

            <div class="modal-footer">
                <a-space>
                    <a-button @click="handleCancel">取消</a-button>
                    <a-button type="primary" @click="handleNext">下一步</a-button>
                </a-space>
            </div>
        </a-modal>

        <!-- 选择房源弹窗 -->
        <a-modal v-model:visible="roomDialogVisible" title="资产接收-选择房源" :width="1000" :footer="false"
            @cancel="handleRoomCancel" class="common-modal-no-padding room-selection-modal">
            <div class="modal-header">
                <a-form :model="roomForm" auto-label-width>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="是否公司自持" field="company">
                                <a-select v-model="roomForm.company" placeholder="全部">
                                    <a-option value="">全部</a-option>
                                    <a-option value="1">是</a-option>
                                    <a-option value="0">否</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="产品类型" field="productType">
                                <a-select v-model="roomForm.productType" placeholder="全部">
                                    <a-option value="">全部</a-option>
                                    <a-option v-for="dict in product_type" :key="dict.value" :value="dict.value">
                                        {{ dict.label }}
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="房源名称" field="roomName">
                                <a-input v-model="roomForm.roomName" placeholder="请输入房源名称" allow-clear />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <div class="modal-content">
                <div class="room-selection">
                    <!-- 全局全选 -->
                    <div class="global-select-all-container" v-if="roomForm.roomData.length > 0">
                        <a-checkbox v-model="roomForm.selectAll" @change="handleGlobalSelectAll">全选</a-checkbox>
                    </div>

                    <div class="building-container" v-for="building in roomForm.roomData" :key="building.id">
                        <div class="building-header">
                            <a-checkbox v-model="roomForm.buildingSelect[building.id]"
                                @change="(checked: boolean) => handleBuildingChange(building.id, checked)">
                                {{ building.buildingName }}
                            </a-checkbox>
                        </div>

                        <div class="room-table">
                            <!-- 表头 -->
                            <div class="table-header">
                                <div class="floor-header">楼层</div>
                                <div class="room-header">房间</div>
                            </div>

                            <!-- 表格内容 -->
                            <div class="table-content">
                                <div v-for="floor in building.floors" :key="floor.id" class="table-row">
                                    <div class="floor-cell">
                                        <a-checkbox v-model="roomForm.rooms[`${building.id}_${floor.id}`]"
                                            @change="(checked: boolean) => handleFloorChange(building.id, floor.id, checked)">
                                            {{ floor.floorName }}
                                        </a-checkbox>
                                    </div>
                                    <div class="room-cell">
                                        <a-checkbox v-for="room in floor.rooms" :key="room.id"
                                            v-model="roomForm.rooms[`${building.id}_${floor.id}_${room.id}`]"
                                            @change="() => handleRoomChange(building.id, floor.id)"
                                            class="room-checkbox">
                                            {{ room.roomName }}
                                        </a-checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="roomForm.roomData.length === 0" class="empty-data"
                        style="position: relative; min-height: 300px;">
                        <div class="empty-container">
                            <a-empty class="centered-empty" description="暂无房间数据" />
                        </div>
                    </div>

                </div>
            </div>

            <div class="modal-footer">
                <a-space>
                    <a-button @click="handleRoomPrev">上一步</a-button>
                    <a-button type="primary" @click="handleRoomNext">下一步</a-button>
                    <a-button @click="handleRoomCancel">取消</a-button>
                </a-space>
            </div>
        </a-modal>
        <add-form ref="addFormRef" v-model:visible="addFormVisible" :projectId="buildingForm.projectId"
            :projectName="projectData?.projectName" :selected-rooms="roomForm.rooms"
            :key="addFormVisible.toString() + Date.now()" :room-data="roomForm.roomData" @submit="handleAddFormSubmit"
            @cancel="handleAddFormCancel" />
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import AddForm from './components/addForm.vue'
import ProjectSelector from '@/components/projectSelector/index.vue'
import {
    getReceiveList,
    getReceiveDetail,
    deleteReceive,
    getBuildingTree,
    getRoomTree,
    getProjectList, updateStatus
} from '@/api/asset/projectReceive'
import { nextTick } from 'vue'
import { useDict } from '@/utils/dict'

// 使用字典
const { product_type } = useDict('product_type')

// 查询表单数据
const formModel = reactive({
    projectId: '',
    projectName: '',
    status: ''
})


// 表格数据
const loading = ref(false)
const tableData = ref<any[]>([])

// 表格列定义
const columns = [
    {
        title: '序号',
        slotName: 'index',
        width: 70,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '接收单编码',
        dataIndex: 'receiveCode',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '接收类型',
        dataIndex: 'receiveType',
        slotName: 'receiveType',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '项目名称',
        dataIndex: 'projectName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '接收楼栋',
        dataIndex: 'receiveBuilding',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '接收房源数',
        dataIndex: 'receiveRoomCount',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '接收原因',
        dataIndex: 'receiveReason',
        width: 140,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '接收日期',
        dataIndex: 'receiveDate',
        slotName: 'receiveDate',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '审批状态',
        dataIndex: 'approvalStatus',
        slotName: 'approvalStatus',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '审批通过时间',
        dataIndex: 'approvalTime',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 190,
        fixed: 'right',
        align: 'center'
    }
]

// 分页配置
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
    showTotal: true,
    showJumper: true,
    showPageSize: true
})

const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
});

const selectedKeys = ref<string[]>([]);

// 多选框选中数据
const handleSelectionChange = (rowKeys: string[]) => {
    selectedKeys.value = rowKeys
};;

// 获取状态颜色
const getStatusColor = (status: number) => {
    const colorMap: Record<number, string> = {
        0: 'gray',    // 草稿
        1: 'blue',    // 审批中
        2: 'green',   // 已审批
        3: 'red'      // 审批拒绝
    }
    return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: number) => {
    const statusMap: Record<number, string> = {
        0: '草稿',
        1: '审批中',
        2: '已审批',
        3: '审批拒绝'
    }
    return statusMap[status] || '未知'
}

// 查询方法
const searchForm = () => {
    pagination.current = 1
    fetchData()
}

// 重置方法
const resetForm = () => {
    formModel.projectId = ''
    formModel.projectName = ''
    formModel.status = ''
    fetchData()
}

// 分页方法
const onPageChange = (current: number) => {
    pagination.current = current
    fetchData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    fetchData()
}

// 获取项目列表（用于ProjectSelector组件）
const loadProjectList = async () => {
    try {
        const response = await getProjectList({})

        // 处理不同的API响应格式
        if (response && response.code === 200) {
            let dataSource: any[] = []

            if (response.rows && Array.isArray(response.rows)) {
                // 标准分页格式
                dataSource = response.rows
            } else if (response.data) {
                // data字段包含数据
                dataSource = Array.isArray(response.data) ? response.data : (response.data as any).rows || []
            }

            return {
                data: dataSource.map((item: any) => ({
                    id: item.id,
                    projectName: item.mdmName || item.name || ''
                }))
            }
        }

        return { data: [] }
    } catch (error) {
        console.error('获取项目列表失败:', error)
        return { data: [] }
    }
}

// 处理项目选择变化（用于ProjectSelector组件）
const projectData = ref<any>({
    id: '',
    projectName: ''
})
const handleProjectSelectorChange = (projectId: string, project: any) => {
    projectData.value = project
    // 项目变化时重新查询数据
    searchForm()
}


// 获取接收列表数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            projectId: formModel.projectId,
            receiveBuilding: formModel.projectName,
            approvalStatus: formModel.status ? parseInt(formModel.status) : undefined
        }

        const res = await getReceiveList(params)

        // 处理不同的返回格式
        if (res && res.code === 200 && res.rows) {
            // 标准分页格式
            tableData.value = res.rows.map((item: any) => ({
                ...item,
                approvalStatusText: getStatusText(item.approvalStatus)
            }))
            pagination.total = res.total || 0
        } else if (res && res.code === 200 && res.data) {
            // data字段包含数据
            const dataSource = res.data.rows || res.data
            if (Array.isArray(dataSource)) {
                tableData.value = dataSource.map((item: any) => ({
                    ...item,
                    approvalStatusText: getStatusText(item.approvalStatus)
                }))
                pagination.total = res.data.total || dataSource.length || 0
            } else {
                tableData.value = []
                pagination.total = 0
            }
        } else {
            tableData.value = []
            pagination.total = 0
        }
    } catch (error) {
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 编辑方法
const handleEdit = (record: any) => {

    try {
        // 设置项目信息
        buildingForm.projectId = record.projectId

        // 打开详情弹窗
        addFormVisible.value = true

        // 使用nextTick确保子组件挂载完成后再调用openDetail
        nextTick(() => {
            // 使用接收单ID，编辑模式可编辑
            const detailProps = {
                id: record.id,
                isEdit: true,
                projectId: record.projectId,
                projectName: projectData.value.projectName,
                readOnly: false // 可编辑模式
            }
            // 打开addForm弹窗并传入详情ID
            addFormRef.value?.openDetail(detailProps)
        })
    } catch (error) {
        Message.error('获取详情失败')
    }
}

// 提交方法
const handleSubmit = async (record: any) => {
    try {
        // 调用API
        const response = await updateStatus(record.id)

        if (response && response.code === 200) {
            Message.success('提交成功')
            // 刷新列表
            fetchData()
        } else {
            Message.error('提交失败: ' + (response?.msg || '未知错误'))
        }
    } catch (error) {
    }
}

// 详情方法
const handleDetail = async (record: any) => {
    try {
        // 设置编辑模式和ID
        buildingForm.projectId = record.projectId

        // 打开详情弹窗
        addFormVisible.value = true
        nextTick(() => {
            // 使用接收单ID，详情模式为只读
            const detailProps = {
                id: record.id,
                isEdit: true,
                projectId: record.projectId,
                projectName: record.projectName,
                readOnly: true // 只读模式
            }

            // 打开addForm弹窗并传入详情ID
            addFormRef.value?.openDetail(detailProps)
        })
    } catch (error) {
        Message.error('获取详情失败')
    }
}

// 删除方法
const handleDelete = async (record: any) => {
    try {

        // 弹出确认对话框
        Modal.confirm({
            title: '删除确认',
            content: `确定要删除接收单编码 "${record.receiveCode}" 吗？`,
            okText: '确认',
            cancelText: '取消',
            onOk: async () => {
                try {
                    loading.value = true;
                    const res = await deleteReceive(record.id);

                    // 处理响应
                    if (res.code === 200) {
                        Message.success('删除成功');
                        // 刷新列表
                        fetchData();
                    } else {
                        Message.error('删除失败: ' + (res?.msg || '未知错误'))
                    }
                } catch (error) {
                    // 删除失败，错误信息由拦截器处理
                } finally {
                    loading.value = false;
                }
            }
        });
    } catch (error) {
    }
}

// 树形数据
const treeData = ref<any[]>([]);

// 楼栋树加载状态
const buildingLoading = ref(false);

// 选中的树节点
const checkedKeys = ref<string[]>([]);
const treeRef = ref();

// 弹窗相关
const dialogVisible = ref(false);
const buildingForm = reactive({
    projectId: '',
    buildingName: '',
    selectedBuildings: [] as string[]
})

// 打开弹窗
const handleAdd = () => {
    if (!formModel.projectId) {
        Message.warning('请先选择项目')
        return
    }
    // 重置选择状态
    checkedKeys.value = []
    buildingForm.buildingName = ''

    // 默认使用当前选中的项目（如果有）
    buildingForm.projectId = formModel.projectId || ''


    // 显示弹窗
    dialogVisible.value = true
}

// 取消
const handleCancel = () => {
    dialogVisible.value = false
    buildingForm.projectId = ''
    buildingForm.buildingName = ''
    checkedKeys.value = []
    treeData.value = []
}

// 房源选择弹窗
const roomDialogVisible = ref(false);
const roomForm = reactive({
    company: '',
    productType: '',
    roomName: '',
    selectAll: false,
    rooms: {} as Record<string, boolean>,
    buildingSelect: {} as Record<string, boolean>, // 存储楼栋选择状态
    roomData: [] as any[] // 存储房间数据
});

// 处理全局全选变化
const handleGlobalSelectAll = (checked: boolean) => {
    console.log('全局全选状态变更为:', checked)

    // 先设置所有楼栋的选中状态
    roomForm.roomData.forEach(building => {
        roomForm.buildingSelect[building.id] = checked;
    });

    // 设置所有楼层和房间的选中状态
    Object.keys(roomForm.rooms).forEach(key => {
        roomForm.rooms[key] = checked;
    });

    // 确保全选状态与实际选择状态一致
    roomForm.selectAll = checked;

    console.log('全选操作完成，当前选择状态:', {
        selectAll: roomForm.selectAll,
        buildingSelect: roomForm.buildingSelect,
        roomsCount: Object.keys(roomForm.rooms).length,
        selectedRoomsCount: Object.values(roomForm.rooms).filter(Boolean).length
    })
}

// 处理楼栋选择变化
const handleBuildingChange = (buildingId: string, checked: boolean) => {

    // 设置该楼栋下所有楼层和房间的选中状态
    Object.keys(roomForm.rooms).forEach(key => {
        // 查找匹配的楼层和房间键 (buildingId_*)
        if (key.startsWith(`${buildingId}_`)) {
            roomForm.rooms[key] = checked;
        }
    });

    // 更新楼栋选中状态
    roomForm.buildingSelect[buildingId] = checked;

    // 更新全选状态 - 检查是否所有楼栋都被选中
    updateSelectAllStatus();
}

// 监听每层楼的选中状态
const handleFloorChange = (buildingId: string, floorId: string, checked: boolean) => {

    // 设置该层所有房间的选中状态
    Object.keys(roomForm.rooms).forEach(key => {
        // 查找匹配的房间键 (buildingId_floorId_roomId)
        if (key.startsWith(`${buildingId}_${floorId}_`)) {
            roomForm.rooms[key] = checked;
        }
    });

    // 更新该楼层的选中状态
    roomForm.rooms[`${buildingId}_${floorId}`] = checked;

    // 检查该楼栋所有楼层是否都被选中，并更新楼栋状态
    updateBuildingSelectStatus(buildingId);

    // 检查是否所有楼栋都被选中，并更新全选状态
    updateSelectAllStatus();
}

// 处理房间选中状态变化
const handleRoomChange = (buildingId: string, floorId: string) => {
    const floorKey = `${buildingId}_${floorId}`;

    // 查找属于该楼层的所有房间
    const floorRooms = Object.keys(roomForm.rooms).filter(key =>
        key.startsWith(`${buildingId}_${floorId}_`)
    );

    if (floorRooms.length === 0) return;

    // 检查是否所有房间都被选中
    const allRoomsSelected = floorRooms.every(roomKey => roomForm.rooms[roomKey]);


    // 更新该层的选中状态
    roomForm.rooms[floorKey] = allRoomsSelected;

    // 更新楼栋的选中状态
    updateBuildingSelectStatus(buildingId);

    // 检查是否所有楼栋都被选中，并更新全选状态
    updateSelectAllStatus();
}

// 更新楼栋选中状态
const updateBuildingSelectStatus = (buildingId: string) => {
    // 获取该楼栋下所有楼层的key (buildingId_floorId)
    const floorKeys = Object.keys(roomForm.rooms).filter(key =>
        key.startsWith(`${buildingId}_`) && key.split('_').length === 2
    );

    if (floorKeys.length === 0) {
        roomForm.buildingSelect[buildingId] = false;
        return;
    }

    // 检查该楼栋所有楼层是否都被选中
    const allFloorsSelected = floorKeys.every(key => roomForm.rooms[key]);

    roomForm.buildingSelect[buildingId] = allFloorsSelected;
}

// 更新全选状态
const updateSelectAllStatus = () => {
    // 检查是否有楼栋
    if (roomForm.roomData.length === 0) {
        roomForm.selectAll = false
        return
    }

    // 检查是否所有楼栋都被选中
    roomForm.selectAll = roomForm.roomData.every(building => {
        return roomForm.buildingSelect[building.id] === true;
    });
}

// 下一步
const handleNext = () => {
    if (checkedKeys.value.length === 0) {
        Message.warning('请至少选择一个楼栋')
        return
    }

    // 存储选中的楼栋ID和名称
    buildingForm.selectedBuildings = checkedKeys.value.filter(key => !key.toString().startsWith('parcel_') && !key.toString().startsWith('floor_'))

    // 获取选中楼栋的名称，用于展示
    const selectedBuildingNames = buildingForm.selectedBuildings.map(id => {
        const findName = (nodes: any[]): string | null => {
            for (const node of nodes) {
                if (node.key === id) return node.title
                if (node.children?.length) {
                    const found = findName(node.children)
                    if (found) return found
                }
            }
            return null
        }

        return findName(treeData.value) || id
    })


    dialogVisible.value = false
    roomDialogVisible.value = true

    // 进入房间选择页面时加载房间数据
    loadRoomTree()
}

// 上一步
const handleRoomPrev = () => {
    roomDialogVisible.value = false
    dialogVisible.value = true
}

// 获取房间树形数据
const loadRoomTree = async () => {
    if (!buildingForm.selectedBuildings || buildingForm.selectedBuildings.length === 0) {
        roomForm.roomData = []
        // 清空选择状态
        roomForm.rooms = {}
        roomForm.buildingSelect = {}
        roomForm.selectAll = false
        return
    }

    try {
        const params = {
            buildingIds: buildingForm.selectedBuildings,
            roomName: roomForm.roomName || undefined,
            productType: roomForm.productType !== '' ? roomForm.productType : undefined,
            isCompanySelf: roomForm.company === '' ? undefined : roomForm.company === '1',
            type: 1
        }

        console.log('加载房间树数据，参数:', params)
        const res = await getRoomTree(params)

        // 处理不同的返回格式
        if (res && Array.isArray(res)) {
            // 直接返回数组
            roomForm.roomData = res
            initRoomSelection(res)
        } else if (res && res.code === 200) {
            // 标准API响应格式
            if (Array.isArray(res.data)) {
                roomForm.roomData = res.data
                initRoomSelection(res.data)
            } else if (res.data && typeof res.data === 'object') {
                // 数据可能在data的某个属性中
                const dataSource = res.data.list || res.data.rows || res.data

                if (Array.isArray(dataSource)) {
                    roomForm.roomData = dataSource
                    initRoomSelection(dataSource)
                } else {
                    roomForm.roomData = []
                    // 清空选择状态
                    roomForm.rooms = {}
                    roomForm.buildingSelect = {}
                    roomForm.selectAll = false
                }
            } else {
                roomForm.roomData = []
                // 清空选择状态
                roomForm.rooms = {}
                roomForm.buildingSelect = {}
                roomForm.selectAll = false
            }
        } else {
            roomForm.roomData = []
            // 清空选择状态
            roomForm.rooms = {}
            roomForm.buildingSelect = {}
            roomForm.selectAll = false
        }
    } catch (error) {
        console.error('加载房间树数据失败:', error)
        roomForm.roomData = []
        // 清空选择状态
        roomForm.rooms = {}
        roomForm.buildingSelect = {}
        roomForm.selectAll = false
    }
}

// 初始化房间选择状态
const initRoomSelection = (buildings: any[]) => {
    console.log('初始化房间选择状态，楼栋数量:', buildings.length)

    // 清空之前的选择
    roomForm.rooms = {};
    roomForm.buildingSelect = {};
    roomForm.selectAll = false;

    // 为每个楼栋、楼层和房间设置初始选择状态
    buildings.forEach(building => {
        // 初始化楼栋选择状态
        roomForm.buildingSelect[building.id] = false;

        if (building.floors && Array.isArray(building.floors)) {
            building.floors.forEach((floor: any) => {
                const floorKey = `${building.id}_${floor.id}`;
                roomForm.rooms[floorKey] = false;

                if (floor.rooms && Array.isArray(floor.rooms)) {
                    floor.rooms.forEach((room: any) => {
                        const roomKey = `${building.id}_${floor.id}_${room.id}`;
                        roomForm.rooms[roomKey] = false;
                    });
                }
            });
        }
    });

    console.log('房间选择状态初始化完成:', {
        buildingCount: Object.keys(roomForm.buildingSelect).length,
        roomCount: Object.keys(roomForm.rooms).length,
        selectAll: roomForm.selectAll
    })
}

// 监听房间查询条件变化
watch(() => [roomForm.roomName, roomForm.productType, roomForm.company], () => {
    console.log('房间查询条件变化，重新加载房间树')
    loadRoomTree()
}, { deep: true })

// 处理房间选择弹窗的下一步
const handleRoomNext = () => {
    // 检查是否有选中的房间
    const hasSelectedRooms = Object.entries(roomForm.rooms).some(([key, value]) =>
        value && key.split('_').length === 3 // 只计算房间级别的选择
    )

    if (!hasSelectedRooms) {
        Message.warning('请至少选择一个房间')
        return
    }

    // 获取所有选中的房间ID
    const selectedRoomIds = Object.entries(roomForm.rooms)
        .filter(([key, value]) => value && key.split('_').length === 3)
        .map(([key]) => key.split('_')[2])


    // 传递选中的房间和完整的房间数据
    roomDialogVisible.value = false
    addFormVisible.value = true
}

// 房源弹窗取消
const handleRoomCancel = () => {
    console.log('房源弹窗取消，清理选择状态')

    roomDialogVisible.value = false
    roomForm.company = ''
    roomForm.productType = ''
    roomForm.roomName = ''
    roomForm.selectAll = false
    roomForm.rooms = {}
    roomForm.buildingSelect = {}
    roomForm.roomData = []

    console.log('房源弹窗状态清理完成')
}

// 添加表单弹窗
const addFormVisible = ref(false);
const addFormRef = ref();

// 添加表单提交
const handleAddFormSubmit = () => {
    addFormVisible.value = false;
    fetchData();
};

// 添加表单取消
const handleAddFormCancel = () => {
    addFormVisible.value = false;
};

// 获取楼栋树形数据
const loadBuildingTree = async () => {

    // 如果没有项目ID，显示提示并返回
    if (!buildingForm.projectId) {
        treeData.value = []
        return
    }

    // 设置加载状态
    buildingLoading.value = true

    try {

        const response = await getBuildingTree(buildingForm.projectId, buildingForm.buildingName || '')

        // 尝试获取数据，处理各种可能的数据结构
        let dataToProcess = []

        try {
            if (response) {
                // 处理不同的响应结构
                if (Array.isArray(response)) {
                    // 直接是数组
                    dataToProcess = response
                } else if (response.code === 200 && response.data) {
                    // 标准API响应格式 {code: 200, data: [...]}
                    if (Array.isArray(response.data)) {
                        dataToProcess = response.data
                    } else if (typeof response.data === 'object') {
                        // 数据可能嵌套在data中
                        if (response.data.list && Array.isArray(response.data.list)) {
                            dataToProcess = response.data.list
                        } else if (response.data.rows && Array.isArray(response.data.rows)) {
                            dataToProcess = response.data.rows
                        } else {
                            // 单个对象，包装成数组
                            dataToProcess = [response.data]
                        }
                    }
                } else if (response.rows && Array.isArray(response.rows)) {
                    // {rows: [...]} 格式
                    dataToProcess = response.rows
                } else if (response.list && Array.isArray(response.list)) {
                    // {list: [...]} 格式
                    dataToProcess = response.list
                }
            }

            if (dataToProcess.length === 0) {
                treeData.value = []
                Message.info('当前项目没有楼栋数据')
            } else {
                // 格式化并设置树数据
                const formattedTreeData = formatTreeData(dataToProcess)

                // 检查格式化是否成功
                if (formattedTreeData.length === 0) {
                    Message.warning('无法解析楼栋数据，请联系管理员')
                    treeData.value = []
                } else {
                    console.log('formattedTreeData', formattedTreeData)
                    treeData.value = formattedTreeData
                }
            }
        } catch (error) {
            treeData.value = []
            Message.error('解析楼栋数据时出错')
        }
    } catch (error) {
        Message.error('获取楼栋结构失败')
        treeData.value = []
    } finally {
        // 结束加载状态
        buildingLoading.value = false
    }
}

// 格式化树形数据，使其符合 a-tree 组件要求
const formatTreeData = (data: any[]) => {

    if (!data || data.length === 0) {
        return []
    }

    try {
        return data
            .filter(parcel => parcel !== null && parcel !== undefined) // 过滤掉null或undefined的地块
            .map(parcel => {
                // 创建唯一标识符，防止key为undefined
                const parcelId = parcel.id || parcel.parcelId || `parcel_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
                const parcelName = parcel.parcelName || parcel.name || '未命名地块'


                // 创建地块节点
                const parcelNode = {
                    key: `parcel_${parcelId}`,
                    title: parcelName,
                    selectable: true,
                    children: []
                }

                // 处理楼栋数据
                if (parcel.buildings && Array.isArray(parcel.buildings)) {

                    // 过滤掉null或undefined的楼栋
                    const validBuildings = parcel.buildings.filter((b: any) => b !== null && b !== undefined)

                    if (validBuildings.length > 0) {
                        parcelNode.children = validBuildings.map((building: any) => {
                            // 确保building.id和building.buildingName有值，使用唯一值防止重复
                            const buildingId = building.id || building.buildingId || `building_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
                            const buildingName = building.buildingName || building.name || '未命名楼栋'


                            // 创建楼栋节点
                            const buildingNode: any = {
                                key: String(buildingId), // 确保key是字符串
                                title: buildingName,
                                selectable: true,
                                children: []
                            }

                            // 处理楼层数据
                            if (building.floors && Array.isArray(building.floors)) {

                                // 过滤掉null或undefined的楼层
                                const validFloors = building.floors.filter((f: any) => f !== null && f !== undefined)

                                if (validFloors.length > 0) {
                                    buildingNode.children = validFloors.map((floor: any) => {
                                        const floorId = floor.id || floor.floorId || `floor_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
                                        const floorName = floor.floorName || floor.name || '未命名楼层'

                                        return {
                                            key: `floor_${floorId}`,
                                            title: floorName,
                                            selectable: false,
                                            isLeaf: true
                                        }
                                    })
                                }
                            }

                            return buildingNode
                        })
                    }
                }

                return parcelNode
            })
    } catch (error) {
        return []
    }
}

// 处理项目选择变化
const handleProjectChange = (projectId: string) => {

    // 清空之前的数据和选择
    buildingForm.buildingName = ''
    checkedKeys.value = []
    treeData.value = [] // 清空原有树数据

    // 设置加载状态，给用户视觉反馈
    buildingLoading.value = true

    // 设置当前选中的项目ID
    buildingForm.projectId = projectId

    // 使用setTimeout确保状态更新后再加载树
    setTimeout(() => {

        // 定义重试机制
        const loadTreeWithRetry = (retryCount = 0) => {

            // 项目变更后立即加载楼栋树
            loadBuildingTree().then(() => {
                buildingLoading.value = false


                // 如果没有数据且重试次数小于3，则重试
                if (treeData.value.length === 0 && retryCount < 3) {
                    setTimeout(() => {
                        // 再次检查项目ID是否有效
                        if (buildingForm.projectId) {
                            loadTreeWithRetry(retryCount + 1)
                        } else {
                            buildingLoading.value = false
                        }
                    }, 1000)
                }
            }).catch(err => {
                buildingLoading.value = false

                if (retryCount < 3) {
                    setTimeout(() => {
                        // 再次检查项目ID是否有效
                        if (buildingForm.projectId) {
                            loadTreeWithRetry(retryCount + 1)
                        } else {
                            buildingLoading.value = false
                            Message.error('项目数据已丢失，请重新选择项目')
                        }
                    }, 1000)
                } else {
                    Message.error('加载楼栋数据失败，请刷新页面或重新选择项目')
                }
            })
        }

        // 开始加载，带有重试机制
        loadTreeWithRetry()
    }, 100)
}

// 处理楼栋名称变化
const handleBuildingNameChange = () => {
    // 楼栋名称变化时重新加载
    loadBuildingTree()
}

// 弹窗打开时的处理函数
const handleDialogOpen = () => {
    // 项目列表加载完成后再加载楼栋树
    loadBuildingTree()
}
</script>

<style scoped lang="less">
.container {
    padding: 0 12px;

    .general-card {
        box-sizing: border-box;
        padding-top: 16px;
    }

    :deep(.arco-card) {
        background-color: var(--color-bg-2);
    }

    :deep(.arco-form) {
        .arco-form-item {
            margin-right: 16px;
            margin-bottom: 16px;
        }
    }

    :deep(.arco-table) {
        .arco-table-th {
            background-color: var(--color-neutral-2);
        }

        .arco-btn[type='text'] {
            color: rgb(var(--primary-6));

            &:hover {
                color: rgb(var(--primary-5));
            }
        }
    }
}

/* 房间选择模态框样式 */
.room-selection-modal {
    .modal-header {
        // padding: 16px;
        width: 100%;
        box-sizing: border-box;
        padding: 16px 16px 0 16px;
    }

    .modal-content {
        padding: 0 16px 16px 16px;
        height: 70vh;
        overflow-y: auto;
    }

    .modal-footer {
        // padding: 16px;
        position: sticky;
        bottom: 0;
        background: white;
        border-top: 1px solid #e5e6eb;
        padding: 16px 20px;
        text-align: right;
        z-index: 10;
    }
}
.building-modal {
    .modal-footer {
        position: sticky;
        bottom: 0;
        background: white;
        border-top: 1px solid #e5e6eb;
        padding: 16px 0 0 0;
        text-align: right;
        z-index: 10;
        // box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        // margin-top: 16px;
    }
}

// .modal-content {
//     // flex: 1;
//     max-height: 60vh;
//     overflow-y: auto;
//     // padding: 16px;
//     // padding-bottom: 20px;
// }

// .modal-footer {
//     position: sticky;
//     bottom: 0;
//     background: white;
//     border-top: 1px solid #e5e6eb;
//     padding: 16px 20px;
//     text-align: right;
//     z-index: 10;
//     // box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
//     // margin-top: 16px;
// }

:deep(.arco-tree) {
    padding: 8px;
}

:deep(.arco-empty) {
    padding: 0;
}

:deep(.arco-spin) {
    display: flex;
}

:deep(.centered-empty) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.room-selection {

    .building-container {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .building-header {
        font-weight: bold;
        font-size: 16px;
        color: rgb(var(--primary-6));
        padding: 8px;
        background-color: #f5f7fa;
        border-radius: 4px 4px 0 0;
        border: 1px solid var(--color-neutral-3);
    }

    .room-table {
        border: 1px solid var(--color-neutral-3);
        border-top: none;
        border-radius: 0 0 4px 4px;
        overflow: hidden;

        .table-header {
            display: flex;
            background-color: #e8f3ff;
            border-bottom: 1px solid var(--color-neutral-3);

            .floor-header,
            .room-header {
                padding: 4px 4px;
                font-weight: bold;
            }

            .floor-header {
                width: 120px;
                border-right: 1px solid var(--color-neutral-3);
            }

            .room-header {
                flex: 1;
            }
        }

        .table-content {
            max-height: 300px;
            overflow-y: auto;

            .table-row {
                display: flex;
                border-bottom: 1px solid var(--color-neutral-3);

                &:last-child {
                    border-bottom: none;
                }

                .floor-cell,
                .room-cell {
                    box-sizing: border-box;
                    padding: 4px 4px;
                }

                .floor-cell {
                    // min-width: 100px;
                    width: 120px;
                    line-height: 32px;
                    overflow: hidden;
                    border-right: 1px solid var(--color-neutral-3);
                }

                .room-cell {
                    flex: 1;
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                    gap: 4px;
                }
            }
        }
    }

    .empty-data {
        padding: 0;
        border: 1px solid var(--color-neutral-3);
        border-radius: 4px;
        margin-top: 16px;
        min-height: 200px;
    }

    .global-select-all-container {
        padding: 8px 4px;
        background-color: #f0f8ff;
        border: 1px solid var(--color-primary-3);
        border-radius: 4px;
        margin-bottom: 8px;

        :deep(.arco-checkbox) {
            font-weight: bold;
            color: rgb(var(--primary-6));
        }
    }

    .select-all-container {
        padding: 8px 0;
        border-radius: 4px;
    }

    .room-checkbox {
        margin-right: 8px;
    }
}

:deep(.arco-modal-body) {
    padding: 16px !important;
}

:deep(.arco-checkbox-label) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 4px !important;
}
</style>
