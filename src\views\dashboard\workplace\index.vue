<template>
    <div class="workplace-container">
        <!-- 顶部背景区域 -->
        <div class="top-banner">
            <!-- 顶部欢迎区域 -->
            <div class="welcome-header">

                <!-- 顶部区域，两块内容，左右布局 -->
                <div class="top-navigation">
                    <!-- 左侧：导航标签和项目选择 -->
                    <div class="left-nav">
                        <!-- <div class="nav-tabs">
                            <div class="nav-tab active">
                                <span>工作台</span>
                                <div class="tab-indicator"></div>
                            </div>
                            <div class="nav-tab">可视化</div>
                        </div> -->
                        <div class="project-select">
                            <ProjectTreeSelect class="dashboard-project" v-model="projectId" />
                        </div>
                    </div>

                    <!-- 右侧：用户信息和中心选择 -->
                    <div class="right-nav">
                        <!-- 中心选择器 -->
                        <div class="center-selector">
                            <a-dropdown trigger="click" @select="handleCenterSwitch">
                                <div class="center-selector-trigger">
                                    <!-- <div class="center-icon"> -->
                                    <img style="width: 16px;" src="/src/assets/images/dashboard/icon-center.png" />
                                    <!-- </div> -->
                                    <span>{{ centerStore.getCenterName }}</span>
                                    <img style="width: 25px;" src="/src/assets/images/dashboard/icon-change.png" />
                                </div>
                                <template #content>
                                    <a-doption v-for="center in centerOptions" :key="center.value"
                                        :value="center.value">
                                        {{ center.label }}
                                    </a-doption>
                                </template>
                            </a-dropdown>
                        </div>

                        <!-- 用户信息 -->
                        <a-dropdown>
                            <div class="user-info">
                                <div class="user-avatar">
                                    <img alt="avatar" :src="userStore.avatar || '/src/assets/images/user/avatar.png'" />
                                </div>
                                <span class="user-name">{{ userStore.name || '张一凡' }}</span>
                                <div class="user-dropdown">
                                    <icon-down />
                                </div>
                            </div>
                            <template #content>
                                <a-doption @click="switchRoles">
                                    <template #icon>
                                        <icon-tag />
                                    </template>
                                    切换角色
                                </a-doption>
                                <a-doption @click="$router.push({ name: 'Info' })">
                                    <template #icon>
                                        <icon-user />
                                    </template>
                                    个人中心
                                </a-doption>
                                <a-doption @click="$router.push({ name: 'Setting' })">
                                    <template #icon>
                                        <icon-settings />
                                    </template>
                                    用户设置
                                </a-doption>
                                <a-divider style="margin: 4px 0;" />
                                <a-doption @click="handleLogout">
                                    <template #icon>
                                        <icon-export />
                                    </template>
                                    退出登录
                                </a-doption>
                            </template>
                        </a-dropdown>
                    </div>
                </div>
                <div class="welcome-info">
                    <h2 class="welcome-title">欢迎回来，{{ userStore.name || '张一凡' }}</h2>
                    <p class="date-info">今天是 {{ currentDate }} {{ weekDay }}</p>
                </div>
            </div>

            <!-- 统计卡片区域 -->
            <div class="stats-cards-container">
                <div class="stats-card" v-for="(card, index) in statsCards" :key="index">
                    <div class="card-background">
                        <div class="card-icon-wrapper">
                            <img :src="getImageUrl(card.icon)" alt="">
                        </div>
                        <div class="card-text">
                            <div class="card-title">{{ card.title }}</div>
                            <div class="card-value">{{ card.value }} <span class="card-unit">{{ card.unit }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-container">
            <div class="section top-section">
                <div class="section-item">
                    <MyApproval />
                </div>
                <div class="section-item">
                    <HydropowerAlert />
                </div>
                <div class="section-item">
                    <RoomStatus />
                </div>
                <div class="section-item">
                    <features />
                </div>
            </div>
            <div class="section">
                <div class="section-item">
                    <unsignedContract />
                </div>
                <div class="section-item">
                    <contractPerformance />
                </div>
                <div class="section-item">
                    <processedFlow />
                </div>
                <div class="section-item">
                    <pendingBill />
                </div>
            </div>
            <div class="section">
                <div class="section-item">
                    <entryAndExit />
                </div>
                <div class="section-item">
                    <roomPrepare />
                </div>
                <div class="section-item">
                    <assetsManagement />
                </div>
                <div class="section-item">
                    <systemApproval />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import {
    IconDown,
    IconCheckCircle,
    IconClockCircle,
    IconCloseCircle,
    IconExclamationCircle,
    IconHome,
    IconUser,
    IconFile,
    IconSettings,
    IconCalendar,
    IconTool,
    IconArrowUp,
    IconArrowDown,
    IconTag,
    IconExport,
    IconRight
} from '@arco-design/web-vue/es/icon'
import dayjs from 'dayjs'
import { useCenterStore, useUserStore } from '@/store'
import useUser from '@/hooks/user'
import type { CenterType } from '@/store/modules/center'
import MyApproval from './components/myApproval.vue'
import HydropowerAlert from './components/hydropowerAlert.vue'
import RoomStatus from './components/roomStatus.vue'
import features from './components/features.vue'
import unsignedContract from './components/unsignedContract.vue'
import contractPerformance from './components/contractPerformance.vue'
import processedFlow from './components/processedFlow.vue'
import pendingBill from './components/pendingBill.vue'
import entryAndExit from './components/entryAndExit.vue'
import roomPrepare from './components/roomPrepare.vue'
import assetsManagement from './components/assetsManagement.vue'
import systemApproval from './components/systemApproval.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'

// Store 和 Router
const router = useRouter()
const centerStore = useCenterStore()
const userStore = useUserStore()
const { logout } = useUser()

const projectId = ref('')

// 响应式数据
const currentTime = ref(new Date())
const activeTab = ref('quick')

// 计算属性
const currentDate = computed(() => {
    return dayjs(currentTime.value).format('YYYY年MM月DD日')
})

const weekDay = computed(() => {
    const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    return weekDays[currentTime.value.getDay()]
})

// 中心切换选项
const centerOptions = computed(() => centerStore.centerList)

// 统计卡片数据
const statsCards = ref([
    {
        title: '待转签定单',
        value: '3',
        icon: 'icon-tab-1',
    },
    {
        title: '逾期代收账单',
        value: '39.3',
        unit: '万元',
        icon: 'icon-tab-2',
    },
    {
        title: '待处理流水',
        value: '89',
        icon: 'icon-tab-3',
    },
    {
        title: '待退租合同',
        value: '6',
        icon: 'icon-tab-4',
    },
    {
        title: '待出/进场',
        value: '9/8',
        icon: 'icon-tab-5',
    },
    {
        title: '营收待提报',
        value: '6',
        icon: 'icon-tab-6',
    },
    {
        title: '房源待定价',
        value: '7',
        icon: 'icon-tab-7',
    }
])

// 快捷操作
const quickActions = ref([
    { name: '新增定单', icon: IconFile },
    { name: '新增客户', icon: IconUser },
    { name: '房源租控', icon: IconHome },
    { name: '创建房源', icon: IconSettings },
    { name: '出场办理', icon: IconArrowDown },
    { name: '进场办理', icon: IconArrowUp },
    { name: '新建合同', icon: IconFile }
])

// 系统内待审批
const systemApprovals = ref([
    { label: '项目目标上报', count: '6' },
    { label: '集团目标下达', count: '1' },
    { label: '房源拆分合并', count: '11' },
    { label: '营收提报', count: '5' }
])

// 资产/房源管理
const assetManagement = ref([
    { label: '项目目标上报', count: '6' },
    { label: '集团目标下达', count: '1' },
    { label: '资产面积变更', count: '12' },
    { label: '待生效房源', count: '12' }
])

// 房源准备
const roomPreparation = ref([
    { label: '待定价', count: '3', type: 'blue' },
    { label: '待绑定电水表', count: '6', type: 'purple' },
    { label: '待配置宿舍户型', count: '6', type: 'orange' },
    { label: '待绑定门锁', count: '6', type: 'cyan' }
])

// 进出场统计
const accessStats = ref([
    { label: '待进场', count: '9', icon: 'IconArrowUp' },
    { label: '待出场', count: '8', icon: 'IconArrowDown' },
    { label: '出场办理中', count: '9', icon: 'IconSettings' }
])

// 方法
const handleCenterSwitch = (value: CenterType) => {
    centerStore.setCurrentCenter(value)
    Message.success(`已切换到${centerStore.getCenterName}`)
}

const handleLogout = () => {
    logout()
}

const switchRoles = async () => {
    const res = await userStore.switchRoles()
    if (typeof res === 'string') {
        Message.success(res)
    }
}

const getImageUrl = (name: string) => {
    return new URL(`/src/assets/images/dashboard/${name}.png`, import.meta.url).href
}
</script>

<script lang="ts">
export default {
    name: 'Dashboard',
};
</script>

<style lang="less" scoped>
.workplace-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: white;
}

.top-banner {
    height: 280px;
    background-image: url('@/assets/images/dashboard/banner-w.png');
    background-size: cover;
    background-position: top center;
    background-repeat: no-repeat;
    position: relative;
    padding: 12px 16px 0;
    color: white;

    >* {
        position: relative;
        z-index: 2;
    }
}

.bottom-content {
    flex: 1;
    background: white;
    padding: 24px;
}

.welcome-header {

    .welcome-info {
        box-sizing: border-box;
        padding: 12px 64px;
        position: absolute;
        top: 80px;

        .welcome-title {
            font-size: 20px;
            line-height: 28px;
            font-weight: 500;
            margin: 0 0 8px 0;
            color: white;
        }

        .date-info {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
    }
}

.top-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    height: 38px;

    .left-nav {
        flex: 1;
        height: 100%;
        padding: 0 32px 0 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(270deg, rgba(46, 199, 255, 0.0001) 0.94%, #0466C4 96.12%);
        border-radius: 4px;
        gap: 20px;

        .nav-tabs {
            display: flex;
            gap: 36px;
            align-items: center;
            font-size: 16px;
            font-weight: 600;

            .nav-tab {
                position: relative;
                padding: 0;
                cursor: pointer;
                transition: all 0.3s;

                span {
                    font-family: 'PingFang SC';
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 22px;
                    color: white;
                }

                &.active {
                    span {
                        color: #88E7FF;
                    }

                    .tab-indicator {
                        position: absolute;
                        bottom: -5px;
                        left: 11px;
                        width: 32px;
                        height: 1px;
                        background: #88E7FF;
                        border-radius: 0;
                    }
                }

                &:not(.active) {
                    span {
                        color: white;
                    }
                }
            }
        }

        :deep(.dashboard-project) {
            height: 26px;
            background: #009DFF32;
            border: none;
            color: #fff;
            border-radius: 17px;
            &.arco-select-view-focus {
              box-shadow: none;  
            }

            .arco-select-view-value {
                min-height: 26px;
            }

            .arco-select-view-suffix {
                color: #fff;
            }

        }
    }

    .right-nav {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 16px;

        .center-selector {
            .center-selector-trigger {
                display: flex;
                align-items: center;
                background: rgba(0, 157, 255, 0.19);
                border: 1px solid white;
                border-radius: 17px;
                padding: 0 2px 0 12px;
                height: 32px;
                width: 159px;
                cursor: pointer;
                gap: 6px;

                .center-icon {
                    color: white;
                    font-size: 16px;
                }

                span {
                    flex: 1;
                    font-weight: 500;
                    font-size: 16px;
                    color: white;
                }

                .dropdown-icon {
                    color: white;
                    font-size: 12px;
                }
            }
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;

            .user-avatar {
                width: 32px;
                height: 32px;
                background: #AEE0FF;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #4A90E2;
                font-size: 18px;
                overflow: hidden;
                border: 2px solid rgba(255, 255, 255, 0.3);

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .user-name {
                font-family: 'PingFang SC';
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                color: white;
            }

            .user-dropdown {
                color: white;
                font-size: 12px;
            }
        }
    }
}

.stats-cards-container {
    position: absolute;
    left: 24px;
    bottom: -36px;
    display: flex;
    align-items: center;
    gap: 24px;

    .stats-card {
        width: 100px;
        height: 90px;
        position: relative;

        .card-background {
            background: linear-gradient(135deg, #FFFFFF 0%, #E5E5F4 100%);
            border-radius: 8px;
            padding: 20px 0 16px;
            box-shadow: 0 4px 32px rgba(44, 51, 49, 0.08);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
            height: 100%;
        }

        .card-icon-wrapper {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .card-text {
            width: 100%;

            .card-title {
                font-size: 14px;
                color: #3A4252;
                margin-bottom: 4px;
                text-align: center;
                line-height: 1.3;
            }

            .card-value {
                font-size: 14px;
                font-weight: 600;
                color: #3A4252;
                text-align: center;
            }
        }
    }
}

.page-container {
    background: #F1F3F3;

    .section {
        background-color: #fff;
        display: grid;
        grid-template-columns: 23% 22% 22% auto;
        column-gap: 16px;
        padding: 0 20px;
        border-bottom: 16px solid #F1F3F3;

        &.top-section {
            padding-top: 32px;
            padding-right: 0;
        }
    }
}

// 响应式设计
@media (max-width: 1440px) {
    .stats-cards-container {
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
    }

    .main-content-grid {
        flex-direction: column;

        .right-content {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
        }
    }

    .left-content .content-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .top-banner {
        min-height: 250px;
        padding: 12px;
    }

    .page-handle {
        right: 12px !important;
        top: -16px !important;

        .page-handle-content {
            padding: 2px !important;
            border-radius: 20px !important;

            .page-handle-one {
                padding: 6px 12px !important;
                font-size: 12px !important;
                border-radius: 18px !important;
            }
        }
    }

    .top-navigation {
        padding: 6px 12px;
        flex-direction: column;
        gap: 12px;
        height: auto;

        .left-nav .nav-tabs {
            gap: 24px;
        }

        .right-nav {
            gap: 12px;

            .user-info .user-name {
                display: none;
            }
        }
    }

    .bottom-content {
        padding: 12px;
    }

    .stats-cards-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;

        .stats-card .card-background {
            height: 100px;
            padding: 18px 12px 12px;
        }

        .stats-card .card-icon-wrapper {
            width: 36px;
            height: 36px;
            font-size: 18px;
            top: -18px;
        }

        .stats-card .card-text {
            margin-top: 18px;

            .card-title {
                font-size: 13px;
            }

            .card-value {
                font-size: 13px;
            }
        }
    }

    .left-content .content-row {
        grid-template-columns: 1fr;
    }

    .main-content-grid .right-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .top-banner {
        min-height: 200px;
        padding: 8px;
    }

    .top-navigation {
        padding: 4px 8px;

        .left-nav .nav-tabs {
            gap: 16px;

            .nav-tab span {
                font-size: 14px;
            }
        }

        .right-nav {
            .center-selector .center-selector-trigger span {
                font-size: 12px;
            }

            .user-info {
                .user-avatar {
                    width: 28px;
                    height: 28px;
                    font-size: 14px;
                }
            }
        }
    }

    .bottom-content {
        padding: 8px;
    }

    .stats-cards-container {
        grid-template-columns: 1fr;
        gap: 8px;

        .stats-card .card-background {
            height: 90px;
            padding: 16px 10px 10px;
        }

        .stats-card .card-icon-wrapper {
            width: 32px;
            height: 32px;
            font-size: 16px;
            top: -16px;
        }

        .stats-card .card-text {
            margin-top: 16px;

            .card-title {
                font-size: 12px;
            }

            .card-value {
                font-size: 12px;
            }
        }
    }
}

// 全局样式（用户下拉菜单）
:global(.arco-dropdown-option) {
    &:hover {
        background-color: rgba(74, 144, 226, 0.1);
    }
}

:global(.arco-divider-horizontal) {
    margin: 4px 0 !important;
}
</style>
