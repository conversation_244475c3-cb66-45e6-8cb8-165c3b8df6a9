# 智能设备选中项传递优化

## 需求描述
参考 `src/views/property/operations/components/ActiveTab.vue` 页面的实现方式，修改 `src/views/smartDevices/components/DoorLockTab.vue` 组件的选中项传递机制，确保选中状态的正确同步。

## 问题分析
原有实现存在以下问题：
1. 直接操作 `rowSelection.selectedRowKeys`，容易造成状态不一致
2. 缺少防循环更新机制，可能导致无限循环
3. 选中状态同步不够稳定

## 解决方案

### 1. 参考实现分析
从 `ActiveTab.vue` 中学习到的最佳实践：
- 使用 `v-model:selectedKeys="localSelectedKeys"` 进行双向绑定
- 维护本地选中状态 `localSelectedKeys`
- 使用双向监听机制防止循环更新
- 通过 JSON.stringify 比较避免不必要的更新

### 2. 核心改进

#### 2.1 表格绑定方式
**修改前：**
```vue
<a-table
  row-key="roomId"
  :row-selection="rowSelection"
  ...
>
```

**修改后：**
```vue
<a-table
  row-key="roomId"
  :row-selection="rowSelection"
  v-model:selectedKeys="localSelectedKeys"
  ...
>
```

#### 2.2 数据结构调整
**修改前：**
```typescript
// 行选择配置
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
  selectedRowKeys: [] as string[]
})
```

**修改后：**
```typescript
// 内部选中的行keys
const localSelectedKeys = ref<string[]>([])

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false
})
```

#### 2.3 监听逻辑重构
**修改前：**
```typescript
// 监听选中行变化
watch(() => props.selectedKeys, (newVal) => {
  rowSelection.selectedRowKeys = newVal
})

// 行选择变化
watch(() => rowSelection.selectedRowKeys, (newVal) => {
  emit('update:selectedKeys', newVal || [])
})
```

**修改后：**
```typescript
// 监听selectedKeys的变化，确保添加防循环检查
watch(() => props.selectedKeys, (newVal) => {
  // 检查是否与当前值相同，如果相同则不更新
  if (JSON.stringify(localSelectedKeys.value) !== JSON.stringify(newVal)) {
    localSelectedKeys.value = [...newVal]
  }
}, { deep: true })

// 监听本地selectedKeys的变化，同步到父组件，添加防循环检查
watch(() => localSelectedKeys.value, (newVal) => {
  // 检查是否与props中的值相同，如果相同则不触发更新
  if (JSON.stringify(props.selectedKeys) !== JSON.stringify(newVal)) {
    emit('update:selectedKeys', newVal)
  }
}, { deep: true })
```

### 3. 修改的文件

#### 3.1 DoorLockTab.vue
- 添加 `localSelectedKeys` 本地状态管理
- 修改表格绑定方式使用 `v-model:selectedKeys`
- 重构监听逻辑，添加防循环检查
- 移除 `rowSelection.selectedRowKeys` 的直接操作

#### 3.2 WaterElectricTab.vue
- 同步应用相同的改进，保持组件行为一致
- 确保两个Tab组件的选中机制完全一致

## 技术实现要点

### 1. 防循环更新机制
```typescript
// 使用 JSON.stringify 进行深度比较
if (JSON.stringify(localSelectedKeys.value) !== JSON.stringify(newVal)) {
  localSelectedKeys.value = [...newVal]
}
```

### 2. 双向数据绑定
- 父组件 → 子组件：通过 props.selectedKeys 传递
- 子组件 → 父组件：通过 emit('update:selectedKeys') 回传
- 本地状态：通过 localSelectedKeys 维护

### 3. 状态同步流程
```
用户操作表格选择
    ↓
v-model:selectedKeys 更新 localSelectedKeys
    ↓
watch(localSelectedKeys) 触发
    ↓
emit('update:selectedKeys') 通知父组件
    ↓
父组件更新 selectedKeys
    ↓
watch(props.selectedKeys) 触发（但因为值相同，不会更新）
```

## 优势

### 1. 稳定性提升
- 防止循环更新导致的性能问题
- 确保选中状态的一致性
- 减少不必要的重新渲染

### 2. 代码质量
- 遵循Vue 3最佳实践
- 代码结构更清晰
- 易于维护和调试

### 3. 用户体验
- 选中状态响应更及时
- 避免选中状态丢失
- 跨Tab切换时状态保持正确

## 测试验证

### 1. 功能测试
- ✅ 单个设备选中/取消选中
- ✅ 全选/取消全选
- ✅ 跨页选择状态保持
- ✅ Tab切换时选中状态清空
- ✅ 批量操作时正确传递选中项

### 2. 性能测试
- ✅ 无循环更新问题
- ✅ 选中状态变化响应及时
- ✅ 大量数据时选择操作流畅

## 兼容性说明
- 完全兼容现有的父组件调用方式
- 不影响现有的批量操作功能
- 保持与原有API接口的兼容性

此次优化确保了智能设备页面的选中项传递机制更加稳定可靠，为后续的批量操作功能提供了坚实的基础。
