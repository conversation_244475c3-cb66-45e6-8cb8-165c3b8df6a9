<template>
    <section>
        <sectionTitle title="优惠条款" />
        <div class="content">
            <a-table :data="sortedFees" :scroll="{ x: 1 }" :bordered="{ cell: true }" :pagination="false">
                <template #columns>
                    <a-table-column title="条款类型" :width="140" align="center">
                        <template #cell="{ record }">
                            <div class="detail-text">{{ record.freeType === 0 ? '装修免租' : record.freeType === 1 ?
                                '经营免租' : '合同免租' }}</div>
                        </template>
                    </a-table-column>
                    <a-table-column title="开始日期" :width="180" align="center">
                        <template #cell="{ record }">
                            <div class="detail-text">{{ dayjs(record.startDate).format('YYYY-MM-DD') || '' }}</div>
                        </template>
                    </a-table-column>
                    <a-table-column title="免租天数" align="center" :width="180">
                        <template #cell="{ record }">
                            <div class="detail-text">{{ (record.freeRentMonth || 0) + '个月 ' + (record.freeRentDay
                                || 0) + '天' }}</div>
                        </template>
                    </a-table-column>
                    <a-table-column title="结束日期" :width="180" align="center">
                        <template #cell="{ record }">
                            <div class="detail-text">{{ dayjs(record.endDate).format('YYYY-MM-DD') || '' }}</div>
                        </template>
                    </a-table-column>
                    <a-table-column title="备注" :width="150" align="center" ellipsis tooltip>
                        <template #cell="{ record }">
                            <div class="detail-text">{{ record.remark || '' }}</div>
                        </template>
                    </a-table-column>
                </template>
            </a-table>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract/index'
import { ContractAddDTO } from '@/api/contract'
import dayjs from 'dayjs'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)

// 添加排序逻辑
const sortedFees = computed(() => {
    if (!contractData.value.fees || contractData.value.fees.length === 0) {
        return []
    }
    
    return [...contractData.value.fees].sort((a, b) => {
        const dateA = dayjs(a.startDate)
        const dateB = dayjs(b.startDate)
        return dateA.isBefore(dateB) ? -1 : dateA.isAfter(dateB) ? 1 : 0
    })
})
</script>