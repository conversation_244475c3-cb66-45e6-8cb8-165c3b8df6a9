<template>
  <a-table
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :bordered="{ cell: true }"
    :scroll="{ x: 1 }"
    row-key="id"
  >
    <template #nameTitle>
      <span style="color: red;">*</span>姓名
    </template>
    
    <template #phoneTitle>
      <span style="color: red;">*</span>电话
    </template>
    
    <template #name="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model.trim="record.name" 
        placeholder="请输入姓名"
        size="small"
      />
      <span v-else>{{ record.name }}</span>
    </template>
    
    <template #phone="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.phone" 
        placeholder="请输入电话"
        size="small"
      />
      <span v-else>{{ record.phone }}</span>
    </template>
    
    <template #relationship="{ record, rowIndex }">
      <a-select 
        v-if="record.isEditing && customerType === 1" 
        v-model="record.relationship" 
        placeholder="请选择关系"
        size="small"
        allow-clear
      >
        <a-option :value="1">夫妻</a-option>
        <a-option :value="2">合伙人</a-option>
        <a-option :value="3">父母</a-option>
        <a-option :value="4">朋友</a-option>
        <a-option :value="5">子女</a-option>
        <a-option :value="6">其他</a-option>
      </a-select>
      <span v-else-if="customerType === 1">{{ getRelationshipLabel(record.relationship) }}</span>
      <span v-else>-</span>
    </template>
    
    <template #gender="{ record, rowIndex }">
      <a-radio-group 
        v-if="record.isEditing" 
        v-model="record.gender" 
        size="small"
      >
        <a-radio :value="1">男</a-radio>
        <a-radio :value="2">女</a-radio>
      </a-radio-group>
      <span v-else>{{ getGenderLabel(record.gender) }}</span>
    </template>
    
    <template #idNumber="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.idNumber" 
        placeholder="请输入证件号码"
        size="small"
      />
      <span v-else>{{ record.idNumber }}</span>
    </template>
    
    <template #position="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.position" 
        placeholder="请输入职务"
        size="small"
      />
      <span v-else>{{ record.position }}</span>
    </template>
    
    <template #isPreferred="{ record, rowIndex }">
      <a-radio 
        v-if="customerType === 2"
        :model-value="record.isPreferred" 
        :disabled="readonly"
        @change="handlePreferredChange(record.id)"
      />
      <span v-else>-</span>
    </template>
    
    <template #remark="{ record, rowIndex }">
      <a-textarea 
        v-if="record.isEditing" 
        v-model="record.remark" 
        placeholder="请输入备注"
        :rows="1"
        size="small"
      />
      <span v-else>{{ record.remark }}</span>
    </template>
    
    <template #operations="{ record, rowIndex }">
      <a-space v-if="!readonly">
        <a-button 
          v-if="record.isEditing" 
          type="text" 
          size="small" 
          @click="saveRow(record, rowIndex)"
        >
          保存
        </a-button>
        <a-button 
          v-else
          type="text" 
          size="small" 
          @click="editRow(record)"
        >
          修改
        </a-button>
        
        <a-button 
          type="text" 
          size="small" 
          status="danger" 
          @click="deleteRow(rowIndex)"
        >
          删除
        </a-button>
      </a-space>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Message } from '@arco-design/web-vue'

interface Contact {
  id: number | string
  name: string
  phone: string
  relationship?: number // 个人客户关系，改为number类型
  position?: string // 企业客户职务
  department?: string // 企业客户部门
  gender: number // 1男 2女 3未知
  idNumber: string
  isPreferred?: boolean // 是否首选联系人
  remark: string
  isEditing?: boolean
}

interface Props {
  modelValue: Contact[]
  readonly?: boolean
  customerType?: number // 1个人 2企业
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  readonly: false,
  customerType: 1
})

const emit = defineEmits(['update:modelValue', 'setPreferred', 'contactDeleted'])

const tableData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const columns = computed(() => {
  const baseColumns: any[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      slotName: 'name',
      width: 100,
      titleSlotName: 'nameTitle',
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '电话',
      dataIndex: 'phone',
      slotName: 'phone',
      width: 120,
      titleSlotName: 'phoneTitle',
      ellipsis: true,
      tooltip: true,
      align: 'center'
    }
  ]
  
  // 个人客户显示关系字段
  if (props.customerType === 1) {
    baseColumns.push({
      title: '关系',
      dataIndex: 'relationship',
      slotName: 'relationship',
      width: 110,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    })
  }
  
  baseColumns.push(
    {
      title: '性别',
      dataIndex: 'gender',
      slotName: 'gender',
      width: 140,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '证件号码',
      dataIndex: 'idNumber',
      slotName: 'idNumber',
      width: 140,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    }
  )
  
  // 企业客户显示职务字段（在证件号码后面）
  if (props.customerType === 2) {
    baseColumns.push({
      title: '职务',
      dataIndex: 'position',
      slotName: 'position',
      width: 120,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    })
  }
  
  // 企业客户显示首选联系人列
  if (props.customerType === 2) {
    baseColumns.push({
      title: '首选联系人',
      dataIndex: 'isPreferred',
      slotName: 'isPreferred',
      width: 120,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    })
  }
  
  baseColumns.push({
    title: '备注',
    dataIndex: 'remark',
    slotName: 'remark',
    width: 140,
    ellipsis: true,
    tooltip: true,
    align: 'center'
  })
  
  // 只有非只读模式才显示操作列
  if (!props.readonly) {
    baseColumns.push({
      title: '操作',
      slotName: 'operations',
      width: 120,
      fixed: 'right',
      align: 'center'
    })
  }
  
  return baseColumns
})

const relationshipOptions = {
  1: '夫妻',
  2: '合伙人',
  3: '父母',
  4: '朋友',
  5: '子女',
  6: '其他'
}

const getRelationshipLabel = (value?: number) => {
  if (!value) return ''
  return relationshipOptions[value as keyof typeof relationshipOptions] || ''
}

const getGenderLabel = (value: number) => {
  const genderMap = { 1: '男', 2: '女', 3: '未知' }
  return genderMap[value as keyof typeof genderMap] || '未知'
}

const handlePreferredChange = (contactId: number | string) => {
  // 设置首选联系人，其他联系人取消首选
  const newData = tableData.value.map(item => ({
    ...item,
    isPreferred: item.id === contactId
  }))
  tableData.value = newData
  emit('setPreferred', contactId)
}

const editRow = (record: Contact) => {
  record.isEditing = true
}

const saveRow = (record: Contact, index: number) => {
  if (!record.name) {
    Message.warning('请输入姓名')
    return
  }
  if (!record.phone) {
    Message.warning('请输入电话')
    return
  }
  
  record.isEditing = false
  Message.success('保存成功')
}

const deleteRow = (index: number) => {
  const newData = [...tableData.value]
  newData.splice(index, 1)
  tableData.value = newData
  Message.success('删除成功')
  emit('contactDeleted', index)
}
</script>

<style scoped lang="less">
:deep(.arco-table-td) {
  padding: 8px !important;
}

:deep(.arco-input),
:deep(.arco-select),
:deep(.arco-textarea) {
  border: none;
  box-shadow: none;
}
</style> 