<template>
    <a-drawer v-model:visible="visible" title="资产详情" class="common-drawer" @cancel="handleCancel">
        <a-form :model="formModel" auto-label-width>
            <section-title title="基本信息" />
            <a-card :bordered="false">
                <a-grid :cols="3" :col-gap="16" :row-gap="0">
                    <a-grid-item>
                        <a-form-item label="房源名称：">
                            <a-input :disabled="true" v-model="formModel.roomName" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="产品类型：">
                            <a-input :disabled="true" :model-value="getProductTypeText(formModel.productType)" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="所属项目：">
                            <a-input :disabled="true" v-model="formModel.projectName" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="地块：">
                            <a-input :disabled="true" v-model="formModel.parcelName" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="楼栋：">
                            <a-input :disabled="true" v-model="formModel.buildingName" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="楼层：">
                            <a-input :disabled="true" v-model="formModel.floorName" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="面积类型：">
                            <a-input :disabled="true" v-model="formModel.areaTypeName" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="建筑面积：">
                            <a-input :disabled="true" v-model="formModel.buildArea">
                                <template #suffix>m²</template>
                            </a-input>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="室内面积：">
                            <a-input :disabled="true" v-model="formModel.innerArea">
                                <template #suffix>m²</template>
                            </a-input>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="是否可售：">
                            <a-input :disabled="true" :model-value="formModel.isSale ? '是' : '否'" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="是否公司自持：">
                            <a-input :disabled="true" :model-value="formModel.isCompanySelf ? '是' : '否'" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="自持物业类型：">
                            <a-input :disabled="true" v-model="formModel.propertyTypeName" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="自持到期时间：">
                            <a-input :disabled="true" v-model="formModel.selfHoldingTime" />
                        </a-form-item>
                    </a-grid-item>
                </a-grid>
            </a-card>
            <section-title title="资产状态" />
            <a-card :bordered="false">
                <a-grid :cols="3" :col-gap="16" :row-gap="0">
                    <a-grid-item>
                        <a-form-item label="资产状态：">
                            <a-input :disabled="true" :model-value="statusMap.get(formModel.status || 0)" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="接收日期：">
                            <a-input :disabled="true" v-model="formModel.receiveDate" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="接收类型：">
                            <a-input :disabled="true" v-model="formModel.receiveTypeName" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="处置日期：">
                            <a-input :disabled="true" v-model="formModel.disposalDate" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="处置方式：">
                            <a-input :disabled="true" v-model="formModel.disposalMethodName" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="最近变更日期：">
                            <a-input :disabled="true" v-model="formModel.changeDate" />
                        </a-form-item>
                    </a-grid-item>
                </a-grid>
            </a-card>
        </a-form>

        <template #footer>
            <a-button @click="handleCancel">关闭</a-button>
        </template>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { getProjectRoomDetail } from '@/api/asset/projectManage'
import { useDict } from '@/utils/dict'

// 字典支持
const { product_type } = useDict('product_type')

// 产品类型转换
const getProductTypeText = (value: string | number) => {
    if (!value) return '-'
    const item = product_type.value.find((item: any) => item.value === String(value))
    return item ? item.label : value
}

const visible = ref(false)
const formModel = ref({
    roomName: '',
    productType: '',
    projectName: '',
    parcelName: '',
    buildingName: '',
    floorName: '',
    areaTypeName: '',
    buildArea: '',
    innerArea: '',
    isSale: '',
    isCompanySelf: '',
    propertyTypeName: '',
    selfHoldingTime: '',
    status: undefined,
    receiveDate: '',
    receiveTypeName: '',
    disposalDate: '',
    disposalMethodName: '',
    changeDate: ''
})

const statusMap = new Map([
    [0, '初始'],
    [1, '接收'],
    [2, '处置'],
])

const open = (record: any) => {
    visible.value = true
    getProjectRoomDetailData(record)
}

const getProjectRoomDetailData = async (record: any) => {
    const res = await getProjectRoomDetail(record.id)
    if (res) {
        formModel.value = res.data
        formModel.value.projectName = record.projectName
    }
}

const handleCancel = () => {
    visible.value = false
}

defineExpose({
    open
})
</script>

<style lang="less" scoped></style>