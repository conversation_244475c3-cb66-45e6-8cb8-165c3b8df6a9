<template>
    <section>
        <sectionTitle title="合同补充信息" />
        <div class="content">
            <a-form :disabled="isDetailMode" :model="contractData" :rules="isDetailMode ? {} : rules" ref="formRef" auto-label-width layout="vertical">
                <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                    <!-- 商铺 -->
                    <template v-if="contractPurposeLabel === '商铺'">
                        <a-grid-item>
                            <a-form-item label="经营业态" field="bizTypeId">
                                    <a-tree-select v-model="contractData.bizTypeId" placeholder="请选择"
                                        :data="business_format" :field-names="{ title: 'label', key: 'value' }"
                                        selectable="leaf" @change="handleSelect">
                                    </a-tree-select>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="承租方品牌">
                                    <a-input v-model="contractData.lesseeBrand" placeholder="请输入" maxlength="20" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="经营品类">
                                    <a-input v-model="contractData.businessCategory" placeholder="请输入" maxlength="50" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="开业日期" field="openDate">
                                    <a-date-picker v-model="contractData.openDate" format="YYYY-MM-DD"
                                        style="width: 100%" placeholder="请选择日期" />
                            </a-form-item>
                        </a-grid-item>
                    </template>

                    <!-- 中央食堂 -->
                    <template v-if="contractPurposeLabel === '中央食堂'">
                        <a-grid-item>
                            <a-form-item label="开业日期" field="openDate">
                                    <a-date-picker v-model="contractData.openDate" format="YYYY-MM-DD"
                                        style="width: 100%" placeholder="请选择日期" />
                            </a-form-item>
                        </a-grid-item>
                    </template>

                    <!-- 厂房 -->
                    <template v-if="contractPurposeLabel === '厂房'">
                        <a-grid-item>
                            <a-form-item label="生产火灾危险性类别" field="fireRiskCategory">
                                    <a-select v-model="contractData.fireRiskCategory" placeholder="请选择">
                                        <a-option v-for="item in fire_level" :key="item.value"
                                            :value="Number(item.value)">{{ item.label }}</a-option>
                                    </a-select>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="喷淋系统" field="sprinklerSystem">
                                    <a-select v-model="contractData.sprinklerSystem" placeholder="请选择">
                                        <a-option v-for="item in spray_system" :key="item.value"
                                            :value="Number(item.value)">{{ item.label }}</a-option>
                                    </a-select>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="厂房从事" field="factoryEngaged">
                                    <a-input v-model="contractData.factoryEngaged" placeholder="请输入" maxlength="50" />
                            </a-form-item>
                        </a-grid-item>
                    </template>

                    <!-- 综合体 -->
                    <template v-if="contractPurposeLabel === '综合体'">
                        <a-grid-item>
                            <a-form-item label="经营业态" field="bizTypeId">
                                    <a-tree-select v-model="contractData.bizTypeId" placeholder="请选择"
                                        :data="business_format" :field-names="{ title: 'label', key: 'value' }"
                                        selectable="leaf" @change="handleSelect">
                                    </a-tree-select>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="承租方品牌" field="lesseeBrand">
                                    <a-input v-model="contractData.lesseeBrand" placeholder="请输入" maxlength="20" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="经营品类">
                                    <a-input v-model="contractData.businessCategory" placeholder="请输入" maxlength="50" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="交接日期" field="handoverDate">
                                    <a-date-picker v-model="contractData.handoverDate" format="YYYY-MM-DD"
                                        style="width: 100%" placeholder="请选择日期" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="开业日期" field="openDate">
                                    <a-date-picker v-model="contractData.openDate" format="YYYY-MM-DD"
                                        style="width: 100%" placeholder="请选择日期" />
                            </a-form-item>
                        </a-grid-item>
                    </template>

                    <!-- 车位 -->
                    <template v-if="contractPurposeLabel === '车位'">
                        <a-grid-item>
                            <a-form-item label="车位类型" field="parkingSpaceType">
                                    <a-select v-model="contractData.parkingSpaceType" placeholder="请选择">
                                        <a-option v-for="item in parking_type" :key="item.value"
                                            :value="Number(item.value)">{{ item.label }}</a-option>
                                    </a-select>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="是否包含车位管理费" field="hasParkingFee">
                                    <a-radio-group v-model="contractData.hasParkingFee">
                                        <a-radio :value="true">是</a-radio>
                                        <a-radio :value="false">否</a-radio>
                                    </a-radio-group>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item v-if="contractData.hasParkingFee">
                            <a-form-item label="车位管理费" field="parkingFeeAmount">
                                    <a-input-number v-model="contractData.parkingFeeAmount" placeholder="请输入" :min="0"
                                        :precision="2">
                                        <template #suffix>元/月</template>
                                    </a-input-number>
                            </a-form-item>
                        </a-grid-item>
                    </template>

                    <!-- 广告位、设备类、其他 -->
                    <template v-if="['广告位', '设备类', '其他'].includes(contractPurposeLabel)">
                        <a-grid-item>
                            <a-form-item label="场地交付日期" field="venueDeliveryDate">
                                <a-date-picker v-model="contractData.venueDeliveryDate" format="YYYY-MM-DD"
                                    style="width: 100%" placeholder="请选择日期" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="租赁场地位置" field="venueLocation">
                                <a-input v-model="contractData.venueLocation" placeholder="请输入" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="每日活动开展时间" field="activityDate">
                                <a-time-picker v-model="contractData.activityDate" type="time-range" format="HH:mm"
                                    style="width: 100%" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="场地用途" field="venuePurpose">
                                <a-input v-model="contractData.venuePurpose" placeholder="请输入" />
                            </a-form-item>
                        </a-grid-item>
                    </template>
                </a-grid>
            </a-form>
        </div>
    </section>
</template>

<script lang="ts" setup>
/**
 * @description: 合同补充信息
 * 字段说明：
 * 商铺：业态、承租方品牌、经营品类、开业日期
 * 厂房：生产火灾危险性类别、喷淋系统、厂房从事
 * 综合体：业态、承租方品牌、经营品类、交接日期、开业日期
 * 车位：车位类型、是否包含车位管理费、车位管理费
 */
import { ref, computed } from 'vue'
import type { FormInstance } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import { getDictLabel } from '@/dict'
import { useDict, useDictTree } from '@/utils/dict'

const contractPurposeLabel = computed(() => {
    return getDictLabel('diversification_purpose', Number(contractData.value.contractPurpose))
})

const { business_format } = useDictTree('business_format')

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType)
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || (editType.value === 'change' && !changeType.value.includes(2)) || editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo')
const formItemMargin = computed(() => isDetailMode.value ? '16px' : '16px')

const { parking_type, fire_level, spray_system } = useDict('parking_type', 'fire_level', 'spray_system')

// 统一表单校验规则
const rules = computed(() => {
    if (isDetailMode.value) return {}
    return {
        bizTypeId: [{ required: true, message: '请输入/请选择业态' }],
        lesseeBrand: [{ required: true, message: '请输入承租方品牌' }],
        businessCategory: [{ required: true, message: '请输入经营品类' }],
        openDate: [{ required: true, message: '请选择开业日期' }],
        fireRiskCategory: [{ required: true, message: '请选择生产火灾危险性类别' }],
        sprinklerSystem: [{ required: true, message: '请选择喷淋系统' }],
        factoryEngaged: [{ required: true, message: '请输入厂房从事内容' }],
        handoverDate: [{ required: true, message: '请选择交接日期' }],
        parkingSpaceType: [{ required: true, message: '请选择车位类型' }],
        hasParkingFee: [{ required: true, message: '请选择是否包含车位管理费' }],
        parkingFeeAmount: [{ required: true, message: '请输入车位管理费' }],
        venueDeliveryDate: [{ required: true, message: '请选择场地交付日期' }],
        venueLocation: [{ required: true, message: '请输入场地交付位置' }],
        venuePurpose: [{ required: true, message: '请输入场地用途' }],
        activityDate: [{ required: true, message: '请选择每日活动开展时间' }],
    }
})

const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            reject()
        } else {
            resolve(true)
        }
    })
}

const handleSelect = (value: string) => {
    // console.log('Selected business format value:', value);

    // 将选中的节点名称（label）赋值给 contractData.bizTypeName
    let selectedNode: { value: string; label: string } | null = null;
    if (Array.isArray(business_format.value)) {
        for (const node of business_format.value) {
            if (node.value === value) {
                selectedNode = node;
                break;
            }
            if (node.children && Array.isArray(node.children)) {
                for (const child of node.children) {
                    if (child.value === value) {
                        selectedNode = child;
                        break;
                    }
                }
            }
        }
    }

    if (selectedNode && selectedNode.label && selectedNode.value) {
        contractData.value.bizTypeName = selectedNode.label;
        contractData.value.bizTypeId = selectedNode.value;
    } else {
        console.warn(`未能找到与值 ${value} 对应的节点`);
    }

    // 额外的日志输出，确认设置的值
    // console.log('Set bizTypeName to:', contractData.value.bizTypeName);
    // console.log('Set bizTypeId to:', contractData.value.bizTypeId);
}

defineExpose({
    validate
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.arco-form-item {
    margin-bottom: v-bind(formItemMargin);
}

.detail-text {
    padding: 4px 0;
    color: #333;
}
</style>