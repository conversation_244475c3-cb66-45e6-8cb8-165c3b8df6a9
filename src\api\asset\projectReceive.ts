import http from '../index';

export interface ProjectVo {
    id: 'string';
    type: 0;
    mdmProjectId: 'string';
    mdmName: 'string';
    mdmSaleName: 'string';
    code: 'string';
    name: 'string';
    provinceCode: 'string';
    provinceName: 'string';
    cityCode: 'string';
    cityName: 'string';
    countryCode: 'string';
    countryName: 'string';
    mdmTypeName: 'string';
    assetType: 0;
    propertyUnit: 'string';
    projectAddress: 'string';
    longitude: 'string';
    latitude: 'string';
    totalSelfArea: 'string';
    createByName: 'string';
    updateByName: 'string';
    isDel: true;
}

// 查询资产接收列表
export function getReceiveList(params: any) {
    return http.post('/business-asset/receive/list', params);
}

// 获取资产接收详细信息
export function getReceiveDetail(id: string) {
    return http.get('/business-asset/receive/detail?id=' + id);
}

// 新增资产接收
export function addReceive(data: any) {
    return http.post('/business-asset/receive', data);
}

// 修改资产接收
export function updateReceive(data: any) {
    return http.put('/business-asset/receive', data);
}

// 删除资产接收
export function deleteReceive(ids: string) {
    return http.delete('/business-asset/receive/delete?ids=' + ids);
}

// 查询房间树形结构
export function getRoomTree(data: any) {
    return http.post('/business-asset/project/room/tree', data);
}

// 查询房间列表
export function getRoomList(params: any) {
    return http.get('/business-asset/project/room/list', params);
}

// 查询项目列表
export function getProjectList(params?: any) {
    return http.get<ProjectVo[]>('/business-asset/project/list', params);
}

// 根据项目ID获取楼栋树结构
export function getBuildingTree(projectId: string, buildingName?: string) {
    return http.get('/business-asset/project/building/tree', {
        projectId,
        buildingName,
    });
}

// 提交资产接收
export function updateStatus(data: any) {
    return http.get('/business-asset/receive/updateStatus?id=' + data);
}
