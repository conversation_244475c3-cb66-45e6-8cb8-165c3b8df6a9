<template>
  <a-tree-select 
    v-model="selectedRoomId" 
    :data="roomTreeData"
    :field-names="{ children: 'children', key: 'id', title: 'name' }" 
    placeholder="请选择房源" 
    allow-clear 
    allow-search
    selectable="leaf" 
    :filter-tree-node="filterTreeNode" 
    :loading="loading" 
    :virtual-list-props="virtualListProps"
    @popup-visible-change="handlePopupVisibleChange"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { getRoomTree, type RoomTreeQueryDTO, type RoomTreeVo } from '@/api/room'
import useProjectStore from '@/store/modules/project'

interface Props {
  modelValue?: string | number | { id: string | number; name: string } | null
  contractType?: string // 合同类型，0-非宿舍,1-宿舍,2-多经,3-日租房
  buildingType?: string // 业态
  buildingId?: string // 楼栋id
  rentStatus?: string // 租控状态 0-可租 1-已租
  roomName?: string // 房源名称
  projectId?: string // 项目ID
  valueType?: 'id' | 'name' | 'object' // 指定返回值类型
  enableVirtualList?: boolean // 是否启用虚拟滚动
  enableCache?: boolean // 是否启用缓存
  pageSize?: number // 分页大小
}

interface TreeNode {
  id: string | number
  name: string
  children?: TreeNode[]
  roomData?: RoomTreeVo
  isLeaf?: boolean
  level?: number
  [key: string]: any
}

const projectStore = useProjectStore()

// 缓存管理
interface CacheItem {
  data: RoomTreeVo[]
  timestamp: number
  params: string
}

// 原生防抖函数实现
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

const emit = defineEmits(['update:modelValue', 'change'])

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  valueType: 'id',
  enableVirtualList: true, // 默认启用虚拟滚动
  enableCache: true, // 默认启用缓存
  pageSize: 50 // 默认分页大小
})

const loading = ref(false)
const roomTreeData = ref<TreeNode[]>([])
const dataCache = new Map<string, CacheItem>()
const CACHE_DURATION = 5 * 60 * 1000 // 缓存5分钟

// 虚拟滚动配置
const virtualListProps = computed(() => {
  if (!props.enableVirtualList) return undefined
  
  return {
    height: 300,
    threshold: 200,
    itemHeight: 32
  }
})

const selectedRoomName = computed(() => {
  return findRoomNodeById(selectedRoomId.value)?.roomData?.roomName || ''
})

// 当前选中的房源ID（用于tree-select组件的v-model）
const selectedRoomId = computed({
  get() {
    if (!props.modelValue) return ''
    
    switch (props.valueType) {
      case 'id':
        return props.modelValue as string | number
      case 'name':
        return findRoomIdByName(props.modelValue as string)
      case 'object':
        return (props.modelValue as { id: string | number; name: string })?.id || ''
      default:
        return props.modelValue as string | number
    }
  },
  set(value: string | number) {
    if (!value) {
      emit('update:modelValue', null)
      emit('change', null)
      return
    }
    
    // 处理房源选择变化
    handleRoomSelection(value)
  }
})

// 生成缓存键
const generateCacheKey = (params: RoomTreeQueryDTO): string => {
  return JSON.stringify(params)
}

// 检查缓存是否有效
const isCacheValid = (cacheItem: CacheItem): boolean => {
  return Date.now() - cacheItem.timestamp < CACHE_DURATION
}

// 从缓存获取数据
const getFromCache = (params: RoomTreeQueryDTO): RoomTreeVo[] | null => {
  if (!props.enableCache) return null
  
  const cacheKey = generateCacheKey(params)
  const cacheItem = dataCache.get(cacheKey)
  
  if (cacheItem && isCacheValid(cacheItem)) {
    console.log('从缓存获取数据:', cacheKey)
    return cacheItem.data
  }
  
  return null
}

// 保存到缓存
const saveToCache = (params: RoomTreeQueryDTO, data: RoomTreeVo[]) => {
  if (!props.enableCache) return
  
  const cacheKey = generateCacheKey(params)
  dataCache.set(cacheKey, {
    data,
    timestamp: Date.now(),
    params: cacheKey
  })
  
  console.log('保存到缓存:', cacheKey)
}

// 清理过期缓存
const cleanExpiredCache = () => {
  const now = Date.now()
  for (const [key, item] of dataCache.entries()) {
    if (now - item.timestamp > CACHE_DURATION) {
      dataCache.delete(key)
    }
  }
}

// 根据房源名称查找房源ID
const findRoomIdByName = (roomName: string): string | number => {
  const findInNodes = (nodes: TreeNode[]): string | number => {
    for (const node of nodes) {
      if (node.roomData && node.roomData.roomName === roomName) {
        return node.id
      }
      if (node.children && node.children.length > 0) {
        const found = findInNodes(node.children)
        if (found) return found
      }
    }
    return ''
  }
  return findInNodes(roomTreeData.value)
}

// 根据房源ID查找房源节点
const findRoomNodeById = (roomId: string | number): TreeNode | null => {
  const findInNodes = (nodes: TreeNode[]): TreeNode | null => {
    for (const node of nodes) {
      if (node.id === roomId && node.roomData) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findInNodes(node.children)
        if (found) return found
      }
    }
    return null
  }
  return findInNodes(roomTreeData.value)
}

// 去重函数：根据roomId去除重复的房源数据
const deduplicateRoomData = (roomTreeList: RoomTreeVo[]): RoomTreeVo[] => {
  const seen = new Set<string>()
  const result: RoomTreeVo[] = []
  
  for (const item of roomTreeList) {
    if (item.level === 4 && item.roomId) {
      // 房间节点：根据roomId去重
      if (!seen.has(item.roomId)) {
        seen.add(item.roomId)
        result.push(item)
      }
    } else {
      // 非房间节点：递归处理子节点
      const deduplicatedItem = {
        ...item,
        children: item.children ? deduplicateRoomData(item.children) : undefined
      }
      result.push(deduplicatedItem)
    }
  }
  
  return result
}

// 将RoomTreeVo转换为TreeNode结构
const convertToTreeData = (roomTreeList: RoomTreeVo[]): TreeNode[] => {
  // 先去重，再转换
  const deduplicatedList = deduplicateRoomData(roomTreeList)
  
  return deduplicatedList.map(item => {
    const treeNode: TreeNode = {
      // 如果id为null且是房间节点，使用roomId作为id
      id: item.id || (item.level === 4 ? item.roomId : '') || '',
      // 如果name为null且是房间节点，使用roomName作为name
      name: item.name || (item.level === 4 ? item.roomName : '') || '',
      level: item.level,
      isLeaf: item.level === 4, // level 4 表示房间，是叶子节点
      children: item.children ? convertToTreeData(item.children) : undefined
    }
    
    // 如果是房间节点（level=4），保存房源数据
    if (item.level === 4) {
      treeNode.roomData = item
    }
    
    return treeNode
  })
}

// 防抖的数据加载函数
const debouncedLoadData = debounce(async () => {
  await getRoomTreeData()
}, 300)

// 获取房源树数据
const getRoomTreeData = async () => {
  loading.value = true
  
  try {
    const params: RoomTreeQueryDTO = {
      pageNum: 1,
      pageSize: 1000, // 设置一个较大的值以获取所有数据
      contractType: props.contractType,
      buildingType: props.buildingType,
      buildingId: props.buildingId,
      rentStatus: props.rentStatus,
      roomName: props.roomName,
      projectId: props.projectId || projectStore.rentProjectId
    }
    
    // 过滤掉undefined的参数
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== undefined)
    ) as RoomTreeQueryDTO
    
    // 尝试从缓存获取数据
    let data = getFromCache(filteredParams)
    
    if (!data) {
      console.log('从API获取数据')
      const response = await getRoomTree(filteredParams)
      data = response.data || []
      
      // 保存到缓存
      saveToCache(filteredParams, data)
    }
    
    // 转换数据格式
    roomTreeData.value = convertToTreeData(data)
    
    // 数据加载完成后，设置选中状态
    await nextTick()
    selectedRoomId.value = selectedRoomId.value
    
    // 如果是object类型且当前值的name显示的是id，尝试更新为正确的房源名称
    if (props.valueType === 'object' && props.modelValue) {
      const currentValue = props.modelValue as { id: string | number; name: string }
      if (currentValue && currentValue.name === String(currentValue.id)) {
        const roomNode = findRoomNodeById(currentValue.id)
        if (roomNode?.roomData?.roomName) {
          const updatedValue = {
            id: currentValue.id,
            name: roomNode.roomData.roomName
          }
          emit('update:modelValue', updatedValue)
          emit('change', updatedValue, roomNode.roomData)
        }
      }
    }
    
    // 清理过期缓存
    cleanExpiredCache()
    
  } catch (error) {
    console.error('获取房源树数据失败:', error)
    roomTreeData.value = []
  } finally {
    loading.value = false
  }
}

// 处理房源选择变化
const handleRoomSelection = async (value: string | number) => {
  // 递归查找选中的房源节点
  const findRoomNode = (nodes: TreeNode[], targetId: string | number): TreeNode | null => {
    for (const node of nodes) {
      if (node.id === targetId) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findRoomNode(node.children, targetId)
        if (found) return found
      }
    }
    return null
  }

  let selectedRoom = findRoomNode(roomTreeData.value, value)
  
  // 如果在当前数据中找不到，尝试加载完整数据
  if (!selectedRoom) {
    try {
      // 临时加载完整数据来获取房源信息
      const params: RoomTreeQueryDTO = {
        pageNum: 1,
        pageSize: 1000, // 设置一个较大的值以获取所有数据
        contractType: props.contractType,
        buildingType: props.buildingType,
        buildingId: props.buildingId,
        rentStatus: props.rentStatus,
        roomName: props.roomName,
        projectId: props.projectId || projectStore.rentProjectId
      }
      
      const filteredParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== undefined)
      ) as RoomTreeQueryDTO
      
      const response = await getRoomTree(filteredParams)
      const fullData = response.data || []
      const fullTreeData = convertToTreeData(fullData)
      
      selectedRoom = findRoomNode(fullTreeData, value)
    } catch (error) {
      console.error('获取房源详情失败:', error)
    }
  }
  
  // 根据valueType返回不同格式的值
  let emitValue: string | number | { id: string | number; name: string } | null = null
  
  if (value && selectedRoom?.roomData) {
    switch (props.valueType) {
      case 'id':
        emitValue = value
        break
      case 'name':
        emitValue = selectedRoom.roomData.roomName || ''
        break
      case 'object':
        emitValue = {
          id: value,
          name: selectedRoom.roomData.roomName || ''
        }
        break
      default:
        emitValue = value
    }
  } else {
    // 如果找不到房源数据，根据类型返回默认值
    switch (props.valueType) {
      case 'id':
        emitValue = value
        break
      case 'name':
        emitValue = String(value) // 临时显示ID
        break
      case 'object':
        emitValue = {
          id: value,
          name: String(value) // 临时显示ID，等数据加载后会更新
        }
        break
      default:
        emitValue = value
    }
  }
  
  emit('update:modelValue', emitValue)
  emit('change', emitValue, selectedRoom?.roomData)
}

// 处理下拉框显示/隐藏
const handlePopupVisibleChange = (visible: boolean) => {
  if (visible && roomTreeData.value.length === 0) {
    // 首次打开时才加载数据
    getRoomTreeData()
  }
}

// 树节点过滤（优化版）
const filterTreeNode = (value: string, node: any) => {
  if (!value) return true
  
  // 递归检查节点及其子节点是否有匹配
  const checkNodeMatch = (currentNode: any, searchValue: string): boolean => {
    // 如果是房间节点（叶子节点），直接检查匹配
    if (currentNode.isLeaf && currentNode.roomData) {
      return currentNode.name?.toLowerCase().includes(searchValue.toLowerCase()) ||
             currentNode.roomData.roomName?.toLowerCase().includes(searchValue.toLowerCase())
    }
    
    // 如果是非叶子节点（项目、地块、楼栋），检查自身名称匹配
    const selfMatch = currentNode.name?.toLowerCase().includes(searchValue.toLowerCase())
    
    // 检查是否有子节点匹配
    let hasMatchingChild = false
    if (currentNode.children && currentNode.children.length > 0) {
      hasMatchingChild = currentNode.children.some((child: any) => checkNodeMatch(child, searchValue))
    }
    
    // 父节点显示条件：自身匹配 或 有匹配的子节点
    return selfMatch || hasMatchingChild
  }
  
  return checkNodeMatch(node, value)
}

// 清理缓存的方法
const clearCache = () => {
  dataCache.clear()
  console.log('缓存已清理')
}

// 组件挂载时不立即加载数据，等待用户打开下拉框
onMounted(() => {
  // 如果有预设值，则立即加载数据
  if (props.modelValue) {
    getRoomTreeData()
  }
})

// 监听外部传入的值变化
watch(() => props.modelValue, (newValue) => {
  const newSelectedId = selectedRoomId.value
  if (newSelectedId !== selectedRoomId.value) {
    selectedRoomId.value = newSelectedId
  }
}, {
  immediate: true,
  deep: true
})

// 监听选中值变化，确保显示正确
watch(() => selectedRoomId.value, async (newId) => {
  if (newId && props.valueType === 'object' && props.modelValue) {
    const currentValue = props.modelValue as { id: string | number; name: string }
    
    // 如果当前显示的name就是id，说明需要更新为正确的房源名称
    if (currentValue.name === String(currentValue.id)) {
      // 尝试从树数据中查找正确的房源名称
      const roomNode = findRoomNodeById(newId)
      if (roomNode?.roomData?.roomName) {
        const updatedValue = {
          id: currentValue.id,
          name: roomNode.roomData.roomName
        }
        emit('update:modelValue', updatedValue)
        emit('change', updatedValue, roomNode.roomData)
      }
    }
  }
})

// 监听查询参数变化，使用防抖重新加载数据
watch(() => [
  props.contractType,
  props.buildingType,
  props.buildingId,
  props.rentStatus,
  props.roomName,
  props.projectId
], () => {
  if (roomTreeData.value.length > 0) {
    // 只有在已经加载过数据的情况下才重新加载
    debouncedLoadData()
  }
}, {
  deep: true
})

// 监听valueType变化，重新设置选中值
watch(() => props.valueType, () => {
  selectedRoomId.value = selectedRoomId.value
})

// 暴露清理缓存方法
defineExpose({
  clearCache,
  refreshData: getRoomTreeData
})
</script>

<style scoped lang="less">
// 组件样式可以根据需要添加
div.arco-typography, p.arco-typography {
margin-bottom: 0 !important;
}
</style> 