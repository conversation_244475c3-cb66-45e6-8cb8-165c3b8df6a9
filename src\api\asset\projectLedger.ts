import http from '../index'

// 资产台账列表查询参数
export interface LedgerListDto {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  projectId?: string
  projectIdList?: string[]
  buildingName?: string
  roomName?: string
  receiveStartDate?: string
  receiveEndDate?: string
}

// 历史资产台账列表查询参数
export interface LedgerHistoryListDto {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  projectId?: string
  buildingName?: string
  roomName?: string
  disposalStartDate?: string
  disposalEndDate?: string
}

// 面积变动查询参数
export interface LedgerAreaChangeDto {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  projectId?: string
  buildingName?: string
  roomName?: string
}

// 资产台账信息
export interface LedgerVo {
  roomId?: string
  roomName?: string
  projectId?: string
  projectName?: string
  parcelId?: string
  parcelName?: string
  buildingId?: string
  buildingName?: string
  productType?: string
  areaType?: string
  areaTypeName?: string
  buildArea?: number
  innerArea?: number
  propertyType?: string
  propertyTypeName?: string
  receiveDate?: string
  lastChangeDate?: string
}

// 历史资产台账信息
export interface LedgerHistoryVo {
  roomId?: string
  roomName?: string
  projectId?: string
  projectName?: string
  parcelId?: string
  parcelName?: string
  buildingId?: string
  buildingName?: string
  productType?: string
  areaType?: string
  areaTypeName?: string
  buildArea?: number
  innerArea?: number
  propertyType?: string
  propertyTypeName?: string
  disposalDate?: string
  disposalMethod?: string
}

// 面积变动房源信息
export interface AssetChangeRoomVo {
  id?: string
  parcelId?: string
  parcelName?: string
  buildingId?: string
  buildingName?: string
  roomName?: string
  productType?: string
  areaType?: string
  areaTypeName?: string
  buildArea?: number
  innerArea?: number
  mdmAreaTypeName?: string
  mdmBuildingArea?: number
  mdmInsideArea?: number
  buildingAreaDiff?: number
}

/**
 * 已接收资产列表查询
 * @param data 查询参数
 * @returns Promise<LedgerVo[]>
 */
export function getLedgerList(data: LedgerListDto) {
  return http.post<LedgerVo[]>(`/business-asset/ledger/list`, data)
}

/**
 * 历史资产列表查询
 * @param data 查询参数
 * @returns Promise<LedgerHistoryVo[]>
 */
export function getLedgerHistoryList(data: LedgerHistoryListDto) {
  return http.post<LedgerHistoryVo[]>(`/business-asset/ledger/historyList`, data)
}

/**
 * 面积变动查询
 * @param data 查询参数
 * @returns Promise<AssetChangeRoomVo[]>
 */
export function getLedgerAreaChange(data: LedgerAreaChangeDto) {
  return http.get<AssetChangeRoomVo[]>(`/business-asset/change/room/list`, data)
}

/**
 * 导出已接收资产列表
 * @param params 查询参数
 * @returns Promise<any>
 */
export function exportLedgerList(params: LedgerListDto) {
  return http.download(`/business-asset/ledger/export`, params)
}

/**
 * 导出历史资产列表
 * @param params 查询参数
 * @returns Promise<any>
 */
export function exportLedgerHistoryList(params: LedgerHistoryListDto) {
  return http.download(`/business-asset/ledger/historyExport`, {}, { params })
}
