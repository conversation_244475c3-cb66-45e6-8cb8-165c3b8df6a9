<template>
	<a-drawer v-model:visible="visible" title="营收详情" :width="800" :footer="false" @cancel="handleCancel">
		<a-descriptions :column="2" bordered size="large" :label-style="{ fontWeight: '500', width: '120px' }">
			<!-- 基本信息 -->
			<a-descriptions-item label="项目名称">
				{{ detailData.projectName || '-' }}
			</a-descriptions-item>
			<a-descriptions-item label="合同编号">
				{{ detailData.contractNo || '-' }}
			</a-descriptions-item>
			<a-descriptions-item label="承租人">
				{{ detailData.customerName || '-' }}
			</a-descriptions-item>
			<a-descriptions-item label="租赁单元">
				{{ detailData.roomName || '-' }}
			</a-descriptions-item>
			<a-descriptions-item label="营收月">
				{{ detailData.revenueMonth || '-' }}
			</a-descriptions-item>
			<a-descriptions-item label="营收金额">
				<span style="color: #165DFF; font-weight: 500; font-size: 16px;">
					¥{{ formatAmount(detailData.revenueAmount) }}
				</span>
			</a-descriptions-item>
			<a-descriptions-item label="提报日期">
				{{ detailData.reportDate || '-' }}
			</a-descriptions-item>
			<a-descriptions-item label="审核状态">
				<a-tag :color="detailData.isConfirm ? 'green' : 'orange'">
					{{ detailData.isConfirm ? '已审核' : '待审核' }}
				</a-tag>
			</a-descriptions-item>

			<!-- 审核信息 -->
			<template v-if="detailData.isConfirm">
				<a-descriptions-item label="审核人">
					{{ detailData.confirmByName || '-' }}
				</a-descriptions-item>
				<a-descriptions-item label="审核时间">
					{{ detailData.confirmTime || '-' }}
				</a-descriptions-item>
			</template>

			<!-- 附件信息 -->
			<a-descriptions-item label="附件" :span="2">
				<div v-if="detailData.attachment">
					<a-button type="text" @click="handleViewAttachment" style="padding: 0;">
						<template #icon>
							<icon-attachment />
						</template>
						查看附件
					</a-button>
				</div>
				<span v-else>-</span>
			</a-descriptions-item>

			<!-- 创建信息 -->
			<a-descriptions-item label="创建人">
				{{ detailData.createByName || '-' }}
			</a-descriptions-item>
			<a-descriptions-item label="创建时间">
				{{ detailData.createTime || '-' }}
			</a-descriptions-item>

			<!-- 更新信息 -->
			<a-descriptions-item label="更新人">
				{{ detailData.updateByName || '-' }}
			</a-descriptions-item>
			<a-descriptions-item label="更新时间">
				{{ detailData.updateTime || '-' }}
			</a-descriptions-item>
		</a-descriptions>

		<!-- 操作区域 -->
		<div class="detail-actions">
			<a-space>
				<a-button v-if="!detailData.isConfirm" type="primary" @click="handleEdit">
					编辑
				</a-button>
				<a-button v-if="!detailData.isConfirm" type="outline" @click="handleApprove">
					审核
				</a-button>
				<a-button v-if="detailData.isConfirm" type="outline" @click="handleCancelApprove">
					取消审核
				</a-button>
				<a-button @click="handleCancel">
					关闭
				</a-button>
			</a-space>
		</div>
	</a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
	getRevenueDetail,
	cancelApproveRevenue,
	type ContractRevenueVO
} from '@/api/revenue'

// 定义emit
const emit = defineEmits<{
	edit: [record: ContractRevenueVO]
	approve: [record: ContractRevenueVO]
	success: []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const detailData = reactive<ContractRevenueVO>({})

// 格式化金额的工具函数
const formatAmount = (amount: number | undefined) => {
	if (!amount) return '0.00'
	return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 方法定义
const open = async (record: ContractRevenueVO) => {
	visible.value = true

	// 如果有ID，重新获取详细数据
	if (record.id) {
		try {
			loading.value = true
			const response = await getRevenueDetail(record.id)
			if (response.data) {
				let data = {
					...response.data.revenue,
					contract: response.data.contract,
					customer: response.data.customer
				}
				Object.assign(detailData, data)
			}
		} catch (error) {
			Message.error('获取详情失败')
			Object.assign(detailData, record)
		} finally {
			loading.value = false
		}
	} else {
		Object.assign(detailData, record)
	}
}

const handleCancel = () => {
	visible.value = false
	Object.assign(detailData, {})
}

const handleEdit = () => {
	emit('edit', detailData as ContractRevenueVO)
	visible.value = false
}

const handleApprove = () => {
	emit('approve', detailData as ContractRevenueVO)
	visible.value = false
}

const handleCancelApprove = () => {
	Modal.confirm({
		title: '确认取消审核',
		content: '确定要取消审核该营收记录吗？',
		onOk: async () => {
			try {
				await cancelApproveRevenue(detailData.id!)
				Message.success('取消审核成功')
				emit('success')
				visible.value = false
			} catch (error) {
				Message.error('取消审核失败')
			}
		}
	})
}

const handleViewAttachment = () => {
	if (detailData.attachment) {
		window.open(detailData.attachment, '_blank')
	}
}

// 暴露方法给父组件
defineExpose({
	open
})
</script>

<style scoped>
.detail-actions {
	margin-top: 24px;
	text-align: center;
	padding-top: 16px;
	border-top: 1px solid var(--color-border-2);
}

:deep(.arco-descriptions-item-label) {
	background-color: var(--color-fill-1);
}

:deep(.arco-descriptions-item-value) {
	word-break: break-all;
}
</style>