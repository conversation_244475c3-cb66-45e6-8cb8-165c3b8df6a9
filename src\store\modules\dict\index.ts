import { defineStore } from 'pinia';

interface DictItem {
    key: string;
    value: any;
}

interface DictState {
    dict: DictItem[];
}

// 修改为默认导出
export default defineStore('dict', {
    state: (): DictState => ({
        dict: [],
    }),

    getters: {
        getDictList: (state: DictState): DictItem[] => {
            return state.dict;
        },
    },

    actions: {
        // 获取字典
        getDict(_key: string) {
            if (_key == null && _key == '') {
                return null;
            }
            try {
                for (let i = 0; i < this.dict.length; i++) {
                    if (this.dict[i].key == _key) {
                        return this.dict[i].value;
                    }
                }
            } catch (e) {
                return null;
            }
        },
        setDict(key: string, value: any) {
            if (key !== null && key !== '') {
                this.dict.push({
                    key: key,
                    value: value,
                });
            }
        },

        removeDict(key: string) {
            try {
                const index = this.dict.findIndex((item) => item.key === key);
                if (index !== -1) {
                    this.dict.splice(index, 1);
                    return true;
                }
                return false;
            } catch (e) {
                return false;
            }
        },

        cleanDict() {
            this.dict = [];
        },
    },
});
