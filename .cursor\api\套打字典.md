---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁后台/打印模版字典

<a id="opIdedit_1"></a>

## PUT 修改打印模版字典

PUT /template/dict

修改打印模版字典

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "parentId": "string",
  "name": "string",
  "type": 0,
  "code": "string",
  "dbField": "string",
  "sqlText": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PrintTemplateDictAddDTO](#schemaprinttemplatedictadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_1"></a>

## POST 新增打印模版字典

POST /template/dict

新增打印模版字典

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "parentId": "string",
  "name": "string",
  "type": 0,
  "code": "string",
  "dbField": "string",
  "sqlText": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PrintTemplateDictAddDTO](#schemaprinttemplatedictadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_1"></a>

## POST 导出询打印模版字典列表

POST /template/dict/export

导出询打印模版字典列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|parentId|query|string| 否 | 父字典id|父字典id|
|name|query|string| 否 | 名称|名称|
|type|query|integer(int32)| 否 | 字段类型（0字段 1表格）|字段类型（0字段 1表格）|
|code|query|string| 否 | 模板字段code|模板字段code|
|dbField|query|string| 否 | 数据库字段|数据库字段|
|sqlText|query|string| 否 | sql语句|sql语句|
|remark|query|string| 否 | 字典解释说明|字典解释说明|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdlist_4"></a>

## GET 查询打印模版字典列表

GET /template/dict/list

查询打印模版字典列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|parentId|query|string| 否 | 父字典id|父字典id|
|name|query|string| 否 | 名称|名称|
|type|query|integer(int32)| 否 | 字段类型（0字段 1表格）|字段类型（0字段 1表格）|
|code|query|string| 否 | 模板字段code|模板字段code|
|dbField|query|string| 否 | 数据库字段|数据库字段|
|sqlText|query|string| 否 | sql语句|sql语句|
|remark|query|string| 否 | 字典解释说明|字典解释说明|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgetInfo"></a>

## GET 获取打印模版字典详细信息

GET /template/dict/detail

获取打印模版字典详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|打印模版字典ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove"></a>

## DELETE 删除打印模版字典

DELETE /template/dict/delete

删除打印模版字典

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|打印模版字典ID列表|query|array[string]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_PrintTemplateDictAddDTO">PrintTemplateDictAddDTO</h2>

<a id="schemaprinttemplatedictadddto"></a>
<a id="schema_PrintTemplateDictAddDTO"></a>
<a id="tocSprinttemplatedictadddto"></a>
<a id="tocsprinttemplatedictadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "parentId": "string",
  "name": "string",
  "type": 0,
  "code": "string",
  "dbField": "string",
  "sqlText": "string",
  "remark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|parentId|string|false|none|父字典id|父字典id|
|name|string|false|none|名称|名称|
|type|integer(int32)|false|none|字段类型（0字段 1表格）|字段类型（0字段 1表格）|
|code|string|false|none|模板字段code|模板字段code|
|dbField|string|false|none|数据库字段|数据库字段|
|sqlText|string|false|none|sql语句|sql语句|
|remark|string|false|none|字典解释说明|字典解释说明|

