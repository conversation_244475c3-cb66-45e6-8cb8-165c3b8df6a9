<template>
    <div class="flow-usage-record">
        <a-tabs v-model:active-key="activeTab" type="rounded">
            <!-- 使用记录 -->
            <a-tab-pane key="usage" title="使用记录">
                <div class="tab-header">
                    <span class="tab-tip">收入流水展示</span>
                </div>
                <a-table 
                    :columns="usageColumns" 
                    :data="usageData" 
                    :pagination="false" 
                    :bordered="true" 
                    size="small"
                    :scroll="{ x: 1 }"
                >
                    <template #accountType="{ record }">
                        <span>{{ getAccountTypeText(record.accountType) }}</span>
                    </template>
                    <template #orderType="{ record }">
                        <span>{{ getOrderTypeText(record.orderType) }}</span>
                    </template>
                    <template #recordAmount="{ record }">
                        <span :class="{ 'negative-amount': record.recordAmount < 0 }">
                            {{ formatAmount(record.recordAmount) }}
                        </span>
                    </template>
                    <template #confirmStatus="{ record }">
                        <a-tag :color="getConfirmStatusColor(record.confirmStatus)">
                            {{ getConfirmStatusText(record.confirmStatus) }}
                        </a-tag>
                    </template>
                    <template #operation="{ record }">
                        <a-space>
                            <a-button type="text" size="mini" @click="handleTakeRecord(record)">
                                取消记账
                            </a-button>
                            <a-button type="text" size="mini" @click="handleConfirm(record)">
                                确认
                            </a-button>
                        </a-space>
                    </template>
                </a-table>
            </a-tab-pane>

            <!-- 退款记录 -->
            <a-tab-pane key="refund" title="退款记录">
                <div class="tab-header">
                    <span class="tab-tip">支出类流水或者流水退款后展示</span>
                </div>
                <a-table 
                    :columns="refundColumns" 
                    :data="refundData" 
                    :pagination="false" 
                    :bordered="true" 
                    size="small"
                    :scroll="{ x: 1 }"
                >
                    <template #recordAmount="{ record }">
                        <span>{{ formatAmount(record.recordAmount) }}</span>
                    </template>
                    <template #confirmStatus="{ record }">
                        <a-tag :color="getConfirmStatusColor(record.confirmStatus)">
                            {{ getConfirmStatusText(record.confirmStatus) }}
                        </a-tag>
                    </template>
                    <template #operation="{ record }">
                        <a-button type="text" size="mini" @click="handleTakeRecord(record)">
                            取消记账
                        </a-button>
                    </template>
                </a-table>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Message } from '@arco-design/web-vue'

export interface UsageRecord {
    id: string
    projectName: string
    accountType: string
    orderNo: string
    roomUnit: string
    customerName: string
    orderType: string
    orderPeriod: string
    type: string
    recordAmount: number
    recordTime: string
    recorder: string
    confirmStatus: string
    confirmer: string
    confirmTime: string
}

export interface RefundRecord {
    id: string
    refundType: string
    refundNo: string
    refundTarget: string
    refundApplyTime: string
    refundApplicant: string
    auditPassTime: string
    type: string
    recordAmount: number
    recordTime: string
    recorder: string
    confirmStatus: string
    confirmer: string
    confirmTime: string
}

const props = defineProps<{
    flowId?: string
    usageData?: UsageRecord[]
    refundData?: RefundRecord[]
}>()

const emit = defineEmits(['takeRecord', 'confirm'])

// 当前激活的标签页
const activeTab = ref('usage')

// 使用记录数据
const usageData = ref<UsageRecord[]>(props.usageData || [])

// 退款记录数据
const refundData = ref<RefundRecord[]>(props.refundData || [])

// 使用记录表格列
const usageColumns = [
    { title: '项目', dataIndex: 'projectName', width: 120, align: 'center',ellipsis: true, tooltip: true},
    { title: '单据类型', dataIndex: 'accountType', slotName: 'accountType', width: 100, align: 'center', ellipsis: true, tooltip: true},
    { title: '单据编号', dataIndex: 'orderNo', width: 160, align: 'center', ellipsis: true, tooltip: true},
    { title: '租赁单元', dataIndex: 'roomUnit', width: 120, align: 'center', ellipsis: true, tooltip: true},
    { title: '客户名称', dataIndex: 'customerName', width: 120, align: 'center', ellipsis: true, tooltip: true},
    { title: '账单类型', dataIndex: 'orderType', slotName: 'orderType', width: 100, align: 'center', ellipsis: true, tooltip: true},
    { title: '账单起始周期', dataIndex: 'orderPeriod', width: 140, align: 'center', ellipsis: true, tooltip: true},
    { title: '类型', dataIndex: 'type', width: 120, align: 'center', ellipsis: true, tooltip: true},
    { title: '记账金额', dataIndex: 'recordAmount', slotName: 'recordAmount', width: 120, align: 'center', ellipsis: true, tooltip: true},
    { title: '记账时间', dataIndex: 'recordTime', width: 140, align: 'center', ellipsis: true, tooltip: true},
    { title: '记账人', dataIndex: 'recorder', width: 100, align: 'center', ellipsis: true, tooltip: true},
    { title: '确认状态', dataIndex: 'confirmStatus', slotName: 'confirmStatus', width: 100, align: 'center', ellipsis: true, tooltip: true},
    { title: '确认人', dataIndex: 'confirmer', width: 100, align: 'center', ellipsis: true, tooltip: true},
    { title: '确认时间', dataIndex: 'confirmTime', width: 140, align: 'center', ellipsis: true, tooltip: true},
    { title: '操作', slotName: 'operation', width: 160, align: 'center', fixed: 'right' }
]

// 退款记录表格列
const refundColumns = [
    { title: '退款类型', dataIndex: 'refundType', width: 100, align: 'center', ellipsis: true, tooltip: true},
    { title: '退款单', dataIndex: 'refundNo', width: 160, align: 'center', ellipsis: true, tooltip: true},
    { title: '退款对象', dataIndex: 'refundTarget', width: 120, align: 'center', ellipsis: true, tooltip: true},
    { title: '退款申请时间', dataIndex: 'refundApplyTime', width: 140, align: 'center', ellipsis: true, tooltip: true},
    { title: '退款申请人', dataIndex: 'refundApplicant', width: 120, align: 'center', ellipsis: true, tooltip: true},
    { title: '审批通过时间', dataIndex: 'auditPassTime', width: 140, align: 'center', ellipsis: true, tooltip: true},
    { title: '类型', dataIndex: 'type', width: 120, align: 'center', ellipsis: true, tooltip: true},
    { title: '记账金额', dataIndex: 'recordAmount', slotName: 'recordAmount', width: 120, align: 'center', ellipsis: true, tooltip: true},
    { title: '记账时间', dataIndex: 'recordTime', width: 140, align: 'center', ellipsis: true, tooltip: true},
    { title: '记账人', dataIndex: 'recorder', width: 100, align: 'center', ellipsis: true, tooltip: true},
    { title: '确认状态', dataIndex: 'confirmStatus', slotName: 'confirmStatus', width: 100, align: 'center', ellipsis: true, tooltip: true},
    { title: '确认人', dataIndex: 'confirmer', width: 100, align: 'center', ellipsis: true, tooltip: true},
    { title: '确认时间', dataIndex: 'confirmTime', width: 140, align: 'center', ellipsis: true, tooltip: true},
    { title: '操作', slotName: 'operation', width: 160, align: 'center', fixed: 'right' }
]

// 监听props变化
watch(() => props.usageData, (newData) => {
    if (newData) {
        usageData.value = newData
    }
}, { deep: true })

watch(() => props.refundData, (newData) => {
    if (newData) {
        refundData.value = newData
    }
}, { deep: true })

// 获取单据类型文本
const getAccountTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
        'deposit': '定金',
        'contract': '合同'
    }
    return typeMap[type] || type
}

// 获取账单类型文本
const getOrderTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
        'deposit': '定金账单',
        'rent': '租金账单',
        'deposit_refund': '保证金'
    }
    return typeMap[type] || type
}

// 获取确认状态文本
const getConfirmStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
        'manual_confirm': '手动确认',
        'auto_confirm': '自动确认',
        'system_auto': '系统自动'
    }
    return statusMap[status] || status
}

// 获取确认状态颜色
const getConfirmStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'manual_confirm': 'blue',
        'auto_confirm': 'green', 
        'system_auto': 'cyan'
    }
    return colorMap[status] || 'gray'
}

// 格式化金额
const formatAmount = (amount: number) => {
    if (amount === undefined || amount === null) return '0.00'
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 处理取消记账
const handleTakeRecord = (record: UsageRecord | RefundRecord) => {
    emit('takeRecord', record)
}

// 处理确认
const handleConfirm = (record: UsageRecord) => {
    emit('confirm', record)
}

// 设置数据
const setUsageData = (data: UsageRecord[]) => {
    usageData.value = data
}

const setRefundData = (data: RefundRecord[]) => {
    refundData.value = data
}

// 暴露方法
defineExpose({
    setUsageData,
    setRefundData
})
</script>

<style scoped lang="less">
.flow-usage-record {
    .tab-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .tab-tip {
            background: #ffeaa7;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: #2d3436;
        }
    }

    :deep(.arco-table) {
        font-size: 12px;
        
        .arco-table-cell {
            padding: 8px;
        }
    }

    .negative-amount {
        color: #f53f3f;
    }

    :deep(.arco-tabs-nav-type-rounded) {
        .arco-tabs-nav-tab {
            border-radius: 6px 6px 0 0;
        }
    }
}
</style> 