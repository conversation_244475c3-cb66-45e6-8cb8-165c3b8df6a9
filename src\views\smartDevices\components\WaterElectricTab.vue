<template>
  <div>
    <!-- 表格 -->
    <a-table
      row-key="id"
      :loading="loading"
      :pagination="pagination"
      :data="tableData"
      :columns="columns"
      :row-selection="rowSelection"
      :bordered="{ cell: true }"
      :scroll="{ x: 1 }"
      :stripe="true"
      v-model:selectedKeys="localSelectedKeys"
      @page-change="onPageChange"
      @page-size-change="onPageSizeChange"
    >
      <template #index="{ rowIndex }">
        {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
      </template>

      <template #action="{ record }">
        <a-space>
          <a-button type="text" size="small" @click="handleEdit(record)">
            解绑
          </a-button>
          <a-button type="text" size="small" @click="handleDetail(record)">
            详情
          </a-button>
        </a-space>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getWaterElectricityList, type RoomVo } from '@/api/smartWaterElectricity'

// Props
interface Props {
  filterForm: {
    projectId: string;
    buildingOrRoom: string;
  };
  selectedKeys: string[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:selectedKeys': [value: string[]];
  'update:loading': [value: boolean];
  'edit-device': [record: any];
  'view-detail': [record: any];
}>();

// 响应式数据
const loading = ref(false)
const tableData = ref<RoomVo[]>([])

// 内部选中的行keys
const localSelectedKeys = ref<string[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
})

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false
})

// 表格列配置
const columns = [
    {
        title: '序号',
        slotName: 'index',
        width: 80,
        align: 'center'
    },
    {
        title: '项目',
        dataIndex: 'projectName',
        width: 150,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '所属地块',
        dataIndex: 'parcelName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '楼栋',
        dataIndex: 'buildingName',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '房源',
        dataIndex: 'roomName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '用途',
        dataIndex: 'propertyTypeName',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '运营房间名称',
        dataIndex: 'opsSysRoomName',
        width: 150,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '运营房间ID',
        dataIndex: 'opsSysRoomId',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '绑定日期',
        dataIndex: 'opsBindTime',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '操作',
        slotName: 'action',
        width: 120,
        align: 'center',
        fixed: 'right'
    }
]

// 获取表格数据
const fetchTableData = async () => {
  // 如果没有项目ID，不调用接口
  if (!props.filterForm.projectId) {
    tableData.value = []
    pagination.total = 0
    return
  }

  loading.value = true
  emit('update:loading', true)

  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      projectId: props.filterForm.projectId,
      searchParam: props.filterForm.buildingOrRoom || undefined,
      roomName: props.filterForm.buildingOrRoom || undefined,
      bindFlag: 1 // 只显示已绑定的设备
    }

    const response = await getWaterElectricityList(params)

    if (response && Array.isArray(response)) {
      tableData.value = response
      pagination.total = response.length
    } else {
      tableData.value = []
      pagination.total = 0
    }

  } catch (error) {
    console.error('获取数据失败:', error)
    Message.error('获取数据失败')
  } finally {
    loading.value = false
    emit('update:loading', false)
  }
}

// 分页变化
const onPageChange = (page: number) => {
  pagination.current = page
  fetchTableData()
}

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchTableData()
}

// 编辑处理
const handleEdit = (record: RoomVo) => {
  emit('edit-device', record)
}

// 详情处理
const handleDetail = (record: RoomVo) => {
  emit('view-detail', record)
}

// 监听selectedKeys的变化，确保添加防循环检查
watch(() => props.selectedKeys, (newVal) => {
  // 检查是否与当前值相同，如果相同则不更新
  if (JSON.stringify(localSelectedKeys.value) !== JSON.stringify(newVal)) {
    localSelectedKeys.value = [...newVal]
  }
}, { deep: true })

// 监听本地selectedKeys的变化，同步到父组件，添加防循环检查
watch(() => localSelectedKeys.value, (newVal) => {
  // 检查是否与props中的值相同，如果相同则不触发更新
  if (JSON.stringify(props.selectedKeys) !== JSON.stringify(newVal)) {
    emit('update:selectedKeys', newVal)
  }
}, { deep: true })

// 暴露给父组件的方法
defineExpose({
  fetchTableData,
  resetPagination: () => {
    pagination.current = 1
  }
})

// 组件挂载时不自动获取数据，等待父组件调用
onMounted(() => {
  // 不自动调用 fetchTableData，由父组件控制
})
</script>

<style scoped>
/* 样式可以根据需要添加 */
</style>
