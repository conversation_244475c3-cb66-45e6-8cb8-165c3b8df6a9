<template>
    <div class="room-status">
        <div class="status-title">
            <div class="title-decorator"></div>
            房态
        </div>
        <div class="status-content">
            <a-carousel indicator-type="dot" indicator-position="outer" show-arrow="never"
                indicator-class="room-status-indicator" :style="{ width: '100%', height: '100%' }">
                <a-carousel-item v-for="item in data">
                    <div class="main-area">
                        <div class="total-area-card">
                            <div class="building-icon">{{ item.label }}</div>
                            <div class="area-info">
                                <div class="area-label">总计面积</div>
                                <div class="area-value">{{ item.area }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="status-items">
                        <div class="status-item">
                            <div class="status-dot" :style="{ backgroundColor: '#EA5E5E' }"></div>
                            <div class="status-text">在租 {{ item.rent }}m²</div>
                        </div>
                        <div class="status-item">
                            <div class="status-dot" :style="{ backgroundColor: '#29AC0D' }"></div>
                            <div class="status-text">空置 {{ item.free }}m²</div>
                        </div>
                        <div class="status-item">
                            <div class="status-dot" :style="{ backgroundColor: '#F89538' }"></div>
                            <div class="status-text">待生效/签约中/已预定 {{ item.other }}m²</div>
                        </div>
                    </div>
                </a-carousel-item>
            </a-carousel>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const data = ref([
    {
        label: '宿舍',
        area: 281,
        rent: 1322,
        free: 896,
        other: 3360
    },
    {
        label: '宿舍',
        area: 281,
        rent: 1322,
        free: 896,
        other: 3360
    },
    {
        label: '宿舍',
        area: 281,
        rent: 1322,
        free: 896,
        other: 3360
    }
])

const indicators = ref([1, 2, 3, 4, 5])
</script>

<style lang="less" scoped>
.room-status {
    background: #fff;
    border-radius: 10px;
    padding: 20px 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;

    .status-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
        color: #000;
        position: relative;
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .title-decorator {
            width: 5px;
            height: 19px;
            background-color: #0071ff;
            border-radius: 4px 0px 4px 0px;
            margin-right: 10px;
        }
    }

    .status-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        position: relative;
        background-size: contain;
        min-height: 122px;
    }

    .main-area {
        position: relative;
        height: 60px;
    }

    .total-area-card {
        height: 60px;
        display: flex;
        align-items: center;

        .building-icon {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            background: url('@/assets/images/dashboard/room-status-bg.png') no-repeat center center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 500;
            color: #000;
            flex-shrink: 0;
            position: relative;
            z-index: 2;
        }

        .area-info {
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
            background: linear-gradient(270deg, rgba(248, 251, 255, 0.0001) 0%, #D6E8FF 100%);
            transform: translateX(-30px);
            padding-left: 50px;


            .area-label {
                font-size: 14px;
                color: #000;
                line-height: 1;
            }

            .area-value {
                font-size: 20px;
                font-weight: 500;
                color: #000;
                line-height: 1;
            }
        }
    }

    .status-items {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 12px;
        margin-top: 10px;
    }

    .status-item {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 0 10px;

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .status-text {
            font-size: 13px;
            color: #3A4252;
            line-height: 1.4;
        }
    }
}

:deep(.room-status-indicator) {
    .arco-carousel-indicator-item {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #D8D8D8;

        &.arco-carousel-indicator-item-active {
            background: #0071FF;
        }
    }
}
</style>