<template>
  <a-drawer
    v-model:visible="visible"
    :title="title"
    :footer="!props.readonly"
    @cancel="handleCancel"
    class="common-drawer"
  >
    <div class="room-type-container">
      <a-form label-align="right" ref="formRef" :model="formData" layout="horizontal" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>
        <!-- 户型信息 -->
        <div class="room-type-info">
          <section-title title="户型信息" class="first-child" />
          <a-row justify="space-between">
            <a-col :span="11">
              <a-form-item label="户型名称" required field="houseTypeName" :rules="[{ required: true, message: '请输入户型名称' }]">
                <a-input v-model="formData.houseTypeName" placeholder="请输入名称" :disabled="props.readonly" :max-length="30" />
              </a-form-item>
            </a-col>
            <a-col :span="11">
              <a-form-item label="户型信息" required class="room-info">
                <a-space>
                  <a-input-number v-model="formData.roomNum" placeholder="室" :min="0" style="width: 100%" :disabled="props.readonly">
                    <template #append>
                      室
                    </template>
                  </a-input-number>
                  <a-input-number v-model="formData.hallNum" placeholder="厅" :min="0" style="width: 100%" :disabled="props.readonly">
                    <template #append>
                      厅
                    </template>
                  </a-input-number>
                  <a-input-number v-model="formData.toiletNum" placeholder="卫" :min="0" style="width: 100%" :disabled="props.readonly">
                    <template #append>
                      卫
                    </template>
                  </a-input-number>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>  
          <a-row justify="space-between">
            <a-col :span="11">
              <a-form-item label="装修类型" required field="decorationType" :rules="[{ required: true, message: '请选择装修类型' }]">
                <a-select v-model="formData.decorationType" :disabled="props.readonly" placeholder="请选择装修类型">
                  <a-option 
                    v-for="item in decorationTypeOptions" 
                    :key="item.dictValue" 
                    :value="item.dictValue"
                  >
                    {{ item.dictLabel }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="11">
              <a-form-item label="可住人数" required field="livableNum" :rules="[{ required: true, message: '请输入可住人数' }]">
                <a-input-number v-model="formData.livableNum" placeholder="请输入可住人数" :min="1" style="width: 100%" :disabled="props.readonly" />
              </a-form-item>
            </a-col>
          </a-row>  
          <a-row justify="space-between">
            <a-col :span="24">
              <a-form-item label="户型描述" field="houseTypeDesc">
                <a-textarea v-model="formData.houseTypeDesc" placeholder="请输入" :disabled="props.readonly" :max-length="200" allow-clear show-word-limit/>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 标准配套 -->
        <div class="standard-equipment">
          <div class="section-header">
            <section-title title="标准配套" />
            <a-button type="text" @click="showEquipmentSelect" v-if="!props.readonly">
              <icon-plus /> 选择配套
            </a-button>
          </div>
          <div class="equipment-tags">
            <div 
              v-for="(item, index) in formData.assetList"
              :key="index"
              class="equipment-tag"
            >
              <span class="tag-text">
                {{ item.fixedAssetName }} {{ !!item.fixedAssetSpec ? '(' + item.fixedAssetSpec + ')' : '' }}
              </span>
              <icon-close 
                v-if="!props.readonly"
                class="tag-close" 
                @click="removeEquipment(index)"
              />
            </div>
          </div>
        </div>

        <!-- 户型特色 -->
        <div class="room-features">
          <div class="section-header">
            <section-title title="户型特色" />
            <a-button type="text" @click="showAddFeatureModal" v-if="!props.readonly">
              <icon-plus /> 添加特色
            </a-button>
          </div>
          <a-space wrap size="small">
            <a-tag
              v-for="(item, index) in formData.specialFeaturesList"
              :key="index"
              :closable="!props.readonly"
              @close="removeFeature(index)"
            >
              {{ item }}
            </a-tag>
          </a-space>
        </div>

        <!-- 户型图片 -->
        <div class="room-image">
          <section-title title="户型图片" />
          <upload-image
            :model-value="formData.houseTypeImg"
            :disabled="props.readonly"
            :limit="5"
            @update:model-value="handleUploadChange"
          />
        </div>
      </a-form>
    </div>

    <template #footer>
      <div class="footer-btns">
        <a-space v-if="!props.readonly">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" :loading="saveLoading" @click="handleSave">保存</a-button>
        </a-space>
        <a-button v-else @click="handleCancel">关闭</a-button>
      </div>
    </template>

    <!-- 配套选择弹框 -->
    <a-modal
      v-model:visible="equipmentSelectVisible"
      title="选择配套"
      width="800px"
      @ok="handleEquipmentSelect"
      @cancel="equipmentSelectVisible = false"
    >
      <div class="equipment-select-content">
        <!-- 搜索条件 -->
        <a-form layout="inline" :model="equipmentQuery" class="search-form">
          <a-form-item label="物品名称">
            <a-input 
              v-model="equipmentQuery.name" 
              placeholder="请输入物品名称"
              style="width: 200px"
              allow-clear
              @press-enter="loadEquipmentList"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="loadEquipmentList">查询</a-button>
          </a-form-item>
        </a-form>
        <div class="equipment-select-content-table">
          <!-- 固定资产表格 -->
          <a-table 
            :data="equipmentList" 
            :pagination="equipmentPagination"
            :loading="equipmentLoading"
            :bordered="{ cell: true }"
            :stripe="true"
            row-key="id"
            :scroll="{ y: '100%' }"
            :row-selection="equipmentRowSelection"
            v-model:selectedKeys="selectedEquipmentKeys"
            @selection-change="handleEquipmentSelectionChange"
            @page-change="handleEquipmentPageChange"
            @page-size-change="handleEquipmentPageSizeChange"
          >
            <template #columns>
              <a-table-column title="种类" :width="120">
                <template #cell="{ record }">
                  {{ getCategoryText(record.category) }}
                </template>
              </a-table-column>
              <a-table-column title="物品名称" data-index="name" :width="150" />
              <a-table-column title="规格" data-index="specification" />
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>

    <!-- 添加户型特色弹框 -->
    <a-modal
      v-model:visible="addFeatureVisible"
      title="添加户型特色"
      @ok="handleAddFeature"
      @cancel="addFeatureVisible = false"
    >
      <a-form-item label="户型特色">
        <a-input 
          v-model="newFeatureText" 
          placeholder="请输入户型特色"
          @press-enter="handleAddFeature"
          :max-length="10"
        />
      </a-form-item>
    </a-modal>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconPlus, IconDelete, IconClose } from '@arco-design/web-vue/es/icon'
import sectionTitle from '@/components/sectionTitle/index.vue'
import uploadImage from '@/components/upload/uploadImage.vue'
import { getDicts } from '@/api/system/dict'
import { useDictSync } from '@/utils/dict'
import { 
  getFixedAssetsList,
  type FixedAssetsQueryDTO,
  type FixedAssetsVo 
} from '@/api/fixedAssets'
import { 
  saveHouseType, 
  getHouseTypeDetail,
  type ProjectHouseSaveDTO,
  type ProjectHouseTypeVo,
  type HouseTypeAssetRelAddDTO 
} from '@/api/project'

// 扩展户型资产关联DTO，添加显示所需的字段
interface ExtendedHouseTypeAssetRelDTO extends HouseTypeAssetRelAddDTO {
  specification?: string
  remark?: string
  categoryName?: string
  usageScopeName?: string
}

interface FormData {
  id?: string
  projectId: string
  houseTypeName: string
  roomNum: number
  hallNum: number
  toiletNum: number
  decorationType: string
  livableNum: number
  houseTypeDesc: string
  specialFeatures: string
  specialFeaturesList: string[]
  houseTypeImg: string // 支持多图的JSON字符串格式
  assetList: ExtendedHouseTypeAssetRelDTO[]
}

const props = defineProps<{
  visible: boolean
  data?: ProjectHouseTypeVo
  title?: string
  readonly?: boolean
  projectId?: string
}>()

const emit = defineEmits(['update:visible', 'save'])
const formRef = ref()
const saveLoading = ref(false)

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 初始化空数据
const emptyFormData: FormData = {
  id: undefined,
  projectId: '',
  houseTypeName: '',
  roomNum: 0,
  hallNum: 0,
  toiletNum: 0,
  decorationType: '',
  livableNum: 1,
  houseTypeDesc: '',
  specialFeatures: '',
  specialFeaturesList: [],
  houseTypeImg: '',
  assetList: []
}

const formData = reactive<FormData>({ ...emptyFormData })

// 数据字典相关
const decorationTypeOptions = ref<any[]>([])

// 加载数据字典
const loadDictData = async () => {
  try {
    // 加载装修类型字典
    const dictData = await useDictSync('decoration_type')
    if (dictData.decoration_type) {
      decorationTypeOptions.value = dictData.decoration_type
    }
  } catch (error) {
    console.error('加载数据字典失败:', error)
  }
}

// 固定资产选择相关
const equipmentSelectVisible = ref(false)
const equipmentLoading = ref(false)
const equipmentList = ref<FixedAssetsVo[]>([])
const selectedEquipmentKeys = ref<string[]>([])
const selectedEquipmentData = ref<ExtendedHouseTypeAssetRelDTO[]>([])

const equipmentQuery = reactive<FixedAssetsQueryDTO>({
  pageNum: 1,
  pageSize: 10,
  name: ''
})

const equipmentPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
})

// 表格选择配置
const equipmentRowSelection = {
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
}

// 加载固定资产列表
const loadEquipmentList = async () => {
  try {
    equipmentLoading.value = true
    const response = await getFixedAssetsList(equipmentQuery) as any
    if (response.rows) {
      // 处理分页响应数据
      equipmentList.value = response.rows || []
      equipmentPagination.total = response.total || 0
    } else if (response.data) {
      // 处理直接返回数组的情况
      equipmentList.value = Array.isArray(response.data) ? response.data : []
      equipmentPagination.total = equipmentList.value.length
    }
    equipmentPagination.current = equipmentQuery.pageNum
    equipmentPagination.pageSize = equipmentQuery.pageSize
  } catch (error) {
    console.error('加载固定资产列表失败:', error)
    equipmentList.value = []
    equipmentPagination.total = 0
  } finally {
    equipmentLoading.value = false
  }
}

// 分页处理
const handleEquipmentPageChange = (page: number) => {
  equipmentQuery.pageNum = page
  equipmentPagination.current = page
  loadEquipmentList()
}

const handleEquipmentPageSizeChange = (pageSize: number) => {
  equipmentQuery.pageSize = pageSize
  equipmentQuery.pageNum = 1
  equipmentPagination.pageSize = pageSize
  equipmentPagination.current = 1
  loadEquipmentList()
}

const handleEquipmentSelectionChange = (selectedKeys: string[]) => {
  console.log(selectedKeys)
  
  // 移除已取消选择的配套（不在selectedKeys中的）
  selectedEquipmentData.value = selectedEquipmentData.value.filter(item => 
    selectedKeys.includes(item.fixedAssetId || '')
  )
  
  // 添加当前页新选择的配套（在selectedKeys中但不在selectedEquipmentData中的）
  const newSelectedItems = equipmentList.value
    .filter(item => {
      const itemId = item.id
      return itemId && 
        selectedKeys.includes(itemId) && 
        !selectedEquipmentData.value.some(selected => selected.fixedAssetId === itemId)
    })
    .map(item => ({
      fixedAssetId: item.id || '',
      fixedAssetName: item.name || '',
      fixedAssetSpec: item.specification || '',
      remark: item.remark || '',
      categoryName: getCategoryText(item.category),
      usageScopeName: getUsageScopeName(item.usageScope)
    }))
  
  selectedEquipmentData.value.push(...newSelectedItems)
}

const showEquipmentSelect = () => {
  // 每次打开弹框都清空选中状态，相当于新增
  selectedEquipmentKeys.value = []
  selectedEquipmentData.value = []
  
  // 重置分页到第一页
  equipmentQuery.pageNum = 1
  equipmentPagination.current = 1
  
  equipmentSelectVisible.value = true
  loadEquipmentList()
}

const handleEquipmentSelect = () => {
  // 直接使用保存好的选中配套数据
  if (selectedEquipmentData.value.length > 0) {
    // 追加到现有列表，而不是替换
    formData.assetList.push(...selectedEquipmentData.value)
  }
  
  equipmentSelectVisible.value = false
}

// 获取种类名称
const getCategoryText = (category: number | undefined) => {
  if (!category) return '未知'
  const categoryMap: Record<number, string> = {
    1: '家电',
    2: '家具', 
    3: '装饰',
    4: '其他'
  }
  return categoryMap[category] || '未知'
}

const getUsageScopeName = (usageScope: string | undefined): string => {
  if (!usageScope) return ''
  const scope = usageScope.split(',')
  return scope.map(s => s.trim()).join(', ')
}

const removeEquipment = (index: number) => {
  if (index >= 0 && index < formData.assetList.length) {
    formData.assetList.splice(index, 1)
  }
}

// 户型特色相关
const addFeatureVisible = ref(false)
const newFeatureText = ref('')

const showAddFeatureModal = () => {
  newFeatureText.value = ''
  addFeatureVisible.value = true
}

const handleAddFeature = () => {
  if (newFeatureText.value.trim()) {
    formData.specialFeaturesList.push(newFeatureText.value.trim())
    updateSpecialFeatures()
    newFeatureText.value = ''
    addFeatureVisible.value = false
  }
}

const removeFeature = (index: number) => {
  formData.specialFeaturesList.splice(index, 1)
  updateSpecialFeatures()
}

const updateSpecialFeatures = () => {
  // 过滤掉空字符串，然后用逗号连接
  formData.specialFeatures = formData.specialFeaturesList
    .filter(feature => feature.trim() !== '')
    .join(',')
}

// 图片上传相关
const handleUploadChange = (url: string) => {
  formData.houseTypeImg = url
}

// 初始化表单数据
watch(() => props.data, async (newVal) => {
  if (newVal && newVal.id) {
    // 编辑模式，获取详细信息
    try {
      const response = await getHouseTypeDetail(newVal.id)
      if (response && response.data) {
        const detail = response.data
        Object.assign(formData, {
          id: detail.id,
          projectId: props.projectId || detail.projectId || '',
          houseTypeName: detail.houseTypeName || '',
          roomNum: detail.roomNum || 0,
          hallNum: detail.hallNum || 0,
          toiletNum: detail.toiletNum || 0,
          decorationType: String(detail.decorationType || ''),
          livableNum: detail.livableNum || 1,
          houseTypeDesc: detail.houseTypeDesc || '',
          specialFeatures: detail.specialFeatures || '',
          specialFeaturesList: detail.specialFeaturesList || [],
          houseTypeImg: detail.houseTypeImg || '',
          assetList: detail.assetList || []
        })
      }
    } catch (error) {
      console.error('获取户型详情失败:', error)
    }
  } else {
    // 新增模式，重置表单数据
    Object.assign(formData, {
      ...emptyFormData,
      projectId: props.projectId || '',
      specialFeaturesList: [], // 显式重置 specialFeaturesList
      specialFeatures: '' // 同时重置 specialFeatures
    })
  }
}, { immediate: true })

// 监听弹框显示状态，确保每次打开时都正确初始化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 弹框打开时，根据data重新初始化表单
    if (props.data && props.data.id) {
      // 编辑模式 - 会触发上面的watch
    } else {
      // 新增模式 - 确保表单被重置
      Object.assign(formData, {
        ...emptyFormData,
        projectId: props.projectId || '',
        specialFeaturesList: [], // 显式重置 specialFeaturesList
        specialFeatures: '' // 同时重置 specialFeatures
      })
    }
  }
})

const handleCancel = () => {
  visible.value = false
}

const handleSave = async () => {
  try {
    const errors = await formRef.value.validate()
    if (errors) return

    if (!props.projectId) {
      Message.error('项目ID不能为空')
      return
    }

    saveLoading.value = true
    
    const saveData: ProjectHouseSaveDTO = {
      projectId: props.projectId,
      houseTypeName: formData.houseTypeName,
      roomNum: formData.roomNum,
      hallNum: formData.hallNum,
      toiletNum: formData.toiletNum,
      decorationType: Number(formData.decorationType),
      livableNum: formData.livableNum,
      houseTypeDesc: formData.houseTypeDesc,
      specialFeatures: formData.specialFeatures,
      specialFeaturesList: formData.specialFeaturesList,
      // 如果没有上传户型图片，则设为undefined
      houseTypeImg: formData.houseTypeImg || undefined,
      assetList: formData.assetList.map(item => ({
        id: item.id,
        houseTypeId: item.houseTypeId,
        fixedAssetId: item.fixedAssetId,
        fixedAssetName: item.fixedAssetName,
        fixedAssetSpec: item.fixedAssetSpec
      }))
    }

    // 只有编辑模式才传递id
    if (formData.id) {
      saveData.id = formData.id
    }

    await saveHouseType(saveData)
    Message.success('保存成功')
    emit('save')
    visible.value = false
  } catch (error) {
    console.error('保存户型失败:', error)
  } finally {
    saveLoading.value = false
  }
}

onMounted(() => {
  loadDictData()
})
</script>

<style scoped lang="less">
.room-type-container {
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  margin: 16px 0;
  &.first-child {
    margin-top: 8px;
  }
}

.footer-btns {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px 16px 0;
  background: #fff;
  border-top: 1px solid #f0f0f0;
}

.room-info {
  :deep(.arco-input-group) {
    display: flex;
    gap: 8px;
  }
}

.standard-equipment,
.room-features {
  margin: 20px 0;
}

.equipment-select-content {
  .search-form {
    padding:0 0 16px 0;
    background: #f8f9fa;
    border-radius: 4px;
  }
  .equipment-select-content-table {
    height: 450px;
  }
}

:deep(.arco-upload-list-item) {
  width: 200px;
  height: 200px;
}

:deep(.arco-upload-picture-card) {
  width: 200px;
  height: 200px;
}

.upload-button {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-text-3);
}

:deep(.arco-modal-body) {
  padding: 16px;
}

.equipment-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.equipment-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background-color: var(--color-fill-2);
  border: 1px solid var(--color-border-2);
  border-radius: 6px;
  font-size: 12px;
  line-height: 20px;
  color: var(--color-text-1);
  max-width: 100%;
}

.tag-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-close {
  margin-left: 4px;
  cursor: pointer;
  color: var(--color-text-3);
  transition: color 0.2s;
  flex-shrink: 0;
  
  &:hover {
    color: var(--color-text-1);
  }
}
</style> 