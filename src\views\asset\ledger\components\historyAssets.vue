<template>
  <div class="history-assets">
    <!-- 搜索区域 -->
    <a-row>
      <a-col :flex="1">
        <a-form :model="formModel" auto-label-width label-align="right">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="projectId" label="项目">
                <ProjectSelector v-model="formModel.projectId" @change="handleProjectSelectorChange" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="location" label="地块/楼栋">
                <a-input v-model="formModel.location" placeholder="请输入地块/楼栋" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="roomName" label="房源名称">
                <a-input v-model="formModel.roomName" placeholder="请输入名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="dateRange" label="处置日期">
                <a-range-picker v-model="formModel.dateRange" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <a-divider style="height: 84px" direction="vertical" />
      <a-col :flex="'86px'" style="text-align: right">
        <a-space direction="vertical" :size="18">
          <a-button v-permission="['asset:ledger:historylist']" type="primary" @click="search">
            <template #icon>
              <icon-search />
            </template>
            查询
          </a-button>
          <a-button @click="reset">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-divider style="margin-top: 16px" />
    <!-- 表格区域 -->
    <a-row style="margin-bottom: 12px">
      <a-col :span="24" style="text-align: right">
        <a-space>
          <a-button v-permission="['asset:ledger:historyexport']" type="primary" @click="handleExport">
            导出资产清单
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-table row-key="id" :loading="loading" :pagination="pagination" :columns="columns" :data="tableData"
      :bordered="{ cell: true }" :scroll="{ x: 2000 }" @page-change="onPageChange" @page-size-change="onPageSizeChange">
      <template #index="{ rowIndex }">
        {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
      </template>
      <template #roomName="{ record }">
        <PermissionLink v-permission="['asset:project:room:detail']" @click="handleRoomName(record)">{{ record.roomName }}</PermissionLink>
      </template>
      <template #propertyType="{ record }">
        {{ getDictLabel(asset_property_type, record.propertyType) }}
      </template>
      <template #disposalMethod="{ record }">
        {{ getDictLabel(disposal_type, record.disposalMethod) }}
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getLedgerHistoryList, exportLedgerHistoryList, type LedgerHistoryListDto, type LedgerHistoryVo } from '@/api/asset/projectLedger'
import { getProjectList } from '@/api/asset/projectReceive'
import { useDict, getDictLabel } from '@/utils/dict'
import ProjectSelector from '@/components/projectSelector/index.vue'
import { exportExcel } from '@/utils/exportUtil'

const { asset_property_type, disposal_type, product_type } = useDict('asset_property_type', 'disposal_type', 'product_type');

// 产品类型转换
const getProductTypeText = (value: string | number) => {
    if (!value) return '-'
    const item = product_type.value.find((item: any) => item.value === String(value))
    return item ? item.label : value
}

interface ProjectOption {
  id: string
  name: string
}

interface TableItem extends LedgerHistoryVo {
  id: string
}

// 表格数据
const loading = ref(false)
const selectedKeys = ref<string[]>([])
const tableData = ref<TableItem[]>([])
const projectOptions = ref<ProjectOption[]>([])

// 表格列定义
const columns = [
  {
    title: '序号',
    slotName: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '房源名称',
    slotName: 'roomName',
    width: 120,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '所属项目',
    dataIndex: 'projectName',
    width: 150,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '所属地块',
    dataIndex: 'parcelName',
    width: 120,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '楼栋',
    dataIndex: 'buildingName',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '产品类型',
    dataIndex: 'productType',
    width: 100,
    align: 'center',
    render: ({ record }: { record: any }) => {
      return getProductTypeText(record.productType)
    }
  },
  {
    title: '面积类型',
    dataIndex: 'areaTypeName',
    width: 100,
    align: 'center'
  },
  {
    title: '建筑面积',
    dataIndex: 'buildArea',
    width: 100,
    align: 'center'
  },
  {
    title: '套内面积',
    dataIndex: 'innerArea',
    width: 100,
    align: 'center'
  },
  {
    title: '自持物业类型',
    dataIndex: 'propertyType',
    slotName: 'propertyType',
    width: 120,
    align: 'center'
  },
  {
    title: '处置日期',
    dataIndex: 'disposalDate',
    width: 120,
    align: 'center'
  },
  {
    title: '处置方式',
    dataIndex: 'disposalMethod',
    slotName: 'disposalMethod',
    width: 100,
    align: 'center'
  }
]

// 分页
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showPageSize: true
})

// 表单
const formModel = reactive({
  projectId: undefined as string | undefined,
  location: '',
  roomName: '',
  dateRange: [] as any[]
})

// 搜索
const search = async () => {
  loading.value = true
  console.log(formModel.dateRange)
  const params: LedgerHistoryListDto = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    projectId: formModel.projectId,
    buildingName: formModel.location || '',
    roomName: formModel.roomName || '',
    disposalStartDate: formModel.dateRange ? formModel.dateRange[0] : '',
    disposalEndDate: formModel.dateRange ? formModel.dateRange[1] : ''
  }

  const response = await getLedgerHistoryList(params)
  if (response.rows) {
    tableData.value = response.rows.map((item: LedgerHistoryVo, index: number) => ({
      ...item,
      id: item.roomId || `${index}`
    }))
    pagination.total = response.total || 0
  }
  loading.value = false
}

// 重置
const reset = () => {
  formModel.projectId = undefined
  formModel.location = ''
  formModel.roomName = ''
  formModel.dateRange = []
  search()
}

// 分页变化
const onPageChange = (current: number) => {
  pagination.current = current
  search()
}

// 每页条数变化
const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  search()
}

// 导出
const handleExport = async () => {
  try {
    const params: LedgerHistoryListDto = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      projectId: formModel.projectId,
      buildingName: formModel.location,
      roomName: formModel.roomName,
      disposalStartDate: formModel.dateRange ? formModel.dateRange[0] : '',
      disposalEndDate: formModel.dateRange ? formModel.dateRange[1] : ''
    }

    await exportExcel(exportLedgerHistoryList, params, '历史资产')
    Message.success('导出成功')
  } catch (error) {
    Message.error('导出失败')
  }
}

// 查看详情
const handleView = (record: TableItem) => {
  Message.info('查看功能开发中')
}

// 选择事件
const onSelect = (selectedRowKeys: string[]) => {
  selectedKeys.value = selectedRowKeys
}

// 全选事件
const onSelectAll = (checked: boolean) => {
  selectedKeys.value = checked ? tableData.value.map(item => item.id) : []
}

// 点击跳转房源
const handleRoomName = (record: TableItem) => {
  // TODO: 实现房源详情跳转
}

// 处理项目选择变化（用于ProjectSelector组件）
const handleProjectSelectorChange = (projectId: string) => {
  console.log('项目选择变化:', projectId)
  // 项目变化时重新查询数据
  search()
}

// 组件挂载时加载数据
// onMounted(() => {
//   search()
// })
</script>