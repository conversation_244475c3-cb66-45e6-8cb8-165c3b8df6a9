import { mergeConfig } from 'vite';
import baseConfig from './vite.config.base';
import configTerserPlugin from './plugin/terser';
import configWindowsPerformancePlugin from './plugin/performance-win';
import { createMemoryOptimizerPlugin } from './plugin/memory-optimizer';

export default mergeConfig(
    {
        mode: 'production',
        plugins: [
            // 内存优化插件
            createMemoryOptimizerPlugin({
                maxMemory: 16384,
                enableGCOptimization: true,
                enableMemoryMonitoring: true,
                gcThreshold: 2048,
            }),
        ],
        build: {
            // Windows特定的构建优化
            ...configTerserPlugin({
                dropConsole: true,
                dropDebugger: true,
                removeComments: true,
                parallel: true,
                sourcemap: false,
            }),
            // 清空输出目录
            emptyOutDir: true,
            // 关闭文件大小报告以提高速度
            reportCompressedSize: false,
            // 设置较大的chunk警告限制
            chunkSizeWarningLimit: 2000,
        },
        // Windows性能优化配置
        ...configWindowsPerformancePlugin({
            enableCache: true,
            enableMultiThread: true,
            maxMemory: 16384,
            enableFsCache: true,
            enableWindowsOptimizations: true,
        }),
    },
    baseConfig
); 