import http from '../index';

// 登录日志接口返回值类型定义
export interface LoginInfo {
  createBy: string | null;      // 创建者
  createTime: string | null;    // 创建时间
  updateBy: string | null;      // 更新者
  updateTime: string | null;    // 更新时间
  remark: string | null;        // 备注
  infoId: number;              // 访问ID
  browser: string;             // 浏览器类型
  ipaddr: string;              // IP地址
  loginLocation: string;       // 登录地点
  loginTime: string;           // 登录时间
  msg: string;                 // 提示消息
  os: string;                  // 操作系统
  status: string;              // 登录状态（0成功 1失败）
  userName: string;            // 用户名称
}

export interface ListResponse {
  total: number;           // 总记录数
  rows: LoginInfo[];       // 登录日志列表数据
  code: number;           // 响应状态码
  msg: string;            // 响应消息
}

export interface Response {
  code: number;           // 响应状态码
  msg: string;            // 响应消息
}

// 查询登录日志列表
export const list = (query?: any) => {
  return http.get('/business-platform/logininfor/list', query);
}

// 删除登录日志
export const delLogininfor = (infoId: string | number) => {
  return http.delete(`/business-platform/logininfor/${infoId}`);
}

// 解锁用户登录状态
export const unlockLogininfor = (userName: string) => {
  return http.get(`/business-platform/logininfor/unlock/${userName}`);
}

// 清空登录日志
export const cleanLogininfor = () => {
  return http.delete('/business-platform/logininfor/clean');
}

// 导出登录日志
export const exportLoginInfoLog = (data: any) => {
  return http.download('/business-platform/logininfor/export', data);
}