<template>
	<a-modal v-model:visible="visible" title="资产变更-选择房源" :width="1200" :footer="false">
		<a-form :model="formData" layout="horizontal" auto-label-width>
			<a-row :gutter="16">
				<!-- <a-col :span="8">
          <a-form-item label="项目名称">
            <a-select v-model="formData.projectId" placeholder="请选择项目" @change="handleProjectChange">
              <a-option v-for="item in projectOptions" :key="item.id" :value="item.id">
                {{ item.projectName }}
              </a-option>
            </a-select>
          </a-form-item>
        </a-col> -->
				<a-col :span="8">
					<a-form-item label="楼栋名称">
						<a-input v-model="formData.buildingName" placeholder="请输入楼栋名称" @change="handleSearch" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="房源名称">
						<a-input v-model="formData.roomName" placeholder="请输入房源名称" @change="handleSearch" />
					</a-form-item>
				</a-col>
			<!-- </a-row>
			<a-row> -->
				<a-col :span="8" style="text-align: right;">
					<a-space>
						<a-button @click="handleReset">重置</a-button>
						<a-button type="primary" @click="handleSearch">查询</a-button>
					</a-space>
				</a-col>
			</a-row>
		</a-form>

		<div class="room-list">
			<a-table ref="roomTableRef" row-key="id" :data="roomList" :pagination="pagination"
				:row-selection="rowSelection" :loading="loading" :bordered="{ cell: true }"
				@selection-change="handleSelectionChange" @page-change="onPageChange"
				@page-size-change="onPageSizeChange">
				<template #columns>
					<!-- <a-table-column title="房间ID" data-index="id" width="130" /> -->
					<a-table-column title="房源名称" data-index="roomName" :width="120" :ellipsis="true" :tooltip="true" align="center" />
					<a-table-column title="所属地块" data-index="parcelName" :width="160" :ellipsis="true" :tooltip="true" align="center" />
					<a-table-column title="楼栋" data-index="buildingName" :width="120" :ellipsis="true" :tooltip="true" align="center" />
					<a-table-column title="产品类型" data-index="productType" :width="110" :ellipsis="true" :tooltip="true" align="center">
						<template #cell="{ record }">
							{{ getProductTypeText(record.productType) }}
						</template>
					</a-table-column>
					<a-table-column title="原面积类型" data-index="mdmAreaTypeName" :width="120" :ellipsis="true" :tooltip="true" align="center" />
					<a-table-column title="原建筑面积" data-index="mdmBuildingArea" :width="140" :ellipsis="true" :tooltip="true" align="center">
						<template #cell="{ record }">
							{{ record.mdmBuildingArea }}㎡
						</template>
					</a-table-column>
					<a-table-column title="原套内面积" data-index="mdmInsideArea" :width="140" :ellipsis="true" :tooltip="true" align="center">
						<template #cell="{ record }">
							{{ record.mdmInsideArea }}㎡
						</template>
					</a-table-column>
					<a-table-column title="调整后面积类型" data-index="areaTypeName" :width="140" :ellipsis="true" :tooltip="true" align="center" />
					<a-table-column title="调整后建筑面积" data-index="buildArea" :width="140" :ellipsis="true" :tooltip="true" align="center">
						<template #cell="{ record }">
							{{ record.buildArea }}㎡
						</template>
					</a-table-column>
					<a-table-column title="调整后套内面积" data-index="innerArea" :width="140" :ellipsis="true" :tooltip="true" align="center">
						<template #cell="{ record }">
							{{ record.innerArea }}㎡
						</template>
					</a-table-column>
					<a-table-column title="建筑面积差" data-index="buildingAreaDiff" :width="140" :ellipsis="true" :tooltip="true" align="center">
						<template #cell="{ record }">
							<span
								:class="{ 'area-diff-positive': record.buildingAreaDiff > 0, 'area-diff-negative': record.buildingAreaDiff < 0 }">
								{{ record.buildingAreaDiff }}
							</span>
						</template>
					</a-table-column>
				</template>
			</a-table>
		</div>
		<div class="modal-footer">
			<a-space>
				<a-button @click="handleCancel">取消</a-button>
				<a-button type="primary" @click="handleNext" :disabled="selectedKeys.length === 0">
					下一步（已选{{ selectedKeys.length }}项）
				</a-button>
			</a-space>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
	getChangeRoomList,
	getProjectList,
	type AssetChangeRoomQueryDTO,
	type AssetChangeRoomVo,
	type ProjectVo
} from '@/api/asset/projectChange'
import { useDict } from '@/utils/dict'

const emit = defineEmits(['next'])

// 字典支持
const { product_type } = useDict('product_type')

// 产品类型转换
const getProductTypeText = (value: string | number) => {
    if (!value) return '-'
    const item = product_type.value.find((item: any) => item.value === String(value))
    return item ? item.label : value
}

const visible = ref(false)
const loading = ref(false)

// 表单数据
const formData = reactive({
	projectId: '',
	buildingName: '',
	roomName: ''
})

// 表格数据
const roomList = ref<AssetChangeRoomVo[]>([])

// 分页配置
const pagination = reactive({
	total: 0,
	current: 1,
	pageSize: 10,
	showTotal: true,
	showPageSize: true,
	pageSizeOptions: ['10', '20', '50', '100']
})

// 全选状态
const selectedKeys = ref<string[]>([])
const selectedRows = ref<AssetChangeRoomVo[]>([])

// 表格选择配置
const rowSelection = {
	type: 'checkbox',
	showCheckedAll: true,
	onlyCurrent: false,
	selectedKeys: selectedKeys,
	selectedRows: selectedRows,
}

// 处理表格选择变化
const handleSelectionChange = (rowKeys: string[], rows: AssetChangeRoomVo[]) => {
	console.log('选中的 rowKeys:', rowKeys)
	console.log('选中的 rows:', rows)
	selectedKeys.value = rowKeys
	selectedRows.value = rows
}

// 页码变化
const onPageChange = (current: number) => {
	pagination.current = current
	fetchRoomList()
}

// 页大小变化
const onPageSizeChange = (pageSize: number) => {
	pagination.pageSize = pageSize
	pagination.current = 1
	fetchRoomList()
}

// 项目变化
const handleProjectChange = () => {
	formData.buildingName = ''
	formData.roomName = ''
	selectedKeys.value = []
	selectedRows.value = []
	pagination.current = 1
	fetchRoomList()
}

// 搜索
const handleSearch = () => {
	pagination.current = 1
	// 不清空选中状态，保持用户的选择
	fetchRoomList()
}

// 重置
const handleReset = () => {
	// 不重置projectId，因为它是从父组件传入的
	formData.buildingName = ''
	formData.roomName = ''
	selectedKeys.value = []
	selectedRows.value = []
	pagination.current = 1
	
	// 取消表格中的所有选择
	roomTableRef.value?.selectAll(false)
	
	// 如果有项目ID，重新获取房源列表；否则清空列表
	if (formData.projectId) {
		fetchRoomList()
	} else {
		roomList.value = []
		pagination.total = 0
	}
}

// 取消
const handleCancel = () => {
	visible.value = false
	// 取消时只重置选择状态，不清空搜索条件和数据
	selectedKeys.value = []
	selectedRows.value = []
}

// 下一步
const handleNext = () => {
	if (selectedKeys.value.length === 0) {
		Message.warning('请至少选择一个房源')
		return
	}

	// 获取选中的完整房源数据
	const selectedRoomData = roomList.value.filter(room => selectedKeys.value.includes(room.id!))

	visible.value = false

	// 传递完整的房源数据和项目信息
	emit('next', {
		roomIds: selectedKeys.value,
		roomData: selectedRoomData,
		projectInfo: {
			id: formData.projectId,
		}
	})

	// 重置选择状态
	selectedKeys.value = []
	selectedRows.value = []
}

// 获取房源列表
const fetchRoomList = async () => {
	if (!formData.projectId) {
		roomList.value = []
		pagination.total = 0
		return
	}

	loading.value = true
	try {
		const params: AssetChangeRoomQueryDTO = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			projectId: formData.projectId,
			buildingName: formData.buildingName || undefined,
			roomName: formData.roomName || undefined
		}

		const response = await getChangeRoomList(params)
		if (response && response.rows) {
			if (response.rows && Array.isArray(response.rows)) {
				roomList.value = response.rows
				pagination.total = response.total || 0
				
				// 同步选中状态：只保留当前页面中存在的选中项
				const currentPageRoomIds = response.rows.map((room: any) => room.id)
				const validSelectedKeys = selectedKeys.value.filter(key => currentPageRoomIds.includes(key))
				const validSelectedRows = selectedRows.value.filter(row => currentPageRoomIds.includes(row.id))
				
				// 如果选中状态发生了变化，更新状态
				if (validSelectedKeys.length !== selectedKeys.value.length) {
					selectedKeys.value = validSelectedKeys
					selectedRows.value = validSelectedRows
				}
			}
		}
	} catch (error) {
		console.error('获取房源列表失败:', error)
		// Message.error('获取房源列表失败')
	} finally {
		loading.value = false
	}
}

const roomTableRef = ref()

// 暴露方法给父组件
defineExpose({
	show: (projectId?: string) => {
		visible.value = true
		// 重置选择状态和搜索条件
		selectedKeys.value = []
		selectedRows.value = []
		formData.buildingName = ''
		formData.roomName = ''
		pagination.current = 1
		roomTableRef.value?.selectAll(false)

		if (projectId) {
			formData.projectId = projectId
			fetchRoomList()
		} else {
			// 如果没有传入项目ID，清空房源列表
			roomList.value = []
			pagination.total = 0
		}
	}
})
</script>

<style scoped lang="less">
.room-list {
	margin-top: 16px;
}

.modal-footer {
	text-align: center;
	margin-top: 24px;
	padding-top: 16px;
	border-top: 1px solid var(--color-border-2);
}

.area-diff-positive {
	color: #f53f3f;
}

.area-diff-negative {
	color: #00b42a;
}

:deep(.arco-table-th) {
	background-color: var(--color-fill-2);
}

:deep(.arco-table-td) {
	border-bottom: 1px solid var(--color-border-2);
}
</style>