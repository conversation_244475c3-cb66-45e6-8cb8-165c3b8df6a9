<template>
  <a-drawer
    v-model:visible="visible"
    :title="drawerTitle"
    class="common-drawer"
    :footer="mode !== 'view'"
    @cancel="handleCancel"
  >
    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="loading">
          保存
        </a-button>
      </a-space>
    </template>

    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      label-align="right"
      :disabled="mode === 'view'"
    >
      <!-- 表单公共项 -->
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="项目名称" field="projectId" required>
            <ProjectTreeSelect 
              v-model="formData.projectId" 
              :min-level="4"
              @change="handleProjectChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="客户类型" field="customerType" required>
            <a-radio-group v-model="formData.customerType" @change="handleTypeChange">
              <a-radio :value="1">个人</a-radio>
              <a-radio :value="2">企业</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 个人客户表单 -->
      <template v-if="formData.customerType === 1">
        <section-title title="基础信息" />
        <a-row :gutter="16">
          <a-col :span="12">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="姓名" field="customerName" required>
                  <a-input v-model.trim="formData.customerName" placeholder="请输入姓名" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="联系电话" field="contactPhone" required>
                  <a-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                              <a-form-item label="证件类型" field="idType" required>
                <a-select v-model="formData.idType" placeholder="请选择证件类型" @change="handleIdTypeChange">
                    <a-option value="1">身份证</a-option>
                    <a-option value="2">护照</a-option>
                    <a-option value="3">军官证</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="证件号码" field="idNumber" required>
                  <a-input v-model="formData.idNumber" placeholder="请输入证件号码" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="证件有效期" field="idValidPeriod">
                  <a-range-picker v-model="formData.idValidPeriod" style="width: 100%;"/>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="联系地址" field="contactAddress">
                  <a-textarea v-model="formData.contactAddress" placeholder="请输入联系地址" :rows="3"  :max-length="100" allow-clear show-word-limit/>
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-card class="upload-card">
              <div class="card-title">请上传本人身份证正反面</div>
              <a-form-item label="请上传本人身份证正反面" hide-label>
                <a-row :gutter="15">
                  <template v-if="mode === 'view'">
                    <a-col :span="12">
                      <div class="card-item">
                        <img class="card-bg" :src="formData.idFront || idCardFrontImg" />
                      </div>
                    </a-col>
                    <a-col :span="12">
                      <div class="card-item">
                        <img class="card-bg" :src="formData.idBack || idCardBackImg" />
                      </div>
                    </a-col>
                  </template>
                  <template v-else>
                    <a-col :span="12">
                      <a-upload 
                        :custom-request="(option: any) => handleIdCardFrontUpload(option)"
                        :show-file-list="false"
                        accept="image/*"
                      >
                        <template #upload-button>
                          <div class="card-item">
                            <img class="card-bg" :src="formData.idFront || idCardFrontImg" />
                            <span class="card-text" v-if="!formData.idFront">上传身份证人像面</span>
                          </div>
                        </template>
                      </a-upload>
                    </a-col>
                    <a-col :span="12">
                      <a-upload 
                        :custom-request="(option: any) => handleIdCardBackUpload(option)"
                        :show-file-list="false"
                        accept="image/*"
                      >
                        <template #upload-button>
                          <div class="card-item">
                            <img class="card-bg" :src="formData.idBack || idCardBackImg" />
                            <span class="card-text" v-if="!formData.idBack">上传身份证国徽面</span>
                          </div>
                        </template>
                      </a-upload>
                    </a-col>
                  </template>
                </a-row>
              </a-form-item>
              <div class="card-title">身份证上传需求</div>
              <div class="card-img">
                <img src="@/assets/images/customer/<EMAIL>" alt="">
              </div>
              <div class="button-bar" v-if="mode !== 'view'">
                <a-button type="primary" @click="handleIdCardRecognition">开始识别</a-button>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 联系人信息 -->
        <section-title title="联系人信息">
          <template #right>
            <a-button type="primary" size="small" @click="addContact" v-if="mode !== 'view'">
              <template #icon><icon-plus /></template>
              添加
            </a-button>
          </template>
        </section-title>
        <contact-table 
          v-model="safeContactList" 
          :readonly="mode === 'view'"
          :customer-type="formData.customerType"
          @set-preferred="setPreferredContact"
          @contact-deleted="handleContactDeleted"
        />
      </template>

      <!-- 企业客户表单 -->
      <template v-if="formData.customerType === 2">
        <section-title title="基础信息" />
        <a-row :gutter="16">
          <a-col :span="12">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="企业名称" field="customerName" required>
                  <a-input v-model.trim="formData.customerName" placeholder="请输入企业名称" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="统一社会信用代码" field="creditCode" required>
                  <a-input v-model="formData.creditCode" placeholder="请输入统一社会信用代码" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="法人姓名" field="legalName" required>
                  <a-input v-model.trim="formData.legalName" placeholder="请输入法人姓名" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="法人联系电话" field="contactPhone">
                  <a-input v-model="formData.contactPhone" placeholder="请输入法人联系电话" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                              <a-form-item label="法人证件类型" field="idType">
                <a-select v-model="formData.idType" placeholder="请选择证件类型" @change="handleIdTypeChange">
                    <a-option value="1">身份证</a-option>
                    <a-option value="2">护照</a-option>
                    <a-option value="3">军官证</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="法人证件号码" field="idNumber">
                  <a-input v-model="formData.idNumber" placeholder="请输入法人证件号码" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-card class="upload-card">
              <div class="card-title">请上传营业执照</div>
              <div class="card-business">
                <a-form-item label="请上传营业执照" hide-label>
                  <template v-if="mode === 'view'">
                    <div class="card-item business-license">
                      <img class="card-bg" :src="formData.businessLicense || businessLicenseImg" />
                    </div>
                  </template>
                  <template v-else>
                    <a-upload 
                      :custom-request="(option: any) => handleBusinessLicenseUpload(option)"
                      :show-file-list="false"
                      accept="image/*"
                    >
                      <template #upload-button>
                        <div class="card-item business-license">
                          <img class="card-bg" :src="formData.businessLicense || businessLicenseImg" />
                          <span class="card-text" v-if="!formData.businessLicense">上传营业执照</span>
                        </div>
                      </template>
                    </a-upload>
                  </template>
                </a-form-item>
                <div class="button-bar" v-if="mode !== 'view'">
                  <a-button type="primary" @click="handleBusinessLicenseRecognition">开始识别</a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
        <!-- 经办人信息 -->
        <section-title title="经办人信息">
          <template #right>
            <a-button type="primary" size="small" @click="addAgent" v-if="mode !== 'view'">
              <template #icon><icon-plus /></template>
              添加
            </a-button>
          </template>
        </section-title>
        <contact-table 
          v-model="safeContactList" 
          :readonly="mode === 'view'"
          :customer-type="formData.customerType"
          @set-preferred="setPreferredContact"
          @contact-deleted="handleContactDeleted"
        />
      </template>

      <!-- 担保人信息 -->
      <section-title title="担保人信息">
        <template #right>
          <a-button type="primary" size="small" @click="addGuarantor" v-if="mode !== 'view'">
            <template #icon><icon-plus /></template>
            添加
          </a-button>
        </template>
      </section-title>
      <guarantor-table 
        v-model="safeGuarantorList" 
        :readonly="mode === 'view'"
      />

      <!-- 银行账号 -->
      <section-title title="银行账号">
        <template #right>
          <a-button type="primary" size="small" @click="addBankAccount" v-if="mode !== 'view'">
            <template #icon><icon-plus /></template>
            添加
          </a-button>
        </template>
      </section-title>
      <bank-account-table 
        v-model="safeBankAccountList" 
        :readonly="mode === 'view'"
      />

      <!-- 开票信息 -->
      <template v-if="formData.customerType === 2">
        <section-title title="开票信息">
          <template #right>
            <a-button type="primary" size="small" @click="addInvoice" v-if="mode !== 'view'">
              <template #icon><icon-plus /></template>
              添加
            </a-button>
          </template>
        </section-title>
        <invoice-table 
          v-model="safeInvoiceList" 
          :readonly="mode === 'view'"
        />
      </template>

      <!-- 备注 -->
      <section-title title="备注" />
      <a-form-item field="remark">
        <a-textarea v-model="formData.remark" placeholder="请输入备注信息" :rows="4" />
      </a-form-item>

      <!-- 附件 -->
      <section-title title="附件" />
      <upload-file 
        v-model="formData.attachmentFiles" 
        :readonly="mode === 'view'"
        :limit="10"
        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
      />
    </a-form>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconPlus } from '@arco-design/web-vue/es/icon'
import SectionTitle from '@/components/sectionTitle/index.vue'
import UploadFile from '@/components/upload/uploadFile.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import ContactTable from './ContactTable.vue'
import InvoiceTable from './InvoiceTable.vue'
import GuarantorTable from './GuarantorTable.vue'
import BankAccountTable from './BankAccountTable.vue'
import customerApi, { 
  type CustomerAddDTO, 
  type CustomerVo
} from '@/api/customer'
import { uploadFile as uploadFileApi, analyzeImage } from '@/api/common'

// 导入图片
import idCardFrontImg from '@/assets/images/contract/idCard-front.png'
import idCardBackImg from '@/assets/images/contract/idCard-back.png'
// 使用营业执照占位图
import businessLicenseImg from '@/assets/images/customer/business_license.png'

// 定义表格数据类型，与组件期望的类型匹配
interface TableContact {
  id: number | string
  name: string
  phone: string
  relationship?: number // 关系（1夫妻 2合伙人 3父母 4朋友 5子女 6其他）
  position?: string
  department?: string
  gender: number
  idNumber: string
  isPreferred?: boolean
  remark: string
  isEditing?: boolean
}

interface TableGuarantor {
  id: number
  name: string
  phone: string
  idType: string
  idNumber: string
  address: string // 新增地址字段
  remark: string
  isEditing?: boolean
}

interface TableBankAccount {
  id: number
  bankName: string
  accountNumber: string
  accountRemark: string
  isEditing?: boolean
}

interface TableInvoice {
  id: number
  title: string
  taxNumber: string
  address: string
  phone: string
  bankName: string
  accountNumber: string
  isEditing?: boolean
}

interface CustomerForm {
  // 基础字段 - 对应API接口
  id?: string
  projectId?: string
  projectName?: string // 用于显示项目名称
  customerType?: number // 1个人 2企业
  customerName?: string
  creditCode?: string
  legalName?: string
  contactPhone?: string
  idType?: string
  idNumber?: string
  idValidityStart?: string
  idValidityEnd?: string
  idValidPeriod?: [string, string] // 用于日期选择器
  idFront?: string
  idBack?: string
  businessLicense?: string // 营业执照地址
  contactAddress?: string
  ownerId?: string
  ownerName?: string
  attachmentFiles?: string // 保持string类型用于绑定
  remark?: string
  
  // 文件对象存储，用于识别
  idFrontFile?: File
  idBackFile?: File
  businessLicenseFile?: File
  
  // 子表数据 - 使用表格组件期望的类型
  contactList?: TableContact[]
  guarantorList?: TableGuarantor[]
  bankAccountList?: TableBankAccount[]
  invoiceList?: TableInvoice[]
}

const emit = defineEmits(['success'])

const visible = ref(false)
const mode = ref<'add' | 'edit' | 'view'>('add')
const loading = ref(false)
const formRef = ref()

const formData = reactive<CustomerForm>({
  customerType: 1,
  customerName: '',
  contactPhone: '',
  contactAddress: '',
  remark: '',
  attachmentFiles: '',
  contactList: [],
  guarantorList: [],
  bankAccountList: [],
  invoiceList: [],
})

// 为表格组件提供安全的数组数据
const safeContactList = computed({
  get: () => formData.contactList || [],
  set: (value) => { formData.contactList = value }
})

const safeGuarantorList = computed({
  get: () => formData.guarantorList || [],
  set: (value) => { formData.guarantorList = value }
})

const safeBankAccountList = computed({
  get: () => formData.bankAccountList || [],
  set: (value) => { formData.bankAccountList = value }
})

const safeInvoiceList = computed({
  get: () => formData.invoiceList || [],
  set: (value) => { formData.invoiceList = value }
})

const drawerTitle = computed(() => {
  const titles = {
    add: '新增客户',
    edit: '编辑客户',
    view: '查看客户'
  }
  return titles[mode.value]
})



const rules = computed(() => {
  const baseRules = {
    projectId: [{ required: true, message: '请选择项目名称' }],
    customerType: [{ required: true, message: '请选择客户类型' }],
    customerName: [{ required: true, message: '请输入客户名称' }]
  }

  if (formData.customerType === 1) {
    // 个人客户
    return {
      ...baseRules,
      contactPhone: [
        { required: true, message: '请输入联系电话' },
        { 
          validator: (value: string, cb: any) => {
            if (!value) {
              cb()
              return
            }
            const phonePattern = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/
            if (!phonePattern.test(value)) {
              cb('请输入正确的电话号码')
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }
      ],
      idType: [{ required: true, message: '请选择证件类型' }],
      idNumber: [
        { required: true, message: '请输入证件号码' },
        { 
          validator: (value: string, cb: any) => {
            if (formData.idType === '1') {
              // 身份证验证：15位或18位，最后一位可为X
              const idPattern = /^(\d{15}|\d{17}[\dXx])$/
              if (!idPattern.test(value)) {
                cb('请输入正确的身份证号码')
                return
              }
            }
            cb()
          },
          trigger: 'blur'
        }
      ]
    }
  } else {
    // 企业客户
    return {
      ...baseRules,
      contactPhone: [
        { 
          validator: (value: string, cb: any) => {
            if (!value) {
              cb()
              return
            }
            const phonePattern = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/
            if (!phonePattern.test(value)) {
              cb('请输入正确的电话号码')
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }
      ],
      creditCode: [
        { required: true, message: '请输入统一社会信用代码' },
        { 
          validator: (value: string, cb: any) => {
            if (!value) {
              cb()
              return
            }
            const creditCodePattern = /^[0-9A-Z]{18}$/
            if (!creditCodePattern.test(value)) {
              cb('请输入正确的统一社会信用代码')
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }
      ],
      legalName: [{ required: true, message: '请输入法人姓名' }],
      idNumber: [
        { 
          validator: (value: string, cb: any) => {
            if (!value) {
              cb()
              return
            }
            if (formData.idType === '1') {
              // 身份证验证：15位或18位，最后一位可为X
              const idPattern = /^(\d{15}|\d{17}[\dXx])$/
              if (!idPattern.test(value)) {
                cb('请输入正确的身份证号码')
                return
              }
            }
            cb()
          },
          trigger: 'blur'
        }
      ]
    }
  }
})

const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    projectId: undefined,
    projectName: '',
    customerType: 1,
    customerName: '',
    creditCode: '',
    legalName: '',
    contactPhone: '',
    idType: '',
    idNumber: '',
    idValidityStart: '',
    idValidityEnd: '',
    idValidPeriod: undefined,
    idFront: '',
    idBack: '',
    contactAddress: '',
    ownerId: '',
    ownerName: '',
    attachmentFiles: '',
    remark: '',
    businessLicense: '',
    // 清空文件对象
    idFrontFile: undefined,
    idBackFile: undefined,
    businessLicenseFile: undefined,
    contactList: [],
    guarantorList: [],
    bankAccountList: [],
    invoiceList: [],
  })
}

const handleTypeChange = () => {
  // 切换客户类型时清空相关字段
  if (formData.customerType === 1) {
    formData.creditCode = ''
    formData.legalName = ''
    formData.businessLicense = ''
    formData.invoiceList = []
    // 个人客户不需要首选联系人，清除所有首选状态
    if (formData.contactList) {
      formData.contactList.forEach(contact => {
        contact.isPreferred = false
      })
    }
  } else {
    formData.idType = ''
    formData.idNumber = ''
    formData.idValidPeriod = undefined
    formData.idFront = ''
    formData.idBack = ''
    // 企业客户需要确保有首选联系人
    setTimeout(() => {
      ensurePreferredContact()
    }, 0)
  }
}

// 项目选择
const handleProjectChange = (value: string | number, selectedOrg: any) => {
  formData.projectId = value ? String(value) : undefined
  formData.projectName = selectedOrg?.name || ''
}

// 证件类型改变时触发证件号码重新校验
const handleIdTypeChange = () => {
  // 如果已经输入了证件号码，触发重新校验
  if (formData.idNumber) {
    nextTick(() => {
      formRef.value?.validateField('idNumber')
    })
  }
}

// 身份证上传处理
const handleIdCardFrontUpload = async (option: any) => {
  const { onProgress, onSuccess, onError, fileItem } = option
  
  try {
    onProgress({ percent: 50 })
    
    const formDataUpload = new FormData()
    formDataUpload.append('file', fileItem.file)
    
    const response = await uploadFileApi(formDataUpload)
    
    if (response.data && !response.data.error) {
      const fileUrl = response.data.fileUrl || response.data.data || response.data.url
      
      // 设置身份证正面
      formData.idFront = fileUrl
      formData.idFrontFile = fileItem.file
      
      onProgress({ percent: 100 })
      onSuccess(response)
    } else {
      throw new Error(response.data?.msg || '上传失败')
    }
  } catch (error) {
    console.error('身份证正面上传失败:', error)
    onError(error)
  }
}

const handleIdCardBackUpload = async (option: any) => {
  const { onProgress, onSuccess, onError, fileItem } = option
  
  try {
    onProgress({ percent: 50 })
    
    const formDataUpload = new FormData()
    formDataUpload.append('file', fileItem.file)
    
    const response = await uploadFileApi(formDataUpload)
    
    if (response.data && !response.data.error) {
      const fileUrl = response.data.fileUrl || response.data.data || response.data.url
      
      // 设置身份证反面
      formData.idBack = fileUrl
      formData.idBackFile = fileItem.file
      
      onProgress({ percent: 100 })
      onSuccess(response)
    } else {
      throw new Error(response.data?.msg || '上传失败')
    }
  } catch (error) {
    console.error('身份证反面上传失败:', error)
    onError(error)
  }
}

// 营业执照上传处理
const handleBusinessLicenseUpload = async (option: any) => {
  const { onProgress, onSuccess, onError, fileItem } = option
  
  try {
    onProgress({ percent: 50 })
    
    const formDataUpload = new FormData()
    formDataUpload.append('file', fileItem.file)
    
    const response = await uploadFileApi(formDataUpload)
    
    if (response.data && !response.data.error) {
      const fileUrl = response.data.fileUrl || response.data.data || response.data.url
      
      // 设置营业执照
      formData.businessLicense = fileUrl
      formData.businessLicenseFile = fileItem.file
      
      onProgress({ percent: 100 })
      onSuccess(response)
    } else {
      throw new Error(response.data?.msg || '上传失败')
    }
  } catch (error) {
    console.error('营业执照上传失败:', error)
    onError(error)
  }
}

// 身份证识别
const handleIdCardRecognition = async () => {
  // 检查是否至少上传了一张身份证图片
  if (!formData.idFront && !formData.idBack) {
    Message.warning('请先上传身份证正面或反面')
    return
  }
  
  if (!formData.idFrontFile && !formData.idBackFile) {
    Message.warning('请重新上传身份证文件')
    return
  }
  
  try {
    loading.value = true
    
    let frontResults = null
    let backResults = null
    
    // 生成当前日期时间戳
    const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '')
    
    // 如果上传了正面，则识别正面 (type: 1)
    if (formData.idFrontFile) {
      const frontFormData = new FormData()
      // 创建新的文件对象，修改文件名
      const frontFile = new File([formData.idFrontFile], `idcard_front_${timestamp}.${formData.idFrontFile.name.split('.').pop()}`, {
        type: formData.idFrontFile.type
      })
      frontFormData.append('file', frontFile)
      frontResults = await analyzeImage(frontFormData, 1)
    }
    
    // 如果上传了反面，则识别反面 (type: 2)
    if (formData.idBackFile) {
      const backFormData = new FormData()
      // 创建新的文件对象，修改文件名
      const backFile = new File([formData.idBackFile], `idcard_back_${timestamp}.${formData.idBackFile.name.split('.').pop()}`, {
        type: formData.idBackFile.type
      })
      backFormData.append('file', backFile)
      backResults = await analyzeImage(backFormData, 2)
    }
    
    // 存储识别前的字段值，用于判断是否有变化
    const fieldsToValidate: string[] = []
    
    // 处理正面识别结果
    if (frontResults?.data) {
      const frontData = frontResults.data
      if (frontData.name && frontData.name !== formData.customerName) {
        formData.customerName = frontData.name
        fieldsToValidate.push('customerName')
      }
      if (frontData.idCard && frontData.idCard !== formData.idNumber) {
        formData.idNumber = frontData.idCard
        fieldsToValidate.push('idNumber')
      }
      if (frontData.address && frontData.address !== formData.contactAddress) {
        formData.contactAddress = frontData.address
      }
    }
    
    // 处理反面识别结果
    if (backResults?.data) {
      const backData = backResults.data
      if (backData.validatePeriod) {
        // 解析有效期格式 "2004.10.27-2024.10.26"
        const periods = backData.validatePeriod.split('-')
        if (periods.length === 2) {
          // 转换日期格式从 "2004.10.27" 到 "2004-10-27"
          const startDate = periods[0].replace(/\./g, '-')
          const endDate = periods[1].replace(/\./g, '-')
          formData.idValidPeriod = [startDate, endDate]
        }
      }
    }
    
    // 触发相关字段的校验，清除错误提示
    await nextTick()
    for (const field of fieldsToValidate) {
      formRef.value?.validateField(field)
    }
    
    // 根据识别的内容显示不同的成功消息
    let successMessage = '身份证识别成功'
    if (frontResults && backResults) {
      successMessage = '身份证正反面识别成功'
    } else if (frontResults) {
      successMessage = '身份证正面识别成功'
    } else if (backResults) {
      successMessage = '身份证反面识别成功'
    }
    
    Message.success(successMessage)
  } catch (error) {
    console.error('身份证识别失败:', error)
  } finally {
    loading.value = false
  }
}

// 营业执照识别
const handleBusinessLicenseRecognition = async () => {
  if (!formData.businessLicense) {
    Message.warning('请先上传营业执照')
    return
  }
  
  if (!formData.businessLicenseFile) {
    Message.warning('请重新上传营业执照文件')
    return
  }
  
  try {
    loading.value = true
    
    // 生成当前日期时间戳
    const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '')
    
    // 识别营业执照 (type: 2)
    const licenseFormData = new FormData()
    // 创建新的文件对象，修改文件名
    const licenseFile = new File([formData.businessLicenseFile], `business_license_${timestamp}.${formData.businessLicenseFile.name.split('.').pop()}`, {
      type: formData.businessLicenseFile.type
    })
    licenseFormData.append('file', licenseFile)
    
    const results = await analyzeImage(licenseFormData, 3)
    
    // 存储需要校验的字段
    const fieldsToValidate: string[] = []
    
    // 处理识别结果
    if (results?.data) {
      const licenseData = results.data
      if (licenseData.enterpriseName && licenseData.enterpriseName !== formData.customerName) {
        formData.customerName = licenseData.enterpriseName
        fieldsToValidate.push('customerName')
      }
      if (licenseData.unifiedSocialCreditCode && licenseData.unifiedSocialCreditCode !== formData.creditCode) {
        formData.creditCode = licenseData.unifiedSocialCreditCode
        fieldsToValidate.push('creditCode')
      }
      if (licenseData.enterpriseLegalName && licenseData.enterpriseLegalName !== formData.legalName) {
        formData.legalName = licenseData.enterpriseLegalName
        fieldsToValidate.push('legalName')
      }
      if (licenseData.address && licenseData.address !== formData.contactAddress) {
        formData.contactAddress = licenseData.address
      }
    }
    
    // 触发相关字段的校验，清除错误提示
    await nextTick()
    for (const field of fieldsToValidate) {
      formRef.value?.validateField(field)
    }
    
    Message.success('营业执照识别成功')
  } catch (error) {
    console.error('营业执照识别失败:', error)
  } finally {
    loading.value = false
  }
}

// 检查表格是否有未保存的行
const checkUnsavedRows = () => {
  const unsavedTables: string[] = []
  
  // 检查联系人表格
  if (formData.contactList?.some(item => item.isEditing)) {
    unsavedTables.push('联系人')
  }
  
  // 检查担保人表格
  if (formData.guarantorList?.some(item => item.isEditing)) {
    unsavedTables.push('担保人')
  }
  
  // 检查银行账号表格
  if (formData.bankAccountList?.some(item => item.isEditing)) {
    unsavedTables.push('银行账号')
  }
  
  // 检查开票信息表格
  if (formData.invoiceList?.some(item => item.isEditing)) {
    unsavedTables.push('开票信息')
  }
  
  return unsavedTables
}

// 添加表格行的方法
const addContact = () => {
  if (!formData.contactList) formData.contactList = []
  
  // 如果是企业客户，新增联系人时需要处理首选联系人逻辑
  const isPreferred = formData.customerType === 2 && formData.contactList.length === 0
  
  formData.contactList.push({
    id: Date.now(),
    name: '',
    phone: '',
    gender: 1, // 1男 2女 3未知
    relationship: undefined, // 关系字段，非必填
    idNumber: '',
    position: '', // 企业客户适用
    department: '', // 企业客户适用
    isPreferred: isPreferred, // 企业客户的第一个联系人默认为首选
    remark: '',
    isEditing: true
  })
}

const addAgent = () => {
  // 企业客户的经办人也使用contactList，通过position字段区分
  if (!formData.contactList) formData.contactList = []
  
  // 检查是否已有首选联系人，如果没有则设置当前为首选
  const hasPreferred = formData.contactList.some(contact => contact.isPreferred)
  const isPreferred = formData.customerType === 2 && !hasPreferred
  
  formData.contactList.push({
    id: Date.now(),
    name: '',
    phone: '',
    gender: 1,
    relationship: undefined, // 关系字段，非必填
    idNumber: '',
    position: '',
    department: '',
    isPreferred: isPreferred, // 如果没有首选联系人，则设置为首选
    remark: '',
    isEditing: true
  })
}

const addInvoice = () => {
  if (!formData.invoiceList) formData.invoiceList = []
  formData.invoiceList.push({
    id: Date.now(),
    title: '',
    taxNumber: '',
    address: '',
    phone: '',
    bankName: '',
    accountNumber: '',
    isEditing: true
  })
}

const addGuarantor = () => {
  if (!formData.guarantorList) formData.guarantorList = []
  formData.guarantorList.push({
    id: Date.now(),
    name: '',
    phone: '',
    idType: '1',
    idNumber: '',
    address: '',
    remark: '',
    isEditing: true
  })
}

const addBankAccount = () => {
  if (!formData.bankAccountList) formData.bankAccountList = []
  formData.bankAccountList.push({
    id: Date.now(),
    bankName: '',
    accountNumber: '',
    accountRemark: '',
    isEditing: true
  })
}

// 设置首选联系人的方法
const setPreferredContact = (targetId: string | number) => {
  if (!formData.contactList) return
  
  // 只有企业客户才需要首选联系人
  if (formData.customerType !== 2) return
  
  formData.contactList.forEach(contact => {
    contact.isPreferred = contact.id === targetId
  })
}

// 确保企业客户始终有一个首选联系人
const ensurePreferredContact = () => {
  if (formData.customerType !== 2 || !formData.contactList || formData.contactList.length === 0) {
    return
  }
  
  // 检查是否有首选联系人
  const hasPreferred = formData.contactList.some(contact => contact.isPreferred)
  
  // 如果没有首选联系人，将最新的一条设置为首选
  if (!hasPreferred) {
    // 找到最新添加的联系人（id最大的）
    const latestContact = formData.contactList.reduce((latest, current) => {
      const latestId = typeof latest.id === 'string' ? parseInt(latest.id) : latest.id
      const currentId = typeof current.id === 'string' ? parseInt(current.id) : current.id
      return currentId > latestId ? current : latest
    })
    
    if (latestContact) {
      latestContact.isPreferred = true
    }
  }
}

// 监听联系人列表变化，确保企业客户始终有首选联系人
watch(
  () => formData.contactList,
  (newList) => {
    if (formData.customerType === 2 && newList && newList.length > 0) {
      // 延迟执行，确保删除操作完成后再检查
      setTimeout(() => {
        ensurePreferredContact()
      }, 0)
    }
  },
  { deep: true }
)

const show = async (type: 'add' | 'edit' | 'view', data?: any) => {
  mode.value = type
  visible.value = true
  
  if (type === 'add') {
    resetForm()
    formData.projectId = data?.id || ''
    formData.projectName = data?.name || ''
  } else if (data?.id) {
    try {
      loading.value = true
      const response = await customerApi.getCustomerDetail(data.id)
      if (response.data) {
        const customerData = response.data
        
        // 处理附件数据，过滤掉身份证和营业执照
        let filteredAttachmentFiles = ''
        if (customerData.attachmentFiles) {
          try {
            const allAttachments = JSON.parse(customerData.attachmentFiles)
            if (Array.isArray(allAttachments)) {
              const otherAttachments = allAttachments.filter(file => 
                file.fileType === '其他' || 
                (!file.fileName?.includes('身份证') && !file.fileName?.includes('营业执照'))
              )
              filteredAttachmentFiles = otherAttachments.length > 0 ? JSON.stringify(otherAttachments) : ''
            }
          } catch (error) {
            console.warn('解析附件数据失败:', error)
          }
        }
        
        Object.assign(formData, {
          ...customerData,
          // 处理日期范围
          idValidPeriod: customerData.idValidityStart && customerData.idValidityEnd 
            ? [customerData.idValidityStart, customerData.idValidityEnd] 
            : undefined,
          // 使用API返回的项目名称
          projectName: customerData.projectName,
          // 只显示其他附件，不显示身份证和营业执照
          attachmentFiles: filteredAttachmentFiles
        })
        
        // 确保企业客户有首选联系人
        if (formData.customerType === 2) {
          ensurePreferredContact()
        }
      }
    } catch (error) {
      console.error('获取客户详情失败:', error)
    } finally {
      loading.value = false
    }
  }
}

const handleCancel = () => {
  visible.value = false
  resetForm()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  try {
    // 根据开发规范进行表单验证
    const errors = await formRef.value?.validate()
    if (errors) return
    
    // 检查是否有未保存的表格行
    const unsavedTables = checkUnsavedRows()
    if (unsavedTables.length > 0) {
      Message.warning(`请先保存或删除${unsavedTables.join('、')}表格中的编辑行`)
      return
    }
    
    loading.value = true
    
    // 准备提交数据，只传递必要的业务字段
    const submitData: CustomerAddDTO = {
      // 基础字段
      id: formData.id,
      projectId: formData.projectId,
      projectName: formData.projectName,
      customerType: formData.customerType,
      customerName: formData.customerName,
      creditCode: formData.creditCode,
      legalName: formData.legalName,
      contactPhone: formData.contactPhone,
      idType: formData.idType,
      idNumber: formData.idNumber,
      idValidityStart: formData.idValidPeriod?.[0],
      idValidityEnd: formData.idValidPeriod?.[1],
      idFront: formData.idFront,
      idBack: formData.idBack,
      businessLicense: formData.businessLicense,
      contactAddress: formData.contactAddress,
      ownerId: formData.ownerId,
      ownerName: formData.ownerName,
      remark: formData.remark,
      // 处理附件信息，包含身份证和营业执照，空字符串转换为null
      attachmentFiles: prepareAttachmentFiles() || null,
      // 转换表格数据为API格式
      contactList: formData.contactList?.map(item => ({
        id: typeof item.id === 'number' ? item.id.toString() : item.id,
        name: item.name,
        phone: item.phone,
        gender: item.gender,
        relationship: item.relationship,
        idNumber: item.idNumber,
        position: item.position,
        department: item.department,
        isPreferred: item.isPreferred,
        remark: item.remark
      })),
      guarantorList: formData.guarantorList?.map(item => ({
        id: item.id.toString(),
        name: item.name,
        phone: item.phone,
        idType: parseInt(item.idType),
        idNumber: item.idNumber,
        address: item.address,
        remark: item.remark
      })),
      bankAccountList: formData.bankAccountList?.map(item => ({
        id: item.id.toString(),
        bankName: item.bankName,
        accountNumber: item.accountNumber,
        accountRemark: item.accountRemark
      })),
      invoiceList: formData.invoiceList?.map(item => ({
        id: item.id.toString(),
        title: item.title,
        taxNumber: item.taxNumber,
        phone: item.phone,
        address: item.address,
        bankName: item.bankName,
        accountNumber: item.accountNumber
      }))
    }
    
    if (mode.value === 'add') {
      await customerApi.addCustomer(submitData)
      Message.success('新增成功')
    } else {
      await customerApi.editCustomer(submitData)
      Message.success('保存成功')
    }
    
    visible.value = false
    emit('success')
    resetForm()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

const handleContactDeleted = () => {
  // 联系人删除后，确保企业客户仍有首选联系人
  if (formData.customerType === 2) {
    setTimeout(() => {
      ensurePreferredContact()
    }, 0)
  }
}

// 准备附件信息，包含身份证、营业执照和其他附件
const prepareAttachmentFiles = (): string => {
  const attachments: Array<{
    fileName: string
    fileUrl: string
    fileType: '客户身份证' | '营业执照' | '其他'
  }> = []
  
  // 添加身份证正面
  if (formData.idFront) {
    attachments.push({
      fileName: '身份证正面',
      fileUrl: formData.idFront,
      fileType: '客户身份证'
    })
  }
  
  // 添加身份证反面
  if (formData.idBack) {
    attachments.push({
      fileName: '身份证反面',
      fileUrl: formData.idBack,
      fileType: '客户身份证'
    })
  }
  
  // 添加营业执照
  if (formData.businessLicense) {
    attachments.push({
      fileName: '营业执照',
      fileUrl: formData.businessLicense,
      fileType: '营业执照'
    })
  }
  
  // 添加其他附件（从attachmentFiles字段解析）
  if (formData.attachmentFiles) {
    try {
      const otherFiles = JSON.parse(formData.attachmentFiles)
      if (Array.isArray(otherFiles)) {
        otherFiles.forEach(file => {
          // 只添加非身份证和营业执照的附件
          if (file.fileName && file.fileUrl && 
              !file.fileName.includes('身份证') && 
              !file.fileName.includes('营业执照')) {
            attachments.push({
              fileName: file.fileName,
              fileUrl: file.fileUrl,
              fileType: '其他'
            })
          }
        })
      }
    } catch (error) {
      console.warn('解析附件文件失败:', error)
    }
  }
  
  return attachments.length > 0 ? JSON.stringify(attachments) : ''
}

defineExpose({
  show,
  setPreferredContact
})
</script>

<style scoped lang="less">
:deep(.arco-form-item-label) {
  font-weight: 500;
}

:deep(.arco-drawer-body) {
  padding: 16px;
}

:deep(.section-title) {
  margin: 16px 0;
  &.first-child{
    margin-top: 8px;
  }
}

.upload-card {
  width: 100%;
  height: 100%;
  padding: 0 130px;
  box-sizing: border-box;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  :deep(.arco-card-body) {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0;
  }
}

.card-title {
  font-size: 14px;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: Regular;
  color: #333333;
  line-height: 17px;
  margin-bottom: 11px;
}

.card-item {
  position: relative;
  width: 160px;
  height: 141px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  &:hover {
    border-color: rgb(var(--primary-6));
  }

  .card-bg {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .card-text {
    position: absolute;
    bottom: 11.5px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: Regular;
    text-align: center;
    color: #ffffff;
    line-height: 12px;
    letter-spacing: 0px;
  }

  &.business-license {
    width: 227px;
    height: 200px;
    .card-text{
      line-height: 30px;
      color: #ffffff;
      font-size: 18px;
    }
  }
}

.card-img {
  text-align: left;
  img {
    width: 336px;
    height: 91px;
  }
}

.button-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 22px;
  :deep(.arco-btn) {
    width: 131px;
    height: 32px;
    border-radius: 6px;
  }
}
.card-business{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .button-bar{
    margin-left: 30px;
    padding: 0;
  }
}
</style> 