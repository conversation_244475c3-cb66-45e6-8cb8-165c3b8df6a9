---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/财务退款

<a id="opIdsave_4"></a>

## POST 保存退款单接口

POST /financialRefund/save

保存退款单接口

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "refundNo": "string",
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "recordAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "processId": "string",
  "refundStatus": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "recordStatus": 0,
  "confirmStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "isThirdAgent": true,
  "agentAttachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "isDel": true,
  "isSubmit": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialRefundAddDTO](#schemafinancialrefundadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdsaveRecord"></a>

## POST 退款记账接口

POST /financialRefund/saveRecord

退款记账接口

> Body 请求参数

```json
{
  "refundId": "string",
  "flowRelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "pendingAmount": 0,
      "acctAmount": 0,
      "carryoverAmount": 0,
      "remark": "string",
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RefundSaveRecordDTO](#schemarefundsaverecorddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_2"></a>

## POST 查询财务退款列表

POST /financialRefund/list

查询财务退款列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "refundNo": "string",
  "refundTypes": "string",
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "recordAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "processId": "string",
  "refundStatus": 0,
  "refundStatuses": "string",
  "approveStatus": 0,
  "recordStatus": 0,
  "confirmStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "isThirdAgent": true,
  "agentAttachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "applyTimeStart": "2019-08-24T14:15:22Z",
  "applyTimeEnd": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialRefundQueryDTO](#schemafinancialrefundquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdexport_5"></a>

## POST 导出询财务退款列表

POST /financialRefund/export

导出询财务退款列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|refundType|query|integer(int32)| 否 | 退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|refundNo|query|string| 否 | 退款单号|退款单号|
|refundTypes|query|string| 否 | 退款类型（多选逗号拼接）|退款类型（多选逗号拼接）|
|bizId|query|string| 否 | 业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|query|string| 否 | 退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|query|string(date-time)| 否 | 退款申请时间|退款申请时间|
|refundAmount|query|number| 否 | 退款金额|退款金额|
|recordAmount|query|number| 否 | 已记账金额|已记账金额|
|feeType|query|string| 否 | 退款费用类型|退款费用类型|
|refundWay|query|integer(int32)| 否 | 退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|query|string| 否 | 收款方姓名|收款方姓名|
|receiverBank|query|string| 否 | 收款方开户行|收款方开户行|
|receiverAccount|query|string| 否 | 收款方银行账号|收款方银行账号|
|refundRemark|query|string| 否 | 退款申请说明|退款申请说明|
|refundTime|query|string(date-time)| 否 | 退款时间|退款时间|
|processId|query|string| 否 | 审批流程id|审批流程id|
|refundStatus|query|integer(int32)| 否 | 退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|
|refundStatuses|query|string| 否 | 退款单状态（多选逗号拼接）|退款单状态（多选逗号拼接）|
|approveStatus|query|integer(int32)| 否 | 审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|recordStatus|query|integer(int32)| 否 | 记账状态: 0-未记账,1-已记账|记账状态: 0-未记账,1-已记账|
|confirmStatus|query|integer(int32)| 否 | 确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|roomYardStatus|query|integer(int32)| 否 | 一房一码退款状态|一房一码退款状态|
|attachments|query|string| 否 | 附件|附件|
|isThirdAgent|query|boolean| 否 | 是否第三方代收: 0-否,1-是|是否第三方代收: 0-否,1-是|
|agentAttachments|query|string| 否 | 退款申请单|退款申请单|
|cancelTime|query|string(date-time)| 否 | 作废时间|作废时间|
|cancelBy|query|string| 否 | 作废人|作废人|
|cancelByName|query|string| 否 | 作废人姓名|作废人姓名|
|cancelReason|query|string| 否 | 作废原因|作废原因|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|applyTimeStart|query|string(date-time)| 否 | 申请时间开始|申请时间开始|
|applyTimeEnd|query|string(date-time)| 否 | 申请时间结束|申请时间结束|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdconfirmRecord"></a>

## POST 退款确认记账接口

POST /financialRefund/confirmRecord

退款确认记账接口

> Body 请求参数

```json
{
  "refundId": "string",
  "isOneKeyConfirm": true,
  "flowRelId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RefundConfirmRecordDTO](#schemarefundconfirmrecorddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdcancel_1"></a>

## POST 作废退款单接口

POST /financialRefund/cancel

作废退款单接口

> Body 请求参数

```json
{
  "refundId": "string",
  "cancelReason": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[FinancialFlowCancelDTO](#schemafinancialflowcanceldto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddownloadTemplate"></a>

## GET 下载退款模版接口

GET /financialRefund/downloadTemplate

下载退款模版接口

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddetail_3"></a>

## GET 获取退款单详情接口

GET /financialRefund/detail

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|refundId|query|string| 否 ||退款单ID|
|refundType|query|integer(int32)| 否 ||退款类型|
|bizId|query|string| 否 ||业务ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"refund":{},"exit":{},"booking":{"id":"string","projectId":"string","projectName":"string","customerName":"string","propertyType":"string","roomId":"string","roomName":"string","bookingNo":"string","bookingAmount":0,"unpaidAmount":0,"receivableDate":"2019-08-24T14:15:22Z","expectSignDate":"2019-08-24T14:15:22Z","isRefundable":0,"cancelTime":"2019-08-24T14:15:22Z","cancelBy":"string","cancelByName":"string","cancelReason":0,"isRefund":0,"cancelEnclosure":"string","cancelRemark":"string","status":0,"contractId":"string","refundId":"string","createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"contractNo":"string","signDate":"2019-08-24T14:15:22Z","contractLeaseUnit":"string","lesseeName":"string","receivedAmount":0,"receivedDate":"2019-08-24T14:15:22Z","payMethod":"string"},"flow":{"id":"string","projectId":"string","orgName":"string","projectName":"string","orderNo":"string","payDirection":0,"payType":0,"payMethod":"string","targetType":0,"target":"string","targetId":"string","entryTime":"2019-08-24T14:15:22Z","status":0,"amount":0,"usedAmount":0,"payerName":"string","payerPhone":"string","payerAccount":"string","payerCard":"string","payRemark":"string","merchant":"string","payChannel":"string","sourceNo":"string","isOtherIncome":true,"otherIncomeDesc":"string","refundId":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true},"flowRels":[{"id":"string","costId":"string","refundId":"string","flowId":"string","flowNo":"string","type":0,"confirmStatus":0,"confirmTime":"2019-08-24T14:15:22Z","confirmUserId":"string","confirmUserName":"string","payAmount":0,"pendingAmount":0,"acctAmount":0,"carryoverAmount":0,"remark":"string","createByName":"string","createTime":"string","updateByName":"string","isDel":true,"projectId":"string","projectName":"string","entryTime":"2019-08-24T14:15:22Z","payType":0,"payMethod":"string","orderNo":"string","usedAmount":0,"payerName":"string","target":"string","merchant":"string","cumulativeAcctAmount":0}],"flowLogList":[{"id":"string","costId":"string","refundId":"string","flowId":"string","flowNo":"string","type":0,"carryoverId":"string","carryoverNo":"string","amount":0,"createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[FinancialRefundDetailVo](#schemafinancialrefunddetailvo)|

<a id="opIdremove_6"></a>

## DELETE 删除退款单接口

DELETE /financialRefund/delete

删除退款单接口

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||退款单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_CostFlowRelAddDTO">CostFlowRelAddDTO</h2>

<a id="schemacostflowreladddto"></a>
<a id="schema_CostFlowRelAddDTO"></a>
<a id="tocScostflowreladddto"></a>
<a id="tocscostflowreladddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "costId": "string",
  "refundId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "confirmStatus": 0,
  "confirmTime": "2019-08-24T14:15:22Z",
  "confirmUserId": "string",
  "confirmUserName": "string",
  "payAmount": 0,
  "pendingAmount": 0,
  "acctAmount": 0,
  "carryoverAmount": 0,
  "remark": "string",
  "isDel": true
}

```

记账记录列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|costId|string|false|none|账单id|账单id|
|refundId|string|false|none|退款单id|退款单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|账单类型：1-手动记账（收款）、2-自动记账（收款）、3-手动记账（退款）、4-自动记账（退款）、5-结转（转入）、6-结转（转出）|账单类型：1-手动记账（收款）、2-自动记账（收款）、3-手动记账（退款）、4-自动记账（退款）、5-结转（转入）、6-结转（转出）|
|confirmStatus|integer(int32)|false|none|确认状态: 0-未确认、1-自动确认、2-手动确认|确认状态: 0-未确认、1-自动确认、2-手动确认|
|confirmTime|string(date-time)|false|none|确认时间|确认时间|
|confirmUserId|string|false|none|确认人id|确认人id|
|confirmUserName|string|false|none|确认人姓名|确认人姓名|
|payAmount|number|false|none|支付金额|支付金额|
|pendingAmount|number|false|none|账单待收金额|账单待收金额|
|acctAmount|number|false|none|本次记账金额|本次记账金额|
|carryoverAmount|number|false|none|结转金额|结转金额|
|remark|string|false|none|备注|备注|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_FinancialRefundAddDTO">FinancialRefundAddDTO</h2>

<a id="schemafinancialrefundadddto"></a>
<a id="schema_FinancialRefundAddDTO"></a>
<a id="tocSfinancialrefundadddto"></a>
<a id="tocsfinancialrefundadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "refundNo": "string",
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "recordAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "processId": "string",
  "refundStatus": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "recordStatus": 0,
  "confirmStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "isThirdAgent": true,
  "agentAttachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "isDel": true,
  "isSubmit": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|refundType|integer(int32)|false|none|退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|refundNo|string|false|none|退款单号|退款单号|
|bizId|string|false|none|业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|string|false|none|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|string(date-time)|false|none|退款申请时间|退款申请时间|
|refundAmount|number|false|none|退款金额|退款金额|
|recordAmount|number|false|none|已记账金额|已记账金额|
|feeType|string|false|none|退款费用类型|退款费用类型|
|refundWay|integer(int32)|false|none|退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|string|false|none|收款方姓名|收款方姓名|
|receiverBank|string|false|none|收款方开户行|收款方开户行|
|receiverAccount|string|false|none|收款方银行账号|收款方银行账号|
|refundRemark|string|false|none|退款申请说明|退款申请说明|
|refundTime|string(date-time)|false|none|退款时间|退款时间|
|processId|string|false|none|审批流程id|审批流程id|
|refundStatus|integer(int32)|false|none|退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|
|approveStatus|integer(int32)|false|none|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|approveTime|string(date-time)|false|none|审批通过时间|审批通过时间|
|recordStatus|integer(int32)|false|none|记账状态: 0-未记账,1-已记账|记账状态: 0-未记账,1-已记账|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|roomYardStatus|integer(int32)|false|none|一房一码退款状态|一房一码退款状态|
|attachments|string|false|none|附件|附件|
|isThirdAgent|boolean|false|none|是否第三方代收: 0-否,1-是|是否第三方代收: 0-否,1-是|
|agentAttachments|string|false|none|退款申请单|退款申请单|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|string|false|none|作废原因|作废原因|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|isSubmit|boolean|false|none|0-暂存,1-提交|0-暂存,1-提交|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_ContractTerminateCostVo">ContractTerminateCostVo</h2>

<a id="schemacontractterminatecostvo"></a>
<a id="schema_ContractTerminateCostVo"></a>
<a id="tocScontractterminatecostvo"></a>
<a id="tocscontractterminatecostvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "terminateId": "string",
  "costId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "terminateReceivable": 0,
  "receivedAmount": 0,
  "penaltyAmount": 0,
  "refundAmount": 0,
  "freeId": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

退租费用VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|terminateId|string|false|none|退租单id|退租单id|
|costId|string|false|none|t_cost表id|t_cost表id|
|costType|integer(int32)|false|none|账单类型,1-免租期补收,2-退款信息|账单类型,1-免租期补收,2-退款信息|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|subjectId|string|false|none|科目id|科目id|
|subjectName|string|false|none|科目名|科目名|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|terminateReceivable|number|false|none|截止退租日应收（元）|截止退租日应收（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|penaltyAmount|number|false|none|罚没金额（元）|罚没金额（元）|
|refundAmount|number|false|none|预计退款金额（元）|预计退款金额（元）|
|freeId|integer(int32)|false|none|免租期t_contract_fee表id|免租期t_contract_fee表id|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_BookingVo">BookingVo</h2>

<a id="schemabookingvo"></a>
<a id="schema_BookingVo"></a>
<a id="tocSbookingvo"></a>
<a id="tocsbookingvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingNo": "string",
  "bookingAmount": 0,
  "unpaidAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "contractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "contractLeaseUnit": "string",
  "lesseeName": "string",
  "receivedAmount": 0,
  "receivedDate": "2019-08-24T14:15:22Z",
  "payMethod": "string"
}

```

定单基本信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|customerName|string|false|none|客户名称|客户名称|
|propertyType|string|false|none|意向物业类型|意向物业类型|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房源名称|房源名称|
|bookingNo|string|false|none|定单号|定单号|
|bookingAmount|number|false|none|定单金额|定单金额|
|unpaidAmount|number|false|none|待收金额|待收金额|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|cancelEnclosure|string|false|none|退定附件|退定附件|
|cancelRemark|string|false|none|退定说明|退定说明|
|status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|contractId|string|false|none|转签约合同id|转签约合同id|
|refundId|string|false|none|退款单id|退款单id|
|createBy|string|false|none|创建人|创建人|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建日期|创建日期|
|updateBy|string|false|none|更新人|更新人|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新日期|更新日期|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractNo|string|false|none|合同号|合同号|
|signDate|string(date-time)|false|none|合同签订日期|合同签订日期|
|contractLeaseUnit|string|false|none|租赁单元|租赁单元|
|lesseeName|string|false|none|承租人名称|承租人名称|
|receivedAmount|number|false|none|定单已收金额|定单已收金额|
|receivedDate|string(date-time)|false|none|实收日期|实收日期|
|payMethod|string|false|none|支付方式|支付方式|

<h2 id="tocS_FinancialRefundQueryDTO">FinancialRefundQueryDTO</h2>

<a id="schemafinancialrefundquerydto"></a>
<a id="schema_FinancialRefundQueryDTO"></a>
<a id="tocSfinancialrefundquerydto"></a>
<a id="tocsfinancialrefundquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "refundNo": "string",
  "refundTypes": "string",
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "recordAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "processId": "string",
  "refundStatus": 0,
  "refundStatuses": "string",
  "approveStatus": 0,
  "recordStatus": 0,
  "confirmStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "isThirdAgent": true,
  "agentAttachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "applyTimeStart": "2019-08-24T14:15:22Z",
  "applyTimeEnd": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|refundType|integer(int32)|false|none|退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|refundNo|string|false|none|退款单号|退款单号|
|refundTypes|string|false|none|退款类型（多选逗号拼接）|退款类型（多选逗号拼接）|
|bizId|string|false|none|业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|string|false|none|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|string(date-time)|false|none|退款申请时间|退款申请时间|
|refundAmount|number|false|none|退款金额|退款金额|
|recordAmount|number|false|none|已记账金额|已记账金额|
|feeType|string|false|none|退款费用类型|退款费用类型|
|refundWay|integer(int32)|false|none|退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|string|false|none|收款方姓名|收款方姓名|
|receiverBank|string|false|none|收款方开户行|收款方开户行|
|receiverAccount|string|false|none|收款方银行账号|收款方银行账号|
|refundRemark|string|false|none|退款申请说明|退款申请说明|
|refundTime|string(date-time)|false|none|退款时间|退款时间|
|processId|string|false|none|审批流程id|审批流程id|
|refundStatus|integer(int32)|false|none|退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|
|refundStatuses|string|false|none|退款单状态（多选逗号拼接）|退款单状态（多选逗号拼接）|
|approveStatus|integer(int32)|false|none|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|recordStatus|integer(int32)|false|none|记账状态: 0-未记账,1-已记账|记账状态: 0-未记账,1-已记账|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|roomYardStatus|integer(int32)|false|none|一房一码退款状态|一房一码退款状态|
|attachments|string|false|none|附件|附件|
|isThirdAgent|boolean|false|none|是否第三方代收: 0-否,1-是|是否第三方代收: 0-否,1-是|
|agentAttachments|string|false|none|退款申请单|退款申请单|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|string|false|none|作废原因|作废原因|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|applyTimeStart|string(date-time)|false|none|申请时间开始|申请时间开始|
|applyTimeEnd|string(date-time)|false|none|申请时间结束|申请时间结束|

<h2 id="tocS_ContractBookingVo">ContractBookingVo</h2>

<a id="schemacontractbookingvo"></a>
<a id="schema_ContractBookingVo"></a>
<a id="tocScontractbookingvo"></a>
<a id="tocscontractbookingvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "bookingId": "string",
  "bookedRoom": "string",
  "bookerName": "string",
  "bookingReceivedAmount": 0,
  "bookingPaymentDate": "2019-08-24T14:15:22Z",
  "transferBondAmount": 0,
  "transferRentAmount": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同定单列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|bookingId|string|false|none|定单id|定单id|
|bookedRoom|string|false|none|预定房源|预定房源|
|bookerName|string|false|none|预定人姓名|预定人姓名|
|bookingReceivedAmount|number|false|none|定单已收金额|定单已收金额|
|bookingPaymentDate|string(date-time)|false|none|定单收款(生效)日期|定单收款(生效)日期|
|transferBondAmount|number|false|none|转保证金金额|转保证金金额|
|transferRentAmount|number|false|none|转租金金额|转租金金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractCostVo">ContractCostVo</h2>

<a id="schemacontractcostvo"></a>
<a id="schema_ContractCostVo"></a>
<a id="tocScontractcostvo"></a>
<a id="tocscontractcostvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "period": 0,
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "unitPrice": 0,
  "priceUnit": 0,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "isRevenue": true,
  "isDiscount": true,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractStartDate": "2019-08-24T14:15:22Z",
  "rentTicketPeriod": 0
}

```

合同应收列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|period|integer(int32)|false|none|账单期数|账单期数|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|area|number|false|none|商铺面积|商铺面积|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途|收款用途|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|unitPrice|number|false|none|单价|单价|
|priceUnit|integer(int32)|false|none|单价单位|单价单位|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|isRevenue|boolean|false|none|是否营收提成,0-否,1-是|是否营收提成,0-否,1-是|
|isDiscount|boolean|false|none|是否优惠,0-否,1-是|是否优惠,0-否,1-是|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractStartDate|string(date-time)|false|none|合同开始日期, 用来推理租赁月|合同开始日期, 用来推理租赁月|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|

<h2 id="tocS_ContractCustomerVo">ContractCustomerVo</h2>

<a id="schemacontractcustomervo"></a>
<a id="schema_ContractCustomerVo"></a>
<a id="tocScontractcustomervo"></a>
<a id="tocscontractcustomervo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "customerId": "string",
  "customerType": 0,
  "customerName": "string",
  "address": "string",
  "phone": "string",
  "idType": "string",
  "idNumber": "string",
  "isEmployee": true,
  "creditCode": "string",
  "contactName": "string",
  "contactPhone": "string",
  "contactIdNumber": "string",
  "legalName": "string",
  "paymentAccount": "string",
  "paymentBank": "string",
  "guarantorName": "string",
  "guarantorPhone": "string",
  "guarantorIdType": "string",
  "guarantorIdNumber": "string",
  "guarantorAddress": "string",
  "guarantorIdFront": "string",
  "guarantorIdBack": "string",
  "invoiceTitle": "string",
  "invoiceTaxNumber": "string",
  "invoiceAddress": "string",
  "invoicePhone": "string",
  "invoiceBankName": "string",
  "invoiceAccountNumber": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同客户信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|customerId|string|false|none|客户id|客户id|
|customerType|integer(int32)|false|none|客户类型:1-个人1,2-企业|客户类型:1-个人1,2-企业|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|address|string|false|none|地址|地址|
|phone|string|false|none|手机号(个人/法人)|手机号(个人/法人)|
|idType|string|false|none|证件类型(个人/法人)|证件类型(个人/法人)|
|idNumber|string|false|none|证件号码(个人/法人)|证件号码(个人/法人)|
|isEmployee|boolean|false|none|是否是员工:0-否,1-是|是否是员工:0-否,1-是|
|creditCode|string|false|none|统一社会信用代码|统一社会信用代码|
|contactName|string|false|none|联系人|联系人|
|contactPhone|string|false|none|联系人手机号|联系人手机号|
|contactIdNumber|string|false|none|联系人身份证号|联系人身份证号|
|legalName|string|false|none|法人名称|法人名称|
|paymentAccount|string|false|none|付款银行账号|付款银行账号|
|paymentBank|string|false|none|付款银行名称|付款银行名称|
|guarantorName|string|false|none|担保人姓名|担保人姓名|
|guarantorPhone|string|false|none|担保人手机号|担保人手机号|
|guarantorIdType|string|false|none|担保人证件类型|担保人证件类型|
|guarantorIdNumber|string|false|none|担保人证件号码|担保人证件号码|
|guarantorAddress|string|false|none|担保人通讯地址|担保人通讯地址|
|guarantorIdFront|string|false|none|担保人身份证正面地址|担保人身份证正面地址|
|guarantorIdBack|string|false|none|担保人身份证反面地址|担保人身份证反面地址|
|invoiceTitle|string|false|none|开票名称|开票名称|
|invoiceTaxNumber|string|false|none|开票税号|开票税号|
|invoiceAddress|string|false|none|开票单位地址|开票单位地址|
|invoicePhone|string|false|none|开票电话号码|开票电话号码|
|invoiceBankName|string|false|none|开票开户银行|开票开户银行|
|invoiceAccountNumber|string|false|none|开票银行账户|开票银行账户|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractFeeVo">ContractFeeVo</h2>

<a id="schemacontractfeevo"></a>
<a id="schema_ContractFeeVo"></a>
<a id="tocScontractfeevo"></a>
<a id="tocscontractfeevo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "feeType": 0,
  "freeType": 0,
  "freeRentMonth": 0,
  "freeRentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "isCharge": true,
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同费用列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|feeType|integer(int32)|false|none|费用类型,1-免租期|费用类型,1-免租期|
|freeType|integer(int32)|false|none|免租类型,0-装修免租,1-经营免租,2-合同免租|免租类型,0-装修免租,1-经营免租,2-合同免租|
|freeRentMonth|integer(int32)|false|none|免租月数|免租月数|
|freeRentDay|integer(int32)|false|none|免租天数|免租天数|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|isCharge|boolean|false|none|免租是否收费:0-否,1-是(退租时使用)|免租是否收费:0-否,1-是(退租时使用)|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractRoomVo">ContractRoomVo</h2>

<a id="schemacontractroomvo"></a>
<a id="schema_ContractRoomVo"></a>
<a id="tocScontractroomvo"></a>
<a id="tocscontractroomvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "roomId": "string",
  "roomName": "string",
  "buildingName": "string",
  "floorName": "string",
  "parcelName": "string",
  "stageName": "string",
  "area": 0,
  "standardUnitPrice": 0,
  "bottomPrice": 0,
  "priceUnit": 0,
  "discount": 0,
  "signedUnitPrice": 0,
  "signedMonthlyPrice": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "bondPriceType": 0,
  "bondPrice": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同房源列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房间名称|房间名称|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorName|string|false|none|楼层名称|楼层名称|
|parcelName|string|false|none|地块名称|地块名称|
|stageName|string|false|none|分期名称|分期名称|
|area|number|false|none|面积（㎡）|面积（㎡）|
|standardUnitPrice|number|false|none|标准租金（单价）|标准租金（单价）|
|bottomPrice|number|false|none|底价|底价|
|priceUnit|integer(int32)|false|none|单价单位, 使用统一字典, 待定|单价单位, 使用统一字典, 待定|
|discount|number|false|none|折扣|折扣|
|signedUnitPrice|number|false|none|签约单价|签约单价|
|signedMonthlyPrice|number|false|none|签约月总价（元/月）|签约月总价（元/月）|
|startDate|string(date-time)|false|none|实际开始日期|实际开始日期|
|endDate|string(date-time)|false|none|实际结束日期|实际结束日期|
|bondPriceType|integer(int32)|false|none|立项定价保证金金额类型|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|
|bondPrice|number|false|none|立项定价保证金金额|立项定价保证金金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractVo">ContractVo</h2>

<a id="schemacontractvo"></a>
<a id="schema_ContractVo"></a>
<a id="tocScontractvo"></a>
<a id="tocscontractvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningId": "string",
  "ourSigningParty": "string",
  "customerName": "string",
  "unenterNum": 0,
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "changeFromId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "pmPayPeriod": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "venueDeliveryDate": "2019-08-24T14:15:22Z",
  "venueLocation": "string",
  "dailyActivityStartTime": "2019-08-24T14:15:22Z",
  "dailyActivityEndTime": "2019-08-24T14:15:22Z",
  "venuePurpose": "string",
  "otherInfo": "string",
  "contractAttachments": "string",
  "signAttachments": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "changeType": "string",
  "processId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeAttachments": "string",
  "changeExplanation": "string",
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "roomName": "string",
  "terminateDate": "2019-08-24T14:15:22Z",
  "terminateId": "string",
  "customer": {
    "id": "string",
    "contractId": "string",
    "customerId": "string",
    "customerType": 0,
    "customerName": "string",
    "address": "string",
    "phone": "string",
    "idType": "string",
    "idNumber": "string",
    "isEmployee": true,
    "creditCode": "string",
    "contactName": "string",
    "contactPhone": "string",
    "contactIdNumber": "string",
    "legalName": "string",
    "paymentAccount": "string",
    "paymentBank": "string",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "guarantorIdType": "string",
    "guarantorIdNumber": "string",
    "guarantorAddress": "string",
    "guarantorIdFront": "string",
    "guarantorIdBack": "string",
    "invoiceTitle": "string",
    "invoiceTaxNumber": "string",
    "invoiceAddress": "string",
    "invoicePhone": "string",
    "invoiceBankName": "string",
    "invoiceAccountNumber": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "bookings": [
    {
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "fees": [
    {
      "id": "string",
      "contractId": "string",
      "feeType": 0,
      "freeType": 0,
      "freeRentMonth": 0,
      "freeRentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "isCharge": true,
      "remark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "costs": [
    {
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "rooms": [
    {
      "id": "string",
      "contractId": "string",
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "bondPriceType": 0,
      "bondPrice": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|contractNo|string|false|none|合同号|合同号|
|unionId|string|false|none|统一id|统一id|
|version|string|false|none|版本号v00x|版本号v00x|
|isCurrent|boolean|false|none|是否当前生效版本 0-否,1-是|是否当前生效版本 0-否,1-是|
|isLatest|boolean|false|none|是否最新版本 0-否,1-是|是否最新版本 0-否,1-是|
|status|integer(int32)|false|none|一级状态,10-草稿,20-待生效,30-生效中,40-失效,50-作废|一级状态,10-草稿,20-待生效,30-生效中,40-失效,50-作废|
|statusTwo|integer(int32)|false|none|二级状态|二级状态|
|approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|operateType|integer(int32)|false|none|最新操作类型,0-新签,1-变更条款,2-退租|最新操作类型,0-新签,1-变更条款,2-退租|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|ourSigningId|string|false|none|我方签约主体id|我方签约主体id|
|ourSigningParty|string|false|none|我方签约主体信息|我方签约主体信息|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|unenterNum|integer(int32)|false|none|未进场房源数|未进场房源数|
|signWay|integer(int32)|false|none|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|
|signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|originId|string|false|none|续签源合同ID|续签源合同ID|
|changeFromId|string|false|none|变更源合同ID|变更源合同ID|
|landUsage|string|false|none|用地性质|用地性质|
|signerId|string|false|none|合同签约人id|合同签约人id|
|signerName|string|false|none|签约人姓名|签约人姓名|
|ownerId|string|false|none|责任人id|责任人id|
|ownerName|string|false|none|责任人姓名|责任人姓名|
|contractMode|integer(int32)|false|none|合同模式,0-标准合同,1-非标合同|合同模式,0-标准合同,1-非标合同|
|paperContractNo|string|false|none|纸质合同号|纸质合同号|
|signDate|string(date-time)|false|none|签订日期|签订日期|
|handoverDate|string(date-time)|false|none|交房日期|交房日期|
|contractPurpose|integer(int32)|false|none|合同用途,字典:|合同用途,字典:|
|dealChannel|integer(int32)|false|none|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|
|assistantId|string|false|none|协助人id|协助人id|
|assistantName|string|false|none|协助人姓名|协助人姓名|
|rentYear|integer(int32)|false|none|租赁期限-年|租赁期限-年|
|rentMonth|integer(int32)|false|none|租赁期限-月|租赁期限-月|
|rentDay|integer(int32)|false|none|租赁期限-日|租赁期限-日|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|effectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|invalidDate|string(date-time)|false|none|实际失效日期|实际失效日期|
|bookingRelType|integer(int32)|false|none|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|
|bondReceivableDate|string|false|none|保证金应收日期(天数/具体日期)|保证金应收日期(天数/具体日期)|
|bondReceivableType|integer(int32)|false|none|保证金应收日期类型,0-合同签订后, 1-指定日期|保证金应收日期类型,0-合同签订后, 1-指定日期|
|bondPriceType|integer(int32)|false|none|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|
|bondPrice|number|false|none|保证金金额|保证金金额|
|chargeWay|integer(int32)|false|none|收费方式,0-固定租金,1-递增租金,2-营收抽成|收费方式,0-固定租金,1-递增租金,2-营收抽成|
|rentReceivableDate|string|false|none|租金应收日期(天数/具体日期)|租金应收日期(天数/具体日期)|
|rentReceivableType|integer(int32)|false|none|租金应收日期类型,1-租期开始前,2-租期开始后|租金应收日期类型,1-租期开始前,2-租期开始后|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|rentPayPeriod|integer(int32)|false|none|租金支付周期|租金支付周期|
|increaseGap|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|increaseRate|number|false|none|递增率|递增率|
|increaseRule|string|false|none|租金递增规则(非标)|租金递增规则(非标)|
|estimateRevenue|number|false|none|预估营收额|预估营收额|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|revenueType|integer(int32)|false|none|抽点类型: 1-按月营业额, 2-按年营业额|抽点类型: 1-按月营业额, 2-按年营业额|
|isIncludePm|boolean|false|none|是否包含物业费|是否包含物业费|
|pmUnitPrice|number|false|none|月物业费单价|月物业费单价|
|pmMonthlyPrice|number|false|none|月物业费总价|月物业费总价|
|pmPayPeriod|integer(int32)|false|none|物业费支付周期：0-年, 1-按租金账单|物业费支付周期：0-年, 1-按租金账单|
|totalPrice|number|false|none|合同总价|合同总价|
|totalBottomPrice|number|false|none|合同底价|合同底价|
|bizTypeId|string|false|none|业态id|业态id|
|bizTypeName|string|false|none|业态|业态|
|lesseeBrand|string|false|none|承租方品牌|承租方品牌|
|businessCategory|string|false|none|经营品类|经营品类|
|openDate|string(date-time)|false|none|开业日期|开业日期|
|fireRiskCategory|integer(int32)|false|none|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|
|sprinklerSystem|integer(int32)|false|none|喷淋系统,0-安装,1-未安装|喷淋系统,0-安装,1-未安装|
|factoryEngaged|string|false|none|厂房从事|厂房从事|
|deliverDate|string(date-time)|false|none|交接日期|交接日期|
|parkingSpaceType|integer(int32)|false|none|车位类型,0-人防,1-非人防|车位类型,0-人防,1-非人防|
|hasParkingFee|boolean|false|none|是否包含车位管理费|是否包含车位管理费|
|parkingFeeAmount|number|false|none|车位管理费金额|车位管理费金额|
|venueDeliveryDate|string(date-time)|false|none|场地交付日期|场地交付日期|
|venueLocation|string|false|none|租赁场地位置|租赁场地位置|
|dailyActivityStartTime|string(date-time)|false|none|每日活动开始时间|每日活动开始时间|
|dailyActivityEndTime|string(date-time)|false|none|每日活动结束时间|每日活动结束时间|
|venuePurpose|string|false|none|场地用途|场地用途|
|otherInfo|string|false|none|补充条款|补充条款|
|contractAttachments|string|false|none|合同附件|合同附件|
|signAttachments|string|false|none|签署附件|签署附件[{"fileName": "", "fileUrl":"", "isConfirm":1or0}]|
|attachmentsPlan|string|false|none|附件-平面图|附件-平面图|
|isUploadSignature|boolean|false|none|是否上传签署文件 0-否,1-是|是否上传签署文件 0-否,1-是|
|changeType|string|false|none|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|
|processId|string|false|none|流程实例t_oa_process->id|流程实例t_oa_process->id|
|changeDate|string(date-time)|false|none|变更日期|变更日期|
|changeAttachments|string|false|none|变更附件json数组|变更附件json数组|
|changeExplanation|string|false|none|变更说明|变更说明|
|isSignatureConfirm|boolean|false|none|是否确认签署 0-否,1-是|是否确认签署 0-否,1-是|
|isPaperConfirm|boolean|false|none|是否确认纸质文件 0-否,1-是|是否确认纸质文件 0-否,1-是|
|isFinish|boolean|false|none|合同是否完全结束 0-否,1-是|合同是否完全结束 0-否,1-是|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建日期|创建日期|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新日期|更新日期|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|roomName|string|false|none|租赁资源|租赁资源|
|terminateDate|string(date-time)|false|none|退租日期|退租日期|
|terminateId|string|false|none|退租id|退租id|
|customer|[ContractCustomerVo](#schemacontractcustomervo)|false|none||合同客户信息|
|bookings|[[ContractBookingVo](#schemacontractbookingvo)]|false|none|合同定单列表|合同定单列表|
|fees|[[ContractFeeVo](#schemacontractfeevo)]|false|none|合同费用列表|合同费用列表|
|costs|[[ContractCostVo](#schemacontractcostvo)]|false|none|合同应收列表|合同应收列表|
|rooms|[[ContractRoomVo](#schemacontractroomvo)]|false|none|合同房源列表|合同房源列表|

<h2 id="tocS_RefundSaveRecordDTO">RefundSaveRecordDTO</h2>

<a id="schemarefundsaverecorddto"></a>
<a id="schema_RefundSaveRecordDTO"></a>
<a id="tocSrefundsaverecorddto"></a>
<a id="tocsrefundsaverecorddto"></a>

```json
{
  "refundId": "string",
  "flowRelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "pendingAmount": 0,
      "acctAmount": 0,
      "carryoverAmount": 0,
      "remark": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|refundId|string|false|none|退款ID|退款ID|
|flowRelList|[[CostFlowRelAddDTO](#schemacostflowreladddto)]|false|none|记账流水列表|记账流水列表|

<h2 id="tocS_FinancialFlowCancelDTO">FinancialFlowCancelDTO</h2>

<a id="schemafinancialflowcanceldto"></a>
<a id="schema_FinancialFlowCancelDTO"></a>
<a id="tocSfinancialflowcanceldto"></a>
<a id="tocsfinancialflowcanceldto"></a>

```json
{
  "refundId": "string",
  "cancelReason": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|refundId|string|true|none|退款单id|退款单id|
|cancelReason|string|false|none|作废原因|作废原因|

<h2 id="tocS_RefundConfirmRecordDTO">RefundConfirmRecordDTO</h2>

<a id="schemarefundconfirmrecorddto"></a>
<a id="schema_RefundConfirmRecordDTO"></a>
<a id="tocSrefundconfirmrecorddto"></a>
<a id="tocsrefundconfirmrecorddto"></a>

```json
{
  "refundId": "string",
  "isOneKeyConfirm": true,
  "flowRelId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|refundId|string|false|none|退款单ID|退款单ID|
|isOneKeyConfirm|boolean|false|none|是否一键确认|是否一键确认|
|flowRelId|string|false|none|记账记录ID|记账记录ID（单条确认时使用）|

<h2 id="tocS_CostFlowLogVo">CostFlowLogVo</h2>

<a id="schemacostflowlogvo"></a>
<a id="schema_CostFlowLogVo"></a>
<a id="tocScostflowlogvo"></a>
<a id="tocscostflowlogvo"></a>

```json
{
  "id": "string",
  "costId": "string",
  "refundId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "carryoverId": "string",
  "carryoverNo": "string",
  "amount": 0,
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "isDel": true
}

```

账单流水记录列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|costId|string|false|none|账单id|账单id|
|refundId|string|false|none|退款单id|退款单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转|类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转|
|carryoverId|string|false|none|结转单id|结转单id|
|carryoverNo|string|false|none|结转单号|结转单号|
|amount|number|false|none|记账金额|记账金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_CostFlowRelVo">CostFlowRelVo</h2>

<a id="schemacostflowrelvo"></a>
<a id="schema_CostFlowRelVo"></a>
<a id="tocScostflowrelvo"></a>
<a id="tocscostflowrelvo"></a>

```json
{
  "id": "string",
  "costId": "string",
  "refundId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "confirmStatus": 0,
  "confirmTime": "2019-08-24T14:15:22Z",
  "confirmUserId": "string",
  "confirmUserName": "string",
  "payAmount": 0,
  "pendingAmount": 0,
  "acctAmount": 0,
  "carryoverAmount": 0,
  "remark": "string",
  "createByName": "string",
  "createTime": "string",
  "updateByName": "string",
  "isDel": true,
  "projectId": "string",
  "projectName": "string",
  "entryTime": "2019-08-24T14:15:22Z",
  "payType": 0,
  "payMethod": "string",
  "orderNo": "string",
  "usedAmount": 0,
  "payerName": "string",
  "target": "string",
  "merchant": "string",
  "cumulativeAcctAmount": 0
}

```

账单流水关系列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|costId|string|false|none|账单id|账单id|
|refundId|string|false|none|退款单id|退款单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|账单类型：1-收款 2-转入 3-转出 4-退款|账单类型：1-收款 2-转入 3-转出 4-退款|
|confirmStatus|integer(int32)|false|none|确认状态: 0-未确认、1-自动确认、2-手动确认|确认状态: 0-未确认、1-自动确认、2-手动确认|
|confirmTime|string(date-time)|false|none|确认时间|确认时间|
|confirmUserId|string|false|none|确认人id|确认人id|
|confirmUserName|string|false|none|确认人姓名|确认人姓名|
|payAmount|number|false|none|支付金额|支付金额|
|pendingAmount|number|false|none|账单待收金额|账单待收金额|
|acctAmount|number|false|none|本次记账金额|本次记账金额|
|carryoverAmount|number|false|none|结转金额|结转金额|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|entryTime|string(date-time)|false|none|入账时间|入账时间|
|payType|integer(int32)|false|none|支付类型|支付类型|
|payMethod|string|false|none|支付方式|支付方式|
|orderNo|string|false|none|订单号|订单号|
|usedAmount|number|false|none|已使用金额|已使用金额|
|payerName|string|false|none|支付人姓名|支付人姓名|
|target|string|false|none|支付对象|支付对象|
|merchant|string|false|none|收款商户|收款商户|
|cumulativeAcctAmount|number|false|none|累计记账金额|累计记账金额|

<h2 id="tocS_ContractTerminateVo">ContractTerminateVo</h2>

<a id="schemacontractterminatevo"></a>
<a id="schema_ContractTerminateVo"></a>
<a id="tocScontractterminatevo"></a>
<a id="tocscontractterminatevo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "unionId": "string",
  "approveStatus": 0,
  "processId": "string",
  "isExit": true,
  "isPart": true,
  "bondReceivedAmount": 0,
  "rentReceivedAmount": 0,
  "rentOverdueAmount": 0,
  "receivedPeriod": "string",
  "overduePeriod": "string",
  "terminateType": 0,
  "terminateDate": "2019-08-24T14:15:22Z",
  "terminateReason": "string",
  "otherReasonDesc": "string",
  "hasOtherDeduction": true,
  "otherDeductionDesc": "string",
  "terminateRemark": "string",
  "terminateAttachments": "string",
  "signAttachments": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "roomList": [
    {}
  ],
  "costList": [
    {
      "id": "string",
      "contractId": "string",
      "terminateId": "string",
      "costId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "terminateReceivable": 0,
      "receivedAmount": 0,
      "penaltyAmount": 0,
      "refundAmount": 0,
      "freeId": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "contract": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "contractNo": "string",
    "unionId": "string",
    "version": "string",
    "isCurrent": true,
    "isLatest": true,
    "status": 0,
    "statusTwo": 0,
    "approveStatus": 0,
    "operateType": 0,
    "contractType": 0,
    "ourSigningId": "string",
    "ourSigningParty": "string",
    "customerName": "string",
    "unenterNum": 0,
    "signWay": 0,
    "signType": 0,
    "originId": "string",
    "changeFromId": "string",
    "landUsage": "string",
    "signerId": "string",
    "signerName": "string",
    "ownerId": "string",
    "ownerName": "string",
    "contractMode": 0,
    "paperContractNo": "string",
    "signDate": "2019-08-24T14:15:22Z",
    "handoverDate": "2019-08-24T14:15:22Z",
    "contractPurpose": 0,
    "dealChannel": 0,
    "assistantId": "string",
    "assistantName": "string",
    "rentYear": 0,
    "rentMonth": 0,
    "rentDay": 0,
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "effectDate": "2019-08-24T14:15:22Z",
    "invalidDate": "2019-08-24T14:15:22Z",
    "bookingRelType": 0,
    "bondReceivableDate": "string",
    "bondReceivableType": 0,
    "bondPriceType": 0,
    "bondPrice": 0,
    "chargeWay": 0,
    "rentReceivableDate": "string",
    "rentReceivableType": 0,
    "rentTicketPeriod": 0,
    "rentPayPeriod": 0,
    "increaseGap": 0,
    "increaseRate": 0,
    "increaseRule": "string",
    "estimateRevenue": 0,
    "percentageType": 0,
    "fixedPercentage": 0,
    "stepPercentage": "string",
    "revenueType": 0,
    "isIncludePm": true,
    "pmUnitPrice": 0,
    "pmMonthlyPrice": 0,
    "pmPayPeriod": 0,
    "totalPrice": 0,
    "totalBottomPrice": 0,
    "bizTypeId": "string",
    "bizTypeName": "string",
    "lesseeBrand": "string",
    "businessCategory": "string",
    "openDate": "2019-08-24T14:15:22Z",
    "fireRiskCategory": 0,
    "sprinklerSystem": 0,
    "factoryEngaged": "string",
    "deliverDate": "2019-08-24T14:15:22Z",
    "parkingSpaceType": 0,
    "hasParkingFee": true,
    "parkingFeeAmount": 0,
    "venueDeliveryDate": "2019-08-24T14:15:22Z",
    "venueLocation": "string",
    "dailyActivityStartTime": "2019-08-24T14:15:22Z",
    "dailyActivityEndTime": "2019-08-24T14:15:22Z",
    "venuePurpose": "string",
    "otherInfo": "string",
    "contractAttachments": "string",
    "signAttachments": "string",
    "attachmentsPlan": "string",
    "isUploadSignature": true,
    "changeType": "string",
    "processId": "string",
    "changeDate": "2019-08-24T14:15:22Z",
    "changeAttachments": "string",
    "changeExplanation": "string",
    "isSignatureConfirm": true,
    "isPaperConfirm": true,
    "isFinish": true,
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "isDel": true,
    "roomName": "string",
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateId": "string",
    "customer": {
      "id": "string",
      "contractId": "string",
      "customerId": "string",
      "customerType": 0,
      "customerName": "string",
      "address": "string",
      "phone": "string",
      "idType": "string",
      "idNumber": "string",
      "isEmployee": true,
      "creditCode": "string",
      "contactName": "string",
      "contactPhone": "string",
      "contactIdNumber": "string",
      "legalName": "string",
      "paymentAccount": "string",
      "paymentBank": "string",
      "guarantorName": "string",
      "guarantorPhone": "string",
      "guarantorIdType": "string",
      "guarantorIdNumber": "string",
      "guarantorAddress": "string",
      "guarantorIdFront": "string",
      "guarantorIdBack": "string",
      "invoiceTitle": "string",
      "invoiceTaxNumber": "string",
      "invoiceAddress": "string",
      "invoicePhone": "string",
      "invoiceBankName": "string",
      "invoiceAccountNumber": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    },
    "bookings": [
      {
        "id": "string",
        "contractId": "string",
        "bookingId": "string",
        "bookedRoom": "string",
        "bookerName": "string",
        "bookingReceivedAmount": 0,
        "bookingPaymentDate": "2019-08-24T14:15:22Z",
        "transferBondAmount": 0,
        "transferRentAmount": 0,
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "fees": [
      {
        "id": "string",
        "contractId": "string",
        "feeType": 0,
        "freeType": 0,
        "freeRentMonth": 0,
        "freeRentDay": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "isCharge": true,
        "remark": "string",
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "costs": [
      {
        "id": "string",
        "contractId": "string",
        "costType": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "period": 0,
        "customerId": "string",
        "customerName": "string",
        "roomId": "string",
        "roomName": "string",
        "area": 0,
        "subjectId": "string",
        "subjectName": "string",
        "receivableDate": "2019-08-24T14:15:22Z",
        "unitPrice": 0,
        "priceUnit": 0,
        "totalAmount": 0,
        "discountAmount": 0,
        "actualReceivable": 0,
        "receivedAmount": 0,
        "isRevenue": true,
        "isDiscount": true,
        "percentageType": 0,
        "fixedPercentage": 0,
        "stepPercentage": "string",
        "createByName": "string",
        "updateByName": "string",
        "isDel": true,
        "contractStartDate": "2019-08-24T14:15:22Z",
        "rentTicketPeriod": 0
      }
    ],
    "rooms": [
      {
        "id": "string",
        "contractId": "string",
        "roomId": "string",
        "roomName": "string",
        "buildingName": "string",
        "floorName": "string",
        "parcelName": "string",
        "stageName": "string",
        "area": 0,
        "standardUnitPrice": 0,
        "bottomPrice": 0,
        "priceUnit": 0,
        "discount": 0,
        "signedUnitPrice": 0,
        "signedMonthlyPrice": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "bondPriceType": 0,
        "bondPrice": 0,
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ]
  }
}

```

合同退租申请信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||退租ID|
|contractId|string|false|none|合同id|合同id|
|unionId|string|false|none|合同统一id|合同统一id|
|approveStatus|integer(int32)|false|none|审核状态:0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态:0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|processId|string|false|none|流程实例ID|流程实例t_oa_process->id|
|isExit|boolean|false|none|是否同时办理出场:0-否,1-是|是否同时办理出场:0-否,1-是|
|isPart|boolean|false|none|是否部分提前退租:0-否,1-是|是否部分提前退租:0-否,1-是|
|bondReceivedAmount|number|false|none|已收保证金（元）|已收保证金（元）|
|rentReceivedAmount|number|false|none|已收租金（元）|已收租金（元）|
|rentOverdueAmount|number|false|none|逾期租金（元）|逾期租金（元）|
|receivedPeriod|string|false|none|已收账期文字描述|已收账期文字描述|
|overduePeriod|string|false|none|逾期账期文字描述|逾期账期文字描述|
|terminateType|integer(int32)|false|none|退租类型:0-到期退租,1-提前退租|退租类型:0-到期退租,1-提前退租|
|terminateDate|string(date-time)|false|none|退租日期|退租日期|
|terminateReason|string|false|none|退租原因,字典逗号拼接|退租原因,字典逗号拼接|
|otherReasonDesc|string|false|none|其他原因说明|其他原因说明|
|hasOtherDeduction|boolean|false|none|是否有其他扣款|是否有其他扣款|
|otherDeductionDesc|string|false|none|其他扣款描述|其他扣款描述|
|terminateRemark|string|false|none|退租说明|退租说明|
|terminateAttachments|string|false|none|退租附件|退租附件|
|signAttachments|string|false|none|签署附件|签署附件|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|roomList|[object]|false|none||退租房间列表|
|costList|[[ContractTerminateCostVo](#schemacontractterminatecostvo)]|false|none||退租费用列表|
|contract|[ContractVo](#schemacontractvo)|false|none||none|

<h2 id="tocS_ContractTerminateRoomVo">ContractTerminateRoomVo</h2>

<a id="schemacontractterminateroomvo"></a>
<a id="schema_ContractTerminateRoomVo"></a>
<a id="tocScontractterminateroomvo"></a>
<a id="tocscontractterminateroomvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "terminateId": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

退租房间VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|terminateId|string|false|none|退租单id|退租单id|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房间名称|房间名称|
|area|number|false|none|面积（㎡）|面积（㎡）|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_FinancialFlowVo">FinancialFlowVo</h2>

<a id="schemafinancialflowvo"></a>
<a id="schema_FinancialFlowVo"></a>
<a id="tocSfinancialflowvo"></a>
<a id="tocsfinancialflowvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "orgName": "string",
  "projectName": "string",
  "orderNo": "string",
  "payDirection": 0,
  "payType": 0,
  "payMethod": "string",
  "targetType": 0,
  "target": "string",
  "targetId": "string",
  "entryTime": "2019-08-24T14:15:22Z",
  "status": 0,
  "amount": 0,
  "usedAmount": 0,
  "payerName": "string",
  "payerPhone": "string",
  "payerAccount": "string",
  "payerCard": "string",
  "payRemark": "string",
  "merchant": "string",
  "payChannel": "string",
  "sourceNo": "string",
  "isOtherIncome": true,
  "otherIncomeDesc": "string",
  "refundId": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}

```

未明流水信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|orgName|string|false|none|组织名称|组织名称(线下支付使用)|
|projectName|string|false|none|项目名称|项目名称|
|orderNo|string|false|none|订单号|订单号|
|payDirection|integer(int32)|false|none|支付方向: 0-收入, 1-支出|支付方向: 0-收入, 1-支出|
|payType|integer(int32)|false|none|支付类型|支付类型|
|payMethod|string|false|none|支付方式|支付方式|
|targetType|integer(int32)|false|none|支付对象类型|支付对象类型|
|target|string|false|none|支付对象|支付对象|
|targetId|string|false|none|支付对象id|支付对象id|
|entryTime|string(date-time)|false|none|入账时间|入账时间|
|status|integer(int32)|false|none|状态: 0-未记账, 1-部分记账, 2-已记账|状态: 0-未记账, 1-部分记账, 2-已记账|
|amount|number|false|none|支付金额|支付金额|
|usedAmount|number|false|none|已使用金额|已使用金额|
|payerName|string|false|none|支付人姓名|支付人姓名|
|payerPhone|string|false|none|支付人手机号|支付人手机号|
|payerAccount|string|false|none|支付人账号|支付人账号|
|payerCard|string|false|none|支付人身份证/统一社会信用代码|支付人身份证/统一社会信用代码|
|payRemark|string|false|none|支付备注|支付备注|
|merchant|string|false|none|收款商户|收款商户|
|payChannel|string|false|none|收款渠道|收款渠道|
|sourceNo|string|false|none|退款流水的原单号|退款流水的原单号|
|isOtherIncome|boolean|false|none|是否是其他收入: 0-否,1-是|是否是其他收入: 0-否,1-是|
|otherIncomeDesc|string|false|none|其他收入说明|其他收入说明|
|refundId|string|false|none|退款单id|退款单id|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_FinancialRefundDetailVo">FinancialRefundDetailVo</h2>

<a id="schemafinancialrefunddetailvo"></a>
<a id="schema_FinancialRefundDetailVo"></a>
<a id="tocSfinancialrefunddetailvo"></a>
<a id="tocsfinancialrefunddetailvo"></a>

```json
{
  "refund": {},
  "exit": {},
  "booking": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "customerName": "string",
    "propertyType": "string",
    "roomId": "string",
    "roomName": "string",
    "bookingNo": "string",
    "bookingAmount": 0,
    "unpaidAmount": 0,
    "receivableDate": "2019-08-24T14:15:22Z",
    "expectSignDate": "2019-08-24T14:15:22Z",
    "isRefundable": 0,
    "cancelTime": "2019-08-24T14:15:22Z",
    "cancelBy": "string",
    "cancelByName": "string",
    "cancelReason": 0,
    "isRefund": 0,
    "cancelEnclosure": "string",
    "cancelRemark": "string",
    "status": 0,
    "contractId": "string",
    "refundId": "string",
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "isDel": true,
    "contractNo": "string",
    "signDate": "2019-08-24T14:15:22Z",
    "contractLeaseUnit": "string",
    "lesseeName": "string",
    "receivedAmount": 0,
    "receivedDate": "2019-08-24T14:15:22Z",
    "payMethod": "string"
  },
  "flow": {
    "id": "string",
    "projectId": "string",
    "orgName": "string",
    "projectName": "string",
    "orderNo": "string",
    "payDirection": 0,
    "payType": 0,
    "payMethod": "string",
    "targetType": 0,
    "target": "string",
    "targetId": "string",
    "entryTime": "2019-08-24T14:15:22Z",
    "status": 0,
    "amount": 0,
    "usedAmount": 0,
    "payerName": "string",
    "payerPhone": "string",
    "payerAccount": "string",
    "payerCard": "string",
    "payRemark": "string",
    "merchant": "string",
    "payChannel": "string",
    "sourceNo": "string",
    "isOtherIncome": true,
    "otherIncomeDesc": "string",
    "refundId": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "isDel": true
  },
  "flowRels": [
    {
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "pendingAmount": 0,
      "acctAmount": 0,
      "carryoverAmount": 0,
      "remark": "string",
      "createByName": "string",
      "createTime": "string",
      "updateByName": "string",
      "isDel": true,
      "projectId": "string",
      "projectName": "string",
      "entryTime": "2019-08-24T14:15:22Z",
      "payType": 0,
      "payMethod": "string",
      "orderNo": "string",
      "usedAmount": 0,
      "payerName": "string",
      "target": "string",
      "merchant": "string",
      "cumulativeAcctAmount": 0
    }
  ],
  "flowLogList": [
    {
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "carryoverId": "string",
      "carryoverNo": "string",
      "amount": 0,
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|refund|object|false|none||none|
|exit|object|false|none||none|
|booking|[BookingVo](#schemabookingvo)|false|none||定单基本信息|
|flow|[FinancialFlowVo](#schemafinancialflowvo)|false|none||未明流水信息，退款类型为2时有值|
|flowRels|[[CostFlowRelVo](#schemacostflowrelvo)]|false|none|退款流水信息列表|退款流水信息列表|
|flowLogList|[[CostFlowLogVo](#schemacostflowlogvo)]|false|none|账单流水记录列表|账单流水记录列表|

<h2 id="tocS_ExitDetailVo">ExitDetailVo</h2>

<a id="schemaexitdetailvo"></a>
<a id="schema_ExitDetailVo"></a>
<a id="tocSexitdetailvo"></a>
<a id="tocsexitdetailvo"></a>

```json
{
  "exitInfo": {},
  "contractTerminateInfo": {
    "id": "string",
    "contractId": "string",
    "unionId": "string",
    "approveStatus": 0,
    "processId": "string",
    "isExit": true,
    "isPart": true,
    "bondReceivedAmount": 0,
    "rentReceivedAmount": 0,
    "rentOverdueAmount": 0,
    "receivedPeriod": "string",
    "overduePeriod": "string",
    "terminateType": 0,
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateReason": "string",
    "otherReasonDesc": "string",
    "hasOtherDeduction": true,
    "otherDeductionDesc": "string",
    "terminateRemark": "string",
    "terminateAttachments": "string",
    "signAttachments": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true,
    "roomList": [
      {}
    ],
    "costList": [
      {
        "id": "string",
        "contractId": "string",
        "terminateId": "string",
        "costId": "string",
        "costType": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "customerId": "string",
        "customerName": "string",
        "roomId": "string",
        "roomName": "string",
        "subjectId": "string",
        "subjectName": "string",
        "receivableDate": "2019-08-24T14:15:22Z",
        "totalAmount": 0,
        "discountAmount": 0,
        "actualReceivable": 0,
        "terminateReceivable": 0,
        "receivedAmount": 0,
        "penaltyAmount": 0,
        "refundAmount": 0,
        "freeId": 0,
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "contract": {
      "id": "string",
      "projectId": "string",
      "projectName": "string",
      "contractNo": "string",
      "unionId": "string",
      "version": "string",
      "isCurrent": true,
      "isLatest": true,
      "status": 0,
      "statusTwo": 0,
      "approveStatus": 0,
      "operateType": 0,
      "contractType": 0,
      "ourSigningId": "string",
      "ourSigningParty": "string",
      "customerName": "string",
      "unenterNum": 0,
      "signWay": 0,
      "signType": 0,
      "originId": "string",
      "changeFromId": "string",
      "landUsage": "string",
      "signerId": "string",
      "signerName": "string",
      "ownerId": "string",
      "ownerName": "string",
      "contractMode": 0,
      "paperContractNo": "string",
      "signDate": "2019-08-24T14:15:22Z",
      "handoverDate": "2019-08-24T14:15:22Z",
      "contractPurpose": 0,
      "dealChannel": 0,
      "assistantId": "string",
      "assistantName": "string",
      "rentYear": 0,
      "rentMonth": 0,
      "rentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "effectDate": "2019-08-24T14:15:22Z",
      "invalidDate": "2019-08-24T14:15:22Z",
      "bookingRelType": 0,
      "bondReceivableDate": "string",
      "bondReceivableType": 0,
      "bondPriceType": 0,
      "bondPrice": 0,
      "chargeWay": 0,
      "rentReceivableDate": "string",
      "rentReceivableType": 0,
      "rentTicketPeriod": 0,
      "rentPayPeriod": 0,
      "increaseGap": 0,
      "increaseRate": 0,
      "increaseRule": "string",
      "estimateRevenue": 0,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "revenueType": 0,
      "isIncludePm": true,
      "pmUnitPrice": 0,
      "pmMonthlyPrice": 0,
      "pmPayPeriod": 0,
      "totalPrice": 0,
      "totalBottomPrice": 0,
      "bizTypeId": "string",
      "bizTypeName": "string",
      "lesseeBrand": "string",
      "businessCategory": "string",
      "openDate": "2019-08-24T14:15:22Z",
      "fireRiskCategory": 0,
      "sprinklerSystem": 0,
      "factoryEngaged": "string",
      "deliverDate": "2019-08-24T14:15:22Z",
      "parkingSpaceType": 0,
      "hasParkingFee": true,
      "parkingFeeAmount": 0,
      "venueDeliveryDate": "2019-08-24T14:15:22Z",
      "venueLocation": "string",
      "dailyActivityStartTime": "2019-08-24T14:15:22Z",
      "dailyActivityEndTime": "2019-08-24T14:15:22Z",
      "venuePurpose": "string",
      "otherInfo": "string",
      "contractAttachments": "string",
      "signAttachments": "string",
      "attachmentsPlan": "string",
      "isUploadSignature": true,
      "changeType": "string",
      "processId": "string",
      "changeDate": "2019-08-24T14:15:22Z",
      "changeAttachments": "string",
      "changeExplanation": "string",
      "isSignatureConfirm": true,
      "isPaperConfirm": true,
      "isFinish": true,
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "isDel": true,
      "roomName": "string",
      "terminateDate": "2019-08-24T14:15:22Z",
      "terminateId": "string",
      "customer": {
        "id": "string",
        "contractId": "string",
        "customerId": "string",
        "customerType": 0,
        "customerName": "string",
        "address": "string",
        "phone": "string",
        "idType": "string",
        "idNumber": "string",
        "isEmployee": true,
        "creditCode": "string",
        "contactName": "string",
        "contactPhone": "string",
        "contactIdNumber": "string",
        "legalName": "string",
        "paymentAccount": "string",
        "paymentBank": "string",
        "guarantorName": "string",
        "guarantorPhone": "string",
        "guarantorIdType": "string",
        "guarantorIdNumber": "string",
        "guarantorAddress": "string",
        "guarantorIdFront": "string",
        "guarantorIdBack": "string",
        "invoiceTitle": "string",
        "invoiceTaxNumber": "string",
        "invoiceAddress": "string",
        "invoicePhone": "string",
        "invoiceBankName": "string",
        "invoiceAccountNumber": "string",
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      },
      "bookings": [
        {
          "id": "string",
          "contractId": "string",
          "bookingId": "string",
          "bookedRoom": "string",
          "bookerName": "string",
          "bookingReceivedAmount": 0,
          "bookingPaymentDate": "2019-08-24T14:15:22Z",
          "transferBondAmount": 0,
          "transferRentAmount": 0,
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ],
      "fees": [
        {
          "id": "string",
          "contractId": "string",
          "feeType": 0,
          "freeType": 0,
          "freeRentMonth": 0,
          "freeRentDay": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "isCharge": true,
          "remark": "string",
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ],
      "costs": [
        {
          "id": "string",
          "contractId": "string",
          "costType": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "period": 0,
          "customerId": "string",
          "customerName": "string",
          "roomId": "string",
          "roomName": "string",
          "area": 0,
          "subjectId": "string",
          "subjectName": "string",
          "receivableDate": "2019-08-24T14:15:22Z",
          "unitPrice": 0,
          "priceUnit": 0,
          "totalAmount": 0,
          "discountAmount": 0,
          "actualReceivable": 0,
          "receivedAmount": 0,
          "isRevenue": true,
          "isDiscount": true,
          "percentageType": 0,
          "fixedPercentage": 0,
          "stepPercentage": "string",
          "createByName": "string",
          "updateByName": "string",
          "isDel": true,
          "contractStartDate": "2019-08-24T14:15:22Z",
          "rentTicketPeriod": 0
        }
      ],
      "rooms": [
        {
          "id": "string",
          "contractId": "string",
          "roomId": "string",
          "roomName": "string",
          "buildingName": "string",
          "floorName": "string",
          "parcelName": "string",
          "stageName": "string",
          "area": 0,
          "standardUnitPrice": 0,
          "bottomPrice": 0,
          "priceUnit": 0,
          "discount": 0,
          "signedUnitPrice": 0,
          "signedMonthlyPrice": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "bondPriceType": 0,
          "bondPrice": 0,
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ]
    }
  },
  "exitRoomList": [
    {
      "id": "string",
      "exitId": "string",
      "roomId": "string",
      "roomName": "string",
      "propertyType": 0,
      "parcelName": "string",
      "buildingName": "string",
      "exitDate": "2019-08-24T14:15:22Z",
      "rentControl": 0,
      "doorWindowStatus": 0,
      "doorWindowPenalty": 0,
      "keyHandoverStatus": 0,
      "keyPenalty": 0,
      "cleaningStatus": 0,
      "cleaningPenalty": 0,
      "elecMeterReading": 0,
      "coldWaterReading": 0,
      "hotWaterReading": 0,
      "elecFee": 0,
      "waterFee": 0,
      "pmFee": 0,
      "roomPhotos": "string",
      "assetsSituation": "string",
      "remark": "string",
      "isBusinessConfirmed": true,
      "businessConfirmBy": "string",
      "businessConfirmByName": "string",
      "businessConfirmTime": "2019-08-24T14:15:22Z",
      "isFinanceConfirmed": true,
      "financeConfirmBy": "string",
      "financeConfirmByName": "string",
      "financeConfirmTime": "2019-08-24T14:15:22Z",
      "financeConfirmSignature": "string",
      "isEngineeringConfirmed": true,
      "engineeringConfirmBy": "string",
      "engineeringConfirmByName": "string",
      "engineeringConfirmTime": "2019-08-24T14:15:22Z",
      "engineeringConfirmSignature": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "exitRoomAssetsList": [
        {
          "id": "string",
          "exitId": "string",
          "exitRoomId": "string",
          "category": 0,
          "name": "string",
          "specification": "string",
          "count": 0,
          "status": 0,
          "penalty": 0,
          "isAdd": true,
          "remark": "string",
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ]
    }
  ],
  "exitCostList": [
    {
      "id": "string",
      "exitId": "string",
      "costId": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "payType": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "amount": 0,
      "type": 0,
      "remark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|exitInfo|object|false|none||none|
|contractTerminateInfo|[ContractTerminateVo](#schemacontractterminatevo)|false|none||退租信息VO|
|exitRoomList|[[ExitRoomVo](#schemaexitroomvo)]|false|none|出场房间列表|出场房间列表|
|exitCostList|[[ExitCostVo](#schemaexitcostvo)]|false|none|出场费用结算列表|出场费用结算列表|

<h2 id="tocS_ExitCostVo">ExitCostVo</h2>

<a id="schemaexitcostvo"></a>
<a id="schema_ExitCostVo"></a>
<a id="tocSexitcostvo"></a>
<a id="tocsexitcostvo"></a>

```json
{
  "id": "string",
  "exitId": "string",
  "costId": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "payType": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "amount": 0,
  "type": 0,
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

出场费用结算列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|exitId|string|false|none|离场单id|离场单id|
|costId|string|false|none|t_cost表id|t_cost表id|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|payType|integer(int32)|false|none|支付方向: 1-收, 2-支|支付方向: 1-收, 2-支|
|subjectId|string|false|none|科目id|科目id|
|subjectName|string|false|none|科目名|科目名|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|amount|number|false|none|金额（元）|金额（元）|
|type|integer(int32)|false|none|账单类型: 0-退租结算, 1-交割单生成, 2-手动添加,3-退租时生成的罚没金|账单类型: 0-退租结算, 1-交割单生成, 2-手动添加,3-退租时生成的罚没金|
|remark|string|false|none|费用说明|费用说明|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ExitRoomVo">ExitRoomVo</h2>

<a id="schemaexitroomvo"></a>
<a id="schema_ExitRoomVo"></a>
<a id="tocSexitroomvo"></a>
<a id="tocsexitroomvo"></a>

```json
{
  "id": "string",
  "exitId": "string",
  "roomId": "string",
  "roomName": "string",
  "propertyType": 0,
  "parcelName": "string",
  "buildingName": "string",
  "exitDate": "2019-08-24T14:15:22Z",
  "rentControl": 0,
  "doorWindowStatus": 0,
  "doorWindowPenalty": 0,
  "keyHandoverStatus": 0,
  "keyPenalty": 0,
  "cleaningStatus": 0,
  "cleaningPenalty": 0,
  "elecMeterReading": 0,
  "coldWaterReading": 0,
  "hotWaterReading": 0,
  "elecFee": 0,
  "waterFee": 0,
  "pmFee": 0,
  "roomPhotos": "string",
  "assetsSituation": "string",
  "remark": "string",
  "isBusinessConfirmed": true,
  "businessConfirmBy": "string",
  "businessConfirmByName": "string",
  "businessConfirmTime": "2019-08-24T14:15:22Z",
  "isFinanceConfirmed": true,
  "financeConfirmBy": "string",
  "financeConfirmByName": "string",
  "financeConfirmTime": "2019-08-24T14:15:22Z",
  "financeConfirmSignature": "string",
  "isEngineeringConfirmed": true,
  "engineeringConfirmBy": "string",
  "engineeringConfirmByName": "string",
  "engineeringConfirmTime": "2019-08-24T14:15:22Z",
  "engineeringConfirmSignature": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "exitRoomAssetsList": [
    {
      "id": "string",
      "exitId": "string",
      "exitRoomId": "string",
      "category": 0,
      "name": "string",
      "specification": "string",
      "count": 0,
      "status": 0,
      "penalty": 0,
      "isAdd": true,
      "remark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

出场房间列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|exitId|string|false|none|离场单id|离场单id|
|roomId|string|false|none|房间id|房间id|
|roomName|string|false|none|房源名称|房源名称|
|propertyType|integer(int32)|false|none|物业类型字典|物业类型字典|
|parcelName|string|false|none|所属地块|所属地块|
|buildingName|string|false|none|所属楼栋|所属楼栋|
|exitDate|string(date-time)|false|none|出场日期|出场日期|
|rentControl|integer(int32)|false|none|租控管理: 1-当前空置已做断电、锁门处理, 2-二次出租无需断电处理,开门设置更新|租控管理: 1-当前空置已做断电、锁门处理, 2-二次出租无需断电处理,开门设置更新|
|doorWindowStatus|integer(int32)|false|none|门、窗、墙体及其他情况: 1-完好, 2-损坏|门、窗、墙体及其他情况: 1-完好, 2-损坏|
|doorWindowPenalty|number|false|none|赔偿金（门、窗、墙体及其他损坏）|赔偿金（门、窗、墙体及其他损坏）|
|keyHandoverStatus|integer(int32)|false|none|钥匙交接情况: 1-已交齐, 2-未交齐|钥匙交接情况: 1-已交齐, 2-未交齐|
|keyPenalty|number|false|none|赔偿金（钥匙未交齐）|赔偿金（钥匙未交齐）|
|cleaningStatus|integer(int32)|false|none|清洁卫生: 1-自行打扫完毕、洁净, 2-保洁及垃圾清理收费|清洁卫生: 1-自行打扫完毕、洁净, 2-保洁及垃圾清理收费|
|cleaningPenalty|number|false|none|保洁及垃圾清理费金额|保洁及垃圾清理费金额|
|elecMeterReading|number|false|none|电度数|电度数|
|coldWaterReading|number|false|none|冷水度数|冷水度数|
|hotWaterReading|number|false|none|热水度数|热水度数|
|elecFee|number|false|none|电费欠费|电费欠费|
|waterFee|number|false|none|水费欠费|水费欠费|
|pmFee|number|false|none|物业欠费|物业欠费|
|roomPhotos|string|false|none|房间照片|房间照片|
|assetsSituation|string|false|none|固定资产、设备设施评估情况|固定资产、设备设施评估情况|
|remark|string|false|none|备注|备注|
|isBusinessConfirmed|boolean|false|none|商服是否确认: 0-否, 1-是|商服是否确认: 0-否, 1-是|
|businessConfirmBy|string|false|none|商服确认人id|商服确认人id|
|businessConfirmByName|string|false|none|商服确认人名称|商服确认人名称|
|businessConfirmTime|string(date-time)|false|none|商服确认时间|商服确认时间|
|isFinanceConfirmed|boolean|false|none|综合或财务是否确认: 0-否, 1-是|综合或财务是否确认: 0-否, 1-是|
|financeConfirmBy|string|false|none|综合或财务确认人id|综合或财务确认人id|
|financeConfirmByName|string|false|none|综合或财务确认人名称|综合或财务确认人名称|
|financeConfirmTime|string(date-time)|false|none|综合或财务确认时间|综合或财务确认时间|
|financeConfirmSignature|string|false|none|综合或财务确认签名|综合或财务确认签名|
|isEngineeringConfirmed|boolean|false|none|工程或客服是否确认: 0-否, 1-是|工程或客服是否确认: 0-否, 1-是|
|engineeringConfirmBy|string|false|none|工程或客服确认人id|工程或客服确认人id|
|engineeringConfirmByName|string|false|none|工程或客服确认人名称|工程或客服确认人名称|
|engineeringConfirmTime|string(date-time)|false|none|工程或客服确认时间|工程或客服确认时间|
|engineeringConfirmSignature|string|false|none|工程或客服确认签名|工程或客服确认签名|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|exitRoomAssetsList|[[ExitRoomAssetsVo](#schemaexitroomassetsvo)]|false|none|出场房间资产列表|出场房间资产列表|

<h2 id="tocS_ExitRoomAssetsVo">ExitRoomAssetsVo</h2>

<a id="schemaexitroomassetsvo"></a>
<a id="schema_ExitRoomAssetsVo"></a>
<a id="tocSexitroomassetsvo"></a>
<a id="tocsexitroomassetsvo"></a>

```json
{
  "id": "string",
  "exitId": "string",
  "exitRoomId": "string",
  "category": 0,
  "name": "string",
  "specification": "string",
  "count": 0,
  "status": 0,
  "penalty": 0,
  "isAdd": true,
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

出场房间资产列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|exitId|string|false|none|离场单id|离场单id|
|exitRoomId|string|false|none|出场记录-房间id|出场记录-房间id|
|category|integer(int32)|false|none|种类|种类|
|name|string|false|none|物品名称|物品名称|
|specification|string|false|none|规格|规格|
|count|integer(int32)|false|none|数量|数量|
|status|integer(int32)|false|none|现状: 1-完好, 2-损坏, 3-丢失|现状: 1-完好, 2-损坏, 3-丢失|
|penalty|number|false|none|赔偿金|赔偿金|
|isAdd|boolean|false|none|是否手动添加的,0-否,1-是|是否手动添加的,0-否,1-是|
|remark|string|false|none|说明|说明|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ExitVo">ExitVo</h2>

<a id="schemaexitvo"></a>
<a id="schema_ExitVo"></a>
<a id="tocSexitvo"></a>
<a id="tocsexitvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractUnionId": "string",
  "terminateId": "string",
  "refundId": "string",
  "customerId": "string",
  "customerName": "string",
  "processType": 0,
  "progressStatus": 0,
  "isDiscount": true,
  "discountAmount": 0,
  "discountReason": "string",
  "finalAmount": 0,
  "refundProcessType": 0,
  "payeeName": "string",
  "payeeAccount": "string",
  "bankName": "string",
  "licenseStatus": 0,
  "taxCertStatus": 0,
  "refundApplyType": 0,
  "signType": 0,
  "signAttachments": "string",
  "signTime": "2019-08-24T14:15:22Z",
  "copyTime": "2019-08-24T14:15:22Z",
  "copyBy": "string",
  "copyByName": "string",
  "settleTime": "2019-08-24T14:15:22Z",
  "settleBy": "string",
  "settleByName": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractPurpose": 0,
  "terminateType": 0,
  "terminateDate": "2019-08-24T14:15:22Z",
  "terminateRoomName": "string",
  "terminateRoomCount": 0,
  "engineeringCount": 0,
  "financeCount": 0,
  "refundStatus": 0
}

```

出场单基础信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractNo|string|false|none|合同号|合同号|
|contractUnionId|string|false|none|合同统一id|合同统一id|
|terminateId|string|false|none|退租单id|退租单id|
|refundId|string|false|none|退款单id|退款单id|
|customerId|string|false|none|客户id|客户id|
|customerName|string|false|none|承租方，客户名称|承租方，客户名称|
|processType|integer(int32)|false|none|办理流程: 1-先交割后结算, 2-交割并结算|办理流程: 1-先交割后结算, 2-交割并结算|
|progressStatus|integer(int32)|false|none|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|
|isDiscount|boolean|false|none|是否减免: 0-否, 1-是|是否减免: 0-否, 1-是|
|discountAmount|number|false|none|减免金额|减免金额|
|discountReason|string|false|none|减免原因|减免原因|
|finalAmount|number|false|none|最终费用金额(负为应退)|最终费用金额(负为应退)|
|refundProcessType|integer(int32)|false|none|退款处理方式: 1-退款, 2-暂存客户账户|退款处理方式: 1-退款, 2-暂存客户账户|
|payeeName|string|false|none|收款人|收款人|
|payeeAccount|string|false|none|收款账号|收款账号|
|bankName|string|false|none|开户银行|开户银行|
|licenseStatus|integer(int32)|false|none|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|taxCertStatus|integer(int32)|false|none|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|refundApplyType|integer(int32)|false|none|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|
|signType|integer(int32)|false|none|签字方式: 1-线上, 2-线下|签字方式: 1-线上, 2-线下|
|signAttachments|string|false|none|签字附件|签字附件|
|signTime|string(date-time)|false|none|签字时间|签字时间|
|copyTime|string(date-time)|false|none|首次复制确认单时间|首次复制确认单时间|
|copyBy|string|false|none|首次复制确认单人|首次复制确认单人|
|copyByName|string|false|none|首次复制确认单人名称|首次复制确认单人名称|
|settleTime|string(date-time)|false|none|结算时间|结算时间|
|settleBy|string|false|none|结算人|结算人|
|settleByName|string|false|none|结算人名称|结算人名称|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractPurpose|integer(int32)|false|none|合同用途,字典|合同用途,字典|
|terminateType|integer(int32)|false|none|退租类型:0-到期退租,1-提前退租|退租类型:0-到期退租,1-提前退租|
|terminateDate|string(date-time)|false|none|退租日期|退租日期|
|terminateRoomName|string|false|none|退租房源|退租房源|
|terminateRoomCount|integer(int32)|false|none|退租房源数|退租房源数|
|engineeringCount|integer(int32)|false|none|工程未确认数量|工程未确认数量|
|financeCount|integer(int32)|false|none|财务未确认数量|财务未确认数量|
|refundStatus|integer(int32)|false|none|退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|

<h2 id="tocS_FinancialRefundVo">FinancialRefundVo</h2>

<a id="schemafinancialrefundvo"></a>
<a id="schema_FinancialRefundVo"></a>
<a id="tocSfinancialrefundvo"></a>
<a id="tocsfinancialrefundvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "refundType": 0,
  "refundNo": "string",
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "recordAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "processId": "string",
  "refundStatus": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "recordStatus": 0,
  "confirmStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "isThirdAgent": true,
  "agentAttachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

退款单基本信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|refundType|integer(int32)|false|none|退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|refundNo|string|false|none|退款单号|退款单号|
|bizId|string|false|none|业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|string|false|none|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|string(date-time)|false|none|退款申请时间|退款申请时间|
|refundAmount|number|false|none|退款金额|退款金额|
|recordAmount|number|false|none|已记账金额|已记账金额|
|feeType|string|false|none|退款费用类型|退款费用类型|
|refundWay|integer(int32)|false|none|退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|string|false|none|收款方姓名|收款方姓名|
|receiverBank|string|false|none|收款方开户行|收款方开户行|
|receiverAccount|string|false|none|收款方银行账号|收款方银行账号|
|refundRemark|string|false|none|退款申请说明|退款申请说明|
|refundTime|string(date-time)|false|none|退款时间|退款时间|
|processId|string|false|none|审批流程id|审批流程id|
|refundStatus|integer(int32)|false|none|退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏|
|approveStatus|integer(int32)|false|none|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|approveTime|string(date-time)|false|none|审批通过时间|审批通过时间|
|recordStatus|integer(int32)|false|none|记账状态: 0-未记账,1-已记账|记账状态: 0-未记账,1-已记账|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|roomYardStatus|integer(int32)|false|none|一房一码退款状态|一房一码退款状态|
|attachments|string|false|none|附件|附件|
|isThirdAgent|boolean|false|none|是否第三方代收: 0-否,1-是|是否第三方代收: 0-否,1-是|
|agentAttachments|string|false|none|退款申请单|退款申请单|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|string|false|none|作废原因|作废原因|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

