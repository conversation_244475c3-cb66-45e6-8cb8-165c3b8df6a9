<template>
	<div class="container">
		<div class="content">
			<!-- 顶部状态切换 -->
			<a-tabs v-model:activeKey="activeType" size="large" hide-content @change="handleChangeType"
				style="margin-bottom: 16px;">
				<template #extra>
					<a-space>
						<a-button type="primary" @click="handleAdd">
							<template #icon>
								<icon-plus />
							</template>
							新增营收
						</a-button>
						<a-button @click="handleImport">
							<template #icon>
								<icon-import />
							</template>
							导入
						</a-button>
						<a-button @click="handleExport" style="margin-right: 16px;">
							<template #icon>
								<icon-export />
							</template>
							导出
						</a-button>
					</a-space>
				</template>
				<a-tab-pane v-for="item in typeOptions" :key="item.value" :title="item.label" />
			</a-tabs>

			<!-- 搜索区域 -->
			<a-card class="general-card">
				<a-row>
					<a-col :flex="1">
						<a-form :model="formModel" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }"
							label-align="right">
							<a-row :gutter="16">
								<a-col :span="8">
									<a-form-item field="projectId" label="项目">
										<ProjectTreeSelect v-model="formModel.projectId"
											@change="handleProjectChange" />
									</a-form-item>
								</a-col>
								<a-col :span="8">
									<a-form-item field="contractNo" label="合同编号">
										<a-input v-model="formModel.contractNo" placeholder="请输入合同编号" allow-clear />
									</a-form-item>
								</a-col>
								<a-col :span="8">
									<a-form-item field="customerName" label="承租人">
										<a-input v-model="formModel.customerName" placeholder="请输入承租人" allow-clear />
									</a-form-item>
								</a-col>
								<a-col :span="8">
									<a-form-item field="roomName" label="租赁单元">
										<a-input v-model="formModel.roomName" placeholder="请输入租赁单元" allow-clear />
									</a-form-item>
								</a-col>
								<a-col :span="8">
									<a-form-item field="revenueMonth" label="营收月">
										<a-month-picker v-model="formModel.revenueMonth" placeholder="请选择营收月"
											style="width: 100%" />
									</a-form-item>
								</a-col>
								<a-col :span="8">
									<a-form-item field="reportDate" label="提报日期">
										<a-range-picker v-model="reportDateRange" style="width: 100%"
											@change="handleReportDateChange" />
									</a-form-item>
								</a-col>
								<a-col :span="8">
									<a-form-item field="createByName" label="创建人">
										<a-input v-model="formModel.createByName" placeholder="请输入创建人" allow-clear />
									</a-form-item>
								</a-col>
								<a-col :span="8">
									<a-form-item field="createTime" label="创建日期">
										<a-range-picker v-model="createTimeRange" style="width: 100%"
											@change="handleCreateTimeChange" />
									</a-form-item>
								</a-col>
							</a-row>
						</a-form>
					</a-col>
					<a-divider style="height: 84px" direction="vertical" />
					<a-col :flex="'86px'" style="text-align: right">
						<a-space direction="vertical" :size="18">
							<a-button type="primary" @click="search">
								<template #icon>
									<icon-search />
								</template>
								查询
							</a-button>
							<a-button @click="reset">
								<template #icon>
									<icon-refresh />
								</template>
								重置
							</a-button>
						</a-space>
					</a-col>
				</a-row>
				<a-divider style="margin-top: 0" />

				<!-- 批量操作 -->
				<a-row style="margin-bottom: 16px" v-if="selectedRowKeys.length > 0">
					<a-col :span="24">
						<a-space>
							<span>已选择 {{ selectedRowKeys.length }} 项</span>
							<a-button type="primary" @click="handleBatchApprove" :disabled="!canBatchApprove">
								批量审核
							</a-button>
							<a-button @click="handleBatchDelete" status="danger">
								批量删除
							</a-button>
							<a-button @click="clearSelection">
								取消选择
							</a-button>
						</a-space>
					</a-col>
				</a-row>

				<!-- 表格区域 -->
				<a-table ref="tableRef" row-key="id" :loading="loading" :pagination="pagination" :columns="columns"
					:scroll="{ x: 1 }" :data="tableData" :bordered="{ cell: true }" :row-selection="rowSelection"
					@page-change="onPageChange" @page-size-change="onPageSizeChange">
					<template #index="{ rowIndex }">
						{{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
					</template>
					<template #revenueAmount="{ record }">
						<span style="color: #165DFF; font-weight: 500;">
							¥{{ formatAmount(record.revenueAmount) }}
						</span>
					</template>
					<template #isConfirm="{ record }">
						<a-tag :color="record.isConfirm ? 'green' : 'orange'">
							{{ record.isConfirm ? '已审核' : '待审核' }}
						</a-tag>
					</template>
					<template #attachment="{ record }">
						<a-button v-if="record.attachment" type="text" size="mini"
							@click="handleViewAttachment(record)">
							查看附件
						</a-button>
						<span v-else>-</span>
					</template>
					<template #operations="{ record }">
						<a-space>
							<!-- 查看详情 -->
							<a-button type="text" size="mini" @click="handleView(record)">
								查看
							</a-button>

							<!-- 编辑 - 未审核状态 -->
							<a-button type="text" size="mini" @click="handleEdit(record)" v-if="!record.isConfirm">
								编辑
							</a-button>

							<!-- 审核 - 未审核状态 -->
							<a-button type="text" size="mini" @click="handleApprove(record)" v-if="!record.isConfirm">
								审核
							</a-button>
 
							<!-- 取消审核 - 已审核状态 -->
							<a-button type="text" size="mini" @click="handleCancelApprove(record)"
								v-if="record.isConfirm">
								取消审核
							</a-button>
							<!-- 删除 -->
							<a-button type="text" size="mini" @click="handleDelete(record)">
								删除	
							</a-button>

							<!-- 更多操作下拉菜单 -->
							<!-- <a-dropdown @select="handleMoreAction($event, record)">
								<a-button type="text" size="mini" color="#333">
									更多
									<icon-down />
								</a-button>
								<template #content>
									<a-doption value="delete" class="danger-action">
										删除
									</a-doption>
								</template>
		</a-dropdown> -->
						</a-space>
					</template>
				</a-table>
			</a-card>

			<!-- 新增/编辑营收弹窗 -->
			<RevenueFormModal ref="revenueFormRef" @success="onSuccess" />

			<!-- 营收详情弹窗 -->
			<RevenueDetailModal ref="revenueDetailRef" />

			<!-- 审核弹窗 -->
			<RevenueApproveModal ref="revenueApproveRef" @success="onSuccess" />

			<!-- 导入弹窗 -->
			<RevenueImportModal ref="revenueImportRef" @success="onSuccess" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import {
	getRevenueDetail,
	getRevenueList,
	deleteRevenue,
	deleteRevenueById,
	approveRevenue,
	cancelApproveRevenue,
	exportRevenueList,
	type ContractRevenueQueryDTO,
	type ContractRevenueVO
} from '@/api/revenue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import RevenueFormModal from './components/RevenueFormModal.vue'
import RevenueDetailModal from './components/RevenueDetailModal.vue'
import RevenueApproveModal from './components/RevenueApproveModal.vue'
import RevenueImportModal from './components/RevenueImportModal.vue'
// 格式化金额的工具函数
const formatAmount = (amount: number | undefined) => {
	if (!amount) return '0.00'
	return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 状态选项
const typeOptions = [
	{ label: '全部', value: '' },
	{ label: '待审核', value: '0' },
	{ label: '已审核', value: '1' }
]

// 响应式数据
const activeType = ref('')
const loading = ref(false)
const tableData = ref<ContractRevenueVO[]>([])
const selectedRowKeys = ref<string[]>([])

// 搜索表单
const formModel = reactive<Partial<ContractRevenueQueryDTO>>({
	projectId: '',
	contractNo: '',
	customerName: '',
	roomName: '',
	revenueMonth: '',
	createByName: ''
})

// 日期范围
const reportDateRange = ref<string[]>([])
const createTimeRange = ref<string[]>([])

// 分页配置
const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showTotal: true,
	showPageSize: true,
	pageSizeOptions: ['10', '20', '50', '100']
})

// 表格列配置
const columns: TableColumnData[] = [
	{
		title: '序号',
		slotName: 'index',
		width: 80,
		align: 'center'
	},
	{
		title: '项目名称',
		dataIndex: 'projectName',
		width: 150,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '合同编号',
		dataIndex: 'contractNo',
		width: 150,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '承租人',
		dataIndex: 'customerName',
		width: 120,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '租赁单元',
		dataIndex: 'roomName',
		width: 150,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '营收月',
		dataIndex: 'revenueMonth',
		width: 100,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '营收金额',
		slotName: 'revenueAmount',
		width: 120,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '提报日期',
		dataIndex: 'reportDate',
		width: 120,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '审核状态',
		slotName: 'isConfirm',
		width: 100,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '审核人',
		dataIndex: 'confirmByName',
		width: 100,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '审核时间',
		dataIndex: 'confirmTime',
		width: 150,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '附件',
		slotName: 'attachment',
		width: 80,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '创建人',
		dataIndex: 'createByName',
		width: 100,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		width: 150,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '操作',
		slotName: 'operations',
		width: 240,
		fixed: 'right'
	}
]

// 行选择配置
const rowSelection = {
	type: 'checkbox',
	showCheckedAll: true,
	onSelect: (rowKeys: string[]) => {
		selectedRowKeys.value = rowKeys
	},
	onSelectAll: (checked: boolean) => {
		if (checked) {
			selectedRowKeys.value = tableData.value.map(item => item.id!)
		} else {
			selectedRowKeys.value = []
		}
	}
}

// 计算属性
const canBatchApprove = computed(() => {
	return selectedRowKeys.value.some(id => {
		const record = tableData.value.find(item => item.id === id)
		return record && !record.isConfirm
	})
})

// 组件引用
const revenueFormRef = ref()
const revenueDetailRef = ref()
const revenueApproveRef = ref()
const revenueImportRef = ref()

// 方法定义
const handleChangeType = (key: string) => {
	activeType.value = key
	formModel.isConfirm = key === '' ? undefined : key === '1'
	fetchData()
}

const handleProjectChange = () => {
	// 项目变更时的处理逻辑
}

const handleReportDateChange = (dates: string[]) => {
	formModel.reportDateStart = dates?.[0] || ''
	formModel.reportDateEnd = dates?.[1] || ''
}

const handleCreateTimeChange = (dates: string[]) => {
	// 处理创建时间范围变更
}

const search = () => {
	pagination.current = 1
	fetchData()
}

const reset = () => {
	Object.keys(formModel).forEach(key => {
		(formModel as any)[key] = ''
	})
	reportDateRange.value = []
	createTimeRange.value = []
	activeType.value = ''
	pagination.current = 1
	fetchData()
}

const fetchData = async () => {
	loading.value = true
	try {
		const params: ContractRevenueQueryDTO = {
			...formModel,
			pageNum: pagination.current,
			pageSize: pagination.pageSize
		}

		const response = await getRevenueList(params)
		if (response.rows) {
			tableData.value = response.rows || []
			pagination.total = response.total || 0
		}
	} catch (error) {
		Message.error('获取数据失败')
	} finally {
		loading.value = false
	}
}

const onPageChange = (current: number) => {
	pagination.current = current
	fetchData()
}

const onPageSizeChange = (pageSize: number) => {
	pagination.pageSize = pageSize
	pagination.current = 1
	fetchData()
}

// 操作方法
const handleAdd = () => {
	// 传入当前列表查询的项目ID
	const defaultData = {
		projectId: formModel.projectId || ''
	}
	revenueFormRef.value?.open(null, defaultData)
}

const handleEdit = async (record: ContractRevenueVO) => {
	let res = await getRevenueDetail(record.id!)
	//contract customer
	let data = {
		...res.data.revenue,
		contract: res.data.contract,
		customer: res.data.customer
	}
	revenueFormRef.value?.open(data)
}

const handleView = (record: ContractRevenueVO) => {
	revenueDetailRef.value?.open(record)
}

const handleApprove = (record: ContractRevenueVO) => {
	revenueApproveRef.value?.open([record.id!])
}

const handleCancelApprove = (record: ContractRevenueVO) => {
	Modal.confirm({
		title: '确认取消审核',
		content: '确定要取消审核该营收记录吗？',
		onOk: async () => {
			try {
				await cancelApproveRevenue(record.id!)
				Message.success('取消审核成功')
				fetchData()
			} catch (error) {
				Message.error('取消审核失败')
			}
		}
	})
}

const handleViewAttachment = (record: ContractRevenueVO) => {
	if (record.attachment) {
		window.open(record.attachment, '_blank')
	}
}

const handleMoreAction = (action: string, record: ContractRevenueVO) => {
	switch (action) {
		case 'delete':
			handleDelete(record)
			break
	}
}

const handleDelete = (record: ContractRevenueVO) => {
	Modal.confirm({
		title: '确认删除',
		content: '确定要删除该营收记录吗？删除后无法恢复。',
		onOk: async () => {
			try {
				await deleteRevenueById(record.id!)
				Message.success('删除成功')
				fetchData()
			} catch (error) {
				Message.error('删除失败')
			}
		}
	})
}

const handleBatchApprove = () => {
	const pendingIds = selectedRowKeys.value.filter(id => {
		const record = tableData.value.find(item => item.id === id)
		return record && !record.isConfirm
	})

	if (pendingIds.length === 0) {
		Message.warning('请选择待审核的记录')
		return
	}

	revenueApproveRef.value?.open(pendingIds)
}

const handleBatchDelete = () => {
	if (selectedRowKeys.value.length === 0) {
		Message.warning('请选择要删除的记录')
		return
	}

	Modal.confirm({
		title: '确认批量删除',
		content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？删除后无法恢复。`,
		onOk: async () => {
			try {
				await deleteRevenue(selectedRowKeys.value)
				Message.success('批量删除成功')
				clearSelection()
				fetchData()
			} catch (error) {
				Message.error('批量删除失败')
			}
		}
	})
}

const clearSelection = () => {
	selectedRowKeys.value = []
}

const handleImport = () => {
	// 传入当前列表查询的项目ID
	const defaultData = {
		projectId: formModel.projectId || ''
	}
	revenueImportRef.value?.open(defaultData)
}

const handleExport = async () => {
	try {
		const params = {
			...formModel,
			pageNum: 1,
			pageSize: 999999
		}
		await exportRevenueList(params)
		Message.success('导出成功')
	} catch (error) {
		Message.error('导出失败')
	}
}

const onSuccess = () => {
	fetchData()
	clearSelection()
}

// 生命周期
onMounted(() => {
	fetchData()
})
</script>

<style scoped>
.container {
	padding: 0 20px 20px 20px;
}

.content {
	background: #fff;
	border-radius: 4px;
}

.general-card {
	margin-bottom: 16px;
}

:deep(.danger-action) {
	color: #f53f3f;
}

:deep(.arco-table-cell) {
	padding: 8px 12px;
}
</style>