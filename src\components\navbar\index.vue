<template>
  <div :class="['navbar', appStore.navbarTheme]">
    <div class="left-side">
      <!-- <a-space> -->
      <img v-if="appStore.navbarTheme === 'light'" alt="logo" src="../../assets/images/logo.png" class="logo" />
      <img v-else alt="logo" src="../../assets/images/logo-white.png" class="logo" />
      <div class="logo-text">资产管理平台</div>
      
      <!-- 中心切换功能 -->
      <div class="center-switcher">
        <a-dropdown trigger="click" @select="handleCenterSwitch">
          <div class="switcher-trigger">
            <span class="current-center-text">{{ centerStore.getCenterName }}</span>
            <img src="../../assets/icons/svg/switcher.png" alt="切换" class="switcher-icon" />
          </div>
          <template #content>
            <a-doption 
              v-for="center in centerOptions" 
              :key="center.value"
              :value="center.value"
              :class="{ 'selected-option': currentCenter === center.value }"
            >
              <div class="dropdown-item">
                <span>{{ center.label }}</span>
                <icon-check v-if="currentCenter === center.value" class="check-icon" />
              </div>
            </a-doption>
          </template>
        </a-dropdown>
      </div>
      
      <!-- <a-typography-title
          :style="{ margin: 0, fontSize: '18px' }"
          :heading="5"
        >
          wy
        </a-typography-title> -->
      <!-- <icon-menu-fold
          v-if="!topMenu && appStore.device === 'mobile'"
          style="font-size: 22px; cursor: pointer"
          @click="toggleDrawerMenu"
        /> -->
      <!-- </a-space> -->
    </div>
    <div class="center-side">
      <Menu v-if="topMenu" />
    </div>
    <ul class="right-side">
      <!-- <li>
        <a-tooltip :content="$t('settings.search')">
          <a-button class="nav-btn" type="outline" :shape="'circle'">
            <template #icon>
              <icon-search />
            </template>
</a-button>
</a-tooltip>
</li> -->
      <!-- <li>
        <a-tooltip :content="$t('settings.language')">
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="setDropDownVisible"
          >
            <template #icon>
              <icon-language />
            </template>
          </a-button>
        </a-tooltip>
        <a-dropdown trigger="click" @select="changeLocale as any">
          <div ref="triggerBtn" class="trigger-btn"></div>
          <template #content>
            <a-doption
              v-for="item in locales"
              :key="item.value"
              :value="item.value"
            >
              <template #icon>
                <icon-check v-show="item.value === currentLocale" />
              </template>
              {{ item.label }}
            </a-doption>
          </template>
        </a-dropdown>
      </li> -->
      <!-- <li>
        <a-tooltip
          :content="
            theme === 'light'
              ? $t('settings.navbar.theme.toDark')
              : $t('settings.navbar.theme.toLight')
          "
        >
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="handleToggleTheme"
          >
            <template #icon>
              <icon-moon-fill v-if="theme === 'dark'" />
              <icon-sun-fill v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li> -->
      <!-- <li>
        <a-tooltip :content="$t('settings.navbar.alerts')">
          <div class="message-box-trigger">
            <a-badge :count="9" dot>
              <a-button
                class="nav-btn"
                type="outline"
                :shape="'circle'"
                @click="setPopoverVisible"
              >
                <icon-notification />
              </a-button>
            </a-badge>
          </div>
        </a-tooltip>
        <a-popover
          trigger="click"
          :arrow-style="{ display: 'none' }"
          :content-style="{ padding: 0, minWidth: '400px' }"
          content-class="message-popover"
        >
          <div ref="refBtn" class="ref-btn"></div>
          <template #content>
            <message-box />
          </template>
        </a-popover>
      </li> -->
      <li>
        <a-tooltip :content="isFullscreen
          ? $t('settings.navbar.screen.toExit')
          : $t('settings.navbar.screen.toFull')
          ">
          <a-button class="nav-btn" type="outline" :shape="'circle'" @click="toggleFullScreen">
            <template #icon>
              <icon-fullscreen-exit v-if="isFullscreen" />
              <icon-fullscreen v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip :content="$t('settings.title')">
          <a-button class="nav-btn" type="outline" :shape="'circle'" @click="setVisible">
            <template #icon>
              <icon-settings />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <li>
        <a-dropdown trigger="click">
          <a-avatar :size="32" :style="{ marginRight: '8px', cursor: 'pointer' }">
            <!-- <img alt="avatar" :src="avatar" /> -->
            <img alt="avatar" src="../../assets/images/user/avatar.png" />
          </a-avatar>
          <template #content>
            <a-doption>
              <a-space @click="switchRoles">
                <icon-tag />
                <span>
                  {{ $t('messageBox.switchRoles') }}
                </span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="$router.push({ name: 'Info' })">
                <icon-user />
                <span>
                  {{ $t('messageBox.userCenter') }}
                </span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="$router.push({ name: 'Setting' })">
                <icon-settings />
                <span>
                  {{ $t('messageBox.userSettings') }}
                </span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="handleLogout">
                <icon-export />
                <span>
                  {{ $t('messageBox.logout') }}
                </span>
              </a-space>
            </a-doption>
          </template>
        </a-dropdown>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, inject } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useDark, useToggle, useFullscreen } from '@vueuse/core';
import { useAppStore, useUserStore, useCenterStore } from '@/store';
import { LOCALE_OPTIONS } from '@/locale';
import useLocale from '@/hooks/locale';
import useUser from '@/hooks/user';
import Menu from '@/components/menu/index.vue';
import MessageBox from '../message-box/index.vue';
import type { CenterType } from '@/store/modules/center';

const appStore = useAppStore();
const userStore = useUserStore();
const centerStore = useCenterStore();
const { logout } = useUser();
const { changeLocale, currentLocale } = useLocale();
const { isFullscreen, toggle: toggleFullScreen } = useFullscreen();
const locales = [...LOCALE_OPTIONS];
const avatar = computed(() => {
  return userStore.avatar;
});
const theme = computed(() => {
  return appStore.theme;
});
const topMenu = computed(() => appStore.topMenu && appStore.menu);
const isDark = useDark({
  selector: 'body',
  attribute: 'arco-theme',
  valueDark: 'dark',
  valueLight: 'light',
  storageKey: 'arco-theme',
  onChanged(dark: boolean) {
    // overridden default behavior
    appStore.toggleTheme(false);
  },
});
const toggleTheme = useToggle(false);
const handleToggleTheme = () => {
  // toggleTheme();
};
const setVisible = () => {
  appStore.updateSettings({ globalSettings: true });
};
const refBtn = ref();
const triggerBtn = ref();
const setPopoverVisible = () => {
  const event = new MouseEvent('click', {
    view: window,
    bubbles: true,
    cancelable: true,
  });
  refBtn.value.dispatchEvent(event);
};
const handleLogout = () => {
  logout();
};
const setDropDownVisible = () => {
  const event = new MouseEvent('click', {
    view: window,
    bubbles: true,
    cancelable: true,
  });
  triggerBtn.value.dispatchEvent(event);
};
const switchRoles = async () => {
  const res = await userStore.switchRoles();
  if (typeof res === 'string') {
    Message.success(res);
  }
};
const toggleDrawerMenu = inject('toggleDrawerMenu') as () => void;

// 中心切换功能
const centerOptions = [
  { value: 'asset' as CenterType, label: '资产中心' },
  { value: 'lease' as CenterType, label: '租赁中心' },
  { value: 'commission' as CenterType, label: '佣金中心' },
];
const currentCenter = computed(() => centerStore.getCurrentCenter);
const handleCenterSwitch = (value: CenterType) => {
  centerStore.setCurrentCenter(value);
  Message.success(`已切换到${centerStore.getCenterName}`);
};
</script>

<style scoped lang="less">
.navbar {
  display: flex;
  justify-content: space-between;
  height: 100%;
  background-color: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);

}

.left-side {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding-left: 16px;
  position: relative;
  overflow: hidden;

  .logo {
    width: 100%;
    height: 30px;
  }

  .logo-text {
    flex-shrink: 0;
    font-size: 16px;
    box-sizing: border-box;
    padding-left: 10px;
    color: #0f388b;
    font-weight: 600;
    // width: 100%;
  }

  .center-switcher {
    margin-left: 40px;
    
    .switcher-trigger {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      background: rgba(15, 56, 139, 0.1);
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(15, 56, 139, 0.15);
      }
      
      .current-center-text {
        font-size: 14px;
        font-weight: 500;
        color: #0f388b;
        margin-right: 8px;
        white-space: nowrap;
      }
      
      .switcher-icon {
        width: 16px;
        height: 16px;
        opacity: 0.7;
        transition: all 0.3s ease;
      }
      
      &:hover .switcher-icon {
        opacity: 1;
        transform: rotate(90deg);
      }
    }
  }
}

.center-side {
  flex: 1;
}

.right-side {
  display: flex;
  padding-right: 16px;
  list-style: none;

  :deep(.locale-select) {
    border-radius: 16px;
  }

  li {
    display: flex;
    align-items: center;
    padding: 0 10px;
  }

  a {
    color: var(--color-text-1);
    text-decoration: none;
  }

  .nav-btn {
    border-color: rgb(var(--gray-2));
    color: rgb(var(--gray-8));
    font-size: 16px;
  }

  .trigger-btn,
  .ref-btn {
    position: absolute;
    bottom: 14px;
  }

  .trigger-btn {
    margin-left: 14px;
  }
}

.navbar.dark {
  background-color: var(--color-menu-dark-bg);
  border-color: var(--color-menu-dark-bg);

  .nav-btn {
    color: var(--color-white);
    border-color: rgb(46, 46, 48);
  }
}

.navbar.primary {
  background-color: var(--color-menu-primary-bg);
  border-color: var(--color-menu-primary-bg);

  .nav-btn {
    color: var(--color-white);
    border-color: rgb(var(--primary-5));
  }
}

.navbar.primary,
.navbar.dark {
  .logo {
    height: auto;
    transform: translateX(-4px);
  }

  .logo-text {
    color: var(--color-white);
    padding-left: 0;
  }

  .center-switcher {
    .switcher-trigger {
      background: rgba(255, 255, 255, 0.1);
      
      &:hover {
        background: rgba(255, 255, 255, 0.15);
      }
      
      .current-center-text {
        color: var(--color-white);
      }
      
      .switcher-icon {
        filter: brightness(0) invert(1);
      }
    }
  }
}
</style>

<style lang="less">
.message-popover {
  .arco-popover-content {
    margin-top: 0;
  }
}

// 中心切换下拉菜单样式
.arco-dropdown {
  .dropdown-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    
    .check-icon {
      color: #0f388b;
      font-size: 14px;
    }
  }
  
  .selected-option {
    background-color: rgba(15, 56, 139, 0.1);
    
    .dropdown-item span {
      color: #0f388b;
      font-weight: 500;
    }
  }
}
</style>
