---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/财务结转

<a id="opIdedit_11"></a>

## PUT 修改财务结转

PUT /carryover

修改财务结转

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "carryoverNo": "string",
  "carryoverTime": "2019-08-24T14:15:22Z",
  "fromBizId": "string",
  "fromBizNo": "string",
  "fromBizType": 0,
  "toBizId": "string",
  "toBizNo": "string",
  "toBizType": 0,
  "carryoverAmount": 0,
  "carryoverRemark": "string",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialCarryoverAddDTO](#schemafinancialcarryoveradddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_8"></a>

## POST 新增财务结转

POST /carryover

新增财务结转

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "carryoverNo": "string",
  "carryoverTime": "2019-08-24T14:15:22Z",
  "fromBizId": "string",
  "fromBizNo": "string",
  "fromBizType": 0,
  "toBizId": "string",
  "toBizNo": "string",
  "toBizType": 0,
  "carryoverAmount": 0,
  "carryoverRemark": "string",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialCarryoverAddDTO](#schemafinancialcarryoveradddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_13"></a>

## POST 查询财务结转列表

POST /carryover/list

查询财务结转列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "carryoverNo": "string",
  "carryoverTimeStart": "string",
  "carryoverTimeEnd": "string",
  "fromBizId": "string",
  "fromBizNo": "string",
  "fromBizType": 0,
  "fromSubjectId": "string",
  "toBizId": "string",
  "toBizNo": "string",
  "toBizType": 0,
  "toSubjectId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialCarryoverQueryDTO](#schemafinancialcarryoverquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdlist_19"></a>

## GET 查询财务结转列表

GET /carryover/list

查询财务结转列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|carryoverNo|query|string| 否 | 结转单号|结转单号|
|carryoverTime|query|string(date-time)| 否 | 结转时间|结转时间|
|fromBizId|query|string| 否 | 转出业务id:根据业务类型定单id、合同id、流水id|转出业务id:根据业务类型定单id、合同id、流水id|
|fromBizNo|query|string| 否 | 转出业务单号:根据业务类型定单号、合同号、流水号|转出业务单号:根据业务类型定单号、合同号、流水号|
|fromBizType|query|integer(int32)| 否 | 转出业务类型:0:定单、1:合同、2:未明流水|转出业务类型:0:定单、1:合同、2:未明流水|
|toBizId|query|string| 否 | 转入业务id:根据业务类型定单id、合同id、流水id|转入业务id:根据业务类型定单id、合同id、流水id|
|toBizNo|query|string| 否 | 转入业务单号:根据业务类型定单号、合同号、流水号|转入业务单号:根据业务类型定单号、合同号、流水号|
|toBizType|query|integer(int32)| 否 | 转入业务类型:0:定单、1:合同、2:未明流水|转入业务类型:0:定单、1:合同、2:未明流水|
|carryoverAmount|query|number| 否 | 结转金额|结转金额|
|carryoverRemark|query|string| 否 | 结转说明|结转说明|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdexport_16"></a>

## POST 导出询财务结转列表

POST /carryover/export

导出询财务结转列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|carryoverNo|query|string| 否 | 结转单号|结转单号|
|carryoverTimeStart|query|string| 否 | 结转开始时间|结转开始时间|
|carryoverTimeEnd|query|string| 否 | 结转结束时间|结转结束时间|
|fromBizId|query|string| 否 | 转出业务id:根据业务类型定单id、合同id、流水id|转出业务id:根据业务类型定单id、合同id、流水id|
|fromBizNo|query|string| 否 | 转出业务单号:根据业务类型定单号、合同号、流水号|转出业务单号:根据业务类型定单号、合同号、流水号|
|fromBizType|query|integer(int32)| 否 | 转出业务类型:0:定单、1:合同、2:未明流水|转出业务类型:0:定单、1:合同、2:未明流水|
|fromSubjectId|query|string| 否 | 转出费用科目id|转出费用科目id|
|toBizId|query|string| 否 | 转入业务id:根据业务类型定单id、合同id、流水id|转入业务id:根据业务类型定单id、合同id、流水id|
|toBizNo|query|string| 否 | 转入业务单号:根据业务类型定单号、合同号、流水号|转入业务单号:根据业务类型定单号、合同号、流水号|
|toBizType|query|integer(int32)| 否 | 转入业务类型:0:定单、1:合同、2:未明流水|转入业务类型:0:定单、1:合同、2:未明流水|
|toSubjectId|query|string| 否 | 转入费用科目id|转入费用科目id|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIddetail_9"></a>

## GET 结转单详情接口

GET /carryover/detail

根据结转单ID查询结转单详情信息（包含结转流水明细列表）

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||结转单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"carryoverInfo":{"id":"string","projectId":"string","projectName":"string","carryoverNo":"string","carryoverTime":"2019-08-24T14:15:22Z","fromBizId":"string","fromBizNo":"string","fromBizType":0,"fromSubjectName":"string","toBizId":"string","toBizNo":"string","toBizType":0,"toSubjectName":"string","carryoverAmount":0,"carryoverRemark":"string","createByName":"string","updateByName":"string","isDel":true},"carryoverFlowList":[{"id":"string","carryoverId":"string","flowId":"string","flowNo":"string","fromBizId":"string","fromBizNo":"string","fromBizType":0,"fromSubjectId":"string","fromSubjectName":"string","toBizId":"string","toBizNo":"string","toBizType":0,"toSubjectId":"string","toSubjectName":"string","carryoverAmount":0,"createByName":"string","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[FinancialCarryoverDetailVo](#schemafinancialcarryoverdetailvo)|

<a id="opIdremove_10"></a>

## DELETE 删除财务结转

DELETE /carryover/delete

删除财务结转

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|query|array[string]| 是 ||财务结转ID列表|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_FinancialCarryoverAddDTO">FinancialCarryoverAddDTO</h2>

<a id="schemafinancialcarryoveradddto"></a>
<a id="schema_FinancialCarryoverAddDTO"></a>
<a id="tocSfinancialcarryoveradddto"></a>
<a id="tocsfinancialcarryoveradddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "carryoverNo": "string",
  "carryoverTime": "2019-08-24T14:15:22Z",
  "fromBizId": "string",
  "fromBizNo": "string",
  "fromBizType": 0,
  "toBizId": "string",
  "toBizNo": "string",
  "toBizType": 0,
  "carryoverAmount": 0,
  "carryoverRemark": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|carryoverNo|string|false|none|结转单号|结转单号|
|carryoverTime|string(date-time)|false|none|结转时间|结转时间|
|fromBizId|string|false|none|转出业务id:根据业务类型定单id、合同id、流水id|转出业务id:根据业务类型定单id、合同id、流水id|
|fromBizNo|string|false|none|转出业务单号:根据业务类型定单号、合同号、流水号|转出业务单号:根据业务类型定单号、合同号、流水号|
|fromBizType|integer(int32)|false|none|转出业务类型:0:定单、1:合同、2:未明流水|转出业务类型:0:定单、1:合同、2:未明流水|
|toBizId|string|false|none|转入业务id:根据业务类型定单id、合同id、流水id|转入业务id:根据业务类型定单id、合同id、流水id|
|toBizNo|string|false|none|转入业务单号:根据业务类型定单号、合同号、流水号|转入业务单号:根据业务类型定单号、合同号、流水号|
|toBizType|integer(int32)|false|none|转入业务类型:0:定单、1:合同、2:未明流水|转入业务类型:0:定单、1:合同、2:未明流水|
|carryoverAmount|number|false|none|结转金额|结转金额|
|carryoverRemark|string|false|none|结转说明|结转说明|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_FinancialCarryoverQueryDTO">FinancialCarryoverQueryDTO</h2>

<a id="schemafinancialcarryoverquerydto"></a>
<a id="schema_FinancialCarryoverQueryDTO"></a>
<a id="tocSfinancialcarryoverquerydto"></a>
<a id="tocsfinancialcarryoverquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "carryoverNo": "string",
  "carryoverTimeStart": "string",
  "carryoverTimeEnd": "string",
  "fromBizId": "string",
  "fromBizNo": "string",
  "fromBizType": 0,
  "fromSubjectId": "string",
  "toBizId": "string",
  "toBizNo": "string",
  "toBizType": 0,
  "toSubjectId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|carryoverNo|string|false|none|结转单号|结转单号|
|carryoverTimeStart|string|false|none|结转开始时间|结转开始时间|
|carryoverTimeEnd|string|false|none|结转结束时间|结转结束时间|
|fromBizId|string|false|none|转出业务id:根据业务类型定单id、合同id、流水id|转出业务id:根据业务类型定单id、合同id、流水id|
|fromBizNo|string|false|none|转出业务单号:根据业务类型定单号、合同号、流水号|转出业务单号:根据业务类型定单号、合同号、流水号|
|fromBizType|integer(int32)|false|none|转出业务类型:0:定单、1:合同、2:未明流水|转出业务类型:0:定单、1:合同、2:未明流水|
|fromSubjectId|string|false|none|转出费用科目id|转出费用科目id|
|toBizId|string|false|none|转入业务id:根据业务类型定单id、合同id、流水id|转入业务id:根据业务类型定单id、合同id、流水id|
|toBizNo|string|false|none|转入业务单号:根据业务类型定单号、合同号、流水号|转入业务单号:根据业务类型定单号、合同号、流水号|
|toBizType|integer(int32)|false|none|转入业务类型:0:定单、1:合同、2:未明流水|转入业务类型:0:定单、1:合同、2:未明流水|
|toSubjectId|string|false|none|转入费用科目id|转入费用科目id|

<h2 id="tocS_FinancialCarryoverDetailVo">FinancialCarryoverDetailVo</h2>

<a id="schemafinancialcarryoverdetailvo"></a>
<a id="schema_FinancialCarryoverDetailVo"></a>
<a id="tocSfinancialcarryoverdetailvo"></a>
<a id="tocsfinancialcarryoverdetailvo"></a>

```json
{
  "carryoverInfo": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "carryoverNo": "string",
    "carryoverTime": "2019-08-24T14:15:22Z",
    "fromBizId": "string",
    "fromBizNo": "string",
    "fromBizType": 0,
    "fromSubjectName": "string",
    "toBizId": "string",
    "toBizNo": "string",
    "toBizType": 0,
    "toSubjectName": "string",
    "carryoverAmount": 0,
    "carryoverRemark": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "carryoverFlowList": [
    {
      "id": "string",
      "carryoverId": "string",
      "flowId": "string",
      "flowNo": "string",
      "fromBizId": "string",
      "fromBizNo": "string",
      "fromBizType": 0,
      "fromSubjectId": "string",
      "fromSubjectName": "string",
      "toBizId": "string",
      "toBizNo": "string",
      "toBizType": 0,
      "toSubjectId": "string",
      "toSubjectName": "string",
      "carryoverAmount": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|carryoverInfo|[FinancialCarryoverVo](#schemafinancialcarryovervo)|false|none||none|
|carryoverFlowList|[[FinancialCarryoverFlowVo](#schemafinancialcarryoverflowvo)]|false|none|结转流水明细列表|none|

<h2 id="tocS_FinancialCarryoverFlowVo">FinancialCarryoverFlowVo</h2>

<a id="schemafinancialcarryoverflowvo"></a>
<a id="schema_FinancialCarryoverFlowVo"></a>
<a id="tocSfinancialcarryoverflowvo"></a>
<a id="tocsfinancialcarryoverflowvo"></a>

```json
{
  "id": "string",
  "carryoverId": "string",
  "flowId": "string",
  "flowNo": "string",
  "fromBizId": "string",
  "fromBizNo": "string",
  "fromBizType": 0,
  "fromSubjectId": "string",
  "fromSubjectName": "string",
  "toBizId": "string",
  "toBizNo": "string",
  "toBizType": 0,
  "toSubjectId": "string",
  "toSubjectName": "string",
  "carryoverAmount": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

结转流水明细列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|carryoverId|string|false|none|结转单id|结转单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|fromBizId|string|false|none|转出业务id:根据业务类型定单id、合同id、流水id|转出业务id:根据业务类型定单id、合同id、流水id|
|fromBizNo|string|false|none|转出业务单号:根据业务类型订单号、合同号、流水号|转出业务单号:根据业务类型订单号、合同号、流水号|
|fromBizType|integer(int32)|false|none|转出业务类型: 0-定单、1-合同、2-未明流水|转出业务类型: 0-定单、1-合同、2-未明流水|
|fromSubjectId|string|false|none|转出费用科目id|转出费用科目id|
|fromSubjectName|string|false|none|转出费用科目名称|转出费用科目名称|
|toBizId|string|false|none|转入业务id:根据业务类型定单id、合同id、流水id|转入业务id:根据业务类型定单id、合同id、流水id|
|toBizNo|string|false|none|转入业务单号:根据业务类型订单号、合同号、流水号|转入业务单号:根据业务类型订单号、合同号、流水号|
|toBizType|integer(int32)|false|none|转入业务类型: 0-定单、1-合同、2-未明流水|转入业务类型: 0-定单、1-合同、2-未明流水|
|toSubjectId|string|false|none|转入费用科目id|转入费用科目id|
|toSubjectName|string|false|none|转入费用科目名称|转入费用科目名称|
|carryoverAmount|number|false|none|结转金额|结转金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_FinancialCarryoverVo">FinancialCarryoverVo</h2>

<a id="schemafinancialcarryovervo"></a>
<a id="schema_FinancialCarryoverVo"></a>
<a id="tocSfinancialcarryovervo"></a>
<a id="tocsfinancialcarryovervo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "carryoverNo": "string",
  "carryoverTime": "2019-08-24T14:15:22Z",
  "fromBizId": "string",
  "fromBizNo": "string",
  "fromBizType": 0,
  "fromSubjectName": "string",
  "toBizId": "string",
  "toBizNo": "string",
  "toBizType": 0,
  "toSubjectName": "string",
  "carryoverAmount": 0,
  "carryoverRemark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

结转单基本信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|carryoverNo|string|false|none|结转单号|结转单号|
|carryoverTime|string(date-time)|false|none|结转日期|结转日期|
|fromBizId|string|false|none|转出业务id:根据业务类型定单id、合同id、流水id|转出业务id:根据业务类型定单id、合同id、流水id|
|fromBizNo|string|false|none|转出业务单号:根据业务类型定单号、合同号、流水号|转出业务单号:根据业务类型定单号、合同号、流水号|
|fromBizType|integer(int32)|false|none|转出业务类型:0:定单、1:合同、2:未明流水|转出业务类型:0:定单、1:合同、2:未明流水|
|fromSubjectName|string|false|none|转出费用科目|转出费用科目|
|toBizId|string|false|none|转入业务id:根据业务类型定单id、合同id、流水id|转入业务id:根据业务类型定单id、合同id、流水id|
|toBizNo|string|false|none|转入业务单号:根据业务类型定单号、合同号、流水号|转入业务单号:根据业务类型定单号、合同号、流水号|
|toBizType|integer(int32)|false|none|转入业务类型:0:定单、1:合同、2:未明流水|转入业务类型:0:定单、1:合同、2:未明流水|
|toSubjectName|string|false|none|转入费用科目|转入费用科目|
|carryoverAmount|number|false|none|结转金额|结转金额|
|carryoverRemark|string|false|none|结转说明|结转说明|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

