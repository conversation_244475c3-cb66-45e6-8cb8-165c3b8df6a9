# 智能设备页面分离功能实现说明

## 修改概述
将 `src/views/smartDevices/index.vue` 中的智能水电和智能门锁功能完全分离，不再共用按钮、事件和变量。

## 主要修改内容

### 1. 操作按钮分离
**修改前：**
```vue
<a-space>
  <a-button type="primary" @click="handleBindDevice">批量绑定设备</a-button>
  <a-button @click="handleUnbindDevice">批量解绑</a-button>
</a-space>
```

**修改后：**
```vue
<!-- 智能水电操作按钮 -->
<a-space v-if="activeTab === 'water-electric'">
  <a-button type="primary" @click="handleWaterElectricBind">批量绑定设备</a-button>
  <a-button @click="handleWaterElectricUnbind">批量解绑</a-button>
</a-space>
<!-- 智能门锁操作按钮 -->
<a-space v-if="activeTab === 'door-lock'">
  <a-button type="primary" @click="handleDoorLockBind">批量绑定设备</a-button>
  <a-button @click="handleDoorLockUnbind">批量解绑</a-button>
</a-space>
```

### 2. 选中行变量分离
**修改前：**
```javascript
const selectedKeys = ref<string[]>([])
```

**修改后：**
```javascript
// 智能水电选中的行
const waterElectricSelectedKeys = ref<string[]>([])
// 智能门锁选中的行
const doorLockSelectedKeys = ref<string[]>([])
```

### 3. 子组件事件绑定分离
**修改前：**
```vue
<water-electric-tab 
  v-model:selected-keys="selectedKeys"
  @edit-device="handleEditDevice"
  @view-detail="handleViewDetail"
/>
<door-lock-tab 
  v-model:selected-keys="selectedKeys"
  @edit-device="handleEditDevice"
  @view-detail="handleViewDetail"
/>
```

**修改后：**
```vue
<water-electric-tab 
  v-model:selected-keys="waterElectricSelectedKeys"
  @edit-device="handleWaterElectricEdit"
  @view-detail="handleWaterElectricDetail"
/>
<door-lock-tab 
  v-model:selected-keys="doorLockSelectedKeys"
  @edit-device="handleDoorLockEdit"
  @view-detail="handleDoorLockDetail"
/>
```

### 4. 抽屉组件分离
**修改前：**
```vue
<BatchBindDrawer v-if="batchBindVisible" ref="batchBindDrawerRef" />
<DeviceDetailDrawer v-if="detailVisible" ref="deviceDetailDrawerRef" />
```

**修改后：**
```vue
<!-- 智能水电批量绑定抽屉 -->
<BatchBindDrawer
  v-if="waterElectricBatchBindVisible"
  ref="waterElectricBatchBindDrawerRef"
  device-type="water-electric"
/>
<!-- 智能门锁批量绑定抽屉 -->
<BatchBindDrawer
  v-if="doorLockBatchBindVisible"
  ref="doorLockBatchBindDrawerRef"
  device-type="door-lock"
/>
<!-- 智能水电设备详情抽屉 -->
<DeviceDetailDrawer
  v-if="waterElectricDetailVisible"
  ref="waterElectricDetailDrawerRef"
  device-type="water-electric"
/>
<!-- 智能门锁设备详情抽屉 -->
<DeviceDetailDrawer
  v-if="doorLockDetailVisible"
  ref="doorLockDetailDrawerRef"
  device-type="door-lock"
/>
```

### 5. 抽屉状态变量分离
**修改前：**
```javascript
const batchBindVisible = ref(false)
const detailVisible = ref(false)
```

**修改后：**
```javascript
// 智能水电批量绑定抽屉状态
const waterElectricBatchBindVisible = ref(false)
// 智能门锁批量绑定抽屉状态
const doorLockBatchBindVisible = ref(false)
// 智能水电详情抽屉状态
const waterElectricDetailVisible = ref(false)
// 智能门锁详情抽屉状态
const doorLockDetailVisible = ref(false)
```

### 6. 子组件引用分离
**修改前：**
```javascript
const batchBindDrawerRef = ref()
const deviceDetailDrawerRef = ref()
```

**修改后：**
```javascript
const waterElectricBatchBindDrawerRef = ref()
const doorLockBatchBindDrawerRef = ref()
const waterElectricDetailDrawerRef = ref()
const doorLockDetailDrawerRef = ref()
```

### 7. 事件处理方法分离

#### 智能水电方法：
- `handleWaterElectricBind()` - 智能水电批量绑定
- `handleWaterElectricUnbind()` - 智能水电批量解绑
- `handleWaterElectricEdit()` - 智能水电编辑（解绑）
- `handleWaterElectricDetail()` - 智能水电查看详情
- `handleWaterElectricBatchBindSuccess()` - 智能水电批量绑定成功回调
- `handleWaterElectricBatchBindCancel()` - 智能水电批量绑定取消回调
- `handleWaterElectricDetailCancel()` - 智能水电详情抽屉取消回调
- `handleWaterElectricDetailRefresh()` - 智能水电详情抽屉刷新回调

#### 智能门锁方法：
- `handleDoorLockBind()` - 智能门锁批量绑定
- `handleDoorLockUnbind()` - 智能门锁批量解绑
- `handleDoorLockEdit()` - 智能门锁编辑（解绑）
- `handleDoorLockDetail()` - 智能门锁查看详情
- `handleDoorLockBatchBindSuccess()` - 智能门锁批量绑定成功回调
- `handleDoorLockBatchBindCancel()` - 智能门锁批量绑定取消回调
- `handleDoorLockDetailCancel()` - 智能门锁详情抽屉取消回调
- `handleDoorLockDetailRefresh()` - 智能门锁详情抽屉刷新回调

### 8. API接口分离
- 智能水电使用：`deleteWaterElectricityBindRelation` 来自 `@/api/smartWaterElectricity`
- 智能门锁使用：`unbindRoomDevice` 来自 `@/api/smartLock`

### 9. Tab切换和重置方法更新
更新了 `handleTabChange` 和 `handleReset` 方法，清空两个tab的选中项：
```javascript
// 清空两个tab的选中项
waterElectricSelectedKeys.value = []
doorLockSelectedKeys.value = []
```

## 修改效果
1. 智能水电和智能门锁现在有完全独立的操作按钮
2. 每个功能模块有自己的选中行状态管理
3. 抽屉组件完全分离，支持通过 `device-type` 属性区分设备类型
4. 所有事件处理方法都有明确的功能区分
5. API调用使用各自对应的接口方法
6. 提高了代码的可维护性和可扩展性

## 注意事项
1. 子组件 `BatchBindDrawer` 和 `DeviceDetailDrawer` 需要支持 `device-type` 属性来区分设备类型
2. 智能水电的解绑接口已集成，但需要确保API接口正常工作
3. 所有方法都有清晰的命名规范，便于后续维护
