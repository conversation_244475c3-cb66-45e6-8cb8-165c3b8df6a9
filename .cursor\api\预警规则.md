---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/预警规则

<a id="opIdedit"></a>

## PUT 修改预警规则

PUT /warningRules

修改预警规则

> Body 请求参数

```json
{
  "id": "string",
  "expireStatus": true,
  "expireDays": 0,
  "waterElectricityStatus": true,
  "electricityNum": 0,
  "coldWaterNum": 0,
  "hotWaterNum": 0,
  "billStatus": true,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[WarningRulesAddDTO](#schemawarningrulesadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd"></a>

## POST 新增预警规则

POST /warningRules

新增预警规则

> Body 请求参数

```json
{
  "id": "string",
  "expireStatus": true,
  "expireDays": 0,
  "waterElectricityStatus": true,
  "electricityNum": 0,
  "coldWaterNum": 0,
  "hotWaterNum": 0,
  "billStatus": true,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[WarningRulesAddDTO](#schemawarningrulesadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetInfo"></a>

## GET 获取预警规则详细信息

GET /warningRules/detail

获取预警规则详细信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |预警规则ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove"></a>

## DELETE 删除预警规则

DELETE /warningRules/delete

删除预警规则

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[string]| 是 |预警规则ID列表|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_WarningRulesAddDTO">WarningRulesAddDTO</h2>

<a id="schemawarningrulesadddto"></a>
<a id="schema_WarningRulesAddDTO"></a>
<a id="tocSwarningrulesadddto"></a>
<a id="tocswarningrulesadddto"></a>

```json
{
  "id": "string",
  "expireStatus": true,
  "expireDays": 0,
  "waterElectricityStatus": true,
  "electricityNum": 0,
  "coldWaterNum": 0,
  "hotWaterNum": 0,
  "billStatus": true,
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|expireStatus|boolean|false|none|即将到期配置状态 0-否,1-是|即将到期配置状态 0-否,1-是|
|expireDays|integer(int32)|false|none|合同即将到期前多少天|合同即将到期前多少天|
|waterElectricityStatus|boolean|false|none|水电异常配置状态 0-否,1-是|水电异常配置状态 0-否,1-是|
|electricityNum|integer(int32)|false|none|电度数|电度数|
|coldWaterNum|integer(int32)|false|none|冷水吨数|冷水吨数|
|hotWaterNum|integer(int32)|false|none|热水吨数|热水吨数|
|billStatus|boolean|false|none|催缴状态 0-否,1-是|催缴状态 0-否,1-是|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

