<template>
    <div class="assets-management">
        <div class="assets-title">
            <div class="title-decorator"></div>
            资产/房源管理
        </div>
        <div class="assets-content">
            <div class="assets-items">
                <div class="assets-item">
                    <div class="item-label">集团目标下达</div>
                    <div class="item-count">1</div>
                </div>
                <div class="assets-item">
                    <div class="item-label">项目目标上报</div>
                    <div class="item-count">6</div>
                </div>
                <div class="assets-item">
                    <div class="item-label">待生效房源</div>
                    <div class="item-count danger">12</div>
                </div>
                <div class="assets-item">
                    <div class="item-label">资产面积变更</div>
                    <div class="item-count danger">12</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

</script>

<style lang="less" scoped>
.assets-management {
    background: #fff;
    border-radius: 10px 0 0 0;
    padding: 20px 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    .assets-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
        color: #000;
        position: relative;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        z-index: 2;

        .title-decorator {
            width: 5px;
            height: 19px;
            background-color: #0071ff;
            border-radius: 4px 0px 4px 0px;
            margin-right: 14px;
        }
    }

    .assets-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        position: relative;
        border-radius: 10px 0 0 0;

        .assets-items {
            position: relative;
            z-index: 2;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            height: 100%;
        }

        .assets-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            background: linear-gradient(180deg, #FCFEFF 0%, #EAF4FE 98.28%);
            border-radius: 10px;


            .item-label {
                font-size: 14px;
                color: #3A4252;
                line-height: 1.4;
                margin-bottom: 4px;
            }

            .item-count {
                font-size: 20px;
                color: #3A4252;
                line-height: 1.4;
                font-weight: 500;

                &.danger {
                    color: rgb(var(--danger-6))
                }
            }
        }
    }
}
</style>