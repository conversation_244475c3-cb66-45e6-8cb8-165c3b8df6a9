const approvalRoutes = [
    {
        path: '/approval/contract',
        component: () => import('@/approvalForm/contract.vue'),
        name: 'ApprovalContract',
        meta: { title: '合同审批详情' },
    },
    {
        path: '/approval/index',
        name: 'ApprovalIndex',
        component: () => import('@/approvalForm/approval.vue'),
        meta: { title: '审批详情' },

        // meta: {
        //     hidden: true,
        // },
    },
]

const whiteList = approvalRoutes.map(route => route.path)

export { approvalRoutes, whiteList }