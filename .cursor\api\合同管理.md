---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/合同信息

<a id="opIduploadSignAttachment"></a>

## POST 上传签署附件

POST /contract/uploadSignAttachment

> Body 请求参数

```json
{
  "contractId": "string",
  "attachments": [
    {
      "fileName": "string",
      "fileUrl": "string",
      "status": 0
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractUploadSignAttachmentDTO](#schemacontractuploadsignattachmentdto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|boolean|

<a id="opIdupdateOwner_1"></a>

## POST 批量更新合同责任人

POST /contract/updateOwner

> Body 请求参数

```json
{
  "contractIds": [
    "string"
  ],
  "ownerId": "string",
  "ownerName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractUpdateOwnerDTO](#schemacontractupdateownerdto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|boolean|

<a id="opIdsummary_1"></a>

## POST 合同二级状态数量统计

POST /contract/summary

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "changeDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "otherInfo": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "projectId": "string",
  "roomName": "string",
  "customerName": "string",
  "operateTypes": [
    0
  ],
  "statuses": [
    0
  ],
  "statusTwos": [
    0
  ],
  "signDateStart": "string",
  "signDateEnd": "string",
  "contractPurposes": [
    0
  ],
  "signWays": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractQueryDTO](#schemacontractquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"contractPeriodNotStarted":0,"bondNotFullyReceived":0,"periodNotStartedAndBondNotReceived":0,"normalTermination":0,"earlyTermination":0,"roomChange":0,"renewal":0,"normalTerminationPendingCheckout":0,"earlyTerminationPendingCheckout":0,"pendingTermination":0,"invalid":0,"invalidRenewal":0}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[ContractSummaryVo](#schemacontractsummaryvo)|

<a id="opIdsave_7"></a>

## POST 保存合同信息

POST /contract/save

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "customerName": "string",
  "unenterNum": 0,
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "changeFromId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "venueDeliveryDate": "2019-08-24T14:15:22Z",
  "venueLocation": "string",
  "dailyActivityStartTime": "2019-08-24T14:15:22Z",
  "dailyActivityEndTime": "2019-08-24T14:15:22Z",
  "venuePurpose": "string",
  "otherInfo": "string",
  "contractAttachments": "string",
  "signAttachments": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "changeType": "string",
  "processId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeAttachments": "string",
  "changeExplanation": "string",
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "isDel": true,
  "isSubmit": true,
  "customer": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "contractId": "string",
    "customerId": "string",
    "customerType": 0,
    "customerName": "string",
    "address": "string",
    "phone": "string",
    "idType": "string",
    "idNumber": "string",
    "isEmployee": true,
    "creditCode": "string",
    "contactName": "string",
    "contactPhone": "string",
    "contactIdNumber": "string",
    "legalName": "string",
    "paymentAccount": "string",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "guarantorIdType": "string",
    "guarantorIdNumber": "string",
    "guarantorAddress": "string",
    "guarantorIdFront": "string",
    "guarantorIdBack": "string",
    "invoiceTitle": "string",
    "invoiceTaxNumber": "string",
    "invoiceAddress": "string",
    "invoicePhone": "string",
    "invoiceBankName": "string",
    "invoiceAccountNumber": "string",
    "isDel": true
  },
  "bookings": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "isDel": true
    }
  ],
  "fees": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "feeType": 0,
      "freeType": 0,
      "freeRentMonth": 0,
      "freeRentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "isCharge": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "costs": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "rooms": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "bondPriceType": 0,
      "bondPrice": 0,
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractAddDTO](#schemacontractadddto)| 否 |none|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|string|

<a id="opIdsaveChange"></a>

## POST 合同变更保存接口

POST /contract/saveChange

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "customerName": "string",
  "unenterNum": 0,
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "changeFromId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "venueDeliveryDate": "2019-08-24T14:15:22Z",
  "venueLocation": "string",
  "dailyActivityStartTime": "2019-08-24T14:15:22Z",
  "dailyActivityEndTime": "2019-08-24T14:15:22Z",
  "venuePurpose": "string",
  "otherInfo": "string",
  "contractAttachments": "string",
  "signAttachments": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "changeType": "string",
  "processId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeAttachments": "string",
  "changeExplanation": "string",
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "isDel": true,
  "isSubmit": true,
  "customer": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "contractId": "string",
    "customerId": "string",
    "customerType": 0,
    "customerName": "string",
    "address": "string",
    "phone": "string",
    "idType": "string",
    "idNumber": "string",
    "isEmployee": true,
    "creditCode": "string",
    "contactName": "string",
    "contactPhone": "string",
    "contactIdNumber": "string",
    "legalName": "string",
    "paymentAccount": "string",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "guarantorIdType": "string",
    "guarantorIdNumber": "string",
    "guarantorAddress": "string",
    "guarantorIdFront": "string",
    "guarantorIdBack": "string",
    "invoiceTitle": "string",
    "invoiceTaxNumber": "string",
    "invoiceAddress": "string",
    "invoicePhone": "string",
    "invoiceBankName": "string",
    "invoiceAccountNumber": "string",
    "isDel": true
  },
  "bookings": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "isDel": true
    }
  ],
  "fees": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "feeType": 0,
      "freeType": 0,
      "freeRentMonth": 0,
      "freeRentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "isCharge": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "costs": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "rooms": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "bondPriceType": 0,
      "bondPrice": 0,
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractAddDTO](#schemacontractadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[AjaxResult](#schemaajaxresult)|

<a id="opIdmodify"></a>

## POST 更新合同信息

POST /contract/modify

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "customerName": "string",
  "unenterNum": 0,
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "changeFromId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "venueDeliveryDate": "2019-08-24T14:15:22Z",
  "venueLocation": "string",
  "dailyActivityStartTime": "2019-08-24T14:15:22Z",
  "dailyActivityEndTime": "2019-08-24T14:15:22Z",
  "venuePurpose": "string",
  "otherInfo": "string",
  "contractAttachments": "string",
  "signAttachments": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "changeType": "string",
  "processId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeAttachments": "string",
  "changeExplanation": "string",
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "isDel": true,
  "isSubmit": true,
  "customer": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "contractId": "string",
    "customerId": "string",
    "customerType": 0,
    "customerName": "string",
    "address": "string",
    "phone": "string",
    "idType": "string",
    "idNumber": "string",
    "isEmployee": true,
    "creditCode": "string",
    "contactName": "string",
    "contactPhone": "string",
    "contactIdNumber": "string",
    "legalName": "string",
    "paymentAccount": "string",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "guarantorIdType": "string",
    "guarantorIdNumber": "string",
    "guarantorAddress": "string",
    "guarantorIdFront": "string",
    "guarantorIdBack": "string",
    "invoiceTitle": "string",
    "invoiceTaxNumber": "string",
    "invoiceAddress": "string",
    "invoicePhone": "string",
    "invoiceBankName": "string",
    "invoiceAccountNumber": "string",
    "isDel": true
  },
  "bookings": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "isDel": true
    }
  ],
  "fees": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "feeType": 0,
      "freeType": 0,
      "freeRentMonth": 0,
      "freeRentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "isCharge": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "costs": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "rooms": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "bondPriceType": 0,
      "bondPrice": 0,
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|modifyType|query|integer(int32)| 是 |none|
|Authorization|header|string| 否 |none|
|body|body|[ContractAddDTO](#schemacontractadddto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|boolean|

<a id="opIdlist_7"></a>

## POST 查询合同信息列表

POST /contract/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "changeDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "otherInfo": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "projectId": "string",
  "roomName": "string",
  "customerName": "string",
  "operateTypes": [
    0
  ],
  "statuses": [
    0
  ],
  "statusTwos": [
    0
  ],
  "signDateStart": "string",
  "signDateEnd": "string",
  "contractPurposes": [
    0
  ],
  "signWays": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractQueryDTO](#schemacontractquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[TableDataInfo](#schematabledatainfo)|

<a id="opIdinvalid"></a>

## POST 作废合同

POST /contract/invalid

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|boolean|

<a id="opIdgenerateCost"></a>

## POST 生成应收计划

POST /contract/generateCost

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "customerName": "string",
  "unenterNum": 0,
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "changeFromId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "venueDeliveryDate": "2019-08-24T14:15:22Z",
  "venueLocation": "string",
  "dailyActivityStartTime": "2019-08-24T14:15:22Z",
  "dailyActivityEndTime": "2019-08-24T14:15:22Z",
  "venuePurpose": "string",
  "otherInfo": "string",
  "contractAttachments": "string",
  "signAttachments": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "changeType": "string",
  "processId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeAttachments": "string",
  "changeExplanation": "string",
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "isDel": true,
  "isSubmit": true,
  "customer": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "contractId": "string",
    "customerId": "string",
    "customerType": 0,
    "customerName": "string",
    "address": "string",
    "phone": "string",
    "idType": "string",
    "idNumber": "string",
    "isEmployee": true,
    "creditCode": "string",
    "contactName": "string",
    "contactPhone": "string",
    "contactIdNumber": "string",
    "legalName": "string",
    "paymentAccount": "string",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "guarantorIdType": "string",
    "guarantorIdNumber": "string",
    "guarantorAddress": "string",
    "guarantorIdFront": "string",
    "guarantorIdBack": "string",
    "invoiceTitle": "string",
    "invoiceTaxNumber": "string",
    "invoiceAddress": "string",
    "invoicePhone": "string",
    "invoiceBankName": "string",
    "invoiceAccountNumber": "string",
    "isDel": true
  },
  "bookings": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "isDel": true
    }
  ],
  "fees": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "feeType": 0,
      "freeType": 0,
      "freeRentMonth": 0,
      "freeRentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "isCharge": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "costs": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "rooms": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "bondPriceType": 0,
      "bondPrice": 0,
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractAddDTO](#schemacontractadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"costs":[{"id":"string","contractId":"string","costType":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"isRevenue":true,"isDiscount":true,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","createByName":"string","updateByName":"string","isDel":true,"contractStartDate":"2019-08-24T14:15:22Z","rentTicketPeriod":0}],"bookings":[{"id":"string","contractId":"string","bookingId":"string","bookedRoom":"string","bookerName":"string","bookingReceivedAmount":0,"bookingPaymentDate":"2019-08-24T14:15:22Z","transferBondAmount":0,"transferRentAmount":0,"createByName":"string","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[ContractCostPlanVo](#schemacontractcostplanvo)|

<a id="opIdconfirmSignAttachment"></a>

## POST 确认签署附件

POST /contract/confirmSignAttachment

> Body 请求参数

```json
{
  "contractId": "string",
  "attachments": [
    {
      "fileName": "string",
      "fileUrl": "string",
      "status": 0
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractUploadSignAttachmentDTO](#schemacontractuploadsignattachmentdto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|boolean|

<a id="opIdchangeCallback"></a>

## POST 合同变更审批回调接口

POST /contract/changeCallback

> Body 请求参数

```json
{
  "processId": "string",
  "state": "string",
  "act": "string",
  "data": "string",
  "stateCode": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[OaNotifyDto](#schemaoanotifydto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetContractVersionList"></a>

## GET 查询版本列表

GET /contract/version/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","projectName":"string","contractNo":"string","unionId":"string","version":"string","isCurrent":true,"isLatest":true,"status":0,"statusTwo":0,"approveStatus":0,"operateType":0,"contractType":0,"ourSigningParty":"string","customerName":"string","unenterNum":0,"signWay":0,"signType":0,"originId":"string","changeFromId":"string","landUsage":"string","signerId":"string","signerName":"string","ownerId":"string","ownerName":"string","contractMode":0,"paperContractNo":"string","signDate":"2019-08-24T14:15:22Z","handoverDate":"2019-08-24T14:15:22Z","contractPurpose":0,"dealChannel":0,"assistantId":"string","assistantName":"string","rentYear":0,"rentMonth":0,"rentDay":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","effectDate":"2019-08-24T14:15:22Z","invalidDate":"2019-08-24T14:15:22Z","bookingRelType":0,"bondReceivableDate":"string","bondReceivableType":0,"bondPriceType":0,"bondPrice":0,"chargeWay":0,"rentReceivableDate":"string","rentReceivableType":0,"rentTicketPeriod":0,"rentPayPeriod":0,"increaseGap":0,"increaseRate":0,"increaseRule":"string","estimateRevenue":0,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","revenueType":0,"isIncludePm":true,"pmUnitPrice":0,"pmMonthlyPrice":0,"totalPrice":0,"totalBottomPrice":0,"bizTypeId":"string","bizTypeName":"string","lesseeBrand":"string","businessCategory":"string","openDate":"2019-08-24T14:15:22Z","fireRiskCategory":0,"sprinklerSystem":0,"factoryEngaged":"string","deliverDate":"2019-08-24T14:15:22Z","parkingSpaceType":0,"hasParkingFee":true,"parkingFeeAmount":0,"venueDeliveryDate":"2019-08-24T14:15:22Z","venueLocation":"string","dailyActivityStartTime":"2019-08-24T14:15:22Z","dailyActivityEndTime":"2019-08-24T14:15:22Z","venuePurpose":"string","otherInfo":"string","contractAttachments":"string","signAttachments":"string","attachmentsPlan":"string","isUploadSignature":true,"changeType":"string","processId":"string","changeDate":"2019-08-24T14:15:22Z","changeAttachments":"string","changeExplanation":"string","isSignatureConfirm":true,"isPaperConfirm":true,"isFinish":true,"createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"roomName":"string","terminateDate":"2019-08-24T14:15:22Z","terminateId":"string","customer":{"id":"string","contractId":"string","customerId":"string","customerType":0,"customerName":"string","address":"string","phone":"string","idType":"string","idNumber":"string","isEmployee":true,"creditCode":"string","contactName":"string","contactPhone":"string","contactIdNumber":"string","legalName":"string","paymentAccount":"string","guarantorName":"string","guarantorPhone":"string","guarantorIdType":"string","guarantorIdNumber":"string","guarantorAddress":"string","guarantorIdFront":"string","guarantorIdBack":"string","invoiceTitle":"string","invoiceTaxNumber":"string","invoiceAddress":"string","invoicePhone":"string","invoiceBankName":"string","invoiceAccountNumber":"string","createByName":"string","updateByName":"string","isDel":true},"bookings":[{"id":"string","contractId":"string","bookingId":"string","bookedRoom":"string","bookerName":"string","bookingReceivedAmount":0,"bookingPaymentDate":"2019-08-24T14:15:22Z","transferBondAmount":0,"transferRentAmount":0,"createByName":"string","updateByName":"string","isDel":true}],"fees":[{"id":"string","contractId":"string","feeType":0,"freeType":0,"freeRentMonth":0,"freeRentDay":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","isCharge":true,"remark":"string","createByName":"string","updateByName":"string","isDel":true}],"costs":[{"id":"string","contractId":"string","costType":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"isRevenue":true,"isDiscount":true,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","createByName":"string","updateByName":"string","isDel":true,"contractStartDate":"2019-08-24T14:15:22Z","rentTicketPeriod":0}],"rooms":[{"id":"string","contractId":"string","roomId":"string","roomName":"string","buildingName":"string","floorName":"string","parcelName":"string","stageName":"string","area":0,"standardUnitPrice":0,"bottomPrice":0,"priceUnit":0,"discount":0,"signedUnitPrice":0,"signedMonthlyPrice":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","bondPriceType":0,"bondPrice":0,"createByName":"string","updateByName":"string","isDel":true}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ContractVo](#schemacontractvo)]|false|none||[合同基础信息]|
|» 合同基础信息|[ContractVo](#schemacontractvo)|false|none|合同基础信息|合同基础信息|
|»» id|string|false|none|主键ID|none|
|»» projectId|string|false|none|项目id|项目id|
|»» projectName|string|false|none|项目名称|项目名称|
|»» contractNo|string|false|none|合同号|合同号|
|»» unionId|string|false|none|统一id|统一id|
|»» version|string|false|none|版本号v00x|版本号v00x|
|»» isCurrent|boolean|false|none|是否当前生效版本 0-否,1-是|是否当前生效版本 0-否,1-是|
|»» isLatest|boolean|false|none|是否最新版本 0-否,1-是|是否最新版本 0-否,1-是|
|»» status|integer(int32)|false|none|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|
|»» statusTwo|integer(int32)|false|none|二级状态|二级状态|
|»» approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|»» operateType|integer(int32)|false|none|最新操作类型,0-新签,1-变更条款,2-退租|最新操作类型,0-新签,1-变更条款,2-退租|
|»» contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|»» ourSigningParty|string|false|none|我方签约主体信息|我方签约主体信息|
|»» customerName|string|false|none|客户名/公司名|客户名/公司名|
|»» unenterNum|integer(int32)|false|none|未进场房源数|未进场房源数|
|»» signWay|integer(int32)|false|none|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|
|»» signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|»» originId|string|false|none|续签源合同ID|续签源合同ID|
|»» changeFromId|string|false|none|变更源合同ID|变更源合同ID|
|»» landUsage|string|false|none|用地性质|用地性质|
|»» signerId|string|false|none|合同签约人id|合同签约人id|
|»» signerName|string|false|none|签约人姓名|签约人姓名|
|»» ownerId|string|false|none|责任人id|责任人id|
|»» ownerName|string|false|none|责任人姓名|责任人姓名|
|»» contractMode|integer(int32)|false|none|合同模式,0-标准合同,1-非标合同|合同模式,0-标准合同,1-非标合同|
|»» paperContractNo|string|false|none|纸质合同号|纸质合同号|
|»» signDate|string(date-time)|false|none|签订日期|签订日期|
|»» handoverDate|string(date-time)|false|none|交房日期|交房日期|
|»» contractPurpose|integer(int32)|false|none|合同用途,字典:|合同用途,字典:|
|»» dealChannel|integer(int32)|false|none|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|
|»» assistantId|string|false|none|协助人id|协助人id|
|»» assistantName|string|false|none|协助人姓名|协助人姓名|
|»» rentYear|integer(int32)|false|none|租赁期限-年|租赁期限-年|
|»» rentMonth|integer(int32)|false|none|租赁期限-月|租赁期限-月|
|»» rentDay|integer(int32)|false|none|租赁期限-日|租赁期限-日|
|»» startDate|string(date-time)|false|none|开始日期|开始日期|
|»» endDate|string(date-time)|false|none|结束日期|结束日期|
|»» effectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|»» invalidDate|string(date-time)|false|none|实际失效日期|实际失效日期|
|»» bookingRelType|integer(int32)|false|none|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|
|»» bondReceivableDate|string|false|none|保证金应收日期(天数/具体日期)|保证金应收日期(天数/具体日期)|
|»» bondReceivableType|integer(int32)|false|none|保证金应收日期类型,0-合同签订后, 1-指定日期|保证金应收日期类型,0-合同签订后, 1-指定日期|
|»» bondPriceType|integer(int32)|false|none|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|
|»» bondPrice|number|false|none|保证金金额|保证金金额|
|»» chargeWay|integer(int32)|false|none|收费方式,0-固定租金,1-递增租金,2-营收抽成|收费方式,0-固定租金,1-递增租金,2-营收抽成|
|»» rentReceivableDate|string|false|none|租金应收日期(天数/具体日期)|租金应收日期(天数/具体日期)|
|»» rentReceivableType|integer(int32)|false|none|租金应收日期类型,1-租期开始前,2-租期开始后|租金应收日期类型,1-租期开始前,2-租期开始后|
|»» rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|»» rentPayPeriod|integer(int32)|false|none|租金支付周期|租金支付周期|
|»» increaseGap|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|»» increaseRate|number|false|none|递增率|递增率|
|»» increaseRule|string|false|none|租金递增规则(非标)|租金递增规则(非标)|
|»» estimateRevenue|number|false|none|预估营收额|预估营收额|
|»» percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|»» fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|»» stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|»» revenueType|integer(int32)|false|none|抽点类型: 1-按月营业额, 2-按年营业额|抽点类型: 1-按月营业额, 2-按年营业额|
|»» isIncludePm|boolean|false|none|是否包含物业费|是否包含物业费|
|»» pmUnitPrice|number|false|none|月物业费单价|月物业费单价|
|»» pmMonthlyPrice|number|false|none|月物业费总价|月物业费总价|
|»» totalPrice|number|false|none|合同总价|合同总价|
|»» totalBottomPrice|number|false|none|合同底价|合同底价|
|»» bizTypeId|string|false|none|业态id|业态id|
|»» bizTypeName|string|false|none|业态|业态|
|»» lesseeBrand|string|false|none|承租方品牌|承租方品牌|
|»» businessCategory|string|false|none|经营品类|经营品类|
|»» openDate|string(date-time)|false|none|开业日期|开业日期|
|»» fireRiskCategory|integer(int32)|false|none|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|
|»» sprinklerSystem|integer(int32)|false|none|喷淋系统,0-安装,1-未安装|喷淋系统,0-安装,1-未安装|
|»» factoryEngaged|string|false|none|厂房从事|厂房从事|
|»» deliverDate|string(date-time)|false|none|交接日期|交接日期|
|»» parkingSpaceType|integer(int32)|false|none|车位类型,0-人防,1-非人防|车位类型,0-人防,1-非人防|
|»» hasParkingFee|boolean|false|none|是否包含车位管理费|是否包含车位管理费|
|»» parkingFeeAmount|number|false|none|车位管理费金额|车位管理费金额|
|»» venueDeliveryDate|string(date-time)|false|none|场地交付日期|场地交付日期|
|»» venueLocation|string|false|none|租赁场地位置|租赁场地位置|
|»» dailyActivityStartTime|string(date-time)|false|none|每日活动开始时间|每日活动开始时间|
|»» dailyActivityEndTime|string(date-time)|false|none|每日活动结束时间|每日活动结束时间|
|»» venuePurpose|string|false|none|场地用途|场地用途|
|»» otherInfo|string|false|none|补充条款|补充条款|
|»» contractAttachments|string|false|none|合同附件|合同附件|
|»» signAttachments|string|false|none|签署附件|签署附件[{"fileName": "", "fileUrl":"", "isConfirm":1or0}]|
|»» attachmentsPlan|string|false|none|附件-平面图|附件-平面图|
|»» isUploadSignature|boolean|false|none|是否上传签署文件 0-否,1-是|是否上传签署文件 0-否,1-是|
|»» changeType|string|false|none|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|
|»» processId|string|false|none|流程实例t_oa_process->id|流程实例t_oa_process->id|
|»» changeDate|string(date-time)|false|none|变更日期|变更日期|
|»» changeAttachments|string|false|none|变更附件json数组|变更附件json数组|
|»» changeExplanation|string|false|none|变更说明|变更说明|
|»» isSignatureConfirm|boolean|false|none|是否确认签署 0-否,1-是|是否确认签署 0-否,1-是|
|»» isPaperConfirm|boolean|false|none|是否确认纸质文件 0-否,1-是|是否确认纸质文件 0-否,1-是|
|»» isFinish|boolean|false|none|合同是否完全结束 0-否,1-是|合同是否完全结束 0-否,1-是|
|»» createByName|string|false|none|创建人姓名|创建人姓名|
|»» createTime|string(date-time)|false|none|创建日期|创建日期|
|»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»» updateTime|string(date-time)|false|none|更新日期|更新日期|
|»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|»» roomName|string|false|none|租赁资源|租赁资源|
|»» terminateDate|string(date-time)|false|none|退租日期|退租日期|
|»» terminateId|string|false|none|退租id|退租id|
|»» customer|[ContractCustomerVo](#schemacontractcustomervo)|false|none|合同客户信息|合同客户信息|
|»»» id|string|false|none|主键ID|none|
|»»» contractId|string|false|none|合同id|合同id|
|»»» customerId|string|false|none|客户id|客户id|
|»»» customerType|integer(int32)|false|none|客户类型:1-个人1,2-企业|客户类型:1-个人1,2-企业|
|»»» customerName|string|false|none|客户名/公司名|客户名/公司名|
|»»» address|string|false|none|地址|地址|
|»»» phone|string|false|none|手机号(个人/法人)|手机号(个人/法人)|
|»»» idType|string|false|none|证件类型(个人/法人)|证件类型(个人/法人)|
|»»» idNumber|string|false|none|证件号码(个人/法人)|证件号码(个人/法人)|
|»»» isEmployee|boolean|false|none|是否是员工:0-否,1-是|是否是员工:0-否,1-是|
|»»» creditCode|string|false|none|统一社会信用代码|统一社会信用代码|
|»»» contactName|string|false|none|联系人|联系人|
|»»» contactPhone|string|false|none|联系人手机号|联系人手机号|
|»»» contactIdNumber|string|false|none|联系人身份证号|联系人身份证号|
|»»» legalName|string|false|none|法人名称|法人名称|
|»»» paymentAccount|string|false|none|付款银行账号|付款银行账号|
|»»» guarantorName|string|false|none|担保人姓名|担保人姓名|
|»»» guarantorPhone|string|false|none|担保人手机号|担保人手机号|
|»»» guarantorIdType|string|false|none|担保人证件类型|担保人证件类型|
|»»» guarantorIdNumber|string|false|none|担保人证件号码|担保人证件号码|
|»»» guarantorAddress|string|false|none|担保人通讯地址|担保人通讯地址|
|»»» guarantorIdFront|string|false|none|担保人身份证正面地址|担保人身份证正面地址|
|»»» guarantorIdBack|string|false|none|担保人身份证反面地址|担保人身份证反面地址|
|»»» invoiceTitle|string|false|none|开票名称|开票名称|
|»»» invoiceTaxNumber|string|false|none|开票税号|开票税号|
|»»» invoiceAddress|string|false|none|开票单位地址|开票单位地址|
|»»» invoicePhone|string|false|none|开票电话号码|开票电话号码|
|»»» invoiceBankName|string|false|none|开票开户银行|开票开户银行|
|»»» invoiceAccountNumber|string|false|none|开票银行账户|开票银行账户|
|»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|»» bookings|[[ContractBookingVo](#schemacontractbookingvo)]|false|none|合同定单列表|合同定单列表|
|»»» 合同定单列表|[ContractBookingVo](#schemacontractbookingvo)|false|none|合同定单列表|合同定单列表|
|»»»» id|string|false|none|主键ID|none|
|»»»» contractId|string|false|none|合同id|合同id|
|»»»» bookingId|string|false|none|定单id|定单id|
|»»»» bookedRoom|string|false|none|预定房源|预定房源|
|»»»» bookerName|string|false|none|预定人姓名|预定人姓名|
|»»»» bookingReceivedAmount|number|false|none|定单已收金额|定单已收金额|
|»»»» bookingPaymentDate|string(date-time)|false|none|定单收款(生效)日期|定单收款(生效)日期|
|»»»» transferBondAmount|number|false|none|转保证金金额|转保证金金额|
|»»»» transferRentAmount|number|false|none|转租金金额|转租金金额|
|»»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|»» fees|[[ContractFeeVo](#schemacontractfeevo)]|false|none|合同费用列表|合同费用列表|
|»»» 合同费用列表|[ContractFeeVo](#schemacontractfeevo)|false|none|合同费用列表|合同费用列表|
|»»»» id|string|false|none|主键ID|none|
|»»»» contractId|string|false|none|合同id|合同id|
|»»»» feeType|integer(int32)|false|none|费用类型,1-免租期|费用类型,1-免租期|
|»»»» freeType|integer(int32)|false|none|免租类型,0-装修免租,1-经营免租,2-合同免租|免租类型,0-装修免租,1-经营免租,2-合同免租|
|»»»» freeRentMonth|integer(int32)|false|none|免租月数|免租月数|
|»»»» freeRentDay|integer(int32)|false|none|免租天数|免租天数|
|»»»» startDate|string(date-time)|false|none|开始日期|开始日期|
|»»»» endDate|string(date-time)|false|none|结束日期|结束日期|
|»»»» isCharge|boolean|false|none|免租是否收费:0-否,1-是(退租时使用)|免租是否收费:0-否,1-是(退租时使用)|
|»»»» remark|string|false|none|备注|备注|
|»»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|»» costs|[[ContractCostVo](#schemacontractcostvo)]|false|none|合同应收列表|合同应收列表|
|»»» 合同应收列表|[ContractCostVo](#schemacontractcostvo)|false|none|合同应收列表|合同应收列表|
|»»»» id|string|false|none|主键ID|none|
|»»»» contractId|string|false|none|合同id|合同id|
|»»»» costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|»»»» startDate|string(date-time)|false|none|开始日期|开始日期|
|»»»» endDate|string(date-time)|false|none|结束日期|结束日期|
|»»»» period|integer(int32)|false|none|账单期数|账单期数|
|»»»» customerId|string|false|none|商户id|商户id|
|»»»» customerName|string|false|none|商户名|商户名|
|»»»» roomId|string|false|none|商铺id|商铺id|
|»»»» roomName|string|false|none|商铺名|商铺名|
|»»»» area|number|false|none|商铺面积|商铺面积|
|»»»» subjectId|string|false|none|收款用途id|收款用途id|
|»»»» subjectName|string|false|none|收款用途|收款用途|
|»»»» receivableDate|string(date-time)|false|none|应收日期|应收日期|
|»»»» unitPrice|number|false|none|单价|单价|
|»»»» priceUnit|integer(int32)|false|none|单价单位|单价单位|
|»»»» totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|»»»» discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|»»»» actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|»»»» receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|»»»» isRevenue|boolean|false|none|是否营收提成,0-否,1-是|是否营收提成,0-否,1-是|
|»»»» isDiscount|boolean|false|none|是否优惠,0-否,1-是|是否优惠,0-否,1-是|
|»»»» percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|»»»» fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|»»»» stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|»»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|»»»» contractStartDate|string(date-time)|false|none|合同开始日期, 用来推理租赁月|合同开始日期, 用来推理租赁月|
|»»»» rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|»» rooms|[[ContractRoomVo](#schemacontractroomvo)]|false|none|合同房源列表|合同房源列表|
|»»» 合同房源列表|[ContractRoomVo](#schemacontractroomvo)|false|none|合同房源列表|合同房源列表|
|»»»» id|string|false|none|主键ID|none|
|»»»» contractId|string|false|none|合同id|合同id|
|»»»» roomId|string|false|none|房源id|房源id|
|»»»» roomName|string|false|none|房间名称|房间名称|
|»»»» buildingName|string|false|none|楼栋名称|楼栋名称|
|»»»» floorName|string|false|none|楼层名称|楼层名称|
|»»»» parcelName|string|false|none|地块名称|地块名称|
|»»»» stageName|string|false|none|分期名称|分期名称|
|»»»» area|number|false|none|面积（㎡）|面积（㎡）|
|»»»» standardUnitPrice|number|false|none|标准租金（单价）|标准租金（单价）|
|»»»» bottomPrice|number|false|none|底价|底价|
|»»»» priceUnit|integer(int32)|false|none|单价单位, 使用统一字典, 待定|单价单位, 使用统一字典, 待定|
|»»»» discount|number|false|none|折扣|折扣|
|»»»» signedUnitPrice|number|false|none|签约单价|签约单价|
|»»»» signedMonthlyPrice|number|false|none|签约月总价（元/月）|签约月总价（元/月）|
|»»»» startDate|string(date-time)|false|none|实际开始日期|实际开始日期|
|»»»» endDate|string(date-time)|false|none|实际结束日期|实际结束日期|
|»»»» bondPriceType|integer(int32)|false|none|立项定价保证金金额类型|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|
|»»»» bondPrice|number|false|none|立项定价保证金金额|立项定价保证金金额|
|»»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIdgetSupplementList"></a>

## GET 查询补充协议列表

GET /contract/supplement/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
[{"contractId":"string","supplementType":"string","signDate":"2019-08-24T14:15:22Z","effectDate":"2019-08-24T14:15:22Z","status":0,"approveStatus":0}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ContractSupplementVo](#schemacontractsupplementvo)]|false|none||none|
|» contractId|string|false|none|合同id|合同id|
|» supplementType|string|false|none|补充协议类型|补充协议类型|
|» signDate|string(date-time)|false|none|签订日期|签订日期|
|» effectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|» status|integer(int32)|false|none|一级状态|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|
|» approveStatus|integer(int32)|false|none|审核状态|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|

<a id="opIdgetModifyLog"></a>

## GET 查询修改记录

GET /contract/modifyLog

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","contractId":"string","unionId":"string","modifyType":0,"oldValue":"string","newValue":"string","createByName":"string","updateByName":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ContractModifyLogVo](#schemacontractmodifylogvo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» contractId|string|false|none|合同id|合同id|
|» unionId|string|false|none|合同统一id|合同统一id|
|» modifyType|integer(int32)|false|none|修改类型:0-付款账号、1-联系人、2-承租人手机号、3-签约方式|修改类型:0-付款账号、1-联系人、2-承租人手机号、3-签约方式|
|» oldValue|string|false|none|旧值|旧值|
|» newValue|string|false|none|新值|新值|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIdgetPermission"></a>

## GET 获取用户相关权限

GET /contract/getPermission

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"canSelectNonStandard":true,"notRestrictedBySignTime":true,"solidificationDays":0}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[ContractPermissionVo](#schemacontractpermissionvo)|

<a id="opIdgetById"></a>

## GET 合同查询接口

GET /contract/getById

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","projectName":"string","contractNo":"string","unionId":"string","version":"string","isCurrent":true,"isLatest":true,"status":0,"statusTwo":0,"approveStatus":0,"operateType":0,"contractType":0,"ourSigningParty":"string","customerName":"string","unenterNum":0,"signWay":0,"signType":0,"originId":"string","changeFromId":"string","landUsage":"string","signerId":"string","signerName":"string","ownerId":"string","ownerName":"string","contractMode":0,"paperContractNo":"string","signDate":"2019-08-24T14:15:22Z","handoverDate":"2019-08-24T14:15:22Z","contractPurpose":0,"dealChannel":0,"assistantId":"string","assistantName":"string","rentYear":0,"rentMonth":0,"rentDay":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","effectDate":"2019-08-24T14:15:22Z","invalidDate":"2019-08-24T14:15:22Z","bookingRelType":0,"bondReceivableDate":"string","bondReceivableType":0,"bondPriceType":0,"bondPrice":0,"chargeWay":0,"rentReceivableDate":"string","rentReceivableType":0,"rentTicketPeriod":0,"rentPayPeriod":0,"increaseGap":0,"increaseRate":0,"increaseRule":"string","estimateRevenue":0,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","revenueType":0,"isIncludePm":true,"pmUnitPrice":0,"pmMonthlyPrice":0,"totalPrice":0,"totalBottomPrice":0,"bizTypeId":"string","bizTypeName":"string","lesseeBrand":"string","businessCategory":"string","openDate":"2019-08-24T14:15:22Z","fireRiskCategory":0,"sprinklerSystem":0,"factoryEngaged":"string","deliverDate":"2019-08-24T14:15:22Z","parkingSpaceType":0,"hasParkingFee":true,"parkingFeeAmount":0,"venueDeliveryDate":"2019-08-24T14:15:22Z","venueLocation":"string","dailyActivityStartTime":"2019-08-24T14:15:22Z","dailyActivityEndTime":"2019-08-24T14:15:22Z","venuePurpose":"string","otherInfo":"string","contractAttachments":"string","signAttachments":"string","attachmentsPlan":"string","isUploadSignature":true,"changeType":"string","processId":"string","changeDate":"2019-08-24T14:15:22Z","changeAttachments":"string","changeExplanation":"string","isSignatureConfirm":true,"isPaperConfirm":true,"isFinish":true,"createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"roomName":"string","terminateDate":"2019-08-24T14:15:22Z","terminateId":"string","customer":{"id":"string","contractId":"string","customerId":"string","customerType":0,"customerName":"string","address":"string","phone":"string","idType":"string","idNumber":"string","isEmployee":true,"creditCode":"string","contactName":"string","contactPhone":"string","contactIdNumber":"string","legalName":"string","paymentAccount":"string","guarantorName":"string","guarantorPhone":"string","guarantorIdType":"string","guarantorIdNumber":"string","guarantorAddress":"string","guarantorIdFront":"string","guarantorIdBack":"string","invoiceTitle":"string","invoiceTaxNumber":"string","invoiceAddress":"string","invoicePhone":"string","invoiceBankName":"string","invoiceAccountNumber":"string","createByName":"string","updateByName":"string","isDel":true},"bookings":[{"id":"string","contractId":"string","bookingId":"string","bookedRoom":"string","bookerName":"string","bookingReceivedAmount":0,"bookingPaymentDate":"2019-08-24T14:15:22Z","transferBondAmount":0,"transferRentAmount":0,"createByName":"string","updateByName":"string","isDel":true}],"fees":[{"id":"string","contractId":"string","feeType":0,"freeType":0,"freeRentMonth":0,"freeRentDay":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","isCharge":true,"remark":"string","createByName":"string","updateByName":"string","isDel":true}],"costs":[{"id":"string","contractId":"string","costType":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"isRevenue":true,"isDiscount":true,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","createByName":"string","updateByName":"string","isDel":true,"contractStartDate":"2019-08-24T14:15:22Z","rentTicketPeriod":0}],"rooms":[{"id":"string","contractId":"string","roomId":"string","roomName":"string","buildingName":"string","floorName":"string","parcelName":"string","stageName":"string","area":0,"standardUnitPrice":0,"bottomPrice":0,"priceUnit":0,"discount":0,"signedUnitPrice":0,"signedMonthlyPrice":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","bondPriceType":0,"bondPrice":0,"createByName":"string","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[ContractVo](#schemacontractvo)|

<a id="opIdgetContractDetailFlow"></a>

## GET 查询合同详情流水信息

GET /contract/detail/flow

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","costId":"string","refundId":"string","flowId":"string","flowNo":"string","type":0,"confirmStatus":0,"confirmTime":"2019-08-24T14:15:22Z","confirmUserId":"string","confirmUserName":"string","payAmount":0,"pendingAmount":0,"acctAmount":0,"remark":"string","createByName":"string","createTime":"string","updateByName":"string","isDel":true,"projectId":"string","projectName":"string","entryTime":"2019-08-24T14:15:22Z","payType":0,"payMethod":"string","orderNo":"string","usedAmount":0,"payerName":"string","target":"string","merchant":"string","cumulativeAcctAmount":0}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[CostFlowRelVo](#schemacostflowrelvo)]|false|none||[账单流水关系列表]|
|» 账单流水关系列表|[CostFlowRelVo](#schemacostflowrelvo)|false|none|账单流水关系列表|账单流水关系列表|
|»» id|string|false|none|主键ID|none|
|»» costId|string|false|none|账单id|账单id|
|»» refundId|string|false|none|退款单id|退款单id|
|»» flowId|string|false|none|流水id|流水id|
|»» flowNo|string|false|none|流水单号|流水单号|
|»» type|integer(int32)|false|none|账单类型：1-收款 2-转入 3-转出 4-退款|账单类型：1-收款 2-转入 3-转出 4-退款|
|»» confirmStatus|integer(int32)|false|none|确认状态: 0-未确认、1-自动确认、2-手动确认|确认状态: 0-未确认、1-自动确认、2-手动确认|
|»» confirmTime|string(date-time)|false|none|确认时间|确认时间|
|»» confirmUserId|string|false|none|确认人id|确认人id|
|»» confirmUserName|string|false|none|确认人姓名|确认人姓名|
|»» payAmount|number|false|none|支付金额|支付金额|
|»» pendingAmount|number|false|none|账单待收金额|账单待收金额|
|»» acctAmount|number|false|none|本次记账金额|本次记账金额|
|»» remark|string|false|none|备注|备注|
|»» createByName|string|false|none|创建人姓名|创建人姓名|
|»» createTime|string|false|none|创建时间|创建时间|
|»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|»» projectId|string|false|none|项目id|项目id|
|»» projectName|string|false|none|项目名称|项目名称|
|»» entryTime|string(date-time)|false|none|入账时间|入账时间|
|»» payType|integer(int32)|false|none|支付类型|支付类型|
|»» payMethod|string|false|none|支付方式|支付方式|
|»» orderNo|string|false|none|订单号|订单号|
|»» usedAmount|number|false|none|已使用金额|已使用金额|
|»» payerName|string|false|none|支付人姓名|支付人姓名|
|»» target|string|false|none|支付对象|支付对象|
|»» merchant|string|false|none|收款商户|收款商户|
|»» cumulativeAcctAmount|number|false|none|累计记账金额|累计记账金额|

<a id="opIdgetContractDetailBill"></a>

## GET 查询合同详情页账单

GET /contract/detail/bill

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","projectName":"string","contractNo":"string","unionId":"string","version":"string","isCurrent":true,"isLatest":true,"status":0,"statusTwo":0,"approveStatus":0,"operateType":0,"contractType":0,"ourSigningParty":"string","customerName":"string","unenterNum":0,"signWay":0,"signType":0,"originId":"string","changeFromId":"string","landUsage":"string","signerId":"string","signerName":"string","ownerId":"string","ownerName":"string","contractMode":0,"paperContractNo":"string","signDate":"2019-08-24T14:15:22Z","handoverDate":"2019-08-24T14:15:22Z","contractPurpose":0,"dealChannel":0,"assistantId":"string","assistantName":"string","rentYear":0,"rentMonth":0,"rentDay":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","effectDate":"2019-08-24T14:15:22Z","invalidDate":"2019-08-24T14:15:22Z","bookingRelType":0,"bondReceivableDate":"string","bondReceivableType":0,"bondPriceType":0,"bondPrice":0,"chargeWay":0,"rentReceivableDate":"string","rentReceivableType":0,"rentTicketPeriod":0,"rentPayPeriod":0,"increaseGap":0,"increaseRate":0,"increaseRule":"string","estimateRevenue":0,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","revenueType":0,"isIncludePm":true,"pmUnitPrice":0,"pmMonthlyPrice":0,"totalPrice":0,"totalBottomPrice":0,"bizTypeId":"string","bizTypeName":"string","lesseeBrand":"string","businessCategory":"string","openDate":"2019-08-24T14:15:22Z","fireRiskCategory":0,"sprinklerSystem":0,"factoryEngaged":"string","deliverDate":"2019-08-24T14:15:22Z","parkingSpaceType":0,"hasParkingFee":true,"parkingFeeAmount":0,"venueDeliveryDate":"2019-08-24T14:15:22Z","venueLocation":"string","dailyActivityStartTime":"2019-08-24T14:15:22Z","dailyActivityEndTime":"2019-08-24T14:15:22Z","venuePurpose":"string","otherInfo":"string","contractAttachments":"string","signAttachments":"string","attachmentsPlan":"string","isUploadSignature":true,"changeType":"string","processId":"string","changeDate":"2019-08-24T14:15:22Z","changeAttachments":"string","changeExplanation":"string","isSignatureConfirm":true,"isPaperConfirm":true,"isFinish":true,"createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"roomName":"string","terminateDate":"2019-08-24T14:15:22Z","terminateId":"string","customer":{"id":"string","contractId":"string","customerId":"string","customerType":0,"customerName":"string","address":"string","phone":"string","idType":"string","idNumber":"string","isEmployee":true,"creditCode":"string","contactName":"string","contactPhone":"string","contactIdNumber":"string","legalName":"string","paymentAccount":"string","guarantorName":"string","guarantorPhone":"string","guarantorIdType":"string","guarantorIdNumber":"string","guarantorAddress":"string","guarantorIdFront":"string","guarantorIdBack":"string","invoiceTitle":"string","invoiceTaxNumber":"string","invoiceAddress":"string","invoicePhone":"string","invoiceBankName":"string","invoiceAccountNumber":"string","createByName":"string","updateByName":"string","isDel":true},"bookings":[{"id":"string","contractId":"string","bookingId":"string","bookedRoom":"string","bookerName":"string","bookingReceivedAmount":0,"bookingPaymentDate":"2019-08-24T14:15:22Z","transferBondAmount":0,"transferRentAmount":0,"createByName":"string","updateByName":"string","isDel":true}],"fees":[{"id":"string","contractId":"string","feeType":0,"freeType":0,"freeRentMonth":0,"freeRentDay":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","isCharge":true,"remark":"string","createByName":"string","updateByName":"string","isDel":true}],"costs":[{"id":"string","contractId":"string","costType":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"isRevenue":true,"isDiscount":true,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","createByName":"string","updateByName":"string","isDel":true,"contractStartDate":"2019-08-24T14:15:22Z","rentTicketPeriod":0}],"rooms":[{"id":"string","contractId":"string","roomId":"string","roomName":"string","buildingName":"string","floorName":"string","parcelName":"string","stageName":"string","area":0,"standardUnitPrice":0,"bottomPrice":0,"priceUnit":0,"discount":0,"signedUnitPrice":0,"signedMonthlyPrice":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","bondPriceType":0,"bondPrice":0,"createByName":"string","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[ContractVo](#schemacontractvo)|

<a id="opIddelete"></a>

## DELETE 删除合同

DELETE /contract/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|boolean|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_OaNotifyDto">OaNotifyDto</h2>

<a id="schemaoanotifydto"></a>
<a id="schema_OaNotifyDto"></a>
<a id="tocSoanotifydto"></a>
<a id="tocsoanotifydto"></a>

```json
{
  "processId": "string",
  "state": "string",
  "act": "string",
  "data": "string",
  "stateCode": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|processId|string|false|none||none|
|state|string|false|none||none|
|act|string|false|none||none|
|data|string|false|none||none|
|stateCode|integer(int32)|false|none||none|

<h2 id="tocS_ContractAddDTO">ContractAddDTO</h2>

<a id="schemacontractadddto"></a>
<a id="schema_ContractAddDTO"></a>
<a id="tocScontractadddto"></a>
<a id="tocscontractadddto"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "customerName": "string",
  "unenterNum": 0,
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "changeFromId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "venueDeliveryDate": "2019-08-24T14:15:22Z",
  "venueLocation": "string",
  "dailyActivityStartTime": "2019-08-24T14:15:22Z",
  "dailyActivityEndTime": "2019-08-24T14:15:22Z",
  "venuePurpose": "string",
  "otherInfo": "string",
  "contractAttachments": "string",
  "signAttachments": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "changeType": "string",
  "processId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeAttachments": "string",
  "changeExplanation": "string",
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "isDel": true,
  "isSubmit": true,
  "customer": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "contractId": "string",
    "customerId": "string",
    "customerType": 0,
    "customerName": "string",
    "address": "string",
    "phone": "string",
    "idType": "string",
    "idNumber": "string",
    "isEmployee": true,
    "creditCode": "string",
    "contactName": "string",
    "contactPhone": "string",
    "contactIdNumber": "string",
    "legalName": "string",
    "paymentAccount": "string",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "guarantorIdType": "string",
    "guarantorIdNumber": "string",
    "guarantorAddress": "string",
    "guarantorIdFront": "string",
    "guarantorIdBack": "string",
    "invoiceTitle": "string",
    "invoiceTaxNumber": "string",
    "invoiceAddress": "string",
    "invoicePhone": "string",
    "invoiceBankName": "string",
    "invoiceAccountNumber": "string",
    "isDel": true
  },
  "bookings": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "isDel": true
    }
  ],
  "fees": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "feeType": 0,
      "freeType": 0,
      "freeRentMonth": 0,
      "freeRentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "isCharge": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "costs": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "rooms": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "contractId": "string",
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "bondPriceType": 0,
      "bondPrice": 0,
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|contractNo|string|false|none|合同号|合同号|
|unionId|string|false|none|统一id|统一id|
|version|string|false|none|版本号v00x|版本号v00x|
|isCurrent|boolean|false|none|是否当前生效版本 0-否,1-是|是否当前生效版本 0-否,1-是|
|isLatest|boolean|false|none|是否最新版本 0-否,1-是|是否最新版本 0-否,1-是|
|status|integer(int32)|false|none|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|
|statusTwo|integer(int32)|false|none|二级状态|二级状态|
|approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|operateType|integer(int32)|false|none|最新操作类型,0-新签,1-变更条款,2-退租|最新操作类型,0-新签,1-变更条款,2-退租|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|ourSigningParty|string|false|none|我方签约主体信息|我方签约主体信息|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|unenterNum|integer(int32)|false|none|未进场房源数|未进场房源数|
|signWay|integer(int32)|false|none|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|
|signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|originId|string|false|none|续签源合同ID|续签源合同ID|
|changeFromId|string|false|none|变更源合同ID|变更源合同ID|
|landUsage|string|false|none|用地性质|用地性质|
|signerId|string|false|none|合同签约人id|合同签约人id|
|signerName|string|false|none|签约人姓名|签约人姓名|
|ownerId|string|false|none|责任人id|责任人id|
|ownerName|string|false|none|责任人姓名|责任人姓名|
|contractMode|integer(int32)|false|none|合同模式,0-标准合同,1-非标合同|合同模式,0-标准合同,1-非标合同|
|paperContractNo|string|false|none|纸质合同号|纸质合同号|
|signDate|string(date-time)|false|none|签订日期|签订日期|
|handoverDate|string(date-time)|false|none|交房日期|交房日期|
|contractPurpose|integer(int32)|false|none|合同用途,字典:|合同用途,字典:|
|dealChannel|integer(int32)|false|none|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|
|assistantId|string|false|none|协助人id|协助人id|
|assistantName|string|false|none|协助人姓名|协助人姓名|
|rentYear|integer(int32)|false|none|租赁期限-年|租赁期限-年|
|rentMonth|integer(int32)|false|none|租赁期限-月|租赁期限-月|
|rentDay|integer(int32)|false|none|租赁期限-日|租赁期限-日|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|effectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|invalidDate|string(date-time)|false|none|实际失效日期|实际失效日期|
|bookingRelType|integer(int32)|false|none|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|
|bondReceivableDate|string|false|none|保证金应收日期(天数/具体日期)|保证金应收日期(天数/具体日期)|
|bondReceivableType|integer(int32)|false|none|保证金应收日期类型,0-合同签订后, 1-指定日期|保证金应收日期类型,0-合同签订后, 1-指定日期|
|bondPriceType|integer(int32)|false|none|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|
|bondPrice|number|false|none|保证金金额|保证金金额|
|chargeWay|integer(int32)|false|none|收费方式,0-固定租金,1-递增租金,2-营收抽成|收费方式,0-固定租金,1-递增租金,2-营收抽成|
|rentReceivableDate|string|false|none|租金应收日期(天数/具体日期)|租金应收日期(天数/具体日期)|
|rentReceivableType|integer(int32)|false|none|租金应收日期类型,1-租期开始前,2-租期开始后|租金应收日期类型,1-租期开始前,2-租期开始后|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|rentPayPeriod|integer(int32)|false|none|租金支付周期|租金支付周期|
|increaseGap|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|increaseRate|number|false|none|递增率|递增率|
|increaseRule|string|false|none|租金递增规则(非标)|租金递增规则(非标)|
|estimateRevenue|number|false|none|预估营收额|预估营收额|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息, eg: [{"amount": 0, "percentage": 3}]|none|
|revenueType|integer(int32)|false|none|抽点类型: 1-按月营业额, 2-按年营业额|抽点类型: 1-按月营业额, 2-按年营业额|
|isIncludePm|boolean|false|none|是否包含物业费|是否包含物业费|
|pmUnitPrice|number|false|none|月物业费单价|月物业费单价|
|pmMonthlyPrice|number|false|none|月物业费总价|月物业费总价|
|totalPrice|number|false|none|合同总价|合同总价|
|totalBottomPrice|number|false|none|合同底价|合同底价|
|bizTypeId|string|false|none|业态id|业态id|
|bizTypeName|string|false|none|业态|业态|
|lesseeBrand|string|false|none|承租方品牌|承租方品牌|
|businessCategory|string|false|none|经营品类|经营品类|
|openDate|string(date-time)|false|none|开业日期|开业日期|
|fireRiskCategory|integer(int32)|false|none|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|
|sprinklerSystem|integer(int32)|false|none|喷淋系统,0-安装,1-未安装|喷淋系统,0-安装,1-未安装|
|factoryEngaged|string|false|none|厂房从事|厂房从事|
|deliverDate|string(date-time)|false|none|交接日期|交接日期|
|parkingSpaceType|integer(int32)|false|none|车位类型,0-人防,1-非人防|车位类型,0-人防,1-非人防|
|hasParkingFee|boolean|false|none|是否包含车位管理费|是否包含车位管理费|
|parkingFeeAmount|number|false|none|车位管理费金额|车位管理费金额|
|venueDeliveryDate|string(date-time)|false|none|场地交付日期|场地交付日期|
|venueLocation|string|false|none|租赁场地位置|租赁场地位置|
|dailyActivityStartTime|string(date-time)|false|none|每日活动开始时间|每日活动开始时间|
|dailyActivityEndTime|string(date-time)|false|none|每日活动结束时间|每日活动结束时间|
|venuePurpose|string|false|none|场地用途|场地用途|
|otherInfo|string|false|none|补充条款|补充条款|
|contractAttachments|string|false|none|合同附件[{`"fileName": "", "fileUrl":"", "fileType":""}]|none|
|signAttachments|string|false|none|签署附件|签署附件[{"fileName": "", "fileUrl":"", "isConfirm":1or0}]|
|attachmentsPlan|string|false|none|附件-平面图|附件-平面图|
|isUploadSignature|boolean|false|none|是否上传签署文件 0-否,1-是|是否上传签署文件 0-否,1-是|
|changeType|string|false|none|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|
|processId|string|false|none|流程实例t_oa_process->id|流程实例t_oa_process->id|
|changeDate|string(date-time)|false|none|变更日期|变更日期|
|changeAttachments|string|false|none|变更附件json数组|变更附件json数组|
|changeExplanation|string|false|none|变更说明|变更说明|
|isSignatureConfirm|boolean|false|none|是否确认签署 0-否,1-是|是否确认签署 0-否,1-是|
|isPaperConfirm|boolean|false|none|是否确认纸质文件 0-否,1-是|是否确认纸质文件 0-否,1-是|
|isFinish|boolean|false|none|合同是否完全结束 0-否,1-是|合同是否完全结束 0-否,1-是|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|isSubmit|boolean|false|none|0-暂存,1-提交|0-暂存,1-提交|
|customer|[ContractCustomerAddDTO](#schemacontractcustomeradddto)|false|none||合同客户对象|
|bookings|[[ContractBookingAddDTO](#schemacontractbookingadddto)]|false|none|合同定单对象|合同定单对象|
|fees|[[ContractFeeAddDTO](#schemacontractfeeadddto)]|false|none|合同费用对象|合同费用对象|
|costs|[[ContractCostAddDTO](#schemacontractcostadddto)]|false|none|合同账单对象|合同账单对象|
|rooms|[[ContractRoomAddDTO](#schemacontractroomadddto)]|false|none|合同房源对象|合同房源对象|

<h2 id="tocS_ContractRoomAddDTO">ContractRoomAddDTO</h2>

<a id="schemacontractroomadddto"></a>
<a id="schema_ContractRoomAddDTO"></a>
<a id="tocScontractroomadddto"></a>
<a id="tocscontractroomadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "contractId": "string",
  "roomId": "string",
  "roomName": "string",
  "buildingName": "string",
  "floorName": "string",
  "parcelName": "string",
  "stageName": "string",
  "area": 0,
  "standardUnitPrice": 0,
  "bottomPrice": 0,
  "priceUnit": 0,
  "discount": 0,
  "signedUnitPrice": 0,
  "signedMonthlyPrice": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "bondPriceType": 0,
  "bondPrice": 0,
  "isDel": true
}

```

合同房源对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|contractId|string|false|none|合同id|合同id|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房间名称|房间名称|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorName|string|false|none|楼层名称|楼层名称|
|parcelName|string|false|none|地块名称|地块名称|
|stageName|string|false|none|分期名称|分期名称|
|area|number|false|none|面积（㎡）|面积（㎡）|
|standardUnitPrice|number|false|none|标准租金（单价）|标准租金（单价）|
|bottomPrice|number|false|none|底价|底价|
|priceUnit|integer(int32)|false|none|单价单位, 使用统一字典, 待定|单价单位, 使用统一字典, 待定|
|discount|number|false|none|折扣|折扣|
|signedUnitPrice|number|false|none|签约单价|签约单价|
|signedMonthlyPrice|number|false|none|签约月总价（元/月）|签约月总价（元/月）|
|startDate|string(date-time)|false|none|实际开始日期|实际开始日期|
|endDate|string(date-time)|false|none|实际结束日期|实际结束日期|
|bondPriceType|integer(int32)|false|none|立项定价保证金金额类型|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|
|bondPrice|number|false|none|立项定价保证金金额|立项定价保证金金额|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractCostAddDTO">ContractCostAddDTO</h2>

<a id="schemacontractcostadddto"></a>
<a id="schema_ContractCostAddDTO"></a>
<a id="tocScontractcostadddto"></a>
<a id="tocscontractcostadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "period": 0,
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "unitPrice": 0,
  "priceUnit": 0,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "isRevenue": true,
  "isDiscount": true,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "isDel": true,
  "contractStartDate": "2019-08-24T14:15:22Z",
  "rentTicketPeriod": 0
}

```

合同账单对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|contractId|string|false|none|合同id|合同id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|period|integer(int32)|false|none|账单期数|账单期数|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|area|number|false|none|商铺面积|商铺面积|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途|收款用途|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|unitPrice|number|false|none|单价|单价|
|priceUnit|integer(int32)|false|none|单价单位|单价单位|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|isRevenue|boolean|false|none|是否营收提成,0-否,1-是|是否营收提成,0-否,1-是|
|isDiscount|boolean|false|none|是否优惠,0-否,1-是|是否优惠,0-否,1-是|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息, eg: [{"amount": 0, "percentage": 3}]|none|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractStartDate|string(date-time)|false|none|合同开始日期, 用来推理租赁月|合同开始日期, 用来推理租赁月|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|

<h2 id="tocS_ContractFeeAddDTO">ContractFeeAddDTO</h2>

<a id="schemacontractfeeadddto"></a>
<a id="schema_ContractFeeAddDTO"></a>
<a id="tocScontractfeeadddto"></a>
<a id="tocscontractfeeadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "contractId": "string",
  "feeType": 0,
  "freeType": 0,
  "freeRentMonth": 0,
  "freeRentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "isCharge": true,
  "remark": "string",
  "isDel": true
}

```

合同费用对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|contractId|string|false|none|合同id|合同id|
|feeType|integer(int32)|false|none|费用类型,1-免租期|费用类型,1-免租期|
|freeType|integer(int32)|false|none|免租类型,0-装修免租,1-经营免租,2-合同免租|免租类型,0-装修免租,1-经营免租,2-合同免租|
|freeRentMonth|integer(int32)|false|none|免租月数|免租月数|
|freeRentDay|integer(int32)|false|none|免租天数|免租天数|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|isCharge|boolean|false|none|免租是否收费:0-否,1-是(退租时使用)|免租是否收费:0-否,1-是(退租时使用)|
|remark|string|false|none|备注|备注|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractBookingAddDTO">ContractBookingAddDTO</h2>

<a id="schemacontractbookingadddto"></a>
<a id="schema_ContractBookingAddDTO"></a>
<a id="tocScontractbookingadddto"></a>
<a id="tocscontractbookingadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "contractId": "string",
  "bookingId": "string",
  "bookedRoom": "string",
  "bookerName": "string",
  "bookingReceivedAmount": 0,
  "bookingPaymentDate": "2019-08-24T14:15:22Z",
  "transferBondAmount": 0,
  "transferRentAmount": 0,
  "isDel": true
}

```

合同定单对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|contractId|string|false|none|合同id|合同id|
|bookingId|string|false|none|定单id|定单id|
|bookedRoom|string|false|none|预定房源|预定房源|
|bookerName|string|false|none|预定人姓名|预定人姓名|
|bookingReceivedAmount|number|false|none|定单已收金额|定单已收金额|
|bookingPaymentDate|string(date-time)|false|none|定单收款(生效)日期|定单收款(生效)日期|
|transferBondAmount|number|false|none|转保证金金额|转保证金金额|
|transferRentAmount|number|false|none|转租金金额|转租金金额|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractCustomerAddDTO">ContractCustomerAddDTO</h2>

<a id="schemacontractcustomeradddto"></a>
<a id="schema_ContractCustomerAddDTO"></a>
<a id="tocScontractcustomeradddto"></a>
<a id="tocscontractcustomeradddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "contractId": "string",
  "customerId": "string",
  "customerType": 0,
  "customerName": "string",
  "address": "string",
  "phone": "string",
  "idType": "string",
  "idNumber": "string",
  "isEmployee": true,
  "creditCode": "string",
  "contactName": "string",
  "contactPhone": "string",
  "contactIdNumber": "string",
  "legalName": "string",
  "paymentAccount": "string",
  "guarantorName": "string",
  "guarantorPhone": "string",
  "guarantorIdType": "string",
  "guarantorIdNumber": "string",
  "guarantorAddress": "string",
  "guarantorIdFront": "string",
  "guarantorIdBack": "string",
  "invoiceTitle": "string",
  "invoiceTaxNumber": "string",
  "invoiceAddress": "string",
  "invoicePhone": "string",
  "invoiceBankName": "string",
  "invoiceAccountNumber": "string",
  "isDel": true
}

```

合同客户对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|contractId|string|false|none|合同id|合同id|
|customerId|string|false|none|客户id|客户id|
|customerType|integer(int32)|false|none|客户类型:1-个人1,2-企业|客户类型:1-个人1,2-企业|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|address|string|false|none|地址|地址|
|phone|string|false|none|手机号(个人/法人)|手机号(个人/法人)|
|idType|string|false|none|证件类型(个人/法人)|证件类型(个人/法人)|
|idNumber|string|false|none|证件号码(个人/法人)|证件号码(个人/法人)|
|isEmployee|boolean|false|none|是否是员工:0-否,1-是|是否是员工:0-否,1-是|
|creditCode|string|false|none|统一社会信用代码|统一社会信用代码|
|contactName|string|false|none|联系人|联系人|
|contactPhone|string|false|none|联系人手机号|联系人手机号|
|contactIdNumber|string|false|none|联系人身份证号|联系人身份证号|
|legalName|string|false|none|法人名称|法人名称|
|paymentAccount|string|false|none|付款银行账号|付款银行账号|
|guarantorName|string|false|none|担保人姓名|担保人姓名|
|guarantorPhone|string|false|none|担保人手机号|担保人手机号|
|guarantorIdType|string|false|none|担保人证件类型|担保人证件类型|
|guarantorIdNumber|string|false|none|担保人证件号码|担保人证件号码|
|guarantorAddress|string|false|none|担保人通讯地址|担保人通讯地址|
|guarantorIdFront|string|false|none|担保人身份证正面地址|担保人身份证正面地址|
|guarantorIdBack|string|false|none|担保人身份证反面地址|担保人身份证反面地址|
|invoiceTitle|string|false|none|开票名称|开票名称|
|invoiceTaxNumber|string|false|none|开票税号|开票税号|
|invoiceAddress|string|false|none|开票单位地址|开票单位地址|
|invoicePhone|string|false|none|开票电话号码|开票电话号码|
|invoiceBankName|string|false|none|开票开户银行|开票开户银行|
|invoiceAccountNumber|string|false|none|开票银行账户|开票银行账户|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractUploadSignAttachmentDTO">ContractUploadSignAttachmentDTO</h2>

<a id="schemacontractuploadsignattachmentdto"></a>
<a id="schema_ContractUploadSignAttachmentDTO"></a>
<a id="tocScontractuploadsignattachmentdto"></a>
<a id="tocscontractuploadsignattachmentdto"></a>

```json
{
  "contractId": "string",
  "attachments": [
    {
      "fileName": "string",
      "fileUrl": "string",
      "status": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|contractId|string|false|none|合同ID|合同ID|
|attachments|[[SignAttachmentInfo](#schemasignattachmentinfo)]|false|none|附件json数组|附件json数组[{"fileName": "", "fileUrl":"", "status":0or1or2}]|

<h2 id="tocS_SignAttachmentInfo">SignAttachmentInfo</h2>

<a id="schemasignattachmentinfo"></a>
<a id="schema_SignAttachmentInfo"></a>
<a id="tocSsignattachmentinfo"></a>
<a id="tocssignattachmentinfo"></a>

```json
{
  "fileName": "string",
  "fileUrl": "string",
  "status": 0
}

```

附件json数组

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|fileName|string|false|none|文件名|文件名|
|fileUrl|string|false|none|文件地址|文件地址|
|status|integer(int32)|false|none|是否确认|是否确认 0-待确认 1-待确收 2-已确收|

<h2 id="tocS_ContractUpdateOwnerDTO">ContractUpdateOwnerDTO</h2>

<a id="schemacontractupdateownerdto"></a>
<a id="schema_ContractUpdateOwnerDTO"></a>
<a id="tocScontractupdateownerdto"></a>
<a id="tocscontractupdateownerdto"></a>

```json
{
  "contractIds": [
    "string"
  ],
  "ownerId": "string",
  "ownerName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|contractIds|[string]|true|none|合同id列表|合同id列表|
|» 合同id列表|string|false|none|合同id列表|合同id列表|
|ownerId|string|true|none|责任人id|责任人id|
|ownerName|string|true|none|责任人姓名|责任人姓名|

<h2 id="tocS_ContractBookingVo">ContractBookingVo</h2>

<a id="schemacontractbookingvo"></a>
<a id="schema_ContractBookingVo"></a>
<a id="tocScontractbookingvo"></a>
<a id="tocscontractbookingvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "bookingId": "string",
  "bookedRoom": "string",
  "bookerName": "string",
  "bookingReceivedAmount": 0,
  "bookingPaymentDate": "2019-08-24T14:15:22Z",
  "transferBondAmount": 0,
  "transferRentAmount": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同定单列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|bookingId|string|false|none|定单id|定单id|
|bookedRoom|string|false|none|预定房源|预定房源|
|bookerName|string|false|none|预定人姓名|预定人姓名|
|bookingReceivedAmount|number|false|none|定单已收金额|定单已收金额|
|bookingPaymentDate|string(date-time)|false|none|定单收款(生效)日期|定单收款(生效)日期|
|transferBondAmount|number|false|none|转保证金金额|转保证金金额|
|transferRentAmount|number|false|none|转租金金额|转租金金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractCostVo">ContractCostVo</h2>

<a id="schemacontractcostvo"></a>
<a id="schema_ContractCostVo"></a>
<a id="tocScontractcostvo"></a>
<a id="tocscontractcostvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "period": 0,
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "unitPrice": 0,
  "priceUnit": 0,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "isRevenue": true,
  "isDiscount": true,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractStartDate": "2019-08-24T14:15:22Z",
  "rentTicketPeriod": 0
}

```

合同应收列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|period|integer(int32)|false|none|账单期数|账单期数|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|area|number|false|none|商铺面积|商铺面积|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途|收款用途|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|unitPrice|number|false|none|单价|单价|
|priceUnit|integer(int32)|false|none|单价单位|单价单位|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|isRevenue|boolean|false|none|是否营收提成,0-否,1-是|是否营收提成,0-否,1-是|
|isDiscount|boolean|false|none|是否优惠,0-否,1-是|是否优惠,0-否,1-是|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractStartDate|string(date-time)|false|none|合同开始日期, 用来推理租赁月|合同开始日期, 用来推理租赁月|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|

<h2 id="tocS_ContractCustomerVo">ContractCustomerVo</h2>

<a id="schemacontractcustomervo"></a>
<a id="schema_ContractCustomerVo"></a>
<a id="tocScontractcustomervo"></a>
<a id="tocscontractcustomervo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "customerId": "string",
  "customerType": 0,
  "customerName": "string",
  "address": "string",
  "phone": "string",
  "idType": "string",
  "idNumber": "string",
  "isEmployee": true,
  "creditCode": "string",
  "contactName": "string",
  "contactPhone": "string",
  "contactIdNumber": "string",
  "legalName": "string",
  "paymentAccount": "string",
  "guarantorName": "string",
  "guarantorPhone": "string",
  "guarantorIdType": "string",
  "guarantorIdNumber": "string",
  "guarantorAddress": "string",
  "guarantorIdFront": "string",
  "guarantorIdBack": "string",
  "invoiceTitle": "string",
  "invoiceTaxNumber": "string",
  "invoiceAddress": "string",
  "invoicePhone": "string",
  "invoiceBankName": "string",
  "invoiceAccountNumber": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同客户信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|customerId|string|false|none|客户id|客户id|
|customerType|integer(int32)|false|none|客户类型:1-个人1,2-企业|客户类型:1-个人1,2-企业|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|address|string|false|none|地址|地址|
|phone|string|false|none|手机号(个人/法人)|手机号(个人/法人)|
|idType|string|false|none|证件类型(个人/法人)|证件类型(个人/法人)|
|idNumber|string|false|none|证件号码(个人/法人)|证件号码(个人/法人)|
|isEmployee|boolean|false|none|是否是员工:0-否,1-是|是否是员工:0-否,1-是|
|creditCode|string|false|none|统一社会信用代码|统一社会信用代码|
|contactName|string|false|none|联系人|联系人|
|contactPhone|string|false|none|联系人手机号|联系人手机号|
|contactIdNumber|string|false|none|联系人身份证号|联系人身份证号|
|legalName|string|false|none|法人名称|法人名称|
|paymentAccount|string|false|none|付款银行账号|付款银行账号|
|guarantorName|string|false|none|担保人姓名|担保人姓名|
|guarantorPhone|string|false|none|担保人手机号|担保人手机号|
|guarantorIdType|string|false|none|担保人证件类型|担保人证件类型|
|guarantorIdNumber|string|false|none|担保人证件号码|担保人证件号码|
|guarantorAddress|string|false|none|担保人通讯地址|担保人通讯地址|
|guarantorIdFront|string|false|none|担保人身份证正面地址|担保人身份证正面地址|
|guarantorIdBack|string|false|none|担保人身份证反面地址|担保人身份证反面地址|
|invoiceTitle|string|false|none|开票名称|开票名称|
|invoiceTaxNumber|string|false|none|开票税号|开票税号|
|invoiceAddress|string|false|none|开票单位地址|开票单位地址|
|invoicePhone|string|false|none|开票电话号码|开票电话号码|
|invoiceBankName|string|false|none|开票开户银行|开票开户银行|
|invoiceAccountNumber|string|false|none|开票银行账户|开票银行账户|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractFeeVo">ContractFeeVo</h2>

<a id="schemacontractfeevo"></a>
<a id="schema_ContractFeeVo"></a>
<a id="tocScontractfeevo"></a>
<a id="tocscontractfeevo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "feeType": 0,
  "freeType": 0,
  "freeRentMonth": 0,
  "freeRentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "isCharge": true,
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同费用列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|feeType|integer(int32)|false|none|费用类型,1-免租期|费用类型,1-免租期|
|freeType|integer(int32)|false|none|免租类型,0-装修免租,1-经营免租,2-合同免租|免租类型,0-装修免租,1-经营免租,2-合同免租|
|freeRentMonth|integer(int32)|false|none|免租月数|免租月数|
|freeRentDay|integer(int32)|false|none|免租天数|免租天数|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|isCharge|boolean|false|none|免租是否收费:0-否,1-是(退租时使用)|免租是否收费:0-否,1-是(退租时使用)|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractRoomVo">ContractRoomVo</h2>

<a id="schemacontractroomvo"></a>
<a id="schema_ContractRoomVo"></a>
<a id="tocScontractroomvo"></a>
<a id="tocscontractroomvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "roomId": "string",
  "roomName": "string",
  "buildingName": "string",
  "floorName": "string",
  "parcelName": "string",
  "stageName": "string",
  "area": 0,
  "standardUnitPrice": 0,
  "bottomPrice": 0,
  "priceUnit": 0,
  "discount": 0,
  "signedUnitPrice": 0,
  "signedMonthlyPrice": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "bondPriceType": 0,
  "bondPrice": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同房源列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房间名称|房间名称|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorName|string|false|none|楼层名称|楼层名称|
|parcelName|string|false|none|地块名称|地块名称|
|stageName|string|false|none|分期名称|分期名称|
|area|number|false|none|面积（㎡）|面积（㎡）|
|standardUnitPrice|number|false|none|标准租金（单价）|标准租金（单价）|
|bottomPrice|number|false|none|底价|底价|
|priceUnit|integer(int32)|false|none|单价单位, 使用统一字典, 待定|单价单位, 使用统一字典, 待定|
|discount|number|false|none|折扣|折扣|
|signedUnitPrice|number|false|none|签约单价|签约单价|
|signedMonthlyPrice|number|false|none|签约月总价（元/月）|签约月总价（元/月）|
|startDate|string(date-time)|false|none|实际开始日期|实际开始日期|
|endDate|string(date-time)|false|none|实际结束日期|实际结束日期|
|bondPriceType|integer(int32)|false|none|立项定价保证金金额类型|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|
|bondPrice|number|false|none|立项定价保证金金额|立项定价保证金金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractVo">ContractVo</h2>

<a id="schemacontractvo"></a>
<a id="schema_ContractVo"></a>
<a id="tocScontractvo"></a>
<a id="tocscontractvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "customerName": "string",
  "unenterNum": 0,
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "changeFromId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "venueDeliveryDate": "2019-08-24T14:15:22Z",
  "venueLocation": "string",
  "dailyActivityStartTime": "2019-08-24T14:15:22Z",
  "dailyActivityEndTime": "2019-08-24T14:15:22Z",
  "venuePurpose": "string",
  "otherInfo": "string",
  "contractAttachments": "string",
  "signAttachments": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "changeType": "string",
  "processId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeAttachments": "string",
  "changeExplanation": "string",
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "roomName": "string",
  "terminateDate": "2019-08-24T14:15:22Z",
  "terminateId": "string",
  "customer": {
    "id": "string",
    "contractId": "string",
    "customerId": "string",
    "customerType": 0,
    "customerName": "string",
    "address": "string",
    "phone": "string",
    "idType": "string",
    "idNumber": "string",
    "isEmployee": true,
    "creditCode": "string",
    "contactName": "string",
    "contactPhone": "string",
    "contactIdNumber": "string",
    "legalName": "string",
    "paymentAccount": "string",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "guarantorIdType": "string",
    "guarantorIdNumber": "string",
    "guarantorAddress": "string",
    "guarantorIdFront": "string",
    "guarantorIdBack": "string",
    "invoiceTitle": "string",
    "invoiceTaxNumber": "string",
    "invoiceAddress": "string",
    "invoicePhone": "string",
    "invoiceBankName": "string",
    "invoiceAccountNumber": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "bookings": [
    {
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "fees": [
    {
      "id": "string",
      "contractId": "string",
      "feeType": 0,
      "freeType": 0,
      "freeRentMonth": 0,
      "freeRentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "isCharge": true,
      "remark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "costs": [
    {
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "rooms": [
    {
      "id": "string",
      "contractId": "string",
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "bondPriceType": 0,
      "bondPrice": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

合同基础信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|contractNo|string|false|none|合同号|合同号|
|unionId|string|false|none|统一id|统一id|
|version|string|false|none|版本号v00x|版本号v00x|
|isCurrent|boolean|false|none|是否当前生效版本 0-否,1-是|是否当前生效版本 0-否,1-是|
|isLatest|boolean|false|none|是否最新版本 0-否,1-是|是否最新版本 0-否,1-是|
|status|integer(int32)|false|none|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|
|statusTwo|integer(int32)|false|none|二级状态|二级状态|
|approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|operateType|integer(int32)|false|none|最新操作类型,0-新签,1-变更条款,2-退租|最新操作类型,0-新签,1-变更条款,2-退租|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|ourSigningParty|string|false|none|我方签约主体信息|我方签约主体信息|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|unenterNum|integer(int32)|false|none|未进场房源数|未进场房源数|
|signWay|integer(int32)|false|none|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|
|signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|originId|string|false|none|续签源合同ID|续签源合同ID|
|changeFromId|string|false|none|变更源合同ID|变更源合同ID|
|landUsage|string|false|none|用地性质|用地性质|
|signerId|string|false|none|合同签约人id|合同签约人id|
|signerName|string|false|none|签约人姓名|签约人姓名|
|ownerId|string|false|none|责任人id|责任人id|
|ownerName|string|false|none|责任人姓名|责任人姓名|
|contractMode|integer(int32)|false|none|合同模式,0-标准合同,1-非标合同|合同模式,0-标准合同,1-非标合同|
|paperContractNo|string|false|none|纸质合同号|纸质合同号|
|signDate|string(date-time)|false|none|签订日期|签订日期|
|handoverDate|string(date-time)|false|none|交房日期|交房日期|
|contractPurpose|integer(int32)|false|none|合同用途,字典:|合同用途,字典:|
|dealChannel|integer(int32)|false|none|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|
|assistantId|string|false|none|协助人id|协助人id|
|assistantName|string|false|none|协助人姓名|协助人姓名|
|rentYear|integer(int32)|false|none|租赁期限-年|租赁期限-年|
|rentMonth|integer(int32)|false|none|租赁期限-月|租赁期限-月|
|rentDay|integer(int32)|false|none|租赁期限-日|租赁期限-日|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|effectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|invalidDate|string(date-time)|false|none|实际失效日期|实际失效日期|
|bookingRelType|integer(int32)|false|none|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|
|bondReceivableDate|string|false|none|保证金应收日期(天数/具体日期)|保证金应收日期(天数/具体日期)|
|bondReceivableType|integer(int32)|false|none|保证金应收日期类型,0-合同签订后, 1-指定日期|保证金应收日期类型,0-合同签订后, 1-指定日期|
|bondPriceType|integer(int32)|false|none|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|
|bondPrice|number|false|none|保证金金额|保证金金额|
|chargeWay|integer(int32)|false|none|收费方式,0-固定租金,1-递增租金,2-营收抽成|收费方式,0-固定租金,1-递增租金,2-营收抽成|
|rentReceivableDate|string|false|none|租金应收日期(天数/具体日期)|租金应收日期(天数/具体日期)|
|rentReceivableType|integer(int32)|false|none|租金应收日期类型,1-租期开始前,2-租期开始后|租金应收日期类型,1-租期开始前,2-租期开始后|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|rentPayPeriod|integer(int32)|false|none|租金支付周期|租金支付周期|
|increaseGap|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|increaseRate|number|false|none|递增率|递增率|
|increaseRule|string|false|none|租金递增规则(非标)|租金递增规则(非标)|
|estimateRevenue|number|false|none|预估营收额|预估营收额|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|revenueType|integer(int32)|false|none|抽点类型: 1-按月营业额, 2-按年营业额|抽点类型: 1-按月营业额, 2-按年营业额|
|isIncludePm|boolean|false|none|是否包含物业费|是否包含物业费|
|pmUnitPrice|number|false|none|月物业费单价|月物业费单价|
|pmMonthlyPrice|number|false|none|月物业费总价|月物业费总价|
|totalPrice|number|false|none|合同总价|合同总价|
|totalBottomPrice|number|false|none|合同底价|合同底价|
|bizTypeId|string|false|none|业态id|业态id|
|bizTypeName|string|false|none|业态|业态|
|lesseeBrand|string|false|none|承租方品牌|承租方品牌|
|businessCategory|string|false|none|经营品类|经营品类|
|openDate|string(date-time)|false|none|开业日期|开业日期|
|fireRiskCategory|integer(int32)|false|none|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|
|sprinklerSystem|integer(int32)|false|none|喷淋系统,0-安装,1-未安装|喷淋系统,0-安装,1-未安装|
|factoryEngaged|string|false|none|厂房从事|厂房从事|
|deliverDate|string(date-time)|false|none|交接日期|交接日期|
|parkingSpaceType|integer(int32)|false|none|车位类型,0-人防,1-非人防|车位类型,0-人防,1-非人防|
|hasParkingFee|boolean|false|none|是否包含车位管理费|是否包含车位管理费|
|parkingFeeAmount|number|false|none|车位管理费金额|车位管理费金额|
|venueDeliveryDate|string(date-time)|false|none|场地交付日期|场地交付日期|
|venueLocation|string|false|none|租赁场地位置|租赁场地位置|
|dailyActivityStartTime|string(date-time)|false|none|每日活动开始时间|每日活动开始时间|
|dailyActivityEndTime|string(date-time)|false|none|每日活动结束时间|每日活动结束时间|
|venuePurpose|string|false|none|场地用途|场地用途|
|otherInfo|string|false|none|补充条款|补充条款|
|contractAttachments|string|false|none|合同附件|合同附件|
|signAttachments|string|false|none|签署附件|签署附件[{"fileName": "", "fileUrl":"", "isConfirm":1or0}]|
|attachmentsPlan|string|false|none|附件-平面图|附件-平面图|
|isUploadSignature|boolean|false|none|是否上传签署文件 0-否,1-是|是否上传签署文件 0-否,1-是|
|changeType|string|false|none|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|
|processId|string|false|none|流程实例t_oa_process->id|流程实例t_oa_process->id|
|changeDate|string(date-time)|false|none|变更日期|变更日期|
|changeAttachments|string|false|none|变更附件json数组|变更附件json数组|
|changeExplanation|string|false|none|变更说明|变更说明|
|isSignatureConfirm|boolean|false|none|是否确认签署 0-否,1-是|是否确认签署 0-否,1-是|
|isPaperConfirm|boolean|false|none|是否确认纸质文件 0-否,1-是|是否确认纸质文件 0-否,1-是|
|isFinish|boolean|false|none|合同是否完全结束 0-否,1-是|合同是否完全结束 0-否,1-是|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建日期|创建日期|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新日期|更新日期|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|roomName|string|false|none|租赁资源|租赁资源|
|terminateDate|string(date-time)|false|none|退租日期|退租日期|
|terminateId|string|false|none|退租id|退租id|
|customer|[ContractCustomerVo](#schemacontractcustomervo)|false|none||合同客户信息|
|bookings|[[ContractBookingVo](#schemacontractbookingvo)]|false|none|合同定单列表|合同定单列表|
|fees|[[ContractFeeVo](#schemacontractfeevo)]|false|none|合同费用列表|合同费用列表|
|costs|[[ContractCostVo](#schemacontractcostvo)]|false|none|合同应收列表|合同应收列表|
|rooms|[[ContractRoomVo](#schemacontractroomvo)]|false|none|合同房源列表|合同房源列表|

<h2 id="tocS_ContractQueryDTO">ContractQueryDTO</h2>

<a id="schemacontractquerydto"></a>
<a id="schema_ContractQueryDTO"></a>
<a id="tocScontractquerydto"></a>
<a id="tocscontractquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "changeDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "otherInfo": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "projectId": "string",
  "roomName": "string",
  "customerName": "string",
  "operateTypes": [
    0
  ],
  "statuses": [
    0
  ],
  "statusTwos": [
    0
  ],
  "signDateStart": "string",
  "signDateEnd": "string",
  "contractPurposes": [
    0
  ],
  "signWays": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|contractNo|string|false|none|合同号|合同号|
|unionId|string|false|none|统一id|统一id|
|version|string|false|none|版本号v00x|版本号v00x|
|isCurrent|boolean|false|none|是否当前生效版本 0-否,1-是|是否当前生效版本 0-否,1-是|
|isLatest|boolean|false|none|是否最新版本 0-否,1-是|是否最新版本 0-否,1-是|
|status|integer(int32)|false|none|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|
|statusTwo|integer(int32)|false|none|二级状态|二级状态|
|approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|operateType|integer(int32)|false|none|最新操作类型,0-新签,1-变更条款,2-退租|最新操作类型,0-新签,1-变更条款,2-退租|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|ourSigningParty|string|false|none|我方签约主体信息|我方签约主体信息|
|signWay|integer(int32)|false|none|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|
|signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|originId|string|false|none|源合同ID|源合同ID|
|landUsage|string|false|none|用地性质|用地性质|
|signerId|string|false|none|合同签约人id|合同签约人id|
|signerName|string|false|none|签约人姓名|签约人姓名|
|ownerId|string|false|none|责任人id|责任人id|
|ownerName|string|false|none|责任人姓名|责任人姓名|
|contractMode|integer(int32)|false|none|合同模式,0-标准合同,1-非标合同|合同模式,0-标准合同,1-非标合同|
|paperContractNo|string|false|none|纸质合同号|纸质合同号|
|signDate|string(date-time)|false|none|签订日期|签订日期|
|handoverDate|string(date-time)|false|none|交房日期|交房日期|
|contractPurpose|integer(int32)|false|none|合同用途,字典:|合同用途,字典:|
|dealChannel|integer(int32)|false|none|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|
|assistantId|string|false|none|协助人id|协助人id|
|assistantName|string|false|none|协助人姓名|协助人姓名|
|rentYear|integer(int32)|false|none|租赁期限-年|租赁期限-年|
|rentMonth|integer(int32)|false|none|租赁期限-月|租赁期限-月|
|rentDay|integer(int32)|false|none|租赁期限-日|租赁期限-日|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|effectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|invalidDate|string(date-time)|false|none|实际失效日期|实际失效日期|
|changeDate|string(date-time)|false|none|变更日期|变更日期|
|bookingRelType|integer(int32)|false|none|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|
|bondReceivableDate|string|false|none|保证金应收日期(天数/具体日期)|保证金应收日期(天数/具体日期)|
|bondReceivableType|integer(int32)|false|none|保证金应收日期类型,0-合同签订后, 1-指定日期|保证金应收日期类型,0-合同签订后, 1-指定日期|
|bondPriceType|integer(int32)|false|none|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|
|bondPrice|number|false|none|保证金金额|保证金金额|
|chargeWay|integer(int32)|false|none|收费方式,0-固定租金,1-递增租金,2-营收抽成|收费方式,0-固定租金,1-递增租金,2-营收抽成|
|rentReceivableDate|string|false|none|租金应收日期(天数/具体日期)|租金应收日期(天数/具体日期)|
|rentReceivableType|integer(int32)|false|none|租金应收日期类型,1-租期开始前,2-租期开始后|租金应收日期类型,1-租期开始前,2-租期开始后|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|rentPayPeriod|integer(int32)|false|none|租金支付周期|租金支付周期|
|increaseGap|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|increaseRate|number|false|none|递增率|递增率|
|increaseRule|string|false|none|租金递增规则(非标)|租金递增规则(非标)|
|estimateRevenue|number|false|none|预估营收额|预估营收额|
|revenueType|integer(int32)|false|none|抽点类型: 1-按月营业额, 2-按年营业额|抽点类型: 1-按月营业额, 2-按年营业额|
|isIncludePm|boolean|false|none|是否包含物业费|是否包含物业费|
|pmUnitPrice|number|false|none|月物业费单价|月物业费单价|
|pmMonthlyPrice|number|false|none|月物业费总价|月物业费总价|
|totalPrice|number|false|none|合同总价|合同总价|
|totalBottomPrice|number|false|none|合同底价|合同底价|
|bizTypeId|string|false|none|业态id|业态id|
|bizTypeName|string|false|none|业态|业态|
|lesseeBrand|string|false|none|承租方品牌|承租方品牌|
|businessCategory|string|false|none|经营品类|经营品类|
|openDate|string(date-time)|false|none|开业日期|开业日期|
|fireRiskCategory|integer(int32)|false|none|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|
|sprinklerSystem|integer(int32)|false|none|喷淋系统,0-安装,1-未安装|喷淋系统,0-安装,1-未安装|
|factoryEngaged|string|false|none|厂房从事|厂房从事|
|deliverDate|string(date-time)|false|none|交接日期|交接日期|
|parkingSpaceType|integer(int32)|false|none|车位类型,0-人防,1-非人防|车位类型,0-人防,1-非人防|
|hasParkingFee|boolean|false|none|是否包含车位管理费|是否包含车位管理费|
|parkingFeeAmount|number|false|none|车位管理费金额|车位管理费金额|
|otherInfo|string|false|none|补充条款|补充条款|
|attachmentsPlan|string|false|none|附件-平面图|附件-平面图|
|isUploadSignature|boolean|false|none|是否上传签署文件 0-否,1-是|是否上传签署文件 0-否,1-是|
|isSignatureConfirm|boolean|false|none|是否确认签署 0-否,1-是|是否确认签署 0-否,1-是|
|isPaperConfirm|boolean|false|none|是否确认纸质文件 0-否,1-是|是否确认纸质文件 0-否,1-是|
|isFinish|boolean|false|none|合同是否完全结束 0-否,1-是|合同是否完全结束 0-否,1-是|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|projectId|string|false|none|项目id|项目id|
|roomName|string|false|none|租赁资源|租赁资源|
|customerName|string|false|none|承租方名称|承租方名称|
|operateTypes|[integer]|false|none|最新操作类型（多选）|最新操作类型（多选）|
|» 最新操作类型（多选）|integer(int32)|false|none|最新操作类型（多选）|最新操作类型（多选）|
|statuses|[integer]|false|none|合同状态（多选）|合同状态（多选）|
|» 合同状态（多选）|integer(int32)|false|none|合同状态（多选）|合同状态（多选）|
|statusTwos|[integer]|false|none|合同状态二级（多选）|合同状态二级（多选）|
|» 合同状态二级（多选）|integer(int32)|false|none|合同状态二级（多选）|合同状态二级（多选）|
|signDateStart|string|false|none|签约日期开始|签约日期开始|
|signDateEnd|string|false|none|签约日期结束|签约日期结束|
|contractPurposes|[integer]|false|none|合同用途（二级下拉，多选）|合同用途（二级下拉，多选）|
|» 合同用途（二级下拉，多选）|integer(int32)|false|none|合同用途（二级下拉，多选）|合同用途（二级下拉，多选）|
|signWays|[integer]|false|none|签约方式（多选）|签约方式（多选）：0-电子合同、1-纸质合同（甲方电子章）、2-纸质合同（双方实体章）|
|» 签约方式（多选）|integer(int32)|false|none|签约方式（多选）|签约方式（多选）：0-电子合同、1-纸质合同（甲方电子章）、2-纸质合同（双方实体章）|

<h2 id="tocS_ContractSummaryVo">ContractSummaryVo</h2>

<a id="schemacontractsummaryvo"></a>
<a id="schema_ContractSummaryVo"></a>
<a id="tocScontractsummaryvo"></a>
<a id="tocscontractsummaryvo"></a>

```json
{
  "contractPeriodNotStarted": 0,
  "bondNotFullyReceived": 0,
  "periodNotStartedAndBondNotReceived": 0,
  "normalTermination": 0,
  "earlyTermination": 0,
  "roomChange": 0,
  "renewal": 0,
  "normalTerminationPendingCheckout": 0,
  "earlyTerminationPendingCheckout": 0,
  "pendingTermination": 0,
  "invalid": 0,
  "invalidRenewal": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|contractPeriodNotStarted|integer(int32)|false|none|合同期未开始数量|合同期未开始数量|
|bondNotFullyReceived|integer(int32)|false|none|保证金未收齐数量|保证金未收齐数量|
|periodNotStartedAndBondNotReceived|integer(int32)|false|none|合同期未开始&保证金未收齐数量|合同期未开始&保证金未收齐数量|
|normalTermination|integer(int32)|false|none|正常退租数量|正常退租数量|
|earlyTermination|integer(int32)|false|none|提前退租数量|提前退租数量|
|roomChange|integer(int32)|false|none|换房数量|换房数量|
|renewal|integer(int32)|false|none|续签数量|续签数量|
|normalTerminationPendingCheckout|integer(int32)|false|none|正常退租待出场结算数量|正常退租待出场结算数量|
|earlyTerminationPendingCheckout|integer(int32)|false|none|提前退租待出场结算数量|提前退租待出场结算数量|
|pendingTermination|integer(int32)|false|none|待退租数量|待退租数量|
|invalid|integer(int32)|false|none|作废数量|作废数量|
|invalidRenewal|integer(int32)|false|none|作废重签数量|作废重签数量|

<h2 id="tocS_ContractCostPlanVo">ContractCostPlanVo</h2>

<a id="schemacontractcostplanvo"></a>
<a id="schema_ContractCostPlanVo"></a>
<a id="tocScontractcostplanvo"></a>
<a id="tocscontractcostplanvo"></a>

```json
{
  "costs": [
    {
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "bookings": [
    {
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

合同应收计划视图对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|costs|[[ContractCostVo](#schemacontractcostvo)]|false|none||应收计划列表|
|bookings|[[ContractBookingVo](#schemacontractbookingvo)]|false|none||定单列表|

<h2 id="tocS_ContractPermissionVo">ContractPermissionVo</h2>

<a id="schemacontractpermissionvo"></a>
<a id="schema_ContractPermissionVo"></a>
<a id="tocScontractpermissionvo"></a>
<a id="tocscontractpermissionvo"></a>

```json
{
  "canSelectNonStandard": true,
  "notRestrictedBySignTime": true,
  "solidificationDays": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|canSelectNonStandard|boolean|false|none|是否可选非标合同|是否可选非标合同|
|notRestrictedBySignTime|boolean|false|none|是否不受合同签订时间固化限制|是否不受合同签订时间固化限制|
|solidificationDays|integer(int32)|false|none|固化天数|固化天数|

<h2 id="tocS_CostFlowRelVo">CostFlowRelVo</h2>

<a id="schemacostflowrelvo"></a>
<a id="schema_CostFlowRelVo"></a>
<a id="tocScostflowrelvo"></a>
<a id="tocscostflowrelvo"></a>

```json
{
  "id": "string",
  "costId": "string",
  "refundId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "confirmStatus": 0,
  "confirmTime": "2019-08-24T14:15:22Z",
  "confirmUserId": "string",
  "confirmUserName": "string",
  "payAmount": 0,
  "pendingAmount": 0,
  "acctAmount": 0,
  "remark": "string",
  "createByName": "string",
  "createTime": "string",
  "updateByName": "string",
  "isDel": true,
  "projectId": "string",
  "projectName": "string",
  "entryTime": "2019-08-24T14:15:22Z",
  "payType": 0,
  "payMethod": "string",
  "orderNo": "string",
  "usedAmount": 0,
  "payerName": "string",
  "target": "string",
  "merchant": "string",
  "cumulativeAcctAmount": 0
}

```

账单流水关系列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|costId|string|false|none|账单id|账单id|
|refundId|string|false|none|退款单id|退款单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|账单类型：1-收款 2-转入 3-转出 4-退款|账单类型：1-收款 2-转入 3-转出 4-退款|
|confirmStatus|integer(int32)|false|none|确认状态: 0-未确认、1-自动确认、2-手动确认|确认状态: 0-未确认、1-自动确认、2-手动确认|
|confirmTime|string(date-time)|false|none|确认时间|确认时间|
|confirmUserId|string|false|none|确认人id|确认人id|
|confirmUserName|string|false|none|确认人姓名|确认人姓名|
|payAmount|number|false|none|支付金额|支付金额|
|pendingAmount|number|false|none|账单待收金额|账单待收金额|
|acctAmount|number|false|none|本次记账金额|本次记账金额|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|entryTime|string(date-time)|false|none|入账时间|入账时间|
|payType|integer(int32)|false|none|支付类型|支付类型|
|payMethod|string|false|none|支付方式|支付方式|
|orderNo|string|false|none|订单号|订单号|
|usedAmount|number|false|none|已使用金额|已使用金额|
|payerName|string|false|none|支付人姓名|支付人姓名|
|target|string|false|none|支付对象|支付对象|
|merchant|string|false|none|收款商户|收款商户|
|cumulativeAcctAmount|number|false|none|累计记账金额|累计记账金额|

<h2 id="tocS_ContractSupplementVo">ContractSupplementVo</h2>

<a id="schemacontractsupplementvo"></a>
<a id="schema_ContractSupplementVo"></a>
<a id="tocScontractsupplementvo"></a>
<a id="tocscontractsupplementvo"></a>

```json
{
  "contractId": "string",
  "supplementType": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "approveStatus": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|contractId|string|false|none|合同id|合同id|
|supplementType|string|false|none|补充协议类型|补充协议类型|
|signDate|string(date-time)|false|none|签订日期|签订日期|
|effectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|status|integer(int32)|false|none|一级状态|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|
|approveStatus|integer(int32)|false|none|审核状态|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|

<h2 id="tocS_ContractModifyLogVo">ContractModifyLogVo</h2>

<a id="schemacontractmodifylogvo"></a>
<a id="schema_ContractModifyLogVo"></a>
<a id="tocScontractmodifylogvo"></a>
<a id="tocscontractmodifylogvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "unionId": "string",
  "modifyType": 0,
  "oldValue": "string",
  "newValue": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|unionId|string|false|none|合同统一id|合同统一id|
|modifyType|integer(int32)|false|none|修改类型:0-付款账号、1-联系人、2-承租人手机号、3-签约方式|修改类型:0-付款账号、1-联系人、2-承租人手机号、3-签约方式|
|oldValue|string|false|none|旧值|旧值|
|newValue|string|false|none|新值|新值|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

