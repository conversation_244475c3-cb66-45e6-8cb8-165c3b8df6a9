---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/财务流水

<a id="opIdsaveRecord_1"></a>

## POST 保存记账

POST /financialFlow/saveRecord

保存记账

> Body 请求参数

```json
{
  "flowId": "string",
  "flowRelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "pendingAmount": 0,
      "acctAmount": 0,
      "remark": "string",
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialFlowSaveRecordDTO](#schemafinancialflowsaverecorddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdrefund"></a>

## POST 发起退款接口

POST /financialFlow/refund

发起退款接口

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "refundNo": "string",
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "recordAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "refundStatus": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "recordStatus": 0,
  "confirmStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "isDel": true,
  "isSubmit": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialRefundAddDTO](#schemafinancialrefundadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdrecordList"></a>

## POST 查询流水信息列表

POST /financialFlow/recordList

定单管理/退款管理/账单管理点击记账查询按规则自动匹配的流水列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "bookingId": "string",
  "refundId": "string",
  "costId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialFlowRecordQueryDTO](#schemafinancialflowrecordquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdmarkOtherIncome"></a>

## POST 标记为其他收入

POST /financialFlow/markOtherIncome

标记为其他收入

> Body 请求参数

```json
{
  "flowId": "string",
  "otherIncomeDesc": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialFlowMarkOtherIncomeDTO](#schemafinancialflowmarkotherincomedto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_3"></a>

## POST 查询财务流水列表

POST /financialFlow/list

查询财务流水列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "orderNo": "string",
  "payDirection": 0,
  "payTypes": [
    0
  ],
  "payMethods": [
    0
  ],
  "targetTypes": [
    0
  ],
  "payType": 0,
  "payMethod": 0,
  "targetType": 0,
  "target": "string",
  "entryTimeStart": "string",
  "entryTimeEnd": "string",
  "entryTime": "2019-08-24T14:15:22Z",
  "status": 0,
  "amount": 0,
  "usedAmount": 0,
  "payerName": "string",
  "payerPhone": "string",
  "payerAccount": "string",
  "payRemark": "string",
  "merchants": [
    "string"
  ],
  "merchant": "string",
  "payChannel": "string",
  "sourceNo": "string",
  "isOtherIncome": true,
  "otherIncomeDesc": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "searchType": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialFlowQueryDTO](#schemafinancialflowquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdexport_7"></a>

## POST 导出财务流水列表

POST /financialFlow/export

导出财务流水列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|orderNo|query|string| 否 | 订单号|订单号|
|payDirection|query|integer(int32)| 否 | 支付方向: 0-收入, 1-支出|支付方向: 0-收入, 1-支出|
|payTypes|query|array[integer]| 否 | 支付类型（线上/线下多选）|支付类型（线上/线下多选）|
|payMethods|query|array[integer]| 否 | 支付方式（取一房一码里的paytype多选）|支付方式（取一房一码里的paytype多选）|
|targetTypes|query|array[integer]| 否 | 支付对象类型（多选）|支付对象类型（多选）|
|payType|query|integer(int32)| 否 | 支付类型|支付类型|
|payMethod|query|integer(int32)| 否 | 支付方式|支付方式|
|targetType|query|integer(int32)| 否 | 支付对象类型|支付对象类型|
|target|query|string| 否 | 支付对象|支付对象|
|entryTimeStart|query|string| 否 | 入账时间开始|入账时间开始|
|entryTimeEnd|query|string| 否 | 入账时间结束|入账时间结束|
|entryTime|query|string(date-time)| 否 | 入账时间|入账时间|
|status|query|integer(int32)| 否 | 状态: 0-未记账, 1-部分记账, 2-已记账|状态: 0-未记账, 1-部分记账, 2-已记账|
|amount|query|number| 否 | 支付金额|支付金额|
|usedAmount|query|number| 否 | 已使用金额|已使用金额|
|payerName|query|string| 否 | 支付人姓名|支付人姓名|
|payerPhone|query|string| 否 | 支付人手机号|支付人手机号|
|payerAccount|query|string| 否 | 支付人账号|支付人账号|
|payRemark|query|string| 否 | 支付备注|支付备注|
|merchants|query|array[string]| 否 | 收款商户号（多选下拉选择）|收款商户号（多选下拉选择）|
|merchant|query|string| 否 | 收款商户|收款商户|
|payChannel|query|string| 否 | 收款渠道|收款渠道|
|sourceNo|query|string| 否 | 退款流水的原单号|退款流水的原单号|
|isOtherIncome|query|boolean| 否 | 是否是其他收入: 0-否,1-是|是否是其他收入: 0-否,1-是|
|otherIncomeDesc|query|string| 否 | 其他收入说明|其他收入说明|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|searchType|query|integer(int32)| 否 | 1-未明流水 2-其他收入|1-未明流水 2-其他收入|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdcancelOtherIncome"></a>

## POST 取消其他收入

POST /financialFlow/cancelOtherIncome

取消其他收入

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|流水ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdusage"></a>

## GET 查看流水使用记录

GET /financialFlow/usage/{flowId}

查看流水使用记录

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|flowId|path|string| 是 ||流水id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetBillByContractId"></a>

## GET 根据合同查询未收齐账单

GET /financialFlow/getBillByContractId

根据合同查询未收齐账单接口(记账页面使用)

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|contractId|query|string| 是 ||合同id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddetail_2"></a>

## GET 获取流水详情

GET /financialFlow/detail/{flowId}

获取流水详情

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|flowId|path|string| 是 ||流水id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_CostFlowRelAddDTO">CostFlowRelAddDTO</h2>

<a id="schemacostflowreladddto"></a>
<a id="schema_CostFlowRelAddDTO"></a>
<a id="tocScostflowreladddto"></a>
<a id="tocscostflowreladddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "costId": "string",
  "refundId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "confirmStatus": 0,
  "confirmTime": "2019-08-24T14:15:22Z",
  "confirmUserId": "string",
  "confirmUserName": "string",
  "payAmount": 0,
  "pendingAmount": 0,
  "acctAmount": 0,
  "remark": "string",
  "isDel": true
}

```

记账记录列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|costId|string|false|none|账单id|账单id|
|refundId|string|false|none|退款单id|退款单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|账单类型：1-收款 2-转入 3-转出 4-退款|账单类型：1-收款 2-转入 3-转出 4-退款|
|confirmStatus|integer(int32)|false|none|确认状态: 0-未确认、1-自动确认、2-手动确认|确认状态: 0-未确认、1-自动确认、2-手动确认|
|confirmTime|string(date-time)|false|none|确认时间|确认时间|
|confirmUserId|string|false|none|确认人id|确认人id|
|confirmUserName|string|false|none|确认人姓名|确认人姓名|
|payAmount|number|false|none|支付金额|支付金额|
|pendingAmount|number|false|none|账单待收金额|账单待收金额|
|acctAmount|number|false|none|本次记账金额|本次记账金额|
|remark|string|false|none|备注|备注|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_FinancialRefundAddDTO">FinancialRefundAddDTO</h2>

<a id="schemafinancialrefundadddto"></a>
<a id="schema_FinancialRefundAddDTO"></a>
<a id="tocSfinancialrefundadddto"></a>
<a id="tocsfinancialrefundadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "refundNo": "string",
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "recordAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "refundStatus": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "recordStatus": 0,
  "confirmStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "isDel": true,
  "isSubmit": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|refundType|integer(int32)|false|none|退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|refundNo|string|false|none|退款单号|退款单号|
|bizId|string|false|none|业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|string|false|none|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|string(date-time)|false|none|退款申请时间|退款申请时间|
|refundAmount|number|false|none|退款金额|退款金额|
|recordAmount|number|false|none|已记账金额|已记账金额|
|feeType|string|false|none|退款费用类型|退款费用类型|
|refundWay|integer(int32)|false|none|退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|string|false|none|收款方姓名|收款方姓名|
|receiverBank|string|false|none|收款方开户行|收款方开户行|
|receiverAccount|string|false|none|收款方银行账号|收款方银行账号|
|refundRemark|string|false|none|退款申请说明|退款申请说明|
|refundTime|string(date-time)|false|none|退款时间|退款时间|
|refundStatus|integer(int32)|false|none|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|
|approveStatus|integer(int32)|false|none|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|approveTime|string(date-time)|false|none|审批通过时间|审批通过时间|
|recordStatus|integer(int32)|false|none|记账状态: 0-未记账,1-已记账|记账状态: 0-未记账,1-已记账|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|roomYardStatus|integer(int32)|false|none|一房一码退款状态|一房一码退款状态|
|attachments|string|false|none|附件|附件|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|string|false|none|作废原因|作废原因|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|isSubmit|boolean|false|none|0-暂存,1-提交|0-暂存,1-提交|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_FinancialFlowQueryDTO">FinancialFlowQueryDTO</h2>

<a id="schemafinancialflowquerydto"></a>
<a id="schema_FinancialFlowQueryDTO"></a>
<a id="tocSfinancialflowquerydto"></a>
<a id="tocsfinancialflowquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "orderNo": "string",
  "payDirection": 0,
  "payTypes": [
    0
  ],
  "payMethods": [
    0
  ],
  "targetTypes": [
    0
  ],
  "payType": 0,
  "payMethod": 0,
  "targetType": 0,
  "target": "string",
  "entryTimeStart": "string",
  "entryTimeEnd": "string",
  "entryTime": "2019-08-24T14:15:22Z",
  "status": 0,
  "amount": 0,
  "usedAmount": 0,
  "payerName": "string",
  "payerPhone": "string",
  "payerAccount": "string",
  "payRemark": "string",
  "merchants": [
    "string"
  ],
  "merchant": "string",
  "payChannel": "string",
  "sourceNo": "string",
  "isOtherIncome": true,
  "otherIncomeDesc": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "searchType": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|orderNo|string|false|none|订单号|订单号|
|payDirection|integer(int32)|false|none|支付方向: 0-收入, 1-支出|支付方向: 0-收入, 1-支出|
|payTypes|[integer]|false|none|支付类型（线上/线下多选）|支付类型（线上/线下多选）|
|» 支付类型（线上/线下多选）|integer(int32)|false|none|支付类型（线上/线下多选）|支付类型（线上/线下多选）|
|payMethods|[integer]|false|none|支付方式（取一房一码里的paytype多选）|支付方式（取一房一码里的paytype多选）|
|» 支付方式（取一房一码里的paytype多选）|integer(int32)|false|none|支付方式（取一房一码里的paytype多选）|支付方式（取一房一码里的paytype多选）|
|targetTypes|[integer]|false|none|支付对象类型（多选）|支付对象类型（多选）|
|» 支付对象类型（多选）|integer(int32)|false|none|支付对象类型（多选）|支付对象类型（多选）|
|payType|integer(int32)|false|none|支付类型|支付类型|
|payMethod|integer(int32)|false|none|支付方式|支付方式|
|targetType|integer(int32)|false|none|支付对象类型|支付对象类型|
|target|string|false|none|支付对象|支付对象|
|entryTimeStart|string|false|none|入账时间开始|入账时间开始|
|entryTimeEnd|string|false|none|入账时间结束|入账时间结束|
|entryTime|string(date-time)|false|none|入账时间|入账时间|
|status|integer(int32)|false|none|状态: 0-未记账, 1-部分记账, 2-已记账|状态: 0-未记账, 1-部分记账, 2-已记账|
|amount|number|false|none|支付金额|支付金额|
|usedAmount|number|false|none|已使用金额|已使用金额|
|payerName|string|false|none|支付人姓名|支付人姓名|
|payerPhone|string|false|none|支付人手机号|支付人手机号|
|payerAccount|string|false|none|支付人账号|支付人账号|
|payRemark|string|false|none|支付备注|支付备注|
|merchants|[string]|false|none|收款商户号（多选下拉选择）|收款商户号（多选下拉选择）|
|» 收款商户号（多选下拉选择）|string|false|none|收款商户号（多选下拉选择）|收款商户号（多选下拉选择）|
|merchant|string|false|none|收款商户|收款商户|
|payChannel|string|false|none|收款渠道|收款渠道|
|sourceNo|string|false|none|退款流水的原单号|退款流水的原单号|
|isOtherIncome|boolean|false|none|是否是其他收入: 0-否,1-是|是否是其他收入: 0-否,1-是|
|otherIncomeDesc|string|false|none|其他收入说明|其他收入说明|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|searchType|integer(int32)|false|none|1-未明流水 2-其他收入|1-未明流水 2-其他收入|

<h2 id="tocS_FinancialFlowSaveRecordDTO">FinancialFlowSaveRecordDTO</h2>

<a id="schemafinancialflowsaverecorddto"></a>
<a id="schema_FinancialFlowSaveRecordDTO"></a>
<a id="tocSfinancialflowsaverecorddto"></a>
<a id="tocsfinancialflowsaverecorddto"></a>

```json
{
  "flowId": "string",
  "flowRelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "pendingAmount": 0,
      "acctAmount": 0,
      "remark": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|flowId|string|true|none|流水id|流水id|
|flowRelList|[[CostFlowRelAddDTO](#schemacostflowreladddto)]|true|none|记账记录列表|记账记录列表|

<h2 id="tocS_FinancialFlowRecordQueryDTO">FinancialFlowRecordQueryDTO</h2>

<a id="schemafinancialflowrecordquerydto"></a>
<a id="schema_FinancialFlowRecordQueryDTO"></a>
<a id="tocSfinancialflowrecordquerydto"></a>
<a id="tocsfinancialflowrecordquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "bookingId": "string",
  "refundId": "string",
  "costId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|bookingId|string|false|none|定单id|定单id|
|refundId|string|false|none|退款id|退款id|
|costId|string|false|none|账单id|账单id|

<h2 id="tocS_FinancialFlowMarkOtherIncomeDTO">FinancialFlowMarkOtherIncomeDTO</h2>

<a id="schemafinancialflowmarkotherincomedto"></a>
<a id="schema_FinancialFlowMarkOtherIncomeDTO"></a>
<a id="tocSfinancialflowmarkotherincomedto"></a>
<a id="tocsfinancialflowmarkotherincomedto"></a>

```json
{
  "flowId": "string",
  "otherIncomeDesc": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|flowId|string|true|none|流水id|流水id|
|otherIncomeDesc|string|true|none|其他收入说明|其他收入说明|

