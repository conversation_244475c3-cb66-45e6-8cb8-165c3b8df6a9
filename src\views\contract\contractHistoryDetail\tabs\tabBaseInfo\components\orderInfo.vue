<template>
    <section>
        <sectionTitle title="定单信息" />
        <div class="content">
            <div class="form-item">
                <label class="form-label">关联定单类型</label>
                <div class="detail-text">
                    {{ contractData.bookingRelType === 0 ? '房间有效定单' : 
                       contractData.bookingRelType === 1 ? '不关联定单' : 
                       contractData.bookingRelType === 2 ? '其他定单' : '--' }}
                </div>
            </div>

            <!-- 定单列表 -->
            <div v-if="contractData.bookingRelType !== 1">
                <a-table :data="contractData.bookings" :bordered="{ cell: true }" :pagination="false">
                    <template #columns>
                        <a-table-column title="预定房间" data-index="bookedRoom" align="center" />
                        <a-table-column title="预订人姓名" data-index="bookerName" align="center" />
                        <a-table-column title="定单已收金额" align="right">
                            <template #cell="{ record }">
                                {{ formatAmount(record.bookingReceivedAmount) }} 元
                            </template>
                        </a-table-column>
                        <a-table-column title="定单收款日期" data-index="bookingPaymentDate" align="center" />
                    </template>
                </a-table>
            </div>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import { ContractAddDTO } from '@/api/contract';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.form-item {
    margin-bottom: 16px;
    
    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>