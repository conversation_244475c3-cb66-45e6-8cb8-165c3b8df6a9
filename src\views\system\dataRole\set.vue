<template>
    <div class="data-role-set-container">
        <div class="data-role-set-content">
            <!-- 数据角色权限设置 -->
            <div class="common-header">
                <span class="common-header-tag"></span>
                <span class="common-header-title">业务数据权限</span>
            </div>
            <div class="common-flex data-role-setting-item-group">
                <div class="common-flex data-role-setting-item">
                    <div class="data-role-setting-item-title"><span class="common-required">*</span> 房源权限
                    </div>
                    <a-checkbox-group v-model="roomPermissions">
                        <template v-for="item in houseOptions" :key="item.value">
                            <a-checkbox :value="item.value">{{ item.label }}</a-checkbox>
                        </template>
                    </a-checkbox-group>
                </div>
                <div class="common-flex data-role-setting-item">
                    <div class="data-role-setting-item-title"><span class="common-required">*</span> 合同权限
                    </div>
                    <a-radio-group v-model="contractPermission">
                        <template v-for="item in contractOptions" :key="item.value">
                            <a-radio :value="item.value">{{ item.label }}</a-radio>
                        </template>
                    </a-radio-group>
                </div>
            </div>
            <a-divider style="margin-top: 0" />
            <div class="common-header">
                <span class="common-header-tag"></span>
                <span class="common-header-title">项目数据权限</span>
            </div>
            <div class="data-role-setting-item-group">
                <a-tree ref="treeRef" :checkable="true" v-model:selected-keys="selectedKeys" v-model:checked-keys="checkedKeys"
                    default-expand-all auto-expand-parent show-line default-expand-selected default-expand-checked
                    :data="treeData"
                    :field-names="{ title: 'name', key: 'id' }"/>
            </div>
        </div>
        <div class="common-flex data-role-set-footer">
            <a-button type="primary" @click="handleSaveUpdate">保存更新</a-button>
        </div>
    </div>
</template>
<script setup lang="ts">
import { computed, ref, reactive, watch, nextTick, defineProps } from 'vue'

import { getRoleList, getRoleDetail, setRoleOrgBind, getRoleOrgList } from '@/api/role'
import { getOrgList } from '@/api/org'
import { useI18n } from 'vue-i18n';
import useLoading from '@/hooks/loading';
import { queryPolicyList, PolicyRecord, PolicyParams } from '@/api/list';
import { Pagination } from '@/types/global';
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
import cloneDeep from 'lodash/cloneDeep';
import Sortable from 'sortablejs';
import { getOrgTree } from '@/api/org';
import { Message } from '@arco-design/web-vue';
const props = defineProps({
    roleId: {
        type: String,
        required: true,
    },
})

type SizeProps = 'mini' | 'small' | 'medium' | 'large';
type Column = TableColumnData & { checked?: true };
const generateFormModel = () => {
    return {
        number: '',
        name: '',
        contentType: '',
        filterType: '',
        createdTime: [],
        status: '',
    };
};
const { loading, setLoading } = useLoading(true);
const { t } = useI18n();
const renderData = ref<PolicyRecord[]>([]);
const formModel = ref(generateFormModel());
const cloneColumns = ref<Column[]>([]);
const showColumns = ref<Column[]>([]);

const size = ref<SizeProps>('medium');

const basePagination: Pagination = {
    current: 1,
    pageSize: 20,
    showPageSize: true,
};

const allCheckedKeys = ['0-0', '0-0-1', '0-0-2', '0-0-2-1', '0-1', '0-1-1', '0-1-2'];
const allExpandedKeys = ['0-0', '0-1', '0-0-2'];
const selectedKeys = ref([]);
const checkedKeys = ref([]);
const expandedKeys = ref([]);
// 万洋集回
// 口华东区域公司
// 直营片区
// • 团浙南片区
// 囚 浙江（平阳）万洋众创城
// 口 盛诺万洋众创城
// 口 浙东片区
// 口江沪片区
// 口金华片区
// 口华南区域公司口 粤东片区
const treeData = ref([])
//roomPermissions title: 房源权限,all,business,zhongchuang
// contractPermission*	 title: 合同权限, all,personal
const roomPermissions = ref([])
const contractPermission = ref('')

const houseOptions = [
    { label: '商服', value: 'business' },
    { label: '众创城', value: 'zhongchuang' },
    { label: '全部', value: 'all' },
]
const contractOptions = [
    { label: '个人', value: 'personal' },
    { label: '全部', value: 'all' }
]

const handleSaveUpdate = async () => {
    console.log(roomPermissions.value, contractPermission.value)
    const { data } = await setRoleOrgBind({
        roleId: props.roleId,
        orgIdList: checkedKeys.value,
        roomPermissions: roomPermissions.value,
        contractPermission: contractPermission.value
    })
    // console.log(data)
    fetchData()
    Message.success('保存成功')
}

//获取组织树
const getOrgTreeMethod = async () => {
    const { data } = await getOrgTree()
    // console.log(data)
    treeData.value = data
}
const treeRef = ref()
const getRoleOrgListMethod = async () => {
    const { data } = await getRoleOrgList({ roleId: props.roleId })
    console.log(data)
    selectedKeys.value = data
    checkedKeys.value = data
    nextTick(() => {
        treeRef.value.expandAll(true)
    })
}
// 获取角色详情
const getRoleDetailMethod = async () => {
    const { data } = await getRoleDetail({ roleId: props.roleId })
    // console.log(data)
    if (data) {
        if (data.roomPermissions) { 
            roomPermissions.value = data.roomPermissions.split(',') || []
        } else {
            roomPermissions.value = []
        }
        if (data.contractPermissions) {
            contractPermission.value = data.contractPermissions || ''
        } else {
            contractPermission.value = ''
        }
    }
}
const fetchData = async () => {
    setLoading(true);
    try {
        getOrgTreeMethod()
        getRoleDetailMethod()
        getRoleOrgListMethod()
        // console.log(data)
        // renderData.value = data
        // pagination.current = params.current;
        // pagination.total = data
    } catch (err) {
        // you can report use errorHandler or other
    } finally {
        setLoading(false)
    }
}




defineExpose({
    fetchData
})
</script>

<script lang="ts">
export default { name: 'dataRoleSetComponent' }
</script>

<style scoped lang="less">
.data-role-set-container {
    width: 100%;
    height: calc(100vh - 180px);
    
    // height: 100%;

    .data-role-set-content {
        height: calc(100% - 50px);
        box-sizing: border-box;
        overflow-y: auto;
    }

    .data-role-set-footer {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
    }
}

.page-layout-column {
    width: 100%;
    // height: 100%;
    // box-sizing: border-box;
    // padding: 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .page-layout-column-left {
        width: 280px;
        height: 100%;
    }

    .page-layout-column-right {
        flex: 1;
        height: 100%;
        margin-left: 12px;
    }
}

.data-role-setting-item-group {
    padding: 12px;
    border-radius: 4px;
    background-color: rgb(var(--gray-1));

    .data-role-setting-item {
        display: flex;
        align-items: center;
        // justify-content: space-between;
        margin-right: 12px;

        .data-role-setting-item-title {
            font-size: 14px;
            font-weight: 600;
            color: rgb(var(--gray-9));
            margin-right: 12px;
        }

        .data-role-setting-item-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
}

.data-role-user-list {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 12px;
    // border-radius: 4px;
    // background-color: rgb(var(--gray-1));
}

:deep(.arco-table-th) {
    &:last-child {
        .arco-table-th-item-title {
            margin-left: 16px;
        }
    }
}

.action-icon {
    margin-left: 12px;
    cursor: pointer;
}

.active {
    color: #0960bd;
    background-color: #e3f4fc;
}

.setting {
    display: flex;
    align-items: center;
    width: 200px;

    .title {
        margin-left: 12px;
        cursor: pointer;
    }
}
</style>
