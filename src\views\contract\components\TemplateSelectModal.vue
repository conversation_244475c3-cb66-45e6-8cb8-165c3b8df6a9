<template>
  <a-modal
    v-model:visible="visible"
    title="选择打印模版"
    :width="600"
    :on-before-ok="handleTemplateSelect"
    @cancel="handleCancel"
  >
    <a-form :model="templateForm" layout="vertical">
      <a-form-item
        label="请选择模版"
        field="templateId"
        :rules="[{ required: true, message: '请选择模版' }]"
      >
        <a-select
          v-model="templateForm.templateId"
          placeholder="请选择模版"
          allow-clear
          :loading="loading"
        >
          <a-option
            v-for="template in templateList"
            :key="template.id"
            :value="template.id"
          >
            {{ template.templateName }}
          </a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getContractTemplateList, type ContractTemplateVo } from '@/api/contract'

interface Props {
  modelValue: boolean
  contractId?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'select', templateId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  contractId: ''
})

const emits = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const templateList = ref<ContractTemplateVo[]>([])
const templateForm = reactive({
  templateId: ''
})

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal && props.contractId) {
      loadTemplateList()
    }
  },
  { immediate: true }
)

// 监听visible变化，同步到父组件
watch(visible, (newVal) => {
  emits('update:modelValue', newVal)
})

// 加载模版列表
const loadTemplateList = async () => {
  if (!props.contractId) {
    Message.error('合同ID不能为空')
    return
  }

  try {
    loading.value = true
    const { data: result, code } = await getContractTemplateList(props.contractId)
    if (code === 200 && result) {
      templateList.value = result
      templateForm.templateId = '' // 重置选择
    } else {
      Message.error('获取模版列表失败')
    }
  } catch (error) {
    console.error('获取模版列表失败:', error)
    Message.error('获取模版列表失败')
  } finally {
    loading.value = false
  }
}

// 处理模版选择
const handleTemplateSelect = async () => {
  if (!templateForm.templateId) {
    Message.warning('请选择模版')
    return false
  }

  try {
    // 显示加载状态
    loading.value = true

    // 触发选择事件，传递templateId给父组件
    emits('select', templateForm.templateId)

    // 不在这里关闭弹窗，让父组件处理完print接口后再关闭
    return true
  } catch (error) {
    console.error('处理模版选择失败:', error)
    Message.error('处理失败')
    return false
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  visible.value = false
  templateForm.templateId = ''
}

// 暴露方法给父组件
defineExpose({
  loadTemplateList
})
</script>

<style scoped>
/* 可以添加组件特定的样式 */
</style>