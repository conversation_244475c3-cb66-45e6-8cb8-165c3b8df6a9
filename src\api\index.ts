import axios, {
    AxiosInstance,
    AxiosError,
    AxiosRequestConfig,
    AxiosResponse,
} from 'axios';
import useUserStore from '@/store/modules/user';
import { getToken } from '@/utils/auth';
import { Message, Modal } from '@arco-design/web-vue';

// 添加响应数据接口定义
interface ResponseData<T = any> {
    code: number;
    data: T;
    msg: string;
    [key: string]: any; // 允许添加任何其他字段
}

const baseConfig = {
    // 默认地址请求地址，可在 .env.** 文件中修改
    baseURL: import.meta.env.VITE_API_BASE_URL as string,
    // 设置超时时间
    timeout: 120000,
    // 跨域时候允许携带凭证
    withCredentials: true,
};

class RequestHttp {
    service: AxiosInstance;

    public constructor(initConfig: AxiosRequestConfig) {
        this.service = axios.create(initConfig);

        /**
         * @description 请求拦截器
         */
        this.service.interceptors.request.use(
            (config: AxiosRequestConfig) => {
                if (config.headers && getToken()) {
                    config.headers.Authorization = `Bearer ${getToken()}`;
                }
                return config;
            },
            (error: AxiosError) => {
                return Promise.reject(error);
            }
        );

        /**
         * @description 响应拦截器
         */
        this.service.interceptors.response.use(
            async (
                response: AxiosResponse<ResponseData> & {
                    config: AxiosRequestConfig;
                }
            ) => {
                const { data } = response;

                if (!!data.code && data.code !== 200) {
                    Message.error({
                        content: data.msg || 'Error',
                        duration: 5 * 1000,
                    });
                    // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
                    if (
                        [50008, 50012, 50014, 401].includes(data.code) &&
                        response.config.url !== '/api/user/info'
                    ) {
                        // Modal.error({
                        //     title: '确认退出',
                        //     content:
                        //         '你已退出登录，可以取消继续留在该页面，或重新登录',
                        //     okText: '重新登录',
                        //     async onOk() {
                                
                        //     },
                        // });
                        const userStore = useUserStore();
                        await userStore.logout();
                        window.location.reload();
                    }
                    return Promise.reject(new Error(data.msg || 'Error'));
                }
                return data;
            },
            async (error: AxiosError) => {
                Message.error({
                    content: error.response?.data?.msg || 'Request Error',
                    duration: 5 * 1000,
                });
                return Promise.reject(error);
            }
        );
    }

    /**
     * @description 请求常用方法封装
     */
    get<T = any>(
        url: string,
        params?: object,
        _object = {}
    ): Promise<ResponseData<T>> {
        return this.service.get(url, { params, ..._object });
    }

    post<T = any>(
        url: string,
        params?: object | string,
        _object = {}
    ): Promise<ResponseData<T>> {
        return this.service.post(url, params, _object);
    }

    put<T = any>(
        url: string,
        params?: object,
        _object = {}
    ): Promise<ResponseData<T>> {
        return this.service.put(url, params, _object);
    }

    delete<T = any>(
        url: string,
        params?: any,
        _object = {}
    ): Promise<ResponseData<T>> {
        return this.service.delete(url, { params, ..._object });
    }

    download(url: string, params?: object, _object = {}): Promise<BlobPart> {
        return this.service.post(url, params, {
            ..._object,
            responseType: 'blob',
        });
    }
    downloadForGet(url: string, params?: object, _object = {}): Promise<BlobPart> {
        return this.service.get(url, {
            params,
            ..._object,
            responseType: 'blob',
        });
    }
}

export default new RequestHttp(baseConfig);
