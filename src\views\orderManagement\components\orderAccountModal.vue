<template>
	<!-- 定单记账抽屉 -->
	<a-drawer :visible="visible" :title="isViewMode ? '查看记账记录' : '定单记账'" width="1200px" @cancel="handleCancel">
		<div class="order-account-modal">
			<!-- 账单信息 -->
			<section-title title="账单信息" style="margin-bottom: 16px;" />
			<div class="order-info" v-if="bookingDetail?.booking && bookingDetail?.cost">
				<a-descriptions :column="3" size="medium" bordered style="margin-bottom: 16px;">
					<a-descriptions-item label="项目名称">{{ bookingDetail.booking.projectName }}</a-descriptions-item>
					<a-descriptions-item label="预定房源">{{ bookingDetail.booking.roomName }}</a-descriptions-item>
					<a-descriptions-item label="预定方">{{ bookingDetail.booking.customerName }}</a-descriptions-item>
					<!-- <a-descriptions-item label="账单编号">{{ bookingDetail.cost.bizNo }}</a-descriptions-item>
					<a-descriptions-item label="应收日期">{{ bookingDetail.cost.receivableDate }}</a-descriptions-item>
					<a-descriptions-item label="账单状态">{{ getCostStatusText(bookingDetail.cost.status)
						}}</a-descriptions-item> -->
				</a-descriptions>

				<a-table :columns="orderInfoColumns" :data="orderInfoData" :pagination="false" :bordered="true"
					size="small" :show-header="true" :scroll="{ x: 1000 }" class="order-info-table" />
			</div>

			<!-- 流水信息 -->
			<section-title title="流水信息" style="margin: 24px 0 16px 0;" v-if="!isViewMode" />
			<div class="flow-info" v-if="!isViewMode">
				<!-- 记账模式的流水信息 -->
				<a-form :model="flowForm" v-if="advancedSearchVisible" :label-col-props="{ span: 9 }"
					:wrapper-col-props="{ span: 15 }">
					<!-- 原有的搜索表单 -->
					<a-row :gutter="16">
						<!-- <a-col :span="6">
							<a-form-item label="状态">
								<a-select v-model="flowForm.status" placeholder="请选择状态" allow-clear>
									<a-option :value="0">未记账</a-option>
									<a-option :value="1">部分记账</a-option>
									<a-option :value="2">已记账</a-option>
								</a-select>
							</a-form-item>
						</a-col> -->
						<a-col :span="6">
							<a-form-item label="支付类型">
								<a-select v-model="flowForm.payType" placeholder="请选择支付类型" allow-clear>
									<a-option :value="0">线上支付</a-option>
									<a-option :value="1">线下支付</a-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付方式">
								<a-select v-model="flowForm.payMethod" placeholder="请选择支付方式" allow-clear>
									<!-- //【农行掌银】、【微信】、【支付宝】+ 表里现有的【银行转账】 -->
									<a-option :value="'微信'">微信</a-option>
									<a-option :value="'支付宝'">支付宝</a-option>
									<a-option :value="'银行转账'">银行转账</a-option>
									<a-option :value="'农行掌银'">农行掌银</a-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付对象">
								<a-input v-model="flowForm.target" placeholder="请输入支付对象" allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="入账时间">
								<a-range-picker v-model="flowForm.payDate" style="width: 100%" />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="16">

						<a-col :span="6">
							<a-form-item label="支付金额">
								<a-input-number v-model="flowForm.amount" placeholder="请输入支付金额" allow-clear
									style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付人姓名">
								<a-input v-model="flowForm.payerName" placeholder="请输入支付人姓名" allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付人手机号">
								<a-input v-model="flowForm.payerPhone" placeholder="请输入支付人手机号" allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="支付账号">
								<a-input v-model="flowForm.payerAccount" placeholder="请输入支付账号" allow-clear />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="16">

						<a-col :span="6">
							<a-form-item label="收款商户">
								<a-input v-model="flowForm.merchant" placeholder="请输入收款商户" allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="订单号">
								<a-input v-model="flowForm.orderNo" placeholder="请输入订单号" allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-space style="margin-left: 16px;">
								<a-button type="primary" @click="handleSearch">查询</a-button>
								<a-button @click="handleReset">重置</a-button>
							</a-space>
						</a-col>
					</a-row>
				</a-form>
				<a-row>
					<AdvancedSearch @toggle="handleToggle" />
				</a-row>
				<!-- 全选操作 -->
				<div style="margin-bottom: 16px;text-align: right;">
					<a-space>
						<a-button type="primary" @click="handleBatchRecord" :disabled="selectedFlows.length === 0">
							记账
						</a-button>
					</a-space>
				</div>
				<a-table :columns="flowColumns" :data="flowData" row-key="id" :pagination="pagination"
					style="margin-top: 16px" :scroll="{ x: 1850 }" @page-change="handlePageChange" @page-size-change="handlePageSizeChange">
					<template #checkboxHeader>
						<a-checkbox v-model="selectAll" @change="handleSelectAll" size="mini" />
					</template>
					<template #checkbox="{ record }">
						<a-checkbox v-model="record.selected" @change="handleFlowSelect(record)"
							:disabled="isFlowRecorded(record.id)" size="mini" />
					</template>
					<template #status="{ record }">
						<a-tag v-if="record.status === 0" color="red">未记账</a-tag>
						<a-tag v-else-if="record.status === 1" color="orange">部分记账</a-tag>
						<a-tag v-else-if="record.status === 2" color="green">已记账</a-tag>
					</template>
					<template #payType="{ record }">
						<span>
							{{
								record.payType === 0 ? '线上支付' :
									record.payType === 1 ? '线下支付' : ''
							}}
						</span>
					</template>
					<template #payMethod="{ record }">
						<span>
							{{
								record.payType === 0 ?
									(record.payMethod === 1 ? '微信-账单支付' :
										record.payMethod === 2 ? '支付宝-账单支付' :
											record.payMethod === 3 ? '银行卡-账单支付' :
												record.payMethod === 4 ? '现金-账单支付' : '') :
									record.payType === 1 ? '银行转账' : ''
							}}
						</span>
					</template>
					<template #amount="{ record }">
						<span>{{ formatAmount(record.amount) }}</span>
					</template>
					<template #usedAmount="{ record }">
						<span>{{ formatAmount(record.usedAmount) }}</span>
					</template>
					<template #remainAmount="{ record }">
						<span>{{ formatAmount((record.amount || 0) - (record.usedAmount || 0)) }}</span>
					</template>
					<template #operation="{ record }">
						<a-button type="text" @click="handleRecordFlow(record)"
							:disabled="(record.amount || 0) <= (record.usedAmount || 0) || isFlowRecorded(record.id)"
							size="mini">
							记账
						</a-button>
					</template>
				</a-table>
			</div>



			<!-- 记账信息 -->
			<section-title title="记账信息" style="margin: 24px 0 16px 0;" v-if="!isViewMode" />
			<div class="account-info" v-if="!isViewMode">
				<a-table :columns="accountColumns" :data="accountData" row-key="id" :pagination="false"
					:scroll="{ x: 1 }">
					<template #amount="{ record }">
						<span>{{ formatAmount(record.amount) }}</span>
					</template>
					<template #remainAmount="{ record }">
						<span>{{ formatAmount(record.remainAmount) }}</span>
					</template>
					<template #accountAmount="{ record, rowIndex }">
						<a-input-number v-model="record.accountAmount" :precision="2" :min="0.01"
							:max="record.remainAmount" placeholder="请输入金额" style="width: 120px"
							@change="handleAccountAmountChange(rowIndex)" />
					</template>
					<template #remark="{ record }">
						<a-input v-model="record.remark" placeholder="请输入备注" style="width: 140px" :max-length="100" />
					</template>
					<template #operation="{ record, rowIndex }">
						<a-button type="text" status="danger" @click="handleRemove(rowIndex)">移除</a-button>
					</template>
				</a-table>

				<!-- 合计行 -->
				<div v-if="accountData.length > 0" class="account-summary">
					<a-row :gutter="16"
						style="padding: 12px 16px; background-color: var(--color-fill-1); border: 1px solid var(--color-border-2); border-top: none;">
						<a-col :span="24" style="text-align: right;">
							<a-space size="large">
								<span style="font-weight: 500;">本次记账金额合计：</span>
								<span style="font-size: 16px; font-weight: 600; color: var(--color-primary-6);">
									{{ formatAmount(totalAccountAmount) }}
								</span>
							</a-space>
						</a-col>
					</a-row>
				</div>
			</div>

			<!-- 历史记账信息 -->
			<section-title title="历史记账信息" style="margin: 24px 0 16px 0;" v-if="!!historyData.length"/>
			<div class="history-info" v-if="!!historyData.length">
				<a-table :columns="historyColumns" :data="historyData" row-key="id" :pagination="false" :scroll="{ x: 1 }">
					<template #payType="{ record }">
						<span>
							{{
								record.payType === 0 ? '线上支付' :
								record.payType === 1 ? '线下支付' : ''
							}}
						</span>
					</template>
					<template #payMethod="{ record }">
						<span>
							{{
								record.payType === 0 ? 
								(record.payMethod === 1 ? '微信-账单支付' :
								record.payMethod === 2 ? '支付宝-账单支付' :
								record.payMethod === 3 ? '银行卡-账单支付' :
								record.payMethod === 4 ? '现金-账单支付' : '') :
								record.payType === 1 ? '银行转账' : ''
							}}
						</span>
					</template>
					<template #type="{ record }">
						<span>
							<!-- 类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转 -->
							{{ getTypeText(record.type) }}
						</span>
					</template>
					<template #payAmount="{ record }">
						<span>{{ formatAmount(record.payAmount) }}</span>
					</template>
					<template #remainAmount="{ record }">
						<span>{{ formatAmount(record.remainAmount) }}</span>
					</template>
					<template #amount="{ record }">
						<span>{{ formatAmount(record.amount) }}</span>
					</template>
				</a-table>
				

				<!-- 合计行 -->
				<div v-if="historyData.length > 0" class="history-summary">
					<a-row :gutter="16" style="padding: 12px 16px; background-color: var(--color-fill-1); border: 1px solid var(--color-border-2); border-top: none;">
						<a-col :span="24" style="text-align: right;">
							<a-space size="large">
								<span><strong>合计-本次记账金额：{{ formatAmount(totalHistoryAmount) }}</strong></span>
							</a-space>
						</a-col>
					</a-row>
				</div>
			</div>

			<!-- 查看模式：流水信息 -->
			<section-title title="流水信息" style="margin: 24px 0 16px 0;" v-if="isViewMode" />
			<div class="flow-info" v-if="isViewMode">
				<!-- 一键确认按钮 -->
				<div style="margin-bottom: 16px; text-align: right;">
					<a-button type="primary" @click="handleBatchConfirm" :disabled="!hasUnconfirmedFlows">
						一键确认
					</a-button>
				</div>
				<a-table :columns="viewFlowColumns" :data="viewFlowData" row-key="id" :pagination="false"
					:scroll="{ x: 1 }">
					<template #type="{ record }">
						<span>{{ getTypeText(record.type) }}</span>
					</template>
					<template #payType="{ record }">
						<span>
							{{
								record.payType === 0 ? '线上支付' :
									record.payType === 1 ? '线下支付' : ''
							}}
						</span>
					</template>
					<!-- <template #payMethod="{ record }">
						<span>
							{{
								record.payMethod === 1 ? '微信' :
									record.payMethod === 2 ? '支付宝' :
										record.payMethod === 3 ? '银行卡' :
											record.payMethod === 4 ? '现金' : ''
							}}
						</span>
					</template> -->
					<template #payAmount="{ record }">
						<span>{{ formatAmount(record.payAmount) }}</span>
					</template>
					<template #acctAmount="{ record }">
						<span>{{ formatAmount(record.acctAmount) }}</span>
					</template>
					<template #confirmStatus="{ record }">
						<!-- 0-未确认、1-自动确认、2-手动确认 -->	
						<a-tag :color="record.confirmStatus === 0 ? 'red' : record.confirmStatus === 1 ? 'blue' : 'green'">
							{{ record.confirmStatus === 0 ? '未确认' : record.confirmStatus === 1 ? '自动确认' : '手动确认' }}
						</a-tag>
					</template>
					<template #operation="{ record }">
						<a-space>
							<a-button type="text" size="mini" @click="handleConfirmFlow(record)"
								v-if="record.confirmStatus === 0">
								确认
							</a-button>
							<a-button type="text" size="mini" status="danger" @click="handleCancelAccount(record)">
								取消记账
							</a-button>
						</a-space>
					</template>
				</a-table>
			</div>
			<!-- 查看模式：操作记录 -->
			<section-title title="操作记录" style="margin: 24px 0 16px 0;" v-if="!!logData.length"/>
			<div class="history-info" v-if="!!logData.length">
				<a-table :columns="logColumns" :data="logData" row-key="id" :pagination="false" :scroll="{ x: 1 }">
					<template #payType="{ record }">
						<span>
							{{
								record.payType === 0 ? '线上支付' :
								record.payType === 1 ? '线下支付' : ''
							}}
						</span>
					</template>
					<template #payMethod="{ record }">
						<span>
							{{
								record.payType === 0 ? 
								(record.payMethod === 1 ? '微信-账单支付' :
								record.payMethod === 2 ? '支付宝-账单支付' :
								record.payMethod === 3 ? '银行卡-账单支付' :
								record.payMethod === 4 ? '现金-账单支付' : '') :
								record.payType === 1 ? '银行转账' : ''
							}}
						</span>
					</template>
					<template #type="{ record }">
						<span>
							<!-- 类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转 -->
							{{ getTypeText(record.type) }}
						</span>
					</template>
					<template #payAmount="{ record }">
						<span>{{ formatAmount(record.payAmount) }}</span>
					</template>
					<template #remainAmount="{ record }">
						<span>{{ formatAmount(record.remainAmount) }}</span>
					</template>
					<template #amount="{ record }">
						<span>{{ formatAmount(record.amount) }}</span>
					</template>
				</a-table>
				

				<!-- 合计行 -->
				<div v-if="historyData.length > 0" class="history-summary">
					<a-row :gutter="16" style="padding: 12px 16px; background-color: var(--color-fill-1); border: 1px solid var(--color-border-2); border-top: none;">
						<a-col :span="24" style="text-align: right;">
							<a-space size="large">
								<span><strong>合计-本次记账金额：{{ formatAmount(totalHistoryAmount) }}</strong></span>
							</a-space>
						</a-col>
					</a-row>
				</div>
			</div>
		</div>
		<!-- 底部操作按钮 -->
		<template #footer>
			<a-space>
				<a-button @click="handleCancel" :disabled="saving">{{ isViewMode ? '关闭' : '取消' }}</a-button>
				<a-button type="primary" @click="handleSave" :loading="saving" v-if="!isViewMode">保存</a-button>
			</a-space>
		</template>
	</a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import AdvancedSearch from '@/components/advancedSearch/index.vue'
import {
	getFinancialFlowRecordList,
	getFinancialFlowList,
	type FinancialFlowRecordQueryDTO,
	type FinancialFlowQueryDTO,
	type FinancialFlowVo,
	type TableDataInfo
} from '@/api/flowManage'
import {
	saveCostRecord,
	confirmCostRecord,
	cancelCostRecord,
	type CostSaveRecordDTO,
	type CostFlowRelAddDTO,
	type CostConfirmRecordDTO,
	type CostFlowLogVo
} from '@/api/billManage'
import {
	getOrderCostDetail,
	type BookingDetailVo
} from '@/api/orderManagement'

const props = defineProps<{
	data: any
}>()

const emit = defineEmits(['cancel', 'save'])

// 抽屉显示控制
const visible = ref(false)

// 查看模式（true: 查看记账记录，false: 记账操作）
const isViewMode = computed(() => props.data?.viewMode === true)

// 定单详情数据
const bookingDetail = ref<BookingDetailVo | null>(null)

// 打开抽屉
const open = async () => {
	visible.value = true
	// 初始化数据
	await initData()
}

// 关闭抽屉
const close = () => {
	visible.value = false
	bookingDetail.value = null
	// 清理记账数据
	accountData.value = []
	flowData.value = []
	historyData.value = []
	logData.value = []
	viewFlowData.value = []
	// 重置查询模式和表单
	isFilterMode.value = false
	pagination.current = 1
	pagination.total = 0
	Object.assign(flowForm, {
		status: undefined,
		payType: undefined,
		payMethod: undefined,
		target: '',
		payerName: '',
		payerPhone: '',
		payerAccount: '',
		merchant: '',
		payDate: [],
		amount: undefined,
		orderNo: ''
	})
}

// 暴露方法给父组件调用
defineExpose({
	open,
	close
})

// 初始化数据
const initData = async () => {
	try {
		// 获取定单账单详情（包含定单信息、账单信息、流水关系等）
		await fetchBookingDetail()

		if (!isViewMode.value) {
			// recordColumnsView = recordColumnsRe
			// 记账模式：获取可记账的流水数据
			fetchFlowData()
			// 初始化记账数据
			fetchAccountData()
		}else{
			
		}

		// 获取历史记账数据（使用costDetail中的flowRelList）
		fetchHistoryData()
	} catch (error) {
		console.error('初始化数据失败:', error)
		Message.error('获取定单信息失败')
	}
}

// 获取定单账单详情
const fetchBookingDetail = async () => {
	try {
		const response = await getOrderCostDetail(props.data.id)
		bookingDetail.value = response.data || response
	} catch (error) {
		console.error('获取定单详情失败:', error)
		throw error
	}
}

// 定单信息表格列配置
const orderInfoColumns = [
	{ title: '账单类型', dataIndex: 'costType', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '实际应收金额', dataIndex: 'actualReceivable', width: 140, align: 'center' },
	{ title: '实际已收金额', dataIndex: 'receivedAmount', width: 140, align: 'center' },
	{ title: '未收金额', dataIndex: 'unpaidAmount', width: 120, align: 'center' },
	{ title: '应收日期', dataIndex: 'receivableDate', width: 120, align: 'center' },
	// { title: '状态', dataIndex: 'status', width: 100, align: 'center' }
]

// 定单信息数据
const orderInfoData = computed(() => {
	if (!bookingDetail.value?.booking || !bookingDetail.value?.cost) return []

	const booking = bookingDetail.value.booking
	const cost = bookingDetail.value.cost

	return [{
		//取账单subjectName，period有值就拼接上第period期
		costType: cost.subjectName + (cost.period ? `第${cost.period}期` : ''),
		actualReceivable: formatAmount(cost.actualReceivable),
		receivedAmount: formatAmount(cost.receivedAmount || 0),
		unpaidAmount: formatAmount((cost.actualReceivable || 0) - (cost.receivedAmount || 0)),
		receivableDate: cost.receivableDate || '',
		status: getCostStatusText(cost.status)
	}]
})

// 流水表单数据
const flowForm = reactive({
	status: undefined,
	payType: undefined,
	payMethod: undefined,
	target: '',
	payerName: '',
	payerPhone: '',
	payerAccount: '',
	merchant: '',
	payDate: [],
	amount: undefined,
	orderNo: ''
})

// 流水表格列配置
const flowColumns = [
	{
		title: '',
		dataIndex: 'selected',
		slotName: 'checkbox',
		titleSlotName: 'checkboxHeader',
		width: 50,
		align: 'center',
		headerCellStyle: { padding: '8px 4px' },
		bodyCellStyle: { padding: '8px 4px' }
	},
	{ title: '状态', dataIndex: 'status', slotName: 'status', width: 100, align: 'center' },
	{ title: '支付类型', dataIndex: 'payType', slotName: 'payType', width: 100, align: 'center' },
	{ title: '支付方式', dataIndex: 'payMethod', slotName: 'payMethod', width: 140, align: 'center' },
	{ title: '支付对象', dataIndex: 'target', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '入账时间', dataIndex: 'entryTime', width: 180, align: 'center', ellipsis: true, tooltip: true},
	{ title: '支付金额', dataIndex: 'amount', slotName: 'amount', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '已使用金额', dataIndex: 'usedAmount', slotName: 'usedAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '剩余金额', dataIndex: 'remainAmount', slotName: 'remainAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付人姓名', dataIndex: 'payerName', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付人手机号', dataIndex: 'payerPhone', width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付账号', dataIndex: 'payerAccount', width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付备注', dataIndex: 'payRemark', width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '收款商户', dataIndex: 'merchant', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '订单号', dataIndex: 'orderNo', width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '操作', slotName: 'operation', width: 100, align: 'center', fixed: 'right' }
]

// 记账信息 - 记账表格列配置
const accountColumns = [
	{ title: '项目', dataIndex: 'projectName', width: 190, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付对象', dataIndex: 'target', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '流水订单号', dataIndex: 'orderNo', width: 160,align: 'center', ellipsis: true, tooltip: true },
	{ title: '流水支付金额', dataIndex: 'amount', slotName: 'amount', width: 120, align: 'center' },
	// { title: '流水剩余金额', dataIndex: 'remainAmount', slotName: 'remainAmount', width: 120, align: 'center' },
	{ title: '本次记账金额', dataIndex: 'accountAmount', slotName: 'accountAmount', width: 140, align: 'center' },
	// { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 150, align: 'center' },
	{ title: '入账时间', dataIndex: 'entryTime', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付人姓名', dataIndex: 'payerName', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '收款商户', dataIndex: 'merchant', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '操作', slotName: 'operation', width: 100, align: 'center', fixed: 'right' }
]

// 历史记账表格列配置
const historyColumns = [
	// 账单类型：1-保证金,2-租金,3-其他费用
	{ title: '类型', dataIndex: 'type', slotName: 'type', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '实际支付时间', dataIndex: 'entryTime', slotName: 'entryTime', width: 180, align: 'center', ellipsis: true, tooltip: true },

	// { title: '项目', dataIndex: 'projectName', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付类型', dataIndex: 'payType', slotName: 'payType', width: 100, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付方式', dataIndex: 'payMethod', slotName: 'payMethod', width: 100, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付单号', dataIndex: 'orderNo',width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '实际支付金额', dataIndex: 'payAmount', slotName: 'payAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '本次记账金额', dataIndex: 'acctAmount', slotName: 'acctAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '账单累计记账金额', dataIndex: 'cumulativeAcctAmount', slotName: 'acctAmount', width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '操作时间', dataIndex: 'createTime', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '确认状态', dataIndex: 'confirmStatus', slotName: 'confirmStatus', width: 100, align: 'center' }, //0-未确认、1-自动确认、2-手动确认
	{ title: '确认时间', dataIndex: 'confirmTime', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '确认人', dataIndex: 'confirmUserName', width: 160, align: 'center', ellipsis: true, tooltip: true },
]


// 查看模式：流水信息
const viewFlowColumns = [
	// 账单类型：1-保证金,2-租金,3-其他费用
	{ title: '类型', dataIndex: 'type', slotName: 'type', width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '实际支付时间', dataIndex: 'entryTime', slotName: 'entryTime', width: 180, align: 'center', ellipsis: true, tooltip: true },

	// { title: '项目', dataIndex: 'projectName', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付类型', dataIndex: 'payType', slotName: 'payType', width: 100, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付方式', dataIndex: 'payMethod', slotName: 'payMethod', width: 100, align: 'center', ellipsis: true, tooltip: true },
	{ title: '支付单号', dataIndex: 'orderNo',width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '实际支付金额', dataIndex: 'payAmount', slotName: 'payAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '本次记账金额', dataIndex: 'acctAmount', slotName: 'acctAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '账单累计记账金额', dataIndex: 'cumulativeAcctAmount', slotName: 'acctAmount', width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '备注', dataIndex: 'remark', slotName: 'remark', width: 160, align: 'center', ellipsis: true, tooltip: true },

	{ title: '操作时间', dataIndex: 'createTime', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '确认状态', dataIndex: 'confirmStatus', slotName: 'confirmStatus', width: 100, align: 'center' }, //0-未确认、1-自动确认、2-手动确认
	{ title: '确认时间', dataIndex: 'confirmTime', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '确认人', dataIndex: 'confirmUserName', width: 160, align: 'center', ellipsis: true, tooltip: true },
	{ title: '操作', slotName: 'operation', width: 160, align: 'center', fixed: 'right' }
]
// 查看模式：操作记录
const logColumns = [
	// { title: '项目', dataIndex: 'projectName', width: 120, align: 'center', ellipsis: true, tooltip: true },
	// ：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转
	{ title: '类型', dataIndex: 'type', slotName: 'type', width: 120, align: 'center' , ellipsis: true, tooltip: true},
	// { title: '支付方式', dataIndex: 'payMethod', slotName: 'payMethod', width: 120, align: 'center' },
	// { title: '支付对象', dataIndex: 'target', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '单号', dataIndex: 'flowNo', width: 160, align: 'center', ellipsis: true, tooltip: true },
	// { title: '流水支付金额', dataIndex: 'payAmount', slotName: 'payAmount', width: 120, align: 'center' },
	// { title: '流水剩余金额', dataIndex: 'remainAmount', slotName: 'remainAmount', width: 120, align: 'center' },
	{ title: '金额(元)', dataIndex: 'amount', slotName: 'amount', width: 120, align: 'center', ellipsis: true, tooltip: true},
	{ title: '操作时间', dataIndex: 'createTime', slotName: 'createTime', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '操作人', dataIndex: 'createByName', slotName: 'createByName', width: 120, align: 'center', ellipsis: true, tooltip: true },
	// { title: '入账时间', dataIndex: 'entryTime', width: 160, align: 'center' },
	// { title: '支付人姓名', dataIndex: 'payerName', width: 120, align: 'center' },
	// { title: '收款商户', dataIndex: 'merchant', width: 120, align: 'center', ellipsis: true, tooltip: true }
]


// 数据
const flowData = ref<any[]>([])
const accountData = ref<any[]>([])
const historyData = ref<any[]>([])
const logData = ref<any[]>([])
const viewFlowData = ref<any[]>([]) // 查看模式的流水数据
const advancedSearchVisible = ref(false)

// 查询模式：false=初始模式(recordList)，true=筛选模式(list)
const isFilterMode = ref(false)

// 分页数据
const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showTotal: true,
	showPageSize: true,
	pageSizeOptions: [10, 20, 50, 100]
})

// 全选相关
const selectAll = ref(false)
const selectedFlows = ref<any[]>([])

// 记账相关数据（保留用于其他可能的用途）

// 最大可记账金额（保留用于其他可能的用途）
// const maxAccountAmount = computed(() => {
// 	if (!currentFlow.value) return 0
// 	return (currentFlow.value.amount || 0) - (currentFlow.value.usedAmount || 0)
// })

// 合计本次记账金额
const totalAccountAmount = computed(() => {
	return accountData.value.reduce((total, item) => {
		return total + (item.accountAmount || 0)
	}, 0)
})

// 合计历史记账金额
const totalHistoryAmount = computed(() => {
	return historyData.value.reduce((total, item) => {
		return total + (item.amount || 0)
	}, 0)
})

// 是否有未确认的流水
const hasUnconfirmedFlows = computed(() => {
	return viewFlowData.value.some(item => item.confirmStatus !== 1)
})

// 金额格式化
const formatAmount = (amount: number | undefined) => {
	if (amount === undefined || amount === null) return '0.00'
	return amount.toLocaleString('zh-CN', {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2
	})
}

// 获取支付方式文本
const getPayMethodText = (payMethod: number | undefined) => {
	const methodMap = new Map([
		[1, '微信'],
		[2, '支付宝'],
		[3, '银行卡'],
		[4, '现金']
	])
	return methodMap.get(payMethod || 0) || ''
}

// 获取定单状态文本
const getOrderStatusText = (status: number | undefined) => {
	const statusMap = new Map([
		[0, '草稿'],
		[1, '待收费'],
		[2, '已生效'],
		[3, '已转签'],
		[4, '已作废']
	])
	return statusMap.get(status || 0) || '未知'
}

// 获取账单状态文本
const getCostStatusText = (status: number | undefined) => {
	const statusMap = new Map([
		// 账单状态: 0-待收、1-待付、2-已收、3-已付
		[0, '待收'],
		[1, '待付'],
		[2, '已收'],
		[3, '已付']
	])
	return statusMap.get(status || 0) || '未知'
}

// 获取账单类型文本
const getCostTypeText = (costType: number | undefined) => {
	// 账单类型: 1-保证金,2-租金,3-其他费用
	const typeMap = new Map([
		[1, '保证金'],
		[2, '租金'],
		[3, '其他费用']
	])
	return typeMap.get(costType || 0) || '未知'
}
// 记账类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转
const getTypeText = (type: number | undefined) => {
	console.log(type)
	const typeMap = new Map([
		[1, '手动记账（收款）'],
		[2, '自动记账（收款）'],
		[3, '手动记账（退款）'],
		[4, '自动记账（退款）'],
		[5, '结转（转入）'],
		[6, '结转（转出）'],
		[7, '取消记账']
	])
	return typeMap.get(Number(type)) || '未知'
}


// 验证记账金额（已删除，不再使用弹窗方式）

// 查询流水
const handleSearch = () => {
	isFilterMode.value = true // 切换到筛选模式
	pagination.current = 1 // 重置分页到第一页
	fetchFlowData()
}

// 重置查询条件
const handleReset = () => {
	Object.assign(flowForm, {
		status: undefined,
		payType: undefined,
		payMethod: undefined,
		target: '',
		payerName: '',
		payerPhone: '',
		payerAccount: '',
		merchant: '',
		payDate: [],
		amount: undefined,
		orderNo: ''
	})
	isFilterMode.value = false // 切换回初始模式
	pagination.current = 1 // 重置分页
	fetchFlowData()
}

// 高级搜索切换
const handleToggle = (visible: boolean) => {
	advancedSearchVisible.value = visible
}

// 分页处理
const handlePageChange = (page: number) => {
	pagination.current = page
	fetchFlowData()
}

const handlePageSizeChange = (pageSize: number) => {
	pagination.pageSize = pageSize
	pagination.current = 1
	fetchFlowData()
}

// 记账金额变化处理
const handleAccountAmountChange = (rowIndex: number) => {
	const record = accountData.value[rowIndex]
	if (record) {
		// 确保记账金额不超过剩余金额
		if (record.accountAmount > record.remainAmount) {
			record.accountAmount = record.remainAmount
			Message.warning('记账金额不能超过剩余金额')
		}

		// 注意：不要修改流水的已使用金额，那是来自API的固定数据
		// 已使用金额应该保持不变，剩余金额 = 支付金额 - 已使用金额
	}
}

// 检查流水是否已记账
const isFlowRecorded = (flowId: string) => {
	return accountData.value.some(item => item.flowId === flowId)
}

// 处理流水选择
const handleFlowSelect = (record: any) => {
	if (record.selected) {
		if (!selectedFlows.value.find(item => item.id === record.id)) {
			selectedFlows.value.push(record)
		}
	} else {
		const index = selectedFlows.value.findIndex(item => item.id === record.id)
		if (index !== -1) {
			selectedFlows.value.splice(index, 1)
		}
	}

	// 更新全选状态
	updateSelectAllStatus()
}

// 全选处理
const handleSelectAll = (checked: boolean) => {
	const availableFlows = flowData.value.filter(item =>
		(item.amount || 0) > (item.usedAmount || 0) && !isFlowRecorded(item.id)
	)

	if (checked) {
		availableFlows.forEach(item => {
			item.selected = true
			if (!selectedFlows.value.find(selected => selected.id === item.id)) {
				selectedFlows.value.push(item)
			}
		})
	} else {
		availableFlows.forEach(item => {
			item.selected = false
		})
		selectedFlows.value = []
	}
}

// 更新全选状态
const updateSelectAllStatus = () => {
	const availableFlows = flowData.value.filter(item =>
		(item.amount || 0) > (item.usedAmount || 0) && !isFlowRecorded(item.id)
	)

	if (availableFlows.length === 0) {
		selectAll.value = false
	} else {
		selectAll.value = availableFlows.every(item => item.selected)
	}
}

// 批量记账
const handleBatchRecord = () => {
	if (selectedFlows.value.length === 0) {
		Message.warning('请选择要记账的流水')
		return
	}

	// 按入账时间排序
	const sortedFlows = [...selectedFlows.value].sort((a, b) => {
		return new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime()
	})

	sortedFlows.forEach(record => {
		handleRecordFlow(record)
		record.selected = false
	})

	selectedFlows.value = []
	selectAll.value = false

	// Message.success(`已批量记账 ${sortedFlows.length} 条流水`)
}

// 记账流水
const handleRecordFlow = (record: any) => {
	// 计算剩余金额
	const remainAmount = (record.amount || 0) - (record.usedAmount || 0)

	// 计算本次记账金额（根据业务规则）
	const booking = bookingDetail.value?.booking
	const cost = bookingDetail.value?.cost
	const unpaidAmount = (cost?.actualReceivable || 0) - (cost?.receivedAmount || 0) // 账单未收金额
	let accountAmount = 0

	if (remainAmount <= unpaidAmount) {
		accountAmount = remainAmount
	} else {
		accountAmount = unpaidAmount
	}

	// 直接添加到记账列表
	const accountRecord = {
		id: Date.now(), // 临时ID
		flowId: record.id,
		projectName: record.projectName || bookingDetail.value?.booking?.projectName,
		target: record.target,
		orderNo: record.orderNo,
		amount: record.amount,
		remainAmount: remainAmount,
		accountAmount: accountAmount,
		remark: '', // 初始化备注字段
		entryTime: record.entryTime,
		payerName: record.payerName,
		merchant: record.merchant
	}

	accountData.value.push(accountRecord)

	// 注意：不要修改流水的已使用金额，那是来自API的固定数据
	// 只有在保存记账成功后，服务器才会更新流水的已使用金额

	// Message.success('已添加到记账信息')
}

// 已删除弹窗相关的记账方法，现在直接在表格中操作

// 移除记账
const handleRemove = (rowIndex: number) => {
	const record = accountData.value[rowIndex]
	if (record) {
		// 重置流水的选择状态
		const flowIndex = flowData.value.findIndex(item => item.id === record.flowId)
		if (flowIndex !== -1) {
			flowData.value[flowIndex].selected = false
		}

		// 从选中列表中移除
		const selectedIndex = selectedFlows.value.findIndex(item => item.id === record.flowId)
		if (selectedIndex !== -1) {
			selectedFlows.value.splice(selectedIndex, 1)
		}

		accountData.value.splice(rowIndex, 1)

		// 更新全选状态
		updateSelectAllStatus()

		Message.success('移除成功')
	}
}

// 取消
const handleCancel = () => {
	close()
	emit('cancel')
}

// 保存状态
const saving = ref(false)

// 保存
const handleSave = async () => {
	if (accountData.value.length === 0) {
		Message.warning('请先添加记账信息')
		return
	}

	if (saving.value) {
		return
	}

	saving.value = true

	try {
		// 直接使用已获取的账单信息
		if (!bookingDetail.value?.cost?.id) {
			Message.error('未找到对应的账单信息')
			return
		}

		const costId = bookingDetail.value.cost.id

		// 构建记账记录列表
		const flowRelList: CostFlowRelAddDTO[] = accountData.value.map(record => ({
			costId: costId, // 使用查询到的账单ID
			flowId: record.flowId,
			flowNo: record.orderNo,
			type: 1, // 账单类型：1-收款
			confirmStatus: 1, // 确认状态: 1-自动确认
			payAmount: record.amount, // 支付金额
			acctAmount: record.accountAmount // 本次记账金额
		}))

		// 构建保存记账的请求数据
		const saveData: CostSaveRecordDTO = {
			costId: costId, // 使用查询到的账单ID
			flowRelList: flowRelList
		}

		// 调用保存记账接口
		await saveCostRecord(saveData)

		visible.value = false
		emit('save', accountData.value)
		// Message.success('保存记账成功')

	} catch (error: any) {
		console.error('保存记账失败:', error)
		const errorMsg = error?.response?.data?.msg || error?.message || '保存记账失败，请重试'
		// Message.error(errorMsg)
	} finally {
		saving.value = false
	}
}

// 获取流水数据
const fetchFlowData = async () => {
	try {
		let response: any

		console.log('isFilterMode.value', isFilterMode.value)

		if (isFilterMode.value) {
			// 筛选模式：调用 financialFlow/list 接口
			const listQueryParams: FinancialFlowQueryDTO = {
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				searchType: 1,
				projectId: bookingDetail.value?.booking?.projectId, // 传递项目ID
			}

			// 如果有高级搜索条件，添加到查询参数中
			if (flowForm.payType !== undefined) {
				listQueryParams.payType = flowForm.payType
			}
			if (flowForm.payMethod !== undefined) {
				listQueryParams.payMethod = flowForm.payMethod
			}
			if (flowForm.target) {
				listQueryParams.target = flowForm.target
			}
			if (flowForm.payerName) {
				listQueryParams.payerName = flowForm.payerName
			}
			if (flowForm.payerPhone) {
				listQueryParams.payerPhone = flowForm.payerPhone
			}
			if (flowForm.payerAccount) {
				listQueryParams.payerAccount = flowForm.payerAccount
			}
			if (flowForm.merchant) {
				listQueryParams.merchant = flowForm.merchant
			}
			if (flowForm.amount !== undefined) {
				listQueryParams.amount = flowForm.amount
			}
			if (flowForm.orderNo) {
				listQueryParams.orderNo = flowForm.orderNo
			}
			if (flowForm.payDate && flowForm.payDate.length === 2) {
				listQueryParams.entryTimeStart = flowForm.payDate[0]
				listQueryParams.entryTimeEnd = flowForm.payDate[1]
			}

			response = await getFinancialFlowList(listQueryParams)
		} else {
			// 初始模式：调用 financialFlow/recordList 接口
			const recordQueryParams: FinancialFlowRecordQueryDTO = {
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				bookingId: props.data.id, // 使用定单ID查询相关流水
			}

			response = await getFinancialFlowRecordList(recordQueryParams)
		}

		if (response.rows) {
			// 过滤出可记账的流水（status为0或1且有剩余金额的流水）
			flowData.value = response.rows.filter((item: FinancialFlowVo) => {
				const remainAmount = (item.amount || 0) - (item.usedAmount || 0)
				// status为0(未记账)或1(部分记账)且有剩余金额的记录才可以记账
				const isAccountable = (item.status === 0 || item.status === 1) && remainAmount > 0
				return isAccountable
			}).map((item: FinancialFlowVo) => ({
				...item,
				selected: false // 初始化选择状态
			}))
			
			// 更新分页总数
			pagination.total = response.total || 0
		} else {
			flowData.value = []
			pagination.total = 0
		}

		// 重置选择状态
		selectedFlows.value = []
		selectAll.value = false
	} catch (error) {
		console.error('获取流水数据失败:', error)
		Message.error('获取流水数据失败')
		flowData.value = []
	}
}

// 获取记账数据
const fetchAccountData = () => {
	// 初始化为空数组，用户操作时会动态添加
	accountData.value = []
}

// 获取历史记账数据
const fetchHistoryData = async () => {
	try {
		if (isViewMode.value) {
			// 查看模式：使用flowLogList作为历史记账数据
			if (bookingDetail.value?.flowLogList) {
				logData.value = JSON.parse(JSON.stringify(bookingDetail.value.flowLogList))
			} else {
				logData.value = []
			}

			// 同时使用flowRelList作为查看模式的流水信息数据
			if (bookingDetail.value?.flowRelList) {
				viewFlowData.value = bookingDetail.value.flowRelList.map(item => ({
					...item,
					costType: bookingDetail.value?.cost?.costType
				}))
			} else {
				viewFlowData.value = []
			}
		} else {
			// 记账模式：使用flowRelList作为历史记账数据
			if (bookingDetail.value?.flowRelList) {
				historyData.value = JSON.parse(JSON.stringify(bookingDetail.value.flowRelList))
			} else {
				historyData.value = []
			}
		}
	} catch (error) {
		console.error('获取历史记账数据失败:', error)
		historyData.value = []
		logData.value = []
		if (isViewMode.value) {
			viewFlowData.value = []
		}
	}
}

// 一键确认所有流水
const handleBatchConfirm = async () => {
	try {
		const unconfirmedFlows = viewFlowData.value.filter(item => item.confirmStatus !== 1)
		if (unconfirmedFlows.length === 0) {
			Message.warning('没有需要确认的流水')
			return
		}

		if (!bookingDetail.value?.cost?.id) {
			Message.error('账单信息不完整')
			return
		}

		// 调用一键确认API
		const confirmData: CostConfirmRecordDTO = {
			costId: bookingDetail.value.cost.id,
			isOneKeyConfirm: true
		}

		await confirmCostRecord(confirmData)

		// 重新获取数据以更新状态
		await initData()

		Message.success('一键确认成功')
	} catch (error) {
		console.error('一键确认失败:', error)
		Message.error('一键确认失败')
	}
}

// 确认单个流水
const handleConfirmFlow = async (record: any) => {
	try {
		if (!bookingDetail.value?.cost?.id || !record.id) {
			Message.error('数据不完整')
			return
		}

		// 调用确认流水API
		const confirmData: CostConfirmRecordDTO = {
			costId: bookingDetail.value.cost.id,
			isOneKeyConfirm: false,
			flowRelId: record.id
		}

		await confirmCostRecord(confirmData)

		// 重新获取数据以更新状态
		await initData()

		Message.success('确认成功')
	} catch (error) {
		console.error('确认失败:', error)
		Message.error('确认失败')
	}
}

// 取消记账
const handleCancelAccount = async (record: any) => {
	try {
		if (!record.id) {
			Message.error('记账记录ID不存在')
			return
		}

		// 调用取消记账API
		await cancelCostRecord(record.id)

		// 重新获取数据以更新状态
		await initData()

		Message.success('取消记账成功')
	} catch (error) {
		console.error('取消记账失败:', error)
		Message.error('取消记账失败')
	}
}
</script>

<style scoped lang="less">
.order-account-modal {

	.order-info,
	.flow-info,
	.account-info,
	.history-info {
		margin-bottom: 24px;
	}

	.footer {
		text-align: right;
		margin-top: 24px;
	}

	.order-info-table {
		:deep(.arco-table-th) {
			background-color: var(--color-fill-2);
			font-weight: 500;
			text-align: center;
		}

		:deep(.arco-table-td) {
			text-align: center;
		}

		:deep(.arco-table-tr) {
			&:hover {
				background-color: transparent;
			}
		}
	}
}
</style>