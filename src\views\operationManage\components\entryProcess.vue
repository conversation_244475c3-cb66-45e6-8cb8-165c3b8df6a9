<template>
    <div class="entry-process">
        <!-- 基本信息 -->
        <section-title title="基本信息" style="margin-bottom: 16px;" />
        <a-row :gutter="16" class="info-section">
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">合同编号:</span>
                    <span class="value">{{ data?.contractNo }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">合同用途:</span>
                    <span class="value">{{ getDictLabel('diversification_purpose', data.contractPurpose?.toString())
                    }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">租期:</span>
                    <span class="value">{{ data.rentStartDate && data.rentEndDate
                        ? `${data.rentStartDate} 至 ${data.rentEndDate}`
                        : '' }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">承租方:</span>
                    <span class="value">{{ data?.tenantName }}</span>
                </div>
            </a-col>
        </a-row>

        <!-- 物业交割单 -->
        <section-title title="物业交割单" style="margin-bottom: 16px;" />
        <a-collapse :expand-icon-position="expandPosition" style="margin-top: 12px;">
            <a-collapse-item v-for="(house, index) in houseList" :key="index">
                <template #header>
                    <div class="property-header">
                        <span>{{ house.buildingName }} {{ house.roomName }}</span>
                        <!-- <span class="date">进场日期：{{ house.enterDate || '未设置' }}</span> -->
                    </div>
                </template>
                <div class="property-content">
                    <!-- 进场日期 -->
                    <a-row :gutter="16" style="margin-bottom: 16px;">
                        <a-col :span="8">
                            <a-form-item label="进场日期" :validate-trigger="['change', 'input']"
                                :rules="editType === 'detail' ? null : [{ required: true, message: '请选择进场日期' }]">
                                <a-date-picker :disabled="editType === 'detail'" v-model="house.enterDate"
                                    style="width: 100%;" placeholder="请选择进场日期" />
                            </a-form-item>
                        </a-col>
                    </a-row>

                    <!-- 房间配套情况 -->
                    <div class="section-header" v-if="editType !== 'detail'">
                        <section-title title="房间配套情况" />
                        <a-button type="primary" size="small" @click="showAssetLibrary(house)">
                            <template #icon>
                                <icon-plus />
                            </template>
                            添加配套
                        </a-button>
                    </div>
                    <a-table :data="house.assetList" :columns="accessoryColumns" :pagination="false"
                        :bordered="{ cell: true }" row-key="id">
                        <template #index="{ rowIndex }">
                            {{ rowIndex + 1 }}
                        </template>
                        <template #operations="{ record, rowIndex }">
                            <a-checkbox :disabled="editType === 'detail'" v-if="!record.isAdd"
                                v-model="record.isMissing">缺失</a-checkbox>
                            <a-button v-if="record.isAdd && editType !== 'detail'" type="text" size="mini"
                                @click="removeAccessory(house, rowIndex)">移除</a-button>
                        </template>
                    </a-table>

                    <!-- 水电度数 -->
                    <section-title title="水电度数" style="margin: 16px 0;" />
                    <a-descriptions layout="vertical" bordered table-layout="fixed"
                        :label-style="{ textAlign: 'center', color: '#333', backgroundColor: 'var(--color-fill-2)' }">
                        <a-descriptions-item label="电表">
                            <a-input-number :disabled="editType === 'detail'" v-model="house.elecMeterReading"
                                placeholder="请输入读数">
                                <template #suffix>
                                    <span>度</span>
                                </template>
                            </a-input-number>
                        </a-descriptions-item>
                        <a-descriptions-item label="冷水表">
                            <a-input-number :disabled="editType === 'detail'" v-model="house.coldWaterReading"
                                placeholder="请输入读数">
                                <template #suffix>
                                    <span>吨</span>
                                </template>
                            </a-input-number>
                        </a-descriptions-item>
                        <a-descriptions-item label="热水表">
                            <a-input-number :disabled="editType === 'detail'" v-model="house.hotWaterReading"
                                placeholder="请输入读数">
                                <template #suffix>
                                    <span>吨</span>
                                </template>
                            </a-input-number>
                        </a-descriptions-item>
                    </a-descriptions>

                    <!-- 备注 -->
                    <section-title title="备注" style="margin: 16px 0;" />
                    <a-textarea :disabled="editType === 'detail'" v-model="house.remark" placeholder="请输入备注"
                        :auto-size="{ minRows: 3, maxRows: 6 }" />
                </div>
            </a-collapse-item>
        </a-collapse>

        <!-- 底部操作 -->
        <div class="footer-actions">
            <a-space align="center" size="large">
                <a-checkbox :disabled="editType === 'detail'" v-model="sendNotice">给承租方发送进场通知单</a-checkbox>
            </a-space>
            <!-- <div class="footer-buttons">
                <a-space>
                    <a-button @click="cancel">取消</a-button>
                    <a-button type="primary" @click="handleSave">保存</a-button>
                </a-space>
            </div> -->
        </div>

        <!-- 固定资产标准库选择模态框 -->
        <a-modal v-model:visible="assetLibraryVisible" title="选择固定资产" width="800px" @ok="handleAssetLibraryConfirm"
            @cancel="handleAssetLibraryCancel">
            <div class="asset-library-content">
                <!-- 搜索框 -->
                <a-row :gutter="16" style="margin-bottom: 16px;">
                    <a-col :span="8">
                        <a-input v-model="assetSearchForm.name" placeholder="请输入物品名称" />
                    </a-col>
                    <a-col :span="8">
                        <a-select v-model="assetSearchForm.category" placeholder="请选择种类" allow-clear>
                            <a-option :value="1">家具</a-option>
                            <a-option :value="2">家电</a-option>
                            <a-option :value="3">办公用品</a-option>
                            <a-option :value="4">其他</a-option>
                        </a-select>
                    </a-col>
                    <a-col :span="8">
                        <a-button type="primary" @click="searchAssets">搜索</a-button>
                    </a-col>
                </a-row>

                <!-- 固定资产列表 -->
                <a-table :data="assetLibraryData" :columns="assetLibraryColumns" :pagination="assetPagination"
                    :loading="assetLoading" row-key="id" @page-change="handleAssetPageChange"
                    @page-size-change="handleAssetPageSizeChange">
                    <template #selection="{ record }">
                        <a-checkbox :model-value="selectedAssets.some((item: any) => item.id === record.id)"
                            @change="(checked: boolean) => handleAssetSelect(record, checked)" />
                    </template>
                    <template #category="{ record }">
                        {{ getAssetCategoryText(record.category) }}
                    </template>
                </a-table>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import dayjs from 'dayjs'
import { Message } from '@arco-design/web-vue'
import { getDictLabel } from '@/dict'
import { useDict, getDictLabel as getDictLabelUtil } from '@/utils/dict'
import { getFixedAssetsList } from '@/api/fixedAssets'

const { item_type } = useDict('item_type')

// 定义数据类型
interface HouseItem {
    id: string
    roomId: string
    roomName: string
    parcelName: string
    buildingName: string
    enterDate: string | null
    assetList: AccessoryItem[]
    elecMeterReading: number | undefined
    coldWaterReading: number | undefined
    hotWaterReading: number | undefined
    remark: string
    [key: string]: any
}

interface AccessoryItem {
    id?: string
    category: number | string | undefined
    name: string
    specification: string
    count: number
    isMissing: boolean
    isAdd: boolean
}

// 定义props和emits
const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    },
    editType: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['cancel', 'save'])

// 组件状态
const expandPosition = ref('right')
const activeKey = ref([0]) // 默认展开第一项
const houseList = ref<HouseItem[]>([])
const sendNotice = ref(true)

// 配套弹窗相关
const accessoryModalVisible = ref(false)
const accessoryFormRef = ref()
const currentHouse = ref<HouseItem>()
const accessoryForm = reactive({
    category: undefined,
    name: '',
    specification: '',
    count: 1
})

// 配套表单验证规则
const accessoryRules = {
    category: [{ required: true, message: '请选择种类' }],
    name: [{ required: true, message: '请输入物品名称' }],
    count: [{ required: true, message: '请输入数量' }]
}

// 配套列表
const accessoryColumns = [
    {
        title: '序号',
        dataIndex: 'index',
        slotName: 'index',
        width: 80,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '种类',
        dataIndex: 'type',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: { record: AccessoryItem }) => {
            return getDictLabelUtil(item_type.value, record.category ? record.category.toString() : '') || '-'
        }
    },
    {
        title: '物品名称',
        dataIndex: 'name',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '规格',
        dataIndex: 'specification',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '数量',
        dataIndex: 'count',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 监听props变化
watch(() => props.data, (newVal) => {
    if (newVal) {
        houseList.value = props.data.roomList
    }
}, { deep: true, immediate: true })


// 固定资产库相关数据
const assetLibraryVisible = ref(false)
const assetLoading = ref(false)
const assetLibraryData = ref<any[]>([])
const selectedAssets = ref<any[]>([])

// 固定资产搜索表单
const assetSearchForm = reactive<any>({
    pageNum: 1,
    pageSize: 10,
    category: undefined,
    name: ''
})

// 固定资产分页
const assetPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showPageSize: true,
    showTotal: true,
    pageSizeOptions: ['10', '20', '50', '100']
})

// 固定资产库表格列定义
const assetLibraryColumns = [
    {
        title: '选择',
        slotName: 'selection',
        width: 60,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '种类',
        slotName: 'category',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '物品名称',
        dataIndex: 'name',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '规格',
        dataIndex: 'specification',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '使用范围',
        dataIndex: 'usageScope',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '备注',
        dataIndex: 'remark',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 显示固定资产库
const showAssetLibrary = (house: HouseItem) => {
    currentHouse.value = house
    assetLibraryVisible.value = true
    selectedAssets.value = []
    loadAssetLibrary()
}

// 加载固定资产库数据
const loadAssetLibrary = async () => {
    try {
        assetLoading.value = true
        const params = {
            pageNum: assetSearchForm.pageNum,
            pageSize: assetSearchForm.pageSize,
            category: assetSearchForm.category,
            name: assetSearchForm.name
        }
        const res = await getFixedAssetsList(params)
        assetLibraryData.value = res.rows || []
        assetPagination.total = res.total || 0
        assetPagination.current = assetSearchForm.pageNum
    } catch (error) {
        // Message.error('加载固定资产库失败')
        console.error('加载固定资产库失败:', error)
    } finally {
        assetLoading.value = false
    }
}

// 搜索固定资产
const searchAssets = () => {
    assetSearchForm.pageNum = 1
    assetPagination.current = 1
    loadAssetLibrary()
}

// 固定资产分页变化
const handleAssetPageChange = (page: number) => {
    assetSearchForm.pageNum = page
    assetPagination.current = page
    loadAssetLibrary()
}

// 固定资产分页大小变化
const handleAssetPageSizeChange = (pageSize: number) => {
    assetSearchForm.pageSize = pageSize
    assetSearchForm.pageNum = 1
    assetPagination.pageSize = pageSize
    assetPagination.current = 1
    loadAssetLibrary()
}

// 选择/取消选择固定资产
const handleAssetSelect = (asset: any, checked: boolean) => {
    if (checked) {
        selectedAssets.value.push(asset)
    } else {
        const index = selectedAssets.value.findIndex(item => item.id === asset.id)
        if (index !== -1) {
            selectedAssets.value.splice(index, 1)
        }
    }
}

// 获取资产种类文本
const getAssetCategoryText = (category?: number) => {
    const categoryMap: Record<number, string> = {
        1: '家具',
        2: '家电',
        3: '办公用品',
        4: '其他'
    }
    return categoryMap[category || 1] || '未知'
}

// 确认选择固定资产
const handleAssetLibraryConfirm = () => {
    if (selectedAssets.value.length === 0) {
        Message.warning('请选择要添加的固定资产')
        return
    }
    // 判断assetList是否存在
    if (!currentHouse.value?.assetList) {
        currentHouse.value!.assetList = []
    }
    // 将选中的固定资产添加到房间资产列表
    selectedAssets.value.forEach((asset: any) => {
        const newAsset = {
            category: asset.category || 1,
            name: asset.name || '',
            specification: asset.specification || '',
            count: 1,
            isAdd: true, // 可以移除
        }
        currentHouse.value?.assetList?.push(newAsset as any)
    })

    // Message.success(`成功添加 ${selectedAssets.value.length} 个固定资产`)
    assetLibraryVisible.value = false
}

// 取消选择固定资产
const handleAssetLibraryCancel = () => {
    assetLibraryVisible.value = false
    selectedAssets.value = []
}

// 移除配套
const removeAccessory = (house: HouseItem, index: number) => {
    house.assetList.splice(index, 1)
}

// 保存
const handleSave = async () => {
    return new Promise((resolve, reject) => {
        // 验证必填项
        const invalidHouses = houseList.value.filter(house => !house.enterDate)
        if (invalidHouses.length > 0) {
            // 使用消息提示组件
            Message.warning('请填写所有房源的进场日期')
            return
        }
        const data = {
            id: props.data.id || '',
            projectId: props.data.projectId,
            contractId: props.data?.contractId,
            contractUnionId: props.data?.contractUnionId,
            isNotify: sendNotice.value,
            roomList: houseList.value.map(item => {
                return {
                    roomId: item.roomId || '',
                    roomName: item.roomName || '',
                    propertyType: item.propertyType || '',
                    parcelName: item.parcelName || '',
                    buildingName: item.buildingName || '',
                    enterDate: item.enterDate,
                    elecMeterReading: item.elecMeterReading,
                    coldWaterReading: item.coldWaterReading,
                    hotWaterReading: item.hotWaterReading,
                    remark: item.remark,
                    assetList: item.assetList?item.assetList.map(item => {
                        delete item?.id
                        return {
                            ...item
                        }
                    }):[]
                }
            })
        }
        resolve(data)
    })
}

defineExpose({
    handleSave
})
</script>

<style scoped>
.entry-process {
    width: 100%;
}

.info-section {
    margin-bottom: 24px;
}

.info-item {
    margin-bottom: 16px;
    display: flex;
}

.label {
    color: #86909c;
    margin-right: 8px;
    width: 100px;
    text-align: right;
    margin-right: 12px;
}

.value {
    color: #1d2129;
    flex: 1;
}

.property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.date {
    color: #86909c;
    font-size: 14px;
    margin-left: 16px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.footer-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e6eb;
}

.footer-buttons {
    display: flex;
    justify-content: flex-end;
}
</style>