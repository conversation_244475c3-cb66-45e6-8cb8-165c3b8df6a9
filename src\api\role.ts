import http from './index';
import type { RouteRecordNormalized } from 'vue-router';
import { UserState } from '@/store/modules/user/types';
const systemUrl = '/business-platform'

export interface LoginData {
    username: string;
    password: string;
}

export interface LoginRes {
    token: string;
}
export function login(data: LoginData) {
    return http.post<LoginRes>('/auth/login', data);
}

export function getRoleList(data: any) {
    return http.get(`${systemUrl}/role/list`, data);
}

export function getRoleUserList(data: any) {
    return http.post(`${systemUrl}/role/user/list`, data);
}
export function getRoleOrgList(data: any) {
    return http.get(`${systemUrl}/role/org/list`, data);
}

export function getRoleDetail(data: any) {
    return http.get(`${systemUrl}/role/${data.roleId}`, data);
}
export function setRoleOrgBind(data: any) {
    return http.post(`${systemUrl}/role/org/bind`, data);
}
export function saveRole(data: any) {
    return http.post(`${systemUrl}/role`, data);
}
export function deleteRole(data: any) {
    return http.delete(`${systemUrl}/role/${data.roleId}`, data);
}
export function updateRole(data: any) {
    return http.put(`${systemUrl}/role`, data);
}
// POST
// /role/menu/bind角色绑定菜单
export function bindRoleMenu(data: any) {
    return http.post(`${systemUrl}/role/menu/bind`, data);
}
// GET
// /role/menu/list
// 角色菜单
export function getRoleMenuList(data: any) {
    return http.get(`${systemUrl}/role/menu/list`, data);
}

export function getUserInfo(data: any) {
    return http.get<UserState>(`${systemUrl}/user/getInfo`);
}
export function getMenuList() {
    return http.get<RouteRecordNormalized[]>(`/business-platform/menu/list`);
}

export function getUserList(data: any) {
    return http.post<RouteRecordNormalized[]>(`${systemUrl}/user/list`, data);
}

//POST
// /role/user/bind
// 选择用户授权角色
export function bindRoleUser(data: any) {
    return http.post(`${systemUrl}/role/user/bind`, data);
}
// POST
// /role/user/cancel/bind
// 取消授权用户
export function cancelRoleUser(data: any) {
    return http.post(`${systemUrl}/role/user/cancel/bind`, data);
}