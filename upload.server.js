'use strict'
// h5
// ************
// /app/vanyang/h5
//账号密码都是root/YZ-it418

const SFTPClient = require('ssh2-sftp-client')
const https = require('https')
const { execSync } = require('child_process')
const sftp = new SFTPClient()

const config = {
    host: '************',
    port: '22',
    username: 'root',
    password: 'YZ-it418'
}

const localFile = './dist'  // 本地路径
const remotePath = '/app/vanyang/h5' // 远程路径

// 通知接口配置
const notifyUrl = 'https://xizhi.qqoq.net/XZ5d1694e7f56984a4e97c95146f1a0a63.channel'

// 获取今天的Git提交信息
function getTodayGitCommits() {
    try {
        // 获取当前分支名
        const branchName = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim()
        
        // 获取今天的日期（格式：YYYY-MM-DD）
        const today = new Date().toISOString().split('T')[0]
        
        // 获取今天的所有提交记录，格式：哈希|作者|时间|消息
        const gitLogCommand = `git log --since="${today} 00:00:00" --until="${today} 23:59:59" --pretty=format:"%h|%an|%cd|%s" --date=format:"%H:%M:%S"`
        const commitLogs = execSync(gitLogCommand, { encoding: 'utf8' }).trim()
        
        if (!commitLogs) {
            return {
                branch: branchName,
                commits: [],
                totalCount: 0
            }
        }
        
        // 解析提交记录
        const commits = commitLogs.split('\n').map(line => {
            const [hash, author, time, message] = line.split('|')
            return {
                hash: hash,
                author: author,
                time: time,
                message: message
            }
        })
        
        return {
            branch: branchName,
            commits: commits,
            totalCount: commits.length
        }
    } catch (error) {
        console.warn('获取Git信息失败:', error.message)
        return {
            branch: 'unknown',
            commits: [],
            totalCount: 0
        }
    }
}

// 发送通知
async function sendNotification(title, content = '') {
    return new Promise((resolve, reject) => {
        const params = new URLSearchParams({
            title: title,
            content: content
        })
        
        const url = `${notifyUrl}?${params.toString()}`
        
        https.get(url, (res) => {
            let data = ''
            res.on('data', (chunk) => {
                data += chunk
            })
            res.on('end', () => {
                console.log('通知发送成功:', data)
                resolve(data)
            })
        }).on('error', (err) => {
            console.error('通知发送失败:', err)
            reject(err)
        })
    })
}

async function upload() {
    console.log('正在上传到到服务器...')
    try {
        await sftp.connect(config);
        await sftp.uploadDir(localFile, remotePath); // 上传整个目录
        console.log('上传成功！')
        
        // 上传成功后发送通知
        const currentTime = new Date().toLocaleString('zh-CN')
        const gitInfo = getTodayGitCommits()
        
        let commitsText = ''
        if (gitInfo.totalCount === 0) {
            commitsText = '📝 今日提交：无提交记录'
        } else {
            commitsText = `📝 今日提交 (${gitInfo.totalCount} 条)：\n`
            gitInfo.commits.forEach((commit, index) => {
                commitsText += `${index + 1}. [${commit.time}] ${commit.message} - ${commit.author} (${commit.hash})\n`
            })
        }
        
        const successContent = `🎉 项目已成功部署到测试服务器 ${config.host}
📅 部署时间：${currentTime}
🌿 分支：${gitInfo.branch}
🌍 环境：测试环境
${commitsText}`
        
        await sendNotification('H5项目测试环境部署成功', successContent)
        
    } catch (err) {
        console.error('上传失败:', err)
        
        // 上传失败也发送通知
        try {
            const gitInfo = getTodayGitCommits()
            
            let commitsText = ''
            if (gitInfo.totalCount === 0) {
                commitsText = '📝 今日提交：无提交记录'
            } else {
                commitsText = `📝 今日提交 (${gitInfo.totalCount} 条)：\n`
                // 只显示前3条提交记录，避免内容过长
                const displayCommits = gitInfo.commits.slice(0, 3)
                displayCommits.forEach((commit, index) => {
                    commitsText += `${index + 1}. [${commit.time}] ${commit.message} - ${commit.author} (${commit.hash})\n`
                })
                if (gitInfo.totalCount > 3) {
                    commitsText += `... 还有 ${gitInfo.totalCount - 3} 条提交`
                }
            }
            
            const failContent = `❌ 项目部署到测试服务器 ${config.host} 失败
🌿 分支：${gitInfo.branch}
🌍 环境：测试环境
${commitsText}
⚠️ 错误信息：${err.message}`
            
            await sendNotification('H5项目测试环境部署失败', failContent)
        } catch (notifyErr) {
            console.error('发送失败通知时出错:', notifyErr)
        }
    } finally {
        sftp.end()
    }
}
upload().then(r => {})