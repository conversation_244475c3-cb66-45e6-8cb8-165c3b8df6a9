import http from '../index'

// 查询资产处置列表
export function getDisposalList(params: any) {
  return http.get('/business-asset/disposal/list',  params )
}

// 获取资产处置详细信息
export function getDisposalDetail(id: string) {
  return http.get('/business-asset/disposal/detail?id=' + id)
}

// 新增资产处置
export function addDisposal(data: any) {
  return http.post('/business-asset/disposal/add', data)
}

// 修改资产处置
export function updateDisposal(data: any) {
  return http.put('/business-asset/disposal/edit', data)
}

// 删除资产处置
export function deleteDisposal(id: string) {
  return http.delete('/business-asset/disposal/delete?id=' + id)
}

// 查询房间树形结构
export function getRoomTree(data: any) {
  return http.post('/business-asset/project/room/tree', data)
}

// 查询房间列表
export function getRoomList(params: any) {
  return http.get('/business-asset/project/room/list',  params )
}

// 查询项目列表
export function getProjectList(params: any) {
  console.log('调用getProjectList API，参数:', params)
  
  return http.get('/business-asset/project/list', { params })
    .then(response => {
      console.log('getProjectList API 响应成功:', response)
      
      // 详细分析返回的数据结构
      if (response) {
        console.log('响应类型:', typeof response)
        if (response.data) {
          console.log('响应data类型:', typeof response.data)
          console.log('响应data是否为数组:', Array.isArray(response.data))
          if (Array.isArray(response.data) && response.data.length > 0) {
            console.log('data数组第一项:', response.data[0])
            console.log('data数组长度:', response.data.length)
          }
        } else {
          console.log('响应中没有data字段，检查整个响应体是否为数组')
          console.log('响应是否为数组:', Array.isArray(response))
          if (Array.isArray(response) && response.length > 0) {
            console.log('响应数组第一项:', response[0])
            console.log('响应数组长度:', response.length)
          }
        }
      }
      
      return response
    })
    .catch(error => {
      console.error('getProjectList API 请求失败:', error)
      throw error
    })
}

// 根据项目ID获取楼栋树结构
export function getBuildingTree(projectId: string, buildingName?: string) {
  // 构建查询参数对象
  const params: Record<string, any> = { projectId }
  
  // 只有在有值时才添加buildingName参数
  if (buildingName) {
    params.buildingName = buildingName
  }
  
  console.log('调用楼栋树API，参数:', params)
  
  return http.get('/business-asset/project/building/tree',  params)
    .then(response => {
      console.log('楼栋树API响应成功，状态码:', response?.code)
      
      // 调试响应数据
      if (response) {
        if (response.data) {
          console.log('楼栋树data类型:', typeof response.data)
          if (Array.isArray(response.data)) {
            console.log('楼栋树data是数组，长度:', response.data.length)
            if (response.data.length > 0) {
              console.log('第一个地块数据:', response.data[0])
              
              // 检查是否有楼栋数据
              const firstItem = response.data[0]
              if (firstItem.buildings && Array.isArray(firstItem.buildings)) {
                console.log('第一个地块的楼栋数量:', firstItem.buildings.length)
              } else {
                console.log('第一个地块没有楼栋数据或楼栋不是数组')
              }
            }
          } else {
            console.log('楼栋树data不是数组，而是:', response.data)
          }
        } else {
          console.log('楼栋树API响应中没有data字段')
        }
      }
      
      return response
    })
    .catch(error => {
      console.error('楼栋树API请求失败:', error)
      throw error
    })
}

// 提交审批
export function submitOA(data: any) {
  return http.post('/business-asset/disposal/submitOA?id='+data)
}

// 资产处置相关的类型定义
export interface AssetDisposalVo {
  id?: string
  projectId?: string
  disposalCode?: string
  disposalMethod?: string
  disposalBuilding?: string
  disposalRoomCount?: number
  disposalDate?: string
  disposalDescription?: string
  approvalStatus?: string
  approvalTime?: string
  remark?: string
  attachment?: string
  createByName?: string
  updateByName?: string
  isDel?: boolean
}

export interface AssetDisposalAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  projectId?: string
  disposalCode?: string
  disposalMethod?: string
  disposalBuilding?: string
  disposalRoomCount?: number
  disposalDate?: string
  disposalDescription?: string
  approvalStatus?: string
  approvalTime?: string
  remark?: string
  attachment?: string
  roomIds?: string[]
  operationType?: number
}

export interface AssetDisposalDetailVo {
  disposalInfo?: AssetDisposalVo
  roomList?: any[]
}
