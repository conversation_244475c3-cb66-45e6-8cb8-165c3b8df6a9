<template>
  <a-modal
    v-model:visible="modalVisible"
    title="固定资产详情"
    width="800px"
    :footer="false"
  >
    <a-descriptions 
      :column="2" 
      bordered
      :label-style="{ width: '120px' }"
      v-if="detailData"
    >
      <a-descriptions-item label="种类">
        {{ getCategoryText(detailData.category) }}
      </a-descriptions-item>
      
      <a-descriptions-item label="物品名称">
        {{ detailData.name }}
      </a-descriptions-item>
      
      <a-descriptions-item label="规格">
        {{ detailData.specification || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="使用范围">
        {{ getUsageScopeText(detailData.usageScope) }}
      </a-descriptions-item>
      
      <a-descriptions-item label="创建人">
        {{ detailData.createByName || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="更新人">
        {{ detailData.updateByName || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="物品描述" :span="2">
        {{ detailData.remark || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="物品图片" :span="2" v-if="imageList.length > 0">
        <div class="image-gallery">
          <a-image
            v-for="(image, index) in imageList"
            :key="index"
            :src="image"
            :width="100"
            :height="100"
            fit="cover"
            :preview="true"
            :preview-props="{
              src: image,
              actionsLayout: ['zoomIn', 'zoomOut', 'rotateRight', 'rotateLeft']
            }"
            class="gallery-image"
          />
        </div>
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { FixedAssetsVo } from '@/api/fixedAssets'

interface Props {
  visible: boolean
  detailData?: FixedAssetsVo | null
  usageScopeOptions?: any[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const imageList = ref<string[]>([])

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 种类映射
const getCategoryText = (category?: number) => {
  if (!category) return '-'
  const categoryMap: Record<number, string> = {
    1: '家电',
    2: '家具', 
    3: '装饰',
    4: '其他'
  }
  return categoryMap[category] || '未知'
}

// 使用范围映射
const getUsageScopeText = (usageScope?: string) => {
  if (!usageScope) return '-'
  
  // 处理多选情况
  const scopes = usageScope.split(',')
  const scopeLabels: string[] = []
  
  // 判断是否为叶子节点
  const isLeafNode = (node: any) => {
    return !node.childList || node.childList.length === 0
  }
  
  // 在字典数据中查找对应的文本
  const findOption = (options: any[], value: string): any => {
    if (!options || !options.length) return null
    
    for (const option of options) {
      if (option.dictValue === value) {
        return option
      }
      if (option.childList && option.childList.length > 0) {
        const childResult = findOption(option.childList, value)
        if (childResult) return childResult
      }
    }
    return null
  }
  
  // 只显示选中的叶子节点
  scopes.forEach(scope => {
    const option = findOption(props.usageScopeOptions || [], scope)
    if (option) {
      // 如果是叶子节点或没有子节点，则添加到显示列表
      if (isLeafNode(option)) {
        scopeLabels.push(option.dictLabel)
      }
    } else {
      scopeLabels.push(scope) // 如果没找到，至少显示原始值
    }
  })
  
  return scopeLabels.join('、') || '-'
}

// 监听详情数据变化，解析图片
watch(() => props.detailData, (newData) => {
  if (newData?.attachments) {
    try {
      imageList.value = JSON.parse(newData.attachments)
    } catch (error) {
      console.error('解析图片失败:', error)
      imageList.value = []
    }
  } else {
    imageList.value = []
  }
}, { immediate: true })
</script>

<style scoped lang="less">
.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.gallery-image {
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
  
  &:hover {
    transform: scale(1.05);
  }
}
</style> 