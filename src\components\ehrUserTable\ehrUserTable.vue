<template>
    <div class="data-roles-list">
        <div class="data-roles-list-search">
            <a-input-search v-model="searchValue" placeholder="请输入内容" @search="handleSearch" @press-enter="handleSearch"/>
        </div>
        <div class="data-roles-list-content">
            <div class="data-roles-list-content-list">
                <a-table row-key="id" :columns="columns" :data="list" :row-selection="rowSelection" v-model:selectedKeys="selectedKeys" :pagination="pagination" @page-change="handlePageChange" @selection-change="handleSelectionChange" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, nextTick, reactive } from 'vue'
import { getEhrUserList } from '@/api/ehr'

const selectedKeys = ref<any[]>([])

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const pagination = {pageSize: 10, total: 0}

const searchValue = ref('')
const list = ref<any[]>([])


//序号
// 人员名称
// OA账号
// 手机号
// 所属部门
// 状态
// 选择
// {
//             "id": "-1000202716475404311",
//             "loginName": "luobing1",
//             "name": "骆兵",
//             "tel": "13027291731",
//             "userCode": "202504165",
//             "status": "1",
//             "state": 6,
//             "buName": "温岭招商五组",
//             "buId": "4118255552823903870",
//             "postName": null,
//             "userId": null
//         },
const columns = ref<any[]>([
    // {
    //     title: '序号',
    //     dataIndex: 'index',
    // },
    {
        title: '人员名称',
        dataIndex: 'name',
    },
    {
        title: 'OA账号',
        dataIndex: 'loginName',
    },
    {
        title: '手机号',
        dataIndex: 'tel',
    },
    {
        title: '所属部门',
        dataIndex: 'buName',
    },
    {
        title: '状态',
        dataIndex: 'status',
        render: (text: string) => {
            return text === '1' ? '禁用' : '启用'
        }
    },
])

const emit = defineEmits(['change'])
const handleSelectionChange = (selectedRows: any[]) => {
    // console.log(selectedRows)
    // selectedRows.value = selectedRows
    // 还需要返回name
    // const selectedRowsWithName = selectedRows.map((row: any) => ({
    //     ...row,
    //     name: row.name,
    // }))
    let selectedRowsWithName: any[] = []
    list.value.forEach((item: any) => {
        selectedRows.forEach((selectedRow: any) => {
            if (selectedRow.includes(item.id)) {
                selectedRowsWithName.push({
                    name: item.name
                })
            }
        })
    })
    emit('change', selectedRows, selectedRowsWithName)
}
const handleSearch = (value: string) => {
    console.log(searchValue.value)
    fetchData(1, 10)
}
const handlePageChange = (page: number, pageSize: number) => {
    console.log(page, pageSize)
    fetchData(page, pageSize, searchValue.value)
}



const fetchData = async (page: number, pageSize: number) => {
    console.log(searchValue.value)
    const res:any = await getEhrUserList({
        pageNum: page,
        pageSize: pageSize,
        keyword: searchValue.value,
    })
    // console.log(rows, total)
    list.value = res.rows as any[]
    pagination.total = res.total
}

fetchData(1, 10)
</script>
<script lang="ts">
    export default { name: 'orgTree' }
</script>

<style lang="less" scoped>
    .data-roles-list {
        width: 100%;
        height: 100%;
        background: #fff;
        border-radius: 4px;
        padding: 12px;
        box-sizing: border-box;
        .data-roles-list-header {
            justify-content: space-between;
            .data-roles-list-header-title {
                display: flex;
                align-items: center;
            }
            .data-roles-list-header-right {
                display: flex;
                align-items: center;
                color: rgb(var(--arcoblue-6));
                cursor: pointer;
                .data-roles-list-header-right-button {
                    padding: 0 !important;
                }
            }
        }
        .data-roles-list-search {
            margin-top: 12px;
        }
        .data-roles-list-content {
            margin-top: 12px;

            .data-roles-list-content-list {
                .data-roles-list-content-item {
                    padding: 12px;
                    border-bottom: 1px solid #f0f0f0;
                    cursor: pointer;
                    justify-content: space-between;
                    &:hover {
                        background: var(--color-fill-2);
                    }
                    .data-roles-list-content-item-right-handle {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        cursor: pointer;
                    }
                }
                .data-roles-list-content-item-on {
                    background: rgb(var(--arcoblue-6));
                    color: #fff;
                    &:hover {
                        background: rgb(var(--arcoblue-6));
                    }
                }
            }
        }
    }
</style>
