import { defineStore } from 'pinia';
import {
    login as userLogin,
    logout as userLogout,
    getUserInfo,
    LoginData,
} from '@/api/user';
import { getToken, setToken, clearToken } from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import { isHttp, isEmpty } from '@/utils/validate';
import defAva from '@/assets/images/user/avatar.png';
import { useTabBarStore } from '@/store';
import useCenterStore from '@/store/modules/center';

interface UserState {
    token: string | null;
    id: string;
    name: string;
    nickName: string;
    avatar: string;
    roles: string[];
    permissions: string[];
}
const useUserStore = defineStore('user', {
    state: (): UserState => ({
        token: getToken(),
        id: '',
        name: '',
        nickName: '',
        avatar: '',
        roles: [],
        permissions: [],
    }),

    getters: {
        // userInfo(state: UserState): UserState {
        //     return { ...state };
        // },
    },

    actions: {
        switchRoles() {
            // return new Promise((resolve) => {
            //     this.role = this.role === 'user' ? 'admin' : 'user';
            //     resolve(this.role);
            // });
        },
        // Set user's information
        setInfo(partial: Partial<UserState>) {
            this.$patch(partial);
        },

        // Reset user's information
        resetInfo() {
            this.$reset();
        },

        // Get user's information
        async getInfo() {
            return new Promise(async (resolve, reject) => {
                const res = await getUserInfo();
                useCenterStore().setCenterOptions(res.systemPermissions)
                this.permissions = res.permissions;
                const user = res.user;
                let avatar = user.avatar || '';
                if (!isHttp(avatar)) {
                    avatar = isEmpty(avatar) ? defAva : avatar;
                }
                if (res.roles && res.roles.length > 0) {
                    // 验证返回的roles是否是一个非空数组
                    this.roles = res.roles;
                } else {
                    this.roles = ['ROLE_DEFAULT'];
                }
                this.id = user.userId;
                this.name = user.userName;
                this.nickName = user.nickName;
                this.avatar = avatar;
                resolve(res);
            });
        },

        // Login
        async login(loginForm: LoginData) {
            try {
                const res = await userLogin(loginForm);
                useCenterStore().isInit = false;
                setToken(res.data.access_token);
                this.token = res.data.access_token;
            } catch (err) {
                clearToken();
                throw err;
            }
        },
        logoutCallBack() {
            this.resetInfo();
            clearToken();
            removeRouteListener();
            useTabBarStore().resetTabList();
        },
        // Logout
        async logout() {
            try {
                await userLogout();
            } finally {
                this.logoutCallBack();
            }
        },
    },
});

export default useUserStore;
