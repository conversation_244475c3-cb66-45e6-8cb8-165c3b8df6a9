<template>
    <section>
        <sectionTitle title="其他费用"></sectionTitle>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-row justify="end" style="margin-bottom: 16px;" v-if="!isDetailMode">
                <a-space>
                    <a-button type="primary" @click="handleAdd">添加账单</a-button>
                </a-space>
            </a-row>
            <a-table :columns="columns" :scroll="{ x: 1400 }" :data="tableData" :pagination="false"
                :bordered="{ cell: true }">
                <template #columns>
                    <a-table-column title="租期" data-index="rentDate" :width="180" align="center">
                        <template #cell="{ record }">
                            {{ dayjs(record.startDate).format('YYYY-MM-DD') }} 至 {{
                                dayjs(record.endDate).format('YYYY-MM-DD') }}
                        </template>
                    </a-table-column>
                    <a-table-column title="商户" data-index="customerName" :width="120" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="费项" data-index="subjectName" :width="120" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="应收日期" data-index="receivableDate" :width="120" align="center" />
                    <a-table-column title="账单总额（元）" data-index="totalAmount" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.totalAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="优惠金额（元）" data-index="discountAmount" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.discountAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单应收金额（元）" data-index="actualReceivable" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable) }}
                        </template>
                    </a-table-column>
                    <!-- <a-table-column title="账单已收金额（元）" data-index="receivedAmount" :width="160" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.receivedAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单剩余应收金额（元）" data-index="remainingAmount" :width="180" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable - record.receivedAmount) }}
                        </template>
                    </a-table-column> -->
                    <a-table-column v-if="!isDetailMode" title="操作" data-index="action" :width="100" align="center"
                        fixed="right">
                        <template #cell="{ record, rowIndex }">
                            <a-button type="text" size="mini" @click="handleEdit(record, rowIndex)">编辑</a-button>
                            <a-button type="text" size="mini" @click="handleDelete(record)">删除</a-button>
                        </template>
                    </a-table-column>
                </template>
            </a-table>
            <a-modal v-model:visible="editModalVisible" :title="editModalTitle" :on-before-ok="handleEditBeforeOk"
                @ok="handleEditOk" @cancel="handleEditCancel">
                <a-form ref="editFormRef" :model="editForm" :rules="editFormRules" auto-label-width>
                    <a-form-item label="租期" field="rentDate">
                        <a-range-picker v-model="editForm.rentDate" format="YYYY-MM-DD" />
                    </a-form-item>
                    <a-form-item label="商户" field="customerName">
                        <a-input disabled v-model="editForm.customerName" style="width: 100%" placeholder="请输入商户" />
                    </a-form-item>
                    <a-form-item label="费项" field="subjectName">
                        <a-select v-model="editForm.subjectId" style="width: 100%" placeholder="请输入费项"
                            @change="handleSubjectChange">
                            <a-option v-for="item in costTypeAndTaxRate" :key="item.value" :value="item.value"
                                :label="item.label"></a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="应收日期" field="receivableDate">
                        <a-date-picker v-model="editForm.receivableDate" style="width: 100%" placeholder="请选择日期"
                            format="YYYY-MM-DD" />
                    </a-form-item>
                    <a-form-item label="账单总额（元）" field="totalAmount">
                        <a-input-number v-model="editForm.totalAmount" style="width: 100%" placeholder="请输入账单总额" />
                    </a-form-item>
                    <a-form-item label="优惠金额（元）" field="discountAmount">
                        <a-input-number disabled v-model="editForm.discountAmount" style="width: 100%" placeholder="请输入优惠金额" />
                    </a-form-item>
                    <!-- <a-form-item label="账单应收金额（元）" field="actualReceivable">
                        <a-input-number v-model="editForm.actualReceivable" style="width: 100%"
                            placeholder="请输入账单应收金额" />
                    </a-form-item> -->
                    <!-- <a-form-item label="账单已收金额（元）" field="receivedAmount">
                        <a-input-number v-model="editForm.receivedAmount" style="width: 100%"
                            placeholder="请输入账单已收金额" />
                    </a-form-item> -->
                </a-form>
            </a-modal>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { FormInstance } from '@arco-design/web-vue';
import { useContractStore } from '@/store/modules/contract'
import { ContractCostDTO } from '@/api/contract';
import dayjs from 'dayjs';
import { getDict } from '@/dict';

const costTypeAndTaxRate = computed(() => {
    const dict = getDict('cost_type_and_tax_rate')
    return dict.filter((item: any) => item.value > 30)
})

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const formatter = (value: string) => {
    const values = value.split('.');
    values[0] = values[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return values.join('.')
};

const parser = (value: string) => {
    return value.replace(/,/g, '')
};

const unitMap = new Map([
    [1, '元/平方米/月'],
    [2, '元/月']
])

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractCosts)
const contractData = computed(() => contractStore.contractData)
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || (editType.value === 'change' && !changeType.value.includes(3)))

const tableData = ref<any[]>([])
const columns = [
    {
        title: '租期',
        dataIndex: 'field1',
        width: 100,
    },
    {
        title: '商户',
        dataIndex: 'field2',
        width: 100,
    },
    {
        title: '费项',
        dataIndex: 'field3',
        width: 100,
    },
    {
        title: '应收日期',
        dataIndex: 'field5',
        width: 100,
    },
    {
        title: '账单总额（元）',
        dataIndex: 'field6',
        width: 130,
    },
    {
        title: '优惠金额（元）',
        dataIndex: 'field7',
        width: 130,
    },
    {
        title: '账单应收金额（元）',
        dataIndex: 'field8',
        width: 160,
    },
    // {
    //     title: '账单已收金额（元）',
    //     dataIndex: 'field9',
    //     width: 160,
    // },
    // {
    //     title: '账单剩余应收金额（元）',
    //     dataIndex: 'field10',
    //     width: 180,
    // },
    {
        title: '操作',
        dataIndex: 'field11',
        width: 100,
        fixed: 'right',
    }
]

const editModalVisible = ref(false)
const editModalTitle = ref('')
const editFormRef = ref<FormInstance>()
const editForm = ref<any>({
    costType: 3,
    rentDate: [],
    customerName: '',
    subjectName: '',
    receivableDate: '',
    totalAmount: 0,
    discountAmount: 0,
    actualReceivable: 0,
    receivedAmount: 0,
})
const editFormRules = ref({
    rentDate: [{ required: true, message: '请选择日期' }],
    subjectName: [{ required: true, message: '请输入费项' }],
    receivableDate: [{ required: true, message: '请选择日期' }],
    totalAmount: [{ required: true, message: '请输入账单总额' }],
    discountAmount: [{ required: true, message: '请输入优惠金额' }],
    actualReceivable: [{ required: true, message: '请输入账单应收金额' }],
    receivedAmount: [{ required: true, message: '请输入账单已收金额' }],
})

const handleEditBeforeOk = async () => {
    const errors = await editFormRef.value?.validate()
    if (errors) return false
    return true
}

const handleEditOk = async () => {
    editModalVisible.value = false
    const data = JSON.parse(JSON.stringify(editForm.value))
    data.startDate = data.rentDate[0]
    data.endDate = data.rentDate[1]
    data.actualReceivable = data.totalAmount - data.discountAmount
    delete data.rentDate
    if (editModalTitle.value === '添加账单') {
        contractCosts.value.costs?.push(data)
    } else {
        contractCosts.value.costs = contractCosts.value.costs?.map(item => {
            if (item.costType === data.costType && item.period === data.period) {
                return data
            }
            return item
        })
    }
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
}

const handleEditCancel = () => {
    editModalVisible.value = false
}

const handleAdd = () => {
    editModalVisible.value = true
    let costData
    if (contractCosts.value.costs && contractCosts.value.costs.length > 0) {
        costData = contractCosts.value.costs[0]
    }
    editForm.value = {
        costType: 3,
        rentDate: [],
        customerName: costData?.customerName ?? '',
        customerId: costData?.customerId ?? '',
        subjectName: '',
        receivableDate: '',
        totalAmount: 0,
        discountAmount: 0,
        actualReceivable: 0,
        receivedAmount: 0,
        period: tableData.value.length + 1
    }
    editModalTitle.value = '添加账单'
}

const handleEdit = (record: any, index: number) => {
    editModalVisible.value = true
    editModalTitle.value = '编辑账单'
    const data = JSON.parse(JSON.stringify(record))
    data.rentDate = [data.startDate, data.endDate]
    editForm.value = data
}
const handleDelete = (record: any) => {
    contractCosts.value.costs = contractCosts.value.costs?.filter(item => {
        return item.costType !== 3 || (item.costType === 3 && item.period !== record.period)
    })
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
}

const handleSubjectChange = (value: string) => {
    const subject = costTypeAndTaxRate.value.find((item: any) => item.value === Number(value))
    if (subject) {
        editForm.value.subjectName = subject.label
    }
}

watch(contractCosts, (newVal) => {
    if (newVal.costs && newVal.costs.length > 0) {
        const otherCosts = newVal.costs.filter(item => item.costType === 3)
        tableData.value = otherCosts
    }
}, { deep: true, immediate: true })
</script>

<style lang="less" scoped></style>