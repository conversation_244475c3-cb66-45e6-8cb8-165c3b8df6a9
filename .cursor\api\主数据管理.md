---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# token 控制

## POST login

POST /auth/login

> Body 请求参数

```json
{
  "username": "string",
  "password": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[LoginBody](#schemaloginbody)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[R«?»](#schemar«?»)|

## DELETE logout

DELETE /auth/logout

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|characterEncoding|query|string| 否 |none|
|attribute|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[R«?»](#schemar«?»)|

## POST refresh

POST /refresh

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|characterEncoding|query|string| 否 |none|
|attribute|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[R«?»](#schemar«?»)|

## POST register

POST /register

> Body 请求参数

```json
{
  "username": "string",
  "password": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RegisterBody](#schemaregisterbody)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[R«?»](#schemar«?»)|

## GET 根据免登陆授权码实现免登陆

GET /getDingTalkToken

1.前端调用钉钉接口获取到code，传入此接口
2.该接口实现

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|code|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[R](#schemar)|

# 代码生成 操作处理

## GET 查询代码生成列表

GET /gen/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tableId|query|string| 否 |编号|
|tableName|query|string| 是 |表名称|
|tableComment|query|string| 是 |表描述|
|subTableName|query|string| 否 |关联父表的表名|
|subTableFkName|query|string| 否 |本表关联父表的外键名|
|className|query|string| 是 |实体类名称(首字母大写)|
|tplCategory|query|string| 否 |使用的模板（crud单表操作 tree树表操作 sub主子表操作）|
|tplWebType|query|string| 否 |前端类型（element-ui模版 element-plus模版）|
|packageName|query|string| 是 |生成包路径|
|moduleName|query|string| 是 |生成模块名|
|businessName|query|string| 是 |生成业务名|
|functionName|query|string| 是 |生成功能名|
|functionAuthor|query|string| 是 |生成作者|
|genType|query|string| 否 |生成代码方式（0zip压缩包 1自定义路径）|
|genPath|query|string| 否 |生成路径（不填默认项目路径）|
|pkColumn.columnId|query|string| 否 |编号|
|pkColumn.tableId|query|string| 否 |归属表编号|
|pkColumn.columnName|query|string| 否 |列名称|
|pkColumn.columnComment|query|string| 否 |列描述|
|pkColumn.columnType|query|string| 否 |列类型|
|pkColumn.javaType|query|string| 否 |JAVA类型|
|pkColumn.javaField|query|string| 是 |JAVA字段名|
|pkColumn.isPk|query|string| 否 |是否主键（1是）|
|pkColumn.isIncrement|query|string| 否 |是否自增（1是）|
|pkColumn.isRequired|query|string| 否 |是否必填（1是）|
|pkColumn.isInsert|query|string| 否 |是否为插入字段（1是）|
|pkColumn.isEdit|query|string| 否 |是否编辑字段（1是）|
|pkColumn.isList|query|string| 否 |是否列表字段（1是）|
|pkColumn.isQuery|query|string| 否 |是否查询字段（1是）|
|pkColumn.queryType|query|string| 否 |查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）|
|pkColumn.htmlType|query|string| 否 |显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）|
|pkColumn.dictType|query|string| 否 |字典类型|
|pkColumn.sort|query|string| 否 |排序|
|pkColumn.createBy|query|string| 否 |创建者|
|pkColumn.createByName|query|string| 否 |none|
|pkColumn.createTime|query|string| 否 |创建时间|
|pkColumn.updateBy|query|string| 否 |更新者|
|pkColumn.updateByName|query|string| 否 |none|
|pkColumn.updateTime|query|string| 否 |更新时间|
|pkColumn.params.key.key|query|string| 否 |none|
|subTable.tableId|query|string| 否 |编号|
|subTable.tableName|query|string| 是 |表名称|
|subTable.tableComment|query|string| 是 |表描述|
|subTable.subTableName|query|string| 否 |关联父表的表名|
|subTable.subTableFkName|query|string| 否 |本表关联父表的外键名|
|subTable.className|query|string| 是 |实体类名称(首字母大写)|
|subTable.tplCategory|query|string| 否 |使用的模板（crud单表操作 tree树表操作 sub主子表操作）|
|subTable.tplWebType|query|string| 否 |前端类型（element-ui模版 element-plus模版）|
|subTable.packageName|query|string| 是 |生成包路径|
|subTable.moduleName|query|string| 是 |生成模块名|
|subTable.businessName|query|string| 是 |生成业务名|
|subTable.functionName|query|string| 是 |生成功能名|
|subTable.functionAuthor|query|string| 是 |生成作者|
|subTable.genType|query|string| 否 |生成代码方式（0zip压缩包 1自定义路径）|
|subTable.genPath|query|string| 否 |生成路径（不填默认项目路径）|
|subTable.pkColumn.columnId|query|string| 否 |编号|
|subTable.pkColumn.tableId|query|string| 否 |归属表编号|
|subTable.pkColumn.columnName|query|string| 否 |列名称|
|subTable.pkColumn.columnComment|query|string| 否 |列描述|
|subTable.pkColumn.columnType|query|string| 否 |列类型|
|subTable.pkColumn.javaType|query|string| 否 |JAVA类型|
|subTable.pkColumn.javaField|query|string| 是 |JAVA字段名|
|subTable.pkColumn.isPk|query|string| 否 |是否主键（1是）|
|subTable.pkColumn.isIncrement|query|string| 否 |是否自增（1是）|
|subTable.pkColumn.isRequired|query|string| 否 |是否必填（1是）|
|subTable.pkColumn.isInsert|query|string| 否 |是否为插入字段（1是）|
|subTable.pkColumn.isEdit|query|string| 否 |是否编辑字段（1是）|
|subTable.pkColumn.isList|query|string| 否 |是否列表字段（1是）|
|subTable.pkColumn.isQuery|query|string| 否 |是否查询字段（1是）|
|subTable.pkColumn.queryType|query|string| 否 |查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）|
|subTable.pkColumn.htmlType|query|string| 否 |显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）|
|subTable.pkColumn.dictType|query|string| 否 |字典类型|
|subTable.pkColumn.sort|query|string| 否 |排序|
|subTable.pkColumn.createBy|query|string| 否 |创建者|
|subTable.pkColumn.createByName|query|string| 否 |none|
|subTable.pkColumn.createTime|query|string| 否 |创建时间|
|subTable.pkColumn.updateBy|query|string| 否 |更新者|
|subTable.pkColumn.updateByName|query|string| 否 |none|
|subTable.pkColumn.updateTime|query|string| 否 |更新时间|
|subTable.pkColumn.params.key.key|query|string| 否 |none|
|subTable.subTable.key|query|string| 否 |none|
|subTable.columns[0].columnId|query|string| 否 |编号|
|subTable.columns[0].tableId|query|string| 否 |归属表编号|
|subTable.columns[0].columnName|query|string| 否 |列名称|
|subTable.columns[0].columnComment|query|string| 否 |列描述|
|subTable.columns[0].columnType|query|string| 否 |列类型|
|subTable.columns[0].javaType|query|string| 否 |JAVA类型|
|subTable.columns[0].javaField|query|string| 是 |JAVA字段名|
|subTable.columns[0].isPk|query|string| 否 |是否主键（1是）|
|subTable.columns[0].isIncrement|query|string| 否 |是否自增（1是）|
|subTable.columns[0].isRequired|query|string| 否 |是否必填（1是）|
|subTable.columns[0].isInsert|query|string| 否 |是否为插入字段（1是）|
|subTable.columns[0].isEdit|query|string| 否 |是否编辑字段（1是）|
|subTable.columns[0].isList|query|string| 否 |是否列表字段（1是）|
|subTable.columns[0].isQuery|query|string| 否 |是否查询字段（1是）|
|subTable.columns[0].queryType|query|string| 否 |查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）|
|subTable.columns[0].htmlType|query|string| 否 |显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）|
|subTable.columns[0].dictType|query|string| 否 |字典类型|
|subTable.columns[0].sort|query|string| 否 |排序|
|subTable.columns[0].createBy|query|string| 否 |创建者|
|subTable.columns[0].createByName|query|string| 否 |none|
|subTable.columns[0].createTime|query|string| 否 |创建时间|
|subTable.columns[0].updateBy|query|string| 否 |更新者|
|subTable.columns[0].updateByName|query|string| 否 |none|
|subTable.columns[0].updateTime|query|string| 否 |更新时间|
|subTable.columns[0].params.key.key|query|string| 否 |none|
|subTable.options|query|string| 否 |其它生成选项|
|subTable.treeCode|query|string| 否 |树编码字段|
|subTable.treeParentCode|query|string| 否 |树父编码字段|
|subTable.treeName|query|string| 否 |树名称字段|
|subTable.parentMenuId|query|string| 否 |上级菜单ID字段|
|subTable.parentMenuName|query|string| 否 |上级菜单名称字段|
|subTable.remark|query|string| 否 |none|
|subTable.createBy|query|string| 否 |创建者|
|subTable.createByName|query|string| 否 |none|
|subTable.createTime|query|string| 否 |创建时间|
|subTable.updateBy|query|string| 否 |更新者|
|subTable.updateByName|query|string| 否 |none|
|subTable.updateTime|query|string| 否 |更新时间|
|subTable.params.key.key|query|string| 否 |none|
|columns[0].columnId|query|string| 否 |编号|
|columns[0].tableId|query|string| 否 |归属表编号|
|columns[0].columnName|query|string| 否 |列名称|
|columns[0].columnComment|query|string| 否 |列描述|
|columns[0].columnType|query|string| 否 |列类型|
|columns[0].javaType|query|string| 否 |JAVA类型|
|columns[0].javaField|query|string| 是 |JAVA字段名|
|columns[0].isPk|query|string| 否 |是否主键（1是）|
|columns[0].isIncrement|query|string| 否 |是否自增（1是）|
|columns[0].isRequired|query|string| 否 |是否必填（1是）|
|columns[0].isInsert|query|string| 否 |是否为插入字段（1是）|
|columns[0].isEdit|query|string| 否 |是否编辑字段（1是）|
|columns[0].isList|query|string| 否 |是否列表字段（1是）|
|columns[0].isQuery|query|string| 否 |是否查询字段（1是）|
|columns[0].queryType|query|string| 否 |查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）|
|columns[0].htmlType|query|string| 否 |显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）|
|columns[0].dictType|query|string| 否 |字典类型|
|columns[0].sort|query|string| 否 |排序|
|columns[0].createBy|query|string| 否 |创建者|
|columns[0].createByName|query|string| 否 |none|
|columns[0].createTime|query|string| 否 |创建时间|
|columns[0].updateBy|query|string| 否 |更新者|
|columns[0].updateByName|query|string| 否 |none|
|columns[0].updateTime|query|string| 否 |更新时间|
|columns[0].params.key.key|query|string| 否 |none|
|options|query|string| 否 |其它生成选项|
|treeCode|query|string| 否 |树编码字段|
|treeParentCode|query|string| 否 |树父编码字段|
|treeName|query|string| 否 |树名称字段|
|parentMenuId|query|string| 否 |上级菜单ID字段|
|parentMenuName|query|string| 否 |上级菜单名称字段|
|remark|query|string| 否 |none|
|createBy|query|string| 否 |创建者|
|createByName|query|string| 否 |none|
|createTime|query|string| 否 |创建时间|
|updateBy|query|string| 否 |更新者|
|updateByName|query|string| 否 |none|
|updateTime|query|string| 否 |更新时间|
|params.key.key|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [],
  "code": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[TableDataInfo](#schematabledatainfo)|

## GET 获取代码生成信息

GET /gen/{tableId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tableId|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## GET 查询数据库列表

GET /gen/db/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tableId|query|string| 否 |编号|
|tableName|query|string| 是 |表名称|
|tableComment|query|string| 是 |表描述|
|subTableName|query|string| 否 |关联父表的表名|
|subTableFkName|query|string| 否 |本表关联父表的外键名|
|className|query|string| 是 |实体类名称(首字母大写)|
|tplCategory|query|string| 否 |使用的模板（crud单表操作 tree树表操作 sub主子表操作）|
|tplWebType|query|string| 否 |前端类型（element-ui模版 element-plus模版）|
|packageName|query|string| 是 |生成包路径|
|moduleName|query|string| 是 |生成模块名|
|businessName|query|string| 是 |生成业务名|
|functionName|query|string| 是 |生成功能名|
|functionAuthor|query|string| 是 |生成作者|
|genType|query|string| 否 |生成代码方式（0zip压缩包 1自定义路径）|
|genPath|query|string| 否 |生成路径（不填默认项目路径）|
|pkColumn.columnId|query|string| 否 |编号|
|pkColumn.tableId|query|string| 否 |归属表编号|
|pkColumn.columnName|query|string| 否 |列名称|
|pkColumn.columnComment|query|string| 否 |列描述|
|pkColumn.columnType|query|string| 否 |列类型|
|pkColumn.javaType|query|string| 否 |JAVA类型|
|pkColumn.javaField|query|string| 是 |JAVA字段名|
|pkColumn.isPk|query|string| 否 |是否主键（1是）|
|pkColumn.isIncrement|query|string| 否 |是否自增（1是）|
|pkColumn.isRequired|query|string| 否 |是否必填（1是）|
|pkColumn.isInsert|query|string| 否 |是否为插入字段（1是）|
|pkColumn.isEdit|query|string| 否 |是否编辑字段（1是）|
|pkColumn.isList|query|string| 否 |是否列表字段（1是）|
|pkColumn.isQuery|query|string| 否 |是否查询字段（1是）|
|pkColumn.queryType|query|string| 否 |查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）|
|pkColumn.htmlType|query|string| 否 |显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）|
|pkColumn.dictType|query|string| 否 |字典类型|
|pkColumn.sort|query|string| 否 |排序|
|pkColumn.createBy|query|string| 否 |创建者|
|pkColumn.createByName|query|string| 否 |none|
|pkColumn.createTime|query|string| 否 |创建时间|
|pkColumn.updateBy|query|string| 否 |更新者|
|pkColumn.updateByName|query|string| 否 |none|
|pkColumn.updateTime|query|string| 否 |更新时间|
|pkColumn.params.key.key|query|string| 否 |none|
|subTable.tableId|query|string| 否 |编号|
|subTable.tableName|query|string| 是 |表名称|
|subTable.tableComment|query|string| 是 |表描述|
|subTable.subTableName|query|string| 否 |关联父表的表名|
|subTable.subTableFkName|query|string| 否 |本表关联父表的外键名|
|subTable.className|query|string| 是 |实体类名称(首字母大写)|
|subTable.tplCategory|query|string| 否 |使用的模板（crud单表操作 tree树表操作 sub主子表操作）|
|subTable.tplWebType|query|string| 否 |前端类型（element-ui模版 element-plus模版）|
|subTable.packageName|query|string| 是 |生成包路径|
|subTable.moduleName|query|string| 是 |生成模块名|
|subTable.businessName|query|string| 是 |生成业务名|
|subTable.functionName|query|string| 是 |生成功能名|
|subTable.functionAuthor|query|string| 是 |生成作者|
|subTable.genType|query|string| 否 |生成代码方式（0zip压缩包 1自定义路径）|
|subTable.genPath|query|string| 否 |生成路径（不填默认项目路径）|
|subTable.pkColumn.columnId|query|string| 否 |编号|
|subTable.pkColumn.tableId|query|string| 否 |归属表编号|
|subTable.pkColumn.columnName|query|string| 否 |列名称|
|subTable.pkColumn.columnComment|query|string| 否 |列描述|
|subTable.pkColumn.columnType|query|string| 否 |列类型|
|subTable.pkColumn.javaType|query|string| 否 |JAVA类型|
|subTable.pkColumn.javaField|query|string| 是 |JAVA字段名|
|subTable.pkColumn.isPk|query|string| 否 |是否主键（1是）|
|subTable.pkColumn.isIncrement|query|string| 否 |是否自增（1是）|
|subTable.pkColumn.isRequired|query|string| 否 |是否必填（1是）|
|subTable.pkColumn.isInsert|query|string| 否 |是否为插入字段（1是）|
|subTable.pkColumn.isEdit|query|string| 否 |是否编辑字段（1是）|
|subTable.pkColumn.isList|query|string| 否 |是否列表字段（1是）|
|subTable.pkColumn.isQuery|query|string| 否 |是否查询字段（1是）|
|subTable.pkColumn.queryType|query|string| 否 |查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）|
|subTable.pkColumn.htmlType|query|string| 否 |显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）|
|subTable.pkColumn.dictType|query|string| 否 |字典类型|
|subTable.pkColumn.sort|query|string| 否 |排序|
|subTable.pkColumn.createBy|query|string| 否 |创建者|
|subTable.pkColumn.createByName|query|string| 否 |none|
|subTable.pkColumn.createTime|query|string| 否 |创建时间|
|subTable.pkColumn.updateBy|query|string| 否 |更新者|
|subTable.pkColumn.updateByName|query|string| 否 |none|
|subTable.pkColumn.updateTime|query|string| 否 |更新时间|
|subTable.pkColumn.params.key.key|query|string| 否 |none|
|subTable.subTable.key|query|string| 否 |none|
|subTable.columns[0].columnId|query|string| 否 |编号|
|subTable.columns[0].tableId|query|string| 否 |归属表编号|
|subTable.columns[0].columnName|query|string| 否 |列名称|
|subTable.columns[0].columnComment|query|string| 否 |列描述|
|subTable.columns[0].columnType|query|string| 否 |列类型|
|subTable.columns[0].javaType|query|string| 否 |JAVA类型|
|subTable.columns[0].javaField|query|string| 是 |JAVA字段名|
|subTable.columns[0].isPk|query|string| 否 |是否主键（1是）|
|subTable.columns[0].isIncrement|query|string| 否 |是否自增（1是）|
|subTable.columns[0].isRequired|query|string| 否 |是否必填（1是）|
|subTable.columns[0].isInsert|query|string| 否 |是否为插入字段（1是）|
|subTable.columns[0].isEdit|query|string| 否 |是否编辑字段（1是）|
|subTable.columns[0].isList|query|string| 否 |是否列表字段（1是）|
|subTable.columns[0].isQuery|query|string| 否 |是否查询字段（1是）|
|subTable.columns[0].queryType|query|string| 否 |查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）|
|subTable.columns[0].htmlType|query|string| 否 |显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）|
|subTable.columns[0].dictType|query|string| 否 |字典类型|
|subTable.columns[0].sort|query|string| 否 |排序|
|subTable.columns[0].createBy|query|string| 否 |创建者|
|subTable.columns[0].createByName|query|string| 否 |none|
|subTable.columns[0].createTime|query|string| 否 |创建时间|
|subTable.columns[0].updateBy|query|string| 否 |更新者|
|subTable.columns[0].updateByName|query|string| 否 |none|
|subTable.columns[0].updateTime|query|string| 否 |更新时间|
|subTable.columns[0].params.key.key|query|string| 否 |none|
|subTable.options|query|string| 否 |其它生成选项|
|subTable.treeCode|query|string| 否 |树编码字段|
|subTable.treeParentCode|query|string| 否 |树父编码字段|
|subTable.treeName|query|string| 否 |树名称字段|
|subTable.parentMenuId|query|string| 否 |上级菜单ID字段|
|subTable.parentMenuName|query|string| 否 |上级菜单名称字段|
|subTable.remark|query|string| 否 |none|
|subTable.createBy|query|string| 否 |创建者|
|subTable.createByName|query|string| 否 |none|
|subTable.createTime|query|string| 否 |创建时间|
|subTable.updateBy|query|string| 否 |更新者|
|subTable.updateByName|query|string| 否 |none|
|subTable.updateTime|query|string| 否 |更新时间|
|subTable.params.key.key|query|string| 否 |none|
|columns[0].columnId|query|string| 否 |编号|
|columns[0].tableId|query|string| 否 |归属表编号|
|columns[0].columnName|query|string| 否 |列名称|
|columns[0].columnComment|query|string| 否 |列描述|
|columns[0].columnType|query|string| 否 |列类型|
|columns[0].javaType|query|string| 否 |JAVA类型|
|columns[0].javaField|query|string| 是 |JAVA字段名|
|columns[0].isPk|query|string| 否 |是否主键（1是）|
|columns[0].isIncrement|query|string| 否 |是否自增（1是）|
|columns[0].isRequired|query|string| 否 |是否必填（1是）|
|columns[0].isInsert|query|string| 否 |是否为插入字段（1是）|
|columns[0].isEdit|query|string| 否 |是否编辑字段（1是）|
|columns[0].isList|query|string| 否 |是否列表字段（1是）|
|columns[0].isQuery|query|string| 否 |是否查询字段（1是）|
|columns[0].queryType|query|string| 否 |查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）|
|columns[0].htmlType|query|string| 否 |显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）|
|columns[0].dictType|query|string| 否 |字典类型|
|columns[0].sort|query|string| 否 |排序|
|columns[0].createBy|query|string| 否 |创建者|
|columns[0].createByName|query|string| 否 |none|
|columns[0].createTime|query|string| 否 |创建时间|
|columns[0].updateBy|query|string| 否 |更新者|
|columns[0].updateByName|query|string| 否 |none|
|columns[0].updateTime|query|string| 否 |更新时间|
|columns[0].params.key.key|query|string| 否 |none|
|options|query|string| 否 |其它生成选项|
|treeCode|query|string| 否 |树编码字段|
|treeParentCode|query|string| 否 |树父编码字段|
|treeName|query|string| 否 |树名称字段|
|parentMenuId|query|string| 否 |上级菜单ID字段|
|parentMenuName|query|string| 否 |上级菜单名称字段|
|remark|query|string| 否 |none|
|createBy|query|string| 否 |创建者|
|createByName|query|string| 否 |none|
|createTime|query|string| 否 |创建时间|
|updateBy|query|string| 否 |更新者|
|updateByName|query|string| 否 |none|
|updateTime|query|string| 否 |更新时间|
|params.key.key|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [],
  "code": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[TableDataInfo](#schematabledatainfo)|

## GET 查询数据表字段列表

GET /gen/column/{tableId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tableId|query|integer| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [],
  "code": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[TableDataInfo](#schematabledatainfo)|

## POST 导入表结构（保存）

POST /gen/importTable

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tables|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## PUT 修改保存代码生成业务

PUT /gen

> Body 请求参数

```json
{
  "tableId": 0,
  "tableName": "string",
  "tableComment": "string",
  "subTableName": "string",
  "subTableFkName": "string",
  "className": "string",
  "tplCategory": "string",
  "tplWebType": "string",
  "packageName": "string",
  "moduleName": "string",
  "businessName": "string",
  "functionName": "string",
  "functionAuthor": "string",
  "genType": "string",
  "genPath": "string",
  "pkColumn": {
    "columnId": 0,
    "tableId": 0,
    "columnName": "string",
    "columnComment": "string",
    "columnType": "string",
    "javaType": "string",
    "javaField": "string",
    "isPk": "string",
    "isIncrement": "string",
    "isRequired": "string",
    "isInsert": "string",
    "isEdit": "string",
    "isList": "string",
    "isQuery": "string",
    "queryType": "string",
    "htmlType": "string",
    "dictType": "string",
    "sort": 0,
    "createBy": "string",
    "createByName": "string",
    "createTime": "string",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "string",
    "params": {
      "key": {}
    }
  },
  "subTable": {
    "tableId": 0,
    "tableName": "string",
    "tableComment": "string",
    "subTableName": "string",
    "subTableFkName": "string",
    "className": "string",
    "tplCategory": "string",
    "tplWebType": "string",
    "packageName": "string",
    "moduleName": "string",
    "businessName": "string",
    "functionName": "string",
    "functionAuthor": "string",
    "genType": "string",
    "genPath": "string",
    "pkColumn": {
      "columnId": 0,
      "tableId": 0,
      "columnName": "string",
      "columnComment": "string",
      "columnType": "string",
      "javaType": "string",
      "javaField": "string",
      "isPk": "string",
      "isIncrement": "string",
      "isRequired": "string",
      "isInsert": "string",
      "isEdit": "string",
      "isList": "string",
      "isQuery": "string",
      "queryType": "string",
      "htmlType": "string",
      "dictType": "string",
      "sort": 0,
      "createBy": "string",
      "createByName": "string",
      "createTime": "string",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "string",
      "params": {
        "key": {}
      }
    },
    "subTable": {
      "tableId": 0,
      "tableName": "string",
      "tableComment": "string",
      "subTableName": "string",
      "subTableFkName": "string",
      "className": "string",
      "tplCategory": "string",
      "tplWebType": "string",
      "packageName": "string",
      "moduleName": "string",
      "businessName": "string",
      "functionName": "string",
      "functionAuthor": "string",
      "genType": "string",
      "genPath": "string",
      "pkColumn": {
        "columnId": 0,
        "tableId": 0,
        "columnName": "string",
        "columnComment": "string",
        "columnType": "string",
        "javaType": "string",
        "javaField": "string",
        "isPk": "string",
        "isIncrement": "string",
        "isRequired": "string",
        "isInsert": "string",
        "isEdit": "string",
        "isList": "string",
        "isQuery": "string",
        "queryType": "string",
        "htmlType": "string",
        "dictType": "string",
        "sort": 0,
        "createBy": "string",
        "createByName": "string",
        "createTime": "string",
        "updateBy": "string",
        "updateByName": "string",
        "updateTime": "string",
        "params": {
          "key": null
        }
      },
      "subTable": {
        "tableId": 0,
        "tableName": "string",
        "tableComment": "string",
        "subTableName": "string",
        "subTableFkName": "string",
        "className": "string",
        "tplCategory": "string",
        "tplWebType": "string",
        "packageName": "string",
        "moduleName": "string",
        "businessName": "string",
        "functionName": "string",
        "functionAuthor": "string",
        "genType": "string",
        "genPath": "string",
        "pkColumn": {
          "columnId": null,
          "tableId": null,
          "columnName": null,
          "columnComment": null,
          "columnType": null,
          "javaType": null,
          "javaField": null,
          "isPk": null,
          "isIncrement": null,
          "isRequired": null,
          "isInsert": null,
          "isEdit": null,
          "isList": null,
          "isQuery": null,
          "queryType": null,
          "htmlType": null,
          "dictType": null,
          "sort": null,
          "createBy": null,
          "createByName": null,
          "createTime": null,
          "updateBy": null,
          "updateByName": null,
          "updateTime": null,
          "params": null
        },
        "subTable": {
          "tableId": null,
          "tableName": null,
          "tableComment": null,
          "subTableName": null,
          "subTableFkName": null,
          "className": null,
          "tplCategory": null,
          "tplWebType": null,
          "packageName": null,
          "moduleName": null,
          "businessName": null,
          "functionName": null,
          "functionAuthor": null,
          "genType": null,
          "genPath": null,
          "pkColumn": null,
          "subTable": null,
          "columns": null,
          "options": null,
          "treeCode": null,
          "treeParentCode": null,
          "treeName": null,
          "parentMenuId": null,
          "parentMenuName": null,
          "remark": null,
          "createBy": null,
          "createByName": null,
          "createTime": null,
          "updateBy": null,
          "updateByName": null,
          "updateTime": null,
          "params": null
        },
        "columns": [
          {}
        ],
        "options": "string",
        "treeCode": "string",
        "treeParentCode": "string",
        "treeName": "string",
        "parentMenuId": 0,
        "parentMenuName": "string",
        "remark": "string",
        "createBy": "string",
        "createByName": "string",
        "createTime": "string",
        "updateBy": "string",
        "updateByName": "string",
        "updateTime": "string",
        "params": {
          "key": null
        }
      },
      "columns": [
        {
          "columnId": 0,
          "tableId": 0,
          "columnName": "string",
          "columnComment": "string",
          "columnType": "string",
          "javaType": "string",
          "javaField": "string",
          "isPk": "string",
          "isIncrement": "string",
          "isRequired": "string",
          "isInsert": "string",
          "isEdit": "string",
          "isList": "string",
          "isQuery": "string",
          "queryType": "string",
          "htmlType": "string",
          "dictType": "string",
          "sort": 0,
          "createBy": "string",
          "createByName": "string",
          "createTime": "string",
          "updateBy": "string",
          "updateByName": "string",
          "updateTime": "string",
          "params": {}
        }
      ],
      "options": "string",
      "treeCode": "string",
      "treeParentCode": "string",
      "treeName": "string",
      "parentMenuId": 0,
      "parentMenuName": "string",
      "remark": "string",
      "createBy": "string",
      "createByName": "string",
      "createTime": "string",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "string",
      "params": {
        "key": {}
      }
    },
    "columns": [
      {
        "columnId": 0,
        "tableId": 0,
        "columnName": "string",
        "columnComment": "string",
        "columnType": "string",
        "javaType": "string",
        "javaField": "string",
        "isPk": "string",
        "isIncrement": "string",
        "isRequired": "string",
        "isInsert": "string",
        "isEdit": "string",
        "isList": "string",
        "isQuery": "string",
        "queryType": "string",
        "htmlType": "string",
        "dictType": "string",
        "sort": 0,
        "createBy": "string",
        "createByName": "string",
        "createTime": "string",
        "updateBy": "string",
        "updateByName": "string",
        "updateTime": "string",
        "params": {
          "key": {}
        }
      }
    ],
    "options": "string",
    "treeCode": "string",
    "treeParentCode": "string",
    "treeName": "string",
    "parentMenuId": 0,
    "parentMenuName": "string",
    "remark": "string",
    "createBy": "string",
    "createByName": "string",
    "createTime": "string",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "string",
    "params": {
      "key": {}
    }
  },
  "columns": [
    {
      "columnId": 0,
      "tableId": 0,
      "columnName": "string",
      "columnComment": "string",
      "columnType": "string",
      "javaType": "string",
      "javaField": "string",
      "isPk": "string",
      "isIncrement": "string",
      "isRequired": "string",
      "isInsert": "string",
      "isEdit": "string",
      "isList": "string",
      "isQuery": "string",
      "queryType": "string",
      "htmlType": "string",
      "dictType": "string",
      "sort": 0,
      "createBy": "string",
      "createByName": "string",
      "createTime": "string",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "string",
      "params": {
        "key": {}
      }
    }
  ],
  "options": "string",
  "treeCode": "string",
  "treeParentCode": "string",
  "treeName": "string",
  "parentMenuId": 0,
  "parentMenuName": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string",
  "params": {
    "key": {}
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[GenTable](#schemagentable)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## DELETE 删除代码生成

DELETE /gen/{tableIds}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tableIds|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## GET 预览代码

GET /gen/preview/{tableId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tableId|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## GET 生成代码（下载方式）

GET /gen/download/{tableName}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tableName|path|string| 是 |none|
|dateHeader|query|string| 否 |none|
|header|query|string| 否 |none|
|intHeader|query|string| 否 |none|
|status|query|string| 否 |none|
|trailerFields.key|query|string| 否 |none|
|characterEncoding|query|string| 否 |none|
|contentLength|query|string| 否 |none|
|contentLengthLong|query|string| 否 |none|
|contentType|query|string| 否 |none|
|bufferSize|query|string| 否 |none|
|locale.defaultLocale.defaultLocale.key|query|string| 否 |none|
|locale.defaultLocale.defaultDisplayLocale.key|query|string| 否 |none|
|locale.defaultLocale.defaultFormatLocale.key|query|string| 否 |none|
|locale.defaultLocale.isoLanguages[0]|query|string| 否 |none|
|locale.defaultLocale.isoCountries[0]|query|string| 否 |none|
|locale.defaultDisplayLocale.defaultLocale.key|query|string| 否 |none|
|locale.defaultDisplayLocale.defaultDisplayLocale.key|query|string| 否 |none|
|locale.defaultDisplayLocale.defaultFormatLocale.key|query|string| 否 |none|
|locale.defaultDisplayLocale.isoLanguages[0]|query|string| 否 |none|
|locale.defaultDisplayLocale.isoCountries[0]|query|string| 否 |none|
|locale.defaultFormatLocale.defaultLocale.key|query|string| 否 |none|
|locale.defaultFormatLocale.defaultDisplayLocale.key|query|string| 否 |none|
|locale.defaultFormatLocale.defaultFormatLocale.key|query|string| 否 |none|
|locale.defaultFormatLocale.isoLanguages[0]|query|string| 否 |none|
|locale.defaultFormatLocale.isoCountries[0]|query|string| 否 |none|
|locale.isoLanguages[0]|query|string| 否 |none|
|locale.isoCountries[0]|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 生成代码（自定义路径）

GET /gen/genCode/{tableName}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tableName|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## GET 同步数据库

GET /gen/synchDb/{tableName}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tableName|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## GET 批量生成代码

GET /code/gen/batchGenCode

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|dateHeader|query|string| 否 |none|
|header|query|string| 否 |none|
|intHeader|query|string| 否 |none|
|status|query|string| 否 |none|
|trailerFields.key|query|string| 否 |none|
|characterEncoding|query|string| 否 |none|
|contentLength|query|string| 否 |none|
|contentLengthLong|query|string| 否 |none|
|contentType|query|string| 否 |none|
|bufferSize|query|string| 否 |none|
|locale.defaultLocale.defaultLocale.key|query|string| 否 |none|
|locale.defaultLocale.defaultDisplayLocale.key|query|string| 否 |none|
|locale.defaultLocale.defaultFormatLocale.key|query|string| 否 |none|
|locale.defaultLocale.isoLanguages[0]|query|string| 否 |none|
|locale.defaultLocale.isoCountries[0]|query|string| 否 |none|
|locale.defaultDisplayLocale.defaultLocale.key|query|string| 否 |none|
|locale.defaultDisplayLocale.defaultDisplayLocale.key|query|string| 否 |none|
|locale.defaultDisplayLocale.defaultFormatLocale.key|query|string| 否 |none|
|locale.defaultDisplayLocale.isoLanguages[0]|query|string| 否 |none|
|locale.defaultDisplayLocale.isoCountries[0]|query|string| 否 |none|
|locale.defaultFormatLocale.defaultLocale.key|query|string| 否 |none|
|locale.defaultFormatLocale.defaultDisplayLocale.key|query|string| 否 |none|
|locale.defaultFormatLocale.defaultFormatLocale.key|query|string| 否 |none|
|locale.defaultFormatLocale.isoLanguages[0]|query|string| 否 |none|
|locale.defaultFormatLocale.isoCountries[0]|query|string| 否 |none|
|locale.isoLanguages[0]|query|string| 否 |none|
|locale.isoCountries[0]|query|string| 否 |none|
|tables|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 调度任务信息操作处理

## GET 查询定时任务列表

GET /quartz/job/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|jobId|query|string| 否 |任务ID|
|jobName|query|string| 否 |任务名称|
|jobGroup|query|string| 否 |任务组名|
|invokeTarget|query|string| 否 |调用目标字符串|
|cronExpression|query|string| 否 |cron执行表达式|
|misfirePolicy|query|string| 否 |cron计划策略|
|concurrent|query|string| 否 |是否并发执行（0允许 1禁止）|
|status|query|string| 否 |任务状态（0正常 1暂停）|
|remark|query|string| 否 |none|
|createBy|query|string| 否 |创建者|
|createByName|query|string| 否 |none|
|createTime|query|string| 否 |创建时间|
|updateBy|query|string| 否 |更新者|
|updateByName|query|string| 否 |none|
|updateTime|query|string| 否 |更新时间|
|params.key.key|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [],
  "code": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[TableDataInfo](#schematabledatainfo)|

## POST 导出定时任务列表

POST /job/export

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|dateHeader|query|string| 否 |none|
|header|query|string| 否 |none|
|intHeader|query|string| 否 |none|
|status|query|array[string]| 否 |none|
|trailerFields.key|query|string| 否 |none|
|characterEncoding|query|string| 否 |none|
|contentLength|query|string| 否 |none|
|contentLengthLong|query|string| 否 |none|
|contentType|query|string| 否 |none|
|bufferSize|query|string| 否 |none|
|locale.defaultLocale.defaultLocale.key|query|string| 否 |none|
|locale.defaultLocale.defaultDisplayLocale.key|query|string| 否 |none|
|locale.defaultLocale.defaultFormatLocale.key|query|string| 否 |none|
|locale.defaultLocale.isoLanguages[0]|query|string| 否 |none|
|locale.defaultLocale.isoCountries[0]|query|string| 否 |none|
|locale.defaultDisplayLocale.defaultLocale.key|query|string| 否 |none|
|locale.defaultDisplayLocale.defaultDisplayLocale.key|query|string| 否 |none|
|locale.defaultDisplayLocale.defaultFormatLocale.key|query|string| 否 |none|
|locale.defaultDisplayLocale.isoLanguages[0]|query|string| 否 |none|
|locale.defaultDisplayLocale.isoCountries[0]|query|string| 否 |none|
|locale.defaultFormatLocale.defaultLocale.key|query|string| 否 |none|
|locale.defaultFormatLocale.defaultDisplayLocale.key|query|string| 否 |none|
|locale.defaultFormatLocale.defaultFormatLocale.key|query|string| 否 |none|
|locale.defaultFormatLocale.isoLanguages[0]|query|string| 否 |none|
|locale.defaultFormatLocale.isoCountries[0]|query|string| 否 |none|
|locale.isoLanguages[0]|query|string| 否 |none|
|locale.isoCountries[0]|query|string| 否 |none|
|jobId|query|string| 否 |任务ID|
|jobName|query|string| 否 |任务名称|
|jobGroup|query|string| 否 |任务组名|
|invokeTarget|query|string| 否 |调用目标字符串|
|cronExpression|query|string| 否 |cron执行表达式|
|misfirePolicy|query|string| 否 |cron计划策略|
|concurrent|query|string| 否 |是否并发执行（0允许 1禁止）|
|remark|query|string| 否 |none|
|createBy|query|string| 否 |创建者|
|createByName|query|string| 否 |none|
|createTime|query|string| 否 |创建时间|
|updateBy|query|string| 否 |更新者|
|updateByName|query|string| 否 |none|
|updateTime|query|string| 否 |更新时间|
|params.key.key|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获取定时任务详细信息

GET /job/{jobId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|jobId|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## POST 新增定时任务

POST /job

> Body 请求参数

```json
{
  "jobId": 0,
  "jobName": "string",
  "jobGroup": "string",
  "invokeTarget": "string",
  "cronExpression": "string",
  "misfirePolicy": "0",
  "concurrent": "string",
  "status": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string",
  "params": {
    "key": {}
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysJob](#schemasysjob)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## PUT 修改定时任务

PUT /job

> Body 请求参数

```json
{
  "jobId": 0,
  "jobName": "string",
  "jobGroup": "string",
  "invokeTarget": "string",
  "cronExpression": "string",
  "misfirePolicy": "0",
  "concurrent": "string",
  "status": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string",
  "params": {
    "key": {}
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysJob](#schemasysjob)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## PUT 定时任务状态修改

PUT /job/changeStatus

> Body 请求参数

```json
{
  "jobId": 0,
  "jobName": "string",
  "jobGroup": "string",
  "invokeTarget": "string",
  "cronExpression": "string",
  "misfirePolicy": "0",
  "concurrent": "string",
  "status": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string",
  "params": {
    "key": {}
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysJob](#schemasysjob)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## PUT 定时任务立即执行一次

PUT /job/run

> Body 请求参数

```json
{
  "jobId": 0,
  "jobName": "string",
  "jobGroup": "string",
  "invokeTarget": "string",
  "cronExpression": "string",
  "misfirePolicy": "0",
  "concurrent": "string",
  "status": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string",
  "params": {
    "key": {}
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysJob](#schemasysjob)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

## DELETE 删除定时任务

DELETE /job/{jobIds}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|jobIds|path|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

# 资产中心/资产接收管理

<a id="opIdedit"></a>

## PUT 修改资产接收

PUT /receive

修改资产接收信息，包括暂存(1)或提交(2)操作

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "receiveCode": "string",
  "receiveType": "string",
  "receiveBuilding": "string",
  "receiveRoomCount": 0,
  "receiveDate": "2019-08-24T14:15:22Z",
  "receiveReason": "string",
  "approvalStatus": 0,
  "approvalTime": "2019-08-24T14:15:22Z",
  "remark": "string",
  "attachment": "string",
  "isDel": true,
  "roomIds": [
    "string"
  ],
  "operationType": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AssetReceiveAddDTO](#schemaassetreceiveadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|操作结果|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd"></a>

## POST 新增资产接收

POST /receive

新增资产接收信息，包括暂存(1)或提交(2)操作

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "receiveCode": "string",
  "receiveType": "string",
  "receiveBuilding": "string",
  "receiveRoomCount": 0,
  "receiveDate": "2019-08-24T14:15:22Z",
  "receiveReason": "string",
  "approvalStatus": 0,
  "approvalTime": "2019-08-24T14:15:22Z",
  "remark": "string",
  "attachment": "string",
  "isDel": true,
  "roomIds": [
    "string"
  ],
  "operationType": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AssetReceiveAddDTO](#schemaassetreceiveadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|操作结果|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist"></a>

## POST 查询资产接收列表

POST /receive/list

根据项目ID、楼栋名称和审批状态查询资产接收列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "receiveCode": "string",
  "receiveType": "string",
  "receiveBuilding": "string",
  "receiveRoomCount": 0,
  "receiveDate": "2019-08-24T14:15:22Z",
  "receiveReason": "string",
  "approvalStatus": 0,
  "approvalTime": "2019-08-24T14:15:22Z",
  "remark": "string",
  "attachment": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AssetReceiveQueryDTO](#schemaassetreceivequerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","receiveCode":"string","receiveType":"string","receiveBuilding":"string","receiveRoomCount":0,"receiveDate":"2019-08-24T14:15:22Z","receiveReason":"string","approvalStatus":0,"approvalTime":"2019-08-24T14:15:22Z","remark":"string","attachment":"string","createByName":"string","updateByName":"string","isDel":true,"roomIds":["string"],"rooms":[{"id":"string","mdmRoomId":"string","projectId":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomName":"string","productType":"string","areaType":"string","areaTypeName":"string","buildArea":0,"innerArea":0,"isSale":0,"isCompanySelf":0,"propertyType":"string","propertyTypeName":"string","selfHoldingTime":"2019-08-24T14:15:22Z","status":"string","receiveId":"string","changeId":"string","disposalId":"string","createByName":"string","updateByName":"string","isDel":0}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[AssetReceiveVo](#schemaassetreceivevo)|

<a id="opIdgetInfo"></a>

## GET 获取资产接收详细信息

GET /receive/detail

根据资产接收ID获取详细信息，包含房间基础信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |资产接收ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","receiveCode":"string","receiveType":"string","receiveBuilding":"string","receiveRoomCount":0,"receiveDate":"2019-08-24T14:15:22Z","receiveReason":"string","approvalStatus":0,"approvalTime":"2019-08-24T14:15:22Z","remark":"string","attachment":"string","createByName":"string","updateByName":"string","isDel":true,"roomIds":["string"],"rooms":[{"id":"string","mdmRoomId":"string","projectId":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomName":"string","productType":"string","areaType":"string","areaTypeName":"string","buildArea":0,"innerArea":0,"isSale":0,"isCompanySelf":0,"propertyType":"string","propertyTypeName":"string","selfHoldingTime":"2019-08-24T14:15:22Z","status":"string","receiveId":"string","changeId":"string","disposalId":"string","createByName":"string","updateByName":"string","isDel":0}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[AssetReceiveVo](#schemaassetreceivevo)|

<a id="opIdremove"></a>

## DELETE 删除资产接收

DELETE /receive/delete

根据ID列表删除资产接收信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[string]| 是 |资产接收ID列表|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|操作结果|[AjaxResult](#schemaajaxresult)|

# 资产中心/项目

<a id="opIdgetRoomTree"></a>

## POST 查询房间树形结构

POST /project/room/tree

查询房间树形结构，按楼栋和楼层分组

> Body 请求参数

```json
{
  "buildingIds": [
    "string"
  ],
  "isSelf": "string",
  "productType": "string",
  "roomName": "string",
  "type": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysRoomSimpleDTO](#schemasysroomsimpledto)| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","buildingName":"string","parcelId":"string","parcelName":"string","floors":[{"id":"string","floorName":"string","rooms":[{"id":"string","roomName":"string","receiveId":"string","disposalId":"string","floorId":"string","floorName":"string","buildingId":"string","buildingName":"string"}]}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysBuildingSimpleVo](#schemasysbuildingsimplevo)]|false|none||none|
|» 楼栋列表|[SysBuildingSimpleVo](#schemasysbuildingsimplevo)|false|none|楼栋列表|none|
|»» id|string|false|none|楼栋ID|none|
|»» buildingName|string|false|none|楼栋名称|none|
|»» parcelId|string|false|none|地块ID|none|
|»» parcelName|string|false|none|地块名称|none|
|»» floors|[[SysFloorSimpleVo](#schemasysfloorsimplevo)]|false|none|楼层列表|none|
|»»» 楼层列表|[SysFloorSimpleVo](#schemasysfloorsimplevo)|false|none|楼层列表|none|
|»»»» id|string|false|none|楼层ID|none|
|»»»» floorName|string|false|none|楼层名称|none|
|»»»» rooms|[[SysRoomSimpleVo](#schemasysroomsimplevo)]|false|none|房间列表|none|
|»»»»» 房间列表|[SysRoomSimpleVo](#schemasysroomsimplevo)|false|none|房间列表|none|
|»»»»»» id|string|false|none|房间ID|none|
|»»»»»» roomName|string|false|none|房间名称|none|
|»»»»»» receiveId|string|false|none|接收ID|none|
|»»»»»» disposalId|string|false|none|处置ID|none|
|»»»»»» floorId|string|false|none|楼层ID|none|
|»»»»»» floorName|string|false|none|楼层名称|none|
|»»»»»» buildingId|string|false|none|楼栋ID|none|
|»»»»»» buildingName|string|false|none|楼栋名称|none|

<a id="opIdsaveRoom"></a>

## POST 保存房间信息

POST /project/room/save

保存房间信息，有id就更新，没有id就新增

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmRoomId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomName": "string",
  "productType": "string",
  "areaType": "string",
  "areaTypeName": "string",
  "buildArea": 0,
  "innerArea": 0,
  "isSelf": "string",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysRoomAddDTO](#schemasysroomadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdsaveBuildingInfo"></a>

## POST 保存楼栋信息

POST /project/building/save

保存楼栋信息，包括楼层信息的生成

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "source": "string",
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysBuildingAddDTO](#schemasysbuildingadddto)| 否 | 楼栋列表|none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdbasicSave"></a>

## POST 保存项目

POST /project/basic/save

保存项目

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "type": "string",
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": "string",
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "isDel": 0,
  "stageList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmStageId": "string",
      "projectId": "string",
      "stageName": "string",
      "isDel": 0
    }
  ],
  "parcelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmParcelId": "string",
      "projectId": "string",
      "stageId": "string",
      "stageName": "string",
      "parcelName": "string",
      "mdmNatureName": "string",
      "landUsage": 0,
      "mdmAddress": "string",
      "address": "string",
      "totalSelfArea": "string",
      "isDel": 0
    }
  ],
  "buildingList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "source": "string",
      "mdmBuildingId": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingName": "string",
      "upFloorNums": 0,
      "underFloorNums": 0,
      "totalSelfArea": "string",
      "isDel": 0
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysProjectAddDTO](#schemasysprojectadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddownloadTemplate"></a>

## GET 下载房间导入模板

GET /project/room/template/download

下载房间批量导入的Excel模板

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdroomList"></a>

## GET 查询房间列表

GET /project/room/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "mdmRoomId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomName": "string",
  "productType": "string",
  "areaType": "string",
  "areaTypeName": "string",
  "buildArea": 0,
  "innerArea": 0,
  "isSale": 0,
  "isCompanySelf": 0,
  "propertyType": "string",
  "propertyTypeName": "string",
  "selfHoldingTime": "2019-08-24T14:15:22Z",
  "status": "string",
  "receiveId": "string",
  "changeId": "string",
  "disposalId": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|mdmRoomId|query|string| 否 | 主数据房间id|主数据房间id|
|projectId|query|string| 否 | 所属项目id|所属项目id|
|parcelId|query|string| 否 | 地块id|地块id|
|buildingId|query|string| 否 | 楼栋id|楼栋id|
|floorId|query|string| 否 | 楼层id|楼层id|
|roomName|query|string| 否 | 房间名称|房间名称|
|productType|query|string| 否 | 产品类型，字典值|产品类型，字典值|
|areaType|query|string| 否 | 面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|areaTypeName|query|string| 否 | 面积类型|面积类型|
|buildArea|query|number| 否 | 建筑面积|建筑面积|
|innerArea|query|number| 否 | 套内面积|套内面积|
|isSale|query|integer(int32)| 否 | 是否可售（0否 1是）|是否可售（0否 1是）|
|isCompanySelf|query|integer(int32)| 否 | 是否公司自持（0否 1是）|是否公司自持（0否 1是）|
|propertyType|query|string| 否 | 自持物业类型（1非自持 2自持）|自持物业类型（1非自持 2自持）|
|propertyTypeName|query|string| 否 | 自持物业类型名称|自持物业类型名称|
|selfHoldingTime|query|string(date-time)| 否 | 自持到期时间|自持到期时间|
|status|query|string| 否 | 房间状态（0初始 1接收 2处置）|房间状态（0初始 1接收 2处置）|
|receiveId|query|string| 否 | 接收id|接收id|
|changeId|query|string| 否 | 最新变更id|最新变更id|
|disposalId|query|string| 否 | 处置id|处置id|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|integer(int32)| 否 | 0 否 1是|0 否 1是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|
|body|body|[SysRoomQueryDTO](#schemasysroomquerydto)| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","mdmRoomId":"string","projectId":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomName":"string","productType":"string","areaType":"string","areaTypeName":"string","buildArea":0,"innerArea":0,"isSale":0,"isCompanySelf":0,"propertyType":"string","propertyTypeName":"string","selfHoldingTime":"2019-08-24T14:15:22Z","status":"string","receiveId":"string","changeId":"string","disposalId":"string","createByName":"string","updateByName":"string","isDel":0}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysRoomVo](#schemasysroomvo)]|false|none||none|
|» id|string|false|none|$column.columnComment|none|
|» mdmRoomId|string|false|none|主数据房间id|主数据房间id|
|» projectId|string|false|none|所属项目id|所属项目id|
|» parcelId|string|false|none|地块id|地块id|
|» parcelName|string|false|none|地块名称|地块名称|
|» buildingId|string|false|none|楼栋id|楼栋id|
|» buildingName|string|false|none|楼栋名称|楼栋名称|
|» floorId|string|false|none|楼层id|楼层id|
|» floorName|string|false|none|楼层名称|楼层名称|
|» roomName|string|false|none|房间名称|房间名称|
|» productType|string|false|none|产品类型，字典值|产品类型，字典值|
|» areaType|string|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|» areaTypeName|string|false|none|面积类型|面积类型|
|» buildArea|number|false|none|建筑面积|建筑面积|
|» innerArea|number|false|none|套内面积|套内面积|
|» isSale|integer(int32)|false|none|是否可售（0否 1是）|是否可售（0否 1是）|
|» isCompanySelf|integer(int32)|false|none|是否公司自持（0否 1是）|是否公司自持（0否 1是）|
|» propertyType|string|false|none|自持物业类型（1非自持 2自持）|自持物业类型（1非自持 2自持）|
|» propertyTypeName|string|false|none|自持物业类型名称|自持物业类型名称|
|» selfHoldingTime|string(date-time)|false|none|自持到期时间|自持到期时间|
|» status|string|false|none|房间状态（0初始 1接收 2处置）|房间状态（0初始 1接收 2处置）|
|» receiveId|string|false|none|接收id|接收id|
|» changeId|string|false|none|最新变更id|最新变更id|
|» disposalId|string|false|none|处置id|处置id|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<a id="opIdgetInfo_1"></a>

## GET 获取项目详细信息

GET /project/detail

获取项目详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|项目ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetBuildingTree"></a>

## GET 查询楼栋树形结构

GET /project/building/tree

查询楼栋树形结构，按地块分组

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|项目ID|query|string| 是 ||none|
|楼栋名称|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","parcelName":"string","buildings":[{"id":"string","buildingName":"string","parcelId":"string","parcelName":"string","floors":[{"id":"string","floorName":"string","rooms":[null]}]}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysParcelSimpleVo](#schemasysparcelsimplevo)]|false|none||none|
|» id|string|false|none|地块ID|none|
|» parcelName|string|false|none|地块名称|none|
|» buildings|[[SysBuildingSimpleVo](#schemasysbuildingsimplevo)]|false|none|楼栋列表|none|
|»» 楼栋列表|[SysBuildingSimpleVo](#schemasysbuildingsimplevo)|false|none|楼栋列表|none|
|»»» id|string|false|none|楼栋ID|none|
|»»» buildingName|string|false|none|楼栋名称|none|
|»»» parcelId|string|false|none|地块ID|none|
|»»» parcelName|string|false|none|地块名称|none|
|»»» floors|[[SysFloorSimpleVo](#schemasysfloorsimplevo)]|false|none|楼层列表|none|
|»»»» 楼层列表|[SysFloorSimpleVo](#schemasysfloorsimplevo)|false|none|楼层列表|none|
|»»»»» id|string|false|none|楼层ID|none|
|»»»»» floorName|string|false|none|楼层名称|none|
|»»»»» rooms|[[SysRoomSimpleVo](#schemasysroomsimplevo)]|false|none|房间列表|none|
|»»»»»» 房间列表|[SysRoomSimpleVo](#schemasysroomsimplevo)|false|none|房间列表|none|
|»»»»»»» id|string|false|none|房间ID|none|
|»»»»»»» roomName|string|false|none|房间名称|none|
|»»»»»»» receiveId|string|false|none|接收ID|none|
|»»»»»»» disposalId|string|false|none|处置ID|none|
|»»»»»»» floorId|string|false|none|楼层ID|none|
|»»»»»»» floorName|string|false|none|楼层名称|none|
|»»»»»»» buildingId|string|false|none|楼栋ID|none|
|»»»»»»» buildingName|string|false|none|楼栋名称|none|

<a id="opIdbuildingList"></a>

## GET 查询楼栋列表

GET /project/building/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "source": "string",
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|source|query|string| 否 | 来源（1主数据 2手动添加）|来源（1主数据 2手动添加）|
|mdmBuildingId|query|string| 否 | 主数据楼栋id|主数据楼栋id|
|projectId|query|string| 否 | 项目id|项目id|
|parcelId|query|string| 否 | 所属地块id|所属地块id|
|buildingName|query|string| 否 | 楼栋名称|楼栋名称|
|upFloorNums|query|integer(int64)| 否 | 地上楼层数|地上楼层数|
|underFloorNums|query|integer(int64)| 否 | 地下楼层数|地下楼层数|
|totalSelfArea|query|string| 否 | 总自持面积|总自持面积|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|integer(int32)| 否 | 0 否 1是|0 否 1是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|
|body|body|[SysBuildingQueryDTO](#schemasysbuildingquerydto)| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","source":"string","mdmBuildingId":"string","projectId":"string","parcelId":"string","buildingName":"string","upFloorNums":0,"underFloorNums":0,"totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":0}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysBuildingVo](#schemasysbuildingvo)]|false|none||none|
|» id|string|false|none|$column.columnComment|none|
|» source|string|false|none|来源（1主数据 2手动添加）|来源（1主数据 2手动添加）|
|» mdmBuildingId|string|false|none|主数据楼栋id|主数据楼栋id|
|» projectId|string|false|none|项目id|项目id|
|» parcelId|string|false|none|所属地块id|所属地块id|
|» buildingName|string|false|none|楼栋名称|楼栋名称|
|» upFloorNums|integer(int32)|false|none|地上楼层数|地上楼层数|
|» underFloorNums|integer(int32)|false|none|地下楼层数|地下楼层数|
|» totalSelfArea|string|false|none|总自持面积|总自持面积|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<a id="opIdgetProjectDetail"></a>

## GET 获取项目详情信息

GET /project/basic/detail

根据项目id查询出对应的信息，包括项目，分期，地块信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||项目ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"project":{"id":"string","type":"string","mdmProjectId":"string","mdmName":"string","mdmSaleName":"string","code":"string","name":"string","provinceCode":"string","provinceName":"string","cityCode":"string","cityName":"string","countryCode":"string","countryName":"string","mdmTypeName":"string","assetType":"string","propertyUnit":"string","projectAddress":"string","longitude":"string","latitude":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":0},"stages":[{"id":"string","mdmStageId":"string","projectId":"string","stageName":"string","createByName":"string","updateByName":"string","isDel":0}],"parcels":[{"id":"string","mdmParcelId":"string","projectId":"string","stageId":"string","parcelName":"string","mdmNatureName":"string","landUsage":0,"mdmAddress":"string","address":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":0}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[SysProjectDetailVo](#schemasysprojectdetailvo)|

<a id="opIddeleteRoom"></a>

## DELETE 删除房间

DELETE /project/room/delete

删除房间信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|房间ID列表|query|array[string]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddeleteProject"></a>

## DELETE 删除项目

DELETE /project/delete

根据项目id删除项目及其关联的分期、地块、楼栋、房间信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||项目ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddeleteBuilding"></a>

## DELETE 删除楼栋

DELETE /project/building/delete

删除楼栋及其关联的楼层信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|楼栋ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资产中心/oa-callback-controller

<a id="opIdcallBackFactory"></a>

## POST callBackFactory

POST /oa/callback

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

# 资产中心/主数据管理

<a id="opIdlist_1"></a>

## POST 查询主数据项目列表

POST /mdm/project/list

根据项目名称查询主数据项目列表，排除已存在于sys_project表的数据

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "pmdVersion": "string",
  "projectName": "string",
  "projectSaleName": "string",
  "projectTypeName": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "financialCompanyName": "string",
  "totalSelfArea": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SynMdmProjectQueryDTO](#schemasynmdmprojectquerydto)| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","pmdVersion":"string","projectName":"string","projectSaleName":"string","projectTypeName":"string","provinceCode":"string","provinceName":"string","cityCode":"string","cityName":"string","countryCode":"string","countryName":"string","projectAddress":"string","longitude":"string","latitude":"string","financialCompanyName":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":0,"stages":[{"id":"string","projectId":"string","stageName":"string","createByName":"string","updateByName":"string","isDel":0}],"parcels":[{"id":"string","projectId":"string","stageId":"string","parcelName":"string","parcelNatureName":"string","landUsage":0,"parcelAddress":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":0}],"buildings":[{"id":"string","projectId":"string","parcelId":"string","buildingName":"string","upFloorNums":"string","underFloorNums":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":0}],"floors":[{"id":"string","projectId":"string","buildingId":"string","floorName":"string","floorTypeName":"string","designFloorHeight":"string","designLoad":"string","createByName":"string","updateByName":"string","isDel":0}],"rooms":[{"id":"string","projectId":"string","parcelId":"string","buildingId":"string","floorId":"string","roomName":"string","apartmentTypeName":"string","isSale":0,"isCompanySelf":0,"propertyTypeName":"string","selfHoldingTime":"2019-08-24T14:15:22Z","publicAreaTypeName":"string","designBuildArea":"string","predictBuildArea":"string","realBuildArea":"string","designInnerArea":"string","predictInnerArea":"string","realInnerArea":"string","createByName":"string","updateByName":"string","isDel":0}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[SynMdmProjectVo](#schemasynmdmprojectvo)|

<a id="opIdgetInfo_2"></a>

## GET 获取主数据项目详细信息

GET /mdm/project/detail

根据项目ID获取项目详情，包含分期、地块、楼栋、房间信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|mdmProjectId|query|string| 是 ||项目ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","pmdVersion":"string","projectName":"string","projectSaleName":"string","projectTypeName":"string","provinceCode":"string","provinceName":"string","cityCode":"string","cityName":"string","countryCode":"string","countryName":"string","projectAddress":"string","longitude":"string","latitude":"string","financialCompanyName":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":0,"stages":[{"id":"string","projectId":"string","stageName":"string","createByName":"string","updateByName":"string","isDel":0}],"parcels":[{"id":"string","projectId":"string","stageId":"string","parcelName":"string","parcelNatureName":"string","landUsage":0,"parcelAddress":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":0}],"buildings":[{"id":"string","projectId":"string","parcelId":"string","buildingName":"string","upFloorNums":"string","underFloorNums":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":0}],"floors":[{"id":"string","projectId":"string","buildingId":"string","floorName":"string","floorTypeName":"string","designFloorHeight":"string","designLoad":"string","createByName":"string","updateByName":"string","isDel":0}],"rooms":[{"id":"string","projectId":"string","parcelId":"string","buildingId":"string","floorId":"string","roomName":"string","apartmentTypeName":"string","isSale":0,"isCompanySelf":0,"propertyTypeName":"string","selfHoldingTime":"2019-08-24T14:15:22Z","publicAreaTypeName":"string","designBuildArea":"string","predictBuildArea":"string","realBuildArea":"string","designInnerArea":"string","predictInnerArea":"string","realInnerArea":"string","createByName":"string","updateByName":"string","isDel":0}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[SynMdmProjectVo](#schemasynmdmprojectvo)|

# 资产中心/mdm-api-controller

<a id="opIdgetRoomList"></a>

## GET getRoomList

GET /esb/sync/mdm/room

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

<a id="opIdgetProjectList"></a>

## GET getProjectList

GET /esb/sync/mdm/project

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

# 租赁后台/打印模版

<a id="opIdedit"></a>

## PUT 修改打印模版

PUT /template

修改打印模版

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "templateName": "string",
  "projectId": "string",
  "applyLevel": "string",
  "applyScope": "string",
  "printType": "string",
  "contractPurpose": "string",
  "effectiveDate": "2019-08-24T14:15:22Z",
  "expirationDate": "2019-08-24T14:15:22Z",
  "status": "string",
  "linkUrl": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[PrintTemplateAddDTO](#schemaprinttemplateadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd"></a>

## POST 新增打印模版

POST /template

新增打印模版

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "templateName": "string",
  "projectId": "string",
  "applyLevel": "string",
  "applyScope": "string",
  "printType": "string",
  "contractPurpose": "string",
  "effectiveDate": "2019-08-24T14:15:22Z",
  "expirationDate": "2019-08-24T14:15:22Z",
  "status": "string",
  "linkUrl": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[PrintTemplateAddDTO](#schemaprinttemplateadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdupdateStatus"></a>

## POST 修改打印模版状态

POST /template/updateStatus

修改打印模版状态

> Body 请求参数

```json
{
  "templateId": "123456",
  "status": "1"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[PrintTemplateStatusDto](#schemaprinttemplatestatusdto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport"></a>

## POST 导出询打印模版列表

POST /template/export

导出询打印模版列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|templateName|query|string| 否 | 模版名称|模版名称|
|projectId|query|string| 否 | 项目ID|项目ID|
|applyLevel|query|string| 否 | 适用层级（1集团 2项目）|适用层级（1集团 2项目）|
|applyScope|query|string| 否 | 适用范围|适用范围|
|printType|query|string| 否 | 套打类型（1合同 2协议）|套打类型（1合同 2协议）|
|contractPurpose|query|string| 否 | 合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|
|effectiveDate|query|string(date-time)| 否 | 生效日期|生效日期|
|expirationDate|query|string(date-time)| 否 | 失效日期|失效日期|
|status|query|string| 否 | 状态（0新增 1生效 2失效）|状态（0新增 1生效 2失效）|
|linkUrl|query|string| 否 | 链接地址|链接地址|
|remark|query|string| 否 | 备注|备注|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdlist_3"></a>

## GET 查询打印模版列表

GET /template/list

查询打印模版列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|templateName|query|string| 否 | 模版名称|模版名称|
|projectId|query|string| 否 | 项目ID|项目ID|
|applyLevel|query|string| 否 | 适用层级（1集团 2项目）|适用层级（1集团 2项目）|
|applyScope|query|string| 否 | 适用范围|适用范围|
|printType|query|string| 否 | 套打类型（1合同 2协议）|套打类型（1合同 2协议）|
|contractPurpose|query|string| 否 | 合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|
|effectiveDate|query|string(date-time)| 否 | 生效日期|生效日期|
|expirationDate|query|string(date-time)| 否 | 失效日期|失效日期|
|status|query|string| 否 | 状态（0新增 1生效 2失效）|状态（0新增 1生效 2失效）|
|linkUrl|query|string| 否 | 链接地址|链接地址|
|remark|query|string| 否 | 备注|备注|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgetInfo_1"></a>

## GET 获取打印模版详细信息

GET /template/detail

获取打印模版详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|打印模版ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_1"></a>

## DELETE 删除打印模版

DELETE /template/delete

删除打印模版

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|打印模版ID列表|query|array[string]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 租赁后台/打印模版字典

<a id="opIdedit_1"></a>

## PUT 修改打印模版字典

PUT /template/dict

修改打印模版字典

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "parentId": "string",
  "name": "string",
  "type": 0,
  "code": "string",
  "dbField": "string",
  "sqlText": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[PrintTemplateDictAddDTO](#schemaprinttemplatedictadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_1"></a>

## POST 新增打印模版字典

POST /template/dict

新增打印模版字典

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "parentId": "string",
  "name": "string",
  "type": 0,
  "code": "string",
  "dbField": "string",
  "sqlText": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[PrintTemplateDictAddDTO](#schemaprinttemplatedictadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_1"></a>

## POST 导出询打印模版字典列表

POST /template/dict/export

导出询打印模版字典列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|parentId|query|string| 否 | 父字典id|父字典id|
|name|query|string| 否 | 名称|名称|
|type|query|integer(int32)| 否 | 字段类型（0字段 1表格）|字段类型（0字段 1表格）|
|code|query|string| 否 | 模板字段code|模板字段code|
|dbField|query|string| 否 | 数据库字段|数据库字段|
|sqlText|query|string| 否 | sql语句|sql语句|
|remark|query|string| 否 | 字典解释说明|字典解释说明|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdlist_4"></a>

## GET 查询打印模版字典列表

GET /template/dict/list

查询打印模版字典列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|parentId|query|string| 否 | 父字典id|父字典id|
|name|query|string| 否 | 名称|名称|
|type|query|integer(int32)| 否 | 字段类型（0字段 1表格）|字段类型（0字段 1表格）|
|code|query|string| 否 | 模板字段code|模板字段code|
|dbField|query|string| 否 | 数据库字段|数据库字段|
|sqlText|query|string| 否 | sql语句|sql语句|
|remark|query|string| 否 | 字典解释说明|字典解释说明|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgetInfo"></a>

## GET 获取打印模版字典详细信息

GET /template/dict/detail

获取打印模版字典详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|打印模版字典ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove"></a>

## DELETE 删除打印模版字典

DELETE /template/dict/delete

删除打印模版字典

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|打印模版字典ID列表|query|array[string]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 租赁后台/房源

<a id="opIdedit_2"></a>

## PUT 修改房源

PUT /room

根据房源ID更新房源信息

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "roomName": "string",
  "type": "string",
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "roomCode": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "areaType": "string",
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": "string",
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": "string",
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": "string",
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": 0,
  "selfUseSubject": "string",
  "selfUsePurpose": "string",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomAddDTO](#schemaroomadddto)| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdadd_2"></a>

## POST 新增房源

POST /room

创建新的房源记录

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "roomName": "string",
  "type": "string",
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "roomCode": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "areaType": "string",
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": "string",
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": "string",
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": "string",
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": 0,
  "selfUseSubject": "string",
  "selfUsePurpose": "string",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomAddDTO](#schemaroomadddto)| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdexport_2"></a>

## POST 导出房源列表

POST /room/export

将房源列表数据导出为Excel文件

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|roomName|query|string| 否 | 房源名称|房源名称|
|type|query|string| 否 | 房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|query|string| 否 | 物业类型，二级字典|物业类型，二级字典|
|projectId|query|string| 否 | 项目id|项目id|
|parcelId|query|string| 否 | 地块id|地块id|
|parcelName|query|string| 否 | 地块名称|地块名称|
|buildingId|query|string| 否 | 楼栋id|楼栋id|
|buildingName|query|string| 否 | 楼栋名称|楼栋名称|
|floorId|query|string| 否 | 楼层id|楼层id|
|floorName|query|string| 否 | 楼层名称|楼层名称|
|roomId|query|string| 否 | 房间id， sys_room表的id|房间id， sys_room表的id|
|status|query|integer(int64)| 否 | 状态（0草稿 10待生效 20生效中 30已失效）|状态（0草稿 10待生效 20生效中 30已失效）|
|roomCode|query|string| 否 | 合成编码|合成编码|
|planEffectDate|query|string(date-time)| 否 | 预计生效日期|预计生效日期|
|actualEffectDate|query|string(date-time)| 否 | 实际生效日期|实际生效日期|
|areaType|query|string| 否 | 面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|buildArea|query|number| 否 | 建筑面积|建筑面积|
|innerArea|query|number| 否 | 套内面积|套内面积|
|rentAreaType|query|string| 否 | 计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|rentArea|query|number| 否 | 计租面积|计租面积|
|storey|query|number| 否 | 层高|层高|
|value|query|number| 否 | 荷载值|荷载值|
|orientation|query|string| 否 | 朝向|朝向|
|houseTypeId|query|string| 否 | 户型id|户型id|
|smartWaterMeter|query|string| 否 | 智能水电表|智能水电表|
|smartLock|query|string| 否 | 智能锁|智能锁|
|assetOperationMode|query|string| 否 | 资产运营模式|资产运营模式|
|assetOperationType|query|string| 否 | 资产运营分类|资产运营分类|
|propertyStatus|query|string| 否 | 产权情况|产权情况|
|paymentStatus|query|string| 否 | 交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|specialTag|query|string| 否 | 特殊标签（1保障房）|特殊标签（1保障房）|
|latestRentPrice|query|number| 否 | 商管承租成本单价（最新）|商管承租成本单价（最新）|
|latestRentStartDate|query|string(date-time)| 否 | 商管承租起计日期（最新）|商管承租起计日期（最新）|
|latestRentEndDate|query|string(date-time)| 否 | 商管承租到期日期（最新）|商管承租到期日期（最新）|
|firstRentPrice|query|number| 否 | 商管承租成本单价第一次|商管承租成本单价第一次|
|firstRentStartDate|query|string(date-time)| 否 | 商管承租起计日期第一次|商管承租起计日期第一次|
|firstRentEndDate|query|string(date-time)| 否 | 商管承租到期日期第一次|商管承租到期日期第一次|
|operationSubject|query|string| 否 | 运营主体（1商服 2众创城）|运营主体（1商服 2众创城）|
|rentalStartDate|query|string(date-time)| 否 | 可招商日期|可招商日期|
|externalRentStartDate|query|string(date-time)| 否 | 对外出租起始日期|对外出租起始日期|
|isSelfUse|query|integer(int32)| 否 | 是否自用（0否 1是）|是否自用（0否 1是）|
|selfUseSubject|query|string| 否 | 自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|selfUsePurpose|query|string| 否 | 自用用途|自用用途|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|integer(int32)| 否 | 0-否,1-是|0-否,1-是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|Excel文件流|None|

<a id="opIdlist_5"></a>

## GET 查询房源列表

GET /room/list

根据查询条件分页获取房源列表数据

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|roomName|query|string| 否 | 房源名称|房源名称|
|type|query|string| 否 | 房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|query|string| 否 | 物业类型，二级字典|物业类型，二级字典|
|projectId|query|string| 否 | 项目id|项目id|
|parcelId|query|string| 否 | 地块id|地块id|
|parcelName|query|string| 否 | 地块名称|地块名称|
|buildingId|query|string| 否 | 楼栋id|楼栋id|
|buildingName|query|string| 否 | 楼栋名称|楼栋名称|
|floorId|query|string| 否 | 楼层id|楼层id|
|floorName|query|string| 否 | 楼层名称|楼层名称|
|roomId|query|string| 否 | 房间id， sys_room表的id|房间id， sys_room表的id|
|status|query|integer(int64)| 否 | 状态（0草稿 10待生效 20生效中 30已失效）|状态（0草稿 10待生效 20生效中 30已失效）|
|roomCode|query|string| 否 | 合成编码|合成编码|
|planEffectDate|query|string(date-time)| 否 | 预计生效日期|预计生效日期|
|actualEffectDate|query|string(date-time)| 否 | 实际生效日期|实际生效日期|
|areaType|query|string| 否 | 面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|buildArea|query|number| 否 | 建筑面积|建筑面积|
|innerArea|query|number| 否 | 套内面积|套内面积|
|rentAreaType|query|string| 否 | 计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|rentArea|query|number| 否 | 计租面积|计租面积|
|storey|query|number| 否 | 层高|层高|
|value|query|number| 否 | 荷载值|荷载值|
|orientation|query|string| 否 | 朝向|朝向|
|houseTypeId|query|string| 否 | 户型id|户型id|
|smartWaterMeter|query|string| 否 | 智能水电表|智能水电表|
|smartLock|query|string| 否 | 智能锁|智能锁|
|assetOperationMode|query|string| 否 | 资产运营模式|资产运营模式|
|assetOperationType|query|string| 否 | 资产运营分类|资产运营分类|
|propertyStatus|query|string| 否 | 产权情况|产权情况|
|paymentStatus|query|string| 否 | 交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|specialTag|query|string| 否 | 特殊标签（1保障房）|特殊标签（1保障房）|
|latestRentPrice|query|number| 否 | 商管承租成本单价（最新）|商管承租成本单价（最新）|
|latestRentStartDate|query|string(date-time)| 否 | 商管承租起计日期（最新）|商管承租起计日期（最新）|
|latestRentEndDate|query|string(date-time)| 否 | 商管承租到期日期（最新）|商管承租到期日期（最新）|
|firstRentPrice|query|number| 否 | 商管承租成本单价第一次|商管承租成本单价第一次|
|firstRentStartDate|query|string(date-time)| 否 | 商管承租起计日期第一次|商管承租起计日期第一次|
|firstRentEndDate|query|string(date-time)| 否 | 商管承租到期日期第一次|商管承租到期日期第一次|
|operationSubject|query|string| 否 | 运营主体（1商服 2众创城）|运营主体（1商服 2众创城）|
|rentalStartDate|query|string(date-time)| 否 | 可招商日期|可招商日期|
|externalRentStartDate|query|string(date-time)| 否 | 对外出租起始日期|对外出租起始日期|
|isSelfUse|query|integer(int32)| 否 | 是否自用（0否 1是）|是否自用（0否 1是）|
|selfUseSubject|query|string| 否 | 自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|selfUsePurpose|query|string| 否 | 自用用途|自用用途|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|integer(int32)| 否 | 0-否,1-是|0-否,1-是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","roomName":"string","type":"string","propertyType":"string","projectId":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomId":"string","status":0,"roomCode":"string","planEffectDate":"2019-08-24T14:15:22Z","actualEffectDate":"2019-08-24T14:15:22Z","areaType":"string","buildArea":0,"innerArea":0,"rentAreaType":"string","rentArea":0,"storey":0,"value":0,"orientation":"string","houseTypeId":"string","smartWaterMeter":"string","smartLock":"string","assetOperationMode":"string","assetOperationType":"string","propertyStatus":"string","paymentStatus":"string","specialTag":"string","latestRentPrice":0,"latestRentStartDate":"2019-08-24T14:15:22Z","latestRentEndDate":"2019-08-24T14:15:22Z","firstRentPrice":0,"firstRentStartDate":"2019-08-24T14:15:22Z","firstRentEndDate":"2019-08-24T14:15:22Z","operationSubject":"string","rentalStartDate":"2019-08-24T14:15:22Z","externalRentStartDate":"2019-08-24T14:15:22Z","isSelfUse":0,"selfUseSubject":"string","selfUsePurpose":"string","createByName":"string","updateByName":"string","isDel":0}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomVo](#schemaroomvo)]|false|none||none|
|» id|string|false|none|$column.columnComment|none|
|» roomName|string|false|none|房源名称|房源名称|
|» type|string|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|» propertyType|string|false|none|物业类型，二级字典|物业类型，二级字典|
|» projectId|string|false|none|项目id|项目id|
|» parcelId|string|false|none|地块id|地块id|
|» parcelName|string|false|none|地块名称|地块名称|
|» buildingId|string|false|none|楼栋id|楼栋id|
|» buildingName|string|false|none|楼栋名称|楼栋名称|
|» floorId|string|false|none|楼层id|楼层id|
|» floorName|string|false|none|楼层名称|楼层名称|
|» roomId|string|false|none|房间id， sys_room表的id|房间id， sys_room表的id|
|» status|integer(int64)|false|none|状态（0草稿 10待生效 20生效中 30已失效）|状态（0草稿 10待生效 20生效中 30已失效）|
|» roomCode|string|false|none|合成编码|合成编码|
|» planEffectDate|string(date-time)|false|none|预计生效日期|预计生效日期|
|» actualEffectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|» areaType|string|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|» buildArea|number|false|none|建筑面积|建筑面积|
|» innerArea|number|false|none|套内面积|套内面积|
|» rentAreaType|string|false|none|计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|» rentArea|number|false|none|计租面积|计租面积|
|» storey|number|false|none|层高|层高|
|» value|number|false|none|荷载值|荷载值|
|» orientation|string|false|none|朝向|朝向|
|» houseTypeId|string|false|none|户型id|户型id|
|» smartWaterMeter|string|false|none|智能水电表|智能水电表|
|» smartLock|string|false|none|智能锁|智能锁|
|» assetOperationMode|string|false|none|资产运营模式|资产运营模式|
|» assetOperationType|string|false|none|资产运营分类|资产运营分类|
|» propertyStatus|string|false|none|产权情况|产权情况|
|» paymentStatus|string|false|none|交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|» specialTag|string|false|none|特殊标签（1保障房）|特殊标签（1保障房）|
|» latestRentPrice|number|false|none|商管承租成本单价（最新）|商管承租成本单价（最新）|
|» latestRentStartDate|string(date-time)|false|none|商管承租起计日期（最新）|商管承租起计日期（最新）|
|» latestRentEndDate|string(date-time)|false|none|商管承租到期日期（最新）|商管承租到期日期（最新）|
|» firstRentPrice|number|false|none|商管承租成本单价第一次|商管承租成本单价第一次|
|» firstRentStartDate|string(date-time)|false|none|商管承租起计日期第一次|商管承租起计日期第一次|
|» firstRentEndDate|string(date-time)|false|none|商管承租到期日期第一次|商管承租到期日期第一次|
|» operationSubject|string|false|none|运营主体（1商服 2众创城）|运营主体（1商服 2众创城）|
|» rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|» externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|» isSelfUse|integer(int32)|false|none|是否自用（0否 1是）|是否自用（0否 1是）|
|» selfUseSubject|string|false|none|自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|» selfUsePurpose|string|false|none|自用用途|自用用途|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|

<a id="opIdgetInfo_2"></a>

## GET 获取房源详细信息

GET /room/detail

根据房源ID获取房源的详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||房源ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","roomName":"string","type":"string","propertyType":"string","projectId":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomId":"string","status":0,"roomCode":"string","planEffectDate":"2019-08-24T14:15:22Z","actualEffectDate":"2019-08-24T14:15:22Z","areaType":"string","buildArea":0,"innerArea":0,"rentAreaType":"string","rentArea":0,"storey":0,"value":0,"orientation":"string","houseTypeId":"string","smartWaterMeter":"string","smartLock":"string","assetOperationMode":"string","assetOperationType":"string","propertyStatus":"string","paymentStatus":"string","specialTag":"string","latestRentPrice":0,"latestRentStartDate":"2019-08-24T14:15:22Z","latestRentEndDate":"2019-08-24T14:15:22Z","firstRentPrice":0,"firstRentStartDate":"2019-08-24T14:15:22Z","firstRentEndDate":"2019-08-24T14:15:22Z","operationSubject":"string","rentalStartDate":"2019-08-24T14:15:22Z","externalRentStartDate":"2019-08-24T14:15:22Z","isSelfUse":0,"selfUseSubject":"string","selfUsePurpose":"string","createByName":"string","updateByName":"string","isDel":0}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[RoomVo](#schemaroomvo)|

<a id="opIdremove_2"></a>

## DELETE 删除房源

DELETE /room/delete

根据房源ID列表批量删除房源

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|query|array[string]| 是 ||房源ID列表|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

# 租赁后台/sys-project-controller

<a id="opIdlist_6"></a>

## GET list_6

GET /project/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|createBy|query|string| 否 ||none|
|createByName|query|string| 否 ||none|
|createTime|query|string(date-time)| 否 ||none|
|updateBy|query|string| 否 ||none|
|updateByName|query|string| 否 ||none|
|updateTime|query|string(date-time)| 否 ||none|
|id|query|integer(int64)| 否 ||none|
|projectName|query|string| 否 ||none|
|projectGroup|query|string| 否 ||none|
|projectType|query|string| 否 ||none|
|projectCode|query|string| 否 ||none|
|companyId|query|integer(int64)| 否 ||none|
|projectLocation|query|string| 否 ||none|
|projectAddress|query|string| 否 ||none|
|projectCover|query|string| 否 ||none|
|deleteFlg|query|string| 否 ||none|
|projectProvinceCode|query|string| 否 ||none|
|projectProvinceName|query|string| 否 ||none|
|projectCityCode|query|string| 否 ||none|
|projectCityName|query|string| 否 ||none|
|projectDistrictCode|query|string| 否 ||none|
|projectDistrictName|query|string| 否 ||none|
|wdProjId|query|string| 否 ||none|
|oldCompanyId|query|string| 否 ||none|
|lat|query|string| 否 ||none|
|lng|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdedit_3"></a>

## PUT edit_3

PUT /project

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": 0,
  "projectName": "string",
  "projectGroup": "string",
  "projectType": "string",
  "projectCode": "string",
  "companyId": 0,
  "projectLocation": "string",
  "projectAddress": "string",
  "projectCover": "string",
  "deleteFlg": "string",
  "projectProvinceCode": "string",
  "projectProvinceName": "string",
  "projectCityCode": "string",
  "projectCityName": "string",
  "projectDistrictCode": "string",
  "projectDistrictName": "string",
  "wdProjId": "string",
  "oldCompanyId": "string",
  "lat": "string",
  "lng": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysProject](#schemasysproject)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_3"></a>

## POST add_3

POST /project

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": 0,
  "projectName": "string",
  "projectGroup": "string",
  "projectType": "string",
  "projectCode": "string",
  "companyId": 0,
  "projectLocation": "string",
  "projectAddress": "string",
  "projectCover": "string",
  "deleteFlg": "string",
  "projectProvinceCode": "string",
  "projectProvinceName": "string",
  "projectCityCode": "string",
  "projectCityName": "string",
  "projectDistrictCode": "string",
  "projectDistrictName": "string",
  "wdProjId": "string",
  "oldCompanyId": "string",
  "lat": "string",
  "lng": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysProject](#schemasysproject)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_3"></a>

## POST export_3

POST /project/export

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|createBy|query|string| 否 ||none|
|createByName|query|string| 否 ||none|
|createTime|query|string(date-time)| 否 ||none|
|updateBy|query|string| 否 ||none|
|updateByName|query|string| 否 ||none|
|updateTime|query|string(date-time)| 否 ||none|
|id|query|integer(int64)| 否 ||none|
|projectName|query|string| 否 ||none|
|projectGroup|query|string| 否 ||none|
|projectType|query|string| 否 ||none|
|projectCode|query|string| 否 ||none|
|companyId|query|integer(int64)| 否 ||none|
|projectLocation|query|string| 否 ||none|
|projectAddress|query|string| 否 ||none|
|projectCover|query|string| 否 ||none|
|deleteFlg|query|string| 否 ||none|
|projectProvinceCode|query|string| 否 ||none|
|projectProvinceName|query|string| 否 ||none|
|projectCityCode|query|string| 否 ||none|
|projectCityName|query|string| 否 ||none|
|projectDistrictCode|query|string| 否 ||none|
|projectDistrictName|query|string| 否 ||none|
|wdProjId|query|string| 否 ||none|
|oldCompanyId|query|string| 否 ||none|
|lat|query|string| 否 ||none|
|lng|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdgetInfo_3"></a>

## GET getInfo_3

GET /project/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_3"></a>

## DELETE remove_3

DELETE /project/{ids}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|path|array[integer]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 租赁后台/固定资产

<a id="opIdedit_4"></a>

## PUT 修改固定资产

PUT /fixed/assets

修改固定资产

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "category": "string",
  "name": "string",
  "specification": "string",
  "attachments": "string",
  "usageScope": "string",
  "remark": "string",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[FixedAssetsAddDTO](#schemafixedassetsadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_4"></a>

## POST 新增固定资产

POST /fixed/assets

新增固定资产

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "category": "string",
  "name": "string",
  "specification": "string",
  "attachments": "string",
  "usageScope": "string",
  "remark": "string",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[FixedAssetsAddDTO](#schemafixedassetsadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_4"></a>

## POST 导出询固定资产列表

POST /fixed/assets/export

导出询固定资产列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|category|query|string| 否 | 种类|种类|
|name|query|string| 否 | 物品名称|物品名称|
|specification|query|string| 否 | 规格|规格|
|attachments|query|string| 否 | 附件|附件|
|usageScope|query|string| 否 | 使用范围|使用范围|
|remark|query|string| 否 | 备注|备注|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|integer(int32)| 否 | 0-否,1-是|0-否,1-是|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdlist_7"></a>

## GET 查询固定资产列表

GET /fixed/assets/list

根据种类、物品名称（模糊查询）分页查询固定资产列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|category|query|string| 否 | 种类|种类|
|name|query|string| 否 | 物品名称|物品名称|
|specification|query|string| 否 | 规格|规格|
|attachments|query|string| 否 | 附件|附件|
|usageScope|query|string| 否 | 使用范围|使用范围|
|remark|query|string| 否 | 备注|备注|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|integer(int32)| 否 | 0-否,1-是|0-否,1-是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","category":"string","name":"string","specification":"string","attachments":"string","usageScope":"string","remark":"string","createByName":"string","updateByName":"string","isDel":0}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[FixedAssetsVo](#schemafixedassetsvo)]|false|none||none|
|» id|string|false|none|id|none|
|» category|string|false|none|种类|种类|
|» name|string|false|none|物品名称|物品名称|
|» specification|string|false|none|规格|规格|
|» attachments|string|false|none|附件|附件|
|» usageScope|string|false|none|使用范围|使用范围|
|» remark|string|false|none|备注|备注|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|

<a id="opIdgetInfo_4"></a>

## GET 获取固定资产详细信息

GET /fixed/assets/detail

获取固定资产详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|固定资产ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_4"></a>

## DELETE 删除固定资产

DELETE /fixed/assets/delete

删除固定资产

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|固定资产ID列表|query|array[string]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 租赁后台/客户管理

<a id="opIdlist"></a>

## POST 客户列表查询接口

POST /customer/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "customerType": 0,
  "customerName": "string",
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "contactAddress": "string",
  "maintainerId": "string",
  "maintainerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[CustomerQueryDTO](#schemacustomerquerydto)| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","customerType":0,"customerName":"string","creditCode":"string","legalName":"string","contactPhone":"string","idType":"string","idNumber":"string","idValidityStart":"2019-08-24T14:15:22Z","idValidityEnd":"2019-08-24T14:15:22Z","idFront":"string","idBack":"string","contactAddress":"string","maintainerId":"string","maintainerName":"string","attachmentFiles":"string","remark":"string","createByName":"string","updateByName":"string","isDel":0}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[CustomerVo](#schemacustomervo)]|false|none||none|
|» id|string|false|none|$column.columnComment|none|
|» projectId|string|false|none|项目id|项目id|
|» customerType|integer(int64)|false|none|客户类型，个人：1，企业：2|客户类型，个人：1，企业：2|
|» customerName|string|false|none|客户姓名，支持身份证识别填充|客户姓名，支持身份证识别填充|
|» creditCode|string|false|none|统一社会信用代码，需校验唯一性|统一社会信用代码，需校验唯一性|
|» legalName|string|false|none|法人姓名|法人姓名|
|» contactPhone|string|false|none|联系电话，需校验唯一性|联系电话，需校验唯一性|
|» idType|string|false|none|证件类型，身份证/护照等|证件类型，身份证/护照等|
|» idNumber|string|false|none|证件号码，支持身份证识别填充|证件号码，支持身份证识别填充|
|» idValidityStart|string(date-time)|false|none|证件有效期开始，支持身份证识别填充|证件有效期开始，支持身份证识别填充|
|» idValidityEnd|string(date-time)|false|none|证件有效期结束，支持身份证识别填充|证件有效期结束，支持身份证识别填充|
|» idFront|string|false|none|身份证正面地址|身份证正面地址|
|» idBack|string|false|none|身份证反面地址|身份证反面地址|
|» contactAddress|string|false|none|联系地址，支持身份证识别填充|联系地址，支持身份证识别填充|
|» maintainerId|string|false|none|维护人id|维护人id|
|» maintainerName|string|false|none|维护人姓名|维护人姓名|
|» attachmentFiles|string|false|none|附件|附件|
|» remark|string|false|none|备注|备注|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<a id="opIdadd_5"></a>

## POST 新增客户接口

POST /customer/add

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "customerType": 0,
  "customerName": "string",
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "contactAddress": "string",
  "maintainerId": "string",
  "maintainerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "isDel": 0,
  "customerContactList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "gender": "string",
      "idNumber": "string",
      "position": "string",
      "department": "string",
      "isPreferred": 0,
      "remark": "string",
      "isDel": 0
    }
  ],
  "customerGuarantorList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "idType": "string",
      "idNumber": "string",
      "remark": "string",
      "isDel": 0
    }
  ],
  "customerBankAccountList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "bankName": "string",
      "accountNumber": "string",
      "accountRemark": "string",
      "isDel": 0
    }
  ],
  "customerInvoiceList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "title": "string",
      "taxNumber": "string",
      "phone": "string",
      "address": "string",
      "bankName": "string",
      "accountNumber": "string",
      "isDel": 0
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[CustomerAddDTO](#schemacustomeradddto)| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

# 租赁后台/通用接口请求

<a id="opIdavatar"></a>

## POST avatar

POST /common/upload

> Body 请求参数

```json
{
  "file": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|object| 否 ||none|
|» file|body|string(binary)| 是 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_1"></a>

## POST 通用接口请求

POST /common/query

通用接口请求

> Body 请求参数

```json
{
  "property1": {},
  "property2": {}
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|name|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|
|body|body|object| 否 ||none|
|» **additionalProperties**|body|object| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":[{}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RList](#schemarlist)|

# 租赁后台/定单管理

<a id="opIdsaveBooking"></a>

## POST 保存定单

POST /booking/save

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "isDel": 0,
  "isSubmit": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingAddDTO](#schemabookingadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_2"></a>

## POST 定单列表查询接口

POST /booking/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "searchType": 0,
  "projectId": "string",
  "customerName": "string",
  "roomName": "string",
  "status": 0,
  "createTimeStart": "string",
  "createTimeEnd": "string",
  "createByName": "string",
  "actualReceiveTimeStart": "string",
  "actualReceiveTimeEnd": "string",
  "contractNo": "string",
  "signDateStart": "string",
  "signDateEnd": "string",
  "contractLeaseUnit": "string",
  "lesseeName": "string",
  "cancelReason": 0,
  "cancelByName": "string",
  "cancelTimeStart": "string",
  "cancelTimeEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingQueryDTO](#schemabookingquerydto)| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","customerName":"string","propertyType":"string","roomId":"string","roomName":"string","bookingAmount":0,"receivableDate":"2019-08-24T14:15:22Z","expectSignDate":"2019-08-24T14:15:22Z","isRefundable":0,"cancelTime":"2019-08-24T14:15:22Z","cancelBy":"string","cancelByName":"string","cancelReason":0,"isRefund":0,"cancelEnclosure":"string","cancelRemark":"string","status":0,"contractId":"string","refundId":"string","createByName":"string","updateByName":"string","isDel":0,"contractNo":"string","signDate":"2019-08-24T14:15:22Z","contractLeaseUnit":"string","lesseeName":"string"}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[BookingVo](#schemabookingvo)]|false|none||none|
|» id|string|false|none|$column.columnComment|none|
|» projectId|string|false|none|项目id|项目id|
|» customerName|string|false|none|客户名称|客户名称|
|» propertyType|string|false|none|意向物业类型|意向物业类型|
|» roomId|string|false|none|房源id|房源id|
|» roomName|string|false|none|房源名称|房源名称|
|» bookingAmount|number|false|none|定单金额|定单金额|
|» receivableDate|string(date-time)|false|none|应收日期|应收日期|
|» expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|» isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|» cancelTime|string(date-time)|false|none|作废时间|作废时间|
|» cancelBy|string|false|none|作废人|作废人|
|» cancelByName|string|false|none|作废人姓名|作废人姓名|
|» cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|» isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|» cancelEnclosure|string|false|none|退定附件|退定附件|
|» cancelRemark|string|false|none|退定说明|退定说明|
|» status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|» contractId|string|false|none|转签约合同id|转签约合同id|
|» refundId|string|false|none|退款单id|退款单id|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|
|» contractNo|string|false|none|合同号|合同号|
|» signDate|string(date-time)|false|none|合同签订日期|合同签订日期|
|» contractLeaseUnit|string|false|none|租赁单元|租赁单元|
|» lesseeName|string|false|none|承租人名称|承租人名称|

<a id="opIdlist_7"></a>

## GET 查询定单列表

GET /booking/list

查询定单列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|searchType|query|integer(int32)| 否 | 页签类型|页签类型:1-待生效 2生效中 3已转签 4已作废|
|projectId|query|string| 否 | 项目id|项目id|
|customerName|query|string| 否 | 客户名称|客户名称|
|roomName|query|string| 否 | 意向房源|意向房源|
|status|query|integer(int64)| 否 | 状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|createTimeStart|query|string(date-time)| 否 | 创建日期开始|创建日期开始|
|createTimeEnd|query|string(date-time)| 否 | 创建日期结束|创建日期结束|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|actualReceiveTimeStart|query|string(date-time)| 否 | 实收日期开始|实收日期开始|
|actualReceiveTimeEnd|query|string(date-time)| 否 | 实收日期结束|实收日期结束|
|contractNo|query|string| 否 | 合同编号|合同编号|
|signDateStart|query|string(date-time)| 否 | 合同签订日期开始|合同签订日期开始|
|signDateEnd|query|string(date-time)| 否 | 合同签订日期结束|合同签订日期结束|
|contractLeaseUnit|query|string| 否 | 合同租赁单元|合同租赁单元|
|lesseeName|query|string| 否 | 承租人名称|承租人名称|
|cancelReason|query|integer(int32)| 否 | 作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|cancelByName|query|string| 否 | 作废人姓名|作废人姓名|
|cancelTimeStart|query|string(date-time)| 否 | 作废日期开始|作废日期开始|
|cancelTimeEnd|query|string(date-time)| 否 | 作废日期结束|作废日期结束|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdinvalidBooking"></a>

## POST 定单作废接口

POST /booking/invalid

> Body 请求参数

```json
{
  "id": "string",
  "cancelRemark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingInvalidDto](#schemabookinginvaliddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[AjaxResult](#schemaajaxresult)|

<a id="opIdcancelBooking"></a>

## POST 退定接口

POST /booking/cancel

> Body 请求参数

```json
{
  "id": "string",
  "cancelRemark": "string",
  "cancelEnclosure": "string",
  "isRefund": 0,
  "isSubmit": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingCancelDto](#schemabookingcanceldto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddetail"></a>

## GET 定单详情接口

GET /booking/detail/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||定单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","customerName":"string","propertyType":"string","roomId":"string","roomName":"string","bookingAmount":0,"receivableDate":"2019-08-24T14:15:22Z","expectSignDate":"2019-08-24T14:15:22Z","isRefundable":0,"cancelTime":"2019-08-24T14:15:22Z","cancelBy":"string","cancelByName":"string","cancelReason":0,"isRefund":0,"cancelEnclosure":"string","cancelRemark":"string","status":0,"contractId":"string","refundId":"string","createByName":"string","updateByName":"string","isDel":0,"contractNo":"string","signDate":"2019-08-24T14:15:22Z","contractLeaseUnit":"string","lesseeName":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[BookingVo](#schemabookingvo)|

<a id="opIddeleteBooking"></a>

## DELETE 删除定单接口

DELETE /booking/delete/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||定单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/用户管理接口

<a id="opIdresetPwd"></a>

## PUT 重置密码

PUT /user/resetPwd

重置密码

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "userId": 0,
  "deptId": 0,
  "userName": "string",
  "nickName": "string",
  "email": "string",
  "phonenumber": "string",
  "sex": "string",
  "avatar": "string",
  "password": "string",
  "status": "string",
  "delFlag": "string",
  "loginIp": "string",
  "loginDate": "2019-08-24T14:15:22Z",
  "remark": "string",
  "syncId": "string",
  "postName": "string",
  "roles": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "roleId": 0,
      "roleName": "string",
      "type": 0,
      "remark": "string",
      "roomPermissions": "string",
      "contractPermissions": "string",
      "menuIds": [
        0
      ],
      "deptIds": [
        0
      ],
      "permissions": [
        "string"
      ],
      "admin": true
    }
  ],
  "roleIds": [
    0
  ],
  "admin": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysUser](#schemasysuser)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdrecordlogin"></a>

## PUT recordlogin

PUT /user/recordlogin

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "userId": 0,
  "deptId": 0,
  "userName": "string",
  "nickName": "string",
  "email": "string",
  "phonenumber": "string",
  "sex": "string",
  "avatar": "string",
  "password": "string",
  "status": "string",
  "delFlag": "string",
  "loginIp": "string",
  "loginDate": "2019-08-24T14:15:22Z",
  "remark": "string",
  "syncId": "string",
  "postName": "string",
  "roles": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "roleId": 0,
      "roleName": "string",
      "type": 0,
      "remark": "string",
      "roomPermissions": "string",
      "contractPermissions": "string",
      "menuIds": [
        0
      ],
      "deptIds": [
        0
      ],
      "permissions": [
        "string"
      ],
      "admin": true
    }
  ],
  "roleIds": [
    0
  ],
  "admin": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysUser](#schemasysuser)| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

<a id="opIdlist"></a>

## POST 用户列表查询

POST /user/list

用户列表查询

> Body 请求参数

```json
{
  "nickName": "string",
  "userName": "string",
  "phonenumber": "string",
  "menuRoles": [
    0
  ],
  "dataRoles": [
    0
  ],
  "status": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysUserBo](#schemasysuserbo)| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdexport"></a>

## POST 用户列表导出

POST /user/export

用户列表导出

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|nickName|query|string| 否 | 人员名称|none|
|userName|query|string| 否 | 账号|none|
|phonenumber|query|string| 否 | 手机号|none|
|menuRoles|query|array[integer]| 否 | 功能角色|none|
|dataRoles|query|array[integer]| 否 | 数据角色|none|
|status|query|integer(int32)| 否 | 状态|none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdchangeStatus"></a>

## POST 用户状态修改

POST /user/changeStatus

用户状态修改

> Body 请求参数

```json
{
  "userId": 0,
  "status": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysUserChangeStatusBo](#schemasysuserchangestatusbo)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdinsertAuthRole"></a>

## POST 用户授权角色

POST /user/authRole

用户授权角色

> Body 请求参数

```json
{
  "ehrUserIdList": [
    "string"
  ],
  "roleIdList": [
    0
  ],
  "userIdList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysUserAuthRoleBo](#schemasysuserauthrolebo)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdinfo"></a>

## GET info

GET /user/info/{username}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|username|path|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"token":"string","userid":0,"username":"string","nickName":"string","loginTime":0,"expireTime":0,"ipaddr":"string","permissions":["string"],"projectPermissions":["string"],"roomPermissions":["string"],"contractPermission":"string","roles":["string"],"sysUser":{"userId":0,"deptId":0,"userName":"string","nickName":"string","email":"string","phonenumber":"string","sex":"string","avatar":"string","password":"string","status":"string","delFlag":"string","loginIp":"string","loginDate":"2019-08-24T14:15:22Z","roles":[{"roleId":0,"roleName":"string","type":0,"remark":"string","roomPermissions":"string","contractPermissions":"string","admin":true}],"admin":true},"admin":true}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RLoginUser](#schemarloginuser)|

<a id="opIdgetInfo"></a>

## GET 获取用户信息

GET /user/getInfo

获取用户信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdauthRole"></a>

## GET 用户详情

GET /user/authRole/{userId}

根据用户编号获取授权角色

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|userId|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/sys-profile-controller

<a id="opIdprofile"></a>

## GET profile

GET /user/profile

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdupdateProfile"></a>

## PUT updateProfile

PUT /user/profile

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "userId": 0,
  "deptId": 0,
  "userName": "string",
  "nickName": "string",
  "email": "string",
  "phonenumber": "string",
  "sex": "string",
  "avatar": "string",
  "password": "string",
  "status": "string",
  "delFlag": "string",
  "loginIp": "string",
  "loginDate": "2019-08-24T14:15:22Z",
  "remark": "string",
  "syncId": "string",
  "postName": "string",
  "roles": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "roleId": 0,
      "roleName": "string",
      "type": 0,
      "remark": "string",
      "roomPermissions": "string",
      "contractPermissions": "string",
      "menuIds": [
        0
      ],
      "deptIds": [
        0
      ],
      "permissions": [
        "string"
      ],
      "admin": true
    }
  ],
  "roleIds": [
    0
  ],
  "admin": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysUser](#schemasysuser)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdupdatePwd"></a>

## PUT updatePwd

PUT /user/profile/updatePwd

> Body 请求参数

```json
{
  "property1": "string",
  "property2": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|object| 否 ||none|
|» **additionalProperties**|body|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdavatar"></a>

## POST avatar

POST /user/profile/avatar

> Body 请求参数

```json
{
  "avatarfile": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|object| 否 ||none|
|» avatarfile|body|string(binary)| 是 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/角色管理接口

<a id="opIdedit"></a>

## PUT 角色修改

PUT /role

角色修改

> Body 请求参数

```json
{
  "roleId": 0,
  "roleName": "string",
  "type": 0,
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysRoleModel](#schemasysrolemodel)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd"></a>

## POST 角色新增

POST /role

角色新增

> Body 请求参数

```json
{
  "roleId": 0,
  "roleName": "string",
  "type": 0,
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysRoleModel](#schemasysrolemodel)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdallocatedList"></a>

## POST 查询已分配用户角色列表

POST /role/user/list

查询已分配用户角色列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "type": 0,
  "roleId": 0,
  "userName": "string",
  "userPhone": "string",
  "status": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysRoleUserQueryBo](#schemasysroleuserquerybo)| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdcancelAuthUser"></a>

## POST 取消授权用户

POST /role/user/cancel/bind

取消授权用户

> Body 请求参数

```json
{
  "roleId": 0,
  "userIdList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysRoleUserCancelBo](#schemasysroleusercancelbo)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdselectAuthUserAll"></a>

## POST 选择用户授权角色

POST /role/user/bind

选择用户授权角色

> Body 请求参数

```json
{
  "roleId": 0,
  "ehrUserIdList": [
    "string"
  ],
  "userIdList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysRoleUserBo](#schemasysroleuserbo)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdbindOrg"></a>

## POST 角色绑定组织

POST /role/org/bind

角色绑定组织

> Body 请求参数

```json
{
  "roleId": 0,
  "roomPermissions": [
    "string"
  ],
  "contractPermission": "string",
  "orgIdList": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysRoleOrgBo](#schemasysroleorgbo)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdbindMenu"></a>

## POST 角色绑定菜单

POST /role/menu/bind

角色绑定菜单

> Body 请求参数

```json
{
  "roleId": 0,
  "menuIdList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysRoleMenuBo](#schemasysrolemenubo)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetInfo_1"></a>

## GET 角色详情

GET /role/{roleId}

根据角色id获取详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|roleId|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetOrgByRoleId"></a>

## GET 角色组织

GET /role/org/list

根据角色获取组织id

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|roleId|query|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetByRoleId"></a>

## GET 角色菜单

GET /role/menu/list

根据角色获取菜单id

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|roleId|query|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_1"></a>

## GET 角色列表查询

GET /role/list

角色列表查询

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|type|query|integer(int32)| 是 | 角色类型|none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_1"></a>

## DELETE 角色删除

DELETE /role/{roleIds}

角色删除

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|roleIds|path|array[integer]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/sys-notice-controller

<a id="opIdedit_1"></a>

## PUT edit_1

PUT /notice

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "noticeId": 0,
  "noticeTitle": "string",
  "noticeType": "string",
  "noticeContent": "string",
  "status": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysNotice](#schemasysnotice)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_1"></a>

## POST add_1

POST /notice

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "noticeId": 0,
  "noticeTitle": "string",
  "noticeType": "string",
  "noticeContent": "string",
  "status": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysNotice](#schemasysnotice)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetInfo_2"></a>

## GET getInfo_2

GET /notice/{noticeId}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|noticeId|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_4"></a>

## GET list_4

GET /notice/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|createBy|query|string| 否 ||none|
|createByName|query|string| 否 ||none|
|createTime|query|string(date-time)| 否 ||none|
|updateBy|query|string| 否 ||none|
|updateByName|query|string| 否 ||none|
|updateTime|query|string(date-time)| 否 ||none|
|noticeId|query|integer(int64)| 否 ||none|
|noticeTitle|query|string| 是 ||none|
|noticeType|query|string| 否 ||none|
|noticeContent|query|string| 否 ||none|
|status|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdremove_3"></a>

## DELETE remove_3

DELETE /notice/{noticeIds}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|noticeIds|path|array[integer]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/菜单管理接口

<a id="opIdedit_2"></a>

## PUT edit_2

PUT /menu

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "menuId": 0,
  "menuName": "string",
  "parentName": "string",
  "parentId": 0,
  "orderNum": 0,
  "path": "string",
  "component": "string",
  "query": "string",
  "routeName": "string",
  "isFrame": "string",
  "isCache": "string",
  "menuType": "string",
  "visible": "string",
  "status": "string",
  "perms": "string",
  "icon": "string",
  "children": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "menuId": 0,
      "menuName": "string",
      "parentName": "string",
      "parentId": 0,
      "orderNum": 0,
      "path": "string",
      "component": "string",
      "query": "string",
      "routeName": "string",
      "isFrame": "string",
      "isCache": "string",
      "menuType": "string",
      "visible": "string",
      "status": "string",
      "perms": "string",
      "icon": "string",
      "children": [
        {
          "createBy": "string",
          "createByName": "string",
          "createTime": "2019-08-24T14:15:22Z",
          "updateBy": "string",
          "updateByName": "string",
          "updateTime": "2019-08-24T14:15:22Z",
          "menuId": 0,
          "menuName": "string",
          "parentName": "string",
          "parentId": 0,
          "orderNum": 0,
          "path": "string",
          "component": "string",
          "query": "string",
          "routeName": "string",
          "isFrame": "string",
          "isCache": "string",
          "menuType": "string",
          "visible": "string",
          "status": "string",
          "perms": "string",
          "icon": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysMenu](#schemasysmenu)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_2"></a>

## POST add_2

POST /menu

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "menuId": 0,
  "menuName": "string",
  "parentName": "string",
  "parentId": 0,
  "orderNum": 0,
  "path": "string",
  "component": "string",
  "query": "string",
  "routeName": "string",
  "isFrame": "string",
  "isCache": "string",
  "menuType": "string",
  "visible": "string",
  "status": "string",
  "perms": "string",
  "icon": "string",
  "children": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "menuId": 0,
      "menuName": "string",
      "parentName": "string",
      "parentId": 0,
      "orderNum": 0,
      "path": "string",
      "component": "string",
      "query": "string",
      "routeName": "string",
      "isFrame": "string",
      "isCache": "string",
      "menuType": "string",
      "visible": "string",
      "status": "string",
      "perms": "string",
      "icon": "string",
      "children": [
        {
          "createBy": "string",
          "createByName": "string",
          "createTime": "2019-08-24T14:15:22Z",
          "updateBy": "string",
          "updateByName": "string",
          "updateTime": "2019-08-24T14:15:22Z",
          "menuId": 0,
          "menuName": "string",
          "parentName": "string",
          "parentId": 0,
          "orderNum": 0,
          "path": "string",
          "component": "string",
          "query": "string",
          "routeName": "string",
          "isFrame": "string",
          "isCache": "string",
          "menuType": "string",
          "visible": "string",
          "status": "string",
          "perms": "string",
          "icon": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysMenu](#schemasysmenu)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetInfo_3"></a>

## GET 菜单详情

GET /menu/{menuId}

根据菜单编号获取详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|menuId|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove"></a>

## DELETE remove

DELETE /menu/{menuId}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|menuId|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdtreeselect"></a>

## GET treeselect

GET /menu/treeselect

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|menuId|query|integer(int64)| 否 ||none|
|menuName|query|string| 否 ||none|
|parentName|query|string| 否 ||none|
|parentId|query|integer(int64)| 否 ||none|
|orderNum|query|integer(int32)| 否 ||none|
|path|query|string| 否 ||none|
|component|query|string| 否 ||none|
|query|query|string| 否 ||none|
|routeName|query|string| 否 ||none|
|isFrame|query|string| 否 ||none|
|isCache|query|string| 否 ||none|
|menuType|query|string| 否 ||none|
|visible|query|string| 否 ||none|
|status|query|string| 否 ||none|
|perms|query|string| 否 ||none|
|icon|query|string| 否 ||none|
|children|query|array[object]| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdroleMenuTreeselect"></a>

## GET roleMenuTreeselect

GET /menu/roleMenuTreeselect/{roleId}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|roleId|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_5"></a>

## GET 菜单列表查询

GET /menu/list

菜单列表查询

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|menuId|query|integer(int64)| 否 ||none|
|menuName|query|string| 否 ||none|
|parentName|query|string| 否 ||none|
|parentId|query|integer(int64)| 否 ||none|
|orderNum|query|integer(int32)| 否 ||none|
|path|query|string| 否 ||none|
|component|query|string| 否 ||none|
|query|query|string| 否 ||none|
|routeName|query|string| 否 ||none|
|isFrame|query|string| 否 ||none|
|isCache|query|string| 否 ||none|
|menuType|query|string| 否 ||none|
|visible|query|string| 否 ||none|
|status|query|string| 否 ||none|
|perms|query|string| 否 ||none|
|icon|query|string| 否 ||none|
|children|query|array[object]| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetRouters"></a>

## GET getRouters

GET /menu/getRouters

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetByRoleId_1"></a>

## GET getByRoleId_1

GET /menu/getByRoleId/{roleId}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|roleId|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":[0]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RListLong](#schemarlistlong)|

# 资管平台/sys-dict-type-controller

<a id="opIdedit_3"></a>

## PUT edit_3

PUT /dict/type

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "dictId": 0,
  "dictName": "string",
  "dictType": "string",
  "status": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysDictType](#schemasysdicttype)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_3"></a>

## POST add_3

POST /dict/type

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "dictId": 0,
  "dictName": "string",
  "dictType": "string",
  "status": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysDictType](#schemasysdicttype)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_3"></a>

## POST export_3

POST /dict/type/export

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|dictId|query|integer(int64)| 否 ||none|
|dictName|query|string| 否 ||none|
|dictType|query|string| 否 ||none|
|status|query|string| 否 ||none|
|remark|query|string| 否 ||none|
|beginTime|query|string| 否 ||none|
|endTime|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdgetInfo_4"></a>

## GET getInfo_4

GET /dict/type/{dictId}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|dictId|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdoptionselect"></a>

## GET optionselect

GET /dict/type/optionselect

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_7"></a>

## GET list_7

GET /dict/type/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|dictId|query|integer(int64)| 否 ||none|
|dictName|query|string| 否 ||none|
|dictType|query|string| 否 ||none|
|status|query|string| 否 ||none|
|remark|query|string| 否 ||none|
|beginTime|query|string| 否 ||none|
|endTime|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdremove_5"></a>

## DELETE remove_5

DELETE /dict/type/{dictIds}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|dictIds|path|array[integer]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdrefreshCache"></a>

## DELETE refreshCache

DELETE /dict/type/refreshCache

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/sys-dict-data-controller

<a id="opIdedit_4"></a>

## PUT edit_4

PUT /dict/data

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "dictCode": 0,
  "dictSort": 0,
  "dictLabel": "string",
  "dictValue": "string",
  "dictType": "string",
  "cssClass": "string",
  "listClass": "string",
  "isDefault": "string",
  "status": "string",
  "remark": "string",
  "default": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysDictData](#schemasysdictdata)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_4"></a>

## POST add_4

POST /dict/data

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "dictCode": 0,
  "dictSort": 0,
  "dictLabel": "string",
  "dictValue": "string",
  "dictType": "string",
  "cssClass": "string",
  "listClass": "string",
  "isDefault": "string",
  "status": "string",
  "remark": "string",
  "default": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysDictData](#schemasysdictdata)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_4"></a>

## POST export_4

POST /dict/data/export

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|dictCode|query|integer(int64)| 否 ||none|
|dictSort|query|integer(int64)| 否 ||none|
|dictLabel|query|string| 否 ||none|
|dictValue|query|string| 否 ||none|
|dictType|query|string| 否 ||none|
|cssClass|query|string| 否 ||none|
|listClass|query|string| 否 ||none|
|isDefault|query|string| 否 ||none|
|status|query|string| 否 ||none|
|remark|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdgetInfo_5"></a>

## GET getInfo_5

GET /dict/data/{dictCode}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|dictCode|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddictType"></a>

## GET dictType

GET /dict/data/type/{dictType}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|dictType|path|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_8"></a>

## GET list_8

GET /dict/data/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|dictCode|query|integer(int64)| 否 ||none|
|dictSort|query|integer(int64)| 否 ||none|
|dictLabel|query|string| 否 ||none|
|dictValue|query|string| 否 ||none|
|dictType|query|string| 否 ||none|
|cssClass|query|string| 否 ||none|
|listClass|query|string| 否 ||none|
|isDefault|query|string| 否 ||none|
|status|query|string| 否 ||none|
|remark|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdremove_6"></a>

## DELETE remove_6

DELETE /dict/data/{dictCodes}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|dictCodes|path|array[integer]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/sys-config-controller

<a id="opIdedit_5"></a>

## PUT edit_5

PUT /config

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "configId": 0,
  "configName": "string",
  "configKey": "string",
  "configValue": "string",
  "configType": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysConfig](#schemasysconfig)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_5"></a>

## POST add_5

POST /config

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "configId": 0,
  "configName": "string",
  "configKey": "string",
  "configValue": "string",
  "configType": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysConfig](#schemasysconfig)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_5"></a>

## POST export_5

POST /config/export

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|configId|query|integer(int64)| 否 ||none|
|configName|query|string| 否 ||none|
|configKey|query|string| 否 ||none|
|configValue|query|string| 否 ||none|
|configType|query|string| 否 ||none|
|beginTime|query|string| 否 ||none|
|endTime|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdgetInfo_6"></a>

## GET getInfo_6

GET /config/{configId}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|configId|path|integer(int64)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_9"></a>

## GET list_9

GET /config/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|configId|query|integer(int64)| 否 ||none|
|configName|query|string| 否 ||none|
|configKey|query|string| 否 ||none|
|configValue|query|string| 否 ||none|
|configType|query|string| 否 ||none|
|beginTime|query|string| 否 ||none|
|endTime|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgetConfigKey"></a>

## GET getConfigKey

GET /config/configKey/{configKey}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|configKey|path|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_7"></a>

## DELETE remove_7

DELETE /config/{configIds}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|configIds|path|array[integer]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdrefreshCache_1"></a>

## DELETE refreshCache_1

DELETE /config/refreshCache

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/sys-operlog-controller

<a id="opIdadd_6"></a>

## POST add_6

POST /operlog

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "operId": 0,
  "title": "string",
  "businessType": 0,
  "businessTypes": [
    0
  ],
  "method": "string",
  "requestMethod": "string",
  "operatorType": 0,
  "operName": "string",
  "deptName": "string",
  "operUrl": "string",
  "operIp": "string",
  "operParam": "string",
  "jsonResult": "string",
  "status": 0,
  "errorMsg": "string",
  "operTime": "2019-08-24T14:15:22Z",
  "costTime": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysOperLog](#schemasysoperlog)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_1"></a>

## POST export_1

POST /operlog/export

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|operId|query|integer(int64)| 否 ||none|
|title|query|string| 否 ||none|
|businessType|query|integer(int32)| 否 ||none|
|businessTypes|query|array[integer]| 否 ||none|
|method|query|string| 否 ||none|
|requestMethod|query|string| 否 ||none|
|operatorType|query|integer(int32)| 否 ||none|
|operName|query|string| 否 ||none|
|deptName|query|string| 否 ||none|
|operUrl|query|string| 否 ||none|
|operIp|query|string| 否 ||none|
|operParam|query|string| 否 ||none|
|jsonResult|query|string| 否 ||none|
|status|query|integer(int32)| 否 ||none|
|errorMsg|query|string| 否 ||none|
|operTime|query|string(date-time)| 否 ||none|
|costTime|query|integer(int64)| 否 ||none|
|beginTime|query|string| 否 ||none|
|endTime|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdlist_2"></a>

## GET list_2

GET /operlog/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|operId|query|integer(int64)| 否 ||none|
|title|query|string| 否 ||none|
|businessType|query|integer(int32)| 否 ||none|
|businessTypes|query|array[integer]| 否 ||none|
|method|query|string| 否 ||none|
|requestMethod|query|string| 否 ||none|
|operatorType|query|integer(int32)| 否 ||none|
|operName|query|string| 否 ||none|
|deptName|query|string| 否 ||none|
|operUrl|query|string| 否 ||none|
|operIp|query|string| 否 ||none|
|operParam|query|string| 否 ||none|
|jsonResult|query|string| 否 ||none|
|status|query|integer(int32)| 否 ||none|
|errorMsg|query|string| 否 ||none|
|operTime|query|string(date-time)| 否 ||none|
|costTime|query|integer(int64)| 否 ||none|
|beginTime|query|string| 否 ||none|
|endTime|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdremove_2"></a>

## DELETE remove_2

DELETE /operlog/{operIds}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|operIds|path|array[integer]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdclean"></a>

## DELETE clean

DELETE /operlog/clean

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/sys-logininfor-controller

<a id="opIdadd_7"></a>

## POST add_7

POST /logininfor

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "infoId": 0,
  "userName": "string",
  "status": "string",
  "ipaddr": "string",
  "msg": "string",
  "accessTime": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysLogininfor](#schemasyslogininfor)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_2"></a>

## POST export_2

POST /logininfor/export

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|infoId|query|integer(int64)| 否 ||none|
|userName|query|string| 否 ||none|
|status|query|string| 否 ||none|
|ipaddr|query|string| 否 ||none|
|msg|query|string| 否 ||none|
|accessTime|query|string(date-time)| 否 ||none|
|beginTime|query|string| 否 ||none|
|endTime|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdunlock"></a>

## GET unlock

GET /logininfor/unlock/{userName}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|userName|path|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_6"></a>

## GET list_6

GET /logininfor/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|infoId|query|integer(int64)| 否 ||none|
|userName|query|string| 否 ||none|
|status|query|string| 否 ||none|
|ipaddr|query|string| 否 ||none|
|msg|query|string| 否 ||none|
|accessTime|query|string(date-time)| 否 ||none|
|beginTime|query|string| 否 ||none|
|endTime|query|string| 否 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdremove_4"></a>

## DELETE remove_4

DELETE /logininfor/{infoIds}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|infoIds|path|array[integer]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdclean_1"></a>

## DELETE clean_1

DELETE /logininfor/clean

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/ehr-org-user-controller

<a id="opIdidmUserList"></a>

## POST idmUserList

POST /ehr/userList

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "orgList": [
    "string"
  ],
  "keyword": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[EhrUserQueryBO](#schemaehruserquerybo)| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdidmOrgList"></a>

## GET idmOrgList

GET /ehr/org

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|orgId|query|string| 是 ||none|
|keyword|query|string| 是 ||none|
|type|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/组织管理接口

<a id="opIdtree"></a>

## GET 获取当前人拥有的组织树

GET /org/tree

获取当前人拥有的组织树

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|name|query|string| 否 | 组织名称|none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/sys-user-online-controller

<a id="opIdlist_3"></a>

## GET list_3

GET /online/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ipaddr|query|string| 是 ||none|
|userName|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdforceLogout"></a>

## DELETE forceLogout

DELETE /online/{tokenId}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|tokenId|path|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 资管平台/sync-mdm-controller

<a id="opIdgetOrgList"></a>

## GET getOrgList

GET /esb/sync/mdm/org

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

# 资管平台/sync-ehr-controller

<a id="opIdgetUserList"></a>

## GET getUserList

GET /esb/sync/ehr/user

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

<a id="opIdgetPostList"></a>

## GET getPostList

GET /esb/sync/ehr/post

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

<a id="opIdgetOrgList_1"></a>

## GET getOrgList_1

GET /esb/sync/ehr/org

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

# 数据模型

<h2 id="tocS_RoomAddDTO">RoomAddDTO</h2>

<a id="schemaroomadddto"></a>
<a id="schema_RoomAddDTO"></a>
<a id="tocSroomadddto"></a>
<a id="tocsroomadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "roomName": "string",
  "type": "string",
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "roomCode": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "areaType": "string",
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": "string",
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": "string",
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": "string",
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": 0,
  "selfUseSubject": "string",
  "selfUsePurpose": "string",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|roomName|string|false|none|房源名称|房源名称|
|type|string|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|string|false|none|物业类型，二级字典|物业类型，二级字典|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|地块id|地块id|
|parcelName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋id|楼栋id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层id|楼层id|
|floorName|string|false|none|楼层名称|楼层名称|
|roomId|string|false|none|房间id， sys_room表的id|房间id， sys_room表的id|
|status|integer(int64)|false|none|状态（0草稿 10待生效 20生效中 30已失效）|状态（0草稿 10待生效 20生效中 30已失效）|
|roomCode|string|false|none|合成编码|合成编码|
|planEffectDate|string(date-time)|false|none|预计生效日期|预计生效日期|
|actualEffectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|areaType|string|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|rentAreaType|string|false|none|计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|rentArea|number|false|none|计租面积|计租面积|
|storey|number|false|none|层高|层高|
|value|number|false|none|荷载值|荷载值|
|orientation|string|false|none|朝向|朝向|
|houseTypeId|string|false|none|户型id|户型id|
|smartWaterMeter|string|false|none|智能水电表|智能水电表|
|smartLock|string|false|none|智能锁|智能锁|
|assetOperationMode|string|false|none|资产运营模式|资产运营模式|
|assetOperationType|string|false|none|资产运营分类|资产运营分类|
|propertyStatus|string|false|none|产权情况|产权情况|
|paymentStatus|string|false|none|交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|specialTag|string|false|none|特殊标签（1保障房）|特殊标签（1保障房）|
|latestRentPrice|number|false|none|商管承租成本单价（最新）|商管承租成本单价（最新）|
|latestRentStartDate|string(date-time)|false|none|商管承租起计日期（最新）|商管承租起计日期（最新）|
|latestRentEndDate|string(date-time)|false|none|商管承租到期日期（最新）|商管承租到期日期（最新）|
|firstRentPrice|number|false|none|商管承租成本单价第一次|商管承租成本单价第一次|
|firstRentStartDate|string(date-time)|false|none|商管承租起计日期第一次|商管承租起计日期第一次|
|firstRentEndDate|string(date-time)|false|none|商管承租到期日期第一次|商管承租到期日期第一次|
|operationSubject|string|false|none|运营主体（1商服 2众创城）|运营主体（1商服 2众创城）|
|rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|isSelfUse|integer(int32)|false|none|是否自用（0否 1是）|是否自用（0否 1是）|
|selfUseSubject|string|false|none|自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|selfUsePurpose|string|false|none|自用用途|自用用途|
|isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_BookingAddDTO">BookingAddDTO</h2>

<a id="schemabookingadddto"></a>
<a id="schema_BookingAddDTO"></a>
<a id="tocSbookingadddto"></a>
<a id="tocsbookingadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "isDel": 0,
  "isSubmit": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|customerName|string|false|none|客户名称|客户名称|
|propertyType|string|false|none|意向物业类型|意向物业类型|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房源名称|房源名称|
|bookingAmount|number|false|none|定单金额|定单金额|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|cancelEnclosure|string|false|none|退定附件|退定附件|
|cancelRemark|string|false|none|退定说明|退定说明|
|status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|contractId|string|false|none|转签约合同id|转签约合同id|
|refundId|string|false|none|退款单id|退款单id|
|isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|
|isSubmit|integer(int32)|false|none|0-暂存,1-提交|0-暂存,1-提交|

<h2 id="tocS_BookingInvalidDto">BookingInvalidDto</h2>

<a id="schemabookinginvaliddto"></a>
<a id="schema_BookingInvalidDto"></a>
<a id="tocSbookinginvaliddto"></a>
<a id="tocsbookinginvaliddto"></a>

```json
{
  "id": "string",
  "cancelRemark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|定单ID|需要作废的定单ID|
|cancelRemark|string|false|none|作废说明|定单作废的说明|

<h2 id="tocS_BookingCancelDto">BookingCancelDto</h2>

<a id="schemabookingcanceldto"></a>
<a id="schema_BookingCancelDto"></a>
<a id="tocSbookingcanceldto"></a>
<a id="tocsbookingcanceldto"></a>

```json
{
  "id": "string",
  "cancelRemark": "string",
  "cancelEnclosure": "string",
  "isRefund": 0,
  "isSubmit": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|订单ID|需要退定的订单ID|
|cancelRemark|string|false|none|退定说明|退定的具体说明|
|cancelEnclosure|string|false|none|退定附件|退定相关的附件|
|isRefund|integer(int32)|false|none|是否退款|是否退款，0-否，1-是|
|isSubmit|integer(int32)|false|none|是否提交|使用该字段判断是暂存还是提交，0-暂存，1-提交|

<h2 id="tocS_SysBuildingAddDTO">SysBuildingAddDTO</h2>

<a id="schemasysbuildingadddto"></a>
<a id="schema_SysBuildingAddDTO"></a>
<a id="tocSsysbuildingadddto"></a>
<a id="tocssysbuildingadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "source": "string",
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "isDel": 0
}

```

楼栋列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|source|string|false|none|来源（1主数据 2手动添加）|来源（1主数据 2手动添加）|
|mdmBuildingId|string|false|none|主数据楼栋id|主数据楼栋id|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|所属地块id|所属地块id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|upFloorNums|integer(int32)|false|none|地上楼层数|地上楼层数|
|underFloorNums|integer(int32)|false|none|地下楼层数|地下楼层数|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|isDel|integer(int32)|false|none|是否删除（0否 1是）|是否删除（0否 1是）|

<h2 id="tocS_SysParcelAddDTO">SysParcelAddDTO</h2>

<a id="schemasysparceladddto"></a>
<a id="schema_SysParcelAddDTO"></a>
<a id="tocSsysparceladddto"></a>
<a id="tocssysparceladddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmParcelId": "string",
  "projectId": "string",
  "stageId": "string",
  "stageName": "string",
  "parcelName": "string",
  "mdmNatureName": "string",
  "landUsage": 0,
  "mdmAddress": "string",
  "address": "string",
  "totalSelfArea": "string",
  "isDel": 0
}

```

地块列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|mdmParcelId|string|false|none|主数据地块id|主数据地块id|
|projectId|string|false|none|项目id|项目id|
|stageId|string|false|none|分期id|分期id|
|stageName|string|false|none|分期名称|分期名称|
|parcelName|string|false|none|地块名称|地块名称|
|mdmNatureName|string|false|none|主数据用地性质名称|主数据用地性质名称|
|landUsage|integer(int32)|false|none|用地性质（1工业用地 2商业用地）|用地性质（1工业用地 2商业用地）|
|mdmAddress|string|false|none|主数据地址|主数据地址|
|address|string|false|none|地址|地址|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|isDel|integer(int32)|false|none|是否删除（0否 1是）|是否删除（0否 1是）|

<h2 id="tocS_SysProjectAddDTO">SysProjectAddDTO</h2>

<a id="schemasysprojectadddto"></a>
<a id="schema_SysProjectAddDTO"></a>
<a id="tocSsysprojectadddto"></a>
<a id="tocssysprojectadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "type": "string",
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": "string",
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "isDel": 0,
  "stageList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmStageId": "string",
      "projectId": "string",
      "stageName": "string",
      "isDel": 0
    }
  ],
  "parcelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmParcelId": "string",
      "projectId": "string",
      "stageId": "string",
      "stageName": "string",
      "parcelName": "string",
      "mdmNatureName": "string",
      "landUsage": 0,
      "mdmAddress": "string",
      "address": "string",
      "totalSelfArea": "string",
      "isDel": 0
    }
  ],
  "buildingList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "source": "string",
      "mdmBuildingId": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingName": "string",
      "upFloorNums": 0,
      "underFloorNums": 0,
      "totalSelfArea": "string",
      "isDel": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|type|string|false|none|项目类型（1内部 2外部）|项目类型（1内部 2外部）|
|mdmProjectId|string|false|none|主数据项目ID|主数据项目ID|
|mdmName|string|false|none|主数据项目名称|主数据项目名称|
|mdmSaleName|string|false|none|主数据销售推广名|主数据销售推广名|
|code|string|false|none|编码|编码|
|name|string|false|none|简称|简称|
|provinceCode|string|false|none|省编码|省编码|
|provinceName|string|false|none|省名称|省名称|
|cityCode|string|false|none|市编码|市编码|
|cityName|string|false|none|市名称|市名称|
|countryCode|string|false|none|区编码|区编码|
|countryName|string|false|none|区名称|区名称|
|mdmTypeName|string|false|none|主数据项目类型|主数据项目类型|
|assetType|string|false|none|项目资产分类（1众创城 2科技城 3产城 4外部项目）|项目资产分类（1众创城 2科技城 3产城 4外部项目）|
|propertyUnit|string|false|none|产权单位|产权单位|
|projectAddress|string|false|none|项目地址|项目地址|
|longitude|string|false|none|地址经度|地址经度|
|latitude|string|false|none|地址维度|地址维度|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|
|stageList|[[SysStageAddDTO](#schemasysstageadddto)]|false|none|分期列表|分期列表|
|parcelList|[[SysParcelAddDTO](#schemasysparceladddto)]|false|none|地块列表|地块列表|
|buildingList|[[SysBuildingAddDTO](#schemasysbuildingadddto)]|false|none|楼栋列表|楼栋列表|

<h2 id="tocS_SysStageAddDTO">SysStageAddDTO</h2>

<a id="schemasysstageadddto"></a>
<a id="schema_SysStageAddDTO"></a>
<a id="tocSsysstageadddto"></a>
<a id="tocssysstageadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmStageId": "string",
  "projectId": "string",
  "stageName": "string",
  "isDel": 0
}

```

分期列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|mdmStageId|string|false|none|主数据分期id|主数据分期id|
|projectId|string|false|none|项目id|项目id|
|stageName|string|false|none|分期名称|分期名称|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SysProjectQueryDTO">SysProjectQueryDTO</h2>

<a id="schemasysprojectquerydto"></a>
<a id="schema_SysProjectQueryDTO"></a>
<a id="tocSsysprojectquerydto"></a>
<a id="tocssysprojectquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "type": "string",
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": "string",
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|type|string|false|none|项目类型（1内部 2外部）|项目类型（1内部 2外部）|
|mdmProjectId|string|false|none|主数据项目ID|主数据项目ID|
|mdmName|string|false|none|主数据项目名称|主数据项目名称|
|mdmSaleName|string|false|none|主数据销售推广名|主数据销售推广名|
|code|string|false|none|编码|编码|
|name|string|false|none|简称|简称|
|provinceCode|string|false|none|省编码|省编码|
|provinceName|string|false|none|省名称|省名称|
|cityCode|string|false|none|市编码|市编码|
|cityName|string|false|none|市名称|市名称|
|countryCode|string|false|none|区编码|区编码|
|countryName|string|false|none|区名称|区名称|
|mdmTypeName|string|false|none|主数据项目类型|主数据项目类型|
|assetType|string|false|none|项目资产分类（1众创城 2科技城 3产城 4外部项目）|项目资产分类（1众创城 2科技城 3产城 4外部项目）|
|propertyUnit|string|false|none|产权单位|产权单位|
|projectAddress|string|false|none|项目地址|项目地址|
|longitude|string|false|none|地址经度|地址经度|
|latitude|string|false|none|地址维度|地址维度|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|boolean|false|none||none|

<h2 id="tocS_PrintTemplateAddDTO">PrintTemplateAddDTO</h2>

<a id="schemaprinttemplateadddto"></a>
<a id="schema_PrintTemplateAddDTO"></a>
<a id="tocSprinttemplateadddto"></a>
<a id="tocsprinttemplateadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "templateName": "string",
  "projectId": "string",
  "applyLevel": "string",
  "applyScope": "string",
  "printType": "string",
  "contractPurpose": "string",
  "effectiveDate": "2019-08-24T14:15:22Z",
  "expirationDate": "2019-08-24T14:15:22Z",
  "status": "string",
  "linkUrl": "string",
  "remark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|templateName|string|false|none|模版名称|模版名称|
|projectId|string|false|none|项目ID|项目ID|
|applyLevel|string|false|none|适用层级（1集团 2项目）|适用层级（1集团 2项目）|
|applyScope|string|false|none|适用范围， 适用层级为项目时，传项目名称|适用范围， 适用层级为项目时，传项目名称|
|printType|string|false|none|套打类型（1合同 2协议）|套打类型（1合同 2协议）|
|contractPurpose|string|false|none|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|
|effectiveDate|string(date-time)|false|none|生效日期|生效日期|
|expirationDate|string(date-time)|false|none|失效日期|失效日期|
|status|string|false|none|状态（0新增 1生效 2失效）|状态（0新增 1生效 2失效）|
|linkUrl|string|false|none|链接地址|链接地址|
|remark|string|false|none|备注|备注|

<h2 id="tocS_PrintTemplateDictAddDTO">PrintTemplateDictAddDTO</h2>

<a id="schemaprinttemplatedictadddto"></a>
<a id="schema_PrintTemplateDictAddDTO"></a>
<a id="tocSprinttemplatedictadddto"></a>
<a id="tocsprinttemplatedictadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "parentId": "string",
  "name": "string",
  "type": 0,
  "code": "string",
  "dbField": "string",
  "sqlText": "string",
  "remark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|parentId|string|false|none|父字典id|父字典id|
|name|string|false|none|名称|名称|
|type|integer(int32)|false|none|字段类型（0字段 1表格）|字段类型（0字段 1表格）|
|code|string|false|none|模板字段code|模板字段code|
|dbField|string|false|none|数据库字段|数据库字段|
|sqlText|string|false|none|sql语句|sql语句|
|remark|string|false|none|字典解释说明|字典解释说明|

<h2 id="tocS_SysProject">SysProject</h2>

<a id="schemasysproject"></a>
<a id="schema_SysProject"></a>
<a id="tocSsysproject"></a>
<a id="tocssysproject"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": 0,
  "projectName": "string",
  "projectGroup": "string",
  "projectType": "string",
  "projectCode": "string",
  "companyId": 0,
  "projectLocation": "string",
  "projectAddress": "string",
  "projectCover": "string",
  "deleteFlg": "string",
  "projectProvinceCode": "string",
  "projectProvinceName": "string",
  "projectCityCode": "string",
  "projectCityName": "string",
  "projectDistrictCode": "string",
  "projectDistrictName": "string",
  "wdProjId": "string",
  "oldCompanyId": "string",
  "lat": "string",
  "lng": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|integer(int64)|false|none||none|
|projectName|string|false|none||none|
|projectGroup|string|false|none||none|
|projectType|string|false|none||none|
|projectCode|string|false|none||none|
|companyId|integer(int64)|false|none||none|
|projectLocation|string|false|none||none|
|projectAddress|string|false|none||none|
|projectCover|string|false|none||none|
|deleteFlg|string|false|none||none|
|projectProvinceCode|string|false|none||none|
|projectProvinceName|string|false|none||none|
|projectCityCode|string|false|none||none|
|projectCityName|string|false|none||none|
|projectDistrictCode|string|false|none||none|
|projectDistrictName|string|false|none||none|
|wdProjId|string|false|none||none|
|oldCompanyId|string|false|none||none|
|lat|string|false|none||none|
|lng|string|false|none||none|

<h2 id="tocS_FixedAssetsAddDTO">FixedAssetsAddDTO</h2>

<a id="schemafixedassetsadddto"></a>
<a id="schema_FixedAssetsAddDTO"></a>
<a id="tocSfixedassetsadddto"></a>
<a id="tocsfixedassetsadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "category": "string",
  "name": "string",
  "specification": "string",
  "attachments": "string",
  "usageScope": "string",
  "remark": "string",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|category|string|false|none|种类|种类|
|name|string|false|none|物品名称|物品名称|
|specification|string|false|none|规格|规格|
|attachments|string|false|none|附件|附件|
|usageScope|string|false|none|使用范围|使用范围|
|remark|string|false|none|备注|备注|
|isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_PrintTemplateStatusDto">PrintTemplateStatusDto</h2>

<a id="schemaprinttemplatestatusdto"></a>
<a id="schema_PrintTemplateStatusDto"></a>
<a id="tocSprinttemplatestatusdto"></a>
<a id="tocsprinttemplatestatusdto"></a>

```json
{
  "templateId": "123456",
  "status": "1"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|templateId|string|false|none||模版id|
|status|string|false|none||模版状态|

<h2 id="tocS_PrintTemplateQueryDTO">PrintTemplateQueryDTO</h2>

<a id="schemaprinttemplatequerydto"></a>
<a id="schema_PrintTemplateQueryDTO"></a>
<a id="tocSprinttemplatequerydto"></a>
<a id="tocsprinttemplatequerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "templateName": "string",
  "projectId": "string",
  "applyLevel": "string",
  "applyScope": "string",
  "printType": "string",
  "contractPurpose": "string",
  "effectiveDate": "2019-08-24T14:15:22Z",
  "expirationDate": "2019-08-24T14:15:22Z",
  "status": "string",
  "linkUrl": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|templateName|string|false|none|模版名称|模版名称|
|projectId|string|false|none|项目ID|项目ID|
|applyLevel|string|false|none|适用层级（1集团 2项目）|适用层级（1集团 2项目）|
|applyScope|string|false|none|适用范围|适用范围|
|printType|string|false|none|套打类型（1合同 2协议）|套打类型（1合同 2协议）|
|contractPurpose|string|false|none|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|
|effectiveDate|string(date-time)|false|none|生效日期|生效日期|
|expirationDate|string(date-time)|false|none|失效日期|失效日期|
|status|string|false|none|状态（0新增 1生效 2失效）|状态（0新增 1生效 2失效）|
|linkUrl|string|false|none|链接地址|链接地址|
|remark|string|false|none|备注|备注|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|

<h2 id="tocS_PrintTemplateDictQueryDTO">PrintTemplateDictQueryDTO</h2>

<a id="schemaprinttemplatedictquerydto"></a>
<a id="schema_PrintTemplateDictQueryDTO"></a>
<a id="tocSprinttemplatedictquerydto"></a>
<a id="tocsprinttemplatedictquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "parentId": "string",
  "name": "string",
  "type": 0,
  "code": "string",
  "dbField": "string",
  "sqlText": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|parentId|string|false|none|父字典id|父字典id|
|name|string|false|none|名称|名称|
|type|integer(int32)|false|none|字段类型（0字段 1表格）|字段类型（0字段 1表格）|
|code|string|false|none|模板字段code|模板字段code|
|dbField|string|false|none|数据库字段|数据库字段|
|sqlText|string|false|none|sql语句|sql语句|
|remark|string|false|none|字典解释说明|字典解释说明|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|

<h2 id="tocS_FixedAssetsQueryDTO">FixedAssetsQueryDTO</h2>

<a id="schemafixedassetsquerydto"></a>
<a id="schema_FixedAssetsQueryDTO"></a>
<a id="tocSfixedassetsquerydto"></a>
<a id="tocsfixedassetsquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "category": "string",
  "name": "string",
  "specification": "string",
  "attachments": "string",
  "usageScope": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|category|string|false|none|种类|种类|
|name|string|false|none|物品名称|物品名称|
|specification|string|false|none|规格|规格|
|attachments|string|false|none|附件|附件|
|usageScope|string|false|none|使用范围|使用范围|
|remark|string|false|none|备注|备注|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_CustomerQueryDTO">CustomerQueryDTO</h2>

<a id="schemacustomerquerydto"></a>
<a id="schema_CustomerQueryDTO"></a>
<a id="tocScustomerquerydto"></a>
<a id="tocscustomerquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "customerType": 0,
  "customerName": "string",
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "contactAddress": "string",
  "maintainerId": "string",
  "maintainerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|customerType|integer(int64)|false|none|客户类型，个人：1，企业：2|客户类型，个人：1，企业：2|
|customerName|string|false|none|客户姓名，支持身份证识别填充|客户姓名，支持身份证识别填充|
|creditCode|string|false|none|统一社会信用代码，需校验唯一性|统一社会信用代码，需校验唯一性|
|legalName|string|false|none|法人姓名|法人姓名|
|contactPhone|string|false|none|联系电话，需校验唯一性|联系电话，需校验唯一性|
|idType|string|false|none|证件类型，身份证/护照等|证件类型，身份证/护照等|
|idNumber|string|false|none|证件号码，支持身份证识别填充|证件号码，支持身份证识别填充|
|idValidityStart|string(date-time)|false|none|证件有效期开始，支持身份证识别填充|证件有效期开始，支持身份证识别填充|
|idValidityEnd|string(date-time)|false|none|证件有效期结束，支持身份证识别填充|证件有效期结束，支持身份证识别填充|
|idFront|string|false|none|身份证正面地址|身份证正面地址|
|idBack|string|false|none|身份证反面地址|身份证反面地址|
|contactAddress|string|false|none|联系地址，支持身份证识别填充|联系地址，支持身份证识别填充|
|maintainerId|string|false|none|维护人id|维护人id|
|maintainerName|string|false|none|维护人姓名|维护人姓名|
|attachmentFiles|string|false|none|附件|附件|
|remark|string|false|none|备注|备注|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerVo">CustomerVo</h2>

<a id="schemacustomervo"></a>
<a id="schema_CustomerVo"></a>
<a id="tocScustomervo"></a>
<a id="tocscustomervo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "customerType": 0,
  "customerName": "string",
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "contactAddress": "string",
  "maintainerId": "string",
  "maintainerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|customerType|integer(int64)|false|none|客户类型，个人：1，企业：2|客户类型，个人：1，企业：2|
|customerName|string|false|none|客户姓名，支持身份证识别填充|客户姓名，支持身份证识别填充|
|creditCode|string|false|none|统一社会信用代码，需校验唯一性|统一社会信用代码，需校验唯一性|
|legalName|string|false|none|法人姓名|法人姓名|
|contactPhone|string|false|none|联系电话，需校验唯一性|联系电话，需校验唯一性|
|idType|string|false|none|证件类型，身份证/护照等|证件类型，身份证/护照等|
|idNumber|string|false|none|证件号码，支持身份证识别填充|证件号码，支持身份证识别填充|
|idValidityStart|string(date-time)|false|none|证件有效期开始，支持身份证识别填充|证件有效期开始，支持身份证识别填充|
|idValidityEnd|string(date-time)|false|none|证件有效期结束，支持身份证识别填充|证件有效期结束，支持身份证识别填充|
|idFront|string|false|none|身份证正面地址|身份证正面地址|
|idBack|string|false|none|身份证反面地址|身份证反面地址|
|contactAddress|string|false|none|联系地址，支持身份证识别填充|联系地址，支持身份证识别填充|
|maintainerId|string|false|none|维护人id|维护人id|
|maintainerName|string|false|none|维护人姓名|维护人姓名|
|attachmentFiles|string|false|none|附件|附件|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerAddDTO">CustomerAddDTO</h2>

<a id="schemacustomeradddto"></a>
<a id="schema_CustomerAddDTO"></a>
<a id="tocScustomeradddto"></a>
<a id="tocscustomeradddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "customerType": 0,
  "customerName": "string",
  "creditCode": "string",
  "legalName": "string",
  "contactPhone": "string",
  "idType": "string",
  "idNumber": "string",
  "idValidityStart": "2019-08-24T14:15:22Z",
  "idValidityEnd": "2019-08-24T14:15:22Z",
  "idFront": "string",
  "idBack": "string",
  "contactAddress": "string",
  "maintainerId": "string",
  "maintainerName": "string",
  "attachmentFiles": "string",
  "remark": "string",
  "isDel": 0,
  "customerContactList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "gender": "string",
      "idNumber": "string",
      "position": "string",
      "department": "string",
      "isPreferred": 0,
      "remark": "string",
      "isDel": 0
    }
  ],
  "customerGuarantorList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "name": "string",
      "phone": "string",
      "idType": "string",
      "idNumber": "string",
      "remark": "string",
      "isDel": 0
    }
  ],
  "customerBankAccountList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "bankName": "string",
      "accountNumber": "string",
      "accountRemark": "string",
      "isDel": 0
    }
  ],
  "customerInvoiceList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "customerId": "string",
      "title": "string",
      "taxNumber": "string",
      "phone": "string",
      "address": "string",
      "bankName": "string",
      "accountNumber": "string",
      "isDel": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|customerType|integer(int64)|false|none|客户类型，个人：1，企业：2|客户类型，个人：1，企业：2|
|customerName|string|false|none|客户姓名，支持身份证识别填充|客户姓名，支持身份证识别填充|
|creditCode|string|false|none|统一社会信用代码，需校验唯一性|统一社会信用代码，需校验唯一性|
|legalName|string|false|none|法人姓名|法人姓名|
|contactPhone|string|false|none|联系电话，需校验唯一性|联系电话，需校验唯一性|
|idType|string|false|none|证件类型，身份证/护照等|证件类型，身份证/护照等|
|idNumber|string|false|none|证件号码，支持身份证识别填充|证件号码，支持身份证识别填充|
|idValidityStart|string(date-time)|false|none|证件有效期开始，支持身份证识别填充|证件有效期开始，支持身份证识别填充|
|idValidityEnd|string(date-time)|false|none|证件有效期结束，支持身份证识别填充|证件有效期结束，支持身份证识别填充|
|idFront|string|false|none|身份证正面地址|身份证正面地址|
|idBack|string|false|none|身份证反面地址|身份证反面地址|
|contactAddress|string|false|none|联系地址，支持身份证识别填充|联系地址，支持身份证识别填充|
|maintainerId|string|false|none|维护人id|维护人id|
|maintainerName|string|false|none|维护人姓名|维护人姓名|
|attachmentFiles|string|false|none|附件|附件|
|remark|string|false|none|备注|备注|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|
|customerContactList|[[CustomerContactAddDTO](#schemacustomercontactadddto)]|false|none|联系人列表|联系人列表|
|customerGuarantorList|[[CustomerGuarantorAddDTO](#schemacustomerguarantoradddto)]|false|none|担保人列表|担保人列表|
|customerBankAccountList|[[CustomerBankAccountAddDTO](#schemacustomerbankaccountadddto)]|false|none|银行账号列表|银行账号列表|
|customerInvoiceList|[[CustomerInvoiceAddDTO](#schemacustomerinvoiceadddto)]|false|none|开票信息列表|开票信息列表|

<h2 id="tocS_CustomerBankAccountAddDTO">CustomerBankAccountAddDTO</h2>

<a id="schemacustomerbankaccountadddto"></a>
<a id="schema_CustomerBankAccountAddDTO"></a>
<a id="tocScustomerbankaccountadddto"></a>
<a id="tocscustomerbankaccountadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "customerId": "string",
  "bankName": "string",
  "accountNumber": "string",
  "accountRemark": "string",
  "isDel": 0
}

```

银行账号列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|customerId|string|false|none|客户ID|客户ID|
|bankName|string|false|none|开户银行|开户银行|
|accountNumber|string|false|none|银行账号|银行账号|
|accountRemark|string|false|none|账户备注|账户备注|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerContactAddDTO">CustomerContactAddDTO</h2>

<a id="schemacustomercontactadddto"></a>
<a id="schema_CustomerContactAddDTO"></a>
<a id="tocScustomercontactadddto"></a>
<a id="tocscustomercontactadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "customerId": "string",
  "name": "string",
  "phone": "string",
  "gender": "string",
  "idNumber": "string",
  "position": "string",
  "department": "string",
  "isPreferred": 0,
  "remark": "string",
  "isDel": 0
}

```

联系人列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|customerId|string|false|none|客户ID|客户ID|
|name|string|false|none|联系人姓名|联系人姓名|
|phone|string|false|none|联系电话|联系电话|
|gender|string|false|none|性别，男/女|性别，男/女|
|idNumber|string|false|none|证件号码|证件号码|
|position|string|false|none|职务，仅企业客户适用|职务，仅企业客户适用|
|department|string|false|none|部门，仅企业客户适用|部门，仅企业客户适用|
|isPreferred|integer(int32)|false|none|是否首选联系人，默认最新记录为首选|是否首选联系人，默认最新记录为首选|
|remark|string|false|none|补充说明|补充说明|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerGuarantorAddDTO">CustomerGuarantorAddDTO</h2>

<a id="schemacustomerguarantoradddto"></a>
<a id="schema_CustomerGuarantorAddDTO"></a>
<a id="tocScustomerguarantoradddto"></a>
<a id="tocscustomerguarantoradddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "customerId": "string",
  "name": "string",
  "phone": "string",
  "idType": "string",
  "idNumber": "string",
  "remark": "string",
  "isDel": 0
}

```

担保人列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|customerId|string|false|none|客户ID|客户ID|
|name|string|false|none|担保人姓名|担保人姓名|
|phone|string|false|none|联系电话|联系电话|
|idType|string|false|none|证件类型|证件类型|
|idNumber|string|false|none|证件号码|证件号码|
|remark|string|false|none|备注，200字限制|备注，200字限制|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_CustomerInvoiceAddDTO">CustomerInvoiceAddDTO</h2>

<a id="schemacustomerinvoiceadddto"></a>
<a id="schema_CustomerInvoiceAddDTO"></a>
<a id="tocScustomerinvoiceadddto"></a>
<a id="tocscustomerinvoiceadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "customerId": "string",
  "title": "string",
  "taxNumber": "string",
  "phone": "string",
  "address": "string",
  "bankName": "string",
  "accountNumber": "string",
  "isDel": 0
}

```

开票信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|customerId|string|false|none|客户ID|客户ID|
|title|string|false|none|抬头名称|抬头名称|
|taxNumber|string|false|none|税号|税号|
|phone|string|false|none|电话号码|电话号码|
|address|string|false|none|单位地址|单位地址|
|bankName|string|false|none|开户银行|开户银行|
|accountNumber|string|false|none|银行账号|银行账号|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_RList">RList</h2>

<a id="schemarlist"></a>
<a id="schema_RList"></a>
<a id="tocSrlist"></a>
<a id="tocsrlist"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {}
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|[object]|false|none||none|

<h2 id="tocS_RoomVo">RoomVo</h2>

<a id="schemaroomvo"></a>
<a id="schema_RoomVo"></a>
<a id="tocSroomvo"></a>
<a id="tocsroomvo"></a>

```json
{
  "id": "string",
  "roomName": "string",
  "type": "string",
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "roomCode": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "areaType": "string",
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": "string",
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": "string",
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": "string",
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": 0,
  "selfUseSubject": "string",
  "selfUsePurpose": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|roomName|string|false|none|房源名称|房源名称|
|type|string|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|string|false|none|物业类型，二级字典|物业类型，二级字典|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|地块id|地块id|
|parcelName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋id|楼栋id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层id|楼层id|
|floorName|string|false|none|楼层名称|楼层名称|
|roomId|string|false|none|房间id， sys_room表的id|房间id， sys_room表的id|
|status|integer(int64)|false|none|状态（0草稿 10待生效 20生效中 30已失效）|状态（0草稿 10待生效 20生效中 30已失效）|
|roomCode|string|false|none|合成编码|合成编码|
|planEffectDate|string(date-time)|false|none|预计生效日期|预计生效日期|
|actualEffectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|areaType|string|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|rentAreaType|string|false|none|计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|rentArea|number|false|none|计租面积|计租面积|
|storey|number|false|none|层高|层高|
|value|number|false|none|荷载值|荷载值|
|orientation|string|false|none|朝向|朝向|
|houseTypeId|string|false|none|户型id|户型id|
|smartWaterMeter|string|false|none|智能水电表|智能水电表|
|smartLock|string|false|none|智能锁|智能锁|
|assetOperationMode|string|false|none|资产运营模式|资产运营模式|
|assetOperationType|string|false|none|资产运营分类|资产运营分类|
|propertyStatus|string|false|none|产权情况|产权情况|
|paymentStatus|string|false|none|交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|specialTag|string|false|none|特殊标签（1保障房）|特殊标签（1保障房）|
|latestRentPrice|number|false|none|商管承租成本单价（最新）|商管承租成本单价（最新）|
|latestRentStartDate|string(date-time)|false|none|商管承租起计日期（最新）|商管承租起计日期（最新）|
|latestRentEndDate|string(date-time)|false|none|商管承租到期日期（最新）|商管承租到期日期（最新）|
|firstRentPrice|number|false|none|商管承租成本单价第一次|商管承租成本单价第一次|
|firstRentStartDate|string(date-time)|false|none|商管承租起计日期第一次|商管承租起计日期第一次|
|firstRentEndDate|string(date-time)|false|none|商管承租到期日期第一次|商管承租到期日期第一次|
|operationSubject|string|false|none|运营主体（1商服 2众创城）|运营主体（1商服 2众创城）|
|rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|isSelfUse|integer(int32)|false|none|是否自用（0否 1是）|是否自用（0否 1是）|
|selfUseSubject|string|false|none|自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|selfUsePurpose|string|false|none|自用用途|自用用途|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_FixedAssetsVo">FixedAssetsVo</h2>

<a id="schemafixedassetsvo"></a>
<a id="schema_FixedAssetsVo"></a>
<a id="tocSfixedassetsvo"></a>
<a id="tocsfixedassetsvo"></a>

```json
{
  "id": "string",
  "category": "string",
  "name": "string",
  "specification": "string",
  "attachments": "string",
  "usageScope": "string",
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|id|none|
|category|string|false|none|种类|种类|
|name|string|false|none|物品名称|物品名称|
|specification|string|false|none|规格|规格|
|attachments|string|false|none|附件|附件|
|usageScope|string|false|none|使用范围|使用范围|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_BookingVo">BookingVo</h2>

<a id="schemabookingvo"></a>
<a id="schema_BookingVo"></a>
<a id="tocSbookingvo"></a>
<a id="tocsbookingvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0,
  "contractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "contractLeaseUnit": "string",
  "lesseeName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|customerName|string|false|none|客户名称|客户名称|
|propertyType|string|false|none|意向物业类型|意向物业类型|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房源名称|房源名称|
|bookingAmount|number|false|none|定单金额|定单金额|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|cancelEnclosure|string|false|none|退定附件|退定附件|
|cancelRemark|string|false|none|退定说明|退定说明|
|status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|contractId|string|false|none|转签约合同id|转签约合同id|
|refundId|string|false|none|退款单id|退款单id|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|
|contractNo|string|false|none|合同号|合同号|
|signDate|string(date-time)|false|none|合同签订日期|合同签订日期|
|contractLeaseUnit|string|false|none|租赁单元|租赁单元|
|lesseeName|string|false|none|承租人名称|承租人名称|

<h2 id="tocS_SysNotice">SysNotice</h2>

<a id="schemasysnotice"></a>
<a id="schema_SysNotice"></a>
<a id="tocSsysnotice"></a>
<a id="tocssysnotice"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "noticeId": 0,
  "noticeTitle": "string",
  "noticeType": "string",
  "noticeContent": "string",
  "status": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|noticeId|integer(int64)|false|none||none|
|noticeTitle|string|true|none||none|
|noticeType|string|false|none||none|
|noticeContent|string|false|none||none|
|status|string|false|none||none|

<h2 id="tocS_SysMenu">SysMenu</h2>

<a id="schemasysmenu"></a>
<a id="schema_SysMenu"></a>
<a id="tocSsysmenu"></a>
<a id="tocssysmenu"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "menuId": 0,
  "menuName": "string",
  "parentName": "string",
  "parentId": 0,
  "orderNum": 0,
  "path": "string",
  "component": "string",
  "query": "string",
  "routeName": "string",
  "isFrame": "string",
  "isCache": "string",
  "menuType": "string",
  "visible": "string",
  "status": "string",
  "perms": "string",
  "icon": "string",
  "children": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "menuId": 0,
      "menuName": "string",
      "parentName": "string",
      "parentId": 0,
      "orderNum": 0,
      "path": "string",
      "component": "string",
      "query": "string",
      "routeName": "string",
      "isFrame": "string",
      "isCache": "string",
      "menuType": "string",
      "visible": "string",
      "status": "string",
      "perms": "string",
      "icon": "string",
      "children": [
        {
          "createBy": "string",
          "createByName": "string",
          "createTime": "2019-08-24T14:15:22Z",
          "updateBy": "string",
          "updateByName": "string",
          "updateTime": "2019-08-24T14:15:22Z",
          "menuId": 0,
          "menuName": "string",
          "parentName": "string",
          "parentId": 0,
          "orderNum": 0,
          "path": "string",
          "component": "string",
          "query": "string",
          "routeName": "string",
          "isFrame": "string",
          "isCache": "string",
          "menuType": "string",
          "visible": "string",
          "status": "string",
          "perms": "string",
          "icon": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|menuId|integer(int64)|false|none||none|
|menuName|string|true|none||none|
|parentName|string|false|none||none|
|parentId|integer(int64)|false|none||none|
|orderNum|integer(int32)|true|none||none|
|path|string|false|none||none|
|component|string|false|none||none|
|query|string|false|none||none|
|routeName|string|false|none||none|
|isFrame|string|false|none||none|
|isCache|string|false|none||none|
|menuType|string|true|none||none|
|visible|string|false|none||none|
|status|string|false|none||none|
|perms|string|false|none||none|
|icon|string|false|none||none|
|children|[[SysMenu](#schemasysmenu)]|false|none||none|

<h2 id="tocS_SysDictType">SysDictType</h2>

<a id="schemasysdicttype"></a>
<a id="schema_SysDictType"></a>
<a id="tocSsysdicttype"></a>
<a id="tocssysdicttype"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "dictId": 0,
  "dictName": "string",
  "dictType": "string",
  "status": "string",
  "remark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|dictId|integer(int64)|false|none||none|
|dictName|string|true|none||none|
|dictType|string|true|none||none|
|status|string|false|none||none|
|remark|string|false|none||none|

<h2 id="tocS_SysConfig">SysConfig</h2>

<a id="schemasysconfig"></a>
<a id="schema_SysConfig"></a>
<a id="tocSsysconfig"></a>
<a id="tocssysconfig"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "configId": 0,
  "configName": "string",
  "configKey": "string",
  "configValue": "string",
  "configType": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|configId|integer(int64)|false|none||none|
|configName|string|true|none||none|
|configKey|string|true|none||none|
|configValue|string|true|none||none|
|configType|string|false|none||none|

<h2 id="tocS_SysOperLog">SysOperLog</h2>

<a id="schemasysoperlog"></a>
<a id="schema_SysOperLog"></a>
<a id="tocSsysoperlog"></a>
<a id="tocssysoperlog"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "operId": 0,
  "title": "string",
  "businessType": 0,
  "businessTypes": [
    0
  ],
  "method": "string",
  "requestMethod": "string",
  "operatorType": 0,
  "operName": "string",
  "deptName": "string",
  "operUrl": "string",
  "operIp": "string",
  "operParam": "string",
  "jsonResult": "string",
  "status": 0,
  "errorMsg": "string",
  "operTime": "2019-08-24T14:15:22Z",
  "costTime": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|operId|integer(int64)|false|none||none|
|title|string|false|none||none|
|businessType|integer(int32)|false|none||none|
|businessTypes|[integer]|false|none||none|
|method|string|false|none||none|
|requestMethod|string|false|none||none|
|operatorType|integer(int32)|false|none||none|
|operName|string|false|none||none|
|deptName|string|false|none||none|
|operUrl|string|false|none||none|
|operIp|string|false|none||none|
|operParam|string|false|none||none|
|jsonResult|string|false|none||none|
|status|integer(int32)|false|none||none|
|errorMsg|string|false|none||none|
|operTime|string(date-time)|false|none||none|
|costTime|integer(int64)|false|none||none|

<h2 id="tocS_SysOperLogQueryBo">SysOperLogQueryBo</h2>

<a id="schemasysoperlogquerybo"></a>
<a id="schema_SysOperLogQueryBo"></a>
<a id="tocSsysoperlogquerybo"></a>
<a id="tocssysoperlogquerybo"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "operId": 0,
  "title": "string",
  "businessType": 0,
  "businessTypes": [
    0
  ],
  "method": "string",
  "requestMethod": "string",
  "operatorType": 0,
  "operName": "string",
  "deptName": "string",
  "operUrl": "string",
  "operIp": "string",
  "operParam": "string",
  "jsonResult": "string",
  "status": 0,
  "errorMsg": "string",
  "operTime": "2019-08-24T14:15:22Z",
  "costTime": 0,
  "beginTime": "string",
  "endTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|operId|integer(int64)|false|none||none|
|title|string|false|none||none|
|businessType|integer(int32)|false|none||none|
|businessTypes|[integer]|false|none||none|
|method|string|false|none||none|
|requestMethod|string|false|none||none|
|operatorType|integer(int32)|false|none||none|
|operName|string|false|none||none|
|deptName|string|false|none||none|
|operUrl|string|false|none||none|
|operIp|string|false|none||none|
|operParam|string|false|none||none|
|jsonResult|string|false|none||none|
|status|integer(int32)|false|none||none|
|errorMsg|string|false|none||none|
|operTime|string(date-time)|false|none||none|
|costTime|integer(int64)|false|none||none|
|beginTime|string|false|none||none|
|endTime|string|false|none||none|

<h2 id="tocS_SysLogininforQueryBo">SysLogininforQueryBo</h2>

<a id="schemasyslogininforquerybo"></a>
<a id="schema_SysLogininforQueryBo"></a>
<a id="tocSsyslogininforquerybo"></a>
<a id="tocssyslogininforquerybo"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "infoId": 0,
  "userName": "string",
  "status": "string",
  "ipaddr": "string",
  "msg": "string",
  "accessTime": "2019-08-24T14:15:22Z",
  "beginTime": "string",
  "endTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|infoId|integer(int64)|false|none||none|
|userName|string|false|none||none|
|status|string|false|none||none|
|ipaddr|string|false|none||none|
|msg|string|false|none||none|
|accessTime|string(date-time)|false|none||none|
|beginTime|string|false|none||none|
|endTime|string|false|none||none|

<h2 id="tocS_EhrUserQueryBO">EhrUserQueryBO</h2>

<a id="schemaehruserquerybo"></a>
<a id="schema_EhrUserQueryBO"></a>
<a id="tocSehruserquerybo"></a>
<a id="tocsehruserquerybo"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "orgList": [
    "string"
  ],
  "keyword": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|orgList|[string]|false|none||none|
|keyword|string|false|none||none|

<h2 id="tocS_SysDictTypeQueryBo">SysDictTypeQueryBo</h2>

<a id="schemasysdicttypequerybo"></a>
<a id="schema_SysDictTypeQueryBo"></a>
<a id="tocSsysdicttypequerybo"></a>
<a id="tocssysdicttypequerybo"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "dictId": 0,
  "dictName": "string",
  "dictType": "string",
  "status": "string",
  "remark": "string",
  "beginTime": "string",
  "endTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|dictId|integer(int64)|false|none||none|
|dictName|string|false|none||none|
|dictType|string|false|none||none|
|status|string|false|none||none|
|remark|string|false|none||none|
|beginTime|string|false|none||none|
|endTime|string|false|none||none|

<h2 id="tocS_SysDictDataQueryBo">SysDictDataQueryBo</h2>

<a id="schemasysdictdataquerybo"></a>
<a id="schema_SysDictDataQueryBo"></a>
<a id="tocSsysdictdataquerybo"></a>
<a id="tocssysdictdataquerybo"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "dictCode": 0,
  "dictSort": 0,
  "dictLabel": "string",
  "dictValue": "string",
  "dictType": "string",
  "cssClass": "string",
  "listClass": "string",
  "isDefault": "string",
  "status": "string",
  "remark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|dictCode|integer(int64)|false|none||none|
|dictSort|integer(int64)|false|none||none|
|dictLabel|string|false|none||none|
|dictValue|string|false|none||none|
|dictType|string|false|none||none|
|cssClass|string|false|none||none|
|listClass|string|false|none||none|
|isDefault|string|false|none||none|
|status|string|false|none||none|
|remark|string|false|none||none|

<h2 id="tocS_SysConfigQueryBo">SysConfigQueryBo</h2>

<a id="schemasysconfigquerybo"></a>
<a id="schema_SysConfigQueryBo"></a>
<a id="tocSsysconfigquerybo"></a>
<a id="tocssysconfigquerybo"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "configId": 0,
  "configName": "string",
  "configKey": "string",
  "configValue": "string",
  "configType": "string",
  "beginTime": "string",
  "endTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|configId|integer(int64)|false|none||none|
|configName|string|false|none||none|
|configKey|string|false|none||none|
|configValue|string|false|none||none|
|configType|string|false|none||none|
|beginTime|string|false|none||none|
|endTime|string|false|none||none|

<h2 id="tocS_RLoginUser">RLoginUser</h2>

<a id="schemarloginuser"></a>
<a id="schema_RLoginUser"></a>
<a id="tocSrloginuser"></a>
<a id="tocsrloginuser"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "token": "string",
    "userid": 0,
    "username": "string",
    "nickName": "string",
    "loginTime": 0,
    "expireTime": 0,
    "ipaddr": "string",
    "permissions": [
      "string"
    ],
    "projectPermissions": [
      "string"
    ],
    "roomPermissions": [
      "string"
    ],
    "contractPermission": "string",
    "roles": [
      "string"
    ],
    "sysUser": {
      "userId": 0,
      "deptId": 0,
      "userName": "string",
      "nickName": "string",
      "email": "string",
      "phonenumber": "string",
      "sex": "string",
      "avatar": "string",
      "password": "string",
      "status": "string",
      "delFlag": "string",
      "loginIp": "string",
      "loginDate": "2019-08-24T14:15:22Z",
      "roles": [
        {
          "roleId": 0,
          "roleName": "string",
          "type": 0,
          "remark": "string",
          "roomPermissions": "string",
          "contractPermissions": "string",
          "admin": true
        }
      ],
      "admin": true
    },
    "admin": true
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|[LoginUser](#schemaloginuser)|false|none||none|

<h2 id="tocS_SysRoleQueryBo">SysRoleQueryBo</h2>

<a id="schemasysrolequerybo"></a>
<a id="schema_SysRoleQueryBo"></a>
<a id="tocSsysrolequerybo"></a>
<a id="tocssysrolequerybo"></a>

```json
{
  "type": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|integer(int32)|true|none|角色类型|none|

<h2 id="tocS_SysOrgBo">SysOrgBo</h2>

<a id="schemasysorgbo"></a>
<a id="schema_SysOrgBo"></a>
<a id="tocSsysorgbo"></a>
<a id="tocssysorgbo"></a>

```json
{
  "name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|false|none|组织名称|none|

<h2 id="tocS_SysMenuQueryBo">SysMenuQueryBo</h2>

<a id="schemasysmenuquerybo"></a>
<a id="schema_SysMenuQueryBo"></a>
<a id="tocSsysmenuquerybo"></a>
<a id="tocssysmenuquerybo"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "menuId": 0,
  "menuName": "string",
  "parentName": "string",
  "parentId": 0,
  "orderNum": 0,
  "path": "string",
  "component": "string",
  "query": "string",
  "routeName": "string",
  "isFrame": "string",
  "isCache": "string",
  "menuType": "string",
  "visible": "string",
  "status": "string",
  "perms": "string",
  "icon": "string",
  "children": [
    {
      "params": {
        "property1": {},
        "property2": {}
      },
      "menuId": 0,
      "menuName": "string",
      "parentName": "string",
      "parentId": 0,
      "orderNum": 0,
      "path": "string",
      "component": "string",
      "query": "string",
      "routeName": "string",
      "isFrame": "string",
      "isCache": "string",
      "menuType": "string",
      "visible": "string",
      "status": "string",
      "perms": "string",
      "icon": "string",
      "children": [
        {
          "params": {
            "property1": {},
            "property2": {}
          },
          "menuId": 0,
          "menuName": "string",
          "parentName": "string",
          "parentId": 0,
          "orderNum": 0,
          "path": "string",
          "component": "string",
          "query": "string",
          "routeName": "string",
          "isFrame": "string",
          "isCache": "string",
          "menuType": "string",
          "visible": "string",
          "status": "string",
          "perms": "string",
          "icon": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|menuId|integer(int64)|false|none||none|
|menuName|string|false|none||none|
|parentName|string|false|none||none|
|parentId|integer(int64)|false|none||none|
|orderNum|integer(int32)|false|none||none|
|path|string|false|none||none|
|component|string|false|none||none|
|query|string|false|none||none|
|routeName|string|false|none||none|
|isFrame|string|false|none||none|
|isCache|string|false|none||none|
|menuType|string|false|none||none|
|visible|string|false|none||none|
|status|string|false|none||none|
|perms|string|false|none||none|
|icon|string|false|none||none|
|children|[[SysMenuQueryBo](#schemasysmenuquerybo)]|false|none||none|

<h2 id="tocS_RListLong">RListLong</h2>

<a id="schemarlistlong"></a>
<a id="schema_RListLong"></a>
<a id="tocSrlistlong"></a>
<a id="tocsrlistlong"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|[integer]|false|none||none|

<h2 id="tocS_SysRole">SysRole</h2>

<a id="schemasysrole"></a>
<a id="schema_SysRole"></a>
<a id="tocSsysrole"></a>
<a id="tocssysrole"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "roleId": 0,
  "roleName": "string",
  "type": 0,
  "remark": "string",
  "roomPermissions": "string",
  "contractPermissions": "string",
  "menuIds": [
    0
  ],
  "deptIds": [
    0
  ],
  "permissions": [
    "string"
  ],
  "admin": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|roleId|integer(int64)|false|none||none|
|roleName|string|true|none||none|
|type|integer(int32)|false|none||none|
|remark|string|false|none||none|
|roomPermissions|string|false|none||none|
|contractPermissions|string|false|none||none|
|menuIds|[integer]|false|none||none|
|deptIds|[integer]|false|none||none|
|permissions|[string]|false|none||none|
|admin|boolean|false|none||none|

<h2 id="tocS_SysRoleModel">SysRoleModel</h2>

<a id="schemasysrolemodel"></a>
<a id="schema_SysRoleModel"></a>
<a id="tocSsysrolemodel"></a>
<a id="tocssysrolemodel"></a>

```json
{
  "roleId": 0,
  "roleName": "string",
  "type": 0,
  "remark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roleId|integer(int64)|false|none||none|
|roleName|string|true|none|角色名称|none|
|type|integer(int32)|true|none|角色类型,1:全部，2:菜单，3:数据|none|
|remark|string|false|none|角色说明|none|

<h2 id="tocS_SysRoleMenuBo">SysRoleMenuBo</h2>

<a id="schemasysrolemenubo"></a>
<a id="schema_SysRoleMenuBo"></a>
<a id="tocSsysrolemenubo"></a>
<a id="tocssysrolemenubo"></a>

```json
{
  "roleId": 0,
  "menuIdList": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roleId|integer(int64)|true|none|角色id|none|
|menuIdList|[integer]|true|none|菜单id|none|
|» 菜单id|integer(int64)|false|none|菜单id|none|

<h2 id="tocS_SysRoleUserQueryBo">SysRoleUserQueryBo</h2>

<a id="schemasysroleuserquerybo"></a>
<a id="schema_SysRoleUserQueryBo"></a>
<a id="tocSsysroleuserquerybo"></a>
<a id="tocssysroleuserquerybo"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "type": 0,
  "roleId": 0,
  "userName": "string",
  "userPhone": "string",
  "status": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|type|integer(int32)|false|none|角色类型|none|
|roleId|integer(int64)|true|none|角色id|none|
|userName|string|false|none|用户姓名|none|
|userPhone|string|false|none|用户手机号|none|
|status|integer(int32)|false|none|用户状态|none|

<h2 id="tocS_SysRoleUserBo">SysRoleUserBo</h2>

<a id="schemasysroleuserbo"></a>
<a id="schema_SysRoleUserBo"></a>
<a id="tocSsysroleuserbo"></a>
<a id="tocssysroleuserbo"></a>

```json
{
  "roleId": 0,
  "ehrUserIdList": [
    "string"
  ],
  "userIdList": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roleId|integer(int64)|true|none|角色id|none|
|ehrUserIdList|[string]|true|none|ehr用户id|none|
|» ehr用户id|string|false|none|ehr用户id|none|
|userIdList|[integer]|false|none||none|

<h2 id="tocS_SysRoleDTO">SysRoleDTO</h2>

<a id="schemasysroledto"></a>
<a id="schema_SysRoleDTO"></a>
<a id="tocSsysroledto"></a>
<a id="tocssysroledto"></a>

```json
{
  "roleId": 0,
  "roleName": "string",
  "type": 0,
  "remark": "string",
  "roomPermissions": "string",
  "contractPermissions": "string",
  "admin": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roleId|integer(int64)|false|none||none|
|roleName|string|false|none||none|
|type|integer(int32)|false|none||none|
|remark|string|false|none||none|
|roomPermissions|string|false|none||none|
|contractPermissions|string|false|none||none|
|admin|boolean|false|none||none|

<h2 id="tocS_SysUserDTO">SysUserDTO</h2>

<a id="schemasysuserdto"></a>
<a id="schema_SysUserDTO"></a>
<a id="tocSsysuserdto"></a>
<a id="tocssysuserdto"></a>

```json
{
  "userId": 0,
  "deptId": 0,
  "userName": "string",
  "nickName": "string",
  "email": "string",
  "phonenumber": "string",
  "sex": "string",
  "avatar": "string",
  "password": "string",
  "status": "string",
  "delFlag": "string",
  "loginIp": "string",
  "loginDate": "2019-08-24T14:15:22Z",
  "roles": [
    {
      "roleId": 0,
      "roleName": "string",
      "type": 0,
      "remark": "string",
      "roomPermissions": "string",
      "contractPermissions": "string",
      "admin": true
    }
  ],
  "admin": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|false|none||none|
|deptId|integer(int64)|false|none||none|
|userName|string|false|none||none|
|nickName|string|false|none||none|
|email|string|false|none||none|
|phonenumber|string|false|none||none|
|sex|string|false|none||none|
|avatar|string|false|none||none|
|password|string|false|none||none|
|status|string|false|none||none|
|delFlag|string|false|none||none|
|loginIp|string|false|none||none|
|loginDate|string(date-time)|false|none||none|
|roles|[[SysRoleDTO](#schemasysroledto)]|false|none||none|
|admin|boolean|false|none||none|

<h2 id="tocS_LoginUser">LoginUser</h2>

<a id="schemaloginuser"></a>
<a id="schema_LoginUser"></a>
<a id="tocSloginuser"></a>
<a id="tocsloginuser"></a>

```json
{
  "token": "string",
  "userid": 0,
  "username": "string",
  "nickName": "string",
  "loginTime": 0,
  "expireTime": 0,
  "ipaddr": "string",
  "permissions": [
    "string"
  ],
  "projectPermissions": [
    "string"
  ],
  "roomPermissions": [
    "string"
  ],
  "contractPermission": "string",
  "roles": [
    "string"
  ],
  "sysUser": {
    "userId": 0,
    "deptId": 0,
    "userName": "string",
    "nickName": "string",
    "email": "string",
    "phonenumber": "string",
    "sex": "string",
    "avatar": "string",
    "password": "string",
    "status": "string",
    "delFlag": "string",
    "loginIp": "string",
    "loginDate": "2019-08-24T14:15:22Z",
    "roles": [
      {
        "roleId": 0,
        "roleName": "string",
        "type": 0,
        "remark": "string",
        "roomPermissions": "string",
        "contractPermissions": "string",
        "admin": true
      }
    ],
    "admin": true
  },
  "admin": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|token|string|false|none||none|
|userid|integer(int64)|false|none||none|
|username|string|false|none||none|
|nickName|string|false|none||none|
|loginTime|integer(int64)|false|none||none|
|expireTime|integer(int64)|false|none||none|
|ipaddr|string|false|none||none|
|permissions|[string]|false|none||none|
|projectPermissions|[string]|false|none||none|
|roomPermissions|[string]|false|none||none|
|contractPermission|string|false|none||none|
|roles|[string]|false|none||none|
|sysUser|[SysUserDTO](#schemasysuserdto)|false|none||none|
|admin|boolean|false|none||none|

<h2 id="tocS_SysUser">SysUser</h2>

<a id="schemasysuser"></a>
<a id="schema_SysUser"></a>
<a id="tocSsysuser"></a>
<a id="tocssysuser"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "userId": 0,
  "deptId": 0,
  "userName": "string",
  "nickName": "string",
  "email": "string",
  "phonenumber": "string",
  "sex": "string",
  "avatar": "string",
  "password": "string",
  "status": "string",
  "delFlag": "string",
  "loginIp": "string",
  "loginDate": "2019-08-24T14:15:22Z",
  "remark": "string",
  "syncId": "string",
  "postName": "string",
  "roles": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "roleId": 0,
      "roleName": "string",
      "type": 0,
      "remark": "string",
      "roomPermissions": "string",
      "contractPermissions": "string",
      "menuIds": [
        0
      ],
      "deptIds": [
        0
      ],
      "permissions": [
        "string"
      ],
      "admin": true
    }
  ],
  "roleIds": [
    0
  ],
  "admin": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|userId|integer(int64)|false|none||none|
|deptId|integer(int64)|false|none||none|
|userName|string|true|none||none|
|nickName|string|false|none||none|
|email|string|false|none||none|
|phonenumber|string|false|none||none|
|sex|string|false|none||none|
|avatar|string|false|none||none|
|password|string|false|none||none|
|status|string|false|none||none|
|delFlag|string|false|none||none|
|loginIp|string|false|none||none|
|loginDate|string(date-time)|false|none||none|
|remark|string|false|none||none|
|syncId|string|false|none||none|
|postName|string|false|none||none|
|roles|[[SysRole](#schemasysrole)]|false|none||none|
|roleIds|[integer]|false|none||none|
|admin|boolean|false|none||none|

<h2 id="tocS_SysUserBo">SysUserBo</h2>

<a id="schemasysuserbo"></a>
<a id="schema_SysUserBo"></a>
<a id="tocSsysuserbo"></a>
<a id="tocssysuserbo"></a>

```json
{
  "nickName": "string",
  "userName": "string",
  "phonenumber": "string",
  "menuRoles": [
    0
  ],
  "dataRoles": [
    0
  ],
  "status": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|nickName|string|false|none|人员名称|none|
|userName|string|false|none|账号|none|
|phonenumber|string|false|none|手机号|none|
|menuRoles|[integer]|false|none|功能角色|none|
|» 功能角色|integer(int64)|false|none|功能角色|none|
|dataRoles|[integer]|false|none|数据角色|none|
|» 数据角色|integer(int64)|false|none|数据角色|none|
|status|integer(int32)|false|none|状态|none|

<h2 id="tocS_SysUserChangeStatusBo">SysUserChangeStatusBo</h2>

<a id="schemasysuserchangestatusbo"></a>
<a id="schema_SysUserChangeStatusBo"></a>
<a id="tocSsysuserchangestatusbo"></a>
<a id="tocssysuserchangestatusbo"></a>

```json
{
  "userId": 0,
  "status": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|false|none|用户id|none|
|status|string|false|none|状态|none|

<h2 id="tocS_SysUserAuthRoleBo">SysUserAuthRoleBo</h2>

<a id="schemasysuserauthrolebo"></a>
<a id="schema_SysUserAuthRoleBo"></a>
<a id="tocSsysuserauthrolebo"></a>
<a id="tocssysuserauthrolebo"></a>

```json
{
  "ehrUserIdList": [
    "string"
  ],
  "roleIdList": [
    0
  ],
  "userIdList": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ehrUserIdList|[string]|false|none|ehr用户id|none|
|» ehr用户id|string|false|none|ehr用户id|none|
|roleIdList|[integer]|true|none|角色id|none|
|» 角色id|integer(int64)|false|none|角色id|none|
|userIdList|[integer]|false|none||none|

<h2 id="tocS_SysRoleOrgBo">SysRoleOrgBo</h2>

<a id="schemasysroleorgbo"></a>
<a id="schema_SysRoleOrgBo"></a>
<a id="tocSsysroleorgbo"></a>
<a id="tocssysroleorgbo"></a>

```json
{
  "roleId": 0,
  "roomPermissions": [
    "string"
  ],
  "contractPermission": "string",
  "orgIdList": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roleId|integer(int64)|true|none|角色id|none|
|roomPermissions|[string]|true|none|房源权限,all,business,zhongchuang|none|
|» 房源权限,all,business,zhongchuang|string|false|none|房源权限,all,business,zhongchuang|none|
|contractPermission|string|true|none|合同权限, all,personal|none|
|orgIdList|[string]|true|none|组织id|none|
|» 组织id|string|false|none|组织id|none|

<h2 id="tocS_SysRoleUserCancelBo">SysRoleUserCancelBo</h2>

<a id="schemasysroleusercancelbo"></a>
<a id="schema_SysRoleUserCancelBo"></a>
<a id="tocSsysroleusercancelbo"></a>
<a id="tocssysroleusercancelbo"></a>

```json
{
  "roleId": 0,
  "userIdList": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roleId|integer(int64)|true|none|角色id|none|
|userIdList|[integer]|true|none|用户id|none|
|» 用户id|integer(int64)|false|none|用户id|none|

<h2 id="tocS_SysLogininfor">SysLogininfor</h2>

<a id="schemasyslogininfor"></a>
<a id="schema_SysLogininfor"></a>
<a id="tocSsyslogininfor"></a>
<a id="tocssyslogininfor"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "infoId": 0,
  "userName": "string",
  "status": "string",
  "ipaddr": "string",
  "msg": "string",
  "accessTime": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|infoId|integer(int64)|false|none||none|
|userName|string|false|none||none|
|status|string|false|none||none|
|ipaddr|string|false|none||none|
|msg|string|false|none||none|
|accessTime|string(date-time)|false|none||none|

<h2 id="tocS_SysDictData">SysDictData</h2>

<a id="schemasysdictdata"></a>
<a id="schema_SysDictData"></a>
<a id="tocSsysdictdata"></a>
<a id="tocssysdictdata"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "dictCode": 0,
  "dictSort": 0,
  "dictLabel": "string",
  "dictValue": "string",
  "dictType": "string",
  "cssClass": "string",
  "listClass": "string",
  "isDefault": "string",
  "status": "string",
  "remark": "string",
  "default": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|dictCode|integer(int64)|false|none||none|
|dictSort|integer(int64)|false|none||none|
|dictLabel|string|true|none||none|
|dictValue|string|true|none||none|
|dictType|string|true|none||none|
|cssClass|string|false|none||none|
|listClass|string|false|none||none|
|isDefault|string|false|none||none|
|status|string|false|none||none|
|remark|string|false|none||none|
|default|boolean|false|none||none|

<h2 id="tocS_BookingQueryDTO">BookingQueryDTO</h2>

<a id="schemabookingquerydto"></a>
<a id="schema_BookingQueryDTO"></a>
<a id="tocSbookingquerydto"></a>
<a id="tocsbookingquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "searchType": 0,
  "projectId": "string",
  "customerName": "string",
  "roomName": "string",
  "status": 0,
  "createTimeStart": "string",
  "createTimeEnd": "string",
  "createByName": "string",
  "actualReceiveTimeStart": "string",
  "actualReceiveTimeEnd": "string",
  "contractNo": "string",
  "signDateStart": "string",
  "signDateEnd": "string",
  "contractLeaseUnit": "string",
  "lesseeName": "string",
  "cancelReason": 0,
  "cancelByName": "string",
  "cancelTimeStart": "string",
  "cancelTimeEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|searchType|integer(int32)|false|none|页签类型|页签类型:1-待生效 2生效中 3已转签 4已作废|
|projectId|string|false|none|项目id|项目id|
|customerName|string|false|none|客户名称|客户名称|
|roomName|string|false|none|意向房源|意向房源|
|status|integer(int64)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|createTimeStart|string|false|none|创建日期开始|创建日期开始|
|createTimeEnd|string|false|none|创建日期结束|创建日期结束|
|createByName|string|false|none|创建人姓名|创建人姓名|
|actualReceiveTimeStart|string|false|none|实收日期开始|实收日期开始|
|actualReceiveTimeEnd|string|false|none|实收日期结束|实收日期结束|
|contractNo|string|false|none|合同编号|合同编号|
|signDateStart|string|false|none|合同签订日期开始|合同签订日期开始|
|signDateEnd|string|false|none|合同签订日期结束|合同签订日期结束|
|contractLeaseUnit|string|false|none|合同租赁单元|合同租赁单元|
|lesseeName|string|false|none|承租人名称|承租人名称|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelTimeStart|string|false|none|作废日期开始|作废日期开始|
|cancelTimeEnd|string|false|none|作废日期结束|作废日期结束|

<h2 id="tocS_AssetReceiveAddDTO">AssetReceiveAddDTO</h2>

<a id="schemaassetreceiveadddto"></a>
<a id="schema_AssetReceiveAddDTO"></a>
<a id="tocSassetreceiveadddto"></a>
<a id="tocsassetreceiveadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "receiveCode": "string",
  "receiveType": "string",
  "receiveBuilding": "string",
  "receiveRoomCount": 0,
  "receiveDate": "2019-08-24T14:15:22Z",
  "receiveReason": "string",
  "approvalStatus": 0,
  "approvalTime": "2019-08-24T14:15:22Z",
  "remark": "string",
  "attachment": "string",
  "isDel": true,
  "roomIds": [
    "string"
  ],
  "operationType": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|编辑时必传|
|projectId|string|true|none|项目id|项目id|
|receiveCode|string|false|none|接收编码|接收编码|
|receiveType|string|true|none|接收类型|接收类型|
|receiveBuilding|string|false|none|接收楼栋|接收楼栋，自动根据房源生成|
|receiveRoomCount|integer(int32)|false|none|接收房源数|接收房源数，自动计算|
|receiveDate|string(date-time)|true|none|接收日期|接收日期|
|receiveReason|string|false|none|接收原因|接收原因|
|approvalStatus|integer(int32)|false|none|审批状态|审批状态：0草稿，1审批中，2审批通过，3审批拒绝|
|approvalTime|string(date-time)|false|none|审批通过时间|审批通过时间，系统自动生成|
|remark|string|false|none|备注|备注信息|
|attachment|string|false|none|上传附件|上传附件路径|
|isDel|boolean|false|none|是否删除|是否删除：false否，true是|
|roomIds|[string]|true|none|房间ID列表|接收的房间ID列表|
|» 房间ID列表|string|false|none|房间ID列表|接收的房间ID列表|
|operationType|integer(int32)|true|none|操作类型|操作类型：1暂存，2提交|

<h2 id="tocS_AssetReceiveQueryDTO">AssetReceiveQueryDTO</h2>

<a id="schemaassetreceivequerydto"></a>
<a id="schema_AssetReceiveQueryDTO"></a>
<a id="tocSassetreceivequerydto"></a>
<a id="tocsassetreceivequerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "receiveCode": "string",
  "receiveType": "string",
  "receiveBuilding": "string",
  "receiveRoomCount": 0,
  "receiveDate": "2019-08-24T14:15:22Z",
  "receiveReason": "string",
  "approvalStatus": 0,
  "approvalTime": "2019-08-24T14:15:22Z",
  "remark": "string",
  "attachment": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|按项目ID筛选|
|receiveCode|string|false|none|接收编码|按接收编码精确匹配|
|receiveType|string|false|none|接收类型|按接收类型筛选|
|receiveBuilding|string|false|none|接收楼栋|按楼栋名称模糊搜索|
|receiveRoomCount|integer(int32)|false|none|接收房源数|按接收房源数筛选|
|receiveDate|string(date-time)|false|none|接收日期|按接收日期筛选|
|receiveReason|string|false|none|接收原因|按接收原因模糊搜索|
|approvalStatus|integer(int32)|false|none|审批状态|按审批状态筛选：0草稿，1审批中，2审批通过，3审批拒绝|
|approvalTime|string(date-time)|false|none|审批通过时间|按审批通过时间筛选|
|remark|string|false|none|备注|按备注模糊搜索|
|attachment|string|false|none|上传附件|按上传附件筛选|
|createBy|string|false|none|创建人账号|按创建人账号筛选|
|createByName|string|false|none|创建人姓名|按创建人姓名模糊搜索|
|createTime|string(date-time)|false|none|创建时间|按创建时间筛选|
|updateBy|string|false|none|更新人账号|按更新人账号筛选|
|updateByName|string|false|none|更新人姓名|按更新人姓名模糊搜索|
|updateTime|string(date-time)|false|none|更新时间|按更新时间筛选|
|isDel|boolean|false|none|是否删除|按是否删除筛选：false否，true是|

<h2 id="tocS_AssetReceiveVo">AssetReceiveVo</h2>

<a id="schemaassetreceivevo"></a>
<a id="schema_AssetReceiveVo"></a>
<a id="tocSassetreceivevo"></a>
<a id="tocsassetreceivevo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "receiveCode": "string",
  "receiveType": "string",
  "receiveBuilding": "string",
  "receiveRoomCount": 0,
  "receiveDate": "2019-08-24T14:15:22Z",
  "receiveReason": "string",
  "approvalStatus": 0,
  "approvalTime": "2019-08-24T14:15:22Z",
  "remark": "string",
  "attachment": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "roomIds": [
    "string"
  ],
  "rooms": [
    {
      "id": "string",
      "mdmRoomId": "string",
      "projectId": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomName": "string",
      "productType": "string",
      "areaType": "string",
      "areaTypeName": "string",
      "buildArea": 0,
      "innerArea": 0,
      "isSale": 0,
      "isCompanySelf": 0,
      "propertyType": "string",
      "propertyTypeName": "string",
      "selfHoldingTime": "2019-08-24T14:15:22Z",
      "status": "string",
      "receiveId": "string",
      "changeId": "string",
      "disposalId": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|receiveCode|string|false|none|接收编码|接收编码|
|receiveType|string|false|none|接收类型|接收类型|
|receiveBuilding|string|false|none|接收楼栋|接收楼栋|
|receiveRoomCount|integer(int32)|false|none|接收房源数|接收房源数|
|receiveDate|string(date-time)|false|none|接收日期|接收日期|
|receiveReason|string|false|none|接收原因|接收原因|
|approvalStatus|integer(int32)|false|none|审批状态（0草稿 1审批中 2审批通过 3审批拒绝）|审批状态（0草稿 1审批中 2审批通过 3审批拒绝）|
|approvalTime|string(date-time)|false|none|审批通过时间|审批通过时间|
|remark|string|false|none|备注|备注|
|attachment|string|false|none|上传附件|上传附件|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0 否 1是|0 否 1是|
|roomIds|[string]|false|none|房间ID列表|房间ID列表|
|» 房间ID列表|string|false|none|房间ID列表|房间ID列表|
|rooms|[[SysRoomVo](#schemasysroomvo)]|false|none|房间基础信息列表|房间基础信息列表|

<h2 id="tocS_SysRoomVo">SysRoomVo</h2>

<a id="schemasysroomvo"></a>
<a id="schema_SysRoomVo"></a>
<a id="tocSsysroomvo"></a>
<a id="tocssysroomvo"></a>

```json
{
  "id": "string",
  "mdmRoomId": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomName": "string",
  "productType": "string",
  "areaType": "string",
  "areaTypeName": "string",
  "buildArea": 0,
  "innerArea": 0,
  "isSale": 0,
  "isCompanySelf": 0,
  "propertyType": "string",
  "propertyTypeName": "string",
  "selfHoldingTime": "2019-08-24T14:15:22Z",
  "status": "string",
  "receiveId": "string",
  "changeId": "string",
  "disposalId": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|mdmRoomId|string|false|none|主数据房间id|主数据房间id|
|projectId|string|false|none|所属项目id|所属项目id|
|parcelId|string|false|none|地块id|地块id|
|parcelName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋id|楼栋id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层id|楼层id|
|floorName|string|false|none|楼层名称|楼层名称|
|roomName|string|false|none|房间名称|房间名称|
|productType|string|false|none|产品类型，字典值|产品类型，字典值|
|areaType|string|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|areaTypeName|string|false|none|面积类型|面积类型|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|isSale|integer(int32)|false|none|是否可售（0否 1是）|是否可售（0否 1是）|
|isCompanySelf|integer(int32)|false|none|是否公司自持（0否 1是）|是否公司自持（0否 1是）|
|propertyType|string|false|none|自持物业类型（1非自持 2自持）|自持物业类型（1非自持 2自持）|
|propertyTypeName|string|false|none|自持物业类型名称|自持物业类型名称|
|selfHoldingTime|string(date-time)|false|none|自持到期时间|自持到期时间|
|status|string|false|none|房间状态（0初始 1接收 2处置）|房间状态（0初始 1接收 2处置）|
|receiveId|string|false|none|接收id|接收id|
|changeId|string|false|none|最新变更id|最新变更id|
|disposalId|string|false|none|处置id|处置id|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SysRoomSimpleDTO">SysRoomSimpleDTO</h2>

<a id="schemasysroomsimpledto"></a>
<a id="schema_SysRoomSimpleDTO"></a>
<a id="tocSsysroomsimpledto"></a>
<a id="tocssysroomsimpledto"></a>

```json
{
  "buildingIds": [
    "string"
  ],
  "isSelf": "string",
  "productType": "string",
  "roomName": "string",
  "type": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|buildingIds|[string]|false|none|楼栋ID列表|按楼栋ID列表筛选|
|» 楼栋ID列表|string|false|none|楼栋ID列表|按楼栋ID列表筛选|
|isSelf|string|false|none|是否自持|是否自持|
|productType|string|false|none|产品类型|产品类型|
|roomName|string|false|none|房源名称|按房源名称模糊搜索|
|type|string|false|none|类型|1接收 2处置|

<h2 id="tocS_SysBuildingSimpleVo">SysBuildingSimpleVo</h2>

<a id="schemasysbuildingsimplevo"></a>
<a id="schema_SysBuildingSimpleVo"></a>
<a id="tocSsysbuildingsimplevo"></a>
<a id="tocssysbuildingsimplevo"></a>

```json
{
  "id": "string",
  "buildingName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "floors": [
    {
      "id": "string",
      "floorName": "string",
      "rooms": [
        {
          "id": "string",
          "roomName": "string",
          "receiveId": "string",
          "disposalId": "string",
          "floorId": "string",
          "floorName": "string",
          "buildingId": "string",
          "buildingName": "string"
        }
      ]
    }
  ]
}

```

楼栋列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|楼栋ID|none|
|buildingName|string|false|none|楼栋名称|none|
|parcelId|string|false|none|地块ID|none|
|parcelName|string|false|none|地块名称|none|
|floors|[[SysFloorSimpleVo](#schemasysfloorsimplevo)]|false|none|楼层列表|none|

<h2 id="tocS_SysFloorSimpleVo">SysFloorSimpleVo</h2>

<a id="schemasysfloorsimplevo"></a>
<a id="schema_SysFloorSimpleVo"></a>
<a id="tocSsysfloorsimplevo"></a>
<a id="tocssysfloorsimplevo"></a>

```json
{
  "id": "string",
  "floorName": "string",
  "rooms": [
    {
      "id": "string",
      "roomName": "string",
      "receiveId": "string",
      "disposalId": "string",
      "floorId": "string",
      "floorName": "string",
      "buildingId": "string",
      "buildingName": "string"
    }
  ]
}

```

楼层列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|楼层ID|none|
|floorName|string|false|none|楼层名称|none|
|rooms|[[SysRoomSimpleVo](#schemasysroomsimplevo)]|false|none|房间列表|none|

<h2 id="tocS_SysRoomSimpleVo">SysRoomSimpleVo</h2>

<a id="schemasysroomsimplevo"></a>
<a id="schema_SysRoomSimpleVo"></a>
<a id="tocSsysroomsimplevo"></a>
<a id="tocssysroomsimplevo"></a>

```json
{
  "id": "string",
  "roomName": "string",
  "receiveId": "string",
  "disposalId": "string",
  "floorId": "string",
  "floorName": "string",
  "buildingId": "string",
  "buildingName": "string"
}

```

房间列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|房间ID|none|
|roomName|string|false|none|房间名称|none|
|receiveId|string|false|none|接收ID|none|
|disposalId|string|false|none|处置ID|none|
|floorId|string|false|none|楼层ID|none|
|floorName|string|false|none|楼层名称|none|
|buildingId|string|false|none|楼栋ID|none|
|buildingName|string|false|none|楼栋名称|none|

<h2 id="tocS_SysRoomAddDTO">SysRoomAddDTO</h2>

<a id="schemasysroomadddto"></a>
<a id="schema_SysRoomAddDTO"></a>
<a id="tocSsysroomadddto"></a>
<a id="tocssysroomadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmRoomId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomName": "string",
  "productType": "string",
  "areaType": "string",
  "areaTypeName": "string",
  "buildArea": 0,
  "innerArea": 0,
  "isSelf": "string",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|mdmRoomId|string|false|none|主数据房间id|主数据房间id|
|projectId|string|false|none|所属项目id|所属项目id|
|parcelId|string|false|none|地块id|地块id|
|buildingId|string|false|none|楼栋id|楼栋id|
|floorId|string|false|none|楼层id|楼层id|
|roomName|string|false|none|房间名称|房间名称|
|productType|string|false|none|产品类型，字典值|产品类型，字典值|
|areaType|string|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|areaTypeName|string|false|none|面积类型名称|面积类型名称|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|isSelf|string|false|none|是否自持（0否 1是）|是否自持（0否 1是）|
|isDel|integer(int32)|false|none|是否删除（0否 1是）|是否删除（0否 1是）|

<h2 id="tocS_SynMdmProjectQueryDTO">SynMdmProjectQueryDTO</h2>

<a id="schemasynmdmprojectquerydto"></a>
<a id="schema_SynMdmProjectQueryDTO"></a>
<a id="tocSsynmdmprojectquerydto"></a>
<a id="tocssynmdmprojectquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "pmdVersion": "string",
  "projectName": "string",
  "projectSaleName": "string",
  "projectTypeName": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "financialCompanyName": "string",
  "totalSelfArea": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|pmdVersion|string|false|none|版本号|版本号|
|projectName|string|false|none|项目名称|项目名称|
|projectSaleName|string|false|none|销售推广名|销售推广名|
|projectTypeName|string|false|none|项目类型|项目类型|
|provinceCode|string|false|none|省编码|省编码|
|provinceName|string|false|none|省名称|省名称|
|cityCode|string|false|none|市编码|市编码|
|cityName|string|false|none|市名称|市名称|
|countryCode|string|false|none|区编码|区编码|
|countryName|string|false|none|区名称|区名称|
|projectAddress|string|false|none|项目地址|项目地址|
|longitude|string|false|none|地址经度 (实际对应字段为EXTEND_N_COL1)|地址经度 (实际对应字段为EXTEND_N_COL1)|
|latitude|string|false|none|地址维度 (实际对应字段为EXTEND_N_COL2)|地址维度 (实际对应字段为EXTEND_N_COL2)|
|financialCompanyName|string|false|none|财务主体公司名称|财务主体公司名称|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SynMdmBuildingVo">SynMdmBuildingVo</h2>

<a id="schemasynmdmbuildingvo"></a>
<a id="schema_SynMdmBuildingVo"></a>
<a id="tocSsynmdmbuildingvo"></a>
<a id="tocssynmdmbuildingvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "upFloorNums": "string",
  "underFloorNums": "string",
  "totalSelfArea": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

楼栋信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|所属地块id|所属地块id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|upFloorNums|string|false|none|地上楼层数|地上楼层数|
|underFloorNums|string|false|none|地下楼层数|地下楼层数|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SynMdmFloorVo">SynMdmFloorVo</h2>

<a id="schemasynmdmfloorvo"></a>
<a id="schema_SynMdmFloorVo"></a>
<a id="tocSsynmdmfloorvo"></a>
<a id="tocssynmdmfloorvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "buildingId": "string",
  "floorName": "string",
  "floorTypeName": "string",
  "designFloorHeight": "string",
  "designLoad": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

楼层信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|buildingId|string|false|none|所属楼栋id|所属楼栋id|
|floorName|string|false|none|楼层名称|楼层名称|
|floorTypeName|string|false|none|楼层属性|楼层属性|
|designFloorHeight|string|false|none|层高|层高|
|designLoad|string|false|none|设计荷载（KG/m2）|设计荷载（KG/m2）|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SynMdmParcelVo">SynMdmParcelVo</h2>

<a id="schemasynmdmparcelvo"></a>
<a id="schema_SynMdmParcelVo"></a>
<a id="tocSsynmdmparcelvo"></a>
<a id="tocssynmdmparcelvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "stageId": "string",
  "parcelName": "string",
  "parcelNatureName": "string",
  "landUsage": 0,
  "parcelAddress": "string",
  "totalSelfArea": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

地块信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|stageId|string|false|none|所属分期id|所属分期id|
|parcelName|string|false|none|地块名称|地块名称|
|parcelNatureName|string|false|none|主数据用地性质|主数据用地性质|
|landUsage|integer(int32)|false|none|用地性质（1工业用地 2商业用地）|用地性质（1工业用地 2商业用地）|
|parcelAddress|string|false|none|地块地址|地块地址|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SynMdmProjectVo">SynMdmProjectVo</h2>

<a id="schemasynmdmprojectvo"></a>
<a id="schema_SynMdmProjectVo"></a>
<a id="tocSsynmdmprojectvo"></a>
<a id="tocssynmdmprojectvo"></a>

```json
{
  "id": "string",
  "pmdVersion": "string",
  "projectName": "string",
  "projectSaleName": "string",
  "projectTypeName": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "financialCompanyName": "string",
  "totalSelfArea": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0,
  "stages": [
    {
      "id": "string",
      "projectId": "string",
      "stageName": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": 0
    }
  ],
  "parcels": [
    {
      "id": "string",
      "projectId": "string",
      "stageId": "string",
      "parcelName": "string",
      "parcelNatureName": "string",
      "landUsage": 0,
      "parcelAddress": "string",
      "totalSelfArea": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": 0
    }
  ],
  "buildings": [
    {
      "id": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingName": "string",
      "upFloorNums": "string",
      "underFloorNums": "string",
      "totalSelfArea": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": 0
    }
  ],
  "floors": [
    {
      "id": "string",
      "projectId": "string",
      "buildingId": "string",
      "floorName": "string",
      "floorTypeName": "string",
      "designFloorHeight": "string",
      "designLoad": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": 0
    }
  ],
  "rooms": [
    {
      "id": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingId": "string",
      "floorId": "string",
      "roomName": "string",
      "apartmentTypeName": "string",
      "isSale": 0,
      "isCompanySelf": 0,
      "propertyTypeName": "string",
      "selfHoldingTime": "2019-08-24T14:15:22Z",
      "publicAreaTypeName": "string",
      "designBuildArea": "string",
      "predictBuildArea": "string",
      "realBuildArea": "string",
      "designInnerArea": "string",
      "predictInnerArea": "string",
      "realInnerArea": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|pmdVersion|string|false|none|版本号|版本号|
|projectName|string|false|none|项目名称|项目名称|
|projectSaleName|string|false|none|销售推广名|销售推广名|
|projectTypeName|string|false|none|项目类型|项目类型|
|provinceCode|string|false|none|省编码|省编码|
|provinceName|string|false|none|省名称|省名称|
|cityCode|string|false|none|市编码|市编码|
|cityName|string|false|none|市名称|市名称|
|countryCode|string|false|none|区编码|区编码|
|countryName|string|false|none|区名称|区名称|
|projectAddress|string|false|none|项目地址|项目地址|
|longitude|string|false|none|地址经度 (实际对应字段为EXTEND_N_COL1)|地址经度 (实际对应字段为EXTEND_N_COL1)|
|latitude|string|false|none|地址维度 (实际对应字段为EXTEND_N_COL2)|地址维度 (实际对应字段为EXTEND_N_COL2)|
|financialCompanyName|string|false|none|财务主体公司名称|财务主体公司名称|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|
|stages|[[SynMdmStageVo](#schemasynmdmstagevo)]|false|none|分期信息列表|none|
|parcels|[[SynMdmParcelVo](#schemasynmdmparcelvo)]|false|none|地块信息列表|none|
|buildings|[[SynMdmBuildingVo](#schemasynmdmbuildingvo)]|false|none|楼栋信息列表|none|
|floors|[[SynMdmFloorVo](#schemasynmdmfloorvo)]|false|none|楼层信息列表|none|
|rooms|[[SynMdmRoomVo](#schemasynmdmroomvo)]|false|none|房间信息列表|none|

<h2 id="tocS_SynMdmRoomVo">SynMdmRoomVo</h2>

<a id="schemasynmdmroomvo"></a>
<a id="schema_SynMdmRoomVo"></a>
<a id="tocSsynmdmroomvo"></a>
<a id="tocssynmdmroomvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomName": "string",
  "apartmentTypeName": "string",
  "isSale": 0,
  "isCompanySelf": 0,
  "propertyTypeName": "string",
  "selfHoldingTime": "2019-08-24T14:15:22Z",
  "publicAreaTypeName": "string",
  "designBuildArea": "string",
  "predictBuildArea": "string",
  "realBuildArea": "string",
  "designInnerArea": "string",
  "predictInnerArea": "string",
  "realInnerArea": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

房间信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|所属项目id|所属项目id|
|parcelId|string|false|none|地块id|地块id|
|buildingId|string|false|none|楼栋id|楼栋id|
|floorId|string|false|none|楼层id|楼层id|
|roomName|string|false|none|房间名称|房间名称|
|apartmentTypeName|string|false|none|户型产品类型|户型产品类型|
|isSale|integer(int32)|false|none|是否可售|是否可售|
|isCompanySelf|integer(int32)|false|none|是否公司自持|是否公司自持|
|propertyTypeName|string|false|none|自持物业类型|自持物业类型|
|selfHoldingTime|string(date-time)|false|none|自持到期时间|自持到期时间|
|publicAreaTypeName|string|false|none|对外面积类型|对外面积类型|
|designBuildArea|string|false|none|设计建筑面积|设计建筑面积|
|predictBuildArea|string|false|none|预测建筑面积|预测建筑面积|
|realBuildArea|string|false|none|实测建筑面积|实测建筑面积|
|designInnerArea|string|false|none|设计套内面积|设计套内面积|
|predictInnerArea|string|false|none|预测套内面积|预测套内面积|
|realInnerArea|string|false|none|实测套内面积|实测套内面积|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SynMdmStageVo">SynMdmStageVo</h2>

<a id="schemasynmdmstagevo"></a>
<a id="schema_SynMdmStageVo"></a>
<a id="tocSsynmdmstagevo"></a>
<a id="tocssynmdmstagevo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "stageName": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

分期信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|stageName|string|false|none|分期名称|分期名称|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SysRoomQueryDTO">SysRoomQueryDTO</h2>

<a id="schemasysroomquerydto"></a>
<a id="schema_SysRoomQueryDTO"></a>
<a id="tocSsysroomquerydto"></a>
<a id="tocssysroomquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "mdmRoomId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomName": "string",
  "productType": "string",
  "areaType": "string",
  "areaTypeName": "string",
  "buildArea": 0,
  "innerArea": 0,
  "isSale": 0,
  "isCompanySelf": 0,
  "propertyType": "string",
  "propertyTypeName": "string",
  "selfHoldingTime": "2019-08-24T14:15:22Z",
  "status": "string",
  "receiveId": "string",
  "changeId": "string",
  "disposalId": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|mdmRoomId|string|false|none|主数据房间id|主数据房间id|
|projectId|string|false|none|所属项目id|所属项目id|
|parcelId|string|false|none|地块id|地块id|
|buildingId|string|false|none|楼栋id|楼栋id|
|floorId|string|false|none|楼层id|楼层id|
|roomName|string|false|none|房间名称|房间名称|
|productType|string|false|none|产品类型，字典值|产品类型，字典值|
|areaType|string|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|areaTypeName|string|false|none|面积类型|面积类型|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|isSale|integer(int32)|false|none|是否可售（0否 1是）|是否可售（0否 1是）|
|isCompanySelf|integer(int32)|false|none|是否公司自持（0否 1是）|是否公司自持（0否 1是）|
|propertyType|string|false|none|自持物业类型（1非自持 2自持）|自持物业类型（1非自持 2自持）|
|propertyTypeName|string|false|none|自持物业类型名称|自持物业类型名称|
|selfHoldingTime|string(date-time)|false|none|自持到期时间|自持到期时间|
|status|string|false|none|房间状态（0初始 1接收 2处置）|房间状态（0初始 1接收 2处置）|
|receiveId|string|false|none|接收id|接收id|
|changeId|string|false|none|最新变更id|最新变更id|
|disposalId|string|false|none|处置id|处置id|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SysProjectVo">SysProjectVo</h2>

<a id="schemasysprojectvo"></a>
<a id="schema_SysProjectVo"></a>
<a id="tocSsysprojectvo"></a>
<a id="tocssysprojectvo"></a>

```json
{
  "id": "string",
  "type": "string",
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": "string",
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

项目基本信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|type|string|false|none|项目类型（1内部 2外部）|项目类型（1内部 2外部）|
|mdmProjectId|string|false|none|主数据项目ID|主数据项目ID|
|mdmName|string|false|none|主数据项目名称|主数据项目名称|
|mdmSaleName|string|false|none|主数据销售推广名|主数据销售推广名|
|code|string|false|none|编码|编码|
|name|string|false|none|简称|简称|
|provinceCode|string|false|none|省编码|省编码|
|provinceName|string|false|none|省名称|省名称|
|cityCode|string|false|none|市编码|市编码|
|cityName|string|false|none|市名称|市名称|
|countryCode|string|false|none|区编码|区编码|
|countryName|string|false|none|区名称|区名称|
|mdmTypeName|string|false|none|主数据项目类型|主数据项目类型|
|assetType|string|false|none|项目资产分类（1众创城 2科技城 3产城 4外部项目）|项目资产分类（1众创城 2科技城 3产城 4外部项目）|
|propertyUnit|string|false|none|产权单位|产权单位|
|projectAddress|string|false|none|项目地址|项目地址|
|longitude|string|false|none|地址经度|地址经度|
|latitude|string|false|none|地址维度|地址维度|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SysParcelSimpleVo">SysParcelSimpleVo</h2>

<a id="schemasysparcelsimplevo"></a>
<a id="schema_SysParcelSimpleVo"></a>
<a id="tocSsysparcelsimplevo"></a>
<a id="tocssysparcelsimplevo"></a>

```json
{
  "id": "string",
  "parcelName": "string",
  "buildings": [
    {
      "id": "string",
      "buildingName": "string",
      "parcelId": "string",
      "parcelName": "string",
      "floors": [
        {
          "id": "string",
          "floorName": "string",
          "rooms": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|地块ID|none|
|parcelName|string|false|none|地块名称|none|
|buildings|[[SysBuildingSimpleVo](#schemasysbuildingsimplevo)]|false|none|楼栋列表|none|

<h2 id="tocS_SysBuildingQueryDTO">SysBuildingQueryDTO</h2>

<a id="schemasysbuildingquerydto"></a>
<a id="schema_SysBuildingQueryDTO"></a>
<a id="tocSsysbuildingquerydto"></a>
<a id="tocssysbuildingquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "source": "string",
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|source|string|false|none|来源（1主数据 2手动添加）|来源（1主数据 2手动添加）|
|mdmBuildingId|string|false|none|主数据楼栋id|主数据楼栋id|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|所属地块id|所属地块id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|upFloorNums|integer(int64)|false|none|地上楼层数|地上楼层数|
|underFloorNums|integer(int64)|false|none|地下楼层数|地下楼层数|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SysBuildingVo">SysBuildingVo</h2>

<a id="schemasysbuildingvo"></a>
<a id="schema_SysBuildingVo"></a>
<a id="tocSsysbuildingvo"></a>
<a id="tocssysbuildingvo"></a>

```json
{
  "id": "string",
  "source": "string",
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|source|string|false|none|来源（1主数据 2手动添加）|来源（1主数据 2手动添加）|
|mdmBuildingId|string|false|none|主数据楼栋id|主数据楼栋id|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|所属地块id|所属地块id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|upFloorNums|integer(int32)|false|none|地上楼层数|地上楼层数|
|underFloorNums|integer(int32)|false|none|地下楼层数|地下楼层数|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SysParcelVo">SysParcelVo</h2>

<a id="schemasysparcelvo"></a>
<a id="schema_SysParcelVo"></a>
<a id="tocSsysparcelvo"></a>
<a id="tocssysparcelvo"></a>

```json
{
  "id": "string",
  "mdmParcelId": "string",
  "projectId": "string",
  "stageId": "string",
  "parcelName": "string",
  "mdmNatureName": "string",
  "landUsage": 0,
  "mdmAddress": "string",
  "address": "string",
  "totalSelfArea": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

地块信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|mdmParcelId|string|false|none|主数据地块id|主数据地块id|
|projectId|string|false|none|项目id|项目id|
|stageId|string|false|none|所属分期id|所属分期id|
|parcelName|string|false|none|地块名称|地块名称|
|mdmNatureName|string|false|none|主数据用地性质|主数据用地性质|
|landUsage|integer(int32)|false|none|用地性质（1工业用地 2商业用地）|用地性质（1工业用地 2商业用地）|
|mdmAddress|string|false|none|主数据地块地址|主数据地块地址|
|address|string|false|none|合同签约地址|合同签约地址|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SysProjectDetailVo">SysProjectDetailVo</h2>

<a id="schemasysprojectdetailvo"></a>
<a id="schema_SysProjectDetailVo"></a>
<a id="tocSsysprojectdetailvo"></a>
<a id="tocssysprojectdetailvo"></a>

```json
{
  "project": {
    "id": "string",
    "type": "string",
    "mdmProjectId": "string",
    "mdmName": "string",
    "mdmSaleName": "string",
    "code": "string",
    "name": "string",
    "provinceCode": "string",
    "provinceName": "string",
    "cityCode": "string",
    "cityName": "string",
    "countryCode": "string",
    "countryName": "string",
    "mdmTypeName": "string",
    "assetType": "string",
    "propertyUnit": "string",
    "projectAddress": "string",
    "longitude": "string",
    "latitude": "string",
    "totalSelfArea": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": 0
  },
  "stages": [
    {
      "id": "string",
      "mdmStageId": "string",
      "projectId": "string",
      "stageName": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": 0
    }
  ],
  "parcels": [
    {
      "id": "string",
      "mdmParcelId": "string",
      "projectId": "string",
      "stageId": "string",
      "parcelName": "string",
      "mdmNatureName": "string",
      "landUsage": 0,
      "mdmAddress": "string",
      "address": "string",
      "totalSelfArea": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": 0
    }
  ]
}

```

项目详情视图对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|project|[SysProjectVo](#schemasysprojectvo)|false|none||项目基本信息|
|stages|[[SysStageVo](#schemasysstagevo)]|false|none||分期信息列表|
|parcels|[[SysParcelVo](#schemasysparcelvo)]|false|none||地块信息列表|

<h2 id="tocS_SysStageVo">SysStageVo</h2>

<a id="schemasysstagevo"></a>
<a id="schema_SysStageVo"></a>
<a id="tocSsysstagevo"></a>
<a id="tocssysstagevo"></a>

```json
{
  "id": "string",
  "mdmStageId": "string",
  "projectId": "string",
  "stageName": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0
}

```

分期信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|mdmStageId|string|false|none|主数据分期id|主数据分期id|
|projectId|string|false|none|项目id|项目id|
|stageName|string|false|none|分期名称|分期名称|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_R«?»">R«?»</h2>

<a id="schemar«?»"></a>
<a id="schema_R«?»"></a>
<a id="tocSr«?»"></a>
<a id="tocsr«?»"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": null
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|null|false|none||none|

<h2 id="tocS_LoginBody">LoginBody</h2>

<a id="schemaloginbody"></a>
<a id="schema_LoginBody"></a>
<a id="tocSloginbody"></a>
<a id="tocsloginbody"></a>

```json
{
  "username": "string",
  "password": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|username|string|false|none||用户名|
|password|string|false|none||用户密码|

<h2 id="tocS_RegisterBody">RegisterBody</h2>

<a id="schemaregisterbody"></a>
<a id="schema_RegisterBody"></a>
<a id="tocSregisterbody"></a>
<a id="tocsregisterbody"></a>

```json
{
  "username": "string",
  "password": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|username|string|false|none||用户名|
|password|string|false|none||用户密码|

<h2 id="tocS_"></h2>

<a id="schema"></a>
<a id="schema_"></a>
<a id="tocS"></a>
<a id="tocs"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_R">R</h2>

<a id="schemar"></a>
<a id="schema_R"></a>
<a id="tocSr"></a>
<a id="tocsr"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|object|false|none||none|

<h2 id="tocS_key">key</h2>

<a id="schemakey"></a>
<a id="schema_key"></a>
<a id="tocSkey"></a>
<a id="tocskey"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_Map«Object»">Map«Object»</h2>

<a id="schemamap«object»"></a>
<a id="schema_Map«Object»"></a>
<a id="tocSmap«object»"></a>
<a id="tocsmap«object»"></a>

```json
{
  "key": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|[key](#schemakey)|false|none||params.key|

<h2 id="tocS_R«List«SysRole»»">R«List«SysRole»»</h2>

<a id="schemar«list«sysrole»»"></a>
<a id="schema_R«List«SysRole»»"></a>
<a id="tocSr«list«sysrole»»"></a>
<a id="tocsr«list«sysrole»»"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "roleId": 0,
      "roleName": "string",
      "type": 0,
      "remark": "string",
      "roomPermissions": "string",
      "contractPermissions": "string",
      "menuIds": [
        0
      ],
      "deptIds": [
        0
      ],
      "permissions": [
        "string"
      ],
      "admin": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|[[SysRole](#schemasysrole)]|false|none||none|

<h2 id="tocS_R«List«Long»»">R«List«Long»»</h2>

<a id="schemar«list«long»»"></a>
<a id="schema_R«List«Long»»"></a>
<a id="tocSr«list«long»»"></a>
<a id="tocsr«list«long»»"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|[integer]|false|none||none|

<h2 id="tocS_R«Boolean»">R«Boolean»</h2>

<a id="schemar«boolean»"></a>
<a id="schema_R«Boolean»"></a>
<a id="tocSr«boolean»"></a>
<a id="tocsr«boolean»"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|boolean|false|none||none|

<h2 id="tocS_R«LoginUser»">R«LoginUser»</h2>

<a id="schemar«loginuser»"></a>
<a id="schema_R«LoginUser»"></a>
<a id="tocSr«loginuser»"></a>
<a id="tocsr«loginuser»"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "token": "string",
    "userid": 0,
    "username": "string",
    "nickName": "string",
    "loginTime": 0,
    "expireTime": 0,
    "ipaddr": "string",
    "permissions": [
      "string"
    ],
    "projectPermissions": [
      "string"
    ],
    "roomPermissions": [
      "string"
    ],
    "contractPermission": "string",
    "roles": [
      "string"
    ],
    "sysUser": {
      "userId": 0,
      "deptId": 0,
      "userName": "string",
      "nickName": "string",
      "email": "string",
      "phonenumber": "string",
      "sex": "string",
      "avatar": "string",
      "password": "string",
      "status": "string",
      "delFlag": "string",
      "loginIp": "string",
      "loginDate": "2019-08-24T14:15:22Z",
      "roles": [
        {
          "roleId": 0,
          "roleName": "string",
          "type": 0,
          "remark": "string",
          "roomPermissions": "string",
          "contractPermissions": "string",
          "admin": true
        }
      ],
      "admin": true
    },
    "admin": true
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|[LoginUser](#schemaloginuser)|false|none||none|

<h2 id="tocS_key1">key1</h2>

<a id="schemakey1"></a>
<a id="schema_key1"></a>
<a id="tocSkey1"></a>
<a id="tocskey1"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_List">List</h2>

<a id="schemalist"></a>
<a id="schema_List"></a>
<a id="tocSlist"></a>
<a id="tocslist"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_R«List»">R«List»</h2>

<a id="schemar«list»"></a>
<a id="schema_R«List»"></a>
<a id="tocSr«list»"></a>
<a id="tocsr«list»"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {}
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|[[List](#schemalist)]|false|none||none|

<h2 id="tocS_TPrintTemplateEditBo">TPrintTemplateEditBo</h2>

<a id="schematprinttemplateeditbo"></a>
<a id="schema_TPrintTemplateEditBo"></a>
<a id="tocStprinttemplateeditbo"></a>
<a id="tocstprinttemplateeditbo"></a>

```json
{
  "id": "string",
  "templateName": "string",
  "projectId": "string",
  "applyLevel": "string",
  "applyScope": "string",
  "printType": "string",
  "contractPurpose": "string",
  "effectiveDate": "string",
  "expirationDate": "string",
  "status": "string",
  "linkUrl": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string",
  "params": {
    "key": {}
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||主键ID<br />主键ID|
|templateName|string|false|none||模版名称<br />模版名称|
|projectId|string|false|none||项目ID<br />项目ID|
|applyLevel|string|false|none||适用层级：集团/项目<br />适用层级：集团/项目|
|applyScope|string|false|none||适用范围<br />适用范围|
|printType|string|false|none||套打类型：合同/协议<br />套打类型：合同/协议|
|contractPurpose|string|false|none||合同/协议用途：宿舍/非宿舍/多经<br />合同/协议用途：宿舍/非宿舍/多经|
|effectiveDate|string|false|none||生效日期<br />生效日期|
|expirationDate|string|false|none||失效日期<br />失效日期|
|status|string|false|none||状态（0正常 1停用）<br />状态（0正常 1停用）|
|linkUrl|string|false|none||链接地址<br />链接地址|
|remark|string|false|none||备注<br />备注|
|createBy|string|false|none||创建者|
|createByName|string|false|none||none|
|createTime|string|false|none||创建时间|
|updateBy|string|false|none||更新者|
|updateByName|string|false|none||none|
|updateTime|string|false|none||更新时间|
|params|[Map«Object»](#schemamap«object»)|false|none||请求参数|

<h2 id="tocS_TPrintTemplateStatusDto">TPrintTemplateStatusDto</h2>

<a id="schematprinttemplatestatusdto"></a>
<a id="schema_TPrintTemplateStatusDto"></a>
<a id="tocStprinttemplatestatusdto"></a>
<a id="tocstprinttemplatestatusdto"></a>

```json
{
  "templateId": "string",
  "status": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|templateId|string|false|none||模版id|
|status|string|false|none||模版状态|

<h2 id="tocS_GenTableColumn">GenTableColumn</h2>

<a id="schemagentablecolumn"></a>
<a id="schema_GenTableColumn"></a>
<a id="tocSgentablecolumn"></a>
<a id="tocsgentablecolumn"></a>

```json
{
  "columnId": 0,
  "tableId": 0,
  "columnName": "string",
  "columnComment": "string",
  "columnType": "string",
  "javaType": "string",
  "javaField": "string",
  "isPk": "string",
  "isIncrement": "string",
  "isRequired": "string",
  "isInsert": "string",
  "isEdit": "string",
  "isList": "string",
  "isQuery": "string",
  "queryType": "string",
  "htmlType": "string",
  "dictType": "string",
  "sort": 0,
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string",
  "params": {
    "key": {}
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|columnId|integer|false|none||编号|
|tableId|integer|false|none||归属表编号|
|columnName|string|false|none||列名称|
|columnComment|string|false|none||列描述|
|columnType|string|false|none||列类型|
|javaType|string|false|none||JAVA类型|
|javaField|string|true|none||JAVA字段名|
|isPk|string|false|none||是否主键（1是）|
|isIncrement|string|false|none||是否自增（1是）|
|isRequired|string|false|none||是否必填（1是）|
|isInsert|string|false|none||是否为插入字段（1是）|
|isEdit|string|false|none||是否编辑字段（1是）|
|isList|string|false|none||是否列表字段（1是）|
|isQuery|string|false|none||是否查询字段（1是）|
|queryType|string|false|none||查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）|
|htmlType|string|false|none||显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）|
|dictType|string|false|none||字典类型|
|sort|integer|false|none||排序|
|createBy|string|false|none||创建者|
|createByName|string|false|none||none|
|createTime|string|false|none||创建时间|
|updateBy|string|false|none||更新者|
|updateByName|string|false|none||none|
|updateTime|string|false|none||更新时间|
|params|[Map«Object»](#schemamap«object»)|false|none||请求参数|

<h2 id="tocS_GenTable">GenTable</h2>

<a id="schemagentable"></a>
<a id="schema_GenTable"></a>
<a id="tocSgentable"></a>
<a id="tocsgentable"></a>

```json
{
  "tableId": 0,
  "tableName": "string",
  "tableComment": "string",
  "subTableName": "string",
  "subTableFkName": "string",
  "className": "string",
  "tplCategory": "string",
  "tplWebType": "string",
  "packageName": "string",
  "moduleName": "string",
  "businessName": "string",
  "functionName": "string",
  "functionAuthor": "string",
  "genType": "string",
  "genPath": "string",
  "pkColumn": {
    "columnId": 0,
    "tableId": 0,
    "columnName": "string",
    "columnComment": "string",
    "columnType": "string",
    "javaType": "string",
    "javaField": "string",
    "isPk": "string",
    "isIncrement": "string",
    "isRequired": "string",
    "isInsert": "string",
    "isEdit": "string",
    "isList": "string",
    "isQuery": "string",
    "queryType": "string",
    "htmlType": "string",
    "dictType": "string",
    "sort": 0,
    "createBy": "string",
    "createByName": "string",
    "createTime": "string",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "string",
    "params": {
      "key": {}
    }
  },
  "subTable": {
    "tableId": 0,
    "tableName": "string",
    "tableComment": "string",
    "subTableName": "string",
    "subTableFkName": "string",
    "className": "string",
    "tplCategory": "string",
    "tplWebType": "string",
    "packageName": "string",
    "moduleName": "string",
    "businessName": "string",
    "functionName": "string",
    "functionAuthor": "string",
    "genType": "string",
    "genPath": "string",
    "pkColumn": {
      "columnId": 0,
      "tableId": 0,
      "columnName": "string",
      "columnComment": "string",
      "columnType": "string",
      "javaType": "string",
      "javaField": "string",
      "isPk": "string",
      "isIncrement": "string",
      "isRequired": "string",
      "isInsert": "string",
      "isEdit": "string",
      "isList": "string",
      "isQuery": "string",
      "queryType": "string",
      "htmlType": "string",
      "dictType": "string",
      "sort": 0,
      "createBy": "string",
      "createByName": "string",
      "createTime": "string",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "string",
      "params": {
        "key": {}
      }
    },
    "subTable": {
      "tableId": 0,
      "tableName": "string",
      "tableComment": "string",
      "subTableName": "string",
      "subTableFkName": "string",
      "className": "string",
      "tplCategory": "string",
      "tplWebType": "string",
      "packageName": "string",
      "moduleName": "string",
      "businessName": "string",
      "functionName": "string",
      "functionAuthor": "string",
      "genType": "string",
      "genPath": "string",
      "pkColumn": {
        "columnId": 0,
        "tableId": 0,
        "columnName": "string",
        "columnComment": "string",
        "columnType": "string",
        "javaType": "string",
        "javaField": "string",
        "isPk": "string",
        "isIncrement": "string",
        "isRequired": "string",
        "isInsert": "string",
        "isEdit": "string",
        "isList": "string",
        "isQuery": "string",
        "queryType": "string",
        "htmlType": "string",
        "dictType": "string",
        "sort": 0,
        "createBy": "string",
        "createByName": "string",
        "createTime": "string",
        "updateBy": "string",
        "updateByName": "string",
        "updateTime": "string",
        "params": {
          "key": null
        }
      },
      "subTable": {
        "tableId": 0,
        "tableName": "string",
        "tableComment": "string",
        "subTableName": "string",
        "subTableFkName": "string",
        "className": "string",
        "tplCategory": "string",
        "tplWebType": "string",
        "packageName": "string",
        "moduleName": "string",
        "businessName": "string",
        "functionName": "string",
        "functionAuthor": "string",
        "genType": "string",
        "genPath": "string",
        "pkColumn": {
          "columnId": null,
          "tableId": null,
          "columnName": null,
          "columnComment": null,
          "columnType": null,
          "javaType": null,
          "javaField": null,
          "isPk": null,
          "isIncrement": null,
          "isRequired": null,
          "isInsert": null,
          "isEdit": null,
          "isList": null,
          "isQuery": null,
          "queryType": null,
          "htmlType": null,
          "dictType": null,
          "sort": null,
          "createBy": null,
          "createByName": null,
          "createTime": null,
          "updateBy": null,
          "updateByName": null,
          "updateTime": null,
          "params": null
        },
        "subTable": {
          "tableId": null,
          "tableName": null,
          "tableComment": null,
          "subTableName": null,
          "subTableFkName": null,
          "className": null,
          "tplCategory": null,
          "tplWebType": null,
          "packageName": null,
          "moduleName": null,
          "businessName": null,
          "functionName": null,
          "functionAuthor": null,
          "genType": null,
          "genPath": null,
          "pkColumn": null,
          "subTable": null,
          "columns": null,
          "options": null,
          "treeCode": null,
          "treeParentCode": null,
          "treeName": null,
          "parentMenuId": null,
          "parentMenuName": null,
          "remark": null,
          "createBy": null,
          "createByName": null,
          "createTime": null,
          "updateBy": null,
          "updateByName": null,
          "updateTime": null,
          "params": null
        },
        "columns": [
          {}
        ],
        "options": "string",
        "treeCode": "string",
        "treeParentCode": "string",
        "treeName": "string",
        "parentMenuId": 0,
        "parentMenuName": "string",
        "remark": "string",
        "createBy": "string",
        "createByName": "string",
        "createTime": "string",
        "updateBy": "string",
        "updateByName": "string",
        "updateTime": "string",
        "params": {
          "key": null
        }
      },
      "columns": [
        {
          "columnId": 0,
          "tableId": 0,
          "columnName": "string",
          "columnComment": "string",
          "columnType": "string",
          "javaType": "string",
          "javaField": "string",
          "isPk": "string",
          "isIncrement": "string",
          "isRequired": "string",
          "isInsert": "string",
          "isEdit": "string",
          "isList": "string",
          "isQuery": "string",
          "queryType": "string",
          "htmlType": "string",
          "dictType": "string",
          "sort": 0,
          "createBy": "string",
          "createByName": "string",
          "createTime": "string",
          "updateBy": "string",
          "updateByName": "string",
          "updateTime": "string",
          "params": {}
        }
      ],
      "options": "string",
      "treeCode": "string",
      "treeParentCode": "string",
      "treeName": "string",
      "parentMenuId": 0,
      "parentMenuName": "string",
      "remark": "string",
      "createBy": "string",
      "createByName": "string",
      "createTime": "string",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "string",
      "params": {
        "key": {}
      }
    },
    "columns": [
      {
        "columnId": 0,
        "tableId": 0,
        "columnName": "string",
        "columnComment": "string",
        "columnType": "string",
        "javaType": "string",
        "javaField": "string",
        "isPk": "string",
        "isIncrement": "string",
        "isRequired": "string",
        "isInsert": "string",
        "isEdit": "string",
        "isList": "string",
        "isQuery": "string",
        "queryType": "string",
        "htmlType": "string",
        "dictType": "string",
        "sort": 0,
        "createBy": "string",
        "createByName": "string",
        "createTime": "string",
        "updateBy": "string",
        "updateByName": "string",
        "updateTime": "string",
        "params": {
          "key": {}
        }
      }
    ],
    "options": "string",
    "treeCode": "string",
    "treeParentCode": "string",
    "treeName": "string",
    "parentMenuId": 0,
    "parentMenuName": "string",
    "remark": "string",
    "createBy": "string",
    "createByName": "string",
    "createTime": "string",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "string",
    "params": {
      "key": {}
    }
  },
  "columns": [
    {
      "columnId": 0,
      "tableId": 0,
      "columnName": "string",
      "columnComment": "string",
      "columnType": "string",
      "javaType": "string",
      "javaField": "string",
      "isPk": "string",
      "isIncrement": "string",
      "isRequired": "string",
      "isInsert": "string",
      "isEdit": "string",
      "isList": "string",
      "isQuery": "string",
      "queryType": "string",
      "htmlType": "string",
      "dictType": "string",
      "sort": 0,
      "createBy": "string",
      "createByName": "string",
      "createTime": "string",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "string",
      "params": {
        "key": {}
      }
    }
  ],
  "options": "string",
  "treeCode": "string",
  "treeParentCode": "string",
  "treeName": "string",
  "parentMenuId": 0,
  "parentMenuName": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string",
  "params": {
    "key": {}
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tableId|integer|false|none||编号|
|tableName|string|true|none||表名称|
|tableComment|string|true|none||表描述|
|subTableName|string|false|none||关联父表的表名|
|subTableFkName|string|false|none||本表关联父表的外键名|
|className|string|true|none||实体类名称(首字母大写)|
|tplCategory|string|false|none||使用的模板（crud单表操作 tree树表操作 sub主子表操作）|
|tplWebType|string|false|none||前端类型（element-ui模版 element-plus模版）|
|packageName|string|true|none||生成包路径|
|moduleName|string|true|none||生成模块名|
|businessName|string|true|none||生成业务名|
|functionName|string|true|none||生成功能名|
|functionAuthor|string|true|none||生成作者|
|genType|string|false|none||生成代码方式（0zip压缩包 1自定义路径）|
|genPath|string|false|none||生成路径（不填默认项目路径）|
|pkColumn|[GenTableColumn](#schemagentablecolumn)|false|none||主键信息|
|subTable|[GenTable](#schemagentable)|false|none||子表信息|
|columns|[[GenTableColumn](#schemagentablecolumn)]|false|none||表列信息|
|options|string|false|none||其它生成选项|
|treeCode|string|false|none||树编码字段|
|treeParentCode|string|false|none||树父编码字段|
|treeName|string|false|none||树名称字段|
|parentMenuId|integer|false|none||上级菜单ID字段|
|parentMenuName|string|false|none||上级菜单名称字段|
|remark|string|false|none||none|
|createBy|string|false|none||创建者|
|createByName|string|false|none||none|
|createTime|string|false|none||创建时间|
|updateBy|string|false|none||更新者|
|updateByName|string|false|none||none|
|updateTime|string|false|none||更新时间|
|params|[Map«Object»](#schemamap«object»)|false|none||请求参数|

<h2 id="tocS_key2">key2</h2>

<a id="schemakey2"></a>
<a id="schema_key2"></a>
<a id="tocSkey2"></a>
<a id="tocskey2"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_key3">key3</h2>

<a id="schemakey3"></a>
<a id="schema_key3"></a>
<a id="tocSkey3"></a>
<a id="tocskey3"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_key4">key4</h2>

<a id="schemakey4"></a>
<a id="schema_key4"></a>
<a id="tocSkey4"></a>
<a id="tocskey4"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_key5">key5</h2>

<a id="schemakey5"></a>
<a id="schema_key5"></a>
<a id="tocSkey5"></a>
<a id="tocskey5"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_SysJob">SysJob</h2>

<a id="schemasysjob"></a>
<a id="schema_SysJob"></a>
<a id="tocSsysjob"></a>
<a id="tocssysjob"></a>

```json
{
  "jobId": 0,
  "jobName": "string",
  "jobGroup": "string",
  "invokeTarget": "string",
  "cronExpression": "string",
  "misfirePolicy": "0",
  "concurrent": "string",
  "status": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string",
  "params": {
    "key": {}
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|jobId|integer|false|none||任务ID|
|jobName|string|false|none||任务名称|
|jobGroup|string|false|none||任务组名|
|invokeTarget|string|false|none||调用目标字符串|
|cronExpression|string|false|none||cron执行表达式|
|misfirePolicy|string|false|none||cron计划策略|
|concurrent|string|false|none||是否并发执行（0允许 1禁止）|
|status|string|false|none||任务状态（0正常 1暂停）|
|remark|string|false|none||none|
|createBy|string|false|none||创建者|
|createByName|string|false|none||none|
|createTime|string|false|none||创建时间|
|updateBy|string|false|none||更新者|
|updateByName|string|false|none||none|
|updateTime|string|false|none||更新时间|
|params|[Map«Object»](#schemamap«object»)|false|none||请求参数|

<h2 id="tocS_TPrintTemplateAddDTO">TPrintTemplateAddDTO</h2>

<a id="schematprinttemplateadddto"></a>
<a id="schema_TPrintTemplateAddDTO"></a>
<a id="tocStprinttemplateadddto"></a>
<a id="tocstprinttemplateadddto"></a>

```json
{
  "id": "string",
  "templateName": "string",
  "projectId": "string",
  "applyLevel": "string",
  "applyScope": "string",
  "printType": "string",
  "contractPurpose": "string",
  "effectiveDate": "string",
  "expirationDate": "string",
  "status": "string",
  "linkUrl": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||主键ID<br />主键ID|
|templateName|string|false|none||模版名称<br />模版名称|
|projectId|string|false|none||项目ID<br />项目ID|
|applyLevel|string|false|none||适用层级（1集团 2项目）<br />适用层级（1集团 2项目）|
|applyScope|string|false|none||适用范围<br />适用范围， 适用层级为项目时，传项目名称|
|printType|string|false|none||套打类型（1合同 2协议）<br />套打类型（1合同 2协议）|
|contractPurpose|string|false|none||合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）<br />合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|
|effectiveDate|string|false|none||生效日期<br />生效日期|
|expirationDate|string|false|none||失效日期<br />失效日期|
|status|string|false|none||状态（0新增 1生效 2失效）<br />状态（0新增 1生效 2失效）|
|linkUrl|string|false|none||链接地址<br />链接地址|
|remark|string|false|none||备注<br />备注|
|createBy|string|false|none||创建者|
|createByName|string|false|none||none|
|createTime|string|false|none||创建时间|
|updateBy|string|false|none||更新者|
|updateByName|string|false|none||none|
|updateTime|string|false|none||更新时间|

<h2 id="tocS_TPrintTemplateDictAddDTO">TPrintTemplateDictAddDTO</h2>

<a id="schematprinttemplatedictadddto"></a>
<a id="schema_TPrintTemplateDictAddDTO"></a>
<a id="tocStprinttemplatedictadddto"></a>
<a id="tocstprinttemplatedictadddto"></a>

```json
{
  "id": "string",
  "parentId": "string",
  "name": "string",
  "type": 0,
  "code": "string",
  "dbField": "string",
  "sqlText": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||主键ID<br />主键ID|
|parentId|string|false|none||父字典id<br />父字典id|
|name|string|false|none||名称<br />名称|
|type|integer|false|none||字段类型（0字段 1表格）<br />字段类型（0字段 1表格）|
|code|string|false|none||模板字段code<br />模板字段code|
|dbField|string|false|none||数据库字段<br />数据库字段|
|sqlText|string|false|none||sql语句<br />sql语句|
|remark|string|false|none||字典解释说明<br />字典解释说明|
|createBy|string|false|none||创建者|
|createByName|string|false|none||none|
|createTime|string|false|none||创建时间|
|updateBy|string|false|none||更新者|
|updateByName|string|false|none||none|
|updateTime|string|false|none||更新时间|

