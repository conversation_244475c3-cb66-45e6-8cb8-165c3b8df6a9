<template>
    <div class="pending-bill">
        <div class="bill-title">
            <div class="title-decorator"></div>
            待收账单
        </div>
        <div class="bill-content">
            <div class="bill-item">
                <div class="bill-left">
                    <div class="bill-label">逾期待收</div>
                    <div class="bill-count">6<span class="unit">笔</span></div>
                </div>
                <div class="bill-amount-card">
                    <img class="bill-icon" src="@/assets/images/dashboard/icon-bill.png" alt="账单" />
                    <div class="bill-amount">39.3<span class="unit">万元</span></div>
                </div>
            </div>
            <div class="divider"></div>
            <div class="bill-item">
                <div class="bill-left">
                    <div class="bill-label">15天内待收</div>
                    <div class="bill-count">16<span class="unit">笔</span></div>
                </div>
                <div class="bill-amount-card">
                    <img class="bill-icon" src="@/assets/images/dashboard/icon-bill.png" alt="账单" />
                    <div class="bill-amount">168.5<span class="unit">万元</span></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

</script>

<style lang="less" scoped>
.pending-bill {
    background: #fff;
    border-radius: 10px 0 0 0;
    padding: 20px 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    .bill-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
        color: #000;
        position: relative;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        z-index: 2;

        .title-decorator {
            width: 5px;
            height: 19px;
            background-color: #0071ff;
            border-radius: 4px 0px 4px 0px;
            margin-right: 14px;
        }
    }

    .bill-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 8px;
        position: relative;
        border-radius: 10px 0 0 0;
        background: linear-gradient(90deg, rgba(54, 161, 246, 0.14) 0%, rgba(255, 255, 255, 0) 100%);

        .bill-item {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 0 16px;

            .bill-left {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .bill-label {
                font-size: 14px;
                color: #3A4252;
                line-height: 1.4;
                margin-bottom: 4px;
                min-width: 70px;
            }

            .bill-count {
                font-size: 20px;
                color: #3A4252;
                line-height: 1.4;
                min-width: 30px;
                font-weight: 500;

                .unit {
                    font-size: 14px;
                }
            }

            .bill-amount-card {
                flex: 1;
                height: 50px;
                background: linear-gradient(90deg, #F2F9FF 0%, #EEF6FF 100%);
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
                padding: 0 20px;

                .bill-icon {
                    width: 20px;
                    height: 21px;
                    object-fit: contain;
                }

                .bill-amount {
                    font-size: 20px;
                    font-weight: 600;
                    color: #5D79A1;
                    line-height: 1.4;

                    .unit {
                        font-size: 14px;
                    }

                    &.danger {
                        color: rgb(var(--danger-6));
                    }
                }
            }
        }

        .divider {
            height: 1px;
            background: #E9E9E9;
            margin: 8px 16px;
            flex-shrink: 0;
        }
    }
}
</style>