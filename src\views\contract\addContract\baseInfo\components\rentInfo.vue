<template>
    <section>
        <sectionTitle title="租金信息" />
        <div class="content">
            <a-form :disabled="isDetailMode" :model="contractData" :rules="isDetailMode ? {} : rules" ref="formRef"
                auto-label-width layout="vertical">
                <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                    <a-grid-item>
                        <a-form-item label="收费方式" field="chargeWay">
                            <a-select :disabled="changeType.includes(3)" v-model="contractData.chargeWay"
                                placeholder="请选择" @change="handleChargeWayChange">
                                <a-option :value="0">固定租金</a-option>
                                <a-option :value="1">递增租金</a-option>
                                <a-option v-if="contractData.contractType === 0" :value="2">营收抽成</a-option>
                            </a-select>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="应收日期" field="rentReceivableDate">
                            <a-input-group>
                                <a-select v-model="contractData.rentReceivableType" style="width: 200px">
                                    <a-option :value="1">租期开始前付</a-option>
                                    <a-option :value="2">租期开始后付</a-option>
                                </a-select>
                                <a-input-number v-model="contractData.rentReceivableDate" placeholder="请输入" :min="1"
                                    :precision="0">
                                    <template #suffix>天</template>
                                </a-input-number>
                            </a-input-group>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="支付周期" field="rentPayPeriod">
                            <a-input-number
                                :disabled="contractData.chargeWay === 2 && contractData.percentageType === 2 && contractData.revenueType === 2"
                                v-model="contractData.rentPayPeriod" placeholder="请输入" :min="1" :precision="0">
                                <template #suffix>个月一付</template>
                            </a-input-number>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item v-if="contractData.contractMode !== 1">
                        <a-form-item label="账单周期" field="rentTicketPeriod">
                            <a-select v-model="contractData.rentTicketPeriod">
                                <a-option v-if="contractData.chargeWay !== 2" :value="1">租赁月</a-option>
                                <a-option :value="2">自然月</a-option>
                            </a-select>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item v-if="contractData.contractMode === 1 && contractData.chargeWay === 1">
                        <a-form-item label="租金递增规则" field="increaseRule">
                            <a-input v-model="contractData.increaseRule" placeholder="请输入租金递增规则说明" maxlength="200" />
                        </a-form-item>
                    </a-grid-item>
                    <template v-if="contractData.chargeWay === 1">
                        <a-grid-item>
                            <a-form-item label="递增间隔" field="increaseGap">
                                <a-input-number v-model="contractData.increaseGap" placeholder="请输入" :min="0"
                                    :precision="0">
                                    <template #suffix>年</template>
                                </a-input-number>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="单价递增率" field="increaseRate">
                                <a-input-number v-model="contractData.increaseRate" placeholder="请输入" :min="0"
                                    :max="100" :precision="2">
                                    <template #suffix>%</template>
                                </a-input-number>
                            </a-form-item>
                        </a-grid-item>
                    </template>
                    <template v-if="contractData.chargeWay === 2">
                        <a-grid-item>
                            <a-form-item label="预计月营收金额" field="estimateRevenue">
                                <a-input-number v-model="contractData.estimateRevenue" placeholder="请输入" :min="0"
                                    :precision="2" :formatter="formatter" :parser="parser">
                                    <template #suffix>元</template>
                                </a-input-number>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item label="抽成类型" field="percentageType">
                                <a-radio-group v-model="contractData.percentageType" @change="getRentPayPeriod">
                                    <a-radio :value="1">固定</a-radio>
                                    <a-radio :value="2">阶梯</a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item v-if="contractData.percentageType === 1">
                            <a-form-item label="固定提成比例" field="fixedPercentage">
                                <a-input-number v-model="contractData.fixedPercentage" placeholder="请输入" :min="0"
                                    :max="100" :precision="2">
                                    <template #suffix>%</template>
                                </a-input-number>
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item v-if="contractData.chargeWay === 2 && contractData.percentageType === 2">
                            <a-form-item label="阶梯类型" field="revenueType">
                                <a-select v-model="contractData.revenueType" @change="getRentPayPeriod">
                                    <a-option :value="1">按月</a-option>
                                    <a-option :value="2">按年</a-option>
                                </a-select>
                            </a-form-item>
                        </a-grid-item>
                    </template>
                </a-grid>
                <!-- 阶梯抽成表格 -->
                <template v-if="contractData.chargeWay === 2 && contractData.percentageType === 2">
                    <a-row justify="end" style="margin: 16px 0" v-if="!isDetailMode">
                        <a-button type="primary" @click="handleAddLadder">新增阶梯</a-button>
                    </a-row>
                    <a-table :data="ladderData" :pagination="false" style="margin-bottom: 16px;" :scroll="{ x: 1 }"
                        :bordered="{ cell: true }">
                        <template #columns>
                            <a-table-column :title="contractData.revenueType === 1 ? '月营收额' : '年营收额'" align="center">
                                <template #cell="{ record }">
                                    <div style="display: flex; align-items: center;">
                                        <a-input-number v-model="record.amount"
                                            :placeholder="record.order === ladderData.length ? '请输入' : '请输入'"
                                            :disabled="(record.order === ladderData.length && record.disabled) || isDetailMode"
                                            :precision="2" :min="0"
                                            @change="record.order === ladderData.length ? handleLastRowBlur(record.amount) : handleBelowRowBlur(record.amount, record.order - 1)" />
                                        <span style="flex-shrink: 0;margin-left: 8px; width: 80px;text-align: left;">{{
                                            record.order === ladderData.length ?
                                                '以上' : '及以下' }}</span>
                                    </div>
                                </template>
                            </a-table-column>
                            <a-table-column title="阶梯提成比例" align="center">
                                <template #cell="{ record }">
                                    <a-input-number v-model="record.percentage" placeholder="请输入" :min="0" :max="100"
                                        :precision="2">
                                        <template #suffix>%</template>
                                    </a-input-number>
                                </template>
                            </a-table-column>
                            <a-table-column v-if="!isDetailMode" title="操作" align="center" fixed="right" :width="80">
                                <template #cell="{ record, rowIndex }">
                                    <a-button v-if="record.order < ladderData.length" type="text" size="mini"
                                        status="danger" @click="handleDeleteLadder(rowIndex)">删除</a-button>
                                </template>
                            </a-table-column>
                        </template>
                    </a-table>
                </template>

                <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                    <!-- 物业费相关字段 -->
                    <a-grid-item>
                        <a-form-item label="物业费是否另收" field="isIncludePm">
                            <a-radio-group v-model="contractData.isIncludePm" @change="handlePmChange">
                                <a-radio :value="true">是</a-radio>
                                <a-radio :value="false">否</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-grid-item>
                    <template v-if="contractData.isIncludePm">
                        <a-grid-item>
                            <a-form-item label="月物业费单价" field="pmUnitPrice">
                                <a-input-number v-model="contractData.pmUnitPrice" placeholder="请输入" :min="0"
                                    :precision="2" :formatter="formatter" :parser="parser">
                                    <template #suffix>元/㎡/月</template>
                                </a-input-number>
                            </a-form-item>
                        </a-grid-item>
                        <!-- <a-grid-item>
                            <a-form-item label="月物业费总价" field="pmMonthlyPrice">
                                <a-input-number v-model="contractData.pmMonthlyPrice" placeholder="请输入" :min="0"
                                    :precision="2" :formatter="formatter" :parser="parser">
                                    <template #suffix>元/月</template>
                                </a-input-number>
                            </a-form-item>
                        </a-grid-item> -->
                        <a-grid-item>
                            <a-form-item label="物业费支付周期" field="pmPayPeriod">
                                <a-radio-group v-model="contractData.pmPayPeriod">
                                    <a-radio :value="0">年</a-radio>
                                    <a-radio :value="1">按租金账单</a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </a-grid-item>
                    </template>
                </a-grid>
            </a-form>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Message, type FormInstance } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const formatter = (value: string) => {
    const values = value.split('.');
    values[0] = values[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return values.join('.')
};

const parser = (value: string) => {
    return value.replace(/,/g, '')
};

const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || (editType.value === 'change' && !changeType.value.includes(3)) || editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo')
const formItemMargin = computed(() => isDetailMode.value ? '16px' : '16px')

// 阶梯数据
// 阶梯数据接口定义
interface LadderItem {
    monthlyRevenue?: number | undefined,
    amount: number | undefined,
    percentage: number | undefined,
    order: number,
    disabled: boolean // 明确定义为必需属性
}

// 修改为空数组，默认不带数据
const ladderData = ref<LadderItem[]>([])

// 监听阶梯数据变化，进行数值验证
// 移除原有的 watch 监听器中的实时校验，只保留基本监听
watch(ladderData, (newVal) => {
    // 这里可以保留一些基本的数据同步逻辑，但不做实时校验
}, { deep: true });

// 新增：处理"及以下"行的失焦校验
const handleBelowRowBlur = (value: number | undefined, rowIndex: number) => {
    if (value === undefined) return;

    let hasError = false;

    // 检查与上一行的关系
    if (rowIndex > 0) {
        const prevValue = ladderData.value[rowIndex - 1].amount;
        if (prevValue !== undefined && value <= prevValue) {
            hasError = true;
        }
    }

    // 检查与下一行的关系（如果下一行不是"以上"行）
    if (rowIndex < ladderData.value.length - 2) {
        const nextValue = ladderData.value[rowIndex + 1].amount;
        if (nextValue !== undefined && value >= nextValue) {
            hasError = true;
        }
    }

    if (hasError) {
        Message.error('阶梯数值必须递增！');
        // 使用 nextTick 确保界面更新
        nextTick(() => {
            ladderData.value[rowIndex].amount = undefined;
        });
        return;
    }

    // 如果是倒数第二行（最后一个"及以下"），自动联动最后一行的"以上"
    if (rowIndex === ladderData.value.length - 2) {
        const lastIndex = ladderData.value.length - 1;
        ladderData.value[lastIndex].amount = value;

        // 新增：设置"以上"列为不可编辑
        nextTick(() => {
            ladderData.value[lastIndex].disabled = true;
        });
    }
};

// 修改 handleLastRowBlur 方法，确保响应式更新
const handleLastRowBlur = (value: number | undefined) => {
    const lastIndex = ladderData.value.length - 1;
    if (lastIndex > 0 && value !== undefined) {
        // 自动填充上一行的"及以下"数据，但不设为不可编辑
        ladderData.value[lastIndex - 1].amount = value;

        // 验证"及以下"列的递增关系
        if (lastIndex > 1) {
            const prevValue = ladderData.value[lastIndex - 2].amount;
            if (prevValue !== undefined && prevValue >= value) {
                Message.error('阶梯数值必须递增！');
                // 使用 nextTick 确保界面更新
                nextTick(() => {
                    ladderData.value[lastIndex].amount = undefined;
                    ladderData.value[lastIndex - 1].amount = undefined;
                    ladderData.value[lastIndex].disabled = false;
                });
                return;
            }
        }

        // 设置"以上"列为不可编辑 - 使用 nextTick 确保响应式更新
        nextTick(() => {
            ladderData.value[lastIndex].disabled = true;
        });
    } else if (lastIndex > 0 && value === undefined) {
        // 如果"以上"的值被清空，则恢复"以上"列的编辑状态
        nextTick(() => {
            ladderData.value[lastIndex].disabled = false;
        });
    }
};

// 统一表单校验规则
const rules = computed(() => {
    if (isDetailMode.value) return {}
    return {
        chargeWay: [{ required: true, message: '请选择收费方式' }],
        rentReceivableType: [{ required: true, message: '请选择应收日期类型' }],
        rentReceivableDate: [{ required: contractData.value.contractMode !== 1, message: '请输入应收日期天数' }],
        rentPayPeriod: [{ required: contractData.value.contractMode !== 1, message: '请输入支付周期' }],
        rentTicketPeriod: [{ required: true, message: '请选择账单周期' }],
        // 增加租金递增规则的必填校验
        // increaseRule: [{ required: contractData.value.contractMode === 1, message: '请输入租金递增规则' }],
        increaseGap: [{ required: contractData.value.chargeWay === 1, message: '请输入递增间隔' }],
        increaseRate: [{ required: contractData.value.chargeWay === 1, message: '请输入单价递增率' }],
        estimateRevenue: [{ required: contractData.value.chargeWay === 2, message: '请输入预计月营收金额' }],
        percentageType: [{ required: contractData.value.chargeWay === 2, message: '请选择抽成类型' }],
        fixedPercentage: [{ required: contractData.value.chargeWay === 2 && contractData.value.percentageType === 1, message: '请输入固定提成比例' }],
        revenueType: [{ required: contractData.value.chargeWay === 2, message: '请选择抽点类型' }],
        isIncludePm: [{ required: true, message: '请选择物业费是否另收' }],
        pmUnitPrice: [{ required: contractData.value.isIncludePm, message: '请输入月物业费单价' }],
        // pmMonthlyPrice: [{ required: contractData.value.isIncludePm, message: '请输入月物业费总价' }],
        pmPayPeriod: [{ required: contractData.value.isIncludePm, message: '请选择物业费支付周期' }],
    }
})

// 新增阶梯
const handleAddLadder = () => {
    // 如果列表为空，添加及以下和以上两条数据
    if (ladderData.value.length === 0) {
        ladderData.value.push({
            amount: undefined,
            percentage: undefined,
            order: 1,
            disabled: false
        });
        ladderData.value.push({
            amount: undefined,
            percentage: undefined,
            order: 2,
            disabled: false
        });
    } else {
        // 如果列表已有数据，在倒数第二位置添加一条新的及以下数据
        const lastIndex = ladderData.value.length - 1;
        ladderData.value.splice(lastIndex, 0, {
            amount: undefined,
            percentage: undefined,
            order: lastIndex + 1,
            disabled: false
        });
    }

    // 重新排序
    ladderData.value.forEach((item, index) => {
        item.order = index + 1;
    });
}

// 删除阶梯
const handleDeleteLadder = (index: number) => {
    console.log(index);
    if (index !== -1 && index !== ladderData.value.length - 1) { // 不允许删除最后一行（以上）
        ladderData.value.splice(index, 1);
        // 重新排序
        ladderData.value.forEach((item, index) => {
            item.order = index + 1;
        });
    }
}

// 处理收费方式变化
const handleChargeWayChange = (newVal: number) => {
    // 重置递增租金相关字段
    if (newVal !== 1) {
        contractData.value.increaseGap = undefined
        contractData.value.increaseRate = undefined
    }

    // 重置营收抽成相关字段
    if (newVal !== 2) {
        contractData.value.estimateRevenue = undefined
        contractData.value.percentageType = 1
        contractData.value.fixedPercentage = undefined
        contractData.value.revenueType = 1
        ladderData.value = []
    }

    if (newVal === 2) {
        contractData.value.rentTicketPeriod = 2
        if (contractData.value.percentageType === 2 && contractData.value.revenueType === 2) {
            contractData.value.rentPayPeriod = 12
        }
    }

}

const getRentPayPeriod = () => {
    if (contractData.value.chargeWay === 2 && contractData.value.percentageType === 2 && contractData.value.revenueType === 2) {
        contractData.value.rentPayPeriod = 12
    }
}

// 处理物业费变化
const handlePmChange = (isInclude: boolean) => {
    if (!isInclude) {
        // 不包含物业费时，清空物业费相关字段
        contractData.value.pmUnitPrice = undefined
        contractData.value.pmMonthlyPrice = undefined
    }
}

const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('租金信息', errors);
            reject()
        } else {
            // 如果是阶梯抽成，还需要验证阶梯数据
            if (contractData.value.chargeWay === 2 && contractData.value.percentageType === 2) {
                // 如果阶梯数据为空，则不需要验证
                if (ladderData.value.length === 0) {
                    contractData.value.stepPercentage = ''
                } else {
                    // 验证阶梯数据完整性
                    for (const ladder of ladderData.value) {
                        if (!ladder.amount || !ladder.percentage) {
                            reject()
                            return
                        }
                    }
                    contractData.value.stepPercentage = JSON.stringify(ladderData.value)
                }
            }
            resolve(true)
        }
    })
}

defineExpose({
    validate
})

watch(() => contractData.value.stepPercentage, (val) => {
    if (val && val.length > 0) {
        ladderData.value = (JSON.parse(val) as LadderItem[]).map(item => {
            return {
                ...item,
                // 兼容旧数据
                amount: item.monthlyRevenue ? Number(item.monthlyRevenue) : item.amount ? Number(item.amount) : undefined,
                percentage: item.percentage ? Number(item.percentage) : undefined
            }
        })
    } else {
        // 修改为空数组，默认不带数据
        ladderData.value = []
    }
}, { deep: true })
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.detail-text {
    padding: 4px 0;
    color: #333;
}

.arco-form-item {
    margin-bottom: v-bind(formItemMargin);
}
</style>