<template>
    <div class="collect-code-container">
        <div class="collect-code-header">
            <div class="title">扫码支付定金</div>
            <div class="room-info">意向房源：{{ data.intentRoom }}</div>
            <div class="customer-info">客户姓名：{{ data.customerName }}</div>
            <div class="amount-info">支付金额：{{ formatAmount(data.amount) }}元</div>
        </div>

        <div class="qrcode-wrapper">
            <img 
                v-if="data.qrcodeUrl" 
                :src="data.qrcodeUrl" 
                class="qrcode-image" 
                alt="收款码"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

interface CollectCodeData {
    intentRoom: string
    customerName: string
    amount: number
    qrcodeUrl: string
}

const props = defineProps<{
    data: CollectCodeData
}>()

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) {
        return '0.00'
    }
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}
</script>

<style scoped lang="less">
.collect-code-container {
    padding: 24px;
    text-align: center;

    .collect-code-header {
        margin-bottom: 24px;

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
        }

        .room-info,
        .customer-info,
        .amount-info {
            margin-bottom: 8px;
            color: #333;
        }
    }

    .qrcode-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;

        .qrcode-image {
            width: 200px;
            height: 200px;
        }
    }
}
</style> 