<template>
  <a-drawer
    v-model:visible="visible"
    title="房源合并"
    @cancel="handleCancel"
    :footer="!isReadOnly"
    class="common-drawer"
  >
    <div class="merge-property">
      <!-- 原房源信息 -->
      <div class="section">
        <div class="section-header">
            <section-title title="待合并房源信息" class="first-child"/>
            <a-button type="text" @click="handleAddProperty" v-if="!isReadOnly">
              <template #icon><icon-plus /></template>
              添加房源
            </a-button>
        </div>
        <a-table
          :columns="originalColumns"
          :data="originalProperties"
          :pagination="false"
          :bordered="{ cell: true }"
          :scroll="{x: 1}"
        >
          <template #propertyType="{ record }">
            {{ getDictLabel(propertyTypeOptions, record.propertyType) }}
          </template>
          <template #operations="{  record, index  }">
            <a-space>
              <a-link @click="() => handleViewDetail(record)">详情</a-link>
              <a-button
                v-if="originalProperties.length > 1 && !isReadOnly"
                type="text"
                size="mini"
                status="danger"
                @click="() => handleRemoveProperty(index)"
                >
                删除
              </a-button>
            </a-space>
          </template>
        </a-table>
      </div>

      <!-- 合并后房源信息 -->
      <div class="section">
        <section-title title="合并后房源信息" />
        <a-table
          :columns="mergedColumns"
          :data="[mergedProperty]"
          :pagination="false"
          :bordered="{ cell: true }"
          :scroll="{x: 1}"
        >
          <template #roomName="{ record }">
            <a-input v-model="record.roomName" placeholder="请输入房源名称" :disabled="isReadOnly" />
          </template>
          <template #propertyType="{ record }">
            <a-tree-select
                v-model="record.propertyType"
                :data="propertyTypeOptions"
                placeholder="请选择物业类型"
                allow-clear
                :disabled="isReadOnly"
                :field-names="{
                    key: 'dictValue',
                    title: 'dictLabel',
                    children: 'childList'
                }"
            />
          </template>
          <template #buildArea="{ record }">
            <a-input-number 
              v-model="record.buildArea" 
              placeholder="请输入建筑面积" 
              :precision="2" 
              :min="0"
              :disabled="isReadOnly"
              style="width: 120px"
            />
          </template>
          <template #innerArea="{ record }">
            <a-input-number 
              v-model="record.innerArea" 
              placeholder="请输入套内面积" 
              :precision="2" 
              :min="0"
              :disabled="isReadOnly"
              style="width: 120px"
            />
          </template>
          <template #operations="{ record }">
            <a-link @click="() => handleMoreInfo(0)">{{ isReadOnly ? '查看更多' : '更多信息编辑' }}</a-link>
          </template>
        </a-table>
      </div>

      <!-- 生效时间 -->
      <div class="section">
        <section-title title="生效时间" />
        <a-form label-align="right" :model="formData"
        layout="horizontal"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        auto-label-width>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="effectiveType" label="生效方式">
                <a-radio-group v-model="formData.effectiveType" :disabled="isReadOnly">
                  <a-radio value="immediate">立即生效</a-radio>
                  <a-radio value="scheduled">到期生效</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="8" v-if="formData.effectiveType === 'scheduled'">
              <a-form-item field="effectiveDate" label="生效日期" validate-trigger="blur" :rules="[{ required: true, message: '请选择生效日期' }]">
                <a-date-picker 
                  v-model="formData.effectiveDate" 
                  :disabled-date="disabledDate"
                  :disabled="isReadOnly"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8" v-if="maxContractEndDate">
              <div class="contract-end-date"> （合同结束日期：{{ maxContractEndDate }}）</div>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="16">
              <a-form-item field="adjustReason" label="调整原因">
                <a-textarea
                  v-model="formData.adjustReason"
                  placeholder="请输入调整原因"
                  :max-length="100"
                  show-word-limit
                  allow-clear
                  :auto-size="{ minRows: 3, maxRows: 5 }"
                  :disabled="isReadOnly"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="16">
              <a-form-item field="attachments" label="相关附件">
                <upload-file v-model="formData.attachments" :disabled="isReadOnly" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button @click="() => handleConfirm(1)">暂存</a-button>
          <a-button type="primary" @click="() => handleConfirm(2)">提交</a-button>
        </a-space>
      </div>
    </template>
  </a-drawer>

  <!-- 添加房源抽屉 -->
  <AddPropertyDrawer
    v-if="showAddPropertyDrawer"
    ref="addPropertyDrawerRef"
    @cancel="showAddPropertyDrawer = false"
    @confirm="handleAddPropertyConfirm"
  />

  <!-- 选择房源弹窗 -->
  <a-modal
    v-model:visible="showSelectRoomsModal"
    title="选择房源"
    :width="1000"
    @cancel="showSelectRoomsModal = false"
  >
    <div class="select-rooms-modal">
      <!-- 搜索区域 -->
      <div class="search-area">
        <a-input
          v-model="searchForm.roomName"
          placeholder="请输入房源名称"
          allow-clear
          style="width: 200px; margin-right: 16px;"
        />
        <a-button type="primary" @click="searchRooms">
          <template #icon><icon-search /></template>
          搜索
        </a-button>
      </div>
      <div class="select-rooms-modal-content" style="height: 55vh; overflow-y: auto;">
        <!-- 表格区域 -->
        <a-table
          :columns="roomColumns"
          :data="roomList"
          :pagination="pagination"
          :loading="roomLoading"
          :row-selection="rowSelection"
          @page-change="onPageChange"
          @selection-change="handleSelectionChange"
          :bordered="{ cell: true }"
          row-key="id"
        />
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-space>
          <a-button @click="showSelectRoomsModal = false">取消</a-button>
          <a-button type="primary" @click="handleConfirmSelectRooms">确定</a-button>
        </a-space>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import uploadFile from '@/components/upload/uploadFile.vue'
import AddPropertyDrawer from './addPropertyDrawer.vue'
import { getDictLabel } from '@/utils/dict'
import { 
  getRoomDetail, 
  getMaxContractEndDate, 
  mergeRoom, 
  getMergeDetail,
  getRoomList,
  RoomStatus,
  type RoomVo,
  type RoomQueryDTO,
  RoomType
} from '@/api/room'
import { useDictSync } from '@/utils/dict'

interface Property {
  id?: string
  roomName?: string
  propertyType?: string
  buildArea?: number
  innerArea?: number
  layout?: string
  parcelName?: string
  buildingName?: string
  floorName?: string
  projectId?: string
  parcelId?: string
  buildingId?: string
  floorId?: string
  rentAreaType?: number
  rentArea?: number
  index?: number
  [key: string]: any
}

// 字典数据
interface DictData {
    dictCode: string;
    dictLabel: string;
    dictValue: string;
    dictSort: number;
    parentCode?: string;
    childList?: DictData[];
    [key: string]: any;
}

const propertyTypeOptions = ref<DictData[]>([])
const visible = ref(false)
const isReadOnly = ref(false)

// 添加当前选择的项目信息
const currentSelection = ref({
  projectId: '',
  projectName: '',
  blockId: '',
  blockName: '',
  buildingId: '',
  buildingName: ''
})

// 记录最大合同结束日期
const maxContractEndDate = ref<string | null>(null)

// 当前编辑的合并记录ID
const currentMergeId = ref<string | null>(null)

// 原房源信息表格列定义
const originalColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '房源名称', dataIndex: 'roomName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼栋', dataIndex: 'buildingName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼层', dataIndex: 'floorName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '物业类型', dataIndex: 'propertyType', slotName: 'propertyType', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '建筑面积(㎡)', dataIndex: 'buildArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '套内面积(㎡)', dataIndex: 'innerArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '计租面积(㎡)', dataIndex: 'rentArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '操作', slotName: 'operations', width: 160, ellipsis: false, tooltip: false, align: 'center',fixed: 'right' }
]

// 合并后房源表格列定义
const mergedColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '房源名称', slotName: 'roomName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼栋', dataIndex: 'buildingName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '楼层', dataIndex: 'floorName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '物业类型', slotName: 'propertyType', width: 160, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '建筑面积(㎡)', slotName: 'buildArea', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '套内面积(㎡)', slotName: 'innerArea', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '计租面积(㎡)', dataIndex: 'rentArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '操作', slotName: 'operations', width: 160, ellipsis: false, tooltip: false, align: 'center',fixed: 'right' }
]

// 原房源信息列表
const originalProperties = ref<Property[]>([])

// 合并后房源信息
const mergedProperty = ref<Property>({
  roomName: '',
  propertyType: '',
  buildArea: 0,
  innerArea: 0,
  parcelName: '',
  buildingName: '',
  floorName: '',
  index: 1
})

// 表单数据
const formData = ref({
  effectiveType: 'immediate',
  effectiveDate: null as string | null,
  adjustReason: '',
  attachments: '' as string
})

// 加载字典数据
const getPropertyTypeDicts = async () => {
    try {
        const dictData = await useDictSync('property_type')
        if (dictData.property_type) {
            // 处理树形结构数据
            const dictList = dictData.property_type as DictData[]
            // 组织成树形结构
            const treeData = dictList.filter(item => !item.parentCode)
            treeData.forEach(parent => {
                parent.childList = dictList.filter(child => child.parentCode === parent.dictCode)
            })
            propertyTypeOptions.value = treeData
        }
    } catch (error) {
        console.error('获取物业类型字典数据失败:', error)
    }
}

const emit = defineEmits(['confirm', 'cancel'])

// 引入房源详情组件
const addPropertyDrawerRef = ref()
const showAddPropertyDrawer = ref(false)

// 引入选择房源弹窗
const selectRoomsModalRef = ref()
const showSelectRoomsModal = ref(false)

// 房源选择相关数据
const roomList = ref<Property[]>([])
const roomLoading = ref(false)
const selectedRoomIds = ref<string[]>([])
const selectedRooms = ref<Property[]>([])

// 分页
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
})

// 搜索表单
const searchForm = reactive({
  roomName: '',
  projectId: '',
  parcelId: '',
  buildingId: '',
  floorId: '',
  status: RoomStatus.ACTIVE, // 默认筛选生效中的房源
})

// 房源表格列
const roomColumns = [
  { title: '房源名称', dataIndex: 'roomName', width: 140, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '物业类型', dataIndex: 'propertyTypeName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '楼栋', dataIndex: 'buildingName', width: 100, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '楼层', dataIndex: 'floorName', width: 80, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '建筑面积(㎡)', dataIndex: 'buildArea', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '套内面积(㎡)', dataIndex: 'innerArea', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '计租面积(㎡)', dataIndex: 'rentArea', width: 120, ellipsis: true, tooltip: true, align: 'center'  }
]

// 表格行选择配置
const rowSelection = computed(() => {
  return {
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
    selectedRowKeys: selectedRoomIds.value,
  }
})

// 处理选择变化
const handleSelectionChange = (rowKeys: string[], selectedRows: Property[]) => {
  console.log('选中的行:', rowKeys, selectedRows)
  selectedRoomIds.value = rowKeys
  selectedRooms.value = selectedRows
}

// 搜索房源
const searchRooms = async () => {
  try {
    roomLoading.value = true
    const params: RoomQueryDTO = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      roomName: searchForm.roomName,
      projectId: searchForm.projectId,
      parcelId: searchForm.parcelId,
      buildingId: searchForm.buildingId,
      floorId: searchForm.floorId,
      status: RoomStatus.ACTIVE,
      type: RoomType.NORMAL
    }
    
    const response = await getRoomList(params)
    if (response.code === 200 && response.rows) {
      // 过滤已选房源
      const existingRoomIds = originalProperties.value
        .filter(item => item.id)
        .map(item => item.id) as string[]
      
      roomList.value = response.rows.map((room: any) => {
        // 禁用已选择的房源
        return {
          ...room,
          disabled: existingRoomIds.includes(room.id)
        }
      })
      pagination.total = response.total
    } else {
      roomList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取房源列表失败:', error)
    roomList.value = []
    pagination.total = 0
  } finally {
    roomLoading.value = false
  }
}

// 页码变化
const onPageChange = (page: number) => {
  pagination.current = page
  searchRooms()
}

// 确认选择房源
const handleConfirmSelectRooms = () => {
  if (!selectedRoomIds.value || selectedRoomIds.value.length === 0) {
    Message.warning('请至少选择一个房源')
    return
  }
  
  // 获取选中的房源完整数据
  const selectedRoomData = roomList.value.filter(room => selectedRoomIds.value.includes(room.id as string))
  
  if (selectedRoomData.length > 0) {
    // 处理选择的房源
    handleSelectRoomsConfirm(selectedRoomData)
    
    // 重置选择状态
    selectedRoomIds.value = []
    selectedRooms.value = []
    showSelectRoomsModal.value = false
  } else {
    Message.warning('未找到选中的房源数据')
  }
}

// 加载房源详情
const loadRoomDetail = async (id: string) => {
  try {
    const response = await getRoomDetail({ id })
    if (response && response.data) {
      return response.data
    }
    return null
  } catch (error) {
    console.error('获取房源详情失败:', error)
    return null
  }
}

// 加载最大合同结束日期
const loadMaxContractEndDate = async (roomIds: string[]) => {
  try {
    const response = await getMaxContractEndDate(roomIds)
    if (response && response.data) {
      maxContractEndDate.value = response.data
    } else {
      maxContractEndDate.value = null
    }
  } catch (error) {
    console.error('获取最大合同结束日期失败:', error)
    maxContractEndDate.value = null
  }
}

// 查看原房源详情
const handleViewDetail = (property: Property) => {
  if (property.id) {
    showAddPropertyDrawer.value = true
    nextTick(() => {
      addPropertyDrawerRef.value?.show('view', property.id, {
        projectId: property.projectId,
        projectName: currentSelection.value.projectName,
        blockId: property.parcelId,
        blockName: property.parcelName,
        buildingId: property.buildingId,
        buildingName: property.buildingName
      })
    })
  } else {
    Message.error('房源ID不存在')
  }
}

// 删除待合并房源
const handleRemoveProperty = (index: number) => {
  originalProperties.value.splice(index, 1)
  // 重新计算序号
  originalProperties.value.forEach((item, idx) => {
    item.index = idx + 1
  })
  
  // 如果删除后还有房源，重新计算合并后房源信息
  if (originalProperties.value.length > 0) {
    // 更新合并后房源信息
    updateMergedProperty()
  } else {
    // 如果没有房源了，重置合并后房源信息
    mergedProperty.value = {
      roomName: '',
      propertyType: '',
      buildArea: 0,
      innerArea: 0,
      parcelName: '',
      buildingName: '',
      floorName: '',
      index: 1
    }
    maxContractEndDate.value = null
  }
}

// 查看更多信息
const handleMoreInfo = (index: number) => {
  // 使用更通用的模式名称'adjust-edit'或'adjust-view'
  const mode = isReadOnly.value ? 'adjust-view' : 'adjust-edit'
  
  // 打开房源信息弹框，传递房源数据
  showAddPropertyDrawer.value = true
  nextTick(() => {
    const selection = {
      projectId: mergedProperty.value.projectId,
      projectName: currentSelection.value.projectName,
      blockId: mergedProperty.value.parcelId,
      blockName: mergedProperty.value.parcelName,
      buildingId: mergedProperty.value.buildingId,
      buildingName: mergedProperty.value.buildingName
    }
    
    console.log('传递给房源编辑的数据:', {
      mode: mode,
      selection: selection,
      property: mergedProperty.value,
      index: index
    })
    
    addPropertyDrawerRef.value?.show(mode, null, selection, mergedProperty.value, index)
  })
}

// 接收房源弹框返回的数据更新合并房源
const handleAddPropertyConfirm = (data: any) => {
  console.log('接收到房源弹框返回的数据:', data)
  
  // 如果是调整编辑模式返回的数据
  if (data && data.type === 'adjust-edit' && data.data) {
    console.log('更新合并房源数据')
    
    // 完全使用房源弹框返回的数据
    mergedProperty.value = data.data
    
    // 关闭房源弹框
    showAddPropertyDrawer.value = false
  }
}

// 禁用日期
const disabledDate = (date: Date) => {
  // 如果有最大合同结束日期，则只能选择该日期之后的日期
  if (maxContractEndDate.value) {
    const contractDate = new Date(maxContractEndDate.value)
    return date.getTime() <= contractDate.getTime()
  }
  // 否则只禁用今天之前的日期
  return date.getTime() <= Date.now()
}

// 加载合并详情
const loadMergeDetail = async (mergeId: string) => {
  try {
    // 保存当前编辑的合并记录ID
    currentMergeId.value = mergeId
    
    const response = await getMergeDetail({ id: mergeId })
    if (response && response.data) {
      const detailData = response.data
      
      // 更新原房源信息
      if (detailData.sourceRooms && Array.isArray(detailData.sourceRooms)) {
        originalProperties.value = detailData.sourceRooms.map((room, index) => ({
          ...room,
          index: index + 1
        }))
      }
      
      // 更新合并后房源信息
      if (detailData.targetRoom) {
        mergedProperty.value = { 
          ...detailData.targetRoom,
          index: 1
        }
      }
      
      // 更新表单数据
      formData.value = {
        effectiveType: detailData.effectType === 1 ? 'immediate' : 'scheduled',
        effectiveDate: detailData.effectDate || null,
        adjustReason: detailData.adjustmentReason || '',
        attachments: detailData.attachments || ''
      }
      
      // 更新最大合同结束日期
      const roomIds = originalProperties.value.map(item => item.id as string).filter(id => !!id)
      if (roomIds.length > 0) {
        await loadMaxContractEndDate(roomIds)
      }
      
      return true
    }
    return false
  } catch (error) {
    console.error('获取合并详情失败:', error)
    Message.error('获取合并详情失败')
    return false
  }
}

// 取消
const handleCancel = () => {
  setTimeout(() => {
    emit('cancel')
  }, 300);
  visible.value = false
  // Reset form data
  originalProperties.value = []
  mergedProperty.value = {
    roomName: '',
    propertyType: '',
    buildArea: 0,
    innerArea: 0,
    parcelName: '',
    buildingName: '',
    floorName: '',
    index: 1
  }
  formData.value = {
    effectiveType: 'immediate',
    effectiveDate: null,
    adjustReason: '',
    attachments: ''
  }
  currentMergeId.value = null
  maxContractEndDate.value = null
}

// 确认
const handleConfirm = async (submitType: number = 2) => {
  if (originalProperties.value.length < 2) {
    Message.error('至少需要两个房源才能合并')
    return
  }
  
  try {
    // 构造合并请求参数
    const params = {
      id: currentMergeId.value || '',
      sourceRoomIds: originalProperties.value.map(item => item.id as string).filter(id => !!id),
      targetRoom: mergedProperty.value,
      effectType: formData.value.effectiveType === 'immediate' ? 1 : 2,
      effectDate: formData.value.effectiveType === 'immediate' ? '' : (formData.value.effectiveDate || ''),
      adjustmentReason: formData.value.adjustReason,
      attachments: formData.value.attachments || null,
      submitType: submitType // 提交类型 1-暂存 2-提交
    }
    
    // 调用合并接口
    const response = await mergeRoom(params)
    Message.success(submitType === 1 ? '暂存成功' : '合并成功')
    if (response && response.code === 200) {
      setTimeout(() => {
        emit('confirm')
      }, 300);
      visible.value = false
    }
  } catch (error) {
    console.error('合并失败:', error)
  }
}

// Show method
function show(propertyData: RoomVo[] | { mergeId?: string }, readOnly = false, selectionData?: any) {
  isReadOnly.value = readOnly
  
  // 重置状态
  currentMergeId.value = null
  maxContractEndDate.value = null
  originalProperties.value = []
  
  // 加载字典数据
  getPropertyTypeDicts().then(() => {
    if(selectionData){
      currentSelection.value = selectionData
    }
    
    if (Array.isArray(propertyData)) {
      // 直接使用传入的房源对象数组
      if (propertyData.length > 0) {
        // 更新原房源列表
        originalProperties.value = propertyData.map((room, index) => ({
          ...room,
          index: index + 1
        }))
        
        // 获取第一个房源的ID
        const firstPropertyId = propertyData[0].id
        
        if (firstPropertyId) {
          // 通过详情接口获取完整字段
          loadRoomDetail(firstPropertyId).then(detailData => {
            if (detailData) {
              // 计算总面积 - 使用toFixed(2)保留两位小数
              const totalBuildArea = parseFloat(propertyData.reduce((sum, prop) => sum + (prop.buildArea || 0), 0).toFixed(2))
              const totalInnerArea = parseFloat(propertyData.reduce((sum, prop) => sum + (prop.innerArea || 0), 0).toFixed(2))
              const totalRentArea = parseFloat(propertyData.reduce((sum, prop) => sum + (prop.rentArea || 0), 0).toFixed(2))
              
              // 使用详情接口返回的完整字段作为基础，更新面积为所有房源之和
              mergedProperty.value = { 
                ...detailData,
                id: undefined, // 清除ID
                roomName: `${detailData.roomName}-合并`,
                buildArea: totalBuildArea,
                innerArea: totalInnerArea,
                rentArea: totalRentArea,
                index: 1
              }
              
              // 更新最大合同结束日期
              const roomIds = propertyData.map(item => item.id as string).filter(id => !!id)
              if (roomIds.length > 0) {
                loadMaxContractEndDate(roomIds).then(() => {
                  // 显示抽屉
                  visible.value = true
                })
              } else {
                visible.value = true
              }
            } else {
              Message.error('无法获取第一个房源的详细信息')
            }
          })
        } else {
          Message.error('第一个房源的ID不存在')
        }
      } else {
        Message.error('请选择要合并的房源')
      }
    } else if (typeof propertyData === 'object' && propertyData.mergeId) {
      // 使用mergeId加载合并详情
      loadMergeDetail(propertyData.mergeId).then(success => {
        if (success) {
          visible.value = true
        }
      })
    } else {
      Message.error('参数错误：请提供房源对象数组或合并记录ID')
    }
  })
}

// 暴露方法
defineExpose({
  show
})

// 添加待合并房源
const handleAddProperty = () => {
  showSelectRoomsModal.value = true
  
  // 更新搜索条件
  searchForm.projectId = currentSelection.value.projectId
  searchForm.parcelId = currentSelection.value.blockId
  searchForm.buildingId = currentSelection.value.buildingId
  
  // 重置分页和选择
  pagination.current = 1
  selectedRoomIds.value = []
  selectedRooms.value = []
  
  // 加载房源数据
  nextTick(() => {
    searchRooms()
  })
}

// 处理选择房源确认
const handleSelectRoomsConfirm = (selectedRooms: Property[]) => {
  if (selectedRooms && selectedRooms.length > 0) {
    console.log('添加选中的房源到待合并列表:', selectedRooms)
    
    // 添加到待合并房源列表
    const newProperties = selectedRooms.map((room, index) => ({
      ...room,
      index: originalProperties.value.length + index + 1
    }))
    originalProperties.value = [...originalProperties.value, ...newProperties]
    
    // 更新合并后房源信息
    updateMergedProperty()
  }
}

// 更新合并后房源信息
const updateMergedProperty = () => {
  if (originalProperties.value.length > 0) {
    // 获取第一个房源作为合并后房源的基础信息
    const firstProperty = originalProperties.value[0]
    
    // 计算总面积 - 使用toFixed(2)保留两位小数
    const totalBuildArea = parseFloat(originalProperties.value.reduce((sum, prop) => sum + (prop.buildArea || 0), 0).toFixed(2))
    const totalInnerArea = parseFloat(originalProperties.value.reduce((sum, prop) => sum + (prop.innerArea || 0), 0).toFixed(2))
    const totalRentArea = parseFloat(originalProperties.value.reduce((sum, prop) => sum + (prop.rentArea || 0), 0).toFixed(2))
    
    console.log('计算合并后面积:', { totalBuildArea, totalInnerArea, totalRentArea })
    
    // 使用第一个房源的详情作为基础，更新面积为所有房源之和
    if (firstProperty.id) {
      loadRoomDetail(firstProperty.id).then(detailData => {
        if (detailData) {
          mergedProperty.value = { 
            ...detailData,
            id: undefined, // 清除ID
            roomName: `${detailData.roomName}-合并`,
            buildArea: totalBuildArea,
            innerArea: totalInnerArea,
            rentArea: totalRentArea,
            index: 1
          }
        } else {
          mergedProperty.value = { 
            ...firstProperty,
            id: undefined, // 清除ID
            roomName: `${firstProperty.roomName}-合并`,
            buildArea: totalBuildArea,
            innerArea: totalInnerArea,
            rentArea: totalRentArea,
            index: 1
          }
        }
      }).catch(() => {
        mergedProperty.value = { 
          ...firstProperty,
          id: undefined, // 清除ID
          roomName: `${firstProperty.roomName}-合并`,
          buildArea: totalBuildArea,
          innerArea: totalInnerArea,
          rentArea: totalRentArea,
          index: 1
        }
      })
    } else {
      mergedProperty.value = { 
        ...firstProperty,
        id: undefined, // 清除ID
        roomName: `${firstProperty.roomName}-合并`,
        buildArea: totalBuildArea,
        innerArea: totalInnerArea,
        rentArea: totalRentArea,
        index: 1
      }
    }
    
    // 更新最大合同结束日期
    const roomIds = originalProperties.value.map(item => item.id as string).filter(id => !!id)
    if (roomIds.length > 0) {
      loadMaxContractEndDate(roomIds)
    }
  }
}
</script>

<style scoped lang="less">
.merge-property {
  .section {
    margin-bottom: 24px;

    .section-title {
      margin: 16px 0;
      &.first-child{
        margin-top: 8px;
      }
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  :deep(.arco-table-th) {
    background-color: var(--color-fill-2) !important;
  }

  :deep(.arco-table-td) {
    background-color: var(--color-bg-2);
  }
  :deep(.arco-form-item-label){
    min-width: 80px;
  }
  .drawer-footer {
    text-align: right;
    padding: 16px 0;
  }

  .contract-end-date{
    margin-top: 30px;
    line-height: 32px;
    color: var(--color-text-1);
  }
}

.select-rooms-modal {
  .search-area {
    margin-bottom: 16px;
    display: flex;
  }
}
</style> 