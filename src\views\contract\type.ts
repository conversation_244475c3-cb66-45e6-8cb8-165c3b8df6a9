export type EditType = 'edit' | 'renew' | 'change' | 'create' | 'detail'

// 合同客户对象接口
export interface ContractCustomer {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 客户id */
    customerId?: string;
    /** 客户类型:1-个人1,2-企业 */
    customerType?: number;
    /** 客户名/公司名 */
    customerName?: string;
    /** 地址 */
    address?: string;
    /** 手机号(个人/法人) */
    phone?: string;
    /** 证件类型(个人/法人) */
    idType?: string;
    /** 证件号码(个人/法人) */
    idNumber?: string;
    /** 是否是员工:0-否,1-是 */
    isEmployee?: boolean;
    /** 统一社会信用代码 */
    creditCode?: string;
    /** 联系人 */
    contactName?: string;
    /** 联系人手机号 */
    contactPhone?: string;
    /** 联系人身份证号 */
    contactIdNumber?: string;
    /** 法人名称 */
    legalName?: string;
    /** 付款银行账号 */
    paymentAccount?: string;
    /** 担保人姓名 */
    guarantorName?: string;
    /** 担保人手机号 */
    guarantorPhone?: string;
    /** 担保人证件类型 */
    guarantorIdType?: string;
    /** 担保人证件号码 */
    guarantorIdNumber?: string;
    /** 担保人通讯地址 */
    guarantorAddress?: string;
    /** 担保人身份证正面地址 */
    guarantorIdFront?: string;
    /** 担保人身份证反面地址 */
    guarantorIdBack?: string;
    /** 开票名称 */
    invoiceTitle?: string;
    /** 开票税号 */
    invoiceTaxNumber?: string;
    /** 开票单位地址 */
    invoiceAddress?: string;
    /** 开票电话号码 */
    invoicePhone?: string;
    /** 开票开户银行 */
    invoiceBankName?: string;
    /** 开票银行账户 */
    invoiceAccountNumber?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

// 合同订单对象接口
export interface ContractBooking {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 定单id */
    bookingId?: string;
    /** 预定房源 */
    bookedRoom?: string;
    /** 预定人姓名 */
    bookerName?: string;
    /** 定单已收金额 */
    bookingReceivedAmount?: number;
    /** 定单收款(生效)日期 */
    bookingPaymentDate?: string;
    /** 转保证金金额 */
    transferBondAmount?: number;
    /** 转租金金额 */
    transferRentAmount?: number;
    /** 0-否,1-是 */
    isDel?: boolean;
}

// 合同费用对象接口
export interface ContractFee {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 费用类型,1-免租期 */
    feeType?: number;
    /** 免租类型,0-装修免租,1-经营免租,2-合同免租 */
    freeType?: number;
    /** 免租月数 */
    freeRentMonth?: number;
    /** 免租天数 */
    freeRentDay?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 备注 */
    remark?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

// 合同账单对象接口
export interface ContractCost {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 账单类型,1-保证金,2-租金,3-其他费用 */
    costType?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 账单期数 */
    period?: number;
    /** 商户id */
    customerId?: string;
    /** 商户名 */
    customerName?: string;
    /** 商铺id */
    roomId?: string;
    /** 商铺名 */
    roomName?: string;
    /** 商铺面积 */
    area?: number;
    /** 收款用途id */
    subjectId?: string;
    /** 收款用途 */
    subjectName?: string;
    /** 应收日期 */
    receivableDate?: string;
    /** 单价 */
    unitPrice?: number;
    /** 单价单位 */
    priceUnit?: number;
    /** 账单总额（元） */
    totalAmount?: number;
    /** 优惠金额（元） */
    discountAmount?: number;
    /** 账单实际应收金额（元） */
    actualReceivable?: number;
    /** 账单已收金额（元） */
    receivedAmount?: number;
    /** 是否营收提成,0-否,1-是 */
    isRevenue?: boolean;
    /** 提成类型: 1-固定提成, 2-阶梯提成 */
    percentageType?: number;
    /** 固定提成比例 */
    fixedPercentage?: number;
    /** 阶梯提成比例json信息, eg: [{"amount": 0, "percentage": 3}] */
    stepPercentage?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

// 合同房源对象接口
export interface ContractRoom {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 房源id */
    roomId: string;
    /** 房间名称 */
    roomName: string;
    /** 面积（㎡） */
    area: number;
    /** 标准租金（单价） */
    standardUnitPrice: number;
    /** 底价 */
    bottomPrice: number;
    /** 单价单位, 使用统一字典, 待定 */
    priceUnit: number;
    /** 折扣 */
    discount: number;
    /** 签约单价 */
    signedUnitPrice: number;
    /** 签约月总价（元/月） */
    signedMonthlyPrice: number;
    /** 实际开始日期 */
    startDate: string;
    /** 实际结束日期 */
    endDate: string;
    /** 0-否,1-是 */
    isDel: boolean;
}

// 定义合同数据接口
export interface Contract {
    /** 主键ID */
    id?: string;
    /** 项目id */
    projectId?: string;
    /** 合同号 */
    contractNo?: string;
    /** 统一id */
    unionId?: string;
    /** 版本号v00x */
    version?: string;
    /** 是否当前生效版本 0-否,1-是 */
    isCurrent?: boolean;
    /** 一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废 */
    status?: number;
    /** 二级状态 */
    statusTwo?: number;
    /** 审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝 */
    approveStatus?: number;
    /** 最新操作类型,0-新签,1-变更条款,2-退租 */
    operateType?: number;
    /** 合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房 */
    contractType?: number;
    /** 我方签约主体信息 */
    ourSigningParty?: string;
    /** 签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章） */
    signWay?: number;
    /** 签约类型,0-新签,1-续签 */
    signType?: number;
    /** 续签源合同ID */
    originId?: string;
    /** 变更源合同ID */
    changeFromId?: string;
    /** 用地性质 */
    landUsage?: string;
    /** 合同签约人id */
    signerId?: string;
    /** 签约人姓名 */
    signerName?: string;
    /** 责任人id */
    ownerId?: string;
    /** 责任人姓名 */
    ownerName?: string;
    /** 合同模式,0-标准合同,1-非标合同 */
    contractMode?: number;
    /** 纸质合同号 */
    paperContractNo?: string;
    /** 签订日期 */
    signDate?: string;
    /** 交房日期 */
    handoverDate?: string;
    /** 合同用途,字典: */
    contractPurpose?: number;
    /** 成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商 */
    dealChannel?: number;
    /** 协助人id */
    assistantId?: string;
    /** 协助人姓名 */
    assistantName?: string;
    /** 租赁期限-年 */
    rentYear?: number;
    /** 租赁期限-月 */
    rentMonth?: number;
    /** 租赁期限-日 */
    rentDay?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 实际生效日期 */
    effectDate?: string;
    /** 保证金应收日期(天数/具体日期) */
    bondReceivableDate?: string;
    /** 保证金应收日期类型,0-合同签订后, 1-指定日期 */
    bondReceivableType?: number;
    /** 保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金 */
    bondPriceType: number;
    /** 保证金金额 */
    bondPrice?: number;
    /** 收费方式,0-固定租金,1-递增租金,2-营收抽成 */
    chargeWay?: number;
    /** 租金应收日期(天数/具体日期) */
    rentReceivableDate?: string;
    /** 租金应收日期类型,1-租期开始前,2-租期开始后 */
    rentReceivableType?: number;
    /** 租金账单周期: 1-租赁月, 2-自然月 */
    rentTicketPeriod?: number;
    /** 租金支付周期 */
    rentPayPeriod?: number;
    /** 递增间隔(年) */
    increaseGap?: number;
    /** 递增率 */
    increaseRate?: number;
    /** 租金递增规则(非标) */
    increaseRule?: string;
    /** 预估营收额 */
    estimateRevenue?: number;
    /** 提成类型: 1-固定提成, 2-阶梯提成 */
    percentageType?: number;
    /** 固定提成比例 */
    fixedPercentage?: number;
    /** 阶梯提成比例json信息, eg: [{"amount": 0, "percentage": 3}] */
    stepPercentage?: string;
    /** 抽点类型: 1-按月营业额, 2-按年营业额 */
    revenueType?: number;
    /** 是否包含物业费 */
    isIncludePm?: boolean;
    /** 月物业费单价 */
    pmUnitPrice?: number;
    /** 月物业费总价 */
    pmMonthlyPrice?: number;
    /** 合同总价 */
    totalPrice?: number;
    /** 业态id */
    bizTypeId?: string;
    /** 业态 */
    bizTypeName?: string;
    /** 承租方品牌 */
    lesseeBrand?: string;
    /** 经营品类 */
    businessCategory?: string;
    /** 开业日期 */
    openDate?: string;
    /** 生产火灾危险性类别,0-丙类,1-丁类,2-戊类 */
    fireRiskCategory?: number;
    /** 喷淋系统,0-安装,1-未安装 */
    sprinklerSystem?: number;
    /** 厂房从事 */
    factoryEngaged?: string;
    /** 交接日期 */
    deliverDate?: string;
    /** 车位类型,0-人防,1-非人防 */
    parkingSpaceType?: number;
    /** 是否包含车位管理费 */
    hasParkingFee?: boolean;
    /** 车位管理费金额 */
    parkingFeeAmount?: number;
    /** 补充条款 */
    otherInfo?: string;
    /** 合同附件[{"fileName": "", "fileUrl":"", "fileType":""}] */
    contractAttachments?: string;
    /** 附件-平面图 */
    attachmentsPlan?: string;
    /** 是否上传签署文件 0-否,1-是 */
    isUploadSignature?: boolean;
    /** 是否确认签署 0-否,1-是 */
    isSignatureConfirm?: boolean;
    /** 是否确认纸质文件 0-否,1-是 */
    isPaperConfirm?: boolean;
    /** 合同是否完全结束 0-否,1-是 */
    isFinish?: boolean;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 0-暂存,1-提交 */
    isSubmit?: boolean;
    /** 合同客户对象 */
    customer: ContractCustomer;
    /** 合同定单对象 */
    bookings?: ContractBooking[];
    /** 合同费用对象 */
    fees?: ContractFee[];
    /** 合同账单对象 */
    costs?: ContractCost[];
    /** 合同房源对象 */
    rooms: ContractRoom[];
}