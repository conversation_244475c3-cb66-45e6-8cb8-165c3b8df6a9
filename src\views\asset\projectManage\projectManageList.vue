<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form :model="queryParams" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
                        label-align="right" auto-label-width>
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="projectName" label="项目名称">
                                    <a-input v-model="queryParams.projectName" placeholder="请输入项目名称" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="assetType" label="项目资产分类">
                                    <a-select v-model="queryParams.assetType" placeholder="请选择资产分类" allow-clear>
                                        <a-option v-for="dict in asset_category" :key="dict.value" :value="dict.value">
                                            {{ dict.label }}
                                        </a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <!-- <a-divider style="height: 84px" direction="vertical" /> -->
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space :size="18">
                        <a-button v-permission="['asset:project:list']" type="primary" @click="handleQuery">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="resetQuery">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin: 0 0 16px 0;" />

            <!-- 操作按钮 -->
            <a-row style="margin-bottom: 16px;text-align: right;">
                <a-col :span="24">
                    <a-space>
                        <a-button v-permission="['asset:project:add']" type="primary" @click="handleAdd">
                            新增内部项目
                        </a-button>
                        <a-button v-permission="['asset:project:add']" type="primary" @click="handleAddExternal">
                            新增外部项目
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>

            <!-- 数据表格 -->
            <a-table :columns="columns" :data="tableData" :loading="loading" :pagination="pagination"
                :bordered="{ cell: true }" @page-change="onPageChange" @page-size-change="onPageSizeChange"
                :scroll="{ x: 1 }">
                <template #index="{ rowIndex }">
                    {{
                        rowIndex +
                        1 +
                        (pagination.current - 1) * pagination.pageSize
                    }}
                </template>
                <template #mdmName="{ record }">
                    <PermissionLink :permissions="['asset:project:detail']" @click="handleViewDetail(record)">{{ record.mdmName }}</PermissionLink>
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <a-button v-permission="['asset:project:edit']" type="text" @click="handleEdit(record)" size="mini">编辑</a-button>
                        <a-button v-permission="['asset:project:delete']" type="text" status="danger" @click="() => handleDelete(record)"
                            size="mini">删除</a-button>
                    </a-space>
                </template>
                <template #assetType="{ record }">
                    {{ getDictLabel(asset_category, record.assetType) }}
                </template>
            </a-table>
        </a-card>

        <!-- 新增内部项目弹窗 -->
        <a-modal v-model:visible="addDialogVisible" title="选择主数据项目" @ok="handleAddConfirm" @cancel="handleAddCancel"
            :mask-closable="true" :width="680" class="common-modal-no-padding">
            <div class="project-select-container">
                <!-- 搜索框 -->
                <div class="search-box">
                    <span class="label">项目名称</span>
                    <a-input v-model="mdmQueryParams.projectName" placeholder="请输入项目名称" allow-clear
                        :style="{ width: '280px' }" @press-enter="fetchMdmProjectList">
                        <template #suffix>
                            <icon-search @click="fetchMdmProjectList" />
                        </template>
                    </a-input>
                </div>
                <div class="project-list-container">
                    <!-- 项目选择表格 -->
                    <a-table :columns="projectColumns" :data="projectList" :loading="mdmLoading"
                        :pagination="mdmPagination" row-key="id" :row-selection="rowSelection"
                        v-model:selectedKeys="selectedKeys" @page-change="onMdmPageChange"
                        @page-size-change="onMdmPageSizeChange" @selection-change="handleMdmSelectionChange">
                        <template #empty>
                            <div style="text-align: center; padding: 16px;">
                                暂无数据
                            </div>
                        </template>
                        <template #projectTypeName="{ record }">
                            {{ record.projectTypeName || record.mdmTypeName || '-' }}
                        </template>
                    </a-table>
                </div>
            </div>

            <template #footer>
                <div style="display: flex; justify-content: flex-end; width: 100%;">
                    <a-space>
                        <a-button type="primary" @click="handleNext">下一步</a-button>
                        <a-button @click="handleAddCancel">取消</a-button>
                    </a-space>
                </div>
            </template>
        </a-modal>

        <!-- 新增项目表单弹窗 -->
        <a-modal v-model:visible="addFormVisible" title="新增内部项目" @ok="handleAddConfirm" @cancel="handleAddFormCancel"
            :mask-closable="true" :width="600">
            <a-form ref="addFormRef" :model="addForm" :rules="addFormRules" label-align="right"
                :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }">
                <a-form-item label="项目名称" field="projectName">
                    <a-input v-model="addForm.projectName" placeholder="请输入项目名称" allow-clear />
                </a-form-item>
                <a-form-item label="项目编码" field="projectCode">
                    <a-input v-model="addForm.projectCode" placeholder="请输入项目编码" allow-clear />
                </a-form-item>
                <a-form-item label="项目简称" field="projectShortName">
                    <a-input v-model="addForm.projectShortName" placeholder="请输入项目简称" allow-clear />
                </a-form-item>
                <a-form-item label="项目资产分类" field="assetType">
                    <a-select v-model="addForm.assetType" placeholder="请选择项目资产分类" allow-clear>
                        <a-option value="众创城域">众创城域</a-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="项目建址" field="projectAddress">
                    <a-input v-model="addForm.projectAddress" placeholder="请输入项目建址" allow-clear />
                </a-form-item>
                <a-form-item label="产权单位" field="propertyUnit">
                    <a-input v-model="addForm.propertyUnit" placeholder="请输入产权单位" allow-clear />
                </a-form-item>
            </a-form>
        </a-modal>
        <!-- 内部项目管理弹窗 -->
        <internal-project-dialog ref="internalProjectRef" @refresh="handleQuery" />
        <!-- 外部项目管理弹窗 -->
        <external-project-dialog ref="externalProjectRef" @refresh="handleQuery" />
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import InternalProjectDialog from './components/internalProject.vue';
import ExternalProjectDialog from './components/externalProject.vue';
import { useDict } from '@/utils/dict'
import { getMdmProjectList, getMdmProjectDetail } from '@/api/asset/masterData';
import { getProjectList, deleteProject } from '@/api/asset/projectManage';

// 查询参数
const queryParams = reactive({
    projectName: '',
    assetType: ''
});

// 选中行的key
const selectedRowKeys = ref<string[]>([]);

// 表格选择变化事件处理
const handleSelectionChange = (rowKeys: string[]) => {
    selectedRowKeys.value = rowKeys;
};

// 表格列定义
const columns = [
    { title: '序号', dataIndex: 'index', slotName: 'index', width: 70, align: 'center' },
    { title: '项目名称', dataIndex: 'mdmName', width: 200, ellipsis: true, tooltip: true, slotName: 'mdmName', align: 'center' },
    { title: '项目编码', dataIndex: 'code', width: 100, ellipsis: true, tooltip: true, align: 'center' },
    { title: '项目简称', dataIndex: 'name', width: 150, ellipsis: true, tooltip: true, align: 'center' },
    { title: '项目资产分类', dataIndex: 'assetType', width: 120, ellipsis: true, tooltip: true, slotName: 'assetType', align: 'center' },
    { title: '项目建址', dataIndex: 'projectAddress', width: 190, ellipsis: true, tooltip: true, align: 'center' },
    { title: '产权单位', dataIndex: 'propertyUnit', width: 160, ellipsis: true, tooltip: true, align: 'center' },
    {
        title: '操作',
        dataIndex: 'operations',
        slotName: 'operations',
        fixed: 'right',
        width: 180,
        align: 'center'
    }
];

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);

// 分页配置
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
    showTotal: true,
    showPageSize: true
});

// 查询方法
const handleQuery = async () => {
    loading.value = true;
    try {
        // 构建查询参数，包含分页信息
        const params = {
            ...queryParams,
            pageNum: pagination.current,
            pageSize: pagination.pageSize
        };
        
        // 调用项目列表接口
        const res = await getProjectList(params);
        console.log('项目列表响应:', res);

        // 处理响应数据
        if (res && res.rows) {
            tableData.value = res.rows;
            pagination.total = res.total || 0;
        } else if (res && res.data && res.data.rows) {
            tableData.value = res.data.rows;
            pagination.total = res.data.total || 0;
        } else if (res && Array.isArray(res)) {
            tableData.value = res;
            pagination.total = res.length;
        } else if (res && res.data && Array.isArray(res.data)) {
            tableData.value = res.data;
            pagination.total = res.data.length;
        } else {
            tableData.value = [];
            pagination.total = 0;
        }

        loading.value = false;
    } catch (error) {
        console.error('查询失败', error);
        Message.error('查询失败');
        tableData.value = [];
        pagination.total = 0;
        loading.value = false;
    }
};

const { asset_category } = useDict('asset_category')

// 获取字典标签的辅助函数
const getDictLabel = (dict: any[], value: string | number): string => {
    const item = dict.find(d => d.value === value || d.value === String(value) || Number(d.value) === value);
    return item ? item.label : '';
};

// 重置查询
const resetQuery = () => {
    queryParams.projectName = '';
    queryParams.assetType = '';
    pagination.current = 1; // 重置到第一页
    handleQuery();
};

// 新增外部项目
const handleAddExternal = () => {
    // TODO: 实现新增外部项目逻辑
    externalProjectRef.value?.show();
};

// 查看项目详情
const handleViewDetail = (record: any) => {
    if (!record) {
        Message.warning('请选择要查看的项目');
        return;
    }

    // 根据项目类型判断打开哪个弹窗
    if (record.type === '1' || record.type === 1) {
        // 打开内部项目弹窗
        internalProjectRef.value?.show(record.id, 'view');
    } else if (record.type === '2' || record.type === 2) {
        // 打开外部项目弹窗
        externalProjectRef.value?.show(record.id, 'view');
    } else {
        Message.warning('未知的项目类型');
    }
};

// 编辑项目
const handleEdit = (record: any) => {
    if (!record) {
        Message.warning('请选择要编辑的项目');
        return;
    }

    // 根据assetType判断打开哪个弹窗
    if (record.type === '1' || record.type === 1) {
        // 打开内部项目弹窗
        internalProjectRef.value?.show(record.id, 'edit');
    } else if (record.type === '2' || record.type === 2) {
        // 打开外部项目弹窗
        externalProjectRef.value?.show(record.id, 'edit');
    } else {
        Message.warning('未知的项目类型');
    }
};

// 删除项目
const handleDelete = async (record: any) => {
    if (!record) {
        Message.warning('请选择要删除的项目');
        return;
    }

    // 弹出确认对话框
    Modal.confirm({
        title: '删除确认',
        content: `确定要删除项目 "${record.mdmName}" 吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
            try {
                loading.value = true;
                const res = await deleteProject(record.id);

                // 处理响应
                if (res.code === 200) {
                    Message.success('删除成功');
                    // 刷新列表
                    handleQuery();
                } else {
                    // 处理业务错误，比如项目已被使用不能删除的情况
                    Message.error(res.msg || '删除失败');
                }
            } catch (error) {
                console.error('删除项目失败:', error);
            } finally {
                loading.value = false;
            }
        }
    });
};

// 分页变化
const onPageChange = (current: number) => {
    pagination.current = current;
    handleQuery();
};

// 每页条数变化
const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    handleQuery();
};

// 初始化加载数据
handleQuery();

// 新增表单ref
const addFormRef = ref();

// 新增弹窗显示状态
const addDialogVisible = ref(false);

// 新增表单数据
const addForm = reactive({
    projectName: '',
    projectCode: '',
    projectShortName: '',
    assetType: '',
    projectAddress: '',
    propertyUnit: ''
});

// 新增表单校验规则
const addFormRules = {
    projectName: [
        { required: true, message: '请输入项目名称' }
    ],
    projectCode: [
        { required: true, message: '请输入项目编码' }
    ],
    assetType: [
        { required: true, message: '请选择项目资产分类' }
    ]
};

// 打开新增弹窗
const handleAdd = () => {
    addDialogVisible.value = true;
    // 打开弹窗时加载主数据项目列表
    fetchMdmProjectList();
};

// 新增确认
const handleAddConfirm = async () => {
    const { errors } = await addFormRef.value.validate();
    if (errors) return;

    try {
        // TODO: 调用新增接口
        Message.success('新增成功');
        addDialogVisible.value = false;
        resetAddForm();
        handleQuery(); // 刷新列表
    } catch (error) {
        Message.error('新增失败');
    }
};

// 重置新增表单
const resetAddForm = () => {
    addForm.projectName = '';
    addForm.projectCode = '';
    addForm.projectShortName = '';
    addForm.assetType = '';
    addForm.projectAddress = '';
    addForm.propertyUnit = '';
    addFormRef.value?.resetFields();
};

// 主数据项目查询参数
const mdmQueryParams = reactive({
    projectName: '',
    pageNum: 1,
    pageSize: 10
});

// 主数据项目列表加载状态
const mdmLoading = ref(false);

// 主数据项目分页配置
const mdmPagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
    showTotal: true,
    showPageSize: true
});

// 主数据项目分页变化
const onMdmPageChange = (current: number) => {
    mdmPagination.current = current;
    mdmQueryParams.pageNum = current;
    fetchMdmProjectList();
};

// 主数据项目每页条数变化
const onMdmPageSizeChange = (pageSize: number) => {
    mdmPagination.pageSize = pageSize;
    mdmQueryParams.pageSize = pageSize;
    mdmQueryParams.pageNum = 1;
    mdmPagination.current = 1;
    fetchMdmProjectList();
};

// 重置主数据项目查询
const resetMdmQuery = () => {
    mdmQueryParams.projectName = '';
    mdmQueryParams.pageNum = 1;
    mdmQueryParams.pageSize = 10;
    mdmPagination.current = 1;
    mdmPagination.pageSize = 10;
    fetchMdmProjectList();
};

// 获取主数据项目列表
const fetchMdmProjectList = async () => {
    mdmLoading.value = true;
    try {
        const res = await getMdmProjectList(mdmQueryParams);
        console.log('主数据项目列表响应:', res);

        // 根据实际API响应结构处理数据
        if (res && res.rows) {
            // 直接返回rows和total的情况
            projectList.value = res.rows || [];
            mdmPagination.total = res.total || 0;
        } else if (res && res.data && res.data.rows) {
            // 嵌套在data中的情况
            projectList.value = res.data.rows || [];
            mdmPagination.total = res.data.total || 0;
        } else if (res && Array.isArray(res)) {
            // 直接返回数组的情况
            projectList.value = res;
            mdmPagination.total = res.length;
        } else if (res && res.data && Array.isArray(res.data)) {
            // data字段是数组的情况
            projectList.value = res.data;
            mdmPagination.total = res.data.length;
        } else {
            projectList.value = [];
            mdmPagination.total = 0;
        }

        console.log('处理后的项目列表数据:', projectList.value);
        mdmLoading.value = false;
    } catch (error) {
        console.error('获取主数据项目列表失败', error);
        Message.error('获取主数据项目列表失败');
        projectList.value = [];
        mdmPagination.total = 0;
        mdmLoading.value = false;
    }
};

// 项目选择表格列定义
const projectColumns = [
    {
        title: '项目名称',
        dataIndex: 'projectName',
        // width: 200,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '项目类型',
        dataIndex: 'projectTypeName',
        slotName: 'projectTypeName',
        width: 280,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    }
];

// 项目列表数据
const projectList = ref<any[]>([]);

const selectedKeys = ref<string[]>([]);

// 表格选择配置
const rowSelection = {
    type: 'radio',
    selectedRowKeys: selectedKeys,
    onChange: (selectedRowKeys: (string | number)[], selectedRows: any[]) => {
        console.log('选中的行数据:', selectedRows, '选中的键:', selectedRowKeys);
        selectedKeys.value = selectedRowKeys as string[];
        if (selectedRows && selectedRows.length > 0) {
            selectedProject.value = selectedRows[0];
            console.log('设置selectedProject成功:', selectedProject.value);
        }
    }
};

// 选中的项目
const selectedProject = ref<any>(null);

// 新增表单弹窗显示状态
const addFormVisible = ref(false);

// 内部项目管理弹窗ref
const internalProjectRef = ref();

// 外部项目管理弹窗ref
const externalProjectRef = ref();

// 处理主数据项目选择变化
const handleMdmSelectionChange = (rowKeys: string[]) => {
    console.log('主数据项目选择变化:', rowKeys);
    selectedKeys.value = rowKeys;

    // 如果选中了项目，根据选中的项目ID找到对应的项目数据
    if (rowKeys && rowKeys.length > 0) {
        const selected = projectList.value.find(item => item.id === rowKeys[0]);
        if (selected) {
            selectedProject.value = selected;
            console.log('已选择项目:', selectedProject.value);
        }
    } else {
        selectedProject.value = null;
    }
};

// 下一步按钮处理
const handleNext = async () => {
    console.log('点击下一步，当前选中项目:', selectedProject.value, '当前选中键:', selectedKeys.value);

    if (!selectedProject.value && (!selectedKeys.value || selectedKeys.value.length === 0)) {
        Message.warning('请选择一个项目');
        return;
    }

    let projectId = '';

    // 优先使用selectedProject中的id
    if (selectedProject.value && selectedProject.value.id) {
        projectId = selectedProject.value.id;
    }
    // 如果没有selectedProject但有selectedKeys，则使用第一个选中的key
    else if (selectedKeys.value && selectedKeys.value.length > 0) {
        projectId = selectedKeys.value[0];
    }

    console.log('获取详情使用的项目ID:', projectId);

    if (!projectId) {
        Message.error('所选项目ID无效');
        return;
    }

    // 关闭选择弹窗
    addDialogVisible.value = false;

    // 打开内部项目管理弹窗，传递项目ID
    internalProjectRef.value?.show(projectId, 'add');
};

// 取消表单弹窗
const handleAddFormCancel = () => {
    addFormVisible.value = false;
    resetAddForm();
};

// 修改原有的取消处理方法
const handleAddCancel = () => {
    addDialogVisible.value = false;
    selectedProject.value = null;
    resetMdmQuery();
};

// 组件挂载时加载数据
onMounted(() => {
    handleQuery();
});
</script>

<style scoped lang="less">
.container {
    padding: 0 16px;

    .general-card {
        box-sizing: border-box;
        padding-top: 16px;
    }

    :deep(.arco-table-th) {
        &:last-child {
            text-align: center;
        }
    }

    :deep(.arco-table-td) {
        &:last-child {
            text-align: center;
        }
    }
}

.project-select-container {
    box-sizing: border-box;
    padding: 0 16px;
    .search-box {
        padding: 16px 0;
        // margin-bottom: 16px;
        display: flex;
        align-items: center;

        .label {
            margin-right: 8px;
        }
    }
    .project-list-container {
        height: 56vh;
        overflow-y: auto;
        padding-bottom: 16px;
    }
}

:deep(.arco-modal-footer) {
    border-top: 1px solid var(--color-neutral-3);
    padding: 16px 16px;
}
</style>
