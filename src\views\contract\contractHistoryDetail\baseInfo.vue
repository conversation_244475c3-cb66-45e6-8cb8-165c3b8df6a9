<template>
    <div class="contract-base-info">
        <!-- 顶部信息区域 -->
        <div class="info-header">
            <div class="contract-title">
                <!-- <h2>上海万洋科技众创城-租赁合同</h2> -->
                <div class="contract-basic">
                    <div class="contract-item">合同号：{{ contractData.contractNo }}</div>
                    <div class="contract-item status-wrapper">
                        <span>合同状态：</span>
                        <div class="status-tag active">{{ getDictLabel('contract_status', contractData.status as number)
                        }}</div>
                    </div>
                    <div class="contract-item">合同用途：{{ getDictLabel('diversification_purpose',
                        contractData.contractPurpose as number) }}</div>
                    <div class="contract-item">合同类型：{{ contractData.contractMode === 0 ? '标准合同' : '非标合同' }}</div>
                </div>
            </div>
        </div>

        <div class="divider"></div>

        <!-- 合同详细信息 -->
        <div class="contract-details">
            <a-descriptions :column="3" :label-style="labelStyle">
                <a-descriptions-item label="合同周期：">{{ contractData.startDate }} 至 {{ contractData.endDate
                }}</a-descriptions-item>
                <a-descriptions-item label="房源信息：">
                    <a-tooltip v-if="getRoomDisplayInfo().needEllipsis" 
                               :content="rooms" 
                               position="top">
                        <span class="room-ellipsis">{{ getRoomDisplayInfo().displayText }}</span>
                    </a-tooltip>
                    <span v-else>{{ rooms }}</span>
                </a-descriptions-item>
                <a-descriptions-item label="签订日期：">{{ contractData.signDate }}</a-descriptions-item>
                <a-descriptions-item label="承租人：">{{ contractData.customer.customerName }}</a-descriptions-item>
                <a-descriptions-item label="承租类型：">{{ contractData.customer.customerType === 2 ? '企业' : '个人'
                }}</a-descriptions-item>
                <template v-if="contractData.customer.customerType === 2">
                    <a-descriptions-item label="统一社会信用代码：">{{ contractData.customer.creditCode }}</a-descriptions-item>
                    <a-descriptions-item label="联系人：">{{ contractData.customer.contactName }}</a-descriptions-item>
                    <a-descriptions-item label="联系人手机号：">{{ contractData.customer.contactPhone }}</a-descriptions-item>
                </template>
            </a-descriptions>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ContractAddDTO } from '@/api/contract';
import { useContractStore } from '@/store/modules/contract/index';
import { getDictLabel } from '@/dict'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)

const rooms = computed(() => {
    return contractData.value.rooms.map((item: any) => item.parcelName + '-' + item.buildingName + '-' + item.roomName).join('、')
})

// 处理房源信息显示逻辑
const getRoomDisplayInfo = () => {
    if (!contractData.value.rooms || contractData.value.rooms.length === 0) {
        return { displayText: '', needEllipsis: false }
    }
    
    if (contractData.value.rooms.length <= 3) {
        return { displayText: rooms.value, needEllipsis: false }
    }
    
    const displayRooms = contractData.value.rooms.slice(0, 3)
    const displayText = displayRooms.map((item: any) => item.parcelName+'-'+item.buildingName+'-'+item.roomName).join('、') + '...'
    return { displayText, needEllipsis: true }
}

const labelStyle = {
    textAlign: 'right',
    color: '#666',
    minWidth: '120px',
    paddingRight: '8px',
    fontWeight: 'normal'
}
</script>

<style lang="less" scoped>
.contract-base-info {
    width: 100%;
    position: relative;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    background: linear-gradient(180deg, #ffffff, #ecf6ff);
    margin-bottom: 16px;


    .info-header {
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .contract-title {
            h2 {
                font-size: 18px;
                font-weight: 600;
                color: #000;
                margin: 0 0 16px 0;
            }

            .contract-basic {
                display: flex;
                flex-wrap: wrap;
                gap: 16px 40px;

                .contract-item {
                    font-size: 14px;
                    color: #666;
                    display: flex;
                    align-items: center;

                    &.status-wrapper {
                        display: flex;
                        align-items: center;

                        .status-tag {
                            padding: 0 8px;
                            height: 24px;
                            line-height: 24px;
                            border-radius: 2px;
                            font-size: 14px;
                            color: #FF8B1A;
                            background-color: #FFE7D3;
                            text-align: center;

                            &.active {
                                color: #FF8B1A;
                                background-color: #FFE7D3;
                            }
                        }
                    }
                }
            }
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }
    }

    .divider {
        height: 1px;
        background-color: #E5E6EA;
        width: 100%;
    }

    .contract-details {
        padding: 16px;

        :deep(.arco-descriptions-item-value) {
            color: #000;
            font-size: 14px;
        }
        
        .room-ellipsis {
            cursor: pointer;
            color: rgb(var(--primary-6));
            
            &:hover {
                color: rgb(var(--primary-5));
            }
        }
    }

    .contract-badge {
        position: absolute;
        right: 16px;
        bottom: 16px;
        width: 120px;
        height: 120px;

        .badge-content {
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, #9AAEE8, #6A8ECA);
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 3px solid rgba(255, 255, 255, 0.53);

            img {
                width: 60%;
                height: 60%;
                object-fit: contain;
            }
        }
    }
}
</style>
