<template>
	<a-drawer :visible="visible" :title="isEdit ? '编辑营收' : '新增营收'" :width="800" @cancel="handleCancel"
		@ok="handleOk" :confirm-loading="loading">
		<a-form ref="formRef" :model="formData" :rules="rules" :label-col-props="{ span: 6 }"
			:wrapper-col-props="{ span: 18 }" layout="horizontal">
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item field="projectId" label="项目" required>
						<ProjectTreeSelect v-model="formData.projectId" @change="handleProjectChange"
							:disabled="isEdit" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item field="customerId" label="承租人" required>
						<a-select v-model="formData.customerId" placeholder="请输入并选择承租人" :loading="customerLoading"
							@search="handleCustomerSearch" @change="handleCustomerChange" :disabled="isEdit" filterable
							remote allow-search>
							<a-option v-for="customer in customerOptions" :key="customer.id" :value="customer.id"
								:label="customer.customerName" />
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item field="contractId" label="合同" required>
						<a-select v-model="formData.contractId" placeholder="请选择合同" :loading="contractLoading"
							@change="handleContractChange" :disabled="isEdit || !formData.customerId">
							<a-option v-for="contract in contractOptions" :key="contract.id" :value="contract.id">
								<template #default>
									<div class="contract-option">
										<div class="contract-title">
											{{ contract.contractNo }}
											<a-tag :color="getContractStatusColor(contract.status || 0)" size="small"
												style="margin-left: 8px;">
												{{ getContractStatusText(contract.status || 0) }}
											</a-tag>
										</div>
										<div class="contract-period">
											{{ formatDate(contract.startDate) }} ~ {{ formatDate(contract.endDate) }}
										</div>
									</div>
								</template>
							</a-option>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item field="roomName" label="租赁单元">
						<a-input :model-value="selectedContract?.roomName || ''" placeholder="选择合同后自动显示" readonly />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item field="revenueMonth" label="营收月" required>
						<a-month-picker v-model="formData.revenueMonth" placeholder="请选择营收月" style="width: 100%"
							:disabled-date="disabledMonthDate" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item field="revenueAmount" label="营收金额" required>
						<a-input-number v-model="formData.revenueAmount" placeholder="请输入营收金额" :precision="2" :min="0"
							style="width: 100%">
							<template #prefix>¥</template>
						</a-input-number>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item field="reportDate" label="提报日期" required>
						<a-date-picker v-model="formData.reportDate" placeholder="请选择提报日期" style="width: 100%" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item field="attachment" label="凭证">
						<UploadFile
							v-model="formData.attachment"
							accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx"
							:maxSize="10"
							:limit="5"
							@success="handleUploadSuccess"
							@error="handleUploadError"
						/>
					</a-form-item>
				</a-col>
			</a-row>

			<!-- 合同信息展示 -->
			<a-divider orientation="left" v-if="selectedContract">合同信息</a-divider>
			<a-row :gutter="16" v-if="selectedContract">
				<a-col :span="12">
					<a-form-item label="合同编号">
						<span>{{ selectedContract.contractNo }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="合同状态">
						<a-tag :color="getContractStatusColor(selectedContract.status || 0)">
							{{ getContractStatusText(selectedContract.status || 0) }}
						</a-tag>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="租赁单元">
						<span>{{ selectedContract.roomName }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="合同期限">
						<span>{{ formatDate(selectedContract.startDate) }} ~ {{ formatDate(selectedContract.endDate)
							}}</span>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
	</a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
	addRevenue,
	updateRevenue,
	getCustomerList,
	getContractOptions,
	getRevenueList,
	type ContractRevenueAddDTO,
	type CustomerQueryDTO,
	type CustomerVO,
	type ContractOptionVO
} from '@/api/revenue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import UploadFile from '@/components/upload/uploadFile.vue'
import dayjs from 'dayjs'

// 定义emit
const emit = defineEmits<{
	success: []
}>()

// 扩展表单数据类型
interface FormData extends ContractRevenueAddDTO {
	customerId?: string
}

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const isEdit = ref(false)

// 表单引用和数据
const formRef = ref<any>()
const formData = reactive<FormData>({
	projectId: '',
	customerId: '',
	contractId: '',
	contractUnionId: '',
	revenueMonth: '',
	revenueAmount: 0,
	reportDate: dayjs().format('YYYY-MM-DD'),
	attachment: ''
})

// 移除fileList，使用全局upload组件的v-model方式

// 客户和合同选项
const customerLoading = ref(false)
const customerOptions = ref<CustomerVO[]>([])
const contractLoading = ref(false)
const contractOptions = ref<ContractOptionVO[]>([])

// 计算属性
const selectedContract = computed(() => {
	return contractOptions.value.find(contract => contract.id === formData.contractId)
})

// 表单验证规则
const rules = {
	projectId: [{ required: true, message: '请选择项目' }],
	customerId: [{ required: true, message: '请选择承租人' }],
	contractId: [{ required: true, message: '请选择合同' }],
	revenueMonth: [
		{ required: true, message: '请选择营收月' },
		{ validator: validateRevenueMonth }
	],
	revenueAmount: [
		{ required: true, message: '请输入营收金额' },
		{ type: 'number', min: 0.01, message: '营收金额必须大于0' }
	],
	reportDate: [{ required: true, message: '请选择提报日期' }]
}

// 自定义验证：营收月
async function validateRevenueMonth(value: string, callback: (error?: string) => void) {
	if (!value || !formData.contractId) {
		return callback()
	}

	try {
		// 检查是否重复提报
		const response = await getRevenueList({
			pageNum: 1,
			pageSize: 1,
			contractId: formData.contractId,
			revenueMonth: value
		})

		if (response.data && response.data.rows && response.data.rows.length > 0) {
			// 如果是编辑模式，排除当前记录
			if (isEdit.value && response.data.rows[0].id === formData.id) {
				return callback()
			}
			return callback('当前月已有提报记录，请勿重复提报')
		}

		callback()
	} catch (error) {
		callback()
	}
}

// 禁用日期函数
const disabledMonthDate = (current: Date) => {
	if (!selectedContract.value) return false

	const currentMonth = dayjs(current)
	const startMonth = dayjs(selectedContract.value.startDate)
	const endMonth = dayjs(selectedContract.value.endDate)

	return currentMonth.isBefore(startMonth, 'month') || currentMonth.isAfter(endMonth, 'month')
}

// 格式化日期
const formatDate = (dateStr?: string) => {
	if (!dateStr) return '-'
	return dayjs(dateStr).format('YYYY-MM-DD')
}

// 方法定义
const open = (record?: any, defaultData?: any) => {
	visible.value = true
	isEdit.value = !!record

	if (record) {
		// 编辑模式 - 填充表单数据
		Object.assign(formData, {
			...record,
			revenueAmount: Number(record.revenueAmount) || 0
		})

		    // 附件数据已通过v-model自动处理

		// 加载客户和合同信息
		if (record.projectId && record.customerId) {
			loadCustomersByProject(record.projectId, record.customerName)
			loadContractsByCustomer(record.projectId, record.customerId)
		}
	} else {
		// 新增模式 - 重置表单
		resetForm()

		// 如果有默认数据，填充默认值
		if (defaultData) {
			Object.assign(formData, defaultData)
		}
	}
}

const resetForm = () => {
	Object.assign(formData, {
		projectId: '',
		customerId: '',
		contractId: '',
		contractUnionId: '',
		revenueMonth: '',
		revenueAmount: 0,
		reportDate: dayjs().format('YYYY-MM-DD'),
		attachment: ''
	})
	  customerOptions.value = []
  contractOptions.value = []
  formRef.value?.resetFields()
}

const handleCancel = () => {
	visible.value = false
	resetForm()
}

const handleOk = async () => {
	try {
		const errors = await formRef.value?.validate()
		console.log('errors', errors)
		if (errors) return
		// return
		// const valid = await formRef.value?.validate()
		// if (!valid) {
			loading.value = true

			      // 附件已通过UploadFile组件自动处理并更新到formData.attachment

			// 移除customerId字段，因为后端不需要
			const submitData = { ...formData }
			delete submitData.customerId

			if (isEdit.value) {
				if (!submitData.attachment) {
					delete submitData.attachment
				}
				await updateRevenue(submitData)
				Message.success('编辑成功')
			} else {
				if (!submitData.attachment) {
					delete submitData.attachment
				}
				await addRevenue(submitData)
				Message.success('新增成功')
			}

			visible.value = false
			emit('success')
			resetForm()
		// }
	} catch (error: any) {
		// Message.error(error.message || (isEdit.value ? '编辑失败' : '新增失败'))
	} finally {
		// loading.value = false
	}
}

const handleProjectChange = (projectId: string) => {
	formData.customerId = ''
	formData.contractId = ''
	formData.contractUnionId = ''
	customerOptions.value = []
	contractOptions.value = []

	if (projectId && !isEdit.value) {
		// 新增模式下才清空客户选项
		customerOptions.value = []
	}
}

const handleCustomerSearch = async (searchValue: string) => {
	if (!formData.projectId || !searchValue.trim()) {
		return
	}

	await loadCustomersByProject(formData.projectId, searchValue)
}

const handleCustomerChange = async (customerId: string) => {
	formData.contractId = ''
	formData.contractUnionId = ''
	contractOptions.value = []

	if (customerId && formData.projectId) {
		await loadContractsByCustomer(formData.projectId, customerId)
	}
}

const handleContractChange = (contractId: string) => {
	// 合同变更时清空营收月，因为需要重新验证
	formData.revenueMonth = ''
	formData.contractUnionId = contractOptions.value.find(contract => contract.id === contractId)?.unionId || ''
}

const loadCustomersByProject = async (projectId: string, searchName?: string) => {
	if (!projectId) return

	customerLoading.value = true
	try {
		const params: CustomerQueryDTO = {
			pageNum: 1,
			pageSize: 50,
			projectId,
			customerName: searchName || ''
		}

		const response = await getCustomerList(params)
		if (response && response.rows) {
			customerOptions.value = response.rows
		}
	} catch (error) {
		Message.error('获取客户列表失败')
	} finally {
		customerLoading.value = false
	}
}

const loadContractsByCustomer = async (projectId: string, customerId: string) => {
	if (!projectId || !customerId) return

	contractLoading.value = true
	try {
		const response = await getContractOptions(projectId, customerId)
		if (response.data) {
			contractOptions.value = response.data
		}
	} catch (error) {
		Message.error('获取合同列表失败')
	} finally {
		contractLoading.value = false
	}
}

const handleUploadSuccess = (fileInfo: any) => {
  Message.success('文件上传成功')
}

const handleUploadError = (error: any) => {
  Message.error('文件上传失败')
}

const getContractStatusColor = (status: number) => {
	const colorMap: Record<number, string> = {
		1: 'orange',  // 待生效
		2: 'green',   // 已生效
		3: 'red',     // 已终止
		4: 'gray'     // 已作废
	}
	return colorMap[status] || 'gray'
}

const getContractStatusText = (status: number) => {
	const textMap: Record<number, string> = {
		1: '待生效',
		2: '已生效',
		3: '已终止',
		4: '已作废'
	}
	return textMap[status] || '未知'
}

// 暴露方法给父组件
defineExpose({
	open
})
</script>

<style scoped>
:deep(.arco-form-item-label) {
	font-weight: 500;
}

:deep(.arco-upload-list-item) {
	margin-top: 8px;
}

.contract-option {
	padding: 4px 0;
}

.contract-title {
	font-weight: 500;
	display: flex;
	align-items: center;
}

.contract-period {
	font-size: 12px;
	color: var(--color-text-3);
	margin-top: 2px;
}
</style>