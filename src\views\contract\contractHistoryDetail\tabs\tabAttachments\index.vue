<template>
    <div>
        <sectionTitle title="合同签署附件"> </sectionTitle>
        <div class="section-content">
            <div class="file-list">
                <div v-for="(file, index) in signAttachments" :key="index" class="file-item">
                    <div class="file-info">
                        <div class="file-status">
                            <a-tag v-if="file.status === 0" type="primary">待确认</a-tag>
                            <a-tag v-if="file.status === 1" type="success">待确收</a-tag>
                            <a-tag v-if="file.status === 2" type="success">已确收</a-tag>
                        </div>
                        <div class="file-icon">
                            <icon-file />
                        </div>
                        <a-typography-paragraph class="file-name" @click="handlePreview(file)" :ellipsis="{
                            rows: 1,
                            showTooltip: true,
                            css: true
                        }">
                            {{ file.fileName || '' }}
                        </a-typography-paragraph>
                    </div>
                    <!-- <a-space>
                        <a-link v-if="file.status === 0" @click="handleConfirm(file)">合同确认</a-link>
                        <a-link v-if="file.status === 1" @click="handleConfirmReceive(file)">合同确收</a-link>
                        <a-link v-if="file.status === 0" @click="handleRemove(index)">删除</a-link>
                    </a-space> -->
                </div>
            </div>
        </div>
        <!-- <sectionTitle title="其他附件"></sectionTitle> -->
    </div>
</template>

<script setup lang="ts">
import { ContractVo } from '@/api/contract';
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract/index';
import { confirmSignAttachment } from '@/api/contract';
import { Message } from '@arco-design/web-vue';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractVo)

const signAttachments = computed(() => JSON.parse(contractData.value.signAttachments || '[]') as any[])

const handlePreview = (file: any) => {
    if (file.fileUrl) {
        window.open(file.fileUrl);
    }
}

const handleConfirm = async (file: any) => {
    file.status = 1
    updateSignAttachments()
}

const handleConfirmReceive = async (file: any) => {
    file.status = 2
    updateSignAttachments()
}

const handleRemove = (index: number) => {
    signAttachments.value.splice(index, 1)
    updateSignAttachments()
}

const updateSignAttachments = async () => {
    const params = {
        contractId: contractData.value.id as string,
        attachments: signAttachments.value
    }
    await confirmSignAttachment(params)
    Message.success('合同确认成功')
}
</script>

<style scoped lang="less">
.file-list {
    width: 100%;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #E5E6EA;
        gap: 16px;

        .file-info {
            flex: 1;
            display: flex;
            align-items: center;

            .file-icon {
                margin-right: 8px;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            :deep(.file-name) {
                flex: 1;
                margin-bottom: 0;
                cursor: pointer;
            }
        }
    }
}

.section-content {
    padding: 16px 0;
}
</style>