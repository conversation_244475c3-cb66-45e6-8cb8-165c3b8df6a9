import http from './index'

const systemUrl = '/business-rent-admin'
const assetUrl = '/business-asset'
// 响应数据接口定义
interface ResponseData<T = any> {
  code: number
  data: T
  msg: string
  [key: string]: any
}

// API响应类型定义
export interface AjaxResult<T = any> {
  error?: boolean
  success?: boolean
  warn?: boolean
  empty?: boolean
  data?: T
  [key: string]: any
}

export interface TableDataInfo<T = any> {
  total?: number
  rows?: T[]
  code?: number
  msg?: string
}

// 项目同步DTO
export interface SynProjectDTO {
  id?: string
  type?: number
  mdmProjectId?: string
  mdmName?: string
  mdmSaleName?: string
  code?: string
  name?: string
  provinceCode?: string
  provinceName?: string
  cityCode?: string
  cityName?: string
  countryCode?: string
  countryName?: string
  mdmTypeName?: string
  assetType?: number
  propertyUnit?: string
  projectAddress?: string
  longitude?: string
  latitude?: string
  totalSelfArea?: string
  isDel?: boolean
  stageList?: SynStageDTO[]
  parcelList?: SynParcelDTO[]
  buildingList?: SynBuildingDTO[]
  floorList?: SynFloorDTO[]
  roomList?: SynRoomDTO[]
}

// 分期DTO
export interface SynStageDTO {
  id?: string
  mdmStageId?: string
  projectId?: string
  stageName?: string
  isDel?: boolean
}

// 地块DTO
export interface SynParcelDTO {
  id?: string
  mdmParcelId?: string
  projectId?: string
  stageId?: string
  parcelName?: string
  mdmNatureName?: string
  landUsage?: number
  mdmAddress?: string
  address?: string
  totalSelfArea?: string
  isDel?: boolean
}

// 楼栋DTO
export interface SynBuildingDTO {
  id?: string
  source?: number
  mdmBuildingId?: string
  projectId?: string
  parcelId?: string
  buildingName?: string
  certificateBuildingName?: string
  upFloorNums?: number
  underFloorNums?: number
  totalSelfArea?: string
  isDel?: boolean
}

// 楼层DTO
export interface SynFloorDTO {
  id?: string
  mdmFloorId?: string
  projectId?: string
  buildingId?: string
  floorName?: string
  floorTypeName?: string
  designFloorHeight?: string
  designLoad?: string
  isDel?: boolean
}

// 房间DTO
export interface SynRoomDTO {
  id?: string
  mdmRoomId?: string
  projectId?: string
  parcelId?: string
  buildingId?: string
  floorId?: string
  roomName?: string
  productType?: string
  areaType?: number
  areaTypeName?: string
  buildArea?: number
  innerArea?: number
  isSale?: boolean
  isCompanySelf?: boolean
  propertyType?: number
  propertyTypeName?: string
  selfHoldingTime?: string
  status?: number
  receiveId?: string
  changeId?: string
  disposalId?: string
  isDel?: boolean
}

// 组织同步DTO
export interface SyncOrgDTO {
  id?: string
  code?: string
  fullCode?: string
  fullName?: string
  name?: string
  orgType?: number
  level?: number
  parentId?: string
  thirdId?: string
  thirdName?: string
  sort?: number
  status?: number
  remark?: string
}

// 宿舍户型保存DTO
export interface ProjectHouseSaveDTO {
  id?: string
  projectId: string
  houseTypeName: string
  roomNum: number
  hallNum: number
  toiletNum: number
  decorationType: number
  livableNum: number
  houseTypeDesc?: string
  specialFeatures?: string
  specialFeaturesList?: string[]
  houseTypeImg?: string
  assetList?: HouseTypeAssetRelAddDTO[]
}

// 户型资产关联DTO
export interface HouseTypeAssetRelAddDTO {
  id?: string
  houseTypeId?: string
  fixedAssetId?: string
  fixedAssetName?: string
  fixedAssetSpec?: string
}

// 项目编辑DTO
export interface SysProjectEditDTO {
  id?: string
  merchantIds?: string[]
  parentId?: string
}

// 楼栋名称更新DTO
export interface SysBuildingUpdateNameDTO {
  id: string
  buildingName: string
}

// 楼栋列表查询DTO
export interface SysBuildingListQueryDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  projectId: string
  buildingOrRoomName?: string
}

// 项目批量关联片区DTO
export interface SysProjectBindAreaDTO {
  projectIds: string[]
  areaId: string
}

// 项目查询参数
export interface ProjectQueryParams {
  id?: string
  parentId?: string
  projectName?: string
  projectIdList?: string[]
  pageNum: number
  pageSize: number
}

// 项目VO
// 商业公司信息
export interface MerchantInfo {
  id: string
  orgCompanyName: string
}

export interface SysProjectVo {
  id?: string
  parentId?: string
  type?: number
  mdmProjectId?: string
  mdmName?: string
  mdmSaleName?: string
  code?: string
  name?: string
  provinceCode?: string
  provinceName?: string
  cityCode?: string
  cityName?: string
  countryCode?: string
  countryName?: string
  mdmTypeName?: string
  assetType?: number
  propertyUnit?: string
  projectAddress?: string
  longitude?: string
  latitude?: string
  totalSelfArea?: string
  createByName?: string
  updateByName?: string
  isDel?: boolean
  parcelList?: SysParcel[]
  merchantList?: MerchantInfo[]
  receivedArea?: number
  totalRoomArea?: number
  roomCount?: number
  pendingDeliveryCount?: number
  landUsageList?: number[]
}

// 地块信息
export interface SysParcel {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  mdmParcelId?: string
  projectId?: string
  stageId?: string
  parcelName?: string
  mdmNatureName?: string
  landUsage?: number
  mdmAddress?: string
  address?: string
  totalSelfArea?: string
  isDel?: boolean
}

// 楼栋VO
export interface SysBuildingVo {
  id?: string
  source?: number
  mdmBuildingId?: string
  projectId?: string
  parcelId?: string
  parcelName?: string
  buildingName?: string
  mdmBuildingName?: string
  certificateBuildingName?: string
  upFloorNums?: number
  underFloorNums?: number
  totalSelfArea?: string
  createByName?: string
  updateByName?: string
  isDel?: boolean
  assetArea?: number
  roomArea?: number
  roomCount?: number
}

// 宿舍户型VO
export interface ProjectHouseTypeVo {
  id?: string
  projectId?: string
  houseTypeName?: string
  roomNum?: number
  hallNum?: number
  toiletNum?: number
  decorationType?: number
  livableNum?: number
  houseTypeDesc?: string
  specialFeatures?: string
  houseTypeImg?: string
  createByName?: string
  updateByName?: string
  isDel?: boolean
}

// 宿舍户型详情VO
export interface ProjectHouseTypeDetailVO {
  id?: string
  projectId?: string
  houseTypeName?: string
  roomNum?: number
  hallNum?: number
  toiletNum?: number
  decorationType?: number
  livableNum?: number
  houseTypeDesc?: string
  specialFeatures?: string
  specialFeaturesList?: string[]
  houseTypeImg?: string
  assetList?: HouseTypeAssetRelVo[]
}

// 户型资产关联VO
export interface HouseTypeAssetRelVo {
  id?: string
  houseTypeId?: string
  fixedAssetId?: string
  fixedAssetName?: string
}

// 项目统计数据VO
export interface SysProjectStatisticsVo {
  projectCount?: number
  receivedArea?: number
  totalRoomArea?: number
  roomCount?: number
  pendingDeliveryCount?: number
}

// 项目树节点VO
export interface ProjectTreeNodeVo {
  id?: string
  name?: string
  children?: ProjectTreeNodeVo[]
}

// 项目树查询参数
export interface ProjectTreeQueryParams {
  projectId?: string
  parcelId?: string
  buildingId?: string
}

/**
 * 房间树查询DTO
 */
export interface SysRoomStructureQueryDTO {
  buildingIds?: string[];
  isCompanySelf?: boolean;
  productType?: string;
  roomName?: string;
  projectId?: string;
  pageNum?: number;
  pageSize?: number;
  type?: number;
}

/**
 * 楼层接口实体
 */
export interface SysFloor {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  mdmFloorId?: string
  projectId?: string
  buildingId?: string
  floorName?: string
  floorTypeName?: string
  designFloorHeight?: string
  designLoad?: string
  isDel?: boolean
}

/**
 * 楼栋接口实体
 */
export interface SysBuilding {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  source?: number
  mdmBuildingId?: string
  projectId?: string
  parcelId?: string
  buildingName?: string
  mdmBuildingName?: string
  certificateBuildingName?: string
  upFloorNums?: number
  underFloorNums?: number
  totalSelfArea?: string
  isDel?: boolean
}

/**
 * 项目房源树结构VO
 */
export interface ProjectRoomTreeVo {
  id?: string
  name?: string
  buildingId?: string
  buildingName?: string
  floorId?: string
  floorName?: string
  children?: ProjectRoomTreeVo[]
}

/**
 * 同步项目数据
 */
export function syncProject(data: SynProjectDTO): Promise<ResponseData<AjaxResult<boolean>>> {
  return http.post(`/sync/project/save`, data)
}

/**
 * 同步组织数据
 */
export function syncOrg(data: SyncOrgDTO[]): Promise<ResponseData<AjaxResult<boolean>>> {
  return http.post(`/sync/org/save`, data)
}

/**
 * 新增编辑宿舍户型
 * @param data 户型数据
 * @returns Promise<boolean>
 */
export function saveHouseType(data: ProjectHouseSaveDTO): Promise<ResponseData<boolean>> {
  return http.post(`${systemUrl}/project/houseType/save`, data)
}

/**
 * 编辑项目信息，包括关联商业公司和所属片区
 */
export function editProject(data: SysProjectEditDTO): Promise<ResponseData<AjaxResult>> {
  return http.post(`${systemUrl}/project/edit`, data)
}

/**
 * 根据楼栋ID修改楼栋名称
 */
export function updateBuildingName(data: SysBuildingUpdateNameDTO): Promise<ResponseData<boolean>> {
  return http.post(`${systemUrl}/project/edit/buildingName`, data)
}

/**
 * 查询项目下的楼栋列表，包含资产面积、房源总面积、房源个数等统计信息
 */
export function getBuildingList(data: SysBuildingListQueryDTO): Promise<ResponseData<SysBuildingVo>> {
  return http.post(`${systemUrl}/project/building/list`, data)
}

/**
 * 批量关联项目与片区的关系
 */
export function bindProjectArea(data: SysProjectBindAreaDTO): Promise<ResponseData<AjaxResult>> {
  return http.post(`${systemUrl}/project/bind/area`, data)
}

/**
 * 根据上级ID查询统计数据
 * @param id 项目ID
 * @returns Promise<SysProjectStatisticsVo>
 */
export function getProjectStatistics(id: string): Promise<ResponseData<SysProjectStatisticsVo>> {
  return http.get(`${systemUrl}/project/statistics/${id}`)
}

/**
 * 查询项目下的所有宿舍户型列表
 */
export function getHouseTypeList(id: string): Promise<ResponseData<ProjectHouseTypeVo[]>> {
  return http.get(`${systemUrl}/project/houseType/list`, { id })
}

/**
 * 根据户型ID查询宿舍户型详情，包含基本信息、户型特色和标准配套
 */
export function getHouseTypeDetail(id: string): Promise<ResponseData<ProjectHouseTypeDetailVO>> {
  return http.get(`${systemUrl}/project/houseType/detail`, { id })
}

/**
 * 根据项目ID查询项目详情，包括项目、地块、楼栋的信息及统计数据
 */
export function getProjectDetail(id: string): Promise<ResponseData<SysProjectVo>> {
  return http.get(`${systemUrl}/project/detail/${id}`)
}

/**
 * 根据条件查询项目列表，支持分页
 */
export function getProjectList(params: ProjectQueryParams): Promise<ResponseData<SysProjectVo>> {
  return http.get(`${systemUrl}/project/list`, params)
}

/**
 * 根据户型ID删除宿舍户型，若被房源引用则不能删除
 */
export function deleteHouseType(id: string): Promise<ResponseData<boolean>> {
  return http.delete(`${systemUrl}/project/houseType/delete/${id}`)
}

/**
 * 地块楼栋组织树接口
 * 根据传入的查询条件查询出所有的楼栋数据，关联地块表，按照地块id分组，构建树形结构
 */
export function getProjectTree(params?: ProjectTreeQueryParams): Promise<ResponseData<ProjectTreeNodeVo>> {
  return http.get(`${systemUrl}/project/tree`, params)
}

/**
 * 选择房源接口
 * @param params 查询参数
 * @returns 房源树结构
 */
export function getProjectRoomTree(params: SysRoomStructureQueryDTO): Promise<ResponseData<ProjectRoomTreeVo[]>> {
  return http.post(`${systemUrl}/project/room/tree`, params);
}

/**
 * 地块楼栋组织树接口
 * @param params 查询参数
 * @returns 组织树结构
 */
export function getBuildingTree(params?: {
  projectId?: string, 
  parcelId?: string, 
  buildingId?: string, 
  buildingName?: string
}): Promise<ResponseData<ProjectTreeNodeVo>> {
  return http.get(`${systemUrl}/project/building/tree`, params)
}

/**
 * 获取地块下拉列表
 * @param projectId 项目ID
 * @returns 地块列表
 */
export function getParcelList(projectId: string): Promise<ResponseData<SysParcel[]>> {
  return http.get(`${systemUrl}/project/parcel/list`, { projectId })
}

/**
 * 获取楼栋下拉列表
 * @param parcelId 地块ID
 * @returns 楼栋列表
 */
export function getBuildingSelectList(parcelId: string): Promise<ResponseData<SysBuilding[]>> {
  return http.get(`${systemUrl}/project/building/selectList`, { parcelId })
}

/**
 * 获取楼层下拉列表
 * @param buildingId 楼栋ID
 * @returns 楼层列表
 */
export function getFloorList(buildingId: string): Promise<ResponseData<SysFloor[]>> {
  return http.get(`${systemUrl}/project/floor/list`, { buildingId })
}

/**
 * 通过模版导入房源
 * @param file 文件数据
 * @returns Promise<ResponseData<AjaxResult>>
 */
export function importRoomTemplate(file: File){
  const formData = new FormData()
  formData.append('file', file)
  return http.post(`${assetUrl}/project/room/template/import`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 通过模版导入车位
 * @param file 文件数据
 * @returns Promise<ResponseData<AjaxResult>>
 */
export function importParkTemplate(file: File){
  const formData = new FormData()
  formData.append('file', file)
  return http.post(`${assetUrl}/project/park/template/import`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 下载房源导入模版
 * @param projectId 项目ID
 * @returns Promise<ResponseData<Blob>>
 */
export function downloadRoomTemplate(projectId: string){
  return http.downloadForGet(`${assetUrl}/project/room/template/download?projectId=${projectId}`)
}

/**
 * 下载车位导入模板
 * @param projectId 项目ID
 * @returns Promise<ResponseData<Blob>>
 */
export function downloadParkTemplate(projectId: string){
  return http.downloadForGet(`${assetUrl}/project/park/template/download?projectId=${projectId}`)
}