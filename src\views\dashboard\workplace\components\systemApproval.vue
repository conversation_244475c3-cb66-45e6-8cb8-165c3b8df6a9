<template>
  <div class="system-approval">
    <div class="system-title">
      <div class="title-decorator"></div>
      系统内待审批
    </div>
    <div class="system-content">
      <div class="system-items">
        <div class="system-item">
          <div class="item-label">集团目标下达</div>
          <div class="item-count">1</div>
        </div>
        <div class="system-item">
          <div class="item-label">项目目标上报</div>
          <div class="item-count">6</div>
        </div>
        <div class="system-item">
          <div class="item-label">营收提报</div>
          <div class="item-count">5</div>
        </div>
        <div class="system-item">
          <div class="item-label">房源拆分合并</div>
          <div class="item-count danger">11</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style lang="less" scoped>
.system-approval {
  background: #fff;
  border-radius: 10px 0 0 0;
  padding: 20px 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  .system-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #000;
    position: relative;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    z-index: 2;

    .title-decorator {
      width: 5px;
      height: 19px;
      background-color: #0071ff;
      border-radius: 4px 0px 4px 0px;
      margin-right: 14px;
    }
  }

  .system-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    border-radius: 10px 0 0 0;

    .system-items {
      position: relative;
      z-index: 2;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      height: 100%;
    }

    .system-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      background: linear-gradient(180deg, #FCFEFF 0%, #EAF4FE 98.28%);
      border-radius: 10px;

      .item-label {
        font-size: 14px;
        color: #3A4252;
        line-height: 1.4;
        margin-bottom: 4px;
      }

      .item-count {
        font-size: 20px;
        color: #3A4252;
        line-height: 1.4;
        font-weight: 500;

        &.danger {
          color: rgb(var(--danger-6));
        }
      }
    }
  }
}
</style>