import http from './index'

const orderUrl = '/business-rent-admin/booking'

/**
 * 查询定单列表
 * @param data 查询参数
 */
export function getOrderList(data: any) {
    return http.post(`${orderUrl}/list`, data)
}

/**
 * 获取定单详情
 * @param id 定单ID
 */
export function getOrderDetail(id: string) {
    return http.get(`${orderUrl}/detail/${id}`)
}

/**
 * 获取定单账单详情（包含costId）
 * @param id 定单ID
 */
export function getOrderCostDetail(id: string) {
    return http.get(`${orderUrl}/costDetail/${id}`)
}

// 定单详情VO（包含账单信息）
export interface BookingDetailVo {
  booking?: BookingVo
  cost?: CostVo
  flowRelList?: CostFlowRelVo[]
  flowLogList?: CostFlowLogVo[]
}

// 定单基本信息VO
export interface BookingVo {
  id?: string
  projectId?: string
  projectName?: string
  customerName?: string
  propertyType?: string
  roomId?: string
  roomName?: string
  bookingNo?: string
  bookingAmount?: number
  receivableDate?: string
  expectSignDate?: string
  isRefundable?: number
  cancelTime?: string
  cancelBy?: string
  cancelByName?: string
  cancelReason?: number
  isRefund?: number
  cancelEnclosure?: string
  cancelRemark?: string
  status?: number
  contractId?: string
  refundId?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  isDel?: boolean
  contractNo?: string
  signDate?: string
  contractLeaseUnit?: string
  lesseeName?: string
  receivedAmount?: number
  receivedDate?: string
  payMethod?: string
}

// 账单信息VO
export interface CostVo {
  id?: string
  projectId?: string
  projectName?: string
  chargeType?: number
  bizId?: string
  refundId?: string
  bizNo?: string
  customerId?: string
  customerName?: string
  costType?: number
  period?: number
  subjectId?: string
  subjectName?: string
  startDate?: string
  endDate?: string
  receivableDate?: string
  status?: number
  canPay?: boolean
  isRevenueBill?: boolean
  isRevenueGenerated?: boolean
  totalAmount?: number
  discountAmount?: number
  actualReceivable?: number
  receivedAmount?: number
  carryoverAmount?: number
  confirmStatus?: number
  createByName?: string
  updateByName?: string
  isDel?: boolean
  contractType?: number
  roomName?: string
  contractStatus?: number
  contractStatusTwo?: number
}

// 账单流水关系VO
export interface CostFlowRelVo {
  id?: string
  costId?: string
  refundId?: string
  flowId?: string
  flowNo?: string
  type?: number
  confirmStatus?: number
  confirmTime?: string
  confirmUserId?: string
  confirmUserName?: string
  payAmount?: number
  pendingAmount?: number
  acctAmount?: number
  remark?: string
  createByName?: string
  createTime?: string
  updateByName?: string
  isDel?: boolean
  projectId?: string
  projectName?: string
  entryTime?: string
  payType?: number
  payMethod?: number
  orderNo?: string
  usedAmount?: number
  payerName?: string
  target?: string
  merchant?: string
  cumulativeAcctAmount?: number
}

// 账单流水记录VO
export interface CostFlowLogVo {
  id?: string
  costId?: string
  refundId?: string
  flowId?: string
  flowNo?: string
  type?: number
  carryoverId?: string
  carryoverNo?: string
  amount?: number
  createByName?: string
  updateByName?: string
  isDel?: boolean
}

// 套打请求DTO
export interface BookingPrintDto {
  id: string // 定单ID
  type: number // 套打类型：10-定单, 100-退订单
  bookingData?: BookingAddDTO // 定单保存所有字段，由于在保存前可以调用，因此需要传所有字段
}

// 定单保存DTO
export interface BookingAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  projectId?: string
  customerName?: string
  propertyType?: string
  roomId?: string
  roomName?: string
  bookingNo?: string
  bookingAmount?: number
  receivableDate?: string
  expectSignDate?: string
  isRefundable?: number
  cancelTime?: string
  cancelBy?: string
  cancelByName?: string
  cancelReason?: number
  isRefund?: number
  cancelEnclosure?: string
  cancelRemark?: string
  status?: number
  contractId?: string
  refundId?: string
  isDel?: boolean
  isSubmit?: number // 0-暂存,1-提交
}

// 附件信息VO
export interface EnclosureInfo {
  fileName?: string
  fileUrl?: string
}

/**
 * 保存定单
 * @param data 定单数据
 */
export function saveOrder(data: any) {
    return http.post(`${orderUrl}/save`, data)
}

/**
 * 新增定单
 * @param data 定单数据
 */
export function addOrder(data: any) {
    return http.post(`${orderUrl}/save`, data)
}

/**
 * 保存定单草稿
 * @param data 定单数据
 */
export function saveOrderDraft(data: any) {
    const draftData = { ...data, isSubmit: 0 }
    return http.post(`${orderUrl}/save`, draftData)
}

/**
 * 更新定单
 * @param data 定单数据
 */
export function updateOrder(data: any) {
    return http.post(`${orderUrl}/save`, data)
}

/**
 * 删除定单
 * @param id 定单ID
 */
export function deleteOrder(id: string) {
    return http.delete(`${orderUrl}/delete/${id}`)
}

/**
 * 定单作废
 * @param data 作废信息
 */
export function invalidOrder(data: any) {
    return http.post(`${orderUrl}/invalid`, data)
}

/**
 * 作废定单（别名）
 * @param data 作废信息
 */
export function voidOrder(data: any) {
    return http.post(`${orderUrl}/invalid`, data)
}

/**
 * 退定
 * @param data 退定数据
 */
export function cancelOrder(data: any) {
    return http.post(`${orderUrl}/cancel`, data)
}

/**
 * 创建退订单
 * @param data 退订单数据
 */
export function createRefundOrder(data: any) {
    return http.post(`${orderUrl}/cancel`, data)
}

/**
 * 保存退订单草稿
 * @param data 退订单数据
 */
export function saveRefundOrderDraft(data: any) {
    const draftData = { ...data, isSubmit: 0 }
    return http.post(`${orderUrl}/cancel`, draftData)
}

/**
 * 定单套打接口
 * @param data 套打数据
 */
export function printBooking(data: BookingPrintDto) {
    return http.post(`${orderUrl}/print`, data)
}

/**
 * 创建退款申请
 * @param data 退款申请数据
 */
export function createRefundApplication(data: any) {
    const refundData = { ...data, isRefund: 1 }
    return http.post(`${orderUrl}/cancel`, refundData)
}

/**
 * 保存退款申请草稿
 * @param data 退款申请数据
 */
export function saveRefundApplicationDraft(data: any) {
    const draftData = { ...data, isRefund: 1, isSubmit: 0 }
    return http.post(`${orderUrl}/cancel`, draftData)
}

/**
 * 批量删除定单
 * @param ids 定单ID数组
 */
export function batchDeleteOrder(ids: string[]) {
    return http.delete(`${orderUrl}/delete/${ids.join(',')}`)
}

/**
 * 获取定单收款码
 * @param id 定单ID
 */
export function getOrderPaymentCode(id: string) {
    return http.get(`${orderUrl}/payment/code/${id}`)
}

/**
 * 获取定单状态统计
 */
export function getOrderStatusStatistics() {
    return http.get(`${orderUrl}/statistics/status`)
}

/**
 * 导出定单列表
 * @param data 查询参数
 */
export function exportOrderList(data: any) {
    return http.download(`${orderUrl}/export`, data)
}

/**
 * 变更定单状态
 * @param data 状态变更数据
 */
export function changeOrderStatus(data: any) {
    return http.post(`${orderUrl}/changeStatus`, data)
}

/**
 * 审核定单
 * @param data 审核数据
 */
export function approveOrder(data: any) {
    return http.post(`${orderUrl}/approve`, data)
}

/**
 * 拒绝定单
 * @param data 拒绝数据
 */
export function rejectOrder(data: any) {
    return http.post(`${orderUrl}/reject`, data)
} 