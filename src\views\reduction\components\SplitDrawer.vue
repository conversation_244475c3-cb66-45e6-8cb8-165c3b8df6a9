<template>
    <a-drawer
        v-model:visible="visible"
        title="账单拆分"
        class="common-drawer"
        :footer="true"
        width="1200px"
        @cancel="handleCancel"
    >
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" @click="handleSave" :loading="loading">保存</a-button>
            </a-space>
        </template>

        <div class="split-drawer">
            <!-- 拆分前信息 -->
            <div class="form-section">
                <SectionTitle title="拆分前信息" style="margin-bottom: 16px;" />
                <a-row :gutter="16">
                    <a-col :span="4">
                        <a-form-item label="账单类型">
                            <span>{{ getCostTypeText(originalBill.costType) }}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="4">
                        <a-form-item label="账单周期">
                            <span>{{ originalBill.startDate }} 至 {{ originalBill.endDate }}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="4">
                        <a-form-item label="应收日期">
                            <span>{{ originalBill.receivableDate }}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="4">
                        <a-form-item label="应收金额（元）">
                            <span>{{ originalBill.actualReceivable }}</span>
                        </a-form-item>
                    </a-col>
                </a-row>
            </div>

            <!-- 拆分信息 -->
            <div class="form-section">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <SectionTitle title="拆分信息" />
                    <a-button type="primary" @click="handleAddRow">新增</a-button>
                </div>
                
                <a-table
                    :columns="splitColumns"
                    :data="tableData"
                    :pagination="false"
                    :bordered="{ cell: true }"
                    size="small"
                    :scroll="{ x: 1000 }"
                    :row-class="getRowClass"
                >
                    <template #index="{ rowIndex }">
                        <span v-if="!isLastRow(rowIndex)">{{ rowIndex + 1 }}</span>
                        <span v-else>合计</span>
                    </template>
                    <template #period="{ record, rowIndex }">
                        <div v-if="!isLastRow(rowIndex)" style="display: flex; gap: 8px; align-items: center;">
                            <a-date-picker
                                v-model="record.startDate"
                                style="width: 120px"
                                format="YYYY-MM-DD"
                                :disabled="true"
                                size="small"
                            />
                            <span>至</span>
                            <a-date-picker
                                v-model="record.endDate"
                                style="width: 120px"
                                format="YYYY-MM-DD"
                                :disabled="isLastDataRow(rowIndex)"
                                :disabled-date="(date: Date) => getDisabledEndDate(date, record, rowIndex)"
                                @change="handleEndDateChange(record, rowIndex)"
                                size="small"
                            />
                        </div>
                    </template>
                    <template #receivableDate="{ record, rowIndex }">
                        <a-date-picker
                            v-if="!isLastRow(rowIndex)"
                            v-model="record.receivableDate"
                            style="width: 120px"
                            format="YYYY-MM-DD"
                            size="small"
                        />
                    </template>
                    <template #totalAmount="{ record, rowIndex }">
                        <span v-if="!isLastRow(rowIndex)">{{ floatCalculate.toFixed(record.totalAmount || 0) }}</span>
                        <span v-else>{{ floatCalculate.toFixed(totalAmount) }}</span>
                    </template>
                    <template #discountAmount="{ record, rowIndex }">
                        <span v-if="!isLastRow(rowIndex)">{{ floatCalculate.toFixed(floatCalculate.add(record.discountAmount || 0, record.originalReductionAmount || 0)) }}</span>
                        <span v-else>{{ floatCalculate.toFixed(floatCalculate.add(totalDiscountAmount, totalOriginalReductionAmount)) }}</span>
                    </template>
                    <template #actualReceivable="{ record, rowIndex }">
                        <span v-if="!isLastRow(rowIndex)">{{ floatCalculate.toFixed(record.actualReceivable || 0) }}</span>
                        <span v-else>{{ floatCalculate.toFixed(totalActualReceivable) }}</span>
                    </template>
                    <template #action="{ rowIndex }">
                        <a-button
                            v-if="canDelete(rowIndex)"
                            type="text"
                            size="mini"
                            status="danger"
                            @click="handleDeleteRow(rowIndex)"
                        >
                            删除
                        </a-button>
                    </template>
                </a-table>
            </div>
        </div>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import SectionTitle from '@/components/sectionTitle/index.vue'

// 组件事件
const emit = defineEmits<{
    success: [splitData?: any]
    cancel: []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)

// 原始账单信息
const originalBill = reactive<any>({
    costId: '',
    tempId: '',
    id: '',
    costType: '',
    startDate: '',
    endDate: '',
    receivableDate: '',
    totalAmount: 0,
    discountAmount: 0,
    originalReductionAmount: 0,
    actualReceivable: 0
})

// 拆分列表
const splitList = ref<any[]>([])

// 表格列定义
const splitColumns = [
    {
        title: '序号',
        dataIndex: 'index',
        slotName: 'index',
        width: 80,
        align: 'center'
    },
    {
        title: '账单周期',
        dataIndex: 'period',
        slotName: 'period',
        width: 280,
        align: 'center'
    },
    {
        title: '应收日期',
        dataIndex: 'receivableDate',
        slotName: 'receivableDate',
        width: 120,
        align: 'center'
    },
    {
        title: '账单总额（元）',
        dataIndex: 'totalAmount',
        slotName: 'totalAmount',
        width: 120,
        align: 'center'
    },
    {
        title: '优惠金额（元）',
        dataIndex: 'discountAmount',
        slotName: 'discountAmount',
        width: 120,
        align: 'center'
    },
    {
        title: '应收金额（元）',
        dataIndex: 'actualReceivable',
        slotName: 'actualReceivable',
        width: 120,
        align: 'center'
    },
    {
        title: '操作',
        dataIndex: 'action',
        slotName: 'action',
        width: 80,
        align: 'center'
    }
]

// 计算属性
const totalAmount = computed(() => {
    return splitList.value.reduce((sum, item) => floatCalculate.add(sum, item.totalAmount || 0), 0)
})

const totalDiscountAmount = computed(() => {
    return splitList.value.reduce((sum, item) => floatCalculate.add(sum, item.discountAmount || 0), 0)
})

const totalOriginalReductionAmount = computed(() => {
    return splitList.value.reduce((sum, item) => floatCalculate.add(sum, item.originalReductionAmount || 0), 0)
})

const totalActualReceivable = computed(() => {
    return splitList.value.reduce((sum, item) => floatCalculate.add(sum, item.actualReceivable || 0), 0)
})

// 表格数据（包含合计行）
const tableData = computed(() => {
    const totalRow = {
        startDate: '',
        endDate: '',
        receivableDate: '',
        totalAmount: totalAmount.value,
        discountAmount: totalDiscountAmount.value,
        actualReceivable: totalActualReceivable.value,
        isTotal: true
    }
    return [...splitList.value, totalRow]
})

// 获取费用类型文本
const getCostTypeText = (costType: number) => {
    const typeMap: Record<number, string> = {
        1: '保证金',
        2: '租金',
        3: '其他费用'
    }
    return typeMap[costType] || ''
}

// 浮点数运算工具函数
const floatCalculate = {
    add: (a: number, b: number): number => {
        const factor = Math.pow(10, 2)
        return Math.round((a + b) * factor) / factor
    },
    subtract: (a: number, b: number): number => {
        const factor = Math.pow(10, 2)
        return Math.round((a - b) * factor) / factor
    },
    multiply: (a: number, b: number): number => {
        const factor = Math.pow(10, 2)
        return Math.round((a * b) * factor) / factor
    },
    divide: (a: number, b: number): number => {
        if (b === 0) return 0
        const factor = Math.pow(10, 2)
        return Math.round((a / b) * factor) / factor
    },
    toFixed: (num: number): number => {
        return Math.round(num * 100) / 100
    }
}

// 显示弹框
const show = (billData: any) => {
    visible.value = true
    
    // 设置原始账单信息
    Object.assign(originalBill, billData)
    
    // 初始化拆分列表（默认两行）
    initSplitList()
}

// 初始化拆分列表
const initSplitList = () => {
    const startDate = originalBill.startDate
    const endDate = originalBill.endDate

    // 计算平均分配的金额
    const halfAmount = floatCalculate.divide(originalBill.totalAmount, 2)
    const halfDiscount = floatCalculate.divide(originalBill.discountAmount, 2)
    const halfOriginalReduction = floatCalculate.divide(originalBill.originalReductionAmount, 2)
    const halfActualReceivable = floatCalculate.divide(originalBill.actualReceivable, 2)

    splitList.value = [
        {
            startDate: startDate,
            endDate: '', // 需要手动选择
            receivableDate: originalBill.receivableDate,
            totalAmount: halfAmount,
            discountAmount: halfDiscount,
            originalReductionAmount: halfOriginalReduction,
            actualReceivable: halfActualReceivable
        },
        {
            startDate: '', // 会根据上一行的结束日期+1自动设置
            endDate: endDate,
            receivableDate: originalBill.receivableDate,
            totalAmount: floatCalculate.subtract(originalBill.totalAmount, halfAmount), // 剩余金额
            discountAmount: floatCalculate.subtract(originalBill.discountAmount, halfDiscount), // 剩余优惠金额
            originalReductionAmount: floatCalculate.subtract(originalBill.originalReductionAmount, halfOriginalReduction), // 剩余原减免金额
            actualReceivable: floatCalculate.subtract(originalBill.actualReceivable, halfActualReceivable) // 剩余应收金额
        }
    ]
}

// 取消
const handleCancel = () => {
    visible.value = false
    emit('cancel')
}

// 判断是否是最后一行（合计行）
const isLastRow = (rowIndex: number) => {
    return rowIndex === tableData.value.length - 1
}

// 判断是否是最后一个数据行（不包括合计行）
const isLastDataRow = (rowIndex: number) => {
    return rowIndex === splitList.value.length - 1
}

// 判断是否可以删除
const canDelete = (rowIndex: number) => {
    // 不能删除合计行，且至少保留2行数据
    return !isLastRow(rowIndex) && splitList.value.length > 2 && rowIndex > 0 && rowIndex < splitList.value.length - 1
}

// 获取行样式类
const getRowClass = (record: any, rowIndex: number) => {
    return isLastRow(rowIndex) ? 'total-row' : ''
}

// 获取结束日期的禁用规则
const getDisabledEndDate = (date: Date, record: any, rowIndex: number) => {
    const dateStr = date.toISOString().split('T')[0]

    // 不能小于等于开始日期
    if (record.startDate && dateStr <= record.startDate) {
        return true
    }

    // 不能大于原始账单的结束日期
    if (originalBill.endDate && dateStr > originalBill.endDate) {
        return true
    }

    return false
}

// 处理结束日期变化
const handleEndDateChange = (record: any, rowIndex: number) => {
    if (!record.endDate) return

    // 更新下一行的开始日期（如果存在）
    if (rowIndex < splitList.value.length - 1) {
        const nextDay = getNextDay(record.endDate)
        splitList.value[rowIndex + 1].startDate = nextDay
    }

    // 重新计算所有金额
    recalculateAllAmounts()
}

// 获取下一天的日期
const getNextDay = (dateStr: string) => {
    const date = new Date(dateStr)
    date.setDate(date.getDate() + 1)
    return date.toISOString().split('T')[0]
}

// 计算金额
const calculateAmounts = (record: any, rowIndex: number) => {
    if (!record.startDate || !record.endDate) return

    // 计算天数
    const startDate = new Date(record.startDate)
    const endDate = new Date(record.endDate)
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1

    // 计算原始账单的总天数
    const originalStartDate = new Date(originalBill.startDate)
    const originalEndDate = new Date(originalBill.endDate)
    const totalDays = Math.ceil((originalEndDate.getTime() - originalStartDate.getTime()) / (1000 * 60 * 60 * 24)) + 1

    // 按比例计算金额
    const ratio = days / totalDays
    record.totalAmount = floatCalculate.multiply(originalBill.totalAmount, ratio)
    record.discountAmount = floatCalculate.multiply(originalBill.discountAmount, ratio)
    record.originalReductionAmount = floatCalculate.multiply(originalBill.originalReductionAmount, ratio)
    // 实际应收 = 账单总额 - 优惠金额 - 原减免金额
    const totalDiscount = floatCalculate.add(record.discountAmount, record.originalReductionAmount)
    record.actualReceivable = floatCalculate.subtract(record.totalAmount, totalDiscount)
}

// 新增行
const handleAddRow = () => {
    if (splitList.value.length < 2) return

    // 在倒数第二行后插入新行
    const insertIndex = splitList.value.length - 1
    const prevRow = splitList.value[insertIndex - 1]
    const lastRow = splitList.value[insertIndex]

    if (!prevRow.endDate) {
        Message.warning('请先完善上一行的结束日期')
        return
    }

    const newRow = {
        startDate: getNextDay(prevRow.endDate),
        endDate: '',
        receivableDate: originalBill.receivableDate,
        totalAmount: 0,
        discountAmount: 0,
        actualReceivable: 0
    }

    // 更新最后一行的开始日期为空，等待用户设置上一行的结束日期
    lastRow.startDate = ''

    splitList.value.splice(insertIndex, 0, newRow)

    // 重新计算所有行的金额
    recalculateAllAmounts()
}

// 重新计算所有金额
const recalculateAllAmounts = () => {
    let totalAmountSum = 0
    let totalDiscountSum = 0
    let totalOriginalReductionSum = 0
    let totalActualReceivableSum = 0

    // 先计算前面所有行的金额（除了最后一行）
    for (let i = 0; i < splitList.value.length - 1; i++) {
        const record = splitList.value[i]
        if (record.startDate && record.endDate) {
            calculateAmounts(record, i)
            totalAmountSum = floatCalculate.add(totalAmountSum, record.totalAmount)
            totalDiscountSum = floatCalculate.add(totalDiscountSum, record.discountAmount)
            totalOriginalReductionSum = floatCalculate.add(totalOriginalReductionSum, record.originalReductionAmount)
            totalActualReceivableSum = floatCalculate.add(totalActualReceivableSum, record.actualReceivable)
        }
    }

    // 最后一行使用剩余金额，确保总和一致
    const lastRecord = splitList.value[splitList.value.length - 1]
    if (lastRecord && lastRecord.startDate && lastRecord.endDate) {
        lastRecord.totalAmount = floatCalculate.subtract(originalBill.totalAmount, totalAmountSum)
        lastRecord.discountAmount = floatCalculate.subtract(originalBill.discountAmount, totalDiscountSum)
        lastRecord.originalReductionAmount = floatCalculate.subtract(originalBill.originalReductionAmount, totalOriginalReductionSum)
        lastRecord.actualReceivable = floatCalculate.subtract(originalBill.actualReceivable, totalActualReceivableSum)
    }
}

// 删除行
const handleDeleteRow = (rowIndex: number) => {
    if (!canDelete(rowIndex)) return

    splitList.value.splice(rowIndex, 1)

    // 重新连接日期
    for (let i = 0; i < splitList.value.length - 1; i++) {
        const currentRow = splitList.value[i]
        const nextRow = splitList.value[i + 1]

        if (currentRow.endDate) {
            nextRow.startDate = getNextDay(currentRow.endDate)
        }
    }

    // 重新计算所有金额
    recalculateAllAmounts()
}

// 保存
const handleSave = () => {
    // 验证数据
    if (!validateSplitData()) {
        return
    }

    // 构造拆分后的数据
    const splitData = {
        originalTempId: originalBill.tempId, // 原账单临时ID
        originalCostId: originalBill.costId, // 原账单ID
        splitList: splitList.value.map((item, index) => {
            // 生成唯一的拆分tempId
            // 如果原账单已经是拆分的，在其基础上继续拆分
            const timestamp = Date.now()
            const randomSuffix = Math.random().toString(36).substring(2, 6)
            const splitTempId = `${originalBill.tempId}_split_${index}_${timestamp}_${randomSuffix}`

            return {
                // 拆分后的账单保持相同的costId
                costId: originalBill.costId,
                tempId: splitTempId, // 确保唯一性的拆分临时ID
                costType: originalBill.costType,
                period: item.period || (index + 1), // 期数
                rentPeriod: `${item.startDate} 至 ${item.endDate}`,
                startDate: item.startDate,
                endDate: item.endDate,
                receivableDate: item.receivableDate,
                totalAmount: item.totalAmount,
                discountAmount: item.discountAmount,
                originalReductionAmount: item.originalReductionAmount, // 原减免金额
                actualReceivable: item.actualReceivable,
                receivedAmount: 0, // 拆分后的账单默认未收款
                isSplit: true // 标记为拆分后的账单
            }
        })
    }

    // 拆分后把数据传递给调整列表
    visible.value = false
    emit('success', splitData)
}

// 验证拆分数据
const validateSplitData = () => {
    // 检查是否所有行都有完整的日期
    for (let i = 0; i < splitList.value.length; i++) {
        const row = splitList.value[i]
        if (!row.startDate || !row.endDate) {
            Message.error(`第${i + 1}行的账单周期不完整`)
            return false
        }
    }

    // 检查金额是否匹配
    const totalAmountDiff = Math.abs(totalAmount.value - originalBill.totalAmount)
    const discountAmountDiff = Math.abs(totalDiscountAmount.value - originalBill.discountAmount)
    const actualReceivableDiff = Math.abs(totalActualReceivable.value - originalBill.actualReceivable)

    if (totalAmountDiff > 0.01) {
        Message.error('拆分后的账单总额与原账单不匹配，请检查拆分信息')
        return false
    }

    if (discountAmountDiff > 0.01) {
        Message.error('拆分后的优惠金额与原账单不匹配，请检查拆分信息')
        return false
    }

    if (actualReceivableDiff > 0.01) {
        Message.error('拆分后的应收金额与原账单不匹配，请检查拆分信息')
        return false
    }

    return true
}

// 暴露方法
defineExpose({
    show
})
</script>

<style scoped lang="less">
.form-section {
    margin-bottom: 24px;

    &:last-child {
        margin-bottom: 0;
    }
}

:deep(.total-row) {
    background-color: #f5f5f5;
    td {
        border-top: 0;
    }
}
</style>
