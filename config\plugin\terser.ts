/**
 * Terser 压缩配置
 * 用于代码压缩和优化
 */
import type { Plugin } from 'vite';

export interface TerserOptions {
    /** 是否移除 console */
    dropConsole?: boolean;
    /** 是否移除 debugger */
    dropDebugger?: boolean;
    /** 是否移除注释 */
    removeComments?: boolean;
    /** 是否启用并行压缩 */
    parallel?: boolean;
    /** 是否生成 source map */
    sourcemap?: boolean;
}

export default function configTerserPlugin(options: TerserOptions = {}): any {
    const {
        dropConsole = true,
        dropDebugger = true,
        removeComments = true,
        parallel = true,
        sourcemap = false,
    } = options;

    return {
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: dropConsole,
                drop_debugger: dropDebugger,
                pure_funcs: dropConsole ? ['console.log', 'console.warn', 'console.error'] : [],
                dead_code: true, // 移除死代码
                unused: true, // 移除未使用的变量
                conditionals: true, // 优化条件语句
                evaluate: true, // 计算常量表达式
                if_return: true, // 优化 if-return 语句
                join_vars: true, // 合并变量声明
                reduce_vars: true, // 减少变量使用
                sequences: true, // 使用逗号操作符
            },
            mangle: {
                safari10: true, // 解决 Safari 10 的兼容性问题
                properties: false, // 不混淆属性名
            },
            format: {
                comments: !removeComments, // 是否保留注释
                beautify: false, // 不美化代码
            },
            // 注意: Vite 会自动处理并行压缩，不需要手动配置 parallel
        },
        sourcemap: sourcemap,
        reportCompressedSize: true,
        assetsInlineLimit: 4096, // 内联小于4kb的资源
    };
} 