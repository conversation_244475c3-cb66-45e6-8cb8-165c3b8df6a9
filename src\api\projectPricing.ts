import http from './index'

const systemUrl = '/business-rent-admin'

// 房间定价信息接口
export interface PricingRoomRelVo {
  id?: string
  pricingId?: string
  roomId?: string
  roomName?: string
  parcelId?: string
  parcelName?: string
  buildingId?: string
  buildingName?: string
  floorId?: string
  floorName?: string
  roomUsage?: string
  roomType?: string
  rentArea?: number // 计租面积
  planningBusiness?: string
  baseRent?: number
  additionalFee?: number
  calcUnit?: number // 计租单位(1元/平方米/月 2元/月 3元/日)
  isRentIncrease?: boolean // 租金是否递增(0否 1是)
  increaseInterval?: number // 递增间隔(年)
  increaseRate?: number // 单价递增率(%)
  depositType?: number // 保证金类型(固定金额等)
  depositAmount?: number // 保证金金额
  paymentMethod?: string // 支付方式(1月付 2季付 3半年付 4年付)
  minRentalPeriod?: string // 租赁期限(开始)
  maxRentalPeriod?: string // 租赁期限(结束)
  rentalPeriodUnit?: string // 租赁期限单位(1月 2年)
  freeRentType?: number // 免租期参考因素(1不限 2租赁期限)
  freeRentPeriod?: number // 免租期(月)
  freeRentTerm?: string // 免租期租赁期限
  createByName?: string
  updateByName?: string
  isDel?: boolean
}

// 立项定价选择房源接口入参DTO
export interface PricingRoomTreeDTO {
  projectId?: string // 项目ID
  buildingIds?: string[] // 楼栋ID列表
  roomUsage?: string // 用途
  isPriced?: number // 是否定价(0否 1是)
  roomName?: string // 房源名称
}

// 立项定价选择房源接口出参VO
export interface PricingRoomTreeVo {
  treeId?: string // 节点ID
  treeName?: string // 节点名称
  roomId?: string // 房间ID
  roomName?: string // 房源名称
  parcelId?: string // 地块ID
  parcelName?: string // 地块
  buildingId?: string // 楼栋ID
  buildingName?: string // 楼栋
  floorId?: string // 楼层ID
  floorName?: string // 楼层
  roomUsage?: string // 用途
  roomType?: string // 户型
  rentArea?: number // 计租面积
  children?: PricingRoomTreeVo[] // 子节点列表
}

// 立项定价申请数据接口
export interface PricingVo {
  id?: string
  applicationCode?: string // 立项定价申请编号
  applicationName?: string // 立项定价申请名称
  pricingType?: number // 定价类型（1首次定价 2过程调价）
  projectId?: string // 项目ID
  projectName?: string // 项目名称
  buildingName?: string // 定价楼栋
  roomCount?: number // 定价房源数
  status?: number // 状态(0草稿 1审批中 2已通过 3已驳回)
  discountRules?: string // 折扣规则配置
  pricingDesc?: string | null // 定价说明文档
  pricingDetails?: string | null // 其他定价说明
  attachments?: string | null // 相关附件
  createByName?: string // 创建人姓名
  updateByName?: string // 更新人姓名
  approveDate?: string | null // 审批日期
  roomList?: PricingRoomRelVo[] | null // 房间定价
}

// 查询参数接口
export interface PricingQueryDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  id?: string
  applicationCode?: string // 立项定价申请编号
  applicationName?: string // 立项定价申请名称
  pricingType?: number // 定价类型（1首次定价 2过程调价）
  projectId?: string // 项目ID
  projectName?: string // 项目名称
  buildingName?: string // 定价楼栋
  roomCount?: number // 定价房源数
  status?: number // 状态(0草稿 1审批中 2已通过 3已驳回)
  discountRules?: string // 折扣规则配置
  pricingDesc?: string // 定价说明文档
  pricingDetails?: string // 其他定价说明
  attachments?: string // 相关附件
  createBy?: string // 创建人账号
  createByName?: string // 创建人姓名
  createTime?: string // 创建时间
  updateBy?: string // 更新人账号
  updateByName?: string // 更新人姓名
  updateTime?: string // 更新时间
  isDel?: boolean
}

// 模板导出参数接口
export interface PricingTemplateDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  projectId?: string // 项目id
  roomIdList?: string[] // 房间id列表
}

// 立项定价申请列表返回数据结构
export interface PricingListResponse {
  total: number
  rows: PricingVo[]
  code: number
  msg: string
}

// 查询立项定价申请列表
export function getPricingList(params: PricingQueryDTO) {
  return http.post<PricingListResponse>(`${systemUrl}/pricing/list`, params)
}

// 通过模版导入立项定价
export function importPricingByTemplate(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return http.post(`${systemUrl}/pricing/template/import`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 通过模版导入历史立项定价
export function importHistoryPricingByTemplate(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return http.post(`${systemUrl}/pricing/template/import/history`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出立项定价模版
export function downloadPricingTemplate(params: PricingTemplateDTO) {
  return http.post(`${systemUrl}/pricing/template/download`, params, {
    responseType: 'blob'
  })
}

// 新增立项定价申请
export function addPricing(data: PricingVo) {
  return http.post(`${systemUrl}/pricing/add`, data)
}

// 修改立项定价申请
export function updatePricing(data: PricingVo) {
  return http.put(`${systemUrl}/pricing/edit`, data)
}

// 删除立项定价申请
export function deletePricing(id: string) {
  return http.delete(`${systemUrl}/pricing/delete/${id}`)
}

// 获取立项定价申请详情
export function getPricingDetail(id: string) {
  return http.get<PricingVo>(`${systemUrl}/pricing/detail/${id}`)
}

// 提交立项定价申请
export function submitPricing(id: string) {
  return http.post(`${systemUrl}/pricing/commit/${id}`)
}

/**
 * 读取导入文件数据
 * @param data 文件数据
 * @returns Promise<any>
 */
export function readPricingTemplateData(data: FormData) {
  return http.post(`${systemUrl}/pricing/template/data/read`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 立项定价选择房源接口
export function getPricingRoomTree(params: PricingRoomTreeDTO) {
  return http.post<PricingRoomTreeVo[]>(`${systemUrl}/pricing/room/tree`, params)
}