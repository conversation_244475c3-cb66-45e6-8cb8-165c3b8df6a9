import { mergeConfig } from 'vite';
import baseConfig from './vite.config.base';
import configCompressPlugin from './plugin/compress';
import configVisualizerPlugin from './plugin/visualizer';
import configArcoResolverPlugin from './plugin/arcoResolver';
import configImageminPlugin from './plugin/imagemin';
import configTerserPlugin from './plugin/terser';

export default mergeConfig(
    {
        mode: 'production',
        plugins: [
            configCompressPlugin('gzip'),
            configVisualizerPlugin(),
            configArcoResolverPlugin(),
            configImageminPlugin(),
        ],
        build: {
            rollupOptions: {
                output: {
                    manualChunks: {
                        arco: ['@arco-design/web-vue'],
                        chart: ['echarts', 'vue-echarts'],
                        vue: [
                            'vue',
                            'vue-router',
                            'pinia',
                            '@vueuse/core',
                            'vue-i18n',
                        ],
                    },
                },
                // 外部化依赖（如果需要CDN加载）
                // external: ['vue', 'vue-router'],
            },
            chunkSizeWarningLimit: 2000,
            // 使用新的 Terser 配置（UAT环境保留console用于调试）
            ...configTerserPlugin({
                dropConsole: false,
                dropDebugger: true,
                removeComments: false,
                parallel: true,
                sourcemap: true,
            }),
        },
    },
    baseConfig
);
