import http from './index';

const roomUrl = '/business-rent-admin';

/**
 * 房源状态枚举
 */
export enum RoomStatus {
    /** 草稿 */
    DRAFT = 0,
    /** 待生效 */
    PENDING = 10,
    /** 生效中 */
    ACTIVE = 20,
    /** 已失效 */
    EXPIRED = 30,
}

/**
 * 房源类型枚举
 */
export enum RoomType {
    /** 普通 */
    NORMAL = 1,
    /** 多经 */
    MULTIPLE = 2,
}

/**
 * 面积类型枚举
 */
export enum AreaType {
    /** 实测 */
    MEASURED = 1,
    /** 预测 */
    ESTIMATED = 2,
}

/**
 * 计租面积类型枚举
 */
export enum RentAreaType {
    /** 建筑面积 */
    BUILD_AREA = 1,
    /** 套内面积 */
    INNER_AREA = 2,
}

/**
 * 交付状态枚举
 */
export enum PaymentStatus {
    /** 未交付 */
    NOT_DELIVERED = 1,
    /** 已交付 */
    DELIVERED = 2,
}

/**
 * 运营主体枚举
 */
export enum OperationSubject {
    /** 商服 */
    BUSINESS = 1,
    /** 众创城 */
    INNOVATION = 2,
}

/**
 * 自用主体枚举
 */
export enum SelfUseSubject {
    /** 运营 */
    OPERATION = 1,
    /** 商服 */
    BUSINESS = 2,
    /** 众创 */
    INNOVATION = 3,
    /** 其他 */
    OTHER = 4,
}

/**
 * 调整类型枚举
 */
export enum AdjustmentType {
    /** 拆分 */
    SPLIT = 3,
    /** 合并 */
    MERGE = 4,
}

/**
 * 审批状态枚举
 */
export enum ApprovalStatus {
    /** 待提交 */
    PENDING_SUBMIT = 0,
    /** 审批中 */
    APPROVING = 1,
    /** 已通过 */
    APPROVED = 2,
    /** 已拒绝 */
    REJECTED = 3,
}

/**
 * 房态简图订单信息VO
 */
export interface BookDiagramVo {
    /** 客户名称 */
    customerName?: string;
    /** 公司名称 */
    companyName?: string;
    /** 预定单金额 */
    bookingAmount?: number;
    /** 预定时间 */
    bookingTime?: string;
    /** 是否可退 */
    canRefund?: boolean;
    /** 房间状态 */
    roomStatus?: string;
}

/**
 * 房态简图合同信息VO
 */
export interface ContractDiagramVo {
    /** 房间id */
    roomId?: string;
    /** 合同id */
    contractId?: string;
    /** 合同号 */
    contractNo?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同类别 */
    contractCategory?: string;
    /** 合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房 */
    contractType?: number;
    /** 签约类型,0-新签,1-续签 */
    signType?: number;
    /** 租金 */
    rentPrice?: number;
    /** 承租类型 */
    tenantType?: string;
    /** 承租人 */
    tenantName?: string;
    /** 承租人证件号 */
    tenantIdCard?: string;
    /** 合同开始时间 */
    startDate?: string;
    /** 合同结束时间 */
    endDate?: string;
    /** 合同租期 */
    rentTerm?: string;
    /** 合同一级状态 */
    status?: number;
    /** 房间状态 */
    roomStatus?: string;
    /** 审批状态 */
    approveStatus?: number;
}

/**
 * 房态简图房间信息VO
 */
export interface RoomDiagramVo {
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 用途 */
    propertyType?: string;
    /** 计租面积 */
    rentArea?: number;
    /** 朝向 */
    orientation?: string;
    /** 是否自用 */
    isSelfUse?: boolean;
    /** 可招商日期 */
    rentalStartDate?: string;
    /** 出场标识 */
    needCheckOut?: boolean;
    /** 状态 */
    roomStatusName?: string;
    /** 状态编码 */
    roomStatus?: number;
    /** 标识数组 */
    tags?: string[];
    /** 户型ID */
    houseTypeId?: string;
    /** 表价 */
    tablePrice?: number;
    /** 计租面积类型 */
    rentAreaType?: number;
    /** 空置天数 */
    emptyDays?: number;
    /** 自用主体 */
    selfUseSubject?: number;
    /** 对外出租起始日期 */
    externalRentStartDate?: string;
    /** 是否脏房 */
    isDirty?: boolean;
    /** 是否锁房 */
    isLock?: boolean;
    /** 是否维修 */
    isMaintain?: boolean;
    /** 订单信息 */
    bookingVo?: BookDiagramVo;
    /** 订单信息列表 */
    bookings?: BookDiagramVo[];
    /** 合同信息 */
    contractVo?: ContractDiagramVo;
    /** 合同信息列表 */
    contracts?: ContractDiagramVo[];
}

/**
 * 房源合并/拆分审批DTO
 */
export interface RoomMergeSplitApprovalDTO {
    /** 创建人账号 */
    createBy?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新人账号 */
    updateBy?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 主键ID */
    id?: string;
    /** 审批结果 */
    approveResult?: string;
}

/**
 * 多批房源导入DTO
 */
export interface ImportMultipleRoomsDTO {
    /** 文件 */
    file?: File;
}

/**
 * 房源导入结果VO
 */
export interface ImportRoomResultVo {
    /** 是否成功 */
    success?: boolean;
    /** 错误信息 */
    error?: boolean;
    /** 警告信息 */
    warn?: boolean;
    /** 是否为空 */
    empty?: boolean;
    /** 额外属性 */
    [key: string]: any;
}

/**
 * 房态简图楼层信息VO
 */
export interface FloorDiagramVo {
    /** 楼层ID */
    floorId?: string;
    /** 楼层名称 */
    floorName?: string;
    /** 房间信息列表 */
    rooms?: RoomDiagramVo[];
}

/**
 * 房源新增/编辑DTO
 */
export interface RoomAddDTO {
    /** 创建人账号 */
    createBy?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新人账号 */
    updateBy?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 主键ID */
    id?: string;
    /** 房源名称 */
    roomName?: string;
    /** 房源类型（1普通 2多经） */
    type?: number;
    /** 物业类型，二级字典 */
    propertyType?: string;
    /** 项目id */
    projectId?: string;
    /** 地块id */
    parcelId?: string;
    /** 地块名称 */
    parcelName?: string;
    /** 楼栋id */
    buildingId?: string;
    /** 楼栋名称 */
    buildingName?: string;
    /** 楼层id */
    floorId?: string;
    /** 楼层名称 */
    floorName?: string;
    /** 房间id， sys_room表的id */
    roomId?: string;
    /** 状态（0草稿 10待生效 20生效中 30已失效 ） */
    status?: number;
    /** 是否锁房（0否 1是） */
    isLock?: boolean;
    /** 是否脏房（0否 1是） */
    isDirty?: boolean;
    /** 是否维修（0否 1是） */
    isMaintain?: boolean;
    /** 合成编码 */
    roomCode?: string;
    /** 描述 */
    remark?: string;
    /** 拆分合并id */
    mergeSplitId?: string;
    /** 预计生效日期 */
    planEffectDate?: string;
    /** 实际生效日期 */
    actualEffectDate?: string;
    /** 最新的立项定价id */
    projectPriceId?: string;
    /** 表价 */
    tablePrice?: number;
    /** 底价 */
    bottomPrice?: number;
    /** 面积类型（1实测 2预测） */
    areaType?: number;
    /** 建筑面积 */
    buildArea?: number;
    /** 套内面积 */
    innerArea?: number;
    /** 计租面积类型（1建筑面积 2套内面积） */
    rentAreaType?: number;
    /** 计租面积 */
    rentArea?: number;
    /** 层高 */
    storey?: number;
    /** 荷载值 */
    value?: number;
    /** 朝向 */
    orientation?: string;
    /** 户型id */
    houseTypeId?: string;
    /** 智能水电表 */
    smartWaterMeter?: string;
    /** 智能锁 */
    smartLock?: string;
    /** 资产运营模式（自持、可售、代招商） */
    assetOperationMode?: string;
    /** 资产运营分类 */
    assetOperationType?: string;
    /** 产权情况 */
    propertyStatus?: string;
    /** 交付状态（1未交付 2已交付） */
    paymentStatus?: number;
    /** 特殊标签（1保障房） */
    specialTag?: string;
    /** 商管承租成本单价（最新） */
    latestRentPrice?: number;
    /** 商管承租起计日期（最新） */
    latestRentStartDate?: string;
    /** 商管承租到期日期（最新） */
    latestRentEndDate?: string;
    /** 商管承租成本单价第一次 */
    firstRentPrice?: number;
    /** 商管承租起计日期第一次 */
    firstRentStartDate?: string;
    /** 商管承租到期日期第一次 */
    firstRentEndDate?: string;
    /** 运营主体（1商服 2众创城）,字典 */
    operationSubject?: number;
    /** 可招商日期 */
    rentalStartDate?: string;
    /** 对外出租起始日期 */
    externalRentStartDate?: string;
    /** 是否自用（0否 1是） */
    isSelfUse?: boolean;
    /** 自用主体（1运营 2商服 3众创 4其他） */
    selfUseSubject?: number;
    /** 自用用途 */
    selfUsePurpose?: string;
    /** 默认流程标识 */
    defaultProcessFlag?: boolean;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 系统房间ID列表 */
    sysRoomIds?: string[];
    /** 多经绑定房源id */
    djBindRoomId?: string;
    /** 多经绑定房源名称 */
    djBindName?: string;
}

/**
 * 房源查询DTO
 */
export interface RoomQueryDTO {
    /** 主键ID */
    id?: string;
    /** 房源名称 */
    roomName?: string;
    /** 房源类型（1普通 2多经） */
    type?: number;
    /** 物业类型，二级字典 */
    propertyType?: string;
    /** 项目id */
    projectId?: string;
    /** 地块id */
    parcelId?: string;
    /** 地块名称 */
    parcelName?: string;
    /** 楼栋id */
    buildingId?: string;
    /** 楼栋名称 */
    buildingName?: string;
    /** 楼层id */
    floorId?: string;
    /** 楼层名称 */
    floorName?: string;
    /** 房间id， sys_room表的id */
    roomId?: string;
    /** 状态（0草稿 10待生效 20生效中 30已失效 ） */
    status?: number;
    /** 是否锁房（0否 1是） */
    isLock?: boolean;
    /** 是否脏房（0否 1是） */
    isDirty?: boolean;
    /** 是否维修（0否 1是） */
    isMaintain?: boolean;
    /** 合成编码 */
    roomCode?: string;
    /** 描述 */
    remark?: string;
    /** 拆分合并id */
    mergeSplitId?: string;
    /** 预计生效日期 */
    planEffectDate?: string;
    /** 实际生效日期 */
    actualEffectDate?: string;
    /** 最新的立项定价id */
    projectPriceId?: string;
    /** 表价 */
    tablePrice?: number;
    /** 底价 */
    bottomPrice?: number;
    /** 面积类型（1实测 2预测） */
    areaType?: number;
    /** 建筑面积 */
    buildArea?: number;
    /** 套内面积 */
    innerArea?: number;
    /** 计租面积类型（1建筑面积 2套内面积） */
    rentAreaType?: number;
    /** 计租面积 */
    rentArea?: number;
    /** 层高 */
    storey?: number;
    /** 荷载值 */
    value?: number;
    /** 朝向 */
    orientation?: string;
    /** 户型id */
    houseTypeId?: string;
    /** 智能水电表 */
    smartWaterMeter?: string;
    /** 智能锁 */
    smartLock?: string;
    /** 资产运营模式（自持、可售、代招商） */
    assetOperationMode?: string;
    /** 资产运营分类 */
    assetOperationType?: string;
    /** 产权情况 */
    propertyStatus?: string;
    /** 交付状态（1未交付 2已交付） */
    paymentStatus?: number;
    /** 特殊标签（1保障房） */
    specialTag?: string;
    /** 商管承租成本单价（最新） */
    latestRentPrice?: number;
    /** 商管承租起计日期（最新） */
    latestRentStartDate?: string;
    /** 商管承租到期日期（最新） */
    latestRentEndDate?: string;
    /** 商管承租成本单价第一次 */
    firstRentPrice?: number;
    /** 商管承租起计日期第一次 */
    firstRentStartDate?: string;
    /** 商管承租到期日期第一次 */
    firstRentEndDate?: string;
    /** 运营主体（1商服 2众创城）,字典 */
    operationSubject?: number;
    /** 可招商日期 */
    rentalStartDate?: string;
    /** 对外出租起始日期 */
    externalRentStartDate?: string;
    /** 是否自用（0否 1是） */
    isSelfUse?: boolean;
    /** 自用主体（1运营 2商服 3众创 4其他） */
    selfUseSubject?: number;
    /** 自用用途 */
    selfUsePurpose?: string;
    /** 创建人账号 */
    createBy?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新人账号 */
    updateBy?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 是否定价（1是 0否） */
    priceFlag?: number;
    /** 页码 */
    pageNum: number;
    /** 页大小 */
    pageSize: number;
    /** 多经绑定房源id */
    djBindRoomId?: string;
    /** 多经绑定房源名称 */
    djBindName?: string;
    /** 地块或楼栋名称 */
    parcelOrBuildingName?: string;
}

/**
 * 房源VO
 */
export interface RoomVo {
    /** 主键ID */
    id?: string;
    /** 房源名称 */
    roomName?: string;
    /** 房源类型（1普通 2多经） */
    type?: number;
    /** 物业类型，二级字典 */
    propertyType?: string;
    /** 项目id */
    projectId?: string;
    /** 地块id */
    parcelId?: string;
    /** 地块名称 */
    parcelName?: string;
    /** 楼栋id */
    buildingId?: string;
    /** 楼栋名称 */
    buildingName?: string;
    /** 楼层id */
    floorId?: string;
    /** 楼层名称 */
    floorName?: string;
    /** 房间id， sys_room表的id */
    roomId?: string;
    /** 状态（0草稿 10待生效 20生效中 30已失效 ） */
    status?: number;
    /** 是否锁房（0否 1是） */
    isLock?: boolean;
    /** 是否脏房（0否 1是） */
    isDirty?: boolean;
    /** 是否维修（0否 1是） */
    isMaintain?: boolean;
    /** 合成编码 */
    roomCode?: string;
    /** 描述 */
    remark?: string;
    /** 拆分合并id */
    mergeSplitId?: string;
    /** 预计生效日期 */
    planEffectDate?: string;
    /** 实际生效日期 */
    actualEffectDate?: string;
    /** 最新的立项定价id */
    projectPriceId?: string;
    /** 表价 */
    tablePrice?: number;
    /** 底价 */
    bottomPrice?: number;
    /** 面积类型（1实测 2预测） */
    areaType?: number;
    /** 建筑面积 */
    buildArea?: number;
    /** 套内面积 */
    innerArea?: number;
    /** 计租面积类型（1建筑面积 2套内面积） */
    rentAreaType?: number;
    /** 计租面积 */
    rentArea?: number;
    /** 层高 */
    storey?: number;
    /** 荷载值 */
    value?: number;
    /** 朝向 */
    orientation?: string;
    /** 户型id */
    houseTypeId?: string;
    /** 智能水电表 */
    smartWaterMeter?: string;
    /** 智能锁 */
    smartLock?: string;
    /** 资产运营模式（自持、可售、代招商） */
    assetOperationMode?: string;
    /** 资产运营分类 */
    assetOperationType?: string;
    /** 产权情况 */
    propertyStatus?: string;
    /** 交付状态（1未交付 2已交付） */
    paymentStatus?: number;
    /** 特殊标签（1保障房） */
    specialTag?: string;
    /** 商管承租成本单价（最新） */
    latestRentPrice?: number;
    /** 商管承租起计日期（最新） */
    latestRentStartDate?: string;
    /** 商管承租到期日期（最新） */
    latestRentEndDate?: string;
    /** 商管承租成本单价第一次 */
    firstRentPrice?: number;
    /** 商管承租起计日期第一次 */
    firstRentStartDate?: string;
    /** 商管承租到期日期第一次 */
    firstRentEndDate?: string;
    /** 运营主体（1商服 2众创城）,字典 */
    operationSubject?: number;
    /** 可招商日期 */
    rentalStartDate?: string;
    /** 对外出租起始日期 */
    externalRentStartDate?: string;
    /** 是否自用（0否 1是） */
    isSelfUse?: boolean;
    /** 自用主体（1运营 2商服 3众创 4其他） */
    selfUseSubject?: number;
    /** 自用用途 */
    selfUsePurpose?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 户型 */
    houseType?: string;
    /** 生效日期 */
    effectDate?: string;
    /** 原房源 */
    originalRoom?: string;
    /** 多经绑定房源id */
    djBindRoomId?: string;
    /** 多经绑定房源名称 */
    djBindName?: string;
    /** 地块或楼栋名称 */
    parcelOrBuildingName?: string;
}

/**
 * 房源树查询DTO
 */
export interface RoomTreeQueryDTO {
    /** 额外参数 */
    params?: Record<string, any>;
    /** 页码 */
    pageNum?: number;
    /** 页大小 */
    pageSize?: number;
    /** 合同类型，0-非宿舍,1-宿舍,2-多经,3-日租房 */
    contractType?: string;
    /** 业态 */
    buildingType?: string;
    /** 楼栋id */
    buildingId?: string;
    /** 租控状态 0-可租 1-已租 */
    rentStatus?: string;
    /** 房源名称 */
    roomName?: string;
    /** 项目id */
    projectId?: string;
    /** 定价标识  */
    pricingFlag?: number;
}

/**
 * 房源树VO
 */
export interface RoomTreeVo {
    /** 树结构id */
    id?: string;
    /** 树结构名称 */
    name?: string;
    /** 父级id */
    parentId?: string;
    /** 房源roomId */
    roomId?: string;
    /** 房源名称 */
    roomName?: string;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 地块id */
    parcelId?: string;
    /** 地块名称 */
    parcelName?: string;
    /** 楼栋id */
    buildingId?: string;
    /** 楼栋名称 */
    buildingName?: string;
    /** 物业类型 */
    propertyType?: string;
    /** 租控状态 0-可租 1-已租（字段待确认） */
    rentStatus?: string;
    /** 计租面积类型（1建筑面积 2套内面积） */
    rentAreaType?: number;
    /** 计租面积 */
    rentArea?: number;
    /** 标准租金（字段待确认） */
    rentAmount?: number;
    /** 价格 */
    price?: number;
    /** 底价 */
    bottomPrice?: number;
    /** 价格单位 */
    priceUnit?: number;
    /** 1：项目 2：地块 3：楼栋 4：房间 */
    level?: number;
    /** 子节点列表 */
    children?: RoomTreeVo[];
}

/**
 * 资产构成查询DTO
 */
export interface AssetCompositionQueryDTO {
    /** 项目id */
    projectId?: string;
    /** 地块id */
    parcelId?: string;
    /** 楼栋id */
    buildingId?: string;
}

/**
 * 资产构成VO
 */
export interface AssetCompositionVo {
    /** 物业类型 */
    propertyType?: string;
    /** 房源数量 */
    roomCount?: number;
    /** 总面积 */
    totalArea?: number;
    /** 已租面积 */
    rentedArea?: number;
    /** 可租面积 */
    availableArea?: number;
    /** 出租率 */
    occupancyRate?: number;
}

/** 房源统计查询参数 */
export interface RoomStatisticsQueryDTO {
    /** 项目id */
    projectId?: string;
    /** 地块id */
    parcelId?: string;
    /** 楼栋id */
    buildingId?: string;
}

/**
 * 房态简图查询DTO
 */
export interface RoomDiagramQueryDTO {
    /** 项目ID */
    projectId?: string;
    /** 地块ID */
    parcelId?: string;
    /** 楼栋ID */
    buildingId?: string;
    /** 楼层ID */
    floorId?: string;
    /** 状态 */
    roomStatus?: number;
    /** 用途 */
    propertyType?: string | null;
    /** 指定房态时间 */
    diagramDate?: string;
}

/** 资产构成统计信息 */
export interface AssetCompositionStatisticsVo {
    /** 类型编码 */
    typeCode?: string;
    /** 类型名称 */
    typeName?: string;
    /** 建筑面积 */
    buildArea?: number;
    /** 数量 */
    count?: number;
}

/** 房源统计结果 */
export interface RoomStatisticsVo {
    /** 自持面积 */
    selfHoldArea?: number;
    /** 已接收资产面积 */
    receivedAssetArea?: number;
    /** 房源总面积 */
    totalRoomArea?: number;
    /** 房源数量 */
    roomCount?: number;
    /** 接收资产构成 */
    receivedAssetComposition?: AssetCompositionStatisticsVo[];
    /** 房源业态构成 */
    roomBizTypeComposition?: AssetCompositionStatisticsVo[];
}

/**
 * 拆分合并记录查询DTO
 */
export interface MergeSplitRecordQueryDTO {
    /** 房源名称 */
    roomName?: string;
    /** 调整类型（1拆分 2合并） */
    adjustmentType?: number;
    /** 审批状态（0待提交 1审批中 2已通过 3已拒绝） */
    approvalStatus?: number;
    /** 创建日期开始 */
    createTimeStart?: string;
    /** 创建日期结束 */
    createTimeEnd?: string;
    /** 项目id */
    projectId?: string;
    /** 页码 */
    pageNum: number;
    /** 页大小 */
    pageSize: number;
}

/**
 * 拆分合并记录VO
 */
export interface MergeSplitRecordVo {
    /** 主键ID */
    id?: string;
    /** 审批状态（0待提交 1审批中 2已通过 3已拒绝） */
    approvalStatus?: number;
    /** 调整类型（1拆分 2合并） */
    adjustmentType?: number;
    /** 申请编号 */
    applicationNo?: string;
    /** 地块 */
    parcelName?: string;
    /** 楼栋 */
    buildingName?: string;
    /** 原房源 */
    originalRooms?: string;
    /** 调整后房源 */
    adjustedRooms?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建人 */
    createByName?: string;
}

/**
 * 拆分合并申请DTO
 */
export interface MergeSplitApplicationDTO {
    /** 主键ID */
    id?: string;
    /** 调整类型（1拆分 2合并） */
    adjustmentType?: number;
    /** 原房源ID列表 */
    originalRoomIds?: string[];
    /** 调整后房源信息 */
    adjustedRooms?: RoomAddDTO[];
    /** 申请原因 */
    reason?: string;
    /** 项目id */
    projectId?: string;
}

/**
 * 房源拆分申请DTO
 */
export interface RoomSplitAddDTO {
    /** 创建人账号 */
    createBy?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新人账号 */
    updateBy?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 主键ID */
    id?: string;
    /** 申请编号 */
    applicationNo?: string;
    /** 操作类型（拆分固定为3） */
    operationType?: number;
    /** 地块ID */
    areaId?: string;
    /** 楼栋ID */
    buildingId?: string;
    /** 来源房间ID */
    sourceRoomId?: string;
    /** 来源房间信息 */
    sourceRoomVo?: RoomVo;
    /** 目标房间信息列表 */
    targetRooms?: RoomAddDTO[];
    /** 审批状态（0草稿 1待审批 2已审批 3驳回） */
    approvalStatus?: number;
    /** 生效方式（1立即生效 2到期生效） */
    effectType?: number;
    /** 生效日期 */
    effectDate?: string;
    /** 附件 */
    attachments?: string;
    /** 调整原因 */
    adjustmentReason?: string;
    /** 备注 */
    remark?: string;
    /** 提交类型（1暂存 2提交） */
    submitType?: number;
}

/**
 * 批量更新房源DTO
 */
export interface BatchUpdateRoomDTO {
    /** 房源ID列表 */
    roomIds?: string[];
    /** 房源信息 */
    roomInfo?: RoomAddDTO;
}

/**
 * 立项定价申请DTO
 */
export interface ProjectPricingApplicationDTO {
    /** 房源ID列表 */
    roomIds?: string[];
    /** 申请原因 */
    reason?: string;
    /** 项目id */
    projectId?: string;
}

/**
 * 导入房源DTO
 */
export interface ImportRoomDTO {
    /** 文件 */
    file?: File;
    /** 项目id */
    projectId?: string;
}

/**
 * 引入已接收资产DTO
 */
export interface ImportReceivedAssetDTO {
    /** 资产ID列表 */
    assetIds?: string[];
    /** 项目id */
    projectId?: string;
}

// 房间查询DTO
export interface SysRoomSimpleDTO {
    buildingIds?: string[];
    isCompanySelf?: boolean;
    productType?: string;
    roomName?: string;
    type?: number;
    projectId?: string;
}

// 房间信息VO
export interface SysRoomVo {
    id?: string;
    mdmRoomId?: string;
    projectId?: string;
    parcelId?: string;
    parcelName?: string;
    buildingId?: string;
    buildingName?: string;
    floorId?: string;
    floorName?: string;
    roomName?: string;
    productType?: string;
    areaType?: number;
    areaTypeName?: string;
    buildArea?: number;
    innerArea?: number;
    isSale?: boolean;
    isCompanySelf?: boolean;
    propertyType?: number;
    propertyTypeName?: string;
    selfHoldingTime?: string;
    status?: number;
    receiveId?: string;
    changeId?: string;
    disposalId?: string;
    createByName?: string;
    updateByName?: string;
    isDel?: boolean;
}

// 楼层信息VO
export interface SysFloorSimpleVo {
    id?: string;
    floorName?: string;
    rooms?: SysRoomVo[];
}

// 楼栋信息VO
export interface SysBuildingSimpleVo {
    id?: string;
    buildingName?: string;
    parcelId?: string;
    parcelName?: string;
    floors?: SysFloorSimpleVo[];
}

/**
 * 修改房源
 * @param data 房源信息
 * @returns Promise<boolean>
 */
export function updateRoom(data: RoomAddDTO) {
    return http.put(`${roomUrl}/room`, data);
}

/**
 * 新增房源
 * @param data 房源信息
 * @returns Promise<boolean>
 */
export function createRoom(data: RoomAddDTO) {
    return http.post(`${roomUrl}/room`, data);
}

/**
 * 查询用户有权限的房间树
 * @param data 查询参数
 * @returns Promise<RoomTreeVo[]>
 */
export function getRoomTree(data: RoomTreeQueryDTO) {
    return http.post(`${roomUrl}/room/roomOptions`, data);
}

/**
 * 导出房源列表
 * @param data 查询参数
 * @returns Promise<Blob>
 */
export function exportRoomList(data: RoomQueryDTO) {
    return http.download(`${roomUrl}/room/export`, data)
}

/**
 * 房源生效接口
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function effectRooms(data: string[]) {
    return http.post(`${roomUrl}/room/effect`, data);
}

/**
 * 修改房源
 * @param data 房源信息
 * @returns Promise<boolean>
 */
export function editRoom(data: RoomAddDTO) {
    return http.post(`${roomUrl}/room/edit`, data);
}

/**
 * 新增房源
 * @param data 房源信息
 * @returns Promise<boolean>
 */
export function addRoom(data: RoomAddDTO) {
    return http.post(`${roomUrl}/room/add`, data);
}

/**
 * 查询房源列表
 * @param data 查询参数
 * @returns Promise<RoomVo[]>
 */
export function getRoomList(data: RoomQueryDTO) {
    return http.get(`${roomUrl}/room/list`, data);
}

/**
 * 删除房源
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function deleteRooms(data: { ids: string[] }) {
    return http.delete(`${roomUrl}/room/delete`, data);
}

/**
 * 获取房源详细信息
 * @param data 房源ID
 * @returns Promise<RoomVo>
 */
export function getRoomDetail(data: { id: string }) {
    return http.get(`${roomUrl}/room/detail`, data);
}

/**
 * 删除单个房源
 * @param data 房源ID
 * @returns Promise<boolean>
 */
export function deleteRoomById(data: { id: string }) {
    return http.delete(`${roomUrl}/room/deleteById`, data);
}

/**
 * 查询房间树形结构（按楼栋和楼层分组）
 * @param data 查询参数
 * @returns Promise<SysBuildingSimpleVo[]>
 */
export function getRoomTreeStructure(data: SysRoomSimpleDTO = {}) {
    return http.post('/business-asset/project/room/tree', data);
}

/**
 * 获取资产构成信息
 * @param data 查询参数
 * @returns Promise<AssetCompositionVo[]>
 */
export function getAssetComposition(data: AssetCompositionQueryDTO) {
    return http.get(`${roomUrl}/room/assetComposition`, data);
}

/**
 * 查询拆分合并记录列表
 * @param data 查询参数
 * @returns Promise<MergeSplitRecordVo[]>
 */
export function getMergeSplitRecordList(data: MergeSplitRecordQueryDTO) {
    return http.post(`${roomUrl}/room/mergeSplit/list`, data);
}

/**
 * 提交拆分合并申请
 * @param data 申请信息
 * @returns Promise<boolean>
 */
export function submitMergeSplitApplication(data: MergeSplitApplicationDTO) {
    return http.post(`${roomUrl}/mergeSplit/submit`, data);
}

/**
 * 编辑拆分合并申请
 * @param data 申请信息
 * @returns Promise<boolean>
 */
export function editMergeSplitApplication(data: MergeSplitApplicationDTO) {
    return http.put(`${roomUrl}/mergeSplit/edit`, data);
}

/**
 * 删除拆分合并申请
 * @param data 申请ID
 * @returns Promise<boolean>
 */
export function deleteMergeSplitApplication(data: { id: string }) {
    return http.delete(`${roomUrl}/mergeSplit/delete`, data);
}

/**
 * 审批拆分合并申请
 * @param data 审批信息
 * @returns Promise<boolean>
 */
export function approveMergeSplitApplication(data: { id: string; status: number; remark?: string }) {
    return http.post(`${roomUrl}/mergeSplit/approve`, data);
}

/**
 * 获取拆分合并申请详情
 * @param data 申请ID
 * @returns Promise<MergeSplitApplicationDTO>
 */
export function getMergeSplitApplicationDetail(data: { id: string }) {
    return http.get(`${roomUrl}/mergeSplit/detail`, data);
}

/**
 * 批量更新房源
 * @param data 批量更新信息
 * @returns Promise<boolean>
 */
export function batchUpdateRooms(data: BatchUpdateRoomDTO) {
    return http.post(`${roomUrl}/room/batchUpdate`, data);
}

/**
 * 合并房源
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function mergeRooms(data: { roomIds: string[] }) {
    return http.post(`${roomUrl}/room/merge`, data);
}

/**
 * 拆分房源
 * @param data 拆分信息
 * @returns Promise<boolean>
 */
export function splitRoom(data: RoomSplitAddDTO) {
    return http.post(`${roomUrl}/room/split`, data);
}

/**
 * 作废房源
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function invalidateRooms(data: { roomIds: string[] }) {
    return http.post(`${roomUrl}/room/invalidate`, data);
}

/**
 * 立即生效房源
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function immediateEffectRooms(data: { roomIds: string[] }) {
    return http.post(`${roomUrl}/room/immediateEffect`, data);
}

/**
 * 发起立项定价申请
 * @param data 申请信息
 * @returns Promise<boolean>
 */
export function submitProjectPricingApplication(data: ProjectPricingApplicationDTO) {
    return http.post(`${roomUrl}/projectPricing/submit`, data);
}

/**
 * 下载房源导入模板
 * @returns Promise<Blob>
 */
export function downloadRoomTemplate() {
    return http.get(`${roomUrl}/room/downloadTemplate`, {}, {
        responseType: 'blob',
    });
}

/**
 * 导入房源
 * @param data 导入信息
 * @returns Promise<boolean>
 */
export function importRooms(data: FormData) {
    return http.post(`${roomUrl}/room/import`, data, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
}

/**
 * 引入已接收资产
 * @param data 资产信息
 * @returns Promise<boolean>
 */
export function importReceivedAssets(data: ImportReceivedAssetDTO) {
    return http.post(`${roomUrl}/room/importReceivedAssets`, data);
}

/**
 * 获取已接收资产列表
 * @param data 查询参数
 * @returns Promise<any[]>
 */
export function getReceivedAssetList(data: { projectId?: string; parcelId?: string; buildingId?: string }) {
    return http.get(`${roomUrl}/asset/receivedList`, data);
}

/**
 * 获取资产构成统计信息
 * @param data 查询参数
 * @returns Promise<RoomStatisticsVo>
 */
export function getAssetCompositionStatistics(data: RoomStatisticsQueryDTO) {
    return http.post<RoomStatisticsVo>(`${roomUrl}/room/statistics`, data);
}

/**
 * 获取房态简图接口
 * @param data 查询参数
 * @returns Promise<FloorDiagramVo[]>
 */
export function getRoomSimpleDiagram(data: RoomDiagramQueryDTO) {
    return http.post<FloorDiagramVo[]>(`${roomUrl}/room/simple/diagram`, data);
}

/**
 * 待生效作废接口
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function cancelPendingRooms(data: string[]) {
    return http.post(`${roomUrl}/room/pending/cancel`, data);
}

/**
 * 生效中作废接口
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function cancelEffectiveRooms(data: string[]) {
    return http.post(`${roomUrl}/room/effect/cancel`, data);
}

/**
 * 定时生效接口
 * @returns Promise<boolean>
 */
export function cronEffectRooms() {
    return http.post(`${roomUrl}/room/cron/effect`);
}

/**
 * 获取房源变更历史接口
 * @param data 房源ID
 * @returns Promise<RoomChangeRecordVo[]>
 */
export interface RoomChangeRecordVo {
    id?: string;
    roomId?: string;
    changeType?: number;
    mergeSplitId?: string;
    mergeSplitType?: string;
    changeTime?: string;
    changeContent?: string;
    remark?: string;
    createByName?: string;
    updateByName?: string;
    isDel?: boolean;
}

export function getRoomChangeHistory(data: { roomId: string }) {
    return http.get<RoomChangeRecordVo[]>(`${roomUrl}/room/change/list`, data);
}

/**
 * 获取拆分详情接口
 * @param data 拆分ID
 * @returns Promise<RoomSplitAddDTO>
 */
export function getSplitDetail(data: { id: string }) {
    return http.get<RoomSplitAddDTO>(`${roomUrl}/room/split/detail`, data);
}

/**
 * 获取合并详情接口
 * @param data 合并ID
 * @returns Promise<RoomMergeAddDTO>
 */
export interface RoomMergeAddDTO {
    id?: string;
    applicationNo?: string;
    operationType?: number;
    areaId?: string;
    buildingId?: string;
    sourceRoomIds?: string[];
    sourceRooms?: RoomVo[];
    targetRoom?: RoomAddDTO;
    approvalStatus?: number;
    effectType?: number;
    effectDate?: string;
    attachments?: string | null;
    adjustmentReason?: string;
    remark?: string;
    submitType?: number;
    isDel?: boolean;
    createBy?: string;
    createByName?: string;
    createTime?: string;
    updateBy?: string;
    updateByName?: string;
    updateTime?: string;
}

export function getMergeDetail(data: { id: string }) {
    return http.get<RoomMergeAddDTO>(`${roomUrl}/room/merge/detail`, data);
}

/**
 * 删除拆分合并记录接口
 * @param data 拆分合并ID
 * @returns Promise<boolean>
 */
export function deleteMergeSplitRecord(data: { id: string }) {
    return http.delete(`${roomUrl}/room/mergeSplit/delete`, data);
}

/**
 * 房源引入已接收资产
 * @param data 房源信息
 * @returns Promise<boolean>
 */
export function introduceReceivedRooms(data: RoomAddDTO) {
    return http.post(`${roomUrl}/room/introduce`, data);
}

/**
 * 待生效作废接口
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function pendingCancelRooms(data: string[]) {
    return http.post(`${roomUrl}/room/pending/cancel`, data);
}

/**
 * 立即生效接口
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function quickEffectRooms(data: string[]) {
    return http.post(`${roomUrl}/room/quick/effect`, data);
}

/**
 * 房源合并接口
 * @param data 合并信息
 * @returns Promise<boolean>
 */
export function mergeRoom(data: RoomMergeAddDTO) {
    return http.post(`${roomUrl}/room/merge`, data);
}

/**
 * 房源拆分合并记录查询DTO
 */
export interface RoomMergeSplitQueryDTO {
    /** 房源名称 */
    roomName?: string;
    /** 调整类型（3拆分 4合并） */
    operationType?: number;
    /** 审批状态（0待提交 1审批中 2已通过 3已拒绝） */
    approvalStatus?: number;
    /** 创建日期开始 */
    createTimeStart?: string;
    /** 创建日期结束 */
    createTimeEnd?: string;
    /** 项目id */
    projectId?: string;
    /** 页码 */
    pageNum: number;
    /** 页大小 */
    pageSize: number;
    /** 地块ID */
    parcelId?: string;
    /** 楼栋ID */
    buildingId?: string;
}

/**
 * 房源拆分合并记录VO
 */
export interface RoomMergeSplitVo {
    /** 主键ID */
    id?: string;
    /** 申请编号 */
    applicationNo?: string;
    /** 操作类型（3拆分 4合并） */
    operationType?: number;
    /** 地块ID */
    areaId?: string;
    /** 地块名称 */
    areaName?: string;
    /** 楼栋ID */
    buildingId?: string;
    /** 楼栋名称 */
    buildingName?: string;
    /** 原房源id */
    sourceRoomId?: string;
    /** 原房源名称 */
    sourceRoomInfo?: string;
    /** 调整后房源id */
    targetRoomId?: string;
    /** 调整后房源名称 */
    targetRoomInfo?: string;
    /** 审批状态（0草稿 1待审批 2已审批 3驳回） */
    approvalStatus?: number;
    /** 生效方式（1立即生效 2到期生效） */
    effectType?: number;
    /** 生效日期 */
    effectDate?: string;
    /** 附件 */
    attachments?: string;
    /** 房间草稿信息 */
    roomInfo?: string;
    /** 调整原因 */
    adjustmentReason?: string;
    /** 备注 */
    remark?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 查询房源拆分合并记录列表
 * @param data 查询参数
 * @returns Promise<RoomMergeSplitVo[]>
 */
export function getRoomMergeSplitList(data: RoomMergeSplitQueryDTO) {
    return http.post<RoomMergeSplitVo[]>(`${roomUrl}/room/mergeSplit/list`, data);
}

/**
 * 生效中作废接口
 * @param data 房源ID列表
 * @returns Promise<boolean>
 */
export function effectCancelRooms(data: string[]) {
    return http.post(`${roomUrl}/room/effect/cancel`, data);
}

/**
 * 获取最大合同结束日期
 * @param data 房源ID列表
 * @returns Promise<string>
 */
export function getMaxContractEndDate(roomIds: string[]) {
    return http.post<string>(`${roomUrl}/room/mergeSplit/getMaxContractEndDate`, roomIds);
}

/**
 * 提交拆分
 * @param data 拆分信息
 * @param id 拆分记录ID
 * @returns Promise<boolean>
 */
export function submitSplitRoom(data: RoomSplitAddDTO, id?: string) {
    let url = `${roomUrl}/room/split/submit`;
    if (id) {
        url += `?id=${id}`;
    }
    return http.post(url, data);
}

/**
 * 提交合并
 * @param data 合并信息
 * @param id 合并记录ID
 * @returns Promise<boolean>
 */
export function submitMergeRoom(data: RoomMergeAddDTO, id?: string) {
    let url = `${roomUrl}/room/merge/submit`;
    if (id) {
        url += `?id=${id}`;
    }
    return http.post(url, data);
}

/**
 * 拆分审批接口
 * @param data 审批信息
 * @returns Promise<boolean>
 */
export function approveSplitRoom(data: { id: string, approveResult: string }) {
    return http.post(`${roomUrl}/room/split/approval`, data);
}

/**
 * 合并审批接口
 * @param data 审批信息
 * @returns Promise<boolean>
 */
export function approveMergeRoom(data: { id: string, approveResult: string }) {
    return http.post(`${roomUrl}/room/merge/approval`, data);
}

/**
 * 通过模版导入房源
 */
export function importRoomsFromTemplate(file: File){
    const formData = new FormData()
    formData.append('file', file)
    return http.post(`${roomUrl}/room/template/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
}

/**
 * 多批房源导入接口
 */
export function importMultipleRooms(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return http.post(`${roomUrl}/room/multiple/template/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
}

/**
 * 房源详情简图接口
 */
export function getRoomDetailDiagram(data: RoomDiagramQueryDTO) {
    return http.post<FloorDiagramVo[]>(`${roomUrl}/room/detail/diagram`, data)
}

/**
 * 下载房源 导入模板 租赁
 */
export function downloadRoomTemplateForLease(projectId: string) {
    return http.get(`${roomUrl}/room/template/download?projectId=${projectId}`, {}, {
        responseType: 'blob',
    });
}

/**
 * 下载多经导入模板
 */
export function downloadMultipleRoomTemplateForLease(projectId: string) {
    return http.get(`${roomUrl}/room/multiple/template/download?projectId=${projectId}`, {}, {
        responseType: 'blob',
    });
}

/**
 * 房间状态设置DTO
 */
export interface RoomConditionDTO {
    /** 房间ID */
    roomId: string;
    /** 是否锁房 */
    isLock: boolean;
    /** 是否脏房 */
    isDirty: boolean;
    /** 是否维修 */
    isMaintain: boolean;
}

/**
 * 设置房间状态
 * @param data 房间状态信息
 * @returns Promise<boolean>
 */
export function setRoomCondition(data: RoomConditionDTO) {
    return http.post(`${roomUrl}/room/setCondition`, data);
}
