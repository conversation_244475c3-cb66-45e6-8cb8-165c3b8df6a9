import http from './index'

const revenueUrl = '/business-rent-admin/revenue'
const customerUrl = '/business-rent-admin/customer'
const contractUrl = '/business-rent-admin/contract'

// 商户营收管理相关接口定义

/**
 * 商户营收新增/修改DTO
 */
export interface ContractRevenueAddDTO {
  id?: string
  projectId?: string
  contractId?: string
  contractUnionId?: string
  revenueMonth?: string
  revenueAmount?: number
  reportDate?: string
  attachment?: string
  isConfirm?: boolean
  confirmBy?: string
  confirmByName?: string
  confirmTime?: string
  isDel?: boolean
}

/**
 * 商户营收查询DTO
 */
export interface ContractRevenueQueryDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  id?: string
  projectId?: string
  contractId?: string
  contractUnionId?: string
  revenueMonth?: string
  revenueAmount?: number
  reportDate?: string
  attachment?: string
  isConfirm?: boolean
  confirmBy?: string
  confirmByName?: string
  confirmTime?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  isDel?: boolean
  contractNo?: string
  customerName?: string
  roomName?: string
  reportDateStart?: string
  reportDateEnd?: string
}

/**
 * 商户营收审批DTO
 */
export interface ContractRevenueApproveDTO {
  ids: string[]
  opinion?: string
}

/**
 * 商户营收VO
 */
export interface ContractRevenueVO {
  id?: string
  projectId?: string
  projectName?: string
  contractId?: string
  contractUnionId?: string
  contractNo?: string
  customerName?: string
  roomName?: string
  revenueMonth?: string
  revenueAmount?: number
  reportDate?: string
  attachment?: string
  isConfirm?: boolean
  confirmBy?: string
  confirmByName?: string
  confirmTime?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  isDel?: boolean
}

/**
 * 查询商户营收管理列表 (POST)
 */
export function getRevenueList(data: ContractRevenueQueryDTO) {
  return http.post(`${revenueUrl}/list`, data)
}

/**
 * 查询商户营收管理列表 (GET)
 */
export function getRevenueListByGet(params: any) {
  return http.get(`${revenueUrl}/list`, { params })
}

/**
 * 获取商户营收管理详细信息
 */
export function getRevenueDetail(id: string) {
  return http.get(`${revenueUrl}/detail`, { id  })
}

/**
 * 新增商户营收管理
 */
export function addRevenue(data: ContractRevenueAddDTO) {
  return http.post(`${revenueUrl}/save`, data)
}

/**
 * 修改商户营收管理
 */
export function updateRevenue(data: ContractRevenueAddDTO) {
  return http.put(`${revenueUrl}`, data)
}

/**
 * 保存营收
 */
export function saveRevenue(data: ContractRevenueAddDTO) {
  return http.post(`${revenueUrl}/save`, data)
}

/**
 * 删除商户营收管理
 */
export function deleteRevenue(ids: string[]) {
  return http.delete(`${revenueUrl}/delete`, { params: { ids } })
}

/**
 * 删除商户营收管理接口 (单个)
 */
export function deleteRevenueById(id: string) {
  return http.delete(`${revenueUrl}/delete/${id}`)
}

/**
 * 商户营收管理审批
 */
export function approveRevenue(data: ContractRevenueApproveDTO) {
  return http.post(`${revenueUrl}/approve`, data)
}

/**
 * 取消审批
 */
export function cancelApproveRevenue(id: string) {
  return http.post(`${revenueUrl}/cancelApprove?id=${id}`)
}

/**
 * 导出商户营收管理列表
 */
export function exportRevenueList(params: any) {
  return http.post(`${revenueUrl}/export`, {}, { params })
}

/**
 * 下载商户营收管理导入模板
 */
export function downloadRevenueTemplate(projectId: string) {
  return http.downloadForGet(`${revenueUrl}/template/download`, { projectId  })
}

/**
 * 通过模版导入商户营收管理
 */
export function importRevenueByTemplate(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return http.post(`${revenueUrl}/template/import`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 新增接口定义

/**
 * 客户查询DTO
 */
export interface CustomerQueryDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  id?: string
  projectId?: string
  customerType?: number
  customerName?: string
  ownerName?: string
  createByName?: string
  creditCode?: string
}

/**
 * 客户VO
 */
export interface CustomerVO {
  id?: string
  projectId?: string
  projectName?: string
  customerName?: string
  customerType?: number
  creditCode?: string
  legalName?: string
  contactPhone?: string
  idType?: string
  idNumber?: string
  idValidityStart?: string
  idValidityEnd?: string
  idFront?: string
  idBack?: string
  businessLicense?: string
  contactAddress?: string
  ownerId?: string
  ownerName?: string
  attachmentFiles?: string
  remark?: string
  createByName?: string
  updateTime?: string
  isDel?: boolean
}

/**
 * 合同选项VO
 */
export interface ContractOptionVO {
  id?: string
  unionId?: string
  contractNo?: string
  roomName?: string
  status?: number
  startDate?: string
  endDate?: string
}

/**
 * 客户列表查询接口
 */
export function getCustomerList(data: CustomerQueryDTO) {
  return http.post(`${customerUrl}/list`, data)
}

/**
 * 获取合同下拉列表选项
 */
export function getContractOptions(projectId: string, customerId: string) {
  return http.get(`${contractUrl}/contractOption`, {
    // params: {
      projectId,
      customerId
    // }
  })
} 