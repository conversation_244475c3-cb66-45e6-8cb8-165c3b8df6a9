import { mergeConfig } from 'vite';
import baseConfig from './vite.config.base';
import configTerserPlugin from './plugin/terser';
import configPerformancePlugin from './plugin/performance';

export default mergeConfig(
    {
        mode: 'production',
        build: {
            // 快速构建优化
            ...configTerserPlugin({
                dropConsole: true,
                dropDebugger: true,
                removeComments: true,
                parallel: true,
                sourcemap: false,
            }),
            // 跳过类型检查以提高速度
            emptyOutDir: true,
            // 减少文件输出大小检查
            reportCompressedSize: false,
            // 设置更大的 chunk 警告限制
            chunkSizeWarningLimit: 3000,
            // 优化构建性能
            rollupOptions: {
                output: {
                    // 使用更简单的哈希算法
                    entryFileNames: 'assets/[name]-[hash:8].js',
                    chunkFileNames: 'assets/[name]-[hash:8].js',
                    assetFileNames: 'assets/[name]-[hash:8].[ext]',
                    // 更激进的代码分割
                    manualChunks: {
                        // 核心依赖
                        vendor: ['vue', 'vue-router', 'pinia'],
                        // UI 库
                        ui: ['@arco-design/web-vue'],
                        // 图表库
                        charts: ['echarts', 'vue-echarts'],
                        // 工具库
                        utils: ['lodash', 'dayjs', 'axios'],
                    },
                },
            },
        },
        // 增强性能配置
        ...configPerformancePlugin({
            enableCache: true,
            enableMultiThread: true,
            maxMemory: 8192,
            enableFsCache: true,
        }),
    },
    baseConfig
); 