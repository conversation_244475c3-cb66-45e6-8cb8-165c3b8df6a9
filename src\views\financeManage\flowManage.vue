<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form :model="formModel" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }"
                        label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="projectId" label="项目">
                                    <ProjectTreeSelect v-model="formModel.projectId" @change="handleProjectChange" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="status" label="流水状态">
                                    <a-select v-model="formModel.status" placeholder="请选择流水状态" allow-clear>
                                        <a-option :value="0">未记账</a-option>
                                        <a-option :value="1">部分记账</a-option>
                                        <a-option :value="2">已记账</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="payDirection" label="交易方向">
                                    <a-select v-model="formModel.payDirection" placeholder="请选择交易方向" allow-clear>
                                        <a-option :value="0">收入</a-option>
                                        <a-option :value="1">支出</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="payType" label="支付类型">
                                    <a-select v-model="formModel.payType" placeholder="请选择支付类型" allow-clear>
                                        <a-option :value="0">线上</a-option>
                                        <a-option :value="1">线下</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="payMethod" label="支付方式">
                                    <a-select v-model="formModel.payMethod" placeholder="请选择支付方式" allow-clear>
                                        <a-option v-for="item in paymentMethodOptions" :key="item.value"
                                            :value="item.value">
                                            {{ item.label }}
                                        </a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="targetType" label="支付对象类型">
                                    <a-select v-model="formModel.targetType" placeholder="请选择支付对象类型" allow-clear>
                                        <a-option :value="1">园区</a-option>
                                        <a-option :value="2">地块</a-option>
                                        <a-option :value="3">楼栋</a-option>
                                        <a-option :value="4">房间</a-option>
                                        <a-option :value="5">账单支付</a-option>
                                        <a-option :value="6">银行转账</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <template v-if="advancedSearchVisible">
                                <a-col :span="8">
                                    <a-form-item field="target" label="支付对象">
                                        <a-input v-model="formModel.target" placeholder="请输入支付对象" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="payerName" label="支付人名称">
                                        <a-input v-model="formModel.payerName" placeholder="请输入支付人名称" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="payerPhone" label="支付人手机号">
                                        <a-input v-model="formModel.payerPhone" placeholder="请输入支付人手机号" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="orderNo" label="订单号">
                                        <a-input v-model="formModel.orderNo" placeholder="请输入订单号" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="payerAccount" label="支付人账号">
                                        <a-input v-model="formModel.payerAccount" placeholder="请输入支付人账号" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="merchant" label="收款商户">
                                        <a-input v-model="formModel.merchant" placeholder="请输入收款商户" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="entryTimeRange" label="入账时间">
                                        <a-range-picker v-model="formModel.entryTimeRange" style="width: 100%"
                                            value-format="YYYY-MM-DD HH:mm:ss" show-time />
                                    </a-form-item>
                                </a-col>
                            </template>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-row>
                <AdvancedSearch @toggle="handleToggle" />
            </a-row>
            <!-- 状态-表头操作区 -->
            <div class="table-header">
                <a-tabs v-model:activeKey="activeStatus" type="rounded" hide-content @change="handleChangeStatus">
                    <a-tab-pane v-for="item in statusOptions" :key="item.value" :title="item.label" />
                    <template #extra>
                        <a-space>
                            <a-button v-permission="['rent:flow:export']" type="primary" @click="handleExport">导出</a-button>
                        </a-space>
                    </template>
                </a-tabs>
            </div>

            <!-- 表格区域 -->
            <a-table row-key="id" :loading="loading" :pagination="pagination" :columns="columns" :data="tableData" :scroll="{ x: 1 }"
                :bordered="{ cell: true }" @page-change="onPageChange" @page-size-change="onPageSizeChange">
                <template #index="{ rowIndex }">
                    {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
                </template>
                <template #status="{ record }">
                    <a-space>
                        <!-- <a-tag v-if="record?.isOtherIncome" color="orange">其他收入</a-tag> -->
                        <a-tag :color="
                            record?.status === 0 ? 'red' :
                            record?.status === 1 ? 'orange' :
                            record?.status === 2 ? 'green' : 'gray'
                        ">
                            {{
                                record?.status === 0 ? '未记账' :
                                record?.status === 1 ? '部分记账' :
                                record?.status === 2 ? '已记账' : '未知'
                            }}
                        </a-tag>
                    </a-space>
                </template>
                
                <template #payDirection="{ record }">
                    <span>
                        {{
                            record?.payDirection === 0 ? '收入' :
                            record?.payDirection === 1 ? '支出' : ''
                        }}
                    </span>
                </template>
                
                <template #payType="{ record }">
                    <span>
                        {{
                            record?.payType === 0 ? '线上' :
                            record?.payType === 1 ? '线下' : ''
                        }}
                    </span>
                </template>
                
                <!-- <template #payMethod="{ record }">
                    <span>
                        {{
                            record?.payMethod === 1 ? '微信' :
                            record?.payMethod === 2 ? '支付宝' :
                            record?.payMethod === 3 ? '银行卡' :
                            record?.payMethod === 4 ? '现金' : ''
                        }}
                    </span>
                </template> -->
                
                <template #targetType="{ record }">
                    <span>
                        {{
                            record?.targetType === 1 ? '园区' :
                            record?.targetType === 2 ? '地块' :
                            record?.targetType === 3 ? '楼栋' :
                            record?.targetType === 4 ? '房间' :
                            record?.targetType === 5 ? '账单支付' :
                            record?.targetType === 6 ? '银行转账' : ''
                        }}
                    </span>
                </template>
                
                <template #amount="{ record }">
                    <span>{{ record?.amount !== undefined ? formatAmount(record.amount) : '' }}</span>
                </template>
                
                <template #usedAmount="{ record }">
                    <span>{{ record?.usedAmount !== undefined ? formatAmount(record.usedAmount) : '' }}</span>
                </template>
                
                <template #remainAmount="{ record }">
                    <span>{{ record?.amount && record?.usedAmount ? formatAmount(record.amount - record.usedAmount) : '' }}</span>
                </template>
                
                <template #operations="{ record }">
                    <a-space>
                        <!-- 查看详情 - 所有页签都显示 -->
                        <!-- <a-button type="text" size="mini" @click="handleViewDetail(record)">
                            详情
                        </a-button> -->
                        
                        <!-- 查看记账记录 - 全部、未明流水页签，已记账、部分记账状态 -->
                        <a-button v-permission="['rent:flow:detail']" v-if="showViewRecordButton(record)" 
                            type="text" size="mini" @click="handleViewRecord(record)">
                            查看记账记录
                        </a-button>
                        
                        <!-- 记账记录 - 全部、未明流水页签，已记账、部分记账状态 -->
                        <a-button v-permission="['rent:flow:usage']" v-if="showUsageButton(record)" 
                            type="text" size="mini" @click="handleViewUsage(record)">
                            使用记录
                        </a-button>
                        
                        <!-- 记账 - 全部、未明流水页签，未记账、部分记账状态 -->
                        <a-button v-permission="['rent:flow:saveRecord']" v-if="showAccountButton(record)" 
                            type="text" size="mini" @click="handleAccount(record)">
                            记账
                        </a-button>
                        
                        <!-- 其他收入 - 全部、未明流水页签，未记账、部分记账状态，且未标记为其他收入 -->
                        <a-button v-permission="['rent:flow:markOtherIncome']" v-if="showOtherIncomeButton(record)" 
                            type="text" size="mini" @click="handleMarkAsOtherIncome(record)">
                            其他收入
                        </a-button>
                        
                        <!-- 取消其他收入 - 其他收入页签，未记账、部分记账状态 -->
                        <a-button v-permission="['rent:flow:markOtherIncome']" v-if="showCancelOtherIncomeButton(record)" 
                            type="text" size="mini" @click="handleCancelOtherIncome(record)">
                            取消其他收入
                        </a-button>
                        
                        <!-- 退款 - 全部、未明流水页签，未记账、部分记账状态 -->
                        <a-button v-permission="['rent:flow:refund']" v-if="showRefundButton(record)" 
                            type="text" size="mini" @click="handleRefund(record)">
                            退款
                        </a-button>
                    </a-space>
                </template>
            </a-table>
        </a-card>

        <!-- 流水详情抽屉 -->
        <a-drawer v-model:visible="flowDrawerVisible" title="流水详情" width="1200px" @cancel="handleFlowCancel">
            <flow-detail v-if="flowDrawerVisible" :data="currentFlowData" />
        </a-drawer>



        <!-- 记账组件 -->
        <account-modal ref="accountModalRef" :data="currentAccountData" @cancel="handleAccountCancel"
            @save="handleAccountSave" />

        <!-- 流水记账组件 -->
        <flow-account-modal ref="flowAccountModalRef" :data="currentFlowAccountData" @cancel="handleFlowAccountCancel"
            @save="handleFlowAccountSave" />

        <!-- 其他收入弹窗 -->
        <a-modal v-model:visible="otherIncomeVisible" title="其他收入" width="400px" 
            @ok="handleOtherIncomeConfirm" @cancel="handleOtherIncomeCancel">
            <p><icon-exclamation-circle-fill style="color: #FF9900;font-size: 16px;" /> 是否将此流水标记为其他收入？</p>
            <a-form>
                <a-form-item label="说明" required>
                    <a-textarea v-model="otherIncomeRemark" placeholder="请输入说明" :max-length="100" show-word-limit allow-clear />
                </a-form-item>
            </a-form>
        </a-modal>

        <!-- 使用记录弹窗 -->
        <a-modal v-model:visible="usageRecordVisible" title="使用记录" width="1200px" 
            @cancel="handleUsageRecordCancel" :footer="false">
            <FlowUsageRecord 
                v-if="usageRecordVisible"
                :flow-id="currentUsageFlowId"
                :usage-data="usageRecordData"
                :refund-data="refundRecordData"
                @take-record="handleTakeRecord"
                @confirm="handleConfirmRecord"
            />
        </a-modal>

        <!-- 退款申请抽屉 -->
        <FlowRefundApplyForm ref="refundFormRef" />

    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import dayjs from 'dayjs'
import { Message } from '@arco-design/web-vue'
import FlowDetail from './components/flowDetail.vue'
import AccountModal from './components/accountModal.vue'
import FlowAccountModal from './components/flowAccountModal.vue'
import FlowUsageRecord from './components/FlowUsageRecord/index.vue'
import AdvancedSearch from '@/components/advancedSearch/index.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import FlowRefundApplyForm from './components/flowRefundApplyForm.vue'
import { 
    getFinancialFlowList, 
    exportFinancialFlowList,
    getFinancialFlowDetail,
    markFinancialFlowAsOtherIncome,
    cancelFinancialFlowOtherIncome,
    getFinancialFlowUsage,
    saveFinancialFlowRecord,
    createFinancialRefund,
    confirmFlowRecord,
    type FinancialFlowQueryDTO,
    type FinancialFlowVo,
    type FinancialFlowMarkOtherIncomeDTO,
    type FinancialFlowSaveRecordDTO,
    type FlowConfirmRecordDTO,
    FlowStatus,
    PayDirection,
    TargetType
} from '@/api/flowManage'
import { cancelCostRecord } from '@/api/billManage'
import type { UsageRecord, RefundRecord } from './components/FlowUsageRecord/index.vue'

const activeStatus = ref<string>('1') // 默认显示未明流水
//searchType 1-未明流水 2-其他收入
const statusOptions = [
    { label: '全部', value: '' },
    { label: '未明流水', value: '1' },
    { label: '其他收入', value: '2' },
]

// 支付方式选项
const paymentMethodOptions = ref([
    { label: '微信', value: 1 },
    { label: '支付宝', value: 2 },
    { label: '银行卡', value: 3 },
    { label: '现金', value: 4 },
])

// 处理项目选择变化
const handleProjectChange = (value: string) => {
    formModel.projectId = value
        // 重置分页并重新查询
        pagination.current = 1
        formModel.pageNum = 1
        fetchData()
}

// 默认日期范围（近3个月）
const defaultDateRange = [
    dayjs().subtract(3, 'month').startOf('day'),
    dayjs().endOf('day')
]

const formModel = reactive<Partial<FinancialFlowQueryDTO> & { entryTimeRange: string[] }>({
    projectId: '',
    orderNo: '',
    status: undefined,
    payDirection: 0, // 默认选择收入
    payType: undefined,
    payMethod: undefined,
    targetType: undefined,
    target: '',
    payerName: '',
    payerPhone: '',
    payerAccount: '',
    merchant: '',
    entryTimeRange: [],
    pageNum: 1,
    pageSize: 10
})

const loading = ref(false)
const tableData = ref<FinancialFlowVo[]>([])

const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
})

const columns = [
{ title: '序号', dataIndex: 'index', slotName: 'index', width: 70, align: 'center', ellipsis: true, tooltip: true },

    {
        title: '状态',
        dataIndex: 'status',
        slotName: 'status',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '项目名称',
        dataIndex: 'projectName',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '订单号',
        dataIndex: 'orderNo',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '交易方向',
        dataIndex: 'payDirection',
        slotName: 'payDirection',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '支付类型',
        dataIndex: 'payType',
        slotName: 'payType',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '支付方式',
        dataIndex: 'payMethod',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '支付对象类型',
        dataIndex: 'targetType',
        slotName: 'targetType',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '支付对象',
        dataIndex: 'target',
        width: 150,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '入账/支出时间',
        dataIndex: 'entryTime',
        width: 180,        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '交易金额',
        dataIndex: 'amount',
        slotName: 'amount',
        width: 120,        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '已使用金额',
        dataIndex: 'usedAmount',
        slotName: 'usedAmount',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '剩余金额',
        dataIndex: 'remainAmount',
        slotName: 'remainAmount',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '对方姓名',
        dataIndex: 'payerName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '对方手机号',
        dataIndex: 'payerPhone',
        width: 130,
        align: 'center'
    },
    {
        title: '对方账号',
        dataIndex: 'payerAccount',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '支付备注',
        dataIndex: 'payRemark',
        width: 150,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '本方商户',
        dataIndex: 'merchant',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '其他收入说明',
        dataIndex: 'otherIncomeDesc',
        width: 150,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    // {
    //     title: '收款渠道',
    //     dataIndex: 'payChannel',
    //     width: 120,
    //     ellipsis: true,
    //     tooltip: true,
    //     align: 'center'
    // },
    {
        title: '操作',
        dataIndex: 'operations',
        slotName: 'operations',
        width: 420,
        fixed: 'right',
        align: 'center'
    }
]

// 高级搜索控制
const advancedSearchVisible = ref(false)

// 高级搜索
const handleToggle = (visible: boolean) => {
    advancedSearchVisible.value = visible
}

// 状态切换
const handleChangeStatus = (key: string) => {
    console.log('搜索类型切换:', key)
    activeStatus.value = key
    // 重置分页
    pagination.current = 1
    formModel.pageNum = 1
    // 根据页签重新获取数据
    fetchData()
}

// 导出
const handleExport = async () => {
    try {
        const params = buildQueryParams()
        await exportFinancialFlowList(params)
        Message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
    }
}

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 构建查询参数
const buildQueryParams = (): FinancialFlowQueryDTO => {
    const params: FinancialFlowQueryDTO = {
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        projectId: formModel.projectId || undefined,
        orderNo: formModel.orderNo || undefined,
        status: formModel.status,
        payDirection: formModel.payDirection,
        payType: formModel.payType,
        payMethod: formModel.payMethod,
        targetType: formModel.targetType,
        target: formModel.target || undefined,
        payerName: formModel.payerName || undefined,
        payerPhone: formModel.payerPhone || undefined,
        payerAccount: formModel.payerAccount || undefined,
        merchant: formModel.merchant || undefined,
        searchType: activeStatus.value ? Number(activeStatus.value) : undefined
    }

    // 处理时间范围
    if (formModel.entryTimeRange && formModel.entryTimeRange.length === 2) {
        params.entryTimeStart = formModel.entryTimeRange[0]
        params.entryTimeEnd = formModel.entryTimeRange[1]
    }

    return params
}

// 查询
const search = () => {
    pagination.current = 1
    formModel.pageNum = 1
    fetchData()
}

// 重置
const reset = () => {
    Object.assign(formModel, {
        projectId: '',
        orderNo: '',
        status: undefined,
        payDirection: 0, // 重置时也默认选择收入
        payType: undefined,
        payMethod: undefined,
        targetType: undefined,
        target: '',
        payerName: '',
        payerPhone: '',
        payerAccount: '',
        merchant: '',
        entryTimeRange: [],
        pageNum: 1,
        pageSize: 10
    })
    pagination.current = 1
    fetchData()
}

// 分页
const onPageChange = (current: number) => {
    pagination.current = current
    formModel.pageNum = current
    fetchData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    formModel.pageSize = pageSize
    pagination.current = 1
    formModel.pageNum = 1
    fetchData()
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = buildQueryParams()
        const response = await getFinancialFlowList(params)
        
        if (response.rows) {
            tableData.value = response.rows || []
            pagination.total = response.total || 0
        }
    } catch (error) {
        console.error('获取数据失败:', error)
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}



// 流水详情抽屉控制
const flowDrawerVisible = ref(false)
const currentFlowData = reactive({
    projectName: '',
    contractNo: '',
    tenantName: '',
    billType: '',
    billPeriod: '',
    dueDate: '',
    totalAmount: 0,
    discountAmount: 0,
    actualAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0,
    flowList: [],
    flowLogList: [],
    costId: ''
})

// 查看详情
const handleViewDetail = async (record: FinancialFlowVo) => {
    try {
        if (record.id) {
            const response = await getFinancialFlowDetail(record.id)
            if (response.data) {
                // 将财务流水数据适配为FlowDetailData格式
                Object.assign(currentFlowData, {
                    projectName: response.data.projectName || '',
                    contractNo: response.data.orderNo || '', // 使用订单号作为合同号
                    tenantName: response.data.payerName || '', // 使用支付人姓名作为承租方
                    billType: getPayMethodText(response.data.payMethod) || '', // 使用支付方式作为账单类型
                    billPeriod: '', // 财务流水没有账单周期概念
                    dueDate: response.data.entryTime || '', // 使用入账时间作为应收日期
                    totalAmount: response.data.amount || 0,
                    discountAmount: 0, // 财务流水没有优惠金额概念
                    actualAmount: response.data.amount || 0,
                    paidAmount: response.data.usedAmount || 0,
                    unpaidAmount: (response.data.amount || 0) - (response.data.usedAmount || 0),
                    flowList: [], // TODO: 如果需要显示相关流水记录，需要调用其他接口
                    flowLogList: [], // TODO: 如果需要显示流水日志，需要调用其他接口
                    costId: response.data.id || ''
                })
                flowDrawerVisible.value = true
            }
        }
    } catch (error) {
        console.error('获取流水详情失败:', error)
    }
}

// 获取支付方式文本
const getPayMethodText = (payMethod: number | undefined) => {
    const methodMap = new Map([
        [1, '微信'],
        [2, '支付宝'],
        [3, '银行卡'],
        [4, '现金']
    ])
    return methodMap.get(payMethod || 0) || ''
}

// 关闭流水详情抽屉
const handleFlowCancel = () => {
    flowDrawerVisible.value = false
}



// 记账组件引用和数据
const accountModalRef = ref()
const currentAccountData = reactive<FinancialFlowVo>({})

// 流水记账组件引用和数据
const flowAccountModalRef = ref()
const currentFlowAccountData = reactive<FinancialFlowVo>({})

// 点击记账按钮
const handleAccount = (record: FinancialFlowVo) => {
    Object.assign(currentFlowAccountData, record)
    flowAccountModalRef.value?.open()
}

// 关闭记账组件
const handleAccountCancel = () => {
    // 组件内部已经处理了关闭逻辑
}

// 保存记账
const handleAccountSave = async () => {
    try {
        // TODO: 调用记账接口
        Message.success('记账成功')
        fetchData()
    } catch (error) {
        console.error('记账失败:', error)
    }
}

// 流水记账相关处理函数
const handleFlowAccountCancel = () => {
    // 组件内部已经处理了关闭逻辑
}

const handleFlowAccountSave = async () => {
    try {
        Message.success('流水记账成功')
        fetchData()
    } catch (error) {
        console.error('流水记账失败:', error)
    }
}

// 其他收入相关
const otherIncomeVisible = ref(false)
const otherIncomeRemark = ref('')
const currentOtherIncomeRecord = ref<FinancialFlowVo | null>(null)

// 使用记录相关
const usageRecordVisible = ref(false)
const currentUsageFlowId = ref('')
const usageRecordData = ref<UsageRecord[]>([])
const refundRecordData = ref<RefundRecord[]>([])

// 数据适配公共函数
const adaptUsageRecordData = (apiData: any) => {
    let usageRecords: UsageRecord[] = []
    let refundRecords: RefundRecord[] = []
    
    // 处理使用记录列表 (recordList)
    if (apiData.recordList && Array.isArray(apiData.recordList)) {
        usageRecords = apiData.recordList.map((item: any) => {
            // 获取账单类型文本
            const getOrderTypeText = (costType: number) => {
                const typeMap: Record<number, string> = {
                    1: '租金',
                    2: '其他费用',
                    3: '保证金',
                    4: '定金'
                }
                return typeMap[costType] || '未知'
            }

            // 获取收费类型文本
            const getChargeTypeText = (chargeType: number) => {
                const typeMap: Record<number, string> = {
                    1: '合同收费',
                    2: '临时收费'
                }
                return typeMap[chargeType] || '未知'
            }

            // 获取确认状态文本和类型
            const getConfirmInfo = (confirmStatus: number) => {
                const statusMap: Record<number, { text: string, type: string }> = {
                    0: { text: '待确认', type: 'manual_confirm' },
                    1: { text: '已确认', type: 'auto_confirm' },
                    2: { text: '系统自动', type: 'system_auto' }
                }
                return statusMap[confirmStatus] || { text: '未知', type: 'manual_confirm' }
            }

            const confirmInfo = getConfirmInfo(item.confirmStatus)

            return {
                id: item.costFlowRelId || '',
                projectName: item.projectName || '',
                accountType: getChargeTypeText(item.chargeType),
                orderNo: item.bizNo || '',
                roomUnit: item.roomName || '',
                customerName: item.customerName || '',
                orderType: getOrderTypeText(item.costType),
                orderPeriod: item.startDate && item.endDate ? `${item.startDate} ~ ${item.endDate}` : '',
                type: item.type === 1 ? '记账（手动）' : item.type === 2 ? '记账（自动）' : '记账',
                recordAmount: item.acctAmount || 0,
                recordTime: item.createTime || '',
                recorder: item.createByName || '',
                confirmStatus: confirmInfo.type,
                confirmer: item.confirmUserName || '',
                confirmTime: item.confirmTime || ''
            }
        })
    }

    // 处理退款记录列表 (refundRecordList)
    if (apiData.refundRecordList && Array.isArray(apiData.refundRecordList)) {
        refundRecords = apiData.refundRecordList.map((item: any) => {
            // 获取确认状态文本和类型
            const getConfirmInfo = (confirmStatus: number) => {
                const statusMap: Record<number, { text: string, type: string }> = {
                    0: { text: '待确认', type: 'manual_confirm' },
                    1: { text: '已确认', type: 'auto_confirm' },
                    2: { text: '系统自动', type: 'system_auto' }
                }
                return statusMap[confirmStatus] || { text: '未知', type: 'manual_confirm' }
            }

            const confirmInfo = getConfirmInfo(item.confirmStatus)

            return {
                id: item.costFlowRelId || '',
                refundType: item.refundType || '流水退款',
                refundNo: item.refundNo || '',
                refundTarget: item.refundTarget || '',
                refundApplyTime: item.applyTime || '',
                refundApplicant: item.createByName || '',
                auditPassTime: item.approveTime || '',
                type: item.type === 1 ? '记账（手动）' : item.type === 2 ? '记账（自动）' : '记账',
                recordAmount: item.acctAmount || 0,
                recordTime: item.recordTime || '',
                recorder: item.recordByName || '',
                confirmStatus: confirmInfo.type,
                confirmer: item.confirmUserName || '',
                confirmTime: item.confirmTime || ''
            }
        })
    }
    
    return { usageRecords, refundRecords }
}

// 标记为其他收入
const handleMarkAsOtherIncome = (record: FinancialFlowVo) => {
    currentOtherIncomeRecord.value = record
    otherIncomeVisible.value = true
}

// 确认标记为其他收入
const handleOtherIncomeConfirm = async () => {
    if (!otherIncomeRemark.value.trim()) {
        Message.error('请输入说明')
        return
    }
    
    try {
        if (currentOtherIncomeRecord.value?.id) {
            const data: FinancialFlowMarkOtherIncomeDTO = {
                flowId: currentOtherIncomeRecord.value.id,
                otherIncomeDesc: otherIncomeRemark.value
            }
            await markFinancialFlowAsOtherIncome(data)
            Message.success('已标记为其他收入')
            otherIncomeVisible.value = false
            otherIncomeRemark.value = ''
            fetchData()
        }
    } catch (error) {
        console.error('标记为其他收入失败:', error)
    }
}

// 取消标记为其他收入
const handleOtherIncomeCancel = () => {
    otherIncomeVisible.value = false
    otherIncomeRemark.value = ''
    currentOtherIncomeRecord.value = null
}

// 取消其他收入标记
const handleCancelOtherIncome = async (record: FinancialFlowVo) => {
    try {
        if (record.id) {
            await cancelFinancialFlowOtherIncome(record.id)
            Message.success('已取消其他收入标记')
            fetchData()
        }
    } catch (error) {
        console.error('取消其他收入标记失败:', error)
    }
}

// 处理退款
const handleRefund = async (record: FinancialFlowVo) => {
    if (!record.id) {
        Message.error('流水ID不存在')
        return
    }

    try {
        // 打开退款申请表单
        refundFormRef.value?.open({
            refundType: 2, // 未明流水退款
            refundId: record.refundId || undefined,
            bizId: record.id,
            flowData: record // 传递流水数据用于表单初始化
        })
    } catch (error) {
        console.error('打开退款申请失败:', error)
        // Message.error('打开退款申请失败')
    }
}

// 按钮显示逻辑
// 查看记账记录按钮 - 全部、未明流水页签，已记账、部分记账状态
const showViewRecordButton = (record: FinancialFlowVo) => {
    if (!record) return false
    // 页签条件：全部('') 或 未明流水('1')
    const isValidTab = activeStatus.value === '' || activeStatus.value === '1'
    // 状态条件：已记账(2) 或 部分记账(1)
    const isValidStatus = record.status === 1 || record.status === 2
    return isValidTab && isValidStatus
}

// 记账记录按钮 - 全部、未明流水页签，已记账、部分记账状态
const showUsageButton = (record: FinancialFlowVo) => {
    if (!record) return false
    // 页签条件：全部('') 或 未明流水('1')
    const isValidTab = activeStatus.value === '' || activeStatus.value === '1'
    // 状态条件：已记账(2) 或 部分记账(1)
    const isValidStatus = record.status === 1 || record.status === 2
    return isValidTab && isValidStatus
}

// 记账按钮 - 全部、未明流水页签，未记账、部分记账状态
const showAccountButton = (record: FinancialFlowVo) => {
    if (!record) return false
    // 页签条件：全部('') 或 未明流水('1')
    const isValidTab = activeStatus.value === '' || activeStatus.value === '1'
    // 状态条件：未记账(0) 或 部分记账(1)
    const isValidStatus = record.status === 0 || record.status === 1
    return isValidTab && isValidStatus
}

// 其他收入按钮 - 全部、未明流水页签，未记账、部分记账状态，且未标记为其他收入
const showOtherIncomeButton = (record: FinancialFlowVo) => {
    if (!record) return false
    // 页签条件：全部('') 或 未明流水('1')
    const isValidTab = activeStatus.value === '' || activeStatus.value === '1'
    // 状态条件：未记账(0) 或 部分记账(1)
    const isValidStatus = record.status === 0 || record.status === 1
    // 未标记为其他收入
    const isNotOtherIncome = !record.isOtherIncome
    return isValidTab && isValidStatus && isNotOtherIncome
}

// 取消其他收入按钮 - 其他收入页签，未记账、部分记账状态
const showCancelOtherIncomeButton = (record: FinancialFlowVo) => {
    if (!record) return false
    // 页签条件：其他收入('2')
    const isValidTab = activeStatus.value === '2'
    // 状态条件：未记账(0) 或 部分记账(1)
    const isValidStatus = record.status === 0 || record.status === 1
    return isValidTab && isValidStatus
}

// 退款按钮 - 全部、未明流水页签，未记账、部分记账状态
const showRefundButton = (record: FinancialFlowVo) => {
    if (!record) return false
    // 页签条件：全部('') 或 未明流水('1')
    const isValidTab = activeStatus.value === '' || activeStatus.value === '1'
    // 状态条件：未记账(0) 或 部分记账(1)
    const isValidStatus = record.status === 0 || record.status === 1
    return isValidTab && isValidStatus
}

// 查看记账记录
const handleViewRecord = async (record: FinancialFlowVo) => {
    try {
        // 清理之前的数据，确保没有污染
        Object.keys(currentFlowAccountData).forEach(key => delete (currentFlowAccountData as any)[key])
        // 打开流水记账组件，但设置为查看模式
        Object.assign(currentFlowAccountData, { ...record, viewMode: true })
        flowAccountModalRef.value?.open()
    } catch (error) {
        console.error('查看记账记录失败:', error)
        Message.error('查看记账记录失败')
    }
}

// 查看使用记录
const handleViewUsage = async (record: FinancialFlowVo) => {
    try {
        if (record.id) {
            currentUsageFlowId.value = record.id
            const response = await getFinancialFlowUsage(record.id)
            if (response.data) {
                const { usageRecords, refundRecords } = adaptUsageRecordData(response.data)
                usageRecordData.value = usageRecords
                refundRecordData.value = refundRecords
                usageRecordVisible.value = true
            }
        }
    } catch (error) {
        console.error('获取使用记录失败:', error)
        Message.error('获取使用记录失败')
    }
}

// 关闭使用记录弹窗
const handleUsageRecordCancel = () => {
    usageRecordVisible.value = false
    currentUsageFlowId.value = ''
    usageRecordData.value = []
    refundRecordData.value = []
}

// 处理取消记账
const handleTakeRecord = async (record: UsageRecord | RefundRecord) => {
    try {
        if (!record.id) {
            Message.error('记账记录ID不存在')
            return
        }

        // 调用取消记账API
        await cancelCostRecord(record.id)

        Message.success('取消记账成功')
        
        // 重新获取使用记录数据
        if (currentUsageFlowId.value) {
            const response = await getFinancialFlowUsage(currentUsageFlowId.value)
            if (response.data) {
                const { usageRecords, refundRecords } = adaptUsageRecordData(response.data)
                usageRecordData.value = usageRecords
                refundRecordData.value = refundRecords
            }
        }
        
        // 刷新流水列表
        fetchData()
    } catch (error) {
        console.error('取消记账失败:', error)
        Message.error('取消记账失败')
    }
}

// 处理确认
const handleConfirmRecord = async (record: UsageRecord) => {
    try {
        if (!currentUsageFlowId.value || !record.id) {
            Message.error('数据不完整')
            return
        }

        // 调用确认流水API
        const confirmData: FlowConfirmRecordDTO = {
            flowId: currentUsageFlowId.value,
            isOneKeyConfirm: false,
            flowRelId: record.id
        }

        await confirmFlowRecord(confirmData)

        Message.success('确认成功')
        
        // 重新获取使用记录数据
        const response = await getFinancialFlowUsage(currentUsageFlowId.value)
        if (response.data) {
            const { usageRecords, refundRecords } = adaptUsageRecordData(response.data)
            usageRecordData.value = usageRecords
            refundRecordData.value = refundRecords
        }
        
        // 刷新流水列表
        fetchData()
    } catch (error) {
        console.error('确认失败:', error)
        Message.error('确认失败')
    }
}

const refundFormRef = ref()

// onMounted(() => {
//     fetchData()
// })
</script>

<style scoped lang="less">
.container {
    padding: 0 16px 0 16px;

    .table-header {
        margin-bottom: 16px;

        .operation-group {
            display: flex;
            gap: 8px;
        }
    }

    .general-card {
        box-sizing: border-box;
        padding-top: 16px;
    }

    .active {
        background-color: rgb(var(--arcoblue-6));
        color: #fff;
    }
}
</style>