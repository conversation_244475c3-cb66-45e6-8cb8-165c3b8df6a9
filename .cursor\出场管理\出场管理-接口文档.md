---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/离场

<a id="opIdedit_8"></a>

## PUT 修改离场

PUT /exit

修改离场

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractUnionId": "string",
  "terminateId": "string",
  "refundId": "string",
  "customerId": "string",
  "customerName": "string",
  "processType": 0,
  "progressStatus": 0,
  "isDiscount": true,
  "discountAmount": 0,
  "discountReason": "string",
  "finalAmount": 0,
  "refundProcessType": 0,
  "payeeName": "string",
  "payeeAccount": "string",
  "bankName": "string",
  "licenseStatus": 0,
  "taxCertStatus": 0,
  "refundApplyType": 0,
  "signType": 0,
  "signAttachments": "string",
  "signTime": "2019-08-24T14:15:22Z",
  "copyTime": "2019-08-24T14:15:22Z",
  "copyBy": "string",
  "copyByName": "string",
  "settleTime": "2019-08-24T14:15:22Z",
  "settleBy": "string",
  "settleByName": "string",
  "exitCostList": [
    {
      "id": "string",
      "exitId": "string",
      "costId": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "payType": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "amount": 0,
      "type": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "isSubmit": true,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ExitAddDTO](#schemaexitadddto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_7"></a>

## POST 新增离场

POST /exit

新增离场

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractUnionId": "string",
  "terminateId": "string",
  "refundId": "string",
  "customerId": "string",
  "customerName": "string",
  "processType": 0,
  "progressStatus": 0,
  "isDiscount": true,
  "discountAmount": 0,
  "discountReason": "string",
  "finalAmount": 0,
  "refundProcessType": 0,
  "payeeName": "string",
  "payeeAccount": "string",
  "bankName": "string",
  "licenseStatus": 0,
  "taxCertStatus": 0,
  "refundApplyType": 0,
  "signType": 0,
  "signAttachments": "string",
  "signTime": "2019-08-24T14:15:22Z",
  "copyTime": "2019-08-24T14:15:22Z",
  "copyBy": "string",
  "copyByName": "string",
  "settleTime": "2019-08-24T14:15:22Z",
  "settleBy": "string",
  "settleByName": "string",
  "exitCostList": [
    {
      "id": "string",
      "exitId": "string",
      "costId": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "payType": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "amount": 0,
      "type": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "isSubmit": true,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ExitAddDTO](#schemaexitadddto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIduploadSignature"></a>

## POST 上传签字单接口

POST /exit/uploadSignature

上传出场单签字附件，完成签字流程

> Body 请求参数

```json
{
  "exitId": "string",
  "signatureFiles": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ExitUploadSignatureDTO](#schemaexituploadsignaturedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdsave_5"></a>

## POST 保存结算单

POST /exit/save

保存结算单(不保存房间交割单相关信息)

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractUnionId": "string",
  "terminateId": "string",
  "refundId": "string",
  "customerId": "string",
  "customerName": "string",
  "processType": 0,
  "progressStatus": 0,
  "isDiscount": true,
  "discountAmount": 0,
  "discountReason": "string",
  "finalAmount": 0,
  "refundProcessType": 0,
  "payeeName": "string",
  "payeeAccount": "string",
  "bankName": "string",
  "licenseStatus": 0,
  "taxCertStatus": 0,
  "refundApplyType": 0,
  "signType": 0,
  "signAttachments": "string",
  "signTime": "2019-08-24T14:15:22Z",
  "copyTime": "2019-08-24T14:15:22Z",
  "copyBy": "string",
  "copyByName": "string",
  "settleTime": "2019-08-24T14:15:22Z",
  "settleBy": "string",
  "settleByName": "string",
  "exitCostList": [
    {
      "id": "string",
      "exitId": "string",
      "costId": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "payType": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "amount": 0,
      "type": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "isSubmit": true,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ExitAddDTO](#schemaexitadddto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdsaveRoom"></a>

## POST 房间交割单单条保存接口

POST /exit/saveRoom

保存房间交割单信息及资产信息

> Body 请求参数

```json
{
  "id": "string",
  "exitId": "string",
  "roomId": "string",
  "roomName": "string",
  "propertyType": 0,
  "parcelName": "string",
  "buildingName": "string",
  "exitDate": "2019-08-24T14:15:22Z",
  "rentControl": 0,
  "doorWindowStatus": 0,
  "doorWindowPenalty": 0,
  "keyHandoverStatus": 0,
  "keyPenalty": 0,
  "cleaningStatus": 0,
  "cleaningPenalty": 0,
  "elecMeterReading": 0,
  "coldWaterReading": 0,
  "hotWaterReading": 0,
  "elecFee": 0,
  "waterFee": 0,
  "pmFee": 0,
  "roomPhotos": "string",
  "assetsSituation": "string",
  "remark": "string",
  "isBusinessConfirmed": true,
  "businessConfirmBy": "string",
  "businessConfirmByName": "string",
  "businessConfirmTime": "2019-08-24T14:15:22Z",
  "isFinanceConfirmed": true,
  "financeConfirmBy": "string",
  "financeConfirmByName": "string",
  "financeConfirmTime": "2019-08-24T14:15:22Z",
  "financeConfirmSignature": "string",
  "isEngineeringConfirmed": true,
  "engineeringConfirmBy": "string",
  "engineeringConfirmByName": "string",
  "engineeringConfirmTime": "2019-08-24T14:15:22Z",
  "engineeringConfirmSignature": "string",
  "exitRoomAssetsList": [
    {
      "id": "string",
      "exitId": "string",
      "exitRoomId": "string",
      "category": 0,
      "name": "string",
      "specification": "string",
      "count": 0,
      "status": 0,
      "penalty": 0,
      "isAdd": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "isSubmit": true,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ExitRoomAddDTO](#schemaexitroomadddto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_9"></a>

## POST 导出询离场列表

POST /exit/export

导出询离场列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|contractId|query|string| 否 | 合同id|合同id|
|contractNo|query|string| 否 | 合同号|合同号|
|contractUnionId|query|string| 否 | 合同统一id|合同统一id|
|terminateId|query|string| 否 | 退租单id|退租单id|
|refundId|query|string| 否 | 退款单id|退款单id|
|customerId|query|string| 否 | 客户id|客户id|
|customerName|query|string| 否 | 客户名称|客户名称|
|processType|query|integer(int32)| 否 | 办理流程: 1-先交割后结算, 2-交割并结算|办理流程: 1-先交割后结算, 2-交割并结算|
|progressStatus|query|integer(int32)| 否 | 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|
|isDiscount|query|boolean| 否 | 是否减免: 0-否, 1-是|是否减免: 0-否, 1-是|
|discountAmount|query|number| 否 | 减免金额|减免金额|
|discountReason|query|string| 否 | 减免原因|减免原因|
|finalAmount|query|number| 否 | 最终费用金额(负为应退)|最终费用金额(负为应退)|
|refundProcessType|query|integer(int32)| 否 | 退款处理方式: 1-退款, 2-暂存客户账户|退款处理方式: 1-退款, 2-暂存客户账户|
|payeeName|query|string| 否 | 收款人|收款人|
|payeeAccount|query|string| 否 | 收款账号|收款账号|
|bankName|query|string| 否 | 开户银行|开户银行|
|licenseStatus|query|integer(int32)| 否 | 营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|taxCertStatus|query|integer(int32)| 否 | 税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|refundApplyType|query|integer(int32)| 否 | 退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|
|signType|query|integer(int32)| 否 | 签字方式: 1-线上, 2-线下|签字方式: 1-线上, 2-线下|
|signAttachments|query|string| 否 | 签字附件|签字附件|
|signTime|query|string(date-time)| 否 | 签字时间|签字时间|
|copyTime|query|string(date-time)| 否 | 首次复制确认单时间|首次复制确认单时间|
|copyBy|query|string| 否 | 首次复制确认单人|首次复制确认单人|
|copyByName|query|string| 否 | 首次复制确认单人名称|首次复制确认单人名称|
|settleTime|query|string(date-time)| 否 | 结算时间|结算时间|
|settleBy|query|string| 否 | 结算人|结算人|
|settleByName|query|string| 否 | 结算人名称|结算人名称|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|status|query|integer(int32)| 否 | 状态: 0-待办理, 1-办理中, 2-已办理|状态: 0-待办理, 1-办理中, 2-已办理|
|tenantName|query|string| 否 | 承租方（模糊匹配）|承租方（模糊匹配）|
|buildingOrRoomName|query|string| 否 | 楼栋/房源名称（模糊搜索）|楼栋/房源名称（模糊搜索）|
|terminateStartDate|query|string(date-time)| 否 | 退租日期开始|退租日期开始|
|terminateEndDate|query|string(date-time)| 否 | 退租日期结束|退租日期结束|
|parcelName|query|string| 否 | 地块名称|地块名称|
|buildingNames|query|array[string]| 否 | 楼栋名称列表|楼栋名称列表|
|isFinanceUnconfirmed|query|boolean| 否 | 是否财务未确认|是否财务未确认|
|isEngineeringUnconfirmed|query|boolean| 否 | 是否工程未确认|是否工程未确认|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIdcopyUrl"></a>

## POST 复制物业确认单接口

POST /exit/copyUrl

记录物业确认单复制时间和复制人信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exitId|query|string| 是 ||出场单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_8"></a>

## DELETE 删除离场

DELETE /exit/delete

删除离场

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|array[string]| 是 ||离场ID列表|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdchooseProcessType"></a>

## POST 选择出场办理类型接口

POST /exit/chooseProcessType

根据出场单ID选择办理流程类型，并更新相应的进度状态

> Body 请求参数

```json
{
  "exitId": "string",
  "processType": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ExitProcessTypeDTO](#schemaexitprocesstypedto)| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdcancel_2"></a>

## POST 作废出场单接口

POST /exit/cancel

作废出场单，并重新生成一条新的出场单

> Body 请求参数

```json
{
  "exitId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ExitCancelDTO](#schemaexitcanceldto)| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdbatchUpdateRoom"></a>

## POST 批量修改房间交割单接口

POST /exit/batchUpdateRoom

批量确认房间交割单或批量设置出场日期

> Body 请求参数

```json
{
  "exitRoomIds": [
    "string"
  ],
  "type": 0,
  "exitDate": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ExitRoomBatchUpdateDTO](#schemaexitroombatchupdatedto)| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdprint"></a>

## GET 打印出场单接口

GET /exit/print

生成出场单打印文件并返回文件URL

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exitId|query|string| 是 ||出场单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_19"></a>

## GET 出场列表查询接口

GET /exit/list

根据项目id、状态、承租方、楼栋/房源、退租日期范围等条件查询出场列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractUnionId": "string",
  "terminateId": "string",
  "refundId": "string",
  "customerId": "string",
  "customerName": "string",
  "processType": 0,
  "progressStatus": 0,
  "isDiscount": true,
  "discountAmount": 0,
  "discountReason": "string",
  "finalAmount": 0,
  "refundProcessType": 0,
  "payeeName": "string",
  "payeeAccount": "string",
  "bankName": "string",
  "licenseStatus": 0,
  "taxCertStatus": 0,
  "refundApplyType": 0,
  "signType": 0,
  "signAttachments": "string",
  "signTime": "2019-08-24T14:15:22Z",
  "copyTime": "2019-08-24T14:15:22Z",
  "copyBy": "string",
  "copyByName": "string",
  "settleTime": "2019-08-24T14:15:22Z",
  "settleBy": "string",
  "settleByName": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "status": 0,
  "tenantName": "string",
  "buildingOrRoomName": "string",
  "terminateStartDate": "2019-08-24T14:15:22Z",
  "terminateEndDate": "2019-08-24T14:15:22Z",
  "parcelName": "string",
  "buildingNames": [
    "string"
  ],
  "isFinanceUnconfirmed": true,
  "isEngineeringUnconfirmed": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|contractId|query|string| 否 | 合同id|合同id|
|contractNo|query|string| 否 | 合同号|合同号|
|contractUnionId|query|string| 否 | 合同统一id|合同统一id|
|terminateId|query|string| 否 | 退租单id|退租单id|
|refundId|query|string| 否 | 退款单id|退款单id|
|customerId|query|string| 否 | 客户id|客户id|
|customerName|query|string| 否 | 客户名称|客户名称|
|processType|query|integer(int32)| 否 | 办理流程: 1-先交割后结算, 2-交割并结算|办理流程: 1-先交割后结算, 2-交割并结算|
|progressStatus|query|integer(int32)| 否 | 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|
|isDiscount|query|boolean| 否 | 是否减免: 0-否, 1-是|是否减免: 0-否, 1-是|
|discountAmount|query|number| 否 | 减免金额|减免金额|
|discountReason|query|string| 否 | 减免原因|减免原因|
|finalAmount|query|number| 否 | 最终费用金额(负为应退)|最终费用金额(负为应退)|
|refundProcessType|query|integer(int32)| 否 | 退款处理方式: 1-退款, 2-暂存客户账户|退款处理方式: 1-退款, 2-暂存客户账户|
|payeeName|query|string| 否 | 收款人|收款人|
|payeeAccount|query|string| 否 | 收款账号|收款账号|
|bankName|query|string| 否 | 开户银行|开户银行|
|licenseStatus|query|integer(int32)| 否 | 营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|taxCertStatus|query|integer(int32)| 否 | 税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|refundApplyType|query|integer(int32)| 否 | 退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|
|signType|query|integer(int32)| 否 | 签字方式: 1-线上, 2-线下|签字方式: 1-线上, 2-线下|
|signAttachments|query|string| 否 | 签字附件|签字附件|
|signTime|query|string(date-time)| 否 | 签字时间|签字时间|
|copyTime|query|string(date-time)| 否 | 首次复制确认单时间|首次复制确认单时间|
|copyBy|query|string| 否 | 首次复制确认单人|首次复制确认单人|
|copyByName|query|string| 否 | 首次复制确认单人名称|首次复制确认单人名称|
|settleTime|query|string(date-time)| 否 | 结算时间|结算时间|
|settleBy|query|string| 否 | 结算人|结算人|
|settleByName|query|string| 否 | 结算人名称|结算人名称|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|status|query|integer(int32)| 否 | 状态: 0-待办理, 1-办理中, 2-已办理|状态: 0-待办理, 1-办理中, 2-已办理|
|tenantName|query|string| 否 | 承租方（模糊匹配）|承租方（模糊匹配）|
|buildingOrRoomName|query|string| 否 | 楼栋/房源名称（模糊搜索）|楼栋/房源名称（模糊搜索）|
|terminateStartDate|query|string(date-time)| 否 | 退租日期开始|退租日期开始|
|terminateEndDate|query|string(date-time)| 否 | 退租日期结束|退租日期结束|
|parcelName|query|string| 否 | 地块名称|地块名称|
|buildingNames|query|array[string]| 否 | 楼栋名称列表|楼栋名称列表|
|isFinanceUnconfirmed|query|boolean| 否 | 是否财务未确认|是否财务未确认|
|isEngineeringUnconfirmed|query|boolean| 否 | 是否工程未确认|是否工程未确认|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|
|body|body|[ExitQueryDTO](#schemaexitquerydto)| 否 ||none|

> 返回示例

> 200 Response

```json
[
  {
    "id": "string",
    "projectId": "string",
    "contractId": "string",
    "contractNo": "string",
    "contractUnionId": "string",
    "terminateId": "string",
    "refundId": "string",
    "customerId": "string",
    "customerName": "string",
    "processType": 0,
    "progressStatus": 0,
    "isDiscount": true,
    "discountAmount": 0,
    "discountReason": "string",
    "finalAmount": 0,
    "refundProcessType": 0,
    "payeeName": "string",
    "payeeAccount": "string",
    "bankName": "string",
    "licenseStatus": 0,
    "taxCertStatus": 0,
    "refundApplyType": 0,
    "signType": 0,
    "signAttachments": "string",
    "signTime": "2019-08-24T14:15:22Z",
    "copyTime": "2019-08-24T14:15:22Z",
    "copyBy": "string",
    "copyByName": "string",
    "settleTime": "2019-08-24T14:15:22Z",
    "settleBy": "string",
    "settleByName": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true,
    "contractPurpose": 0,
    "terminateType": 0,
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateRoomName": "string",
    "terminateRoomCount": 0,
    "engineeringCount": 0,
    "financeCount": 0
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ExitVo](#schemaexitvo)]|false|none||[出场单基础信息]|
|» 出场单基础信息|[ExitVo](#schemaexitvo)|false|none|出场单基础信息|出场单基础信息|
|»» id|string|false|none|主键ID|none|
|»» projectId|string|false|none|项目id|项目id|
|»» contractId|string|false|none|合同id|合同id|
|»» contractNo|string|false|none|合同号|合同号|
|»» contractUnionId|string|false|none|合同统一id|合同统一id|
|»» terminateId|string|false|none|退租单id|退租单id|
|»» refundId|string|false|none|退款单id|退款单id|
|»» customerId|string|false|none|客户id|客户id|
|»» customerName|string|false|none|承租方，客户名称|承租方，客户名称|
|»» processType|integer(int32)|false|none|办理流程: 1-先交割后结算, 2-交割并结算|办理流程: 1-先交割后结算, 2-交割并结算|
|»» progressStatus|integer(int32)|false|none|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|
|»» isDiscount|boolean|false|none|是否减免: 0-否, 1-是|是否减免: 0-否, 1-是|
|»» discountAmount|number|false|none|减免金额|减免金额|
|»» discountReason|string|false|none|减免原因|减免原因|
|»» finalAmount|number|false|none|最终费用金额(负为应退)|最终费用金额(负为应退)|
|»» refundProcessType|integer(int32)|false|none|退款处理方式: 1-退款, 2-暂存客户账户|退款处理方式: 1-退款, 2-暂存客户账户|
|»» payeeName|string|false|none|收款人|收款人|
|»» payeeAccount|string|false|none|收款账号|收款账号|
|»» bankName|string|false|none|开户银行|开户银行|
|»» licenseStatus|integer(int32)|false|none|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|»» taxCertStatus|integer(int32)|false|none|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|»» refundApplyType|integer(int32)|false|none|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|
|»» signType|integer(int32)|false|none|签字方式: 1-线上, 2-线下|签字方式: 1-线上, 2-线下|
|»» signAttachments|string|false|none|签字附件|签字附件|
|»» signTime|string(date-time)|false|none|签字时间|签字时间|
|»» copyTime|string(date-time)|false|none|首次复制确认单时间|首次复制确认单时间|
|»» copyBy|string|false|none|首次复制确认单人|首次复制确认单人|
|»» copyByName|string|false|none|首次复制确认单人名称|首次复制确认单人名称|
|»» settleTime|string(date-time)|false|none|结算时间|结算时间|
|»» settleBy|string|false|none|结算人|结算人|
|»» settleByName|string|false|none|结算人名称|结算人名称|
|»» createByName|string|false|none|创建人姓名|创建人姓名|
|»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|»» contractPurpose|integer(int32)|false|none|合同用途,字典|合同用途,字典|
|»» terminateType|integer(int32)|false|none|退租类型:0-到期退租,1-提前退租|退租类型:0-到期退租,1-提前退租|
|»» terminateDate|string(date-time)|false|none|退租日期|退租日期|
|»» terminateRoomName|string|false|none|退租房源|退租房源|
|»» terminateRoomCount|integer(int32)|false|none|退租房源数|退租房源数|
|»» engineeringCount|integer(int32)|false|none|工程未确认数量|工程未确认数量|
|»» financeCount|integer(int32)|false|none|财务未确认数量|财务未确认数量|

<a id="opIddetail_5"></a>

## GET 出场单详情接口

GET /exit/detail

根据出场单ID获取详情信息，包含出场单信息、退租申请信息、房间列表、费用结算列表、房间资产列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||出场单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "exitInfo": {
    "id": "string",
    "projectId": "string",
    "contractId": "string",
    "contractNo": "string",
    "contractUnionId": "string",
    "terminateId": "string",
    "refundId": "string",
    "customerId": "string",
    "customerName": "string",
    "processType": 0,
    "progressStatus": 0,
    "isDiscount": true,
    "discountAmount": 0,
    "discountReason": "string",
    "finalAmount": 0,
    "refundProcessType": 0,
    "payeeName": "string",
    "payeeAccount": "string",
    "bankName": "string",
    "licenseStatus": 0,
    "taxCertStatus": 0,
    "refundApplyType": 0,
    "signType": 0,
    "signAttachments": "string",
    "signTime": "2019-08-24T14:15:22Z",
    "copyTime": "2019-08-24T14:15:22Z",
    "copyBy": "string",
    "copyByName": "string",
    "settleTime": "2019-08-24T14:15:22Z",
    "settleBy": "string",
    "settleByName": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true,
    "contractPurpose": 0,
    "terminateType": 0,
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateRoomName": "string",
    "terminateRoomCount": 0,
    "engineeringCount": 0,
    "financeCount": 0
  },
  "contractTerminateInfo": {
    "id": "string",
    "contractId": "string",
    "unionId": "string",
    "approveStatus": 0,
    "processId": "string",
    "isExit": true,
    "isPart": true,
    "bondReceivedAmount": 0,
    "rentReceivedAmount": 0,
    "rentOverdueAmount": 0,
    "receivedPeriod": "string",
    "overduePeriod": "string",
    "terminateType": 0,
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateReason": "string",
    "otherReasonDesc": "string",
    "hasOtherDeduction": true,
    "otherDeductionDesc": "string",
    "terminateRemark": "string",
    "terminateAttachments": "string",
    "signAttachments": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true,
    "roomList": [
      {}
    ],
    "costList": [
      {
        "id": "string",
        "contractId": "string",
        "terminateId": "string",
        "costId": "string",
        "costType": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "customerId": "string",
        "customerName": "string",
        "roomId": "string",
        "roomName": "string",
        "subjectId": "string",
        "subjectName": "string",
        "receivableDate": "2019-08-24T14:15:22Z",
        "totalAmount": 0,
        "discountAmount": 0,
        "actualReceivable": 0,
        "terminateReceivable": 0,
        "receivedAmount": 0,
        "penaltyAmount": 0,
        "refundAmount": 0,
        "freeId": 0,
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "contract": {
      "id": "string",
      "projectId": "string",
      "projectName": "string",
      "contractNo": "string",
      "unionId": "string",
      "version": "string",
      "isCurrent": true,
      "isLatest": true,
      "status": 0,
      "statusTwo": 0,
      "approveStatus": 0,
      "operateType": 0,
      "contractType": 0,
      "ourSigningParty": "string",
      "customerName": "string",
      "unenterNum": 0,
      "signWay": 0,
      "signType": 0,
      "originId": "string",
      "changeFromId": "string",
      "landUsage": "string",
      "signerId": "string",
      "signerName": "string",
      "ownerId": "string",
      "ownerName": "string",
      "contractMode": 0,
      "paperContractNo": "string",
      "signDate": "2019-08-24T14:15:22Z",
      "handoverDate": "2019-08-24T14:15:22Z",
      "contractPurpose": 0,
      "dealChannel": 0,
      "assistantId": "string",
      "assistantName": "string",
      "rentYear": 0,
      "rentMonth": 0,
      "rentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "effectDate": "2019-08-24T14:15:22Z",
      "invalidDate": "2019-08-24T14:15:22Z",
      "bookingRelType": 0,
      "bondReceivableDate": "string",
      "bondReceivableType": 0,
      "bondPriceType": 0,
      "bondPrice": 0,
      "chargeWay": 0,
      "rentReceivableDate": "string",
      "rentReceivableType": 0,
      "rentTicketPeriod": 0,
      "rentPayPeriod": 0,
      "increaseGap": 0,
      "increaseRate": 0,
      "increaseRule": "string",
      "estimateRevenue": 0,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "revenueType": 0,
      "isIncludePm": true,
      "pmUnitPrice": 0,
      "pmMonthlyPrice": 0,
      "totalPrice": 0,
      "totalBottomPrice": 0,
      "bizTypeId": "string",
      "bizTypeName": "string",
      "lesseeBrand": "string",
      "businessCategory": "string",
      "openDate": "2019-08-24T14:15:22Z",
      "fireRiskCategory": 0,
      "sprinklerSystem": 0,
      "factoryEngaged": "string",
      "deliverDate": "2019-08-24T14:15:22Z",
      "parkingSpaceType": 0,
      "hasParkingFee": true,
      "parkingFeeAmount": 0,
      "venueDeliveryDate": "2019-08-24T14:15:22Z",
      "venueLocation": "string",
      "dailyActivityStartTime": "2019-08-24T14:15:22Z",
      "dailyActivityEndTime": "2019-08-24T14:15:22Z",
      "venuePurpose": "string",
      "otherInfo": "string",
      "contractAttachments": "string",
      "signAttachments": "string",
      "attachmentsPlan": "string",
      "isUploadSignature": true,
      "changeType": "string",
      "processId": "string",
      "changeDate": "2019-08-24T14:15:22Z",
      "changeAttachments": "string",
      "changeExplanation": "string",
      "isSignatureConfirm": true,
      "isPaperConfirm": true,
      "isFinish": true,
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "isDel": true,
      "roomName": "string",
      "terminateDate": "2019-08-24T14:15:22Z",
      "terminateId": "string",
      "customer": {
        "id": "string",
        "contractId": "string",
        "customerId": "string",
        "customerType": 0,
        "customerName": "string",
        "address": "string",
        "phone": "string",
        "idType": "string",
        "idNumber": "string",
        "isEmployee": true,
        "creditCode": "string",
        "contactName": "string",
        "contactPhone": "string",
        "contactIdNumber": "string",
        "legalName": "string",
        "paymentAccount": "string",
        "guarantorName": "string",
        "guarantorPhone": "string",
        "guarantorIdType": "string",
        "guarantorIdNumber": "string",
        "guarantorAddress": "string",
        "guarantorIdFront": "string",
        "guarantorIdBack": "string",
        "invoiceTitle": "string",
        "invoiceTaxNumber": "string",
        "invoiceAddress": "string",
        "invoicePhone": "string",
        "invoiceBankName": "string",
        "invoiceAccountNumber": "string",
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      },
      "bookings": [
        {
          "id": "string",
          "contractId": "string",
          "bookingId": "string",
          "bookedRoom": "string",
          "bookerName": "string",
          "bookingReceivedAmount": 0,
          "bookingPaymentDate": "2019-08-24T14:15:22Z",
          "transferBondAmount": 0,
          "transferRentAmount": 0,
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ],
      "fees": [
        {
          "id": "string",
          "contractId": "string",
          "feeType": 0,
          "freeType": 0,
          "freeRentMonth": 0,
          "freeRentDay": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "isCharge": true,
          "remark": "string",
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ],
      "costs": [
        {
          "id": "string",
          "contractId": "string",
          "costType": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "period": 0,
          "customerId": "string",
          "customerName": "string",
          "roomId": "string",
          "roomName": "string",
          "area": 0,
          "subjectId": "string",
          "subjectName": "string",
          "receivableDate": "2019-08-24T14:15:22Z",
          "unitPrice": 0,
          "priceUnit": 0,
          "totalAmount": 0,
          "discountAmount": 0,
          "actualReceivable": 0,
          "receivedAmount": 0,
          "isRevenue": true,
          "isDiscount": true,
          "percentageType": 0,
          "fixedPercentage": 0,
          "stepPercentage": "string",
          "createByName": "string",
          "updateByName": "string",
          "isDel": true,
          "contractStartDate": "2019-08-24T14:15:22Z",
          "rentTicketPeriod": 0
        }
      ],
      "rooms": [
        {
          "id": "string",
          "contractId": "string",
          "roomId": "string",
          "roomName": "string",
          "buildingName": "string",
          "floorName": "string",
          "parcelName": "string",
          "stageName": "string",
          "area": 0,
          "standardUnitPrice": 0,
          "bottomPrice": 0,
          "priceUnit": 0,
          "discount": 0,
          "signedUnitPrice": 0,
          "signedMonthlyPrice": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "bondPriceType": 0,
          "bondPrice": 0,
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ]
    }
  },
  "exitRoomList": [
    {}
  ],
  "exitCostList": [
    {
      "id": "string",
      "exitId": "string",
      "costId": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "payType": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "amount": 0,
      "type": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ExitDetailVo](#schemaexitdetailvo)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_ExitAddDTO">ExitAddDTO</h2>

<a id="schemaexitadddto"></a>
<a id="schema_ExitAddDTO"></a>
<a id="tocSexitadddto"></a>
<a id="tocsexitadddto"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractUnionId": "string",
  "terminateId": "string",
  "refundId": "string",
  "customerId": "string",
  "customerName": "string",
  "processType": 0,
  "progressStatus": 0,
  "isDiscount": true,
  "discountAmount": 0,
  "discountReason": "string",
  "finalAmount": 0,
  "refundProcessType": 0,
  "payeeName": "string",
  "payeeAccount": "string",
  "bankName": "string",
  "licenseStatus": 0,
  "taxCertStatus": 0,
  "refundApplyType": 0,
  "signType": 0,
  "signAttachments": "string",
  "signTime": "2019-08-24T14:15:22Z",
  "copyTime": "2019-08-24T14:15:22Z",
  "copyBy": "string",
  "copyByName": "string",
  "settleTime": "2019-08-24T14:15:22Z",
  "settleBy": "string",
  "settleByName": "string",
  "exitCostList": [
    {
      "id": "string",
      "exitId": "string",
      "costId": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "payType": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "amount": 0,
      "type": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "isSubmit": true,
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractNo|string|false|none|合同号|合同号|
|contractUnionId|string|false|none|合同统一id|合同统一id|
|terminateId|string|false|none|退租单id|退租单id|
|refundId|string|false|none|退款单id|退款单id|
|customerId|string|false|none|客户id|客户id|
|customerName|string|false|none|客户名称|客户名称|
|processType|integer(int32)|false|none|办理流程: 1-先交割后结算, 2-交割并结算|办理流程: 1-先交割后结算, 2-交割并结算|
|progressStatus|integer(int32)|false|none|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|
|isDiscount|boolean|false|none|是否减免: 0-否, 1-是|是否减免: 0-否, 1-是|
|discountAmount|number|false|none|减免金额|减免金额|
|discountReason|string|false|none|减免原因|减免原因|
|finalAmount|number|false|none|最终费用金额(负为应退)|最终费用金额(负为应退)|
|refundProcessType|integer(int32)|false|none|退款处理方式: 1-退款, 2-暂存客户账户|退款处理方式: 1-退款, 2-暂存客户账户|
|payeeName|string|false|none|收款人|收款人|
|payeeAccount|string|false|none|收款账号|收款账号|
|bankName|string|false|none|开户银行|开户银行|
|licenseStatus|integer(int32)|false|none|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|taxCertStatus|integer(int32)|false|none|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|refundApplyType|integer(int32)|false|none|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|
|signType|integer(int32)|false|none|签字方式: 1-线上, 2-线下|签字方式: 1-线上, 2-线下|
|signAttachments|string|false|none|签字附件|签字附件|
|signTime|string(date-time)|false|none|签字时间|签字时间|
|copyTime|string(date-time)|false|none|首次复制确认单时间|首次复制确认单时间|
|copyBy|string|false|none|首次复制确认单人|首次复制确认单人|
|copyByName|string|false|none|首次复制确认单人名称|首次复制确认单人名称|
|settleTime|string(date-time)|false|none|结算时间|结算时间|
|settleBy|string|false|none|结算人|结算人|
|settleByName|string|false|none|结算人名称|结算人名称|
|exitCostList|[[ExitCostVo](#schemaexitcostvo)]|false|none|出场费用结算列表|出场费用结算列表|
|isSubmit|boolean|false|none|是否提交: true-确认, false-暂存|是否提交: true-确认, false-暂存|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ExitCostVo">ExitCostVo</h2>

<a id="schemaexitcostvo"></a>
<a id="schema_ExitCostVo"></a>
<a id="tocSexitcostvo"></a>
<a id="tocsexitcostvo"></a>

```json
{
  "id": "string",
  "exitId": "string",
  "costId": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "payType": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "amount": 0,
  "type": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

出场费用结算列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|exitId|string|false|none|离场单id|离场单id|
|costId|string|false|none|t_cost表id|t_cost表id|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|payType|integer(int32)|false|none|支付方向: 1-收, 2-支|支付方向: 1-收, 2-支|
|subjectId|string|false|none|科目id|科目id|
|subjectName|string|false|none|科目名|科目名|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|amount|number|false|none|金额（元）|金额（元）|
|type|integer(int32)|false|none|账单类型: 0-退租结算, 1-交割单生成, 2-手动添加|账单类型: 0-退租结算, 1-交割单生成, 2-手动添加|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractTerminateCostVo">ContractTerminateCostVo</h2>

<a id="schemacontractterminatecostvo"></a>
<a id="schema_ContractTerminateCostVo"></a>
<a id="tocScontractterminatecostvo"></a>
<a id="tocscontractterminatecostvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "terminateId": "string",
  "costId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "terminateReceivable": 0,
  "receivedAmount": 0,
  "penaltyAmount": 0,
  "refundAmount": 0,
  "freeId": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

退租费用VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|terminateId|string|false|none|退租单id|退租单id|
|costId|string|false|none|t_cost表id|t_cost表id|
|costType|integer(int32)|false|none|账单类型,1-免租期补收,2-退款信息|账单类型,1-免租期补收,2-退款信息|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|subjectId|string|false|none|科目id|科目id|
|subjectName|string|false|none|科目名|科目名|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|terminateReceivable|number|false|none|截止退租日应收（元）|截止退租日应收（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|penaltyAmount|number|false|none|罚没金额（元）|罚没金额（元）|
|refundAmount|number|false|none|预计退款金额（元）|预计退款金额（元）|
|freeId|integer(int32)|false|none|免租期t_contract_fee表id|免租期t_contract_fee表id|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractBookingVo">ContractBookingVo</h2>

<a id="schemacontractbookingvo"></a>
<a id="schema_ContractBookingVo"></a>
<a id="tocScontractbookingvo"></a>
<a id="tocscontractbookingvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "bookingId": "string",
  "bookedRoom": "string",
  "bookerName": "string",
  "bookingReceivedAmount": 0,
  "bookingPaymentDate": "2019-08-24T14:15:22Z",
  "transferBondAmount": 0,
  "transferRentAmount": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同定单列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|bookingId|string|false|none|定单id|定单id|
|bookedRoom|string|false|none|预定房源|预定房源|
|bookerName|string|false|none|预定人姓名|预定人姓名|
|bookingReceivedAmount|number|false|none|定单已收金额|定单已收金额|
|bookingPaymentDate|string(date-time)|false|none|定单收款(生效)日期|定单收款(生效)日期|
|transferBondAmount|number|false|none|转保证金金额|转保证金金额|
|transferRentAmount|number|false|none|转租金金额|转租金金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractCostVo">ContractCostVo</h2>

<a id="schemacontractcostvo"></a>
<a id="schema_ContractCostVo"></a>
<a id="tocScontractcostvo"></a>
<a id="tocscontractcostvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "period": 0,
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "unitPrice": 0,
  "priceUnit": 0,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "isRevenue": true,
  "isDiscount": true,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractStartDate": "2019-08-24T14:15:22Z",
  "rentTicketPeriod": 0
}

```

合同应收列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|period|integer(int32)|false|none|账单期数|账单期数|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|area|number|false|none|商铺面积|商铺面积|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途|收款用途|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|unitPrice|number|false|none|单价|单价|
|priceUnit|integer(int32)|false|none|单价单位|单价单位|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|isRevenue|boolean|false|none|是否营收提成,0-否,1-是|是否营收提成,0-否,1-是|
|isDiscount|boolean|false|none|是否优惠,0-否,1-是|是否优惠,0-否,1-是|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractStartDate|string(date-time)|false|none|合同开始日期, 用来推理租赁月|合同开始日期, 用来推理租赁月|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|

<h2 id="tocS_ContractCustomerVo">ContractCustomerVo</h2>

<a id="schemacontractcustomervo"></a>
<a id="schema_ContractCustomerVo"></a>
<a id="tocScontractcustomervo"></a>
<a id="tocscontractcustomervo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "customerId": "string",
  "customerType": 0,
  "customerName": "string",
  "address": "string",
  "phone": "string",
  "idType": "string",
  "idNumber": "string",
  "isEmployee": true,
  "creditCode": "string",
  "contactName": "string",
  "contactPhone": "string",
  "contactIdNumber": "string",
  "legalName": "string",
  "paymentAccount": "string",
  "guarantorName": "string",
  "guarantorPhone": "string",
  "guarantorIdType": "string",
  "guarantorIdNumber": "string",
  "guarantorAddress": "string",
  "guarantorIdFront": "string",
  "guarantorIdBack": "string",
  "invoiceTitle": "string",
  "invoiceTaxNumber": "string",
  "invoiceAddress": "string",
  "invoicePhone": "string",
  "invoiceBankName": "string",
  "invoiceAccountNumber": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同客户信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|customerId|string|false|none|客户id|客户id|
|customerType|integer(int32)|false|none|客户类型:1-个人1,2-企业|客户类型:1-个人1,2-企业|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|address|string|false|none|地址|地址|
|phone|string|false|none|手机号(个人/法人)|手机号(个人/法人)|
|idType|string|false|none|证件类型(个人/法人)|证件类型(个人/法人)|
|idNumber|string|false|none|证件号码(个人/法人)|证件号码(个人/法人)|
|isEmployee|boolean|false|none|是否是员工:0-否,1-是|是否是员工:0-否,1-是|
|creditCode|string|false|none|统一社会信用代码|统一社会信用代码|
|contactName|string|false|none|联系人|联系人|
|contactPhone|string|false|none|联系人手机号|联系人手机号|
|contactIdNumber|string|false|none|联系人身份证号|联系人身份证号|
|legalName|string|false|none|法人名称|法人名称|
|paymentAccount|string|false|none|付款银行账号|付款银行账号|
|guarantorName|string|false|none|担保人姓名|担保人姓名|
|guarantorPhone|string|false|none|担保人手机号|担保人手机号|
|guarantorIdType|string|false|none|担保人证件类型|担保人证件类型|
|guarantorIdNumber|string|false|none|担保人证件号码|担保人证件号码|
|guarantorAddress|string|false|none|担保人通讯地址|担保人通讯地址|
|guarantorIdFront|string|false|none|担保人身份证正面地址|担保人身份证正面地址|
|guarantorIdBack|string|false|none|担保人身份证反面地址|担保人身份证反面地址|
|invoiceTitle|string|false|none|开票名称|开票名称|
|invoiceTaxNumber|string|false|none|开票税号|开票税号|
|invoiceAddress|string|false|none|开票单位地址|开票单位地址|
|invoicePhone|string|false|none|开票电话号码|开票电话号码|
|invoiceBankName|string|false|none|开票开户银行|开票开户银行|
|invoiceAccountNumber|string|false|none|开票银行账户|开票银行账户|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractFeeVo">ContractFeeVo</h2>

<a id="schemacontractfeevo"></a>
<a id="schema_ContractFeeVo"></a>
<a id="tocScontractfeevo"></a>
<a id="tocscontractfeevo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "feeType": 0,
  "freeType": 0,
  "freeRentMonth": 0,
  "freeRentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "isCharge": true,
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同费用列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|feeType|integer(int32)|false|none|费用类型,1-免租期|费用类型,1-免租期|
|freeType|integer(int32)|false|none|免租类型,0-装修免租,1-经营免租,2-合同免租|免租类型,0-装修免租,1-经营免租,2-合同免租|
|freeRentMonth|integer(int32)|false|none|免租月数|免租月数|
|freeRentDay|integer(int32)|false|none|免租天数|免租天数|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|isCharge|boolean|false|none|免租是否收费:0-否,1-是(退租时使用)|免租是否收费:0-否,1-是(退租时使用)|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractRoomVo">ContractRoomVo</h2>

<a id="schemacontractroomvo"></a>
<a id="schema_ContractRoomVo"></a>
<a id="tocScontractroomvo"></a>
<a id="tocscontractroomvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "roomId": "string",
  "roomName": "string",
  "buildingName": "string",
  "floorName": "string",
  "parcelName": "string",
  "stageName": "string",
  "area": 0,
  "standardUnitPrice": 0,
  "bottomPrice": 0,
  "priceUnit": 0,
  "discount": 0,
  "signedUnitPrice": 0,
  "signedMonthlyPrice": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "bondPriceType": 0,
  "bondPrice": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同房源列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房间名称|房间名称|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorName|string|false|none|楼层名称|楼层名称|
|parcelName|string|false|none|地块名称|地块名称|
|stageName|string|false|none|分期名称|分期名称|
|area|number|false|none|面积（㎡）|面积（㎡）|
|standardUnitPrice|number|false|none|标准租金（单价）|标准租金（单价）|
|bottomPrice|number|false|none|底价|底价|
|priceUnit|integer(int32)|false|none|单价单位, 使用统一字典, 待定|单价单位, 使用统一字典, 待定|
|discount|number|false|none|折扣|折扣|
|signedUnitPrice|number|false|none|签约单价|签约单价|
|signedMonthlyPrice|number|false|none|签约月总价（元/月）|签约月总价（元/月）|
|startDate|string(date-time)|false|none|实际开始日期|实际开始日期|
|endDate|string(date-time)|false|none|实际结束日期|实际结束日期|
|bondPriceType|integer(int32)|false|none|立项定价保证金金额类型|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|
|bondPrice|number|false|none|立项定价保证金金额|立项定价保证金金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractVo">ContractVo</h2>

<a id="schemacontractvo"></a>
<a id="schema_ContractVo"></a>
<a id="tocScontractvo"></a>
<a id="tocscontractvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "customerName": "string",
  "unenterNum": 0,
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "changeFromId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "invalidDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "totalBottomPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "venueDeliveryDate": "2019-08-24T14:15:22Z",
  "venueLocation": "string",
  "dailyActivityStartTime": "2019-08-24T14:15:22Z",
  "dailyActivityEndTime": "2019-08-24T14:15:22Z",
  "venuePurpose": "string",
  "otherInfo": "string",
  "contractAttachments": "string",
  "signAttachments": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "changeType": "string",
  "processId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeAttachments": "string",
  "changeExplanation": "string",
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "roomName": "string",
  "terminateDate": "2019-08-24T14:15:22Z",
  "terminateId": "string",
  "customer": {
    "id": "string",
    "contractId": "string",
    "customerId": "string",
    "customerType": 0,
    "customerName": "string",
    "address": "string",
    "phone": "string",
    "idType": "string",
    "idNumber": "string",
    "isEmployee": true,
    "creditCode": "string",
    "contactName": "string",
    "contactPhone": "string",
    "contactIdNumber": "string",
    "legalName": "string",
    "paymentAccount": "string",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "guarantorIdType": "string",
    "guarantorIdNumber": "string",
    "guarantorAddress": "string",
    "guarantorIdFront": "string",
    "guarantorIdBack": "string",
    "invoiceTitle": "string",
    "invoiceTaxNumber": "string",
    "invoiceAddress": "string",
    "invoicePhone": "string",
    "invoiceBankName": "string",
    "invoiceAccountNumber": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "bookings": [
    {
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "fees": [
    {
      "id": "string",
      "contractId": "string",
      "feeType": 0,
      "freeType": 0,
      "freeRentMonth": 0,
      "freeRentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "isCharge": true,
      "remark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "costs": [
    {
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "rooms": [
    {
      "id": "string",
      "contractId": "string",
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "bondPriceType": 0,
      "bondPrice": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|contractNo|string|false|none|合同号|合同号|
|unionId|string|false|none|统一id|统一id|
|version|string|false|none|版本号v00x|版本号v00x|
|isCurrent|boolean|false|none|是否当前生效版本 0-否,1-是|是否当前生效版本 0-否,1-是|
|isLatest|boolean|false|none|是否最新版本 0-否,1-是|是否最新版本 0-否,1-是|
|status|integer(int32)|false|none|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|
|statusTwo|integer(int32)|false|none|二级状态|二级状态|
|approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|operateType|integer(int32)|false|none|最新操作类型,0-新签,1-变更条款,2-退租|最新操作类型,0-新签,1-变更条款,2-退租|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|ourSigningParty|string|false|none|我方签约主体信息|我方签约主体信息|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|unenterNum|integer(int32)|false|none|未进场房源数|未进场房源数|
|signWay|integer(int32)|false|none|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|
|signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|originId|string|false|none|续签源合同ID|续签源合同ID|
|changeFromId|string|false|none|变更源合同ID|变更源合同ID|
|landUsage|string|false|none|用地性质|用地性质|
|signerId|string|false|none|合同签约人id|合同签约人id|
|signerName|string|false|none|签约人姓名|签约人姓名|
|ownerId|string|false|none|责任人id|责任人id|
|ownerName|string|false|none|责任人姓名|责任人姓名|
|contractMode|integer(int32)|false|none|合同模式,0-标准合同,1-非标合同|合同模式,0-标准合同,1-非标合同|
|paperContractNo|string|false|none|纸质合同号|纸质合同号|
|signDate|string(date-time)|false|none|签订日期|签订日期|
|handoverDate|string(date-time)|false|none|交房日期|交房日期|
|contractPurpose|integer(int32)|false|none|合同用途,字典:|合同用途,字典:|
|dealChannel|integer(int32)|false|none|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|
|assistantId|string|false|none|协助人id|协助人id|
|assistantName|string|false|none|协助人姓名|协助人姓名|
|rentYear|integer(int32)|false|none|租赁期限-年|租赁期限-年|
|rentMonth|integer(int32)|false|none|租赁期限-月|租赁期限-月|
|rentDay|integer(int32)|false|none|租赁期限-日|租赁期限-日|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|effectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|invalidDate|string(date-time)|false|none|实际失效日期|实际失效日期|
|bookingRelType|integer(int32)|false|none|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|
|bondReceivableDate|string|false|none|保证金应收日期(天数/具体日期)|保证金应收日期(天数/具体日期)|
|bondReceivableType|integer(int32)|false|none|保证金应收日期类型,0-合同签订后, 1-指定日期|保证金应收日期类型,0-合同签订后, 1-指定日期|
|bondPriceType|integer(int32)|false|none|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|
|bondPrice|number|false|none|保证金金额|保证金金额|
|chargeWay|integer(int32)|false|none|收费方式,0-固定租金,1-递增租金,2-营收抽成|收费方式,0-固定租金,1-递增租金,2-营收抽成|
|rentReceivableDate|string|false|none|租金应收日期(天数/具体日期)|租金应收日期(天数/具体日期)|
|rentReceivableType|integer(int32)|false|none|租金应收日期类型,1-租期开始前,2-租期开始后|租金应收日期类型,1-租期开始前,2-租期开始后|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|rentPayPeriod|integer(int32)|false|none|租金支付周期|租金支付周期|
|increaseGap|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|increaseRate|number|false|none|递增率|递增率|
|increaseRule|string|false|none|租金递增规则(非标)|租金递增规则(非标)|
|estimateRevenue|number|false|none|预估营收额|预估营收额|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|revenueType|integer(int32)|false|none|抽点类型: 1-按月营业额, 2-按年营业额|抽点类型: 1-按月营业额, 2-按年营业额|
|isIncludePm|boolean|false|none|是否包含物业费|是否包含物业费|
|pmUnitPrice|number|false|none|月物业费单价|月物业费单价|
|pmMonthlyPrice|number|false|none|月物业费总价|月物业费总价|
|totalPrice|number|false|none|合同总价|合同总价|
|totalBottomPrice|number|false|none|合同底价|合同底价|
|bizTypeId|string|false|none|业态id|业态id|
|bizTypeName|string|false|none|业态|业态|
|lesseeBrand|string|false|none|承租方品牌|承租方品牌|
|businessCategory|string|false|none|经营品类|经营品类|
|openDate|string(date-time)|false|none|开业日期|开业日期|
|fireRiskCategory|integer(int32)|false|none|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|
|sprinklerSystem|integer(int32)|false|none|喷淋系统,0-安装,1-未安装|喷淋系统,0-安装,1-未安装|
|factoryEngaged|string|false|none|厂房从事|厂房从事|
|deliverDate|string(date-time)|false|none|交接日期|交接日期|
|parkingSpaceType|integer(int32)|false|none|车位类型,0-人防,1-非人防|车位类型,0-人防,1-非人防|
|hasParkingFee|boolean|false|none|是否包含车位管理费|是否包含车位管理费|
|parkingFeeAmount|number|false|none|车位管理费金额|车位管理费金额|
|venueDeliveryDate|string(date-time)|false|none|场地交付日期|场地交付日期|
|venueLocation|string|false|none|租赁场地位置|租赁场地位置|
|dailyActivityStartTime|string(date-time)|false|none|每日活动开始时间|每日活动开始时间|
|dailyActivityEndTime|string(date-time)|false|none|每日活动结束时间|每日活动结束时间|
|venuePurpose|string|false|none|场地用途|场地用途|
|otherInfo|string|false|none|补充条款|补充条款|
|contractAttachments|string|false|none|合同附件|合同附件|
|signAttachments|string|false|none|签署附件|签署附件[{"fileName": "", "fileUrl":"", "isConfirm":1or0}]|
|attachmentsPlan|string|false|none|附件-平面图|附件-平面图|
|isUploadSignature|boolean|false|none|是否上传签署文件 0-否,1-是|是否上传签署文件 0-否,1-是|
|changeType|string|false|none|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|
|processId|string|false|none|流程实例t_oa_process->id|流程实例t_oa_process->id|
|changeDate|string(date-time)|false|none|变更日期|变更日期|
|changeAttachments|string|false|none|变更附件json数组|变更附件json数组|
|changeExplanation|string|false|none|变更说明|变更说明|
|isSignatureConfirm|boolean|false|none|是否确认签署 0-否,1-是|是否确认签署 0-否,1-是|
|isPaperConfirm|boolean|false|none|是否确认纸质文件 0-否,1-是|是否确认纸质文件 0-否,1-是|
|isFinish|boolean|false|none|合同是否完全结束 0-否,1-是|合同是否完全结束 0-否,1-是|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建日期|创建日期|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新日期|更新日期|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|roomName|string|false|none|租赁资源|租赁资源|
|terminateDate|string(date-time)|false|none|退租日期|退租日期|
|terminateId|string|false|none|退租id|退租id|
|customer|[ContractCustomerVo](#schemacontractcustomervo)|false|none||合同客户信息|
|bookings|[[ContractBookingVo](#schemacontractbookingvo)]|false|none|合同定单列表|合同定单列表|
|fees|[[ContractFeeVo](#schemacontractfeevo)]|false|none|合同费用列表|合同费用列表|
|costs|[[ContractCostVo](#schemacontractcostvo)]|false|none|合同应收列表|合同应收列表|
|rooms|[[ContractRoomVo](#schemacontractroomvo)]|false|none|合同房源列表|合同房源列表|

<h2 id="tocS_ExitUploadSignatureDTO">ExitUploadSignatureDTO</h2>

<a id="schemaexituploadsignaturedto"></a>
<a id="schema_ExitUploadSignatureDTO"></a>
<a id="tocSexituploadsignaturedto"></a>
<a id="tocsexituploadsignaturedto"></a>

```json
{
  "exitId": "string",
  "signatureFiles": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|exitId|string|true|none|出场单id|出场单id|
|signatureFiles|string|true|none|签字单文件json数组字符串|签字单文件json数组字符串，格式：[{"fileUrl":"xxxx","fileName":""}]|

<h2 id="tocS_ExitQueryDTO">ExitQueryDTO</h2>

<a id="schemaexitquerydto"></a>
<a id="schema_ExitQueryDTO"></a>
<a id="tocSexitquerydto"></a>
<a id="tocsexitquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractUnionId": "string",
  "terminateId": "string",
  "refundId": "string",
  "customerId": "string",
  "customerName": "string",
  "processType": 0,
  "progressStatus": 0,
  "isDiscount": true,
  "discountAmount": 0,
  "discountReason": "string",
  "finalAmount": 0,
  "refundProcessType": 0,
  "payeeName": "string",
  "payeeAccount": "string",
  "bankName": "string",
  "licenseStatus": 0,
  "taxCertStatus": 0,
  "refundApplyType": 0,
  "signType": 0,
  "signAttachments": "string",
  "signTime": "2019-08-24T14:15:22Z",
  "copyTime": "2019-08-24T14:15:22Z",
  "copyBy": "string",
  "copyByName": "string",
  "settleTime": "2019-08-24T14:15:22Z",
  "settleBy": "string",
  "settleByName": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "status": 0,
  "tenantName": "string",
  "buildingOrRoomName": "string",
  "terminateStartDate": "2019-08-24T14:15:22Z",
  "terminateEndDate": "2019-08-24T14:15:22Z",
  "parcelName": "string",
  "buildingNames": [
    "string"
  ],
  "isFinanceUnconfirmed": true,
  "isEngineeringUnconfirmed": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractNo|string|false|none|合同号|合同号|
|contractUnionId|string|false|none|合同统一id|合同统一id|
|terminateId|string|false|none|退租单id|退租单id|
|refundId|string|false|none|退款单id|退款单id|
|customerId|string|false|none|客户id|客户id|
|customerName|string|false|none|客户名称|客户名称|
|processType|integer(int32)|false|none|办理流程: 1-先交割后结算, 2-交割并结算|办理流程: 1-先交割后结算, 2-交割并结算|
|progressStatus|integer(int32)|false|none|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|
|isDiscount|boolean|false|none|是否减免: 0-否, 1-是|是否减免: 0-否, 1-是|
|discountAmount|number|false|none|减免金额|减免金额|
|discountReason|string|false|none|减免原因|减免原因|
|finalAmount|number|false|none|最终费用金额(负为应退)|最终费用金额(负为应退)|
|refundProcessType|integer(int32)|false|none|退款处理方式: 1-退款, 2-暂存客户账户|退款处理方式: 1-退款, 2-暂存客户账户|
|payeeName|string|false|none|收款人|收款人|
|payeeAccount|string|false|none|收款账号|收款账号|
|bankName|string|false|none|开户银行|开户银行|
|licenseStatus|integer(int32)|false|none|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|taxCertStatus|integer(int32)|false|none|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|refundApplyType|integer(int32)|false|none|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|
|signType|integer(int32)|false|none|签字方式: 1-线上, 2-线下|签字方式: 1-线上, 2-线下|
|signAttachments|string|false|none|签字附件|签字附件|
|signTime|string(date-time)|false|none|签字时间|签字时间|
|copyTime|string(date-time)|false|none|首次复制确认单时间|首次复制确认单时间|
|copyBy|string|false|none|首次复制确认单人|首次复制确认单人|
|copyByName|string|false|none|首次复制确认单人名称|首次复制确认单人名称|
|settleTime|string(date-time)|false|none|结算时间|结算时间|
|settleBy|string|false|none|结算人|结算人|
|settleByName|string|false|none|结算人名称|结算人名称|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|status|integer(int32)|false|none|状态: 0-待办理, 1-办理中, 2-已办理|状态: 0-待办理, 1-办理中, 2-已办理|
|tenantName|string|false|none|承租方（模糊匹配）|承租方（模糊匹配）|
|buildingOrRoomName|string|false|none|楼栋/房源名称（模糊搜索）|楼栋/房源名称（模糊搜索）|
|terminateStartDate|string(date-time)|false|none|退租日期开始|退租日期开始|
|terminateEndDate|string(date-time)|false|none|退租日期结束|退租日期结束|
|parcelName|string|false|none|地块名称|地块名称|
|buildingNames|[string]|false|none|楼栋名称列表|楼栋名称列表|
|» 楼栋名称列表|string|false|none|楼栋名称列表|楼栋名称列表|
|isFinanceUnconfirmed|boolean|false|none|是否财务未确认|是否财务未确认|
|isEngineeringUnconfirmed|boolean|false|none|是否工程未确认|是否工程未确认|

<h2 id="tocS_ExitRoomAddDTO">ExitRoomAddDTO</h2>

<a id="schemaexitroomadddto"></a>
<a id="schema_ExitRoomAddDTO"></a>
<a id="tocSexitroomadddto"></a>
<a id="tocsexitroomadddto"></a>

```json
{
  "id": "string",
  "exitId": "string",
  "roomId": "string",
  "roomName": "string",
  "propertyType": 0,
  "parcelName": "string",
  "buildingName": "string",
  "exitDate": "2019-08-24T14:15:22Z",
  "rentControl": 0,
  "doorWindowStatus": 0,
  "doorWindowPenalty": 0,
  "keyHandoverStatus": 0,
  "keyPenalty": 0,
  "cleaningStatus": 0,
  "cleaningPenalty": 0,
  "elecMeterReading": 0,
  "coldWaterReading": 0,
  "hotWaterReading": 0,
  "elecFee": 0,
  "waterFee": 0,
  "pmFee": 0,
  "roomPhotos": "string",
  "assetsSituation": "string",
  "remark": "string",
  "isBusinessConfirmed": true,
  "businessConfirmBy": "string",
  "businessConfirmByName": "string",
  "businessConfirmTime": "2019-08-24T14:15:22Z",
  "isFinanceConfirmed": true,
  "financeConfirmBy": "string",
  "financeConfirmByName": "string",
  "financeConfirmTime": "2019-08-24T14:15:22Z",
  "financeConfirmSignature": "string",
  "isEngineeringConfirmed": true,
  "engineeringConfirmBy": "string",
  "engineeringConfirmByName": "string",
  "engineeringConfirmTime": "2019-08-24T14:15:22Z",
  "engineeringConfirmSignature": "string",
  "exitRoomAssetsList": [
    {
      "id": "string",
      "exitId": "string",
      "exitRoomId": "string",
      "category": 0,
      "name": "string",
      "specification": "string",
      "count": 0,
      "status": 0,
      "penalty": 0,
      "isAdd": true,
      "remark": "string",
      "isDel": true
    }
  ],
  "isSubmit": true,
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|exitId|string|false|none|离场单id|离场单id|
|roomId|string|false|none|房间id|房间id|
|roomName|string|false|none|房源名称|房源名称|
|propertyType|integer(int32)|false|none|物业类型字典|物业类型字典|
|parcelName|string|false|none|所属地块|所属地块|
|buildingName|string|false|none|所属楼栋|所属楼栋|
|exitDate|string(date-time)|false|none|出场日期|出场日期|
|rentControl|integer(int32)|false|none|租控管理: 1-当前空置已做断电、锁门处理, 2-二次出租无需断电处理,开门设置更新|租控管理: 1-当前空置已做断电、锁门处理, 2-二次出租无需断电处理,开门设置更新|
|doorWindowStatus|integer(int32)|false|none|门、窗、墙体及其他情况: 1-完好, 2-损坏|门、窗、墙体及其他情况: 1-完好, 2-损坏|
|doorWindowPenalty|number|false|none|赔偿金（门、窗、墙体及其他损坏）|赔偿金（门、窗、墙体及其他损坏）|
|keyHandoverStatus|integer(int32)|false|none|钥匙交接情况: 1-已交齐, 2-未交齐|钥匙交接情况: 1-已交齐, 2-未交齐|
|keyPenalty|number|false|none|赔偿金（钥匙未交齐）|赔偿金（钥匙未交齐）|
|cleaningStatus|integer(int32)|false|none|清洁卫生: 1-自行打扫完毕、洁净, 2-保洁及垃圾清理收费|清洁卫生: 1-自行打扫完毕、洁净, 2-保洁及垃圾清理收费|
|cleaningPenalty|number|false|none|保洁及垃圾清理费金额|保洁及垃圾清理费金额|
|elecMeterReading|number|false|none|电度数|电度数|
|coldWaterReading|number|false|none|冷水度数|冷水度数|
|hotWaterReading|number|false|none|热水度数|热水度数|
|elecFee|number|false|none|电费欠费|电费欠费|
|waterFee|number|false|none|水费欠费|水费欠费|
|pmFee|number|false|none|物业欠费|物业欠费|
|roomPhotos|string|false|none|房间照片|房间照片|
|assetsSituation|string|false|none|固定资产、设备设施评估情况|固定资产、设备设施评估情况|
|remark|string|false|none|备注|备注|
|isBusinessConfirmed|boolean|false|none|商服是否确认: 0-否, 1-是|商服是否确认: 0-否, 1-是|
|businessConfirmBy|string|false|none|商服确认人id|商服确认人id|
|businessConfirmByName|string|false|none|商服确认人名称|商服确认人名称|
|businessConfirmTime|string(date-time)|false|none|商服确认时间|商服确认时间|
|isFinanceConfirmed|boolean|false|none|综合或财务是否确认: 0-否, 1-是|综合或财务是否确认: 0-否, 1-是|
|financeConfirmBy|string|false|none|综合或财务确认人id|综合或财务确认人id|
|financeConfirmByName|string|false|none|综合或财务确认人名称|综合或财务确认人名称|
|financeConfirmTime|string(date-time)|false|none|综合或财务确认时间|综合或财务确认时间|
|financeConfirmSignature|string|false|none|综合或财务确认签名|综合或财务确认签名|
|isEngineeringConfirmed|boolean|false|none|工程或客服是否确认: 0-否, 1-是|工程或客服是否确认: 0-否, 1-是|
|engineeringConfirmBy|string|false|none|工程或客服确认人id|工程或客服确认人id|
|engineeringConfirmByName|string|false|none|工程或客服确认人名称|工程或客服确认人名称|
|engineeringConfirmTime|string(date-time)|false|none|工程或客服确认时间|工程或客服确认时间|
|engineeringConfirmSignature|string|false|none|工程或客服确认签名|工程或客服确认签名|
|exitRoomAssetsList|[[ExitRoomAssetsAddDTO](#schemaexitroomassetsadddto)]|false|none|房间资产列表|房间资产列表|
|isSubmit|boolean|false|none|是否提交: true-确认, false-暂存|是否提交: true-确认, false-暂存|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ExitRoomAssetsAddDTO">ExitRoomAssetsAddDTO</h2>

<a id="schemaexitroomassetsadddto"></a>
<a id="schema_ExitRoomAssetsAddDTO"></a>
<a id="tocSexitroomassetsadddto"></a>
<a id="tocsexitroomassetsadddto"></a>

```json
{
  "id": "string",
  "exitId": "string",
  "exitRoomId": "string",
  "category": 0,
  "name": "string",
  "specification": "string",
  "count": 0,
  "status": 0,
  "penalty": 0,
  "isAdd": true,
  "remark": "string",
  "isDel": true
}

```

房间资产列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|exitId|string|false|none|离场单id|离场单id|
|exitRoomId|string|false|none|出场记录-房间id|出场记录-房间id|
|category|integer(int32)|false|none|种类|种类|
|name|string|false|none|物品名称|物品名称|
|specification|string|false|none|规格|规格|
|count|integer(int32)|false|none|数量|数量|
|status|integer(int32)|false|none|现状: 1-完好, 2-损坏, 3-丢失|现状: 1-完好, 2-损坏, 3-丢失|
|penalty|number|false|none|赔偿金|赔偿金|
|isAdd|boolean|false|none|是否手动添加的,0-否,1-是|是否手动添加的,0-否,1-是|
|remark|string|false|none|说明|说明|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ExitCancelDTO">ExitCancelDTO</h2>

<a id="schemaexitcanceldto"></a>
<a id="schema_ExitCancelDTO"></a>
<a id="tocSexitcanceldto"></a>
<a id="tocsexitcanceldto"></a>

```json
{
  "exitId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|exitId|string|true|none|出场单id|出场单id|

<h2 id="tocS_ExitRoomBatchUpdateDTO">ExitRoomBatchUpdateDTO</h2>

<a id="schemaexitroombatchupdatedto"></a>
<a id="schema_ExitRoomBatchUpdateDTO"></a>
<a id="tocSexitroombatchupdatedto"></a>
<a id="tocsexitroombatchupdatedto"></a>

```json
{
  "exitRoomIds": [
    "string"
  ],
  "type": 0,
  "exitDate": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|exitRoomIds|[string]|true|none|离场房间id列表|离场房间id列表|
|» 离场房间id列表|string|false|none|离场房间id列表|离场房间id列表|
|type|integer(int32)|true|none|操作类型: 1-批量确认, 2-批量设置出场日期|操作类型: 1-批量确认, 2-批量设置出场日期|
|exitDate|string(date-time)|false|none|出场日期|出场日期，当type=2时必填|

<h2 id="tocS_ExitProcessTypeDTO">ExitProcessTypeDTO</h2>

<a id="schemaexitprocesstypedto"></a>
<a id="schema_ExitProcessTypeDTO"></a>
<a id="tocSexitprocesstypedto"></a>
<a id="tocsexitprocesstypedto"></a>

```json
{
  "exitId": "string",
  "processType": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|exitId|string|true|none|出场单id|出场单id|
|processType|integer(int32)|true|none|办理流程|办理流程: 1-先交割后结算, 2-交割并结算|

<h2 id="tocS_ContractTerminateVo">ContractTerminateVo</h2>

<a id="schemacontractterminatevo"></a>
<a id="schema_ContractTerminateVo"></a>
<a id="tocScontractterminatevo"></a>
<a id="tocscontractterminatevo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "unionId": "string",
  "approveStatus": 0,
  "processId": "string",
  "isExit": true,
  "isPart": true,
  "bondReceivedAmount": 0,
  "rentReceivedAmount": 0,
  "rentOverdueAmount": 0,
  "receivedPeriod": "string",
  "overduePeriod": "string",
  "terminateType": 0,
  "terminateDate": "2019-08-24T14:15:22Z",
  "terminateReason": "string",
  "otherReasonDesc": "string",
  "hasOtherDeduction": true,
  "otherDeductionDesc": "string",
  "terminateRemark": "string",
  "terminateAttachments": "string",
  "signAttachments": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "roomList": [
    {}
  ],
  "costList": [
    {
      "id": "string",
      "contractId": "string",
      "terminateId": "string",
      "costId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "terminateReceivable": 0,
      "receivedAmount": 0,
      "penaltyAmount": 0,
      "refundAmount": 0,
      "freeId": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "contract": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "contractNo": "string",
    "unionId": "string",
    "version": "string",
    "isCurrent": true,
    "isLatest": true,
    "status": 0,
    "statusTwo": 0,
    "approveStatus": 0,
    "operateType": 0,
    "contractType": 0,
    "ourSigningParty": "string",
    "customerName": "string",
    "unenterNum": 0,
    "signWay": 0,
    "signType": 0,
    "originId": "string",
    "changeFromId": "string",
    "landUsage": "string",
    "signerId": "string",
    "signerName": "string",
    "ownerId": "string",
    "ownerName": "string",
    "contractMode": 0,
    "paperContractNo": "string",
    "signDate": "2019-08-24T14:15:22Z",
    "handoverDate": "2019-08-24T14:15:22Z",
    "contractPurpose": 0,
    "dealChannel": 0,
    "assistantId": "string",
    "assistantName": "string",
    "rentYear": 0,
    "rentMonth": 0,
    "rentDay": 0,
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "effectDate": "2019-08-24T14:15:22Z",
    "invalidDate": "2019-08-24T14:15:22Z",
    "bookingRelType": 0,
    "bondReceivableDate": "string",
    "bondReceivableType": 0,
    "bondPriceType": 0,
    "bondPrice": 0,
    "chargeWay": 0,
    "rentReceivableDate": "string",
    "rentReceivableType": 0,
    "rentTicketPeriod": 0,
    "rentPayPeriod": 0,
    "increaseGap": 0,
    "increaseRate": 0,
    "increaseRule": "string",
    "estimateRevenue": 0,
    "percentageType": 0,
    "fixedPercentage": 0,
    "stepPercentage": "string",
    "revenueType": 0,
    "isIncludePm": true,
    "pmUnitPrice": 0,
    "pmMonthlyPrice": 0,
    "totalPrice": 0,
    "totalBottomPrice": 0,
    "bizTypeId": "string",
    "bizTypeName": "string",
    "lesseeBrand": "string",
    "businessCategory": "string",
    "openDate": "2019-08-24T14:15:22Z",
    "fireRiskCategory": 0,
    "sprinklerSystem": 0,
    "factoryEngaged": "string",
    "deliverDate": "2019-08-24T14:15:22Z",
    "parkingSpaceType": 0,
    "hasParkingFee": true,
    "parkingFeeAmount": 0,
    "venueDeliveryDate": "2019-08-24T14:15:22Z",
    "venueLocation": "string",
    "dailyActivityStartTime": "2019-08-24T14:15:22Z",
    "dailyActivityEndTime": "2019-08-24T14:15:22Z",
    "venuePurpose": "string",
    "otherInfo": "string",
    "contractAttachments": "string",
    "signAttachments": "string",
    "attachmentsPlan": "string",
    "isUploadSignature": true,
    "changeType": "string",
    "processId": "string",
    "changeDate": "2019-08-24T14:15:22Z",
    "changeAttachments": "string",
    "changeExplanation": "string",
    "isSignatureConfirm": true,
    "isPaperConfirm": true,
    "isFinish": true,
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "isDel": true,
    "roomName": "string",
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateId": "string",
    "customer": {
      "id": "string",
      "contractId": "string",
      "customerId": "string",
      "customerType": 0,
      "customerName": "string",
      "address": "string",
      "phone": "string",
      "idType": "string",
      "idNumber": "string",
      "isEmployee": true,
      "creditCode": "string",
      "contactName": "string",
      "contactPhone": "string",
      "contactIdNumber": "string",
      "legalName": "string",
      "paymentAccount": "string",
      "guarantorName": "string",
      "guarantorPhone": "string",
      "guarantorIdType": "string",
      "guarantorIdNumber": "string",
      "guarantorAddress": "string",
      "guarantorIdFront": "string",
      "guarantorIdBack": "string",
      "invoiceTitle": "string",
      "invoiceTaxNumber": "string",
      "invoiceAddress": "string",
      "invoicePhone": "string",
      "invoiceBankName": "string",
      "invoiceAccountNumber": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    },
    "bookings": [
      {
        "id": "string",
        "contractId": "string",
        "bookingId": "string",
        "bookedRoom": "string",
        "bookerName": "string",
        "bookingReceivedAmount": 0,
        "bookingPaymentDate": "2019-08-24T14:15:22Z",
        "transferBondAmount": 0,
        "transferRentAmount": 0,
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "fees": [
      {
        "id": "string",
        "contractId": "string",
        "feeType": 0,
        "freeType": 0,
        "freeRentMonth": 0,
        "freeRentDay": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "isCharge": true,
        "remark": "string",
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "costs": [
      {
        "id": "string",
        "contractId": "string",
        "costType": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "period": 0,
        "customerId": "string",
        "customerName": "string",
        "roomId": "string",
        "roomName": "string",
        "area": 0,
        "subjectId": "string",
        "subjectName": "string",
        "receivableDate": "2019-08-24T14:15:22Z",
        "unitPrice": 0,
        "priceUnit": 0,
        "totalAmount": 0,
        "discountAmount": 0,
        "actualReceivable": 0,
        "receivedAmount": 0,
        "isRevenue": true,
        "isDiscount": true,
        "percentageType": 0,
        "fixedPercentage": 0,
        "stepPercentage": "string",
        "createByName": "string",
        "updateByName": "string",
        "isDel": true,
        "contractStartDate": "2019-08-24T14:15:22Z",
        "rentTicketPeriod": 0
      }
    ],
    "rooms": [
      {
        "id": "string",
        "contractId": "string",
        "roomId": "string",
        "roomName": "string",
        "buildingName": "string",
        "floorName": "string",
        "parcelName": "string",
        "stageName": "string",
        "area": 0,
        "standardUnitPrice": 0,
        "bottomPrice": 0,
        "priceUnit": 0,
        "discount": 0,
        "signedUnitPrice": 0,
        "signedMonthlyPrice": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "bondPriceType": 0,
        "bondPrice": 0,
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ]
  }
}

```

合同退租申请信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||退租ID|
|contractId|string|false|none|合同id|合同id|
|unionId|string|false|none|合同统一id|合同统一id|
|approveStatus|integer(int32)|false|none|审核状态:0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态:0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|processId|string|false|none|流程实例ID|流程实例t_oa_process->id|
|isExit|boolean|false|none|是否同时办理出场:0-否,1-是|是否同时办理出场:0-否,1-是|
|isPart|boolean|false|none|是否部分提前退租:0-否,1-是|是否部分提前退租:0-否,1-是|
|bondReceivedAmount|number|false|none|已收保证金（元）|已收保证金（元）|
|rentReceivedAmount|number|false|none|已收租金（元）|已收租金（元）|
|rentOverdueAmount|number|false|none|逾期租金（元）|逾期租金（元）|
|receivedPeriod|string|false|none|已收账期文字描述|已收账期文字描述|
|overduePeriod|string|false|none|逾期账期文字描述|逾期账期文字描述|
|terminateType|integer(int32)|false|none|退租类型:0-到期退租,1-提前退租|退租类型:0-到期退租,1-提前退租|
|terminateDate|string(date-time)|false|none|退租日期|退租日期|
|terminateReason|string|false|none|退租原因,字典逗号拼接|退租原因,字典逗号拼接|
|otherReasonDesc|string|false|none|其他原因说明|其他原因说明|
|hasOtherDeduction|boolean|false|none|是否有其他扣款|是否有其他扣款|
|otherDeductionDesc|string|false|none|其他扣款描述|其他扣款描述|
|terminateRemark|string|false|none|退租说明|退租说明|
|terminateAttachments|string|false|none|退租附件|退租附件|
|signAttachments|string|false|none|签署附件|签署附件|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|roomList|[object]|false|none||退租房间列表|
|costList|[[ContractTerminateCostVo](#schemacontractterminatecostvo)]|false|none||退租费用列表|
|contract|[ContractVo](#schemacontractvo)|false|none||none|

<h2 id="tocS_ContractTerminateRoomVo">ContractTerminateRoomVo</h2>

<a id="schemacontractterminateroomvo"></a>
<a id="schema_ContractTerminateRoomVo"></a>
<a id="tocScontractterminateroomvo"></a>
<a id="tocscontractterminateroomvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "terminateId": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

退租房间VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|terminateId|string|false|none|退租单id|退租单id|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房间名称|房间名称|
|area|number|false|none|面积（㎡）|面积（㎡）|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ExitVo">ExitVo</h2>

<a id="schemaexitvo"></a>
<a id="schema_ExitVo"></a>
<a id="tocSexitvo"></a>
<a id="tocsexitvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractUnionId": "string",
  "terminateId": "string",
  "refundId": "string",
  "customerId": "string",
  "customerName": "string",
  "processType": 0,
  "progressStatus": 0,
  "isDiscount": true,
  "discountAmount": 0,
  "discountReason": "string",
  "finalAmount": 0,
  "refundProcessType": 0,
  "payeeName": "string",
  "payeeAccount": "string",
  "bankName": "string",
  "licenseStatus": 0,
  "taxCertStatus": 0,
  "refundApplyType": 0,
  "signType": 0,
  "signAttachments": "string",
  "signTime": "2019-08-24T14:15:22Z",
  "copyTime": "2019-08-24T14:15:22Z",
  "copyBy": "string",
  "copyByName": "string",
  "settleTime": "2019-08-24T14:15:22Z",
  "settleBy": "string",
  "settleByName": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractPurpose": 0,
  "terminateType": 0,
  "terminateDate": "2019-08-24T14:15:22Z",
  "terminateRoomName": "string",
  "terminateRoomCount": 0,
  "engineeringCount": 0,
  "financeCount": 0
}

```

出场单基础信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractNo|string|false|none|合同号|合同号|
|contractUnionId|string|false|none|合同统一id|合同统一id|
|terminateId|string|false|none|退租单id|退租单id|
|refundId|string|false|none|退款单id|退款单id|
|customerId|string|false|none|客户id|客户id|
|customerName|string|false|none|承租方，客户名称|承租方，客户名称|
|processType|integer(int32)|false|none|办理流程: 1-先交割后结算, 2-交割并结算|办理流程: 1-先交割后结算, 2-交割并结算|
|progressStatus|integer(int32)|false|none|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废|
|isDiscount|boolean|false|none|是否减免: 0-否, 1-是|是否减免: 0-否, 1-是|
|discountAmount|number|false|none|减免金额|减免金额|
|discountReason|string|false|none|减免原因|减免原因|
|finalAmount|number|false|none|最终费用金额(负为应退)|最终费用金额(负为应退)|
|refundProcessType|integer(int32)|false|none|退款处理方式: 1-退款, 2-暂存客户账户|退款处理方式: 1-退款, 2-暂存客户账户|
|payeeName|string|false|none|收款人|收款人|
|payeeAccount|string|false|none|收款账号|收款账号|
|bankName|string|false|none|开户银行|开户银行|
|licenseStatus|integer(int32)|false|none|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|营业执照办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|taxCertStatus|integer(int32)|false|none|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|税务登记证办理情况: 1-未办理执照, 2-需注销, 3-已注销|
|refundApplyType|integer(int32)|false|none|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|退款申请方式: 1-只结算，暂不退款, 2-结算并申请退款|
|signType|integer(int32)|false|none|签字方式: 1-线上, 2-线下|签字方式: 1-线上, 2-线下|
|signAttachments|string|false|none|签字附件|签字附件|
|signTime|string(date-time)|false|none|签字时间|签字时间|
|copyTime|string(date-time)|false|none|首次复制确认单时间|首次复制确认单时间|
|copyBy|string|false|none|首次复制确认单人|首次复制确认单人|
|copyByName|string|false|none|首次复制确认单人名称|首次复制确认单人名称|
|settleTime|string(date-time)|false|none|结算时间|结算时间|
|settleBy|string|false|none|结算人|结算人|
|settleByName|string|false|none|结算人名称|结算人名称|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractPurpose|integer(int32)|false|none|合同用途,字典|合同用途,字典|
|terminateType|integer(int32)|false|none|退租类型:0-到期退租,1-提前退租|退租类型:0-到期退租,1-提前退租|
|terminateDate|string(date-time)|false|none|退租日期|退租日期|
|terminateRoomName|string|false|none|退租房源|退租房源|
|terminateRoomCount|integer(int32)|false|none|退租房源数|退租房源数|
|engineeringCount|integer(int32)|false|none|工程未确认数量|工程未确认数量|
|financeCount|integer(int32)|false|none|财务未确认数量|财务未确认数量|

<h2 id="tocS_ExitDetailVo">ExitDetailVo</h2>

<a id="schemaexitdetailvo"></a>
<a id="schema_ExitDetailVo"></a>
<a id="tocSexitdetailvo"></a>
<a id="tocsexitdetailvo"></a>

```json
{
  "exitInfo": {
    "id": "string",
    "projectId": "string",
    "contractId": "string",
    "contractNo": "string",
    "contractUnionId": "string",
    "terminateId": "string",
    "refundId": "string",
    "customerId": "string",
    "customerName": "string",
    "processType": 0,
    "progressStatus": 0,
    "isDiscount": true,
    "discountAmount": 0,
    "discountReason": "string",
    "finalAmount": 0,
    "refundProcessType": 0,
    "payeeName": "string",
    "payeeAccount": "string",
    "bankName": "string",
    "licenseStatus": 0,
    "taxCertStatus": 0,
    "refundApplyType": 0,
    "signType": 0,
    "signAttachments": "string",
    "signTime": "2019-08-24T14:15:22Z",
    "copyTime": "2019-08-24T14:15:22Z",
    "copyBy": "string",
    "copyByName": "string",
    "settleTime": "2019-08-24T14:15:22Z",
    "settleBy": "string",
    "settleByName": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true,
    "contractPurpose": 0,
    "terminateType": 0,
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateRoomName": "string",
    "terminateRoomCount": 0,
    "engineeringCount": 0,
    "financeCount": 0
  },
  "contractTerminateInfo": {
    "id": "string",
    "contractId": "string",
    "unionId": "string",
    "approveStatus": 0,
    "processId": "string",
    "isExit": true,
    "isPart": true,
    "bondReceivedAmount": 0,
    "rentReceivedAmount": 0,
    "rentOverdueAmount": 0,
    "receivedPeriod": "string",
    "overduePeriod": "string",
    "terminateType": 0,
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateReason": "string",
    "otherReasonDesc": "string",
    "hasOtherDeduction": true,
    "otherDeductionDesc": "string",
    "terminateRemark": "string",
    "terminateAttachments": "string",
    "signAttachments": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true,
    "roomList": [
      {}
    ],
    "costList": [
      {
        "id": "string",
        "contractId": "string",
        "terminateId": "string",
        "costId": "string",
        "costType": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "customerId": "string",
        "customerName": "string",
        "roomId": "string",
        "roomName": "string",
        "subjectId": "string",
        "subjectName": "string",
        "receivableDate": "2019-08-24T14:15:22Z",
        "totalAmount": 0,
        "discountAmount": 0,
        "actualReceivable": 0,
        "terminateReceivable": 0,
        "receivedAmount": 0,
        "penaltyAmount": 0,
        "refundAmount": 0,
        "freeId": 0,
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "contract": {
      "id": "string",
      "projectId": "string",
      "projectName": "string",
      "contractNo": "string",
      "unionId": "string",
      "version": "string",
      "isCurrent": true,
      "isLatest": true,
      "status": 0,
      "statusTwo": 0,
      "approveStatus": 0,
      "operateType": 0,
      "contractType": 0,
      "ourSigningParty": "string",
      "customerName": "string",
      "unenterNum": 0,
      "signWay": 0,
      "signType": 0,
      "originId": "string",
      "changeFromId": "string",
      "landUsage": "string",
      "signerId": "string",
      "signerName": "string",
      "ownerId": "string",
      "ownerName": "string",
      "contractMode": 0,
      "paperContractNo": "string",
      "signDate": "2019-08-24T14:15:22Z",
      "handoverDate": "2019-08-24T14:15:22Z",
      "contractPurpose": 0,
      "dealChannel": 0,
      "assistantId": "string",
      "assistantName": "string",
      "rentYear": 0,
      "rentMonth": 0,
      "rentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "effectDate": "2019-08-24T14:15:22Z",
      "invalidDate": "2019-08-24T14:15:22Z",
      "bookingRelType": 0,
      "bondReceivableDate": "string",
      "bondReceivableType": 0,
      "bondPriceType": 0,
      "bondPrice": 0,
      "chargeWay": 0,
      "rentReceivableDate": "string",
      "rentReceivableType": 0,
      "rentTicketPeriod": 0,
      "rentPayPeriod": 0,
      "increaseGap": 0,
      "increaseRate": 0,
      "increaseRule": "string",
      "estimateRevenue": 0,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "revenueType": 0,
      "isIncludePm": true,
      "pmUnitPrice": 0,
      "pmMonthlyPrice": 0,
      "totalPrice": 0,
      "totalBottomPrice": 0,
      "bizTypeId": "string",
      "bizTypeName": "string",
      "lesseeBrand": "string",
      "businessCategory": "string",
      "openDate": "2019-08-24T14:15:22Z",
      "fireRiskCategory": 0,
      "sprinklerSystem": 0,
      "factoryEngaged": "string",
      "deliverDate": "2019-08-24T14:15:22Z",
      "parkingSpaceType": 0,
      "hasParkingFee": true,
      "parkingFeeAmount": 0,
      "venueDeliveryDate": "2019-08-24T14:15:22Z",
      "venueLocation": "string",
      "dailyActivityStartTime": "2019-08-24T14:15:22Z",
      "dailyActivityEndTime": "2019-08-24T14:15:22Z",
      "venuePurpose": "string",
      "otherInfo": "string",
      "contractAttachments": "string",
      "signAttachments": "string",
      "attachmentsPlan": "string",
      "isUploadSignature": true,
      "changeType": "string",
      "processId": "string",
      "changeDate": "2019-08-24T14:15:22Z",
      "changeAttachments": "string",
      "changeExplanation": "string",
      "isSignatureConfirm": true,
      "isPaperConfirm": true,
      "isFinish": true,
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "isDel": true,
      "roomName": "string",
      "terminateDate": "2019-08-24T14:15:22Z",
      "terminateId": "string",
      "customer": {
        "id": "string",
        "contractId": "string",
        "customerId": "string",
        "customerType": 0,
        "customerName": "string",
        "address": "string",
        "phone": "string",
        "idType": "string",
        "idNumber": "string",
        "isEmployee": true,
        "creditCode": "string",
        "contactName": "string",
        "contactPhone": "string",
        "contactIdNumber": "string",
        "legalName": "string",
        "paymentAccount": "string",
        "guarantorName": "string",
        "guarantorPhone": "string",
        "guarantorIdType": "string",
        "guarantorIdNumber": "string",
        "guarantorAddress": "string",
        "guarantorIdFront": "string",
        "guarantorIdBack": "string",
        "invoiceTitle": "string",
        "invoiceTaxNumber": "string",
        "invoiceAddress": "string",
        "invoicePhone": "string",
        "invoiceBankName": "string",
        "invoiceAccountNumber": "string",
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      },
      "bookings": [
        {
          "id": "string",
          "contractId": "string",
          "bookingId": "string",
          "bookedRoom": "string",
          "bookerName": "string",
          "bookingReceivedAmount": 0,
          "bookingPaymentDate": "2019-08-24T14:15:22Z",
          "transferBondAmount": 0,
          "transferRentAmount": 0,
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ],
      "fees": [
        {
          "id": "string",
          "contractId": "string",
          "feeType": 0,
          "freeType": 0,
          "freeRentMonth": 0,
          "freeRentDay": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "isCharge": true,
          "remark": "string",
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ],
      "costs": [
        {
          "id": "string",
          "contractId": "string",
          "costType": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "period": 0,
          "customerId": "string",
          "customerName": "string",
          "roomId": "string",
          "roomName": "string",
          "area": 0,
          "subjectId": "string",
          "subjectName": "string",
          "receivableDate": "2019-08-24T14:15:22Z",
          "unitPrice": 0,
          "priceUnit": 0,
          "totalAmount": 0,
          "discountAmount": 0,
          "actualReceivable": 0,
          "receivedAmount": 0,
          "isRevenue": true,
          "isDiscount": true,
          "percentageType": 0,
          "fixedPercentage": 0,
          "stepPercentage": "string",
          "createByName": "string",
          "updateByName": "string",
          "isDel": true,
          "contractStartDate": "2019-08-24T14:15:22Z",
          "rentTicketPeriod": 0
        }
      ],
      "rooms": [
        {
          "id": "string",
          "contractId": "string",
          "roomId": "string",
          "roomName": "string",
          "buildingName": "string",
          "floorName": "string",
          "parcelName": "string",
          "stageName": "string",
          "area": 0,
          "standardUnitPrice": 0,
          "bottomPrice": 0,
          "priceUnit": 0,
          "discount": 0,
          "signedUnitPrice": 0,
          "signedMonthlyPrice": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "bondPriceType": 0,
          "bondPrice": 0,
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ]
    }
  },
  "exitRoomList": [
    {}
  ],
  "exitCostList": [
    {
      "id": "string",
      "exitId": "string",
      "costId": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "payType": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "amount": 0,
      "type": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|exitInfo|[ExitVo](#schemaexitvo)|false|none||出场单基础信息|
|contractTerminateInfo|[ContractTerminateVo](#schemacontractterminatevo)|false|none||退租信息VO|
|exitRoomList|[object]|false|none|出场房间列表|出场房间列表|
|exitCostList|[[ExitCostVo](#schemaexitcostvo)]|false|none|出场费用结算列表|出场费用结算列表|

<h2 id="tocS_ExitRoomVo">ExitRoomVo</h2>

<a id="schemaexitroomvo"></a>
<a id="schema_ExitRoomVo"></a>
<a id="tocSexitroomvo"></a>
<a id="tocsexitroomvo"></a>

```json
{
  "id": "string",
  "exitId": "string",
  "roomId": "string",
  "roomName": "string",
  "propertyType": 0,
  "parcelName": "string",
  "buildingName": "string",
  "exitDate": "2019-08-24T14:15:22Z",
  "rentControl": 0,
  "doorWindowStatus": 0,
  "doorWindowPenalty": 0,
  "keyHandoverStatus": 0,
  "keyPenalty": 0,
  "cleaningStatus": 0,
  "cleaningPenalty": 0,
  "elecMeterReading": 0,
  "coldWaterReading": 0,
  "hotWaterReading": 0,
  "elecFee": 0,
  "waterFee": 0,
  "pmFee": 0,
  "roomPhotos": "string",
  "assetsSituation": "string",
  "remark": "string",
  "isBusinessConfirmed": true,
  "businessConfirmBy": "string",
  "businessConfirmByName": "string",
  "businessConfirmTime": "2019-08-24T14:15:22Z",
  "isFinanceConfirmed": true,
  "financeConfirmBy": "string",
  "financeConfirmByName": "string",
  "financeConfirmTime": "2019-08-24T14:15:22Z",
  "financeConfirmSignature": "string",
  "isEngineeringConfirmed": true,
  "engineeringConfirmBy": "string",
  "engineeringConfirmByName": "string",
  "engineeringConfirmTime": "2019-08-24T14:15:22Z",
  "engineeringConfirmSignature": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "exitRoomAssetsList": [
    {
      "id": "string",
      "exitId": "string",
      "exitRoomId": "string",
      "category": 0,
      "name": "string",
      "specification": "string",
      "count": 0,
      "status": 0,
      "penalty": 0,
      "isAdd": true,
      "remark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

出场房间列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|exitId|string|false|none|离场单id|离场单id|
|roomId|string|false|none|房间id|房间id|
|roomName|string|false|none|房源名称|房源名称|
|propertyType|integer(int32)|false|none|物业类型字典|物业类型字典|
|parcelName|string|false|none|所属地块|所属地块|
|buildingName|string|false|none|所属楼栋|所属楼栋|
|exitDate|string(date-time)|false|none|出场日期|出场日期|
|rentControl|integer(int32)|false|none|租控管理: 1-当前空置已做断电、锁门处理, 2-二次出租无需断电处理,开门设置更新|租控管理: 1-当前空置已做断电、锁门处理, 2-二次出租无需断电处理,开门设置更新|
|doorWindowStatus|integer(int32)|false|none|门、窗、墙体及其他情况: 1-完好, 2-损坏|门、窗、墙体及其他情况: 1-完好, 2-损坏|
|doorWindowPenalty|number|false|none|赔偿金（门、窗、墙体及其他损坏）|赔偿金（门、窗、墙体及其他损坏）|
|keyHandoverStatus|integer(int32)|false|none|钥匙交接情况: 1-已交齐, 2-未交齐|钥匙交接情况: 1-已交齐, 2-未交齐|
|keyPenalty|number|false|none|赔偿金（钥匙未交齐）|赔偿金（钥匙未交齐）|
|cleaningStatus|integer(int32)|false|none|清洁卫生: 1-自行打扫完毕、洁净, 2-保洁及垃圾清理收费|清洁卫生: 1-自行打扫完毕、洁净, 2-保洁及垃圾清理收费|
|cleaningPenalty|number|false|none|保洁及垃圾清理费金额|保洁及垃圾清理费金额|
|elecMeterReading|number|false|none|电度数|电度数|
|coldWaterReading|number|false|none|冷水度数|冷水度数|
|hotWaterReading|number|false|none|热水度数|热水度数|
|elecFee|number|false|none|电费欠费|电费欠费|
|waterFee|number|false|none|水费欠费|水费欠费|
|pmFee|number|false|none|物业欠费|物业欠费|
|roomPhotos|string|false|none|房间照片|房间照片|
|assetsSituation|string|false|none|固定资产、设备设施评估情况|固定资产、设备设施评估情况|
|remark|string|false|none|备注|备注|
|isBusinessConfirmed|boolean|false|none|商服是否确认: 0-否, 1-是|商服是否确认: 0-否, 1-是|
|businessConfirmBy|string|false|none|商服确认人id|商服确认人id|
|businessConfirmByName|string|false|none|商服确认人名称|商服确认人名称|
|businessConfirmTime|string(date-time)|false|none|商服确认时间|商服确认时间|
|isFinanceConfirmed|boolean|false|none|综合或财务是否确认: 0-否, 1-是|综合或财务是否确认: 0-否, 1-是|
|financeConfirmBy|string|false|none|综合或财务确认人id|综合或财务确认人id|
|financeConfirmByName|string|false|none|综合或财务确认人名称|综合或财务确认人名称|
|financeConfirmTime|string(date-time)|false|none|综合或财务确认时间|综合或财务确认时间|
|financeConfirmSignature|string|false|none|综合或财务确认签名|综合或财务确认签名|
|isEngineeringConfirmed|boolean|false|none|工程或客服是否确认: 0-否, 1-是|工程或客服是否确认: 0-否, 1-是|
|engineeringConfirmBy|string|false|none|工程或客服确认人id|工程或客服确认人id|
|engineeringConfirmByName|string|false|none|工程或客服确认人名称|工程或客服确认人名称|
|engineeringConfirmTime|string(date-time)|false|none|工程或客服确认时间|工程或客服确认时间|
|engineeringConfirmSignature|string|false|none|工程或客服确认签名|工程或客服确认签名|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|exitRoomAssetsList|[[ExitRoomAssetsVo](#schemaexitroomassetsvo)]|false|none|出场房间资产列表|出场房间资产列表|

<h2 id="tocS_ExitRoomAssetsVo">ExitRoomAssetsVo</h2>

<a id="schemaexitroomassetsvo"></a>
<a id="schema_ExitRoomAssetsVo"></a>
<a id="tocSexitroomassetsvo"></a>
<a id="tocsexitroomassetsvo"></a>

```json
{
  "id": "string",
  "exitId": "string",
  "exitRoomId": "string",
  "category": 0,
  "name": "string",
  "specification": "string",
  "count": 0,
  "status": 0,
  "penalty": 0,
  "isAdd": true,
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

出场房间资产列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|exitId|string|false|none|离场单id|离场单id|
|exitRoomId|string|false|none|出场记录-房间id|出场记录-房间id|
|category|integer(int32)|false|none|种类|种类|
|name|string|false|none|物品名称|物品名称|
|specification|string|false|none|规格|规格|
|count|integer(int32)|false|none|数量|数量|
|status|integer(int32)|false|none|现状: 1-完好, 2-损坏, 3-丢失|现状: 1-完好, 2-损坏, 3-丢失|
|penalty|number|false|none|赔偿金|赔偿金|
|isAdd|boolean|false|none|是否手动添加的,0-否,1-是|是否手动添加的,0-否,1-是|
|remark|string|false|none|说明|说明|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

