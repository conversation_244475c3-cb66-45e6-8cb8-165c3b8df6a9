<template>
    <div class="entry-detail">
        <section-title title="基本信息" />
        <a-row :gutter="16" class="info-section">
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">合同编号:</span>
                    <span class="value">{{ data?.contractNo }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">合同用途:</span>
                    <span class="value">{{ data?.contractPurpose }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">承租方:</span>
                    <span class="value">{{ data?.tenantName }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">承租类型:</span>
                    <span class="value">{{ data?.leaseType }}</span>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="info-item">
                    <span class="label">租期:</span>
                    <span class="value">{{ data?.leasePeriod }}</span>
                </div>
            </a-col>
        </a-row>

        <section-title title="待进场房源" />
        <a-table :data="houseData" :columns="columns" :pagination="false" :bordered="{ cell: true }">
            <template #operations="{ record }">
                <a-button type="text" size="mini" @click="handleHouseEntry(record)">办理进场</a-button>
            </template>
        </a-table>

        <a-divider />
        <!-- <div class="footer-actions">
            <a-space>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" @click="handleSave">保存</a-button>
            </a-space>
        </div> -->
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, watch } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'

interface HouseItem {
    id: string
    buildingName: string
    houseName: string
    houseArea: number
    houseType: string
    status: number
    [key: string]: any
}

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
})

const emits = defineEmits(['cancel', 'save'])

// 房源数据
const houseData = ref<HouseItem[]>([])

// 表格列定义
const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 80,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '楼栋名称',
        dataIndex: 'buildingName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '房源名称',
        dataIndex: 'houseName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '房源面积(㎡)',
        dataIndex: 'houseArea',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '房源类型',
        dataIndex: 'houseType',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: { record: HouseItem }) => {
            return record.status === 0 ? '待进场' : '已进场'
        }
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 初始化
onMounted(() => {
    initHouseData()
})

// 监听props.data变化
watch(() => props.data, (newVal) => {
    if (newVal) {
        initHouseData()
    }
}, { deep: true })

// 初始化房源数据
const initHouseData = () => {
    if (props.data) {
        if (props.data.selectedHouses && props.data.selectedHouses.length > 0) {
            // 使用选择的房源数据
            houseData.value = props.data.selectedHouses.map((item: any, index: number) => ({
                id: item.id,
                index: index + 1,
                buildingName: item.buildingName,
                houseName: item.houseName,
                houseArea: 0, // 可以根据实际情况设置
                houseType: '待定', // 可以根据实际情况设置
                status: 0,
                plotName: item.plotName,
                floorName: item.floorName
            }))
        } else {
            // 如果没有选择的房源，则使用模拟数据
            houseData.value = generateMockHouseData()
        }
    }
}

// 模拟待进场房源数据
const generateMockHouseData = () => {
    const mockData: HouseItem[] = []
    for (let i = 1; i <= 5; i++) {
        mockData.push({
            id: `house-${i}`,
            index: i,
            buildingName: `A${i}栋`,
            houseName: `A${i}-${100 + i}室`,
            houseArea: 80 + i * 10,
            houseType: i % 2 === 0 ? '商铺' : '办公室',
            status: 0
        })
    }
    return mockData
}

// 办理单个房源进场
const handleHouseEntry = (record: HouseItem) => {
    record.status = 1
}

// 取消
const cancel = () => {
    emits('cancel')
}

// 保存
const handleSave = () => {
    // 检查是否所有房源都已进场
    const allEntered = houseData.value.every(item => item.status === 1)
    
    // 提交数据
    const formData = {
        contractId: props.data?.contractId,
        houses: houseData.value.map(item => ({
            houseId: item.id,
            status: item.status
        }))
    }
    
    emits('save', formData)
}
</script>

<style scoped>
.entry-detail {
    padding: 16px;
}

.info-section {
    margin-bottom: 24px;
}

.info-item {
    margin-bottom: 16px;
    display: flex;
}

.label {
    color: #86909c;
    margin-right: 8px;
    min-width: 80px;
}

.value {
    color: #1d2129;
    flex: 1;
}

.footer-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
</style> 