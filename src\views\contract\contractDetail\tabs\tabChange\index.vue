<template>
    <div class="tab-change">
        <a-space direction="vertical" :size="16" fill>
            <a-row justify="end" v-if="[20, 30].includes(contractData.status) && !contractStore.isApproval">
                <a-button type="primary" @click="handleAdd">新增变更</a-button>
            </a-row>
            <a-table :columns="columns" :data="tableData" :bordered="{ cell: true }">
                <template #operation="{ record }">
                    <a-space>
                        <a-button v-permission="['rent:contract:change:view']" type="text" size="mini" @click="handleDetail(record)">查看</a-button>
                        <a-button v-permission="['rent:contract:change:edit']" v-if="record.status === 10 && record.approveStatus != 2 && record.approveStatus != 1" type="text" size="mini"
                            @click="handleEdit(record)">编辑</a-button>
                        <a-button v-permission="['rent:contract:change:delete']" v-if="record.status === 10 && record.approveStatus != 2 && record.approveStatus != 1" type="text" size="mini"
                            @click="handleDelete(record)">删除</a-button>
                        <a-button v-if="[20].includes(record.status)" type="text" size="mini"
                            @click="handleInvalid(record)">作废</a-button>
                    </a-space>
                </template>
            </a-table>
        </a-space>

        <a-modal v-model:visible="visible" title="变更类型" :on-before-ok="handleBeforeOk" @ok="handleOk">
            <a-checkbox-group direction="vertical" v-model="contractStore.changeType">
                <a-checkbox v-permission="['rent:contract:change:subject']" :value="1">主体变更</a-checkbox>
                <a-checkbox v-permission="['rent:contract:change:terms']" :value="2">条款变更</a-checkbox>
                <a-checkbox v-permission="['rent:contract:change:price']" :value="3">费用条款变更</a-checkbox>
            </a-checkbox-group>
        </a-modal>

        <addContractDrawer ref="addContractDrawerRef" @submit="getChangeList" />
    </div>
</template>

<script setup lang="ts">
import { Message, Modal } from '@arco-design/web-vue'
import { ref } from 'vue'
import addContractDrawer from '@/views/contract/addContractDrawer.vue'
import { useContractStore } from '@/store/modules/contract/index'
import { ContractAddDTO, deleteContract, getContractVersionList, invalidContract } from '@/api/contract'
import { centerMap } from '@/store/modules/center'
import { getDictLabel } from '@/dict'
import { IconExclamationCircleFill } from '@arco-design/web-vue/es/icon'
import dayjs from 'dayjs'

// 获取合同数据
const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)
const changeTypeMap = new Map([
    [1, '主体变更'],
    [2, '条款变更'],
    [3, '费用条款变更']
])

const getChangeType = (value: string) => {
    if (!value) return ''
    return value.split(',').reduce((cur, item) => cur + (cur === '' ? '' : ',') + changeTypeMap.get(Number(item)), '')
}

const columns = computed(() => {
    const cols =  [
        {
            title: '变更类型',
            dataIndex: 'changeType',
            width: 100,
            align: 'center',
            render: ({ record }: { record: any }) => {
                return getChangeType(record.changeType)
            },
            ellipsis: true,
            tooltip: true
        },
        {
            title: '申请时间',
            dataIndex: 'createTime',
            width: 100,
            align: 'center',
            render: ({ record }: { record: any }) => {
                return dayjs(record.createTime).format('YYYY-MM-DD')
            }
        },
        {
            title: '变更生效日期',
            dataIndex: 'changeDate',
            width: 100,
            align: 'center'
        },
        {
            title: '单据状态',
            dataIndex: 'status',
            width: 100,
            align: 'center',
            render: ({ record }: { record: any }) => {
                return getDictLabel('contract_status', record.status.toString())
            }
        },
        {
            title: '审批状态',
            dataIndex: 'approveStatus',
            width: 100,
            align: 'center',
            render: ({ record }: { record: any }) => {
                return getDictLabel('contract_approve_status', record.approveStatus)
            }
        },
        {
            title: '操作',
            dataIndex: 'operation',
            slotName: 'operation',
            width: 80,
            fixed: 'right',
            align: 'center'
        }
    ]
    if (contractStore.isApproval) {
        return cols.filter(item => item.dataIndex !== 'operation')
    }
    return cols
})

const tableData = ref<any[]>([])

const handleAdd = () => {
    contractStore.changeType = []
    visible.value = true
}

const visible = ref(false)

const handleBeforeOk = () => {
    if (contractStore.changeType.length === 0) {
        Message.error('请选择变更类型')
        return false
    }
    return true
}
const addContractDrawerRef = ref()
const handleOk = () => {
    addContractDrawerRef.value?.open('change', '', contractData.value, undefined, false)
}

const handleDetail = (record: any) => {
    addContractDrawerRef.value?.open('changeDetail', '', record, undefined, true)
}
const handleEdit = (record: any) => {
    addContractDrawerRef.value?.open('change', '', record, undefined, true)
}
const handleDelete = (record: any) => {
    // 只有草稿状态的合同才能删除
    if (record.status !== 10) {
        Message.warning('只有草稿状态的合同才能删除')
        return
    }

    Modal.info({
        title: '提示',
        content: () => {
            return h('div', {
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                }
            }, [h(IconExclamationCircleFill, {
                style: {
                    color: 'rgb(var(--warning-6))',
                    fontSize: '22px'
                }
            }), '是否确认执行当前删除操作？'])
        },
        okText: '确认',
        cancelText: '取消',
        hideCancel: false,
        alignCenter: false,
        simple: false,
        maskClosable: false,
        modalClass: 'confirm-popup',
        width: 400,
        onOk: async () => {
            const loading = Message.loading('删除中...')
            try {
                await deleteContract(record.id)
                loading.close()
                Message.success('删除成功')
                // 重新加载数据
                getChangeList()
            } catch (error: any) {
                loading.close()
                console.error('删除合同失败:', error)
            }
        }
    })
}
const handleInvalid = (record: any) => {
    // 只有待生效状态的合同才能作废
    if (record.status !== 20) {
        Message.warning('只有待生效状态的合同才能作废')
        return
    }

    Modal.info({
        title: '提示',
        content: () => {
            return h('div', {
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                }
            }, [h(IconExclamationCircleFill, {
                style: {
                    color: 'rgb(var(--warning-6))',
                    fontSize: '22px'
                }
            }), '是否确认执行当前作废操作？'])
        },
        okText: '确认作废',
        cancelText: '取消',
        hideCancel: false,
        alignCenter: false,
        simple: false,
        maskClosable: false,
        modalClass: 'confirm-popup',
        width: 400,
        onOk: async () => {
            const loading = Message.loading('作废中...')
            try {
                await invalidContract(record.id)
                loading.close()
                Message.success('作废成功')
                // 重新加载数据
                getChangeList()
            } catch (error: any) {
                loading.close()
                console.error('作废合同失败:', error)
            }
        }
    })
}

const getChangeList = async () => {
    const { data } = await getContractVersionList(contractData.value.id as string)
    tableData.value = data ? data.filter(item => item.operateType === 1) : []
}

watch(() => contractData.value.id, (newVal) => {
    if (newVal) {
        getChangeList()
    }
}, { deep: true, immediate: true })
</script>

<style scoped></style>