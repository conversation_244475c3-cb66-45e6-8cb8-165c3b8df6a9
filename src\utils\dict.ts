import useDictStore from '@/store/modules/dict';
import { getDicts, getDictsTree } from '@/api/system/dict';
import { ref, toRefs } from 'vue';

/**
 * 获取字典数据
 */
export function useDict(...args: string[]) {
    const res: any = ref({});
    return (() => {
        args.forEach((dictType, index) => {
            (res.value as Record<string, any>)[dictType] = [];
            const dicts = useDictStore().getDict(dictType);
            if (dicts) {
                (res.value as Record<string, any>)[dictType] = dicts;
            } else {
                getDicts(dictType).then((resp: any) => {
                    // 递归处理字典数据
                    const transformDictData = (data: any[], parentValue?: string): any[] => {
                        return data.map((item: any) => ({
                            label: item.dictLabel,
                            value: item.dictValue,
                            elTagType: item.listClass,
                            elTagClass: item.cssClass,
                            parentCode: item.parentCode || parentValue || null,
                            children: item.childList ? transformDictData(item.childList, item.dictValue) : null
                        }));
                    };
                    
                    (res.value as Record<string, any>)[dictType] = transformDictData(resp.data);
                    useDictStore().setDict(
                        dictType,
                        (res.value as Record<string, any>)[dictType]
                    );
                });
            }
        });
        return toRefs(res.value);
    })();
}

/**
 * 获取字典数据(树形)
 */
export function useDictTree(...args: string[]) {
    const res: any = ref({});
    return (() => {
        args.forEach((dictType, index) => {
            (res.value as Record<string, any>)[dictType] = [];
            const dicts = useDictStore().getDict(dictType);
            if (dicts) {
                (res.value as Record<string, any>)[dictType] = dicts;
            } else {
                getDictsTree(dictType).then((resp: any) => {
                    // 递归处理字典数据
                    const transformDictData = (data: any[], parentValue?: string): any[] => {
                        return data.map((item: any) => ({
                            label: item.dictLabel,
                            value: item.dictValue,
                            elTagType: item.listClass,
                            elTagClass: item.cssClass,
                            parentCode: item.parentCode || parentValue || null,
                            children: item.childList ? transformDictData(item.childList, item.dictValue) : null
                        }));
                    };
                    
                    (res.value as Record<string, any>)[dictType] = transformDictData(resp.data);
                    useDictStore().setDict(
                        dictType,
                        (res.value as Record<string, any>)[dictType]
                    );
                });
            }
        });
        return toRefs(res.value);
    })();
}

/**
 * 同步获取字典数据
 */
export async function useDictSync(...args: string[]) {
    const res: Record<string, any> = {};
    
    // 创建一个Promise数组来存储所有字典请求
    const promises = args.map(async (dictType) => {
        // 先尝试从缓存获取
        const dicts = useDictStore().getDict(dictType);
        if (dicts) {
            res[dictType] = dicts;
            return;
        }
        
        // 缓存中没有，发起请求
        try {
            const resp = await getDicts(dictType);
            // 递归处理字典数据
            const transformDictData = (data: any[], parentValue?: string): any[] => {
                return data.map((item: any) => ({
                    label: item.dictLabel,
                    dictLabel: item.dictLabel,
                    value: item.dictValue,
                    dictValue: item.dictValue,
                    elTagType: item.listClass,
                    elTagClass: item.cssClass,
                    dictCode: item.dictCode,
                    parentCode: item.parentCode || parentValue || null,
                    childList: item.childList ? transformDictData(item.childList, item.dictValue) : null,
                    children: item.childList ? transformDictData(item.childList, item.dictValue) : null
                }));
            };
            
            const dictData = transformDictData(resp.data);
            
            // 存入缓存
            useDictStore().setDict(dictType, dictData);
            res[dictType] = dictData;
        } catch (error) {
            console.error(`获取字典[${dictType}]失败:`, error);
            res[dictType] = [];
        }
    });
    
    // 等待所有请求完成
    await Promise.all(promises);
    
    return res;
}

// 递归获取字典标签
export const getDictLabel = (dictArray: any[], value: string | number): string => {
    for (const node of dictArray) {
        if (node.value === value) {
            return node.label;
        } else if (node.children) {
            const label = getDictLabel(node.children, value);
            if (label) {
                return label;
            }
        }
    }
    return '';
}

// 根据parentCode获取子字典项
export const getDictByParentCode = (dictArray: any[], parentCode: string | number): any[] => {
    return dictArray.filter(item => item.parentCode === parentCode);
}