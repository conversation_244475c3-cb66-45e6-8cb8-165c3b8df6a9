<template>
    <div class="select-entry-houses">
        <!-- 筛选条件 -->
        <a-form :model="formModel" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" label-align="right">
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item field="houseName" label="房源名称">
                        <a-input v-model="formModel.houseName" placeholder="请输入房源名称" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
        </a-form>

        <!-- 表格区域 -->
        <a-table row-key="id" :loading="loading" :columns="columns" :data="tableData" :bordered="{ cell: true }"
            :row-selection="{ type: 'checkbox', showCheckedAll: true }" @selection-change="selectionChange"></a-table>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getUnenteredRooms } from '@/api/entry'

// 定义类型
interface HouseItem {
    id: string
    houseName: string
    plotName: string
    buildingName: string
    floorName: string
    checked?: boolean
    [key: string]: any
}

// 定义props和emits
const props = defineProps({
    contractId: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['cancel', 'next', 'select'])

// 表格数据
const loading = ref(false)
const allTableData = ref<HouseItem[]>([]) // 存储所有数据
const tableData = ref<HouseItem[]>([]) // 当前页显示的数据
const selectedRows = ref<HouseItem[]>([])

// 表格列定义
const columns = [
    {
        title: '房源名称',
        dataIndex: 'roomName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '所属地块',
        dataIndex: 'parcelName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '楼栋',
        dataIndex: 'buildingName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '楼层',
        dataIndex: 'floorName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 查询表单
const formModel = reactive({
    houseName: ''
})

// 生命周期
onMounted(() => {
    fetchTableData()
})

// 获取表格数据
const fetchTableData = async () => {
    if (!props.contractId) {
        console.warn('缺少合同ID')
        return
    }

    loading.value = true
    try {
        const params = {
            contractId: props.contractId,
            roomName: formModel.houseName
        }
        const res = await getUnenteredRooms(params)
        if (res && res.code === 200) {
            // 根据接口返回的数据结构进行适配
            tableData.value = (res.data || []).map((item: any) => ({
                id: item.id,
                roomId: item.roomId,
                roomName: item.roomName,
                propertyType: item.propertyType || '',
                parcelName: item.parcelName || '',
                buildingName: item.buildingName || '',
                floorName: item.floorName || '',
            }))
            if (!formModel.houseName) {
                allTableData.value = JSON.parse(JSON.stringify(tableData.value))
            }
        } else {
            tableData.value = []
        }
    } catch (error) {
        console.error('获取未进场房源失败', error)
        tableData.value = []
    } finally {
        loading.value = false
    }
}

// 查询
const search = () => {
    fetchTableData()
}

// 重置
const reset = () => {
    formModel.houseName = ''
    fetchTableData()
}

const selectedRowKeys = ref<string[]>([])
const selectionChange = (keys: string[]) => {
    selectedRowKeys.value = keys
    // selectedRows.value = allTableData.value.filter(item => selectedRowKeys.includes(item.roomId))
}

// 暴露方法，供父组件调用
defineExpose({
    getSelectedHouses: () => selectedRowKeys.value
})
</script>

<style scoped></style>