<template>
    <div class="test-container">
        <a-card title="退款详情组件数据类型测试">
            <a-space direction="vertical" size="large" style="width: 100%">
                <!-- 测试数据展示 -->
                <a-card title="测试数据" size="small">
                    <a-descriptions :column="2" bordered>
                        <a-descriptions-item label="退款类型（原始）">{{ testData.refundType }} ({{ typeof testData.refundType }})</a-descriptions-item>
                        <a-descriptions-item label="退款方式（原始）">{{ testData.refundWay }} ({{ typeof testData.refundWay }})</a-descriptions-item>
                        <a-descriptions-item label="退款类型（显示）">{{ getRefundTypeName(testData.refundType) }}</a-descriptions-item>
                        <a-descriptions-item label="退款方式（显示）">{{ getRefundMethodName(testData.refundWay) }}</a-descriptions-item>
                        <a-descriptions-item label="退款申请说明" :span="2">{{ testData.refundRemark || '暂无' }}</a-descriptions-item>
                    </a-descriptions>
                </a-card>

                <!-- 操作按钮 -->
                <a-space>
                    <a-button type="primary" @click="handleViewDetail">查看详情</a-button>
                    <a-button @click="handleEditDetail">编辑详情</a-button>
                    <a-button @click="switchTestData">切换测试数据</a-button>
                </a-space>

                <!-- 数据类型说明 -->
                <a-alert 
                    type="info" 
                    title="数据类型说明"
                    description="退款类型和退款方式应该使用数字类型：0-退租退款/原路退回，1-退定退款/银行转账，2-未明流水退款"
                />
            </a-space>
        </a-card>

        <!-- 退款详情组件 -->
        <refund-detail ref="refundDetailRef" @refresh="handleRefresh" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import RefundDetail from './views/financeManage/components/refundDetail.vue'
import type { FinancialRefundVo } from './api/refundManage'

// 退款详情组件引用
const refundDetailRef = ref()

// 测试数据
const testDataList = [
    {
        id: 'test001',
        projectName: '测试项目A',
        refundType: 0, // 数字类型：退租退款
        refundWay: 0,  // 数字类型：原路退回
        bizId: 'biz001',
        refundTarget: '测试合同001',
        applyTime: '2025-01-15 10:00:00',
        refundAmount: 5000,
        feeType: '保证金',
        receiverName: '张三',
        receiverBank: '中国银行',
        receiverAccount: '****************',
        refundRemark: '正常退租，退还保证金',
        refundStatus: 0, // 草稿
        approveStatus: 0, // 草稿
        attachments: '[]'
    },
    {
        id: 'test002',
        projectName: '测试项目B',
        refundType: 1, // 数字类型：退定退款
        refundWay: 1,  // 数字类型：银行转账
        bizId: 'biz002',
        refundTarget: '测试订单002',
        applyTime: '2025-01-15 14:00:00',
        refundAmount: 2000,
        feeType: '定金',
        receiverName: '李四',
        receiverBank: '工商银行',
        receiverAccount: '****************',
        refundRemark: '客户取消预定，退还定金',
        refundStatus: 1, // 待退款
        approveStatus: 2, // 审批通过
        attachments: '[]'
    },
    {
        id: 'test003',
        projectName: '测试项目C',
        refundType: 2, // 数字类型：未明流水退款
        refundWay: 0,  // 数字类型：原路退回
        bizId: 'biz003',
        refundTarget: '测试流水003',
        applyTime: '2025-01-15 16:00:00',
        refundAmount: 1500,
        feeType: '未明流水',
        receiverName: '王五',
        receiverBank: '建设银行',
        receiverAccount: '****************',
        refundRemark: '未明流水退款处理',
        refundStatus: 2, // 已退款
        approveStatus: 2, // 审批通过
        attachments: '[]'
    }
]

const currentTestIndex = ref(0)
const testData = reactive<FinancialRefundVo>(testDataList[0])

// 退款类型名称映射
const getRefundTypeName = (type: number | undefined) => {
    const typeMap: Record<number, string> = {
        0: '退租退款',
        1: '退定退款',
        2: '未明流水退款'
    }
    return typeMap[type || 0] || '未知类型'
}

// 退款方式名称映射
const getRefundMethodName = (method: number | undefined) => {
    const methodMap: Record<number, string> = {
        0: '原路退回',
        1: '银行转账'
    }
    return methodMap[method || 0] || '未知方式'
}

// 查看详情
const handleViewDetail = () => {
    console.log('查看详情 - 数据类型检查:', {
        refundType: testData.refundType,
        refundTypeType: typeof testData.refundType,
        refundWay: testData.refundWay,
        refundWayType: typeof testData.refundWay
    })
    refundDetailRef.value?.open(testData, 'view')
}

// 编辑详情
const handleEditDetail = () => {
    console.log('编辑详情 - 数据类型检查:', {
        refundType: testData.refundType,
        refundTypeType: typeof testData.refundType,
        refundWay: testData.refundWay,
        refundWayType: typeof testData.refundWay
    })
    refundDetailRef.value?.open(testData, 'edit')
}

// 切换测试数据
const switchTestData = () => {
    currentTestIndex.value = (currentTestIndex.value + 1) % testDataList.length
    Object.assign(testData, testDataList[currentTestIndex.value])
    Message.info(`切换到测试数据 ${currentTestIndex.value + 1}`)
}

// 刷新回调
const handleRefresh = () => {
    Message.success('数据已刷新')
    console.log('刷新后的数据类型:', {
        refundType: testData.refundType,
        refundTypeType: typeof testData.refundType,
        refundWay: testData.refundWay,
        refundWayType: typeof testData.refundWay
    })
}
</script>

<style scoped lang="less">
.test-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}
</style> 