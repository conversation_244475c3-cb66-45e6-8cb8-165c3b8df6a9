<template>
    <a-drawer
      v-model:visible="visible"
      title="房源拆分"
      @cancel="handleCancel"
      :footer="!isReadOnly"
      class="common-drawer"
    >
    <div class="split-property">
        <!-- 原房源信息 -->
        <div class="section">
          <section-title :title="'原房源信息'" class="first-child"/>
          <a-table
            :columns="originalColumns"
            :data="[originalProperty]"
            :pagination="false"
            :bordered="{ cell: true }"
            :scroll="{x: 1}"
          >
            <template #propertyType="{ record }">
              {{ getDictLabel(propertyTypeOptions, record.propertyType) }}
            </template>
            <template #operations="{ record, index }">
              <a-link @click="handleViewDetail">详情</a-link>
            </template>
          </a-table>
        </div>

        <!-- 拆分后房源信息 -->
        <div class="section">
          <div class="section-header">
            <section-title title="拆分后房源信息" />
            <a-button type="text" @click="handleAddProperty" v-if="!isReadOnly">
              <template #icon><icon-plus /></template>
              添加房源
            </a-button>
          </div>
          <a-table
            :columns="splitColumns"
            :data="splitProperties"
            :pagination="false"
            :bordered="{ cell: true }"
            :scroll="{x: 1}"
            row-key="index"
          >
            <template #roomName="{ record }">
              <a-input :disabled="isReadOnly" v-model="record.roomName" placeholder="请输入房源名称" />
            </template>
            <template #propertyType="{ record }">
              <a-tree-select
                  v-model="record.propertyType"
                  :data="propertyTypeOptions"
                  placeholder="请选择物业类型"
                  allow-clear
                  :disabled="isReadOnly"
                  :field-names="{
                      key: 'dictValue',
                      title: 'dictLabel',
                      children: 'childList'
                  }"
              />
            </template>
            <template #buildArea="{ record }">
              <a-input-number 
                v-model="record.buildArea" 
                placeholder="请输入建筑面积" 
                :precision="2" 
                :min="0"
                :disabled="isReadOnly"
                style="width: 120px"
              />
            </template>
            <template #innerArea="{ record }">
              <a-input-number 
                v-model="record.innerArea" 
                placeholder="请输入套内面积" 
                :precision="2" 
                :min="0"
                :disabled="isReadOnly"
                style="width: 120px"
              />
            </template>
            <template #operations="{ record, rowIndex }">
              <a-space>
                <a-link @click="() => handleMoreInfo(rowIndex)">{{ isReadOnly ? '查看更多' : '更多信息编辑' }}</a-link>
                <a-button 
                  v-if="splitProperties.length > 1 && !isReadOnly"
                  type="text" 
                  size="mini"
                  status="danger" 
                  @click="() => handleRemoveProperty(rowIndex)"
                >
                  删除
                </a-button>
              </a-space>
            </template>
          </a-table>
        </div>
        <!-- 生效时间 -->
        <div class="section">
          <section-title title="生效时间" />
          <a-form label-align="right" :model="formData"
          layout="horizontal"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          auto-label-width>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="effectiveType" label="生效方式">
                  <a-radio-group v-model="formData.effectiveType" :disabled="isReadOnly">
                    <a-radio value="immediate">立即生效</a-radio>
                    <a-radio value="scheduled">到期生效</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
              <a-col :span="8" v-if="formData.effectiveType === 'scheduled'">
                <a-form-item field="effectiveDate" label="生效日期" validate-trigger="blur" :rules="[{ required: true, message: '请选择生效日期' }]">
                  <a-date-picker 
                    v-model="formData.effectiveDate" 
                    :disabled-date="disabledDate"
                    :disabled="isReadOnly"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8" v-if="maxContractEndDate">
                <div class="contract-end-date"> （合同结束日期：{{ maxContractEndDate }}）</div>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="16">
                <a-form-item field="adjustReason" label="调整原因">
                  <a-textarea
                    v-model="formData.adjustReason"
                    placeholder="请输入调整原因"
                    :max-length="100"
                    show-word-limit
                    allow-clear
                    :auto-size="{ minRows: 3, maxRows: 5 }"
                    :disabled="isReadOnly"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="16">
                <a-form-item field="attachments" label="相关附件">
                  <upload-file v-model="formData.attachments" :disabled="isReadOnly" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>
      <!-- 底部按钮 -->
      <template #footer>
        <div class="drawer-footer">
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button @click="() => handleConfirm(1)">暂存</a-button>
            <a-button type="primary" @click="() => handleConfirm(2)">提交</a-button>
          </a-space>
        </div>
      </template>
    </a-drawer>
    <AddPropertyDrawer
      v-if="showAddPropertyDrawer"
      ref="addPropertyDrawerRef"
      @confirm="handleAddPropertyConfirm"
      @cancel="showAddPropertyDrawer = false"
    />
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import uploadFile from '@/components/upload/uploadFile.vue'
import { deleteMergeSplitRecord, splitRoom, getRoomDetail, getSplitDetail, getMaxContractEndDate } from '@/api/room'
import { useDictSync, getDictLabel } from '@/utils/dict'
import { settings } from 'nprogress'
import AddPropertyDrawer from './addPropertyDrawer.vue'

interface Property {
  id?: string
  roomName?: string
  propertyType?: string
  buildArea?: number
  innerArea?: number
  layout?: string
  parcelName?: string
  buildingName?: string
  floorName?: string
  projectId?: string
  parcelId?: string
  buildingId?: string
  floorId?: string
  rentAreaType?: number
  rentArea?: number
  index?: number
  [key: string]: any
}

interface OriginalProperty extends Property {
  contractEndDate?: string
}
// 字典数据
interface DictData {
    dictCode: string;
    dictLabel: string;
    dictValue: string;
    dictSort: number;
    parentCode?: string;
    childList?: DictData[];
    [key: string]: any;
}

const propertyTypeOptions = ref<DictData[]>([])
const visible = ref(false)

// 原房源信息表格列定义
const originalColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '房源名称', dataIndex: 'roomName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼栋', dataIndex: 'buildingName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼层', dataIndex: 'floorName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '物业类型', dataIndex: 'propertyType', slotName: 'propertyType', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '建筑面积(㎡)', dataIndex: 'buildArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '套内面积(㎡)', dataIndex: 'innerArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '计租面积(㎡)', dataIndex: 'rentArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '操作', slotName: 'operations', width: 160, ellipsis: false, tooltip: false, align: 'center',fixed: 'right' }
]

// 拆分后房源表格列定义
const splitColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '房源名称', slotName: 'roomName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '楼栋', dataIndex: 'buildingName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '楼层', dataIndex: 'floorName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '物业类型', slotName: 'propertyType', width: 160, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '建筑面积(㎡)', slotName: 'buildArea', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '套内面积(㎡)', slotName: 'innerArea', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '计租面积(㎡)', dataIndex: 'rentArea', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '操作', slotName: 'operations', width: 260, ellipsis: false, tooltip: false, align: 'center',fixed: 'right' }
]

// 原房源信息
const originalProperty = ref<OriginalProperty>({
  roomName: '',
  propertyType: '',
  buildArea: 0,
  innerArea: 0,
  parcelName: '',
  buildingName: '',
  floorName: '',
  contractEndDate: ''
})

// 添加当前选择的项目信息
const currentSelection = ref({
  projectId: '',
  projectName: '',
  blockId: '',
  blockName: '',
  buildingId: '',
  buildingName: ''
})

// 拆分后的房源列表
const splitProperties = ref<Property[]>([])

// 更改 effectiveForm 为更通用的名称
const formData = ref({
  effectiveType: 'immediate',
  effectiveDate: null as string | null,
  adjustReason: '',
  attachments: '' as string
})

const getPropertyTypeDicts = async () => {
    try {
        const dictData = await useDictSync('property_type')
        if (dictData.property_type) {
            // 处理树形结构数据
            const dictList = dictData.property_type as DictData[]
            // 组织成树形结构
            const treeData = dictList.filter(item => !item.parentCode)
            treeData.forEach(parent => {
                parent.childList = dictList.filter(child => child.parentCode === parent.dictCode)
            })
            propertyTypeOptions.value = treeData
        }
    } catch (error) {
        console.error('获取物业类型字典数据失败:', error)
    }
}

const emit = defineEmits(['confirm','cancel'])

const isReadOnly = ref(false)

// 当前编辑的拆分记录ID
const currentSplitId = ref<string | null>(null)



// 记录最大合同结束日期
const maxContractEndDate = ref<string | null>(null)

// 初始化拆分房源
const initSplitProperties = async () => {
  // 如果有原房源ID，先获取完整的房源详情
  if (originalProperty.value.id) {
    try {
      const response = await getRoomDetail({ id: originalProperty.value.id })
      if (response && response.data) {
        // 将接口返回的详情数据更新到原房源对象中
        const detailData = response.data
        // 保留原始ID
        const originalId = originalProperty.value.id
        
        // 更新原房源信息，保留原始index值
        originalProperty.value = { 
          ...detailData,
          id: originalId,
          index: 1
        }
        
        // 获取最大合同结束日期
        await loadMaxContractEndDate(originalId)
      }
    } catch (error) {
      console.error('获取房源详情失败:', error)
    }
  }

  const originalArea = originalProperty.value.buildArea || 0
  const halfArea = originalArea / 2
  
  // 创建拆分房源时，复制原房源的所有字段（除了id）
  const commonRoomProps = { 
    ...originalProperty.value,
    // 只清除id，其他所有字段都保留
    id: undefined,
    // 重新设置面积
    buildArea: halfArea,
    rentArea: halfArea
  }
  
  splitProperties.value = [
    {
      ...commonRoomProps,
      roomName: `${originalProperty.value.roomName || ''}-1`,
      index: 1
    },
    {
      ...commonRoomProps,
      roomName: `${originalProperty.value.roomName || ''}-2`,
      index: 2
    }
  ]
}

// 计算总面积
const totalSplitArea = computed(() => {
  return splitProperties.value.reduce((sum, property) => sum + (property.buildArea || 0), 0)
})

// 校验面积是否合法
const isAreaValid = computed(() => {
  const originalArea = originalProperty.value.buildArea || 0
  return Math.abs(totalSplitArea.value - originalArea) < 0.01
})

// 加载最大合同结束日期
const loadMaxContractEndDate = async (roomId: string) => {
  try {
    const response = await getMaxContractEndDate([roomId])
    if (response && response.data) {
      maxContractEndDate.value = response.data
    } else {
      maxContractEndDate.value = null
    }
  } catch (error) {
    console.error('获取最大合同结束日期失败:', error)
    maxContractEndDate.value = null
  }
}

// 添加拆分房源
const handleAddProperty = () => {
  const newIndex = splitProperties.value.length + 1
  
  // 复制原房源的所有字段到新房源（除了id）
  const newProperty = { 
    ...originalProperty.value,
    // 只清除id，其他所有字段都保留
    id: undefined,
    // 初始面积设为0
    buildArea: 0,
    rentArea: 0,
    innerArea: 0,
    // 新房源名称
    roomName: `${originalProperty.value.roomName}-${newIndex}`,
    index: newIndex
  }
  
  splitProperties.value.push(newProperty)
}

// 删除拆分房源
const handleRemoveProperty = (index: number) => {
  console.log(index)
  
  // 直接从列表中移除，无需调用删除接口
  splitProperties.value.splice(index, 1)
  
  // 重新计算序号
  splitProperties.value.forEach((item, idx) => {
    item.index = idx + 1
  })
}

// 引入房源详情组件
const addPropertyDrawerRef = ref()
const showAddPropertyDrawer = ref(false)

// 查看原房源详情
const handleViewDetail = () => {
  if (originalProperty.value.id) {
    showAddPropertyDrawer.value = true
    nextTick(() => {
      addPropertyDrawerRef.value?.show('view', originalProperty.value.id, {
        projectId: originalProperty.value.projectId,
        projectName: currentSelection.value.projectName,
        blockId: originalProperty.value.parcelId,
        blockName: originalProperty.value.parcelName,
        buildingId: originalProperty.value.buildingId,
        buildingName: originalProperty.value.buildingName
      })
    })
  } else {
    Message.error('原房源ID不存在')
  }
}

// 查看更多信息
const handleMoreInfo = (index: number) => {
  console.log('点击更多信息, 行索引:', index)
  
  // 验证索引的有效性
  if (index < 0 || index >= splitProperties.value.length) {
    console.error('无效的行索引:', index, '当前拆分房源数量:', splitProperties.value.length)
    Message.error('无效的数据索引')
    return
  }
  
  const property = splitProperties.value[index]
  console.log('获取到的房源数据:', property)
  
  // 使用更通用的模式名称'adjust-edit'，并根据当前抽屉是否为只读模式决定传递只读或编辑模式
  const mode = isReadOnly.value ? 'adjust-view' : 'adjust-edit'
  
  // 打开房源信息弹框，传递房源数据
  showAddPropertyDrawer.value = true
  nextTick(() => {
    const selection = {
      projectId: property.projectId || originalProperty.value.projectId,
      projectName: currentSelection.value.projectName,
      blockId: property.parcelId || originalProperty.value.parcelId,
      blockName: property.parcelName || originalProperty.value.parcelName,
      buildingId: property.buildingId || originalProperty.value.buildingId,
      buildingName: property.buildingName || originalProperty.value.buildingName
    }
    
    console.log('传递给房源编辑的数据:', {
      mode: mode,
      selection: selection,
      property: property,
      index: index
    })
    
    addPropertyDrawerRef.value?.show(mode, null, selection, property, index)
  })
}

// 禁用日期
const disabledDate = (date: Date) => {
  // 如果有最大合同结束日期，则只能选择该日期之后的日期
  if (maxContractEndDate.value) {
    const contractDate = new Date(maxContractEndDate.value)
    return date.getTime() <= contractDate.getTime()
  }
  // 否则只禁用今天之前的日期
  return date.getTime() <= Date.now()
}

// 取消
const handleCancel = () => {
  setTimeout(() => {
    emit('cancel')
  }, 300);
  visible.value = false
  // Reset form data
  splitProperties.value = []
  formData.value = {
    effectiveType: 'immediate',
    effectiveDate: null,
    adjustReason: '',
    attachments: ''
  }
}

// 确认
const handleConfirm = async (submitType: number = 2) => {
  if (!isAreaValid.value) {
    Message.error('拆分后房源面积之和需等于原房源面积')
    return
  }
  
  try {
    // 构造拆分请求参数 - 直接使用splitProperties
    const params = {
      id:'',
      sourceRoomId: originalProperty.value.id,
      // 可以直接使用splitProperties，字段名已经和API保持一致
      targetRooms: splitProperties.value,
      effectType: formData.value.effectiveType === 'immediate' ? 1 : 2,
      effectDate: formData.value.effectiveType === 'immediate' ? '' : (formData.value.effectiveDate || ''),
      adjustmentReason: formData.value.adjustReason,
      attachments: formData.value.attachments || null,
      submitType: submitType // 提交类型 1-暂存 2-提交
    }
    
    // 如果是编辑模式（有currentSplitId），则添加id参数
    if (currentSplitId.value) {
      params.id = currentSplitId.value
    }
    
    // 调用拆分接口
    const response = await splitRoom(params)
    Message.success(submitType === 1 ? '暂存成功' : '拆分成功')
    if (response && response.code === 200) {
      setTimeout(() => {
        emit('confirm')
      }, 300);
      visible.value = false
    }
  } catch (error) {
    console.error('拆分失败:', error)
  }
}

// 加载拆分详情
const loadSplitDetail = async (splitId: string) => {
  try {
    // 保存当前编辑的拆分记录ID
    currentSplitId.value = splitId
    
    const response = await getSplitDetail({ id: splitId })
    if (response && response.data) {
      const detailData = response.data
      
      // 更新原房源信息
      if (detailData.sourceRoomVo) {
        originalProperty.value = { 
          ...detailData.sourceRoomVo,
          index: 1
        }
      }
      
      // 更新拆分后房源列表
      if (detailData.targetRooms && Array.isArray(detailData.targetRooms)) {
        splitProperties.value = detailData.targetRooms.map((room, index) => ({
          ...room,
          index: index + 1
        }))
      }
      
      // 更新表单数据
      formData.value = {
        effectiveType: detailData.effectType === 1 ? 'immediate' : 'scheduled',
        effectiveDate: detailData.effectDate || null,
        adjustReason: detailData.adjustmentReason || '',
        attachments: detailData.attachments || ''
      }
      
      return true
    }
    return false
  } catch (error) {
    console.error('获取拆分详情失败:', error)
    Message.error('获取拆分详情失败')
    return false
  }
}

// Show method
const show = async (propertyData: { id?: string, splitId?: string }, readOnly = false, selectionData?: any) => {
  isReadOnly.value = readOnly
  
  // 重置当前编辑的拆分记录ID和最大合同结束日期
  currentSplitId.value = null
  maxContractEndDate.value = null
  
  // 加载字典数据
  await getPropertyTypeDicts()
  
  if(selectionData){
    currentSelection.value = selectionData
  }
  
  if (propertyData.splitId) {
    // 使用splitId加载拆分详情
    const success = await loadSplitDetail(propertyData.splitId)
    if (success && originalProperty.value.id) {
      // 获取最大合同结束日期
      await loadMaxContractEndDate(originalProperty.value.id)
      visible.value = true
    }
  } else if (propertyData.id) {
    // 使用ID初始化一个基础对象
    originalProperty.value = { 
      roomName: '',
      propertyType: '',
      buildArea: 0,
      innerArea: 0,
      parcelName: '',
      buildingName: '',
      floorName: '',
      id: propertyData.id,
      index: 1
    }
    
    // 获取最大合同结束日期
    await loadMaxContractEndDate(propertyData.id)
    
    // 初始化拆分房源（包括获取详情数据）
    await initSplitProperties()
    
    // 显示抽屉
    visible.value = true
  } else {
    Message.error('参数错误：缺少ID或splitId')
  }
}

// 暴露方法
defineExpose({
  show
})

// 接收房源弹框返回的数据更新拆分房源
const handleAddPropertyConfirm = (data: any) => {
  console.log('接收到房源弹框返回的数据:', data)
  
  // 如果是调整编辑模式返回的数据
  if (data && data.type === 'adjust-edit' && data.data && typeof data.index === 'number') {
    const index = data.index
    
    // 检查索引是否有效
    if (index >= 0 && index < splitProperties.value.length) {
      console.log('更新拆分房源数据，索引:', index)
      
      // 完全使用房源弹框返回的数据替换当前拆分房源
      splitProperties.value.splice(index, 1, data.data)
      
      // 关闭房源弹框
      showAddPropertyDrawer.value = false
    }
  }
}
</script>

<style scoped lang="less">
.split-property {
  .section {
    margin-bottom: 24px;
    .section-title {
      margin: 16px 0;
      &.first-child{
        margin-top: 8px;
      }
    }
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .area-validation {
    display: flex;
    align-items: center;
    color: #666;
    margin-top: 8px;

    &.error {
      color: rgb(var(--danger-6));
    }

    .arco-icon {
      margin-right: 8px;
    }
  }

  .drawer-footer {
    text-align: right;
    padding: 16px 0;
  }

  :deep(.arco-table-th) {
    background-color: var(--color-fill-2) !important;
  }

  :deep(.arco-table-td) {
    background-color: var(--color-bg-2);
  }
  :deep(.arco-form-item-label){
    min-width: 80px;
  }
  .contract-end-date{
    margin-top: 30px;
    line-height: 32px;
    color: var(--color-text-1);
  }
}
</style> 