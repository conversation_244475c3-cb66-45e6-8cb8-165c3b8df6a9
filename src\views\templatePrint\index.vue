<template>
    <div class="container">
        <!-- <a-card class="general-card" :title="'模板打印管理'"> -->

        <!-- <a-divider style="margin-top: 0" /> -->

        <div class="content-container">
            <a-tabs v-model:activeKey="currentPage" hide-content @change="handleMenuClick">
                <a-tab-pane v-permission="['print:template:list']" key="template" title="套打模板" />
                <a-tab-pane v-permission="['print:template:dict:list']" key="fields" title="套打字典" />
            </a-tabs>
            <template-page v-if="currentPage === 'template'" />
            <fields-page v-if="currentPage === 'fields'" />
        </div>
        <!-- </a-card> -->
    </div>
</template>

<script setup>
import { ref } from 'vue';
import TemplatePage from './templatePage.vue';
import FieldsPage from './fields.vue';

const currentPage = ref('template');

const handleMenuClick = (key) => {
    currentPage.value = key;
};
</script>

<script>
export default {
    name: 'TemplatePrint',
}
</script>

<style scoped lang="less">
.container {
    padding: 0 16px;
}

.content-container {
    // margin-top: 6px;
    background: #fff;
}

:deep(.arco-menu) {
    border-bottom: none;
}

:deep(.arco-menu-inner) {
    padding: 6px 0 !important;
}
</style>