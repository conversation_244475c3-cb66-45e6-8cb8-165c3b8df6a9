# 合同退租组件使用说明

## 组件概述

`ContractTerminationMain` 是一个完整的合同退租功能组件，支持退租申请和出场结算两个步骤。组件封装了完整的业务流程，可以作为独立组件在不同页面中复用。

## 组件特性

- ✅ **完全自包含**: 组件内部管理所有状态和业务逻辑
- ✅ **灵活的参数传递**: 支持 props 和方法调用两种方式传递参数
- ✅ **双向绑定**: 支持 `v-model` 控制显示/隐藏
- ✅ **事件通知**: 提供完整的事件回调机制
- ✅ **步骤控制**: 内置步骤条，支持多步骤流程
- ✅ **表单验证**: 支持步骤间的表单验证
- ✅ **加载状态**: 内置 loading 状态管理

## API 接口

### Props

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `contractId` | `string` | `''` | 合同ID，用于新建退租申请 |
| `terminateId` | `string` | `''` | 退租ID，用于编辑已有退租申请 |
| `modelValue` | `boolean` | `false` | 控制组件显示/隐藏，支持 v-model |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: boolean)` | 显示状态变化时触发 |
| `save` | `(data: ContractTerminateAddDTO)` | 暂存时触发 |
| `submit` | `(data: ContractTerminateAddDTO)` | 提交时触发 |
| `close` | `()` | 关闭时触发 |
| `cancel` | `()` | 取消时触发 |

### 暴露方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `open` | `{ contractId?: string; terminateId?: string }` | `void` | 打开组件 |
| `close` | - | `void` | 关闭组件 |
| `handleSaveClick` | - | `Promise<void>` | 触发暂存 |
| `handleSubmitClick` | - | `Promise<void>` | 触发提交 |
| `handleNextClick` | - | `Promise<void>` | 下一步 |
| `handlePrev` | - | `void` | 上一步 |
| `getCurrentStepData` | - | `any` | 获取当前步骤数据 |
| `validateCurrentStep` | - | `Promise<boolean>` | 验证当前步骤 |

### 暴露状态

| 状态名 | 类型 | 说明 |
|--------|------|------|
| `visible` | `Readonly<Ref<boolean>>` | 当前显示状态 |
| `currentStep` | `Readonly<Ref<number>>` | 当前步骤（1或2） |
| `loading` | `Readonly<Ref<boolean>>` | 加载状态 |

## 使用方式

### 1. 基础使用（推荐）

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <a-button @click="openTermination">退租</a-button>
    
    <!-- 退租组件 -->
    <ContractTerminationMain 
      ref="terminationRef"
      @save="handleSave"
      @submit="handleSubmit"
      @close="handleClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ContractTerminationMain from '@/views/contractManage/contractTerminationMain.vue'

const terminationRef = ref()

// 打开退租申请
const openTermination = () => {
  terminationRef.value?.open({ contractId: 'contract123' })
}

// 处理暂存
const handleSave = (data) => {
  console.log('暂存数据:', data)
}

// 处理提交
const handleSubmit = (data) => {
  console.log('提交数据:', data)
  // 可以在这里刷新列表或其他操作
}

// 处理关闭
const handleClose = () => {
  console.log('组件已关闭')
}
</script>
```

### 2. 使用 v-model 控制

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <a-button @click="visible = true">退租</a-button>
    
    <!-- 退租组件 -->
    <ContractTerminationMain 
      v-model="visible"
      :contract-id="contractId"
      @save="handleSave"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ContractTerminationMain from '@/views/contractManage/contractTerminationMain.vue'

const visible = ref(false)
const contractId = ref('contract123')

const handleSave = (data) => {
  console.log('暂存数据:', data)
}

const handleSubmit = (data) => {
  console.log('提交数据:', data)
  visible.value = false // 提交后关闭
}
</script>
```

### 3. 编辑已有退租申请

```vue
<template>
  <ContractTerminationMain 
    ref="terminationRef"
    @save="handleSave"
    @submit="handleSubmit"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ContractTerminationMain from '@/views/contractManage/contractTerminationMain.vue'

const terminationRef = ref()

// 编辑已有退租申请
const editTermination = (terminateId: string) => {
  terminationRef.value?.open({ terminateId })
}
</script>
```

### 4. 路由参数传递

组件支持从路由参数中获取 `contractId` 和 `terminateId`：

```typescript
// 路由跳转
router.push({
  path: '/contract-termination',
  query: { contractId: 'contract123' }
})

// 组件会自动读取路由参数
```

## 合同列表集成示例

在合同列表页面中的完整使用示例：

```vue
<template>
  <div class="contract-list">
    <!-- 表格操作列 -->
    <a-table :data="tableData">
      <template #operations="{ record }">
        <a-button 
          v-if="record.status === 30" 
          type="text" 
          size="mini"
          @click="handleQuit(record)"
        >
          退租
        </a-button>
      </template>
    </a-table>
    
    <!-- 退租组件 -->
    <ContractTerminationMain 
      ref="terminationRef"
      @save="handleTerminationSave"
      @submit="handleTerminationSubmit"
      @close="handleTerminationClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import ContractTerminationMain from '@/views/contractManage/contractTerminationMain.vue'

const terminationRef = ref()
const tableData = ref([])

// 退租操作
const handleQuit = (record: any) => {
  // 状态验证
  if (record.status !== 30) {
    Message.warning('只有生效中状态的合同才能退租')
    return
  }
  
  // 打开退租申请
  terminationRef.value?.open({ contractId: record.id })
}

// 处理暂存
const handleTerminationSave = (data: any) => {
  Message.success('退租申请暂存成功')
}

// 处理提交
const handleTerminationSubmit = (data: any) => {
  Message.success('退租申请提交成功')
  // 刷新列表
  loadTableData()
}

// 处理关闭
const handleTerminationClose = () => {
  console.log('退租申请已关闭')
}

const loadTableData = async () => {
  // 加载表格数据
}
</script>
```

## 业务流程

### 步骤一：退租申请

1. **数据初始化**: 根据传入的 `contractId` 或 `terminateId` 调用接口获取数据
2. **表单填写**: 用户填写退租相关信息
3. **联动查询**: 根据用户选择动态查询预计退款信息
4. **暂存/提交**: 支持暂存草稿或直接提交申请

### 步骤二：出场结算

1. **结算信息**: 显示退租后的结算详情
2. **确认结算**: 最终确认并完成退租流程

## 接口调用

组件内部会自动调用以下接口：

1. **获取详情**: `GET /terminate/detail`
2. **查询账单**: `POST /terminate/getBill`
3. **查询免租期租金**: `POST /terminate/freeRent`
4. **保存申请**: `POST /terminate/save`

## 注意事项

1. **权限控制**: 确保只有生效中（status=30）的合同才能退租
2. **参数传递**: `contractId` 和 `terminateId` 只需传递其中一个
3. **表单验证**: 组件内部包含完整的表单验证逻辑
4. **状态管理**: 组件会自动管理加载状态和步骤状态
5. **事件处理**: 建议处理所有事件回调，特别是 `submit` 事件用于刷新列表

## 样式定制

组件使用了 CSS 变量和深度选择器，可以通过以下方式定制样式：

```vue
<style>
/* 定制抽屉宽度 */
:deep(.arco-drawer) {
  width: 1400px !important;
}

/* 定制步骤条样式 */
:deep(.arco-steps-item-title) {
  font-size: 16px;
}
</style>
```

## 版本信息

- **组件版本**: v1.0.0
- **依赖版本**: Vue 3.x + Arco Design Vue 2.x
- **最后更新**: 2024-01-15 