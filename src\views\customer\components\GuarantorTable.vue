<template>
  <a-table
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :bordered="{ cell: true }"
    :scroll="{ x: 1 }"
    row-key="id"
  >
    <template #nameTitle>
      <span style="color: red;">*</span>姓名
    </template>
    
    <template #phoneTitle>
      <span style="color: red;">*</span>电话
    </template>
    
    <template #addressTitle>
      <span style="color: red;">*</span>通讯地址
    </template>
    
    <template #name="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model.trim="record.name" 
        placeholder="请输入姓名"
        size="small"
      />
      <span v-else>{{ record.name }}</span>
    </template>
    
    <template #phone="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.phone" 
        placeholder="请输入电话"
        size="small"
      />
      <span v-else>{{ record.phone }}</span>
    </template>
    
    <template #idType="{ record, rowIndex }">
      <a-select 
        v-if="record.isEditing" 
        v-model="record.idType" 
        placeholder="请选择证件类型"
        size="small"
      >
        <a-option value="1">身份证</a-option>
        <a-option value="2">护照</a-option>
        <a-option value="3">军官证</a-option>
      </a-select>
      <span v-else>{{ getIdTypeLabel(record.idType) }}</span>
    </template>
    
    <template #idNumber="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.idNumber" 
        placeholder="请输入证件号码"
        size="small"
      />
      <span v-else>{{ record.idNumber }}</span>
    </template>
    
    <template #address="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.address" 
        placeholder="请输入通讯地址"
        size="small"
      />
      <span v-else>{{ record.address }}</span>
    </template>
    
    <template #remark="{ record, rowIndex }">
      <a-textarea 
        v-if="record.isEditing" 
        v-model="record.remark" 
        placeholder="请输入备注"
        :rows="1"
        size="small"
      />
      <span v-else>{{ record.remark }}</span>
    </template>
    
    <template #operations="{ record, rowIndex }">
      <a-space v-if="!readonly">
        <a-button 
          v-if="record.isEditing" 
          type="text" 
          size="small" 
          @click="saveRow(record, rowIndex)"
        >
          保存
        </a-button>
        <a-button 
          v-else
          type="text" 
          size="small" 
          @click="editRow(record)"
        >
          修改
        </a-button>
        
        <a-button 
          type="text" 
          size="small" 
          status="danger" 
          @click="deleteRow(rowIndex)"
        >
          删除
        </a-button>
      </a-space>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Message } from '@arco-design/web-vue'

interface Guarantor {
  id: number
  name: string
  phone: string
  idType: string
  idNumber: string
  address: string
  remark: string
  isEditing?: boolean
}

interface Props {
  modelValue: Guarantor[]
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  readonly: false
})

const emit = defineEmits(['update:modelValue'])

const tableData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const columns = computed(() => {
  const baseColumns: any[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      slotName: 'name',
      width: 100,
      titleSlotName: 'nameTitle',
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '电话',
      dataIndex: 'phone',
      slotName: 'phone',
      width: 140,
      titleSlotName: 'phoneTitle',
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '证件类型',
      dataIndex: 'idType',
      slotName: 'idType',
      width: 100,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '证件号码',
      dataIndex: 'idNumber',
      slotName: 'idNumber',
      width: 180,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '通讯地址',
      dataIndex: 'address',
      slotName: 'address',
      width: 200,
      titleSlotName: 'addressTitle',
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '备注',
      dataIndex: 'remark',
      slotName: 'remark',
      width: 150,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    }
  ]
  
  // 只有非只读模式才显示操作列
  if (!props.readonly) {
    baseColumns.push({
      title: '操作',
      slotName: 'operations',
      width: 120,
      fixed: 'right',
      align: 'center'
    })
  }
  
  return baseColumns
})

const idTypeOptions = {
  '1': '身份证',
  '2': '护照',
  '3': '军官证'
}

const getIdTypeLabel = (value: string) => {
  return idTypeOptions[value as keyof typeof idTypeOptions] || value
}

const editRow = (record: Guarantor) => {
  record.isEditing = true
}

const saveRow = (record: Guarantor, index: number) => {
  if (!record.name) {
    Message.warning('请输入姓名')
    return
  }
  if (!record.phone) {
    Message.warning('请输入电话')
    return
  }
  if (!record.address) {
    Message.warning('请输入通讯地址')
    return
  }
  
  record.isEditing = false
  Message.success('保存成功')
}

const deleteRow = (index: number) => {
  const newData = [...tableData.value]
  newData.splice(index, 1)
  tableData.value = newData
  Message.success('删除成功')
}
</script>

<style scoped lang="less">
:deep(.arco-table-td) {
  padding: 8px !important;
}

:deep(.arco-input),
:deep(.arco-select),
:deep(.arco-textarea) {
  border: none;
  box-shadow: none;
}
</style> 