<template>
  <div class="container">
    <div class="rent-tabs-wrapper">
      <a-tabs default-active-key="simple" @change="handleTabChange">
        <a-tab-pane v-permission="['rent:room:simple:diagram']" key="simple" title="房态简图">
          <room-status-simple ref="simpleViewRef" />
        </a-tab-pane>
        <a-tab-pane v-permission="['rent:room:detail:diagram']" key="detailed" title="房态详图">
          <room-status-detailed ref="detailedViewRef" />
        </a-tab-pane>
      </a-tabs>
      <div class="tabs-gradient-bar" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import RoomStatusSimple  from './components/roomStatusSimple.vue'
import RoomStatusDetailed from './components/roomStatusDetailed.vue'

const simpleViewRef = ref()
const detailedViewRef = ref()

// 处理tab切换
const handleTabChange = (key: string) => {
  // 切换到简图时
  if (key === 'simple') {
    // 获取详图的查询参数并同步到简图
    if (detailedViewRef.value?.getQueryParams && simpleViewRef.value?.setQueryParams) {
      const detailedParams = detailedViewRef.value.getQueryParams()
      simpleViewRef.value.setQueryParams(detailedParams)
    }
    // 刷新简图数据
    if (simpleViewRef.value?.loadSimpleDiagramData) {
      simpleViewRef.value.loadSimpleDiagramData()
    }
  }
  // 切换到详图时
  if (key === 'detailed') {
    // 获取简图的查询参数并同步到详图
    if (simpleViewRef.value?.getQueryParams && detailedViewRef.value?.setQueryParams) {
      const simpleParams = simpleViewRef.value.getQueryParams()
      detailedViewRef.value.setQueryParams(simpleParams)
    }
    // 刷新详图数据
    if (detailedViewRef.value?.loadDetailDiagramData) {
      detailedViewRef.value.loadDetailDiagramData()
    }
  }
}
</script>

<style scoped lang="less">
.container {
  padding:0 16px 16px 16px;
}

.rent-tabs-wrapper {
  background: #fff;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  position: relative;
  padding-bottom: 0;
}

:deep(.arco-tabs) {
  background: #fff;
  border-radius: 4px 4px 0 0;
}
:deep(.arco-tabs-nav) {
  background: linear-gradient(90deg, #f7faff 0%, #eaf3ff 100%);
  border-radius: 4px 4px 0 0;
  min-height: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  border-bottom: none;
  margin-bottom: 0;
  padding: 0;
}
:deep(.arco-tabs-nav-tab) {
  background: transparent;
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 0;
}
:deep(.arco-tabs-tab) {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
  height: 48px;
  line-height: 48px;
  padding: 0 32px;
  border-radius: 4px 4px 0 0;
  background: transparent;
  transition: background 0.2s, color 0.2s;
}
:deep(.arco-tabs-tab-active) {
  color: #165dff;
  border-radius: 4px 4px 0 0;
}
:deep(.arco-tabs-tab:not(.arco-tabs-tab-active):hover) {
  background: #f7f8fa;
  color: #165dff;
}
:deep(.arco-tabs-ink-bar) {
  display: none;
}

.tabs-gradient-bar {
  height: 8px;
  border-radius: 0 0 4px 4px;
  background: linear-gradient(180deg, #fff 0%, #f0f2f6 100%);
  width: 100%;
}

.general-card {
  margin-bottom: 16px;
}
</style> 