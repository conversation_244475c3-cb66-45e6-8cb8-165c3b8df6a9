<template>
    <a-drawer
        v-model:visible="visible"
        title="批量绑定智能水电表"
        class="common-drawer"
        :footer="false"
        @cancel="handleCancel"
    >

        <!-- 运营项目关联 -->
        <div class="brand-section">
            <SectionTitle title="关联运营项目" />
            <div class="brand-cards">
                <!-- 项目信息 -->
                 <a-row :gutter="8">
                    <a-col :span="6">
                        <a-form-item label="项目：">
                            <span>{{ projectInfo.projectName }}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6" style="display: none;">
                        <a-form-item label="对应运营项目：">
                            <a-select v-model="selectedOpsProjectId" placeholder="请选择对应运营项目">
                                <a-option v-for="item in opsProjectList" :key="item.id" :value="item.id">
                                    {{ item.name }}
                                </a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                 </a-row>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧房源列表 -->
            <div class="left-panel">
                <SectionTitle title="房源">
                    <template #right>
                        <div class="header-actions">
                            <a-button type="text" @click="handleExportRooms">导出房源</a-button>
                            <a-button type="text" @click="handleImportBinding" :loading="importLoading">导入绑定关系</a-button>
                        </div>
                    </template>
                </SectionTitle>
                
                <!-- 房源筛选 -->
                <div class="filter-section" style="margin-top: 16px;">
                    <a-row :gutter="8">
                        <a-col :span="4">
                            <a-select v-model="roomFilter.bindFlag" placeholder="绑定状态" allow-clear>
                                <a-option value="">全部</a-option>
                                <a-option :value="0">未绑定</a-option>
                                <a-option :value="1">已绑定</a-option>
                            </a-select>
                        </a-col>
                        <a-col :span="5">
                            <a-select v-model="roomFilter.parcelId" placeholder="地块" allow-clear @change="handleParcelChange">
                                <a-option v-for="item in parcelOptions" :key="item.id" :value="item.id">
                                    {{ item.parcelName }}
                                </a-option>
                            </a-select>
                        </a-col>
                        <a-col :span="5">
                            <a-select v-model="roomFilter.buildingId" placeholder="楼栋" allow-clear>
                                <a-option v-for="item in buildingOptions" :key="item.id" :value="item.id">
                                    {{ item.buildingName }}
                                </a-option>
                            </a-select>
                        </a-col>
                        <a-col :span="8">
                            <a-input v-model="roomFilter.roomName" placeholder="请输入房源名称" allow-clear />
                        </a-col>
                        <a-col :span="2">
                            <a-button type="primary" @click="handleRoomSearch">搜索</a-button>
                        </a-col>
                    </a-row>
                </div>
                <!-- 房源表格 -->
                <div class="room-table">
                    <a-table
                        :data="roomList"
                        :columns="roomColumns"
                        :pagination="roomPagination"
                        :loading="roomLoading"
                        row-key="roomId"
                        size="small"
                        :scroll="{x: '100%',y: '100%'}"
                        :scrollbar="true"
                        :bordered="{ cell: true }"
                        @page-change="handleRoomPageChange"
                        @page-size-change="handleRoomPageSizeChange"
                    >
                        <template #opsSysRoomId="{ record, rowIndex }">
                            <span>{{ record.opsSysRoomId }}</span>
                            <!-- <a-input
                                v-model="record.opsSysRoomId"
                                placeholder="请选择运营房间"
                                readonly
                                @click="handleSelectOpsRoom(record, rowIndex)"
                                style="cursor: pointer;"
                            /> -->
                        </template>
                    </a-table>
                </div>
            </div>

            <!-- 右侧设备信息 -->
            <div class="right-panel" style="display: none;">
                <SectionTitle title="运营房间">
                    <template #right>
                        <div class="header-actions">
                            <a-button type="text" @click="handleSyncOpsRooms">同步运营房间数据</a-button>
                            <a-button type="text" @click="handleExportOpsRooms">导出运营房间</a-button>
                        </div>
                    </template>
                </SectionTitle>

                <!-- 运营房间筛选 -->
                <div class="filter-section" style="margin-top: 16px;">
                    <a-row :gutter="8">
                        <a-col :span="5">
                            <a-select v-model="roomFilter.parcelId" placeholder="地块" allow-clear @change="handleParcelChange">
                                <a-option v-for="item in parcelOptions" :key="item.id" :value="item.id">
                                    {{ item.parcelName }}
                                </a-option>
                            </a-select>
                        </a-col>
                        <a-col :span="5">
                            <a-select v-model="roomFilter.buildingId" placeholder="楼栋" allow-clear>
                                <a-option v-for="item in buildingOptions" :key="item.id" :value="item.id">
                                    {{ item.buildingName }}
                                </a-option>
                            </a-select>
                        </a-col>
                        <a-col :span="8">
                            <a-input v-model="roomFilter.roomName" placeholder="请输入房源名称" allow-clear />
                        </a-col>
                        <a-col :span="2">
                            <a-button type="primary" @click="handleRoomSearch">搜索</a-button>
                        </a-col>
                    </a-row>
                </div>

                <!-- 运营房间表格 -->
                <div class="device-table">
                    <a-table
                        :data="opsRoomList"
                        :columns="opsRoomColumns"
                        :pagination="opsRoomPagination"
                        :loading="opsRoomLoading"
                        row-key="id"
                        size="small"
                        :scroll="{x: '100%',y: '100%'}"
                        :scrollbar="true"
                        :bordered="{ cell: true }"
                        @page-change="handleOpsRoomPageChange"
                        @page-size-change="handleOpsRoomPageSizeChange"
                    >
                        <template #action="{ record }">
                            <a-button
                                type="text"
                                size="small"
                                @click="handleSelectOpsRoomFromList(record)"
                                :disabled="!currentSelectingRoom || isOpsRoomSelected(record)"
                            >
                                {{ isOpsRoomSelected(record) ? '已选择' : '选择' }}
                            </a-button>
                        </template>
                    </a-table>
                </div>
            </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="footer-actions">
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleSave">保存</a-button>
        </div>


    </a-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import SectionTitle from '@/components/sectionTitle/index.vue'
import {
    getWaterElectricityRoomList,
    exportWaterElectricityList,
    importWaterElectricityRoomList,
    saveWaterElectricityBindRelation,
    type RoomSimpleForWaterVo,
    type WaterElectricityQueryDTO
} from '@/api/smartWaterElectricity'
import { getParcelList, getBuildingSelectList, type SysParcel, type SysBuilding } from '@/api/project'
import { exportExcel, importExcelFile } from '@/utils/exportUtil'

// 使用水电房源类型
type ExtendedRoomVo = RoomSimpleForWaterVo

// 运营项目类型定义
interface OpsProject {
    id: string
    name: string
}

// 运营房间类型定义
interface OpsRoom {
    id: string
    name: string
    roomCode: string
}

// Props - 不再需要必传的项目信息
// interface Props {
//     // 可以保留一些可选的配置项
// }

// Emits
const emit = defineEmits<{
    'success': []
    'cancel': []
}>()

// 响应式数据
const visible = ref(true) // 使用 v-if 控制时，组件创建时就应该是可见的

// 项目信息 - 通过 show 方法设置
const projectInfo = ref({
    projectId: '',
    projectName: ''
})

// 运营项目相关
const selectedOpsProjectId = ref('')
const opsProjectList = ref<OpsProject[]>([])

// 导入相关
const importLoading = ref(false)

// 房源相关
const roomLoading = ref(false)
const roomList = ref<ExtendedRoomVo[]>([])
const roomFilter = reactive({
    bindFlag: 0, // 默认显示未绑定 (对应接口字段)
    parcelId: '', // 对应接口字段
    buildingId: '', // 对应接口字段
    roomName: ''
})

const roomPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true
})

// 运营房间相关
const opsRoomLoading = ref(false)
const opsRoomList = ref<OpsRoom[]>([])
const opsRoomFilter = reactive({
    roomName: ''
})

const opsRoomPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true
})

// 当前选择的房源
const currentSelectingRoom = ref<ExtendedRoomVo | null>(null)
const currentSelectingIndex = ref(-1)

// 全局绑定关系存储（用于跨分页保持绑定关系）
const globalBindingMap = ref<Map<string, {
    opsSysRoomId: string
    opsSysRoomName: string
}>>(new Map())

// 地块和楼栋选项数据
const parcelOptions = ref<SysParcel[]>([])
const buildingOptions = ref<SysBuilding[]>([])
const parcelLoading = ref(false)
const buildingLoading = ref(false)

// 房源表格列配置
const roomColumns = [
    {
        title: '房源名称',
        dataIndex: 'fullName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '运营房间ID',
        slotName: 'opsSysRoomId',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '运营房间名称',
        dataIndex: 'opsSysRoomName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    }
]

// 运营房间表格列配置
const opsRoomColumns = [
    {
        title: '操作',
        slotName: 'action',
        width: 80,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '运营房间名称',
        dataIndex: 'name',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '运营房间编码',
        dataIndex: 'roomCode',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    }
]



// 初始化运营项目数据（模拟数据）
const initOpsProjectList = () => {
    opsProjectList.value = [
        { id: '1', name: '运营项目A' },
        { id: '2', name: '运营项目B' },
        { id: '3', name: '运营项目C' }
    ]
    // 默认选择第一个项目
    if (opsProjectList.value.length > 0) {
        selectedOpsProjectId.value = opsProjectList.value[0].id
    }
}

// 房源相关方法
const fetchRoomList = async (clearBindings = false) => {
    if (!projectInfo.value.projectId) return

    roomLoading.value = true
    try {
        const params: WaterElectricityQueryDTO = {
            pageNum: roomPagination.current,
            pageSize: roomPagination.pageSize,
            projectId: projectInfo.value.projectId,
            bindFlag: roomFilter.bindFlag,
            parcelId: roomFilter.parcelId,
            buildingId: roomFilter.buildingId,
            roomName: roomFilter.roomName
        }

        const response = await getWaterElectricityRoomList(params)
        if (response && response.rows) {
            // 直接使用接口返回的数据
            roomList.value = response.rows

            // 如果需要清空绑定关系，清空全局绑定映射
            if (clearBindings) {
                globalBindingMap.value.clear()
            } else {
                // 恢复绑定关系
                roomList.value.forEach(room => {
                    const binding = globalBindingMap.value.get(room.roomId)
                    if (binding) {
                        room.opsSysRoomId = binding.opsSysRoomId
                        room.opsSysRoomName = binding.opsSysRoomName
                    }
                })
            }

            roomPagination.total = response.total
        }
    } catch (error) {
        console.error('获取房源列表失败:', error)
    } finally {
        roomLoading.value = false
    }
}

const handleRoomSearch = () => {
    roomPagination.current = 1
    fetchRoomList(true) // 搜索时清空绑定关系
}

const handleRoomPageChange = (page: number) => {
    roomPagination.current = page
    fetchRoomList(false) // 分页时不清空绑定关系
}
const handleRoomPageSizeChange = (pageSize: number) => {
    roomPagination.pageSize = pageSize
    roomPagination.current = 1
    fetchRoomList(false) // 分页时不清空绑定关系
}

// 运营房间相关方法（模拟数据）
const fetchOpsRoomList = async () => {
    if (!selectedOpsProjectId.value) return

    opsRoomLoading.value = true
    try {
        // 模拟运营房间数据
        const mockData: OpsRoom[] = [
            { id: 'ops_room_1', name: '运营房间001', roomCode: 'OPS001' },
            { id: 'ops_room_2', name: '运营房间002', roomCode: 'OPS002' },
            { id: 'ops_room_3', name: '运营房间003', roomCode: 'OPS003' },
            { id: 'ops_room_4', name: '运营房间004', roomCode: 'OPS004' },
            { id: 'ops_room_5', name: '运营房间005', roomCode: 'OPS005' }
        ]

        // 模拟分页
        const startIndex = (opsRoomPagination.current - 1) * opsRoomPagination.pageSize
        const endIndex = startIndex + opsRoomPagination.pageSize
        opsRoomList.value = mockData.slice(startIndex, endIndex)
        opsRoomPagination.total = mockData.length

    } catch (error) {
        console.error('获取运营房间列表失败:', error)
        opsRoomList.value = []
        opsRoomPagination.total = 0
    } finally {
        opsRoomLoading.value = false
    }
}

const handleOpsRoomSearch = () => {
    opsRoomPagination.current = 1
    fetchOpsRoomList()
}

const handleOpsRoomPageChange = (page: number) => {
    opsRoomPagination.current = page
    fetchOpsRoomList()
}

const handleOpsRoomPageSizeChange = (pageSize: number) => {
    opsRoomPagination.pageSize = pageSize
    opsRoomPagination.current = 1
    fetchOpsRoomList()
}

// 运营房间选择相关
const handleSelectOpsRoom = (record: any, rowIndex: number) => {
    currentSelectingRoom.value = record
    currentSelectingIndex.value = rowIndex
    Message.info('请在右侧运营房间列表中选择一个房间')
}

const handleSelectOpsRoomFromList = (opsRoom: OpsRoom) => {
    if (!currentSelectingRoom.value) {
        Message.warning('请先选择要绑定的房源')
        return
    }

    // 检查运营房间是否已被其他房源绑定（包括全局绑定映射中的数据）
    const isOpsRoomAlreadyBound = Array.from(globalBindingMap.value.values()).some(binding =>
        binding.opsSysRoomId === opsRoom.id
    ) || roomList.value.some(room =>
        room.roomId !== currentSelectingRoom.value?.roomId &&
        room.opsSysRoomId === opsRoom.id
    )

    if (isOpsRoomAlreadyBound) {
        Message.warning('该运营房间已被其他房源绑定，请选择其他房间')
        return
    }

    // 保存绑定关系到全局映射
    globalBindingMap.value.set(currentSelectingRoom.value.roomId, {
        opsSysRoomId: opsRoom.id,
        opsSysRoomName: opsRoom.name
    })

    // 填充运营房间信息到房源列表
    currentSelectingRoom.value.opsSysRoomId = opsRoom.id
    currentSelectingRoom.value.opsSysRoomName = opsRoom.name

    // 清除选择状态
    currentSelectingRoom.value = null
    currentSelectingIndex.value = -1

    Message.success('运营房间选择成功')
}

// 检查运营房间是否已被选择
const isOpsRoomSelected = (opsRoom: OpsRoom) => {
    // 检查全局绑定映射中是否包含该运营房间（使用房间ID判断）
    return Array.from(globalBindingMap.value.values()).some(binding =>
        binding.opsSysRoomId === opsRoom.id
    )
}

// 清除运营房间绑定
const handleClearOpsRoom = (record: any) => {
    // 从全局绑定映射中移除
    globalBindingMap.value.delete(record.roomId)

    // 清空房源的运营房间信息
    record.opsSysRoomId = ''
    record.opsSysRoomName = ''

    Message.success('运营房间绑定已清除')
}

// 其他操作方法
const handleSyncOpsRooms = async () => {
    Message.info('暂无接口，无法同步运营房间数据')
}

const handleExportOpsRooms = async () => {
    Message.info('暂无接口，无法导出运营房间')
}

const handleExportRooms = async () => {
    if (!projectInfo.value.projectId) {
        Message.warning('请先选择项目')
        return
    }

    try {
        // 构建导出查询参数，使用当前筛选条件
        const exportParams: WaterElectricityQueryDTO = {
            pageNum: 1,
            pageSize: 10000, // 导出时使用大页码
            projectId: projectInfo.value.projectId,
            bindFlag: roomFilter.bindFlag,
            parcelId: roomFilter.parcelId,
            buildingId: roomFilter.buildingId,
            roomName: roomFilter.roomName
        }

        await exportExcel(exportWaterElectricityList, exportParams, '房源列表')
        Message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
    }
}

const handleImportBinding = () => {
    // 创建文件输入元素
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.xlsx,.xls'
    input.style.display = 'none'

    input.onchange = async (event: Event) => {
        const target = event.target as HTMLInputElement
        const file = target.files?.[0]

        if (!file) return

        // 检查文件类型
        const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']
        if (!allowedTypes.includes(file.type)) {
            Message.error('请选择Excel文件（.xlsx或.xls格式）')
            return
        }

        // 检查文件大小（限制10MB）
        if (file.size > 10 * 1024 * 1024) {
            Message.error('文件大小不能超过10MB')
            return
        }

        importLoading.value = true
        try {
            await importExcelFile(file, importWaterElectricityRoomList)
            Message.success('导入成功')

            // 导入成功后重新加载数据
            fetchRoomList(true) // 清空绑定关系，重新加载
        } catch (error) {
            console.error('导入失败:', error)
        } finally {
            importLoading.value = false
            // 清理input元素
            document.body.removeChild(input)
        }
    }

    // 添加到DOM并触发点击
    document.body.appendChild(input)
    input.click()
}

const handleSave = async () => {
    // 获取已绑定的房源数据
    const bindingData = roomList.value.filter(room => room.opsSysRoomId && room.opsSysRoomName)

    if (bindingData.length === 0) {
        Message.warning('请至少绑定一个运营房间')
        return
    }

    try {
        await saveWaterElectricityBindRelation(bindingData)
        emit('success')
        handleCancel()
    } catch (error) {
        console.error('绑定失败:', error)
    }
}

const handleCancel = () => {
    // 重置数据
    currentSelectingRoom.value = null
    currentSelectingIndex.value = -1
    // 触发取消事件
    emit('cancel')
}

// 加载地块数据
const loadParcelList = async (projectId: string) => {
    if (!projectId) return
    parcelLoading.value = true
    try {
        const res = await getParcelList(projectId)
        if (res && res.data) {
            parcelOptions.value = res.data
        }
    } catch (error) {
        console.error('获取地块列表失败:', error)
        parcelOptions.value = []
    } finally {
        parcelLoading.value = false
    }
}

// 加载楼栋数据
const loadBuildingList = async (parcelId: string) => {
    if (!parcelId) return
    buildingLoading.value = true
    try {
        const res = await getBuildingSelectList(parcelId)
        if (res && res.data) {
            buildingOptions.value = res.data
        }
    } catch (error) {
        console.error('获取楼栋列表失败:', error)
        buildingOptions.value = []
    } finally {
        buildingLoading.value = false
    }
}

// 处理地块变化
const handleParcelChange = (parcelId: string) => {
    // 清空楼栋选择
    roomFilter.buildingId = ''
    buildingOptions.value = []

    // 如果选择了地块，加载对应的楼栋列表
    if (parcelId) {
        loadBuildingList(parcelId)
    }
}

// show 方法 - 用于初始化组件
const show = (params: { projectId: string, projectName: string }) => {
    // 设置项目信息
    projectInfo.value = {
        projectId: params.projectId,
        projectName: params.projectName
    }

    // 初始化数据
    initOpsProjectList() // 初始化运营项目列表
    fetchOpsRoomList() // 获取运营房间列表
    loadParcelList(params.projectId) // 加载地块列表
    fetchRoomList(true) // 初始化时清空绑定关系
}

// 暴露方法给父组件
defineExpose({
    show
})


</script>

<style lang="less" scoped>
.project-info {
    margin-bottom: 16px;
    font-weight: 500;
}

.brand-section {
    margin-bottom: 24px;

    .brand-cards {
        padding: 0 16px;
        margin-top: 16px;
        width: 100%;
        overflow: auto;
        .brand-card {
            position: relative;
            padding: 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            text-align: center;
            justify-content: space-between;

            &:hover {
                border-color: #1890ff;
            }

            &.add-brand {
                background-color: #f0f0f0;
                color: #666;
                justify-content: center;
                cursor: pointer;
            }

            .brand-label {
                flex: 1;
            }

            .delete-icon {
                color: #333333;
                cursor: pointer;
                font-size: 14px;
                position: absolute;
                right: 0;
                top: 0;
            }
        }
    }
}

.main-content {
    display: flex;
    gap: 24px;
    height: 600px;
    
    .left-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .right-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .header-actions {
        display: flex;
        gap: 8px;
    }
    
    .filter-section {
        margin-bottom: 16px;
    }
    
    .room-table,
    .device-table {
        flex: 1;
        overflow: hidden;

    }
}

.footer-actions {
    margin-top: 24px;
    text-align: right;
    
    .arco-btn + .arco-btn {
        margin-left: 12px;
    }
}
</style>
