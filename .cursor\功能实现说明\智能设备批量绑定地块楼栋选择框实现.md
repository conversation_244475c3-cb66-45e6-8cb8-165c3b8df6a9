# 智能设备批量绑定地块楼栋选择框实现

## 需求描述
将 `src/views/smartDevices/components/BatchBindDrawer.vue` 文件中的地块和楼栋筛选条件从输入框改为选择框，数据来源参照 `src/views/property/operations/components/addOperationDrawer.vue` 页面的实现方式。

## 实现方案

### 1. 导入相关API和类型
```typescript
import { getParcelList, getBuildingSelectList, type SysParcel, type SysBuilding } from '@/api/project'
```

### 2. 添加数据定义
```typescript
// 地块和楼栋选项数据
const parcelOptions = ref<SysParcel[]>([])
const buildingOptions = ref<SysBuilding[]>([])
const parcelLoading = ref(false)
const buildingLoading = ref(false)
```

### 3. 修改模板结构
将原来的输入框：
```vue
<a-input v-model="roomFilter.parcelId" placeholder="地块" allow-clear />
<a-input v-model="roomFilter.buildingId" placeholder="楼栋" allow-clear />
```

改为选择框：
```vue
<a-select v-model="roomFilter.parcelId" placeholder="地块" allow-clear @change="handleParcelChange">
    <a-option v-for="item in parcelOptions" :key="item.id" :value="item.id">
        {{ item.parcelName }}
    </a-option>
</a-select>
<a-select v-model="roomFilter.buildingId" placeholder="楼栋" allow-clear>
    <a-option v-for="item in buildingOptions" :key="item.id" :value="item.id">
        {{ item.buildingName }}
    </a-option>
</a-select>
```

### 4. 添加数据加载方法
```typescript
// 加载地块数据
const loadParcelList = async (projectId: string) => {
    if (!projectId) return
    parcelLoading.value = true
    try {
        const res = await getParcelList(projectId)
        if (res && res.data) {
            parcelOptions.value = res.data
        }
    } catch (error) {
        console.error('获取地块列表失败:', error)
        parcelOptions.value = []
    } finally {
        parcelLoading.value = false
    }
}

// 加载楼栋数据
const loadBuildingList = async (parcelId: string) => {
    if (!parcelId) return
    buildingLoading.value = true
    try {
        const res = await getBuildingSelectList(parcelId)
        if (res && res.data) {
            buildingOptions.value = res.data
        }
    } catch (error) {
        console.error('获取楼栋列表失败:', error)
        buildingOptions.value = []
    } finally {
        buildingLoading.value = false
    }
}

// 处理地块变化
const handleParcelChange = (parcelId: string) => {
    // 清空楼栋选择
    roomFilter.buildingId = ''
    buildingOptions.value = []
    
    // 如果选择了地块，加载对应的楼栋列表
    if (parcelId) {
        loadBuildingList(parcelId)
    }
}
```

### 5. 修改初始化方法
在 `show` 方法中添加地块数据的加载：
```typescript
const show = (params: { projectId: string, projectName: string }) => {
    // 设置项目信息
    projectInfo.value = {
        projectId: params.projectId,
        projectName: params.projectName
    }

    // 初始化数据
    fetchBrandList()
    loadParcelList(params.projectId) // 加载地块列表
    fetchRoomList()
    fetchDeviceList()
}
```

## 功能特点

### 1. 级联选择
- 选择地块后自动加载对应的楼栋列表
- 地块变化时自动清空楼栋选择

### 2. 数据来源
- 地块数据：通过 `getParcelList(projectId)` API 获取
- 楼栋数据：通过 `getBuildingSelectList(parcelId)` API 获取

### 3. 用户体验
- 支持清空选择（allow-clear）
- 显示友好的中文名称
- 错误处理和加载状态管理

### 4. 数据结构
- 地块：`{ id: string, parcelName: string, ... }`
- 楼栋：`{ id: string, buildingName: string, ... }`

## 技术实现要点

1. **API复用**：直接使用项目管理模块已有的地块楼栋API接口
2. **类型安全**：使用TypeScript类型定义确保数据结构正确
3. **级联逻辑**：地块变化时清空楼栋选择并重新加载楼栋数据
4. **错误处理**：API调用失败时清空数据并记录错误日志
5. **初始化**：组件显示时自动加载地块数据

## 参考实现
实现方式完全参照 `src/views/property/operations/components/addOperationDrawer.vue` 中的地块楼栋选择逻辑，确保了代码的一致性和可维护性。
