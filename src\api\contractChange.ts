import http from './index'

// 合同变更保存
export function saveContractChange(data: any) {
  return http.post<boolean>('/business-rent-admin/contractChange/save', data)
}

// 合同变更列表查询
export function getContractChangeList(data: any) {
  return http.post<any>('/business-rent-admin/contractChange/list', data)
}

// 生成房间款项
export function generateContractChangeCost(data: any) {
  return http.post<any>('/business-rent-admin/contractChange/generateCost', data)
}

// 合同变更删除
export function deleteContractChange(id: string) {
  return http.post<boolean>('/business-rent-admin/contractChange/delete', undefined, { params: { id } })
}

// 合同变更审批回调
export function approveContractChange(data: any) {
  return http.post<boolean>('/business-rent-admin/contractChange/approve', data)
}

// 合同变更详情查询
export function getContractChangeDetail(id: string) {
  return http.get<any>('/business-rent-admin/contractChange/detail', { id })
}
