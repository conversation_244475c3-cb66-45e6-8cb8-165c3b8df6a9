<template>
  <div class="all-properties">
    <!-- 数据表格 -->
    <a-table 
      :columns="columns" 
      :scroll="{x: 1}" 
      :data="tableData" 
      :pagination="pagination" 
      :bordered="{ cell: true }" 
      :stripe="true"
      :loading="props.loading"
      row-key="id"
      @page-change="onPageChange" 
      @page-size-change="onPageSizeChange"
      @selection-change="handleSelectionChange"
    >
      <template #name="{ record }">
        <PermissionLink v-permission="['rent:room:query']" @click="handleViewDetail(record)">{{ record.roomName }}</PermissionLink>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { 
  getRoomList,
  type RoomQueryDTO, 
  type RoomVo,
  RoomStatus,
  PaymentStatus,
  RoomType
} from '@/api/room'

// 定义props
const props = defineProps<{
  selection: {
    projectId: string
    projectName: string
    blockId: string
    blockName: string
    buildingId: string
    buildingName: string
  }
  filterForm: any
  loading: boolean
}>()

// 定义emits
const emit = defineEmits<{
  'update:loading': [value: boolean]
  'selection-change': [keys: string[]]
  'view-detail': [id: string]
  'edit-room': [id: string]
}>()

// 表格数据
const tableData = ref<RoomVo[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
})

// 选中的行
const selectedKeys = ref<string[]>([])
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
})

// 表格列定义
const columns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { 
    title: '房源状态', 
    dataIndex: 'status', 
    width: 120, 
    ellipsis: true, 
    tooltip: true, 
    align: 'center',
    render: (data: any) => {
      const statusMap: Record<number, string> = {
        [RoomStatus.ACTIVE]: '生效中',
        [RoomStatus.DRAFT]: '草稿',
        [RoomStatus.PENDING]: '待生效',
        [RoomStatus.EXPIRED]: '已失效'
      }
      return statusMap[data.record.status] || '未知'
    }
  },
  { title: '房源名称', dataIndex: 'roomName', slotName: 'name', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼栋', dataIndex: 'buildingName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼层', dataIndex: 'floorName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '计租面积(㎡)', dataIndex: 'rentArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '物业类型', dataIndex: 'propertyTypeName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { 
    title: '交付状态', 
    dataIndex: 'paymentStatus', 
    width: 120, 
    ellipsis: true, 
    tooltip: true, 
    align: 'center',
    render: (data: any) => {
      return data.record.paymentStatus === PaymentStatus.DELIVERED ? '已交付' : '未交付'
    }
  },
  { title: '户型', dataIndex: 'houseTypeName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '可招租日期', dataIndex: 'availableRentDate', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '对外出租起始日期', dataIndex: 'externalRentStartDate', width: 160, ellipsis: true, tooltip: true, align: 'center' }
]

// 查询房源列表
const fetchRoomList = async () => {
  // 如果没有项目ID，不调用接口
  if (!props.filterForm.projectId) {
    console.log('没有项目ID，跳过接口调用')
    emit('update:loading', false)
    return
  }

  try {
    emit('update:loading', true)
    const queryParams: RoomQueryDTO = {
      ...props.filterForm,
      // 全部房源不设置状态过滤
      type: RoomType.NORMAL, // 默认为普通房源
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    
    console.log('AllProperties 调用接口，参数:', queryParams)
    const response = await getRoomList(queryParams)
    if (response.rows) {
      tableData.value = response.rows || []
      pagination.total = response.total || 0
      
      // 添加序号
      tableData.value.forEach((item: any, index) => {
        item.index = (pagination.current - 1) * pagination.pageSize + index + 1
      })
    }
  } catch (error) {
    console.error('查询房源列表失败:', error)
  } finally {
    emit('update:loading', false)
  }
}

// 分页变化
const onPageChange = (page: number) => {
  pagination.current = page
  props.filterForm.pageNum = page
  fetchRoomList()
}

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  props.filterForm.pageSize = pageSize
  props.filterForm.pageNum = 1
  fetchRoomList()
}

// 处理选择变化
const handleSelectionChange = (rowKeys: string[]) => {
  selectedKeys.value = rowKeys
  emit('selection-change', rowKeys)
}

// 查看详情
const handleViewDetail = (record: RoomVo) => {
  console.log('查看详情:', record)
  if (record.id) {
    emit('view-detail', record.id)
  } else {
    Message.error('房源ID不存在')
  }
}

// 监听父组件的刷新事件
const handleRefresh = () => {
  fetchRoomList()
}

// 移除对selection的监听，避免重复调用接口
// 改为只通过全局事件触发刷新

// 监听特定的刷新事件
onMounted(() => {
  document.addEventListener('refresh-property-all-data', handleRefresh)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('refresh-property-all-data', handleRefresh)
})
</script>

<style scoped lang="less">
.all-properties {
  :deep(.arco-table-th) {
    background-color: var(--color-fill-2);
  }

  :deep(.arco-table-td) {
    background-color: var(--color-bg-2);
  }

  :deep(.arco-pagination) {
    margin-top: 16px;
    justify-content: flex-end;
  }
}
</style>