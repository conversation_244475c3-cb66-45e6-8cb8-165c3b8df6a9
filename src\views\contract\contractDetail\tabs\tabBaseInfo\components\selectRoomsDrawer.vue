<template>
    <a-drawer class="common-drawer-small" :title="contractData.contractType === 2 ? '选择多经点位' : '选择房源'"
        :visible="visible" @ok="handleOk" @cancel="handleCancel" unmountOnClose>
        <a-space direction="vertical" fill>
            <section>
                <sectionTitle :title="contractData.contractType === 2 ? '多经点位列表' : '房源列表'"></sectionTitle>
                <a-card :bordered="false" :body-style="{ padding: '16px 0 0' }">
                    <a-form :model="queryForm" auto-label-width>
                        <a-row :gutter="16">
                            <a-col :span="5">
                                <a-form-item label="物业类型" field="buildingType">
                                    <a-tree-select v-model="queryForm.buildingType" :data="purposeTypeOptions"
                                        placeholder="请选择物业类型" allow-clear
                                        :fieldNames="{ title: 'label', key: 'value' }"></a-tree-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="5">
                                <a-form-item label="楼栋" field="buildingId">
                                    <a-tree-select selectable="leaf" :data="buildingTree"
                                        :fieldNames="{ label: 'name', value: 'id' }" v-model="queryForm.buildingId"
                                        placeholder="请选择楼栋" allow-clear></a-tree-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="5">
                                <a-form-item label="租控状态" field="rentStatus">
                                    <a-select v-model="queryForm.rentStatus" placeholder="请选择租控状态" allow-clear>
                                        <a-option value="0">可租</a-option>
                                        <a-option value="1">已租</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="5">
                                <a-form-item label="房源名称" field="roomName">
                                    <a-input v-model="queryForm.roomName" placeholder="请输入房源名称" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="4">
                                <a-button type="primary" @click="handleQuery" :loading="loading">查询</a-button>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-card>
                <a-card>
                    <a-tree ref="treeRef" blockNode checkable v-model:checked-keys="checkedKeys"
                        checked-strategy="child" :data="treeData" :loading="loading" :virtualListProps="{
                            height: 200,
                        }" />
                </a-card>
                <a-card :bordered="false" :body-style="{ display: 'flex', justifyContent: 'center' }">
                    <a-space>
                        <a-button type="primary" @click="handleCheck">加入</a-button>
                        <a-button type="primary" @click="handleRemove">移除</a-button>
                    </a-space>
                </a-card>
            </section>
            <section>
                <sectionTitle title="选中房源列表"></sectionTitle>
                <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
                    <a-space direction="vertical" fill>
                        <a-space>
                            <a-input v-model="tableQuery" placeholder="请输入房源"></a-input>
                            <a-button type="primary" @click="handleTableQuery">查询</a-button>
                        </a-space>
                        <a-table :data="tableQueryData" rowKey="roomId" :row-selection="rowSelection"
                            v-model:selectedKeys="selectedKeys" :virtual-list-props="{ height: 200 }"
                            :pagination="false">
                            <template #columns>
                                <a-table-column :title="contractData.contractType === 2 ? '多经点位' : '房源'"
                                    data-index="roomName"></a-table-column>
                            </template>
                        </a-table>
                    </a-space>
                </a-card>
            </section>
        </a-space>
    </a-drawer>
</template>

<script lang="ts" setup>
import { ContractRoomDTO } from '@/api/contract';
import { getRoomTree, type RoomTreeQueryDTO, type RoomTreeVo } from '@/api/room';
import sectionTitle from '@/components/sectionTitle/index.vue';
import { cloneDeep } from 'lodash';
import { Message } from '@arco-design/web-vue';
import { useContractStore } from '@/store/modules/contract/index';
import { getDict } from '@/dict';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

const diversificationPurpose = getDict('diversification_purpose');
const purposeTypeOptions = computed(() => {
    if (contractData.value.contractType === 0) {
        return diversificationPurpose.filter((item: any) => ['厂房', '商业', '车位', '办公'].includes(item.label))
    } else if (contractData.value.contractType === 1) {
        return diversificationPurpose.filter((item: any) => ['宿舍'].includes(item.label))
    } else if (contractData.value.contractType === 2) {
        return diversificationPurpose.filter((item: any) => !['日租房'].includes(item.label))
    }
})

const visible = ref(false);
const loading = ref(false);

const openDrawer = (rooms: any[] = []) => {
    checkedKeys.value = rooms.map(item => item.roomId);
    tableQuery.value = '';
    tableQueryData.value = JSON.parse(JSON.stringify(rooms));
    selectedKeys.value = rooms.map(item => item.roomId);
    tableData.value = JSON.parse(JSON.stringify(rooms));
    visible.value = true;
    queryForm.value.contractType = contractData.value.contractType + ''
    // 打开抽屉时加载房源树数据
    loadRoomTree();
}

// 房源列表
const queryForm = ref<RoomTreeQueryDTO>({
    buildingType: undefined,
    buildingId: undefined,
    rentStatus: undefined,
    roomName: undefined,
    projectId: contractStore.currentProjectId,
    contractType: '',
    pricingFlag: 1
})

// 房源树数据
const treeData = ref<any[]>([]);

// 转换房源树数据格式
const transformTreeData = (data: RoomTreeVo[]): any[] => {
    if (!Array.isArray(data)) {
        return [];
    }

    return data.map(item => {
        const transformedItem: any = {
            title: item.name || item.roomName || '未命名',
            key: item.id || item.roomId || Math.random().toString(36).substr(2, 9),
            ...item
        };

        // 递归处理子节点
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
            transformedItem.children = transformTreeData(item.children);
        }

        return transformedItem;
    });
};

// 获取楼栋树, 递归遍历房源数，过滤掉房间节点
const buildingTree = ref<any[]>([]);
const filterTreeData = (data: any[]) => {
    return data.filter(item => {
        if (item.level <= 3) {
            if (item.children) {
                item.children = filterTreeData(item.children);
            }
            return true;
        }
        return false;
    });
};

// 加载房源树数据
const loadRoomTree = async () => {
    try {
        loading.value = true;
        const response = await getRoomTree(queryForm.value);
        treeData.value = transformTreeData(response.data || []);
        buildingTree.value = filterTreeData(JSON.parse(JSON.stringify(treeData.value)))
        nextTick(() => {
            treeRef.value?.expandAll();
        })
    } catch (error) {
        console.error('加载房源树失败:', error);
    } finally {
        loading.value = false;
    }
};

// 查询房源
const handleQuery = () => {
    loadRoomTree();
};
const checkedKeys = ref<string[]>([]);
const treeRef = useTemplateRef('treeRef');
const handleCheck = () => {
    const checkedNodes = treeRef.value?.getCheckedNodes();
    const roomList: ContractRoomDTO[] = []
    checkedNodes.forEach((item: any) => {
        roomList.push({
            id: item.id,
            contractId: undefined,
            roomId: item.roomId,
            roomName: item.roomName,
            area: item.rentArea || 0,
            standardUnitPrice: item.price,
            bottomPrice: item.bottomPrice || 0,
            priceUnit: item.priceUnit || 0,
            discount: 100,
            signedUnitPrice: 0,
            signedMonthlyPrice: 0,
            startDate: item.startDate || '',
            endDate: item.endDate || '',
            propertyType: item.propertyType || '',
        })
    })
    // 只选择房间级别的节点（level = 4）
    tableData.value = roomList
    tableQueryData.value = cloneDeep(roomList);
}

// 选中房源列表
const tableData = ref<ContractRoomDTO[]>([]);
const selectedKeys = ref<string[]>([]);
const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
});
const tableQuery = ref('');
const tableQueryData = ref<ContractRoomDTO[]>([]);
const handleTableQuery = () => {
    tableQueryData.value = tableData.value.filter((item: ContractRoomDTO) => {
        return item.roomName.includes(tableQuery.value);
    });
}
const handleRemove = () => {
    tableData.value = tableData.value.filter((item: ContractRoomDTO) => {
        return !selectedKeys.value.some(key => key === item.roomId);
    });
    tableQueryData.value = cloneDeep(tableData.value);
    checkedKeys.value = tableData.value.map(item => item.roomId);
}

const emit = defineEmits(['submit']);
/**
 * 选择的房间需要是相同立项定价-计价方式、相同立项定价-物业类型；否则提示：您选择的房源与已选择房源的计价方式/物业类型不同，请重新选择
 */
const handleOk = () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请选择房源')
        return
    }
    // 判断选择的房源计价方式和物业类型是否一样
    const priceUnit = tableData.value[0].priceUnit
    const isSamePriceUnit = tableData.value.every(item => item.priceUnit === priceUnit)
    if (!isSamePriceUnit) {
        Message.warning('您选择的房源与已选择房源的计价方式不同，请重新选择')
        return
    }
    const propertyType = tableData.value[0].propertyType
    const isSamePropertyType = tableData.value.every(item => item.propertyType === propertyType)
    if (!isSamePropertyType) {
        Message.warning('您选择的房源与已选择房源的物业类型不同，请重新选择')
        return
    }
    const rooms = tableData.value.map(item => {
        return {
            ...item,
            discount: 100
        }
    })
    emit('submit', rooms);
    visible.value = false;
}
const handleCancel = () => {
    visible.value = false;
}

defineExpose({
    openDrawer
})
</script>

<style lang="less" scoped></style>