<template>
    <a-drawer class="common-drawer" v-model:visible="drawerVisible" :title="drawerTitle" :body-style="{ padding: 0 }"
        unmount-on-close>
        <a-card class="flex-card" :bordered="false" :header-style="{ border: 'none' }"
            :body-style="{ padding: '0 16px' }">
            <template #title>
                <a-steps type="arrow" :current="current" small>
                    <a-step v-for="(item, index) in steps" :key="index">
                        <template #icon>
                            <icon-check-circle-fill v-if="current >= (index + 1)" size="24" />
                            <icon-more v-else size="20"
                                style="background-color: #97A3AA; border-radius: 50%; color: #fff;" />
                        </template>
                        {{ item }}
                    </a-step>
                </a-steps>
            </template>
            <expandApply v-if="current === 1" ref="expandApplyRef" :contractChangeDetail="contractChangeDetail" :isViewMode="isViewMode" />
            <expandRoom v-if="current === 2" :newCosts="newCosts ?? []" :oldCosts="oldCosts ?? []"
                :changeCosts="changeCosts ?? []" :isViewMode="isViewMode" />
            <expandBill v-if="current === 3" :newCosts="newCosts ?? []" :oldCosts="oldCosts ?? []"
                :changeCosts="changeCosts ?? []" :isViewMode="isViewMode" />
        </a-card>
        <template #footer>
            <a-space>
                <a-button v-if="current === 1" @click="drawerVisible = false">{{ isViewMode ? '关闭' : '取消' }}</a-button>
                <a-button v-if="current === 3 && !isViewMode" @click="handleSubmit('save')" :loading="submitLoading">暂存</a-button>
                <a-button v-if="current > 1" @click="current--" type="primary">上一步</a-button>
                <a-button v-if="current < 3" @click="handleNextStep" type="primary"
                    :loading="nextStepLoading">下一步</a-button>
                <a-button v-if="current === 3 && !isViewMode" @click="handleSubmit('submit')" type="primary"
                    :loading="submitLoading">提交审批</a-button>
            </a-space>
        </template>
    </a-drawer>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue';
import dayjs from 'dayjs';
import expandApply from './expandContract/apply/index.vue';
import expandRoom from './expandContract/room/index.vue';
import expandBill from './expandContract/bill/index.vue';
import { getContractById, ContractAddDTO, ContractVo, getContractDetailBill, print, getContractTemplateList } from '@/api/contract';
import { useContractStore } from '@/store/modules/contract/index';
import { saveContractChange, getContractChangeDetail, generateContractChangeCost } from '@/api/contractChange';
import { Message } from '@arco-design/web-vue';
import { json } from 'stream/consumers';
const drawerVisible = ref(false);
const drawerTitle = ref('合同同期扩租');
const isViewMode = ref(false); // 添加查看模式标识
import { ContractRoomDTO } from '@/api/contract';

// 步骤数组 - 改为计算属性
const steps = computed(() => {
    return ['扩租申请', '新房源账单', '扩租后账单'];
});

const current = ref<number>(1);

// 添加loading状态
const nextStepLoading = ref(false);
const submitLoading = ref(false);

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

// 合同变更详情数据
const contractChangeDetail = ref({
    remark: '',
    changeDate: '',
    changeRooms: [] as ContractRoomDTO[],
});
const changeCosts = ref<any[] | null>(null)
const newCosts = ref<any[] | null>(null)
const oldCosts = ref<any[] | null>(null)
const transferCosts = ref<any[] | null>(null)

const specialChangeId = ref<string | null>(null) // 特殊变更id

// 引用apply组件
const expandApplyRef = ref();

const emits = defineEmits(['submit'])

const open = async (data: any) => {
    current.value = 1;
    isViewMode.value = data.mode === 'view'; // 设置查看模式
    console.log('data', data);
    // 重置specialChangeId，确保新申请时id为null
    specialChangeId.value = null;
    // 调用API获取合同详情
    const response = await getContractById(data.id);
    if (response.data) {
        // 更新合同数据
        console.log('response.data', response.data);
        const contractInfo = response.data as ContractVo;
        console.log('contractInfo', contractInfo);
        contractInfo.fees = contractInfo.fees || []
        // 对优惠条款按开始日期正序排列
        if (contractInfo.fees.length > 0) {
            contractInfo.fees.sort((a, b) => {
                const dateA = dayjs(a.startDate)
                const dateB = dayjs(b.startDate)
                return dateA.isBefore(dateB) ? -1 : 1
            })
        }
        contractInfo.costs = contractInfo.costs || []
        contractInfo.rooms = contractInfo.rooms || []
        contractInfo.rentReceivableDate = Number(contractInfo.rentReceivableDate)
        if (contractInfo.dailyActivityEndTime && contractInfo.dailyActivityStartTime) {
            (contractInfo as any).activityDate = [contractInfo.dailyActivityStartTime, contractInfo.dailyActivityEndTime]
        }
        
        // 如果是查看模式且有变更详情，需要确保contractInfo.rooms只包含原有房源
        if (isViewMode.value && data.specialChangeId) {
            // 先保存原始的rooms数据
            const originalRooms = [...contractInfo.rooms];
            
            // 获取变更详情，过滤出原有房源
            try {
                const contractChangeResponse = await getContractChangeDetail(data.specialChangeId);
                if (contractChangeResponse.data && contractChangeResponse.data.changeRooms) {
                    // 过滤出原有房源（type === 2）
                    const originalRoomsFromChange = contractChangeResponse.data.changeRooms.filter((room: any) => room.type === 2);
                    if (originalRoomsFromChange.length > 0) {
                        // 使用变更详情中的原有房源
                        contractInfo.rooms = originalRoomsFromChange;
                    }
                }
            } catch (error) {
                console.error('获取变更详情失败:', error);
                // 如果获取失败，保持原始数据
                contractInfo.rooms = originalRooms;
            }
        }
        
        // 将合同详情赋值给 contractStore
        contractStore.contractData = { ...contractStore.contractData, ...contractInfo as any };

    }
    // specialChangeType 1 扩租 2 换房
    if (data.specialChangeId && data.specialChangeType == 1) {
        // specialChangeId 可能为 string 类型，需兼容 null
        specialChangeId.value = data.specialChangeId ?? null
        // 获取合同变更详情
        try {
            const contractChangeResponse = await getContractChangeDetail(data.specialChangeId);
            if (contractChangeResponse.data) {
                // 初始化contractChangeDetail数据
                contractChangeDetail.value = {
                    remark: contractChangeResponse.data.contractChange?.remark || '',
                    changeDate: contractChangeResponse.data.contractChange?.changeDate || dayjs().format('YYYY-MM-DD'),
                    changeRooms: contractChangeResponse.data.changeRooms || []
                };
                // 兼容处理：如果传入了mode参数，优先使用mode参数；否则根据审核状态判断
                if (data.mode !== undefined) {
                    // 从补充协议表格点击查看/编辑按钮时，使用传入的mode参数
                    isViewMode.value = data.mode === 'view';
                } else {
                    // 从合同列表直接点击扩租按钮时，根据审核状态判断
                    if(contractChangeResponse.data.contractChange.approveStatus<2){ // 审核中可以编辑   
                        isViewMode.value = false
                    }else{ // 审核通过可以查看
                        isViewMode.value = true
                    }
                }
                // 如果是查看模式，直接加载费用数据
                if (isViewMode.value) {
                    // 优先使用costSnapshot中的数据
                    if (contractChangeResponse.data.contractChange?.costSnapshot) {
                        try {
                            const costSnapshot = JSON.parse(contractChangeResponse.data.contractChange.costSnapshot);
                            changeCosts.value = costSnapshot.changeCosts || null;
                            newCosts.value = costSnapshot.newCosts || null;
                            oldCosts.value = costSnapshot.oldCosts || null;
                            transferCosts.value = costSnapshot.transferCosts || null;
                            console.log('从costSnapshot解析的费用数据:', { changeCosts: changeCosts.value, newCosts: newCosts.value, oldCosts: oldCosts.value });
                        } catch (error) {
                            console.error('解析费用快照失败:', error);
                        }
                    }
                    
                    // 如果costSnapshot中没有数据，使用直接返回的费用数据
                    if (!newCosts.value && contractChangeResponse.data.newCosts) {
                        newCosts.value = contractChangeResponse.data.newCosts;
                        console.log('使用直接返回的newCosts:', newCosts.value);
                    }
                    if (!oldCosts.value && contractChangeResponse.data.oldCosts) {
                        oldCosts.value = contractChangeResponse.data.oldCosts;
                        console.log('使用直接返回的oldCosts:', oldCosts.value);
                    }
                    if (!changeCosts.value && contractChangeResponse.data.changeCosts) {
                        changeCosts.value = contractChangeResponse.data.changeCosts;
                        console.log('使用直接返回的changeCosts:', changeCosts.value);
                    }
                }
                
                // 解析changeRooms，将新房源展示在新房源选择页面
                if (contractChangeResponse.data.changeRooms && contractChangeResponse.data.changeRooms.length > 0) {
                    // 过滤出新房源（type === 1）
                    const newRooms = contractChangeResponse.data.changeRooms.filter((room: any) => room.type === 1);
                    if (newRooms.length > 0) {
                        // 将新房源数据传递给apply组件
                        contractChangeDetail.value.changeRooms = newRooms;
                    }
                }
            }
        } catch (error) {
            console.error('获取合同变更详情失败:', error);
            // 如果获取失败，使用默认值
            contractChangeDetail.value = {
                remark: '',
                changeDate: dayjs().format('YYYY-MM-DD'),
                changeRooms: []
            };
        }
    } else {
        // 如果获取失败，使用默认值
        contractChangeDetail.value = {
            remark: '',
            changeDate: dayjs().format('YYYY-MM-DD'),
            changeRooms: []
        };
    }
    contractChangeDetail.value.changeDate = dayjs().format('YYYY-MM-DD');
    drawerVisible.value = true;
};

// 处理下一步按钮点击
const handleNextStep = async () => {
    if (current.value === 1) {
        // 从apply组件获取数据
        const formData = expandApplyRef.value?.getFormData();
        if (formData) {
            console.log('从apply组件获取的数据:', formData);

            // 验证扩租日期
            if (!formData.changeDate) {
                Message.error('请选择扩租日期');
                return;
            }

            // 验证房源数量
            const newRooms = formData.changeRooms.filter((room: any) => room.type === 1);

            if (newRooms.length === 0) {
                Message.error('请选择扩租新房源');
                return;
            }

            // 更新contractChangeDetail
            contractChangeDetail.value = { ...formData };
            
            // 如果不是查看模式，才调用接口生成费用
            if (!isViewMode.value) {
                // formData.changeRooms 已经包含了所有房源（新房源和原有房源），不需要重复添加
                // 添加loading状态
                nextStepLoading.value = true;
                try {
                    await generateCost();
                } catch (error) {
                    console.error('生成费用失败:', error);
                    Message.error('生成费用失败，请重试');
                    return;
                } finally {
                    nextStepLoading.value = false;
                }
            }
        }
    }

    // 进入下一步
    current.value++;
};

// 清理费用数据中的id字段，确保新创建的费用数据不包含id
const cleanCosts = (costs: any[] | null) => {
    if (!costs || !Array.isArray(costs)) return costs;
    return costs.map(cost => {
        const { id, ...cleanCost } = cost;
        return cleanCost;
    });
};

const generateCost = async () => {
    const response = await generateContractChangeCost({
        id: specialChangeId.value,
        contractId: contractStore.contractData.id,
        changeRooms: contractChangeDetail.value.changeRooms,
        changeDate: contractChangeDetail.value.changeDate,
        remark: contractChangeDetail.value.remark,
        changeType: 1
    });
    if (response.code === 200) {
        // 立即清理费用数据中的id字段
        changeCosts.value = cleanCosts(response.data.changeCosts)
        newCosts.value = cleanCosts(response.data.newCosts)
        oldCosts.value = cleanCosts(response.data.oldCosts)
        transferCosts.value = cleanCosts(response.data.transferCosts)
    } else {
        Message.error(response.msg);
    }
}

const handleSubmit = async (type: string) => {
    // 添加loading状态
    submitLoading.value = true;
    
    let costSnapshotObj: any = {
        newCosts: cleanCosts(newCosts.value),
        oldCosts: cleanCosts(oldCosts.value),
        transferCosts: cleanCosts(transferCosts.value)
    }
    
    try {
        var params: any = {
            changeType: 1,
            contractId: contractStore.contractData.id,
            changeCosts: cleanCosts(changeCosts.value),
            costSnapshot: JSON.stringify(costSnapshotObj),
            ...contractChangeDetail.value
        }
        
        // 只有在编辑现有申请时才传递id
        if (specialChangeId.value) {
            params.id = specialChangeId.value
        }
        if (params.changeRooms.length > 0) {
            params.changeRooms = params.changeRooms.map((item: any) => {
                return {
                    ...item,
                    id: null
                }
            })
        }
        if (type === 'submit') {
            params.isSubmit = 1
        } else {
            params.isSubmit = 0
        }
        // 移除return，实际调用API
        const res = await saveContractChange(params);
        console.log('res', res);
        if (res.code === 200) {
            if (type === 'submit') {
                Message.success('提交成功');
                drawerVisible.value = false;
                emits('submit')
            } else {
                Message.success('保存成功');
                specialChangeId.value = res.msg
                emits('submit')
            }
        } else {
            Message.error(res.msg);
        }
    } catch (error) {
        console.error('保存失败:', error);
    } finally {
        submitLoading.value = false;
    }
};

defineExpose({
    open
});
</script>
<style lang="less" scoped>
.arco-card {
    :deep(.arco-steps) {
        .arco-steps-item {
            justify-content: center;
            background: linear-gradient(270deg, #dce3ea 0%, #e9edf4 100%);

            &::after {
                border-left: 20px solid #dce3ea !important;
            }

            &.arco-steps-item-active,
            &.arco-steps-item-finish {
                .arco-steps-item-title {
                    color: rgb(var(--success-6));
                }
            }
        }

        .arco-steps-item-title {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            gap: 8px;
        }
    }
}
</style>