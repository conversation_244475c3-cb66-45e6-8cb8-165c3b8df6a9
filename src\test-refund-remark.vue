<template>
    <div class="test-container">
        <a-card title="退款申请说明显示测试">
            <a-space direction="vertical" size="large" style="width: 100%">
                <!-- 测试数据展示 -->
                <a-card title="测试数据" size="small">
                    <a-descriptions :column="1" bordered>
                        <a-descriptions-item label="退款ID">{{ testData.id }}</a-descriptions-item>
                        <a-descriptions-item label="退款申请说明">{{ testData.refundRemark }}</a-descriptions-item>
                        <a-descriptions-item label="说明长度">{{ testData.refundRemark?.length || 0 }} 字符</a-descriptions-item>
                        <a-descriptions-item label="说明类型">{{ typeof testData.refundRemark }}</a-descriptions-item>
                    </a-descriptions>
                </a-card>

                <!-- 操作按钮 -->
                <a-space>
                    <a-button type="primary" @click="handleViewDetail">查看详情</a-button>
                    <a-button @click="handleEditDetail">编辑详情</a-button>
                    <a-button @click="updateRemark">更新说明</a-button>
                </a-space>

                <!-- 直接显示测试 -->
                <a-card title="直接显示测试" size="small">
                    <div class="direct-display">
                        <div class="info-row">
                            <span class="info-label">退款申请说明：</span>
                            <span class="info-value">{{ testData.refundRemark || '暂无说明' }}</span>
                        </div>
                    </div>
                </a-card>
            </a-space>
        </a-card>

        <!-- 退款详情组件 -->
        <refund-detail ref="refundDetailRef" @refresh="handleRefresh" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import RefundDetail from './views/financeManage/components/refundDetail.vue'
import type { FinancialRefundVo } from './api/refundManage'

// 退款详情组件引用
const refundDetailRef = ref()

// 测试数据
const testData = reactive<FinancialRefundVo>({
    id: 'test-remark-001',
    projectName: '测试项目',
    refundType: 1,
    refundWay: 0,
    bizId: 'biz001',
    refundTarget: '测试订单001',
    applyTime: '2025-01-15 10:00:00',
    refundAmount: 5000,
    feeType: '定金',
    receiverName: '张三',
    receiverBank: '中国银行',
    receiverAccount: '****************',
    refundRemark: '客户因个人原因取消预定，申请退还定金。已与客户确认退款方式为原路退回，预计3-5个工作日到账。',
    refundStatus: 0,
    approveStatus: 0,
    attachments: '[]'
})

// 查看详情
const handleViewDetail = () => {
    console.log('查看详情 - refundRemark:', testData.refundRemark)
    refundDetailRef.value?.open(testData, 'view')
}

// 编辑详情
const handleEditDetail = () => {
    console.log('编辑详情 - refundRemark:', testData.refundRemark)
    refundDetailRef.value?.open(testData, 'edit')
}

// 更新说明
const updateRemark = () => {
    const remarks = [
        '客户因个人原因取消预定，申请退还定金。',
        '正常退租，退还保证金，房屋状况良好。',
        '未明流水退款处理，已核实为重复支付。',
        '提前退租，扣除违约金后退还剩余租金。',
        ''
    ]
    const randomRemark = remarks[Math.floor(Math.random() * remarks.length)]
    testData.refundRemark = randomRemark
    Message.info(`已更新退款申请说明: ${randomRemark || '(空)'} (${randomRemark.length}字符)`)
}

// 刷新回调
const handleRefresh = () => {
    Message.success('数据已刷新')
    console.log('刷新后的refundRemark:', testData.refundRemark)
}
</script>

<style scoped lang="less">
.test-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.direct-display {
    .info-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        
        .info-label {
            color: #646a73;
            margin-right: 8px;
            min-width: 120px;
            font-weight: 500;
        }
        
        .info-value {
            color: #1d2129;
            flex: 1;
            line-height: 1.5;
            word-break: break-all;
        }
    }
}
</style> 