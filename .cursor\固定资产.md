# 固定资产
    - 在 views 文件夹下 创建 固定资产 文件夹
## 页面结构
 - 筛选条件
    - 种类
    - 物品名称
 - 操作按钮
    - 新增
 - 表格
    - 字段：序号，种类，物品名称，规格，使用范围，描述，操作
    - 操作：编辑，删除，详情
    - 分页

## 新增
    1. 在固定资产 文件夹 创建 ‘新增’ 组件
    2. 组件内包含
        - 种类：下拉框，必填；选项：家电，家具，装饰，其他
        - 物品名称：输入框，必填
        - 规格：输入框
        - 使用范围：多选，必填；选项：宿舍，商铺，厂房，办公，中央食堂，综合体，车位，多经
        - 物品描述：textarea
        - 物品图片：上传图片
        - 操作按钮
            - 保存
            - 取消
## 接口
    - 接口是 .cursor/api/固定资产.md 文件
    - 在 api 文件夹下 创建 固定资产 接口文件
    - 根据 .cursor/api/固定资产.md 文件 创建 固定资产 接口文件
    
