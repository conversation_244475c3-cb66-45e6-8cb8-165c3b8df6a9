import http from './index';

/**
 * 临时收费审批状态枚举
 */
export enum TemporaryApproveStatus {
    /** 草稿 */
    DRAFT = 0,
    /** 审批中 */
    APPROVING = 1,
    /** 已通过 */
    APPROVED = 2,
    /** 已驳回 */
    REJECTED = 3,
    /** 作废 */
    CANCELLED = 4,
}

/**
 * 临时收费类型枚举
 */
export enum TemporaryCostType {
    /** 其他费用 */
    OTHER = 3,
}

/**
 * 临时收费添加DTO
 */
export interface TemporaryCostAddDTO {
    /** 主键ID */
    id?: string;
    /** 项目ID */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 客户ID */
    customerId?: string;
    /** 客户名称 */
    customerName?: string;
    /** 合同ID */
    contractId?: string;
    /** 合同编号 */
    contractNo?: string;
    /** 合同统一ID */
    unionId?: string;
    /** 房间名称，多个按照逗号分开 */
    roomName?: string;
    /** 收款用途id */
    subjectId?: string;
    /** 收款用途名称 */
    subjectName?: string;
    /** 账单类型: 3-其他费用 */
    costType?: TemporaryCostType;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 应收日期 */
    receivableDate?: string;
    /** 账单总额 */
    totalAmount?: number;
    /** 优惠金额 */
    discountAmount?: number;
    /** 实际应收金额 */
    actualReceivable?: number;
    /** 已收金额 */
    receivedAmount?: number;
    /** 说明 */
    remark?: string;
    /** 附件 */
    attachments?: string;
    /** 审批状态(0草稿 1审批中 2已通过 3已驳回 4作废) */
    approveStatus?: TemporaryApproveStatus;
    /** 审批通过时间 */
    approveTime?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 临时收费查询DTO
 */
export interface TemporaryCostQueryDTO {
    /** 查询参数 */
    params?: Record<string, any>;
    /** 页码 */
    pageNum: number;
    /** 页面大小 */
    pageSize: number;
    /** 项目ID */
    projectId?: string;
    /** 账单类型: 3-其他费用 */
    costType?: TemporaryCostType;
    /** 收款用途id列表 */
    subjectIds?: number[];
    /** 审批状态列表(0草稿 1审批中 2已通过 3已驳回 4作废) */
    approveStatus?: number[];
    /** 合同编号 */
    contractNo?: string;
    /** 房间名称，多个按照逗号分开 */
    roomName?: string;
    /** 客户名称 */
    customerName?: string;
    /** 合同统一ID */
    unionId?: string;
}

/**
 * 临时收费详情VO
 */
export interface TemporaryCostVo {
    /** 主键ID */
    id?: string;
    /** 项目ID */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 账单类型: 3-其他费用 */
    costType?: TemporaryCostType;
    /** 收款用途id */
    subjectId?: string;
    /** 收款用途名称 */
    subjectName?: string;
    /** 账单周期 */
    costPeriod?: string;
    /** 客户ID */
    customerId?: string;
    /** 客户名称 */
    customerName?: string;
    /** 合同ID */
    contractId?: string;
    /** 合同编号 */
    contractNo?: string;
    /** 合同统一ID */
    unionId?: string;
    /** 房间名称，多个按照逗号分开 */
    roomName?: string;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 合同开始日期 */
    contractStartDate?: string;
    /** 合同结束日期 */
    contractEndDate?: string;
    /** 合同周期 */
    contractPeriod?: string;
    /** 应收日期 */
    receivableDate?: string;
    /** 账单总额 */
    totalAmount?: number;
    /** 优惠金额 */
    discountAmount?: number;
    /** 实际应收金额 */
    actualReceivable?: number;
    /** 已收金额 */
    receivedAmount?: number;
    /** 说明 */
    remark?: string;
    /** 附件 */
    attachments?: string;
    /** 审批状态(0草稿 1审批中 2已通过 3已驳回 4作废) */
    approveStatus?: TemporaryApproveStatus;
    /** 审批通过时间 */
    approveTime?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 通用响应结果
 */
export interface AjaxResult {
    /** 是否有错误 */
    error?: boolean;
    /** 是否成功 */
    success?: boolean;
    /** 是否有警告 */
    warn?: boolean;
    /** 是否为空 */
    empty?: boolean;
    /** 附加属性 */
    [key: string]: any;
}

/**
 * 表格数据信息
 */
export interface TableDataInfo {
    /** 总数 */
    total?: number;
    /** 行数据 */
    rows?: any[];
    /** 状态码 */
    code?: number;
    /** 消息 */
    msg?: string;
}

/**
 * 合同费用减免DTO
 */
export interface ContractCostReductionDTO {
    /** 项目id */
    projectId?: string;
    /** 合同号 */
    contractNo?: string;
    /** 承租方名称 */
    customerName?: string;
    /** 房间id */
    roomId?: string;
    /** 承租方id */
    customerId?: string;
    /** 类型（1减免 2缓交 3分期） */
    type?: number;
    /** 合同id */
    contractId?: string;
    /** 账单类型,1-保证金,2-租金,3-其他费用 */
    costType?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
}

const systemUrl = '/business-rent-admin';

/**
 * 修改临时收费
 * @param data 临时收费信息
 * @returns
 */
export function editTemporaryCost(data: TemporaryCostAddDTO) {
    return http.put<boolean>(`${systemUrl}/temporary/edit`, data);
}

/**
 * 发起审批
 * @param id 临时收费ID
 * @returns
 */
export function submitTemporaryCost(id: string) {
    return http.post<boolean>(`${systemUrl}/temporary/submit?id=${id}`, {});
}

/**
 * 临时收费导出参数
 */
export interface TemporaryCostExportDTO {
    /** 项目ID */
    projectId?: string;
    /** 账单类型: 3-其他费用 */
    costType?: TemporaryCostType;
    /** 收款用途id列表 */
    subjectIds?: number[];
    /** 审批状态列表(0草稿 1审批中 2已通过 3已驳回 4作废) */
    approveStatus?: number[];
    /** 合同编号 */
    contractNo?: string;
    /** 房间名称，多个按照逗号分开 */
    roomName?: string;
    /** 客户名称 */
    customerName?: string;
}

/**
 * 导出临时收费列表
 * @param params 导出参数
 * @returns
 */
export function exportTemporaryCostList(params: TemporaryCostExportDTO) {
    return http.post(`${systemUrl}/temporary/export`, params, {
        responseType: 'blob'
    });
}

/**
 * 审批通过临时收费
 * @param id 临时收费ID
 * @returns
 */
export function approveTemporaryCost(id: string) {
    return http.post<AjaxResult>(`${systemUrl}/temporary/approve?id=${id}`, {});
}

/**
 * 新增临时收费
 * @param data 临时收费信息
 * @returns
 */
export function addTemporaryCost(data: TemporaryCostAddDTO) {
    return http.post<AjaxResult>(`${systemUrl}/temporary/add`, data);
}

/**
 * 查询临时收费列表
 * @param params 查询参数
 * @returns
 */
export function getTemporaryCostList(params: TemporaryCostQueryDTO) {
    return http.post<TableDataInfo>(`${systemUrl}/temporary/list`, params);
}

/**
 * 获取临时收费详细信息
 * @param id 临时收费ID
 * @returns
 */
export function getTemporaryCostDetail(id: string) {
    return http.get<TemporaryCostVo>(`${systemUrl}/temporary/detail?id=${id}`, {});
}

/**
 * 删除临时收费
 * @param id 临时收费ID列表
 * @returns
 */
export function deleteTemporaryCost(id: string) {
    return http.delete<boolean>(`${systemUrl}/temporary/delete?id=${id}`, {});
}

/**
 * 获取符合条件的合同
 * @param data 查询条件
 * @returns
 */
export function getContractList(data: ContractCostReductionDTO) {
    return http.post<AjaxResult>(`${systemUrl}/temporary/contract/list`, data);
}
