import { DirectiveBinding } from 'vue';
import { useUserStore } from '@/store';

function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding;
    const userStore = useUserStore();
    const { permissions } = userStore;
    // console.log('permissions', permissions);
    // console.log('value', value);
    
    if (Array.isArray(value)) {
        if (value.length > 0) {
            const permissionValues = value;
            
            // 如果用户拥有超级权限，则默认拥有所有权限
            if (permissions.includes("*:*:*")) {
                return;
            }
            
            // 检查是否有匹配的权限
            const hasPermission = permissionValues.some(permission => 
                permissions.includes(permission)
            );
            
            if (!hasPermission && el.parentNode) {
                el.parentNode.removeChild(el);
            }
        }
    } else {
        throw new Error(`need permissions! Like v-permission="['system:user:list']"`);
    }
}

export default {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
        checkPermission(el, binding);
    },
    updated(el: HTMLElement, binding: DirectiveBinding) {
        checkPermission(el, binding);
    },
};
