<template>
  <div style="padding: 20px;">
    <h3>测试房源选择组件 - Object类型</h3>
    
    <div style="margin-bottom: 20px;">
      <h4>当前值：</h4>
      <pre>{{ JSON.stringify(selectedRoom, null, 2) }}</pre>
    </div>
    
    <div style="margin-bottom: 20px;">
      <h4>房源选择（Object类型）：</h4>
      <RoomTreeSelect 
        v-model="selectedRoom"
        value-type="object"
        style="width: 300px;"
        @change="handleRoomChange"
      />
    </div>
    
    <div style="margin-bottom: 20px;">
      <a-button @click="setTestValue">设置测试值（ID: 123, Name: 123）</a-button>
      <a-button @click="clearValue" style="margin-left: 10px;">清空</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import RoomTreeSelect from '@/components/roomTreeSelect/index.vue'

const selectedRoom = ref<{ id: string | number; name: string } | null>(null)

const handleRoomChange = (value: any) => {
  console.log('房源选择变化:', value)
  const displayValue = typeof value === 'object' ? `${value.name} (${value.id})` : value
  Message.success(`已选择房源: ${displayValue}`)
}

const setTestValue = () => {
  selectedRoom.value = {
    id: 123,
    name: '123' // 故意设置name为ID，测试是否会自动更新为正确的房源名称
  }
}

const clearValue = () => {
  selectedRoom.value = null
}
</script> 