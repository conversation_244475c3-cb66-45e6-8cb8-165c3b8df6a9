<template>
    <a-drawer class="common-drawer" v-model:visible="drawerVisible" title="合同详情" :body-style="{ padding: 0 }">
        <contractDetail v-if="drawerVisible" />
        <template #footer>
            <a-space>
                <!-- <a-button v-if="contractStore.editType === 'edit'" @click="handleCancel">取消</a-button> -->
                <!-- <a-button v-if="contractStore.editType === 'edit'" type="primary" @click="handleCancel">保存</a-button> -->
                <a-button v-if="[10].includes(contractData.status as number)" type="primary" @click="handleEdit">编辑</a-button>
                <a-button v-permission="['rent:contract:detail:print']" type="primary" @click="handlePreview" :loading="printLoading">预览&打印</a-button>
                <a-dropdown @select="handleMoreAction">
                    <a-button type="primary">
                        更多操作
                        <icon-down />
                    </a-button>
                    <template #content>
                        <a-doption value="history">历史版本</a-doption>
                        <a-doption v-if="[30, 40].includes(contractData.status as number)" value="renew">续签</a-doption>
                        <a-doption v-if="contractData.customer.customerType === 2"
                            value="updateBank">更新付款银行</a-doption>
                        <a-doption v-if="contractData.customer.customerType === 2"
                            value="updateContact">更新联系人</a-doption>
                        <a-doption value="updateSignMethod">更新签约方式</a-doption>
                        <a-doption v-if="contractData.customer.customerType === 1"
                            value="updateRentNo">更新承租人手机号</a-doption>
                        <a-doption value="uploadFile">上传签署附件</a-doption>
                        <a-doption value="audit">审批记录</a-doption>
                    </template>
                </a-dropdown>
            </a-space>
        </template>
        <!-- 编辑/续签合同 -->
        <addContractDrawer ref="createContractRef" @submit="handleUpdate" />
        <!-- 上传签署附件 -->
        <uploadFileDialog ref="uploadFileDialogRef" @submit="handleUpdate" />
        <historyVersion ref="historyVersionRef" />

        <!-- 模版选择弹窗 -->
        <TemplateSelectModal v-model="templateModalVisible" :contract-id="contractData.id || ''"
            @select="handleTemplateSelected" />

        <!-- WordViewer预览弹窗 -->
        <WordViewer v-model="wordViewerVisible" :file-url="printFileUrl" :title="'合同预览'"
            @cancel="handleWordViewerCancel" />
        <!-- 更新付款账号 更新联系人 更新承租人手机号 更新签约方式 -->
        <updateContractDrawer ref="updateContractDrawerRef" @submit="handleUpdate" />
    </a-drawer>
</template>

<script setup lang="ts">
import addContractDrawer from './addContractDrawer.vue'
import { useContractStore } from '@/store/modules/contract/index';
import { getContractById, ContractAddDTO, ContractVo, getContractDetailBill, print, getContractTemplateList } from '@/api/contract';
import customerApi from '@/api/customer'
import uploadFileDialog from './contractDetail/uploadFileDialog.vue'
import historyVersion from './contractDetail/historyVersion.vue'
import contractDetail from './contractDetail/index.vue'
// 导入模版选择组件
import TemplateSelectModal from './components/TemplateSelectModal.vue'
// 导入WordViewer组件
import WordViewer from '@/components/WordViewer/index.vue'
import { Message } from '@arco-design/web-vue'
import updateContractDrawer from './updateContractDrawer.vue';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

const drawerVisible = ref(false)

// 模版选择和打印相关状态
const templateModalVisible = ref(false)
const wordViewerVisible = ref(false)
const printFileUrl = ref('')
const printLoading = ref(false)

const open = (record: any) => {
    contractStore.resetContractDetail()
    contractStore.resetHistoryVersionContractDetail()
    drawerVisible.value = true
    fetchContractDetail(record.id)
}

const emits = defineEmits(['submit'])
const handleUpdate = () => {
    fetchContractDetail()
    emits('submit')
}

// 获取合同详情
const fetchContractDetail = async (id?: string) => {
    try {
        const { data } = await getContractById(id || contractData.value.id as string);
        if (data) {
            // 更新合同数据
            const contractInfo = data as ContractVo;

            contractInfo.fees = contractInfo.fees || []
            contractInfo.costs = contractInfo.costs || []
            contractInfo.rooms = contractInfo.rooms || []
            contractInfo.rentReceivableDate = Number(contractInfo.rentReceivableDate)

            // 编辑时直接使用原数据16px
            contractStore.contractDetail = { ...contractStore.contractDetail, ...contractInfo as any };
            fetchBill()
            fetchLesseeInfo()
        }
    } catch (error) {
        console.error(error);
    }
};

// 获取账单
const fetchBill = async () => {
    const { data } = await getContractDetailBill(contractData.value.id as string)
    contractStore.contractDetailCosts = data
}

// 获取承租人信息
const fetchLesseeInfo = async () => {
    const { data } = await customerApi.getCustomerDetail(contractData.value.customer.customerId as string)
    contractStore.contractLesseeInfo = data
}

// 处理预览&打印按钮点击
const handlePreview = async () => {
    if (!contractData.value.id) {
        Message.error('合同ID不能为空')
        return
    }

    try {
        printLoading.value = true

        // 先获取模版列表
        const { data: templateList, code } = await getContractTemplateList(contractData.value.id)

        if (code === 200 && templateList) {
            if (templateList.length === 0) {
                Message.warning('暂无可用的打印模版')
                return
            } else if (templateList.length === 1) {
                // 只有一个模版时，直接使用该模版生成预览
                const templateId = templateList[0].id
                await handleTemplateSelected(templateId)
            } else {
                // 多个模版时，显示选择弹窗
                templateModalVisible.value = true
            }
        } else {
            Message.error('获取模版列表失败')
        }
    } catch (error) {
        console.error('获取模版列表失败:', error)
        Message.error('获取模版列表失败')
    } finally {
        printLoading.value = false
    }
}

// 处理模版选择完成
const handleTemplateSelected = async (templateId: string) => {
    const contractId = contractData.value.id

    if (!contractId) {
        Message.error('合同ID不能为空')
        return
    }

    try {
        printLoading.value = true

        // 调用print接口
        const { data: result, code } = await print(contractId, templateId)

        if (code === 200 && result) {
            // 假设print接口返回的数据结构中包含fileUrl字段
            printFileUrl.value = result.fileUrl || ''

            if (printFileUrl.value) {
                // 关闭模板选择弹窗
                templateModalVisible.value = false
                // 打开WordViewer预览弹窗
                wordViewerVisible.value = true
                Message.success('合同预览生成成功')
            } else {
                Message.error('未获取到合同预览文件')
            }
        } else {
            Message.error('生成合同预览失败')
        }
    } catch (error) {
        console.error('生成合同预览失败:', error)
        Message.error('生成合同预览失败')
    } finally {
        printLoading.value = false
    }
}

// 处理WordViewer关闭
const handleWordViewerCancel = () => {
    wordViewerVisible.value = false
}

const createContractRef = ref()
const updateContractDrawerRef = ref()
const handleMoreAction = (value: string) => {
    if (value === 'renew') {
        createContractRef.value.open('renew', '', contractData.value)
    } else if (value === 'history') {
        handleHistoryVersion()
    } else if (value === 'updateBank') {
        updateContractDrawerRef.value.open('updateBank', '', contractData.value)
    } else if (value === 'updateContact') {
        updateContractDrawerRef.value.open('updateContact', '', contractData.value)
    } else if (value === 'updateSignMethod') {
        updateContractDrawerRef.value.open('updateSignMethod', '', contractData.value)
    } else if (value === 'uploadFile') {
        handleUploadFile()
    } else if (value === 'updateRentNo') {
        updateContractDrawerRef.value.open('updateRentNo', '', contractData.value)
    } else if (value === 'audit') {
        // 点击跳转到OA对应的审批页面
        // window.open(`${import.meta.env.VITE_OA_URL}/contract/audit?id=${contractData.value.id}`, '_blank')
    }
}

const handleEdit = () => {
    createContractRef.value.open('edit', '', contractData.value)
}

// 上传附件
const uploadFileDialogRef = ref()
const handleUploadFile = () => {
    uploadFileDialogRef.value.open(contractData.value.id as string)
}

// 历史版本
const historyVersionRef = ref()
const handleHistoryVersion = () => {
    historyVersionRef.value.open()
}

defineExpose({
    open
})
</script>

<style lang="less" scoped>
.arco-card {
    .wrap {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
}
</style>