<template>
    <div class="base-info">
        <div class="section">
            <section-title title="基础信息" />
            <div class="section-content">
                <a-row :gutter="24">
                    <!-- 左侧表单 -->
                    <a-col :span="16">
                        <a-form :model="lesseeInfo" auto-label-width>
                            <a-row :gutter="16" v-if="lesseeInfo.customerType === 1">
                                <a-col :span="12">
                                    <a-form-item label="姓名" field="customerName">
                                        {{ lesseeInfo.customerName }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="联系电话" field="contactPhone">
                                        {{ lesseeInfo.contactPhone }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="证件类型" field="idType">
                                        {{ idTypeMap.get(Number(lesseeInfo.idType)) }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="证件号码" field="idNumber">
                                        {{ lesseeInfo.idNumber }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="证件有效期" field="idValidPeriod">
                                        {{ lesseeInfo.idValidityStart }} - {{ lesseeInfo.idValidityEnd }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="24">
                                    <a-form-item label="联系地址" field="contactAddress">
                                        {{ lesseeInfo.contactAddress }}
                                    </a-form-item>
                                </a-col>
                            </a-row>
                            <a-row :gutter="16" v-if="lesseeInfo.customerType === 2">
                                <a-col :span="12">
                                    <a-form-item label="企业名称">
                                        {{ lesseeInfo.customerName }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="统一社会信用代码">
                                        {{ lesseeInfo.creditCode }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="法人姓名">
                                        {{ lesseeInfo.legalName }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="法人联系电话">
                                        {{ lesseeInfo.contactPhone }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="法人证件类型">
                                        {{ idTypeMap.get(Number(lesseeInfo.idType)) }}
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="法人证件号码">
                                        {{ lesseeInfo.idNumber }}
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </a-form>
                    </a-col>

                    <!-- 右侧图片识别区域 -->
                    <a-col :span="8">
                        <a-row :gutter="16" v-if="lesseeInfo.customerType === 1">
                            <a-col :span="12">
                                <a-image width="100%" height="160px" fit="cover" v-if="lesseeInfo.idFront" class="customer-image"
                                    :src="lesseeInfo.idFront" alt="" />
                            </a-col>
                            <a-col :span="12">
                                <a-image width="100%" height="160px" fit="cover" v-if="lesseeInfo.idBack" class="customer-image"
                                    :src="lesseeInfo.idBack" alt="" />
                            </a-col>
                        </a-row>
                        <a-row :gutter="16" v-if="lesseeInfo.customerType === 2">
                            <a-col :span="12">
                                <a-image width="100%" v-if="lesseeInfo.businessLicense" class="customer-image"
                                    :src="lesseeInfo.businessLicense" alt="" />
                            </a-col>
                        </a-row>
                    </a-col>
                </a-row>
            </div>
        </div>

        <div class="section">
            <section-title :title="lesseeInfo.customerType === 2 ? '经办人信息' : '联系人信息'" />
            <div class="section-content">
                <a-table :data="lesseeInfo.contactList" :pagination="false">
                    <template #columns>
                        <a-table-column title="姓名" data-index="name" :width="100" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="电话" data-index="phone" :width="120" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column v-if="lesseeInfo.customerType === 1" title="关系" data-index="relationship"
                            :width="110" ellipsis tooltip align="center">
                            <template #cell="{ record }">
                                {{ relationshipMap.get(record.relationship) }}
                            </template>
                        </a-table-column>
                        <a-table-column title="性别" data-index="gender" :width="140" ellipsis tooltip align="center">
                            <template #cell="{ record }">
                                {{ genderMap.get(record.gender) }}
                            </template>
                        </a-table-column>
                        <a-table-column title="证件号码" data-index="idNumber" :width="140" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column v-if="lesseeInfo.customerType === 2" title="职务" data-index="position"
                            :width="120" ellipsis tooltip align="center"></a-table-column>
                        <a-table-column v-if="lesseeInfo.customerType === 2" title="首选联系人" data-index="isPreferred"
                            :width="120" ellipsis tooltip align="center">
                            <template #cell="{ record }">
                                <a-radio
                                    :model-value="record.isPreferred"
                                    disabled
                                />
                            </template>
                        </a-table-column>
                        <a-table-column title="备注" data-index="remark" :width="140" ellipsis tooltip
                            align="center"></a-table-column>
                    </template>
                </a-table>
            </div>
        </div>

        <div class="section">
            <section-title title="担保人信息" />
            <div class="section-content">
                <a-table :data="lesseeInfo.guarantorList" :pagination="false">
                    <template #columns>
                        <a-table-column title="姓名" data-index="name" :width="100" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="电话" data-index="phone" :width="140" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="证件类型" data-index="idType" :width="100" ellipsis tooltip align="center">
                            <template #cell="{ record }">
                                {{ idTypeMap.get(Number(record.idType)) }}
                            </template>
                        </a-table-column>
                        <a-table-column title="证件号码" data-index="idNumber" :width="100" ellipsis tooltip align="center">
                        </a-table-column>
                        <a-table-column title="通讯地址" data-index="address" :width="200" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="备注" data-index="remark" :width="150" ellipsis tooltip
                            align="center"></a-table-column>
                    </template>
                </a-table>
            </div>
        </div>

        <div class="section">
            <section-title title="银行帐号" />
            <div class="section-content">
                <a-table :data="lesseeInfo.bankAccountList" :pagination="false">
                    <template #columns>
                        <a-table-column title="银行" data-index="bankName" :width="200" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="账号" data-index="accountNumber" :width="220" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="备注" data-index="accountRemark" :width="180" ellipsis tooltip
                            align="center"></a-table-column>
                    </template>
                </a-table>
            </div>
        </div>

        <div class="section" v-if="lesseeInfo.customerType === 2">
            <section-title title="开票信息" />
            <div class="section-content">
                <a-table :data="lesseeInfo.invoiceList" :pagination="false">
                    <template #columns>
                        <a-table-column title="抬头名称" data-index="title" :width="140" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="税号" data-index="taxNumber" :width="180" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="单位地址" data-index="address" :width="180" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="电话号码" data-index="phone" :width="140" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="开户银行" data-index="bankName" :width="140" ellipsis tooltip
                            align="center"></a-table-column>
                        <a-table-column title="银行账号" data-index="accountNumber" :width="180" ellipsis tooltip
                            align="center"></a-table-column>
                    </template>
                </a-table>
            </div>
        </div>

        <div class="section">
            <section-title title="备注" />
            <div class="section-content">
                {{ lesseeInfo.remark }}
            </div>
        </div>

        <div class="section">
            <section-title title="附件" />
            <div class="section-content">
                <upload-file v-model="attachmentFiles" readonly />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import { CustomerVo } from '@/api/customer'
import uploadFile from '@/components/upload/uploadFile.vue'
const contractStore = useContractStore()
const lesseeInfo = computed(() => contractStore.historyVersionContractLesseeInfo as CustomerVo)
const attachmentFiles = computed(() => lesseeInfo.value.attachmentFiles || '[]')

const idTypeMap = new Map([
    [1, '身份证'],
    [2, '护照'],
    [3, '军官证'],
])

const genderMap = new Map([
    [1, '男'],
    [2, '女']
])
const relationshipMap = new Map([
    [1, '夫妻'],
    [2, '合伙人'],
    [3, '父母'],
    [4, '朋友'],
    [5, '子女'],
    [6, '其他'],
])
</script>

<style scoped lang="less">
.base-info {
    .section {
        &+.section {
            margin-top: 26px;
        }

        .section-content {
            margin-top: 16px;
        }
    }

    .customer-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}
</style>