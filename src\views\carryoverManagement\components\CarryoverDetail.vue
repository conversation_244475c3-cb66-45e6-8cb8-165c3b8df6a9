<template>
    <a-drawer v-model:visible="visible" title="结转单详情" width="80%" @cancel="handleClose" :footer="null">
        <div v-if="detailData && detailData.carryoverInfo" class="carryover-detail">
            <a-descriptions :column="2" bordered>
                <a-descriptions-item label="项目名称">{{ detailData.carryoverInfo.projectName || '-'
                    }}</a-descriptions-item>
                <a-descriptions-item label="结转单号">{{ detailData.carryoverInfo.carryoverNo || '-'
                    }}</a-descriptions-item>
                <a-descriptions-item label="结转日期">{{ detailData.carryoverInfo.carryoverTime || '-'
                    }}</a-descriptions-item>
                <a-descriptions-item label="结转金额">{{ detailData.carryoverInfo.carryoverAmount || 0
                    }}</a-descriptions-item>
                <a-descriptions-item label="转出业务" :span="2">
                    {{ bizTypeMap[detailData.carryoverInfo.fromBizType] || '未知' }} - {{
                        detailData.carryoverInfo.fromBizNo || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="转出费用科目" :span="2">
                    {{ detailData.carryoverInfo.fromSubjectName || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="转入业务" :span="2">
                    {{ bizTypeMap[detailData.carryoverInfo.toBizType] || '未知' }} - {{ detailData.carryoverInfo.toBizNo
                    || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="转入费用科目" :span="2">
                    {{ detailData.carryoverInfo.toSubjectName || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="结转说明" :span="2">
                    {{ detailData.carryoverInfo.carryoverRemark || '-' }}
                </a-descriptions-item>
            </a-descriptions>

            <div class="mt-4">
                <h3>结转流水明细</h3>
                <a-table :columns="flowColumns" :data="detailData.carryoverFlowList || []" :pagination="false"
                    row-key="id" />
            </div>
        </div>
        <a-empty v-else description="暂无详情数据" />
    </a-drawer>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits } from 'vue'
import { getCarryoverDetail } from '@/api/carryoverManagement'

const props = defineProps({
    id: {
        type: String,
        default() {
            return '';
        },
    },
});

const emit = defineEmits(['close'])

const visible = ref(true)
const detailData = ref(null)

const bizTypeMap = {
    0: '定单',
    1: '合同',
    2: '未明流水'
}

const flowColumns = [
    {
        title: '流水单号', dataIndex: 'flowNo', key: 'flowNo', ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '转出业务',
        key: 'fromBizType',
        customRender: ({ record }) => `${bizTypeMap[record.fromBizType]} - ${record.fromBizNo}`,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '转出费用科目', dataIndex: 'fromSubjectName', key: 'fromSubjectName', ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '转入业务',
        key: 'toBizType',
        customRender: ({ record }) => `${bizTypeMap[record.toBizType]} - ${record.toBizNo}`,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '转入费用科目', dataIndex: 'toSubjectName', key: 'toSubjectName', ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '结转金额', dataIndex: 'carryoverAmount', key: 'carryoverAmount', ellipsis: true,
        tooltip: true,
        align: 'center'
    }
]

const fetchDetail = async () => {
  try {
    const response = await getCarryoverDetail(props.id)
    
    if (response && response.data) {
      detailData.value = response.data
    } else {
      detailData.value = response
    }
  } catch (error) {
    console.error('获取结转详情失败', error)
  }
}

const handleClose = () => {
    visible.value = false
    emit('close')
}

onMounted(fetchDetail)
</script>

<style scoped>
.carryover-detail {
    padding: 20px;
}
</style>