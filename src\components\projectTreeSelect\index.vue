<template>
  <!-- 【扩展】支持多选：通过multiple属性控制单选/多选模式 -->
  <a-tree-select 
    v-model="selectedOrgId" 
    :data="orgTreeData"
    :field-names="{ children: 'children', key: 'id', title: 'name' }" 
    :placeholder="multiple ? '请选择项目（可多选）' : '请选择项目'" 
    allow-search
    :selectable="'leaf'"
    :multiple="multiple"
    :tree-checkable="multiple"
    :tree-check-strictly="multiple"
    :filter-tree-node="filterTreeNode" 
    :loading="loading" 
    @change="handleOrgChange" 
    v-bind="$attrs" 
  />
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { getOrgTree } from '@/api/org'
import useProjectStore from '@/store/modules/project'

interface Props {
  modelValue?: string | number | (string | number)[] | string[] | number[]
  minLevel?: number
  multiple?: boolean // 【新增】是否支持多选
}

interface OrgNode {
  id: string | number
  name: string
  children?: OrgNode[]
  [key: string]: any
}

const projectStore = useProjectStore()

const emit = defineEmits(['update:modelValue', 'change'])

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  minLevel: 4,
  multiple: false // 【新增】默认为单选模式，保持向后兼容
})
const loading = ref(false)
const orgTreeData = ref<OrgNode[]>([])

// 【扩展】根据multiple属性处理不同的数据类型
const selectedOrgId = computed({
  get() {
    if (props.multiple) {
      // 多选模式：确保返回数组
      if (Array.isArray(props.modelValue)) {
        return props.modelValue
      }
      return props.modelValue ? [props.modelValue] : []
    } else {
      // 单选模式：返回单个值
      if (Array.isArray(props.modelValue)) {
        return props.modelValue[0] || ''
      }
      return props.modelValue || ''
    }
  },
  set(value) {
    // 这里的value由a-tree-select组件传递
    handleOrgChange(value)
  }
})

// 检查节点是否满足最小层级要求
const checkMinLevel = (node: OrgNode, currentLevel: number = 1): boolean => {
    if (props.minLevel <= 0) return true // 没有层级要求
    
    // 如果当前层级已经达到要求，返回true
    if (currentLevel >= props.minLevel) return true
    
    // 如果有子节点，递归检查子节点
    if (node.children && node.children.length > 0) {
        return node.children.some(child => checkMinLevel(child, currentLevel + 1))
    }
    
    // 没有子节点且当前层级不满足要求
    return false
}

// 过滤树数据，移除不满足层级要求的节点
const filterByLevel = (nodes: OrgNode[], currentLevel: number = 1): OrgNode[] => {
    if (props.minLevel <= 0) return nodes // 没有层级要求
    
    return nodes.filter(node => {
        // 检查当前节点是否满足层级要求
        if (!checkMinLevel(node, currentLevel)) {
            return false
        }
        
        return true
    }).map(node => {
        // 创建新的节点对象，避免修改原始数据
        const newNode = { ...node }
        
        // 如果有子节点，递归过滤子节点
        if (node.children && node.children.length > 0) {
            const filteredChildren = filterByLevel(node.children, currentLevel + 1)
            // 如果过滤后没有子节点了，需要重新检查是否满足层级要求
            if (filteredChildren.length === 0) {
                // 如果当前层级不满足要求，则不包含此节点
                if (currentLevel < props.minLevel) {
                    return null
                }
                newNode.children = []
            } else {
                newNode.children = filteredChildren
            }
        }
        
        return newNode
    }).filter(node => node !== null) as OrgNode[]
}

// 获取组织树数据
const getOrgTreeData = async () => {
  loading.value = true
  try {
    const { data } = await getOrgTree()
    orgTreeData.value = filterByLevel(data as OrgNode[] || [])
    
    // 【优化】根据多选模式处理默认选择逻辑
    if (props.multiple) {
      // 多选模式：如果没有传入值，设置为空数组，不做自动选择
      if (!props.modelValue || (Array.isArray(props.modelValue) && props.modelValue.length === 0)) {
        // 多选模式下不自动选择，保持空数组
        return
      }
    } else {
      // 单选模式：完全保持原有逻辑不变
      if (!projectStore.rentProjectId && !props.modelValue) {
      // 递归查找第一个叶子节点
      const findFirstLeafNode = (nodes: OrgNode[]): OrgNode | null => {
        for (const node of nodes) {
          if (!node.children || node.children.length === 0) {
            return node
          }
          const leaf = findFirstLeafNode(node.children)
          if (leaf) return leaf
        }
        return null
      }

      const firstLeaf = findFirstLeafNode(orgTreeData.value)
      if (firstLeaf) {
          handleOrgChange(firstLeaf.id)
        }
      } else if (projectStore.rentProjectId && !props.modelValue) {
        handleOrgChange(projectStore.rentProjectId)
      }
    }
  } catch (error) {
    orgTreeData.value = []
  } finally {
    loading.value = false
  }
}

// 【扩展】处理组织选择变化：支持单选和多选
const handleOrgChange = (value: string | number | (string | number)[] | string[] | number[]) => {
  // 递归查找选中的组织节点
  const findOrgNode = (nodes: OrgNode[], targetId: string | number): OrgNode | null => {
    for (const node of nodes) {
      if (node.id === targetId) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findOrgNode(node.children, targetId)
        if (found) return found
      }
    }
    return null
  }

  if (props.multiple) {
    // 【新增】多选模式处理
    const selectedValues = Array.isArray(value) ? value : (value ? [value] : [])
    const selectedOrgs = selectedValues.map(id => findOrgNode(orgTreeData.value, id)).filter(Boolean)
    
    emit('update:modelValue', selectedValues)
    emit('change', selectedValues, selectedOrgs)
  } else {
    // 【保持】单选模式处理（保持向后兼容）
    const singleValue = Array.isArray(value) ? value[0] : value
    const selectedOrg = singleValue ? findOrgNode(orgTreeData.value, singleValue) : null
    
    if (!!singleValue) {
      projectStore.setRentProjectId(singleValue as string)
  }
    emit('update:modelValue', singleValue)
    emit('change', singleValue, selectedOrg)
  }
}

const filterTreeNode = (value: string, node: any) => {
  return node.name?.includes(value)
}

// 组件挂载时加载组织树
onMounted(() => {
  getOrgTreeData()
})

// 【简化】监听外部传入的值变化：使用computed自动处理，减少复杂逻辑
// 由于使用了computed的selectedOrgId，大部分同步逻辑已经自动处理
// 只在必要时进行额外处理
watch(() => props.modelValue, () => {
  // 监听器主要用于触发computed重新计算
  // 具体的值处理逻辑都在computed中完成，更安全
}, {
  immediate: true
})
</script>

<style scoped lang="less">
// 组件样式可以根据需要添加
</style>
