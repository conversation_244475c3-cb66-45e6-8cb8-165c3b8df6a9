import http from './index';

/**
 * 账单状态枚举
 */
export enum CostStatus {
    /** 待收 */
    PENDING_RECEIVE = 0,
    /** 待付 */
    PENDING_PAY = 1,
    /** 已收 */
    RECEIVED = 2,
    /** 已付 */
    PAID = 3,
}

/**
 * 账单类型枚举
 */
export enum CostType {
    /** 保证金 */
    DEPOSIT = 1,
    /** 租金 */
    RENT = 2,
    /** 其他费用 */
    OTHER = 3,
}

/**
 * 收费类别枚举
 */
export enum ChargeType {
    /** 定单 */
    ORDER = 0,
    /** 合同 */
    CONTRACT = 1,
}

/**
 * 确认状态枚举
 */
export enum ConfirmStatus {
    /** 待确认 */
    PENDING = 0,
    /** 已确认 */
    CONFIRMED = 1,
}

/**
 * 合同类型枚举
 */
export enum ContractType {
    /** 非宿舍 */
    NON_DORMITORY = 0,
    /** 宿舍 */
    DORMITORY = 1,
    /** 多经 */
    MULTIPLE = 2,
    /** 日租房 */
    DAILY_RENT = 3,
}

/**
 * 子状态枚举
 */
export enum SubStatus {
    /** 逾期 */
    OVERDUE = 0,
    /** 今日 */
    TODAY = 1,
    /** 近7天 */
    SEVEN_DAYS = 2,
    /** 待确认 */
    PENDING_CONFIRM = 3,
    /** 已确认 */
    CONFIRMED = 4,
}

/**
 * 流水类型枚举
 */
export enum FlowType {
    /** 收款 */
    RECEIVE = 1,
    /** 转入 */
    TRANSFER_IN = 2,
    /** 转出 */
    TRANSFER_OUT = 3,
    /** 退款 */
    REFUND = 4,
}

/**
 * 流水确认状态枚举
 */
export enum FlowConfirmStatus {
    /** 未确认 */
    UNCONFIRMED = 0,
    /** 自动确认 */
    AUTO_CONFIRMED = 1,
    /** 手动确认 */
    MANUAL_CONFIRMED = 2,
}

/**
 * 账单添加DTO
 */
export interface CostAddDTO {
    /** 创建人账号 */
    createBy?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新人账号 */
    updateBy?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 主键ID */
    id?: string;
    /** 项目id */
    projectId?: string;
    /** 收费类别: 0-定单, 1-合同 */
    chargeType?: ChargeType;
    /** 业务id, 定单类别对应定单id, 合同类别对应合同id */
    bizId?: string;
    /** 业务单号 */
    bizNo?: string;
    /** 承租人id */
    customerId?: string;
    /** 承租人名称 */
    customerName?: string;
    /** 账单类型: 1-保证金,2-租金,3-其他费用 */
    costType?: CostType;
    /** 账单期数 */
    period?: number;
    /** 收款用途id */
    subjectId?: string;
    /** 收款用途名称 */
    subjectName?: string;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 应收日期 */
    receivableDate?: string;
    /** 账单状态: 0-待收、1-待付、2-已收、3-已付 */
    status?: CostStatus;
    /** 是否可收/付 0-否,1-是 */
    canPay?: boolean;
    /** 账单总额 */
    totalAmount?: number;
    /** 优惠金额 */
    discountAmount?: number;
    /** 实际应收金额 */
    actualReceivable?: number;
    /** 已收金额 */
    receivedAmount?: number;
    /** 结转金额 */
    carryoverAmount?: number;
    /** 确认状态: 0-待确认, 1-已确认 */
    confirmStatus?: ConfirmStatus;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 账单查询DTO
 */
export interface CostQueryDTO {
    /** 查询参数 */
    params?: Record<string, any>;
    /** 页码 */
    pageNum: number;
    /** 页面大小 */
    pageSize: number;
    /** 主键ID */
    id?: string;
    /** 项目id */
    projectId?: string;
    /** 收费类别: 0-定单, 1-合同 */
    chargeType?: ChargeType;
    /** 业务id, 定单类别对应定单id, 合同类别对应合同id */
    bizId?: string;
    /** 业务单号 */
    bizNo?: string;
    /** 承租人id */
    customerId?: string;
    /** 承租人名称 */
    customerName?: string;
    /** 账单类型: 1-保证金,2-租金,3-其他费用 */
    costType?: CostType;
    /** 账单期数 */
    period?: number;
    /** 收款用途id */
    subjectId?: string;
    /** 收款用途名称 */
    subjectName?: string;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 应收日期 */
    receivableDate?: string;
    /** 账单状态: 0-待收、1-待付、2-已收、3-已付 */
    status?: CostStatus;
    /** 是否可收/付 0-否,1-是 */
    canPay?: boolean;
    /** 账单总额 */
    totalAmount?: number;
    /** 优惠金额 */
    discountAmount?: number;
    /** 实际应收金额 */
    actualReceivable?: number;
    /** 已收金额 */
    receivedAmount?: number;
    /** 结转金额 */
    carryoverAmount?: number;
    /** 确认状态: 0-待确认, 1-已确认 */
    confirmStatus?: ConfirmStatus;
    /** 创建人账号 */
    createBy?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新人账号 */
    updateBy?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房 */
    contractType?: ContractType;
    /** 子状态(0-逾期,1-今日,2-近7天,3-待确认,4-已确认) */
    subStatus?: SubStatus;
    /** 房间名称 */
    roomName?: string;
    /** 账单状态（多选拼接） */
    statusList?: string;
    /** 应收日期开始 */
    receivableDateStart?: string;
    /** 应收日期结束 */
    receivableDateEnd?: string;
}

/**
 * 账单统计结果
 */
export interface CostSummaryVo {
    /** 逾期数 */
    overdueCount?: number;
    /** 今日数 */
    todayCount?: number;
    /** 近七天数 */
    sevenDaysCount?: number;
    /** 待确认数 */
    pendingConfirmCount?: number;
    /** 已确认数 */
    confirmedCount?: number;
}

/**
 * 记账记录DTO
 */
export interface CostFlowRelAddDTO {
    /** 创建人账号 */
    createBy?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新人账号 */
    updateBy?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 主键ID */
    id?: string;
    /** 账单id */
    costId?: string;
    /** 流水id */
    flowId?: string;
    /** 流水单号 */
    flowNo?: string;
    /** 账单类型：1-收款 2-转入 3-转出 4-退款 */
    type?: FlowType;
    /** 确认状态: 0-未确认、1-自动确认、2-手动确认 */
    confirmStatus?: FlowConfirmStatus;
    /** 确认时间 */
    confirmTime?: string;
    /** 确认人id */
    confirmUserId?: string;
    /** 确认人姓名 */
    confirmUserName?: string;
    /** 支付金额 */
    payAmount?: number;
    /** 本次记账金额 */
    acctAmount?: number;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 保存记账DTO
 */
export interface CostSaveRecordDTO {
    /** 账单id */
    costId: string;
    /** 记账记录列表 */
    flowRelList: CostFlowRelAddDTO[];
}

/**
 * 确认记账DTO
 */
export interface CostConfirmRecordDTO {
    /** 账单id */
    costId: string;
    /** 是否一键确认 */
    isOneKeyConfirm: boolean;
    /** 记账记录id（非一键确认时必填） */
    flowRelId?: string;
}

/**
 * 账单信息VO
 */
export interface CostVo {
    /** 主键ID */
    id?: string;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 收费类别: 0-定单, 1-合同 */
    chargeType?: ChargeType;
    /** 业务id, 定单类别对应定单id, 合同类别对应合同id */
    bizId?: string;
    /** 业务单号 */
    bizNo?: string;
    /** 承租人id */
    customerId?: string;
    /** 承租人名称 */
    customerName?: string;
    /** 账单类型: 1-保证金,2-租金,3-其他费用 */
    costType?: CostType;
    /** 账单期数 */
    period?: number;
    /** 收款用途id */
    subjectId?: string;
    /** 收款用途名称 */
    subjectName?: string;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 应收日期 */
    receivableDate?: string;
    /** 账单状态: 0-待收、1-待付、2-已收、3-已付 */
    status?: CostStatus;
    /** 是否可收/付 0-否,1-是 */
    canPay?: boolean;
    /** 账单总额 */
    totalAmount?: number;
    /** 优惠金额 */
    discountAmount?: number;
    /** 实际应收金额 */
    actualReceivable?: number;
    /** 已收金额 */
    receivedAmount?: number;
    /** 结转金额 */
    carryoverAmount?: number;
    /** 确认状态: 0-待确认, 1-已确认 */
    confirmStatus?: ConfirmStatus;
    /** 创建人姓名 */
    createByName?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房 */
    contractType?: ContractType;
    /** 租赁资源 */
    roomName?: string;
    /** 合同一级状态 */
    contractStatus?: number;
    /** 合同二级状态 */
    contractStatusTwo?: number;
}

/**
 * 账单流水关系VO
 */
export interface CostFlowRelVo {
    /** 主键ID */
    id?: string;
    /** 账单id */
    costId?: string;
    /** 流水id */
    flowId?: string;
    /** 流水单号 */
    flowNo?: string;
    /** 账单类型：1-收款 2-转入 3-转出 4-退款 */
    type?: FlowType;
    /** 确认状态: 0-未确认、1-自动确认、2-手动确认 */
    confirmStatus?: FlowConfirmStatus;
    /** 确认时间 */
    confirmTime?: string;
    /** 确认人id */
    confirmUserId?: string;
    /** 确认人姓名 */
    confirmUserName?: string;
    /** 支付金额 */
    payAmount?: number;
    /** 本次记账金额 */
    acctAmount?: number;
    /** 创建人姓名 */
    createByName?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 入账时间 */
    entryTime?: string;
    /** 支付类型 */
    payType?: number;
    /** 支付方式 */
    payMethod?: number;
    /** 订单号 */
    orderNo?: string;
    /** 已使用金额 */
    usedAmount?: number;
    /** 支付人姓名 */
    payerName?: string;
    /** 支付对象 */
    target?: string;
    /** 收款商户 */
    merchant?: string;
}

/**
 * 账单流水记录VO
 */
export interface CostFlowLogVo {
    /** 主键ID */
    id?: string;
    /** 账单id */
    costId?: string;
    /** 流水id */
    flowId?: string;
    /** 流水单号 */
    flowNo?: string;
    /** 类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转 */
    type?: number;
    /** 结转单id */
    carryoverId?: string;
    /** 结转单号 */
    carryoverNo?: string;
    /** 记账金额 */
    amount?: number;
    /** 创建人姓名 */
    createByName?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 账单详情VO
 */
export interface CostDetailVo {
    /** 账单信息 */
    cost?: CostVo;
    /** 账单流水关系列表 */
    flowRelList?: CostFlowRelVo[];
    /** 账单流水记录列表 */
    flowLogList?: CostFlowLogVo[];
}

/**
 * 通用响应结果
 */
export interface AjaxResult {
    /** 是否有错误 */
    error?: boolean;
    /** 是否成功 */
    success?: boolean;
    /** 是否有警告 */
    warn?: boolean;
    /** 是否为空 */
    empty?: boolean;
    /** 附加属性 */
    [key: string]: any;
}

/**
 * 表格数据信息
 */
export interface TableDataInfo {
    /** 总数 */
    total?: number;
    /** 行数据 */
    rows?: any[];
    /** 状态码 */
    code?: number;
    /** 消息 */
    msg?: string;
}

/**
 * 修改账单
 * @param params 账单信息
 * @returns
 */
export function updateCost(params: CostAddDTO) {
    return http.put<AjaxResult>('/business-rent-admin/cost', params);
}

/**
 * 新增账单
 * @param params 账单信息
 * @returns
 */
export function addCost(params: CostAddDTO) {
    return http.post<AjaxResult>('/business-rent-admin/cost', params);
}

/**
 * 账单统计
 * @param params 查询参数
 * @returns
 */
export function getCostSummary(params: CostQueryDTO) {
    return http.post<CostSummaryVo>('/business-rent-admin/cost/summary', params);
}

/**
 * 保存记账
 * @param params 保存记账参数
 * @returns
 */
export function saveCostRecord(params: CostSaveRecordDTO) {
    return http.post<boolean>('/business-rent-admin/cost/saveRecord', params);
}

/**
 * 查询账单列表（POST）
 * @param params 查询参数
 * @returns
 */
export function getCostList(params: CostQueryDTO) {
    return http.post<TableDataInfo>('/business-rent-admin/cost/list', params);
}

/**
 * 查询账单列表（GET）
 * @param params 查询参数
 * @returns
 */
export function getCostListByGet(params: CostQueryDTO) {
    return http.get<TableDataInfo>('/business-rent-admin/cost/list', params);
}

/**
 * 导出账单列表
 * @param params 查询参数
 * @returns
 */
export function exportCostList(params: CostQueryDTO) {
    return http.download('/business-rent-admin/cost/export', params);
}

/**
 * 确认记账
 * @param params 确认记账参数
 * @returns
 */
export function confirmCostRecord(params: CostConfirmRecordDTO) {
    return http.post<boolean>('/business-rent-admin/cost/confirmRecord', params);
}

/**
 * 取消记账
 * @param flowRelId 账单流水关联id
 * @returns
 */
export function cancelCostRecord(flowRelId: string) {
    return http.post<boolean>(`/business-rent-admin/cost/cancelRecord?flowRelId=${flowRelId}`, {});
}

/**
 * 查看账单码
 * @param costId 账单ID
 * @returns
 */
export function getCostQrcode(costId: string) {
    return http.get<string>('/business-rent-admin/cost/qrcode', { id: costId });
}

/**
 * 查看账单流水详情
 * @param costId 账单ID
 * @returns
 */
export function getCostDetail(costId: string) {
    return http.get<CostDetailVo>('/business-rent-admin/cost/detail', { id: costId });
}

/**
 * 获取账单详细信息
 * @param costId 账单ID
 * @returns
 */
export function getCostInfo(costId: string) {
    return http.get<AjaxResult>('/business-rent-admin/cost/getInfo', { id: costId });
}

/**
 * 删除账单
 * @param costIds 账单ID列表
 * @returns
 */
export function deleteCost(costIds: string[]) {
    return http.delete<AjaxResult>('/business-rent-admin/cost/delete', {
        ids: costIds,
    });
}
