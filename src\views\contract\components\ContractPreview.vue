<template>
    <div class="contract-preview-container">
        <div v-if="loading" class="loading-container">
            <a-spin tip="合同预览加载中..." />
        </div>
        <div v-show="!loading" ref="viewerRef" class="viewer-content"></div>

        <!-- 工具栏 - 只包含下载和打印按钮，靠右展示 -->
        <div class="toolbar">
            <a-space>
                <a-button type="primary" @click="handleDownload">
                    <template #icon>
                        <icon-download />
                    </template>
                    下载合同
                </a-button>
                <a-button @click="handlePrint">
                    <template #icon>
                        <icon-printer />
                    </template>
                    打印合同
                </a-button>
            </a-space>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconDownload, IconPrinter } from '@arco-design/web-vue/es/icon'
import { renderAsync } from 'docx-preview'

const props = defineProps<{
    fileUrl: string
    title?: string
}>()

// 移除 back 和 submit 事件，这些将在父组件处理
// const emit = defineEmits<{
//     (e: 'back'): void
//     (e: 'submit'): void
// }>()

const loading = ref(true)
const viewerRef = ref<HTMLElement>()

// 处理文件 URL
const getProxyUrl = (url: string) => {
    try {
        const urlObj = new URL(url)
        if (urlObj.hostname === '************') {
            // 提取路径中的 /statics 部分
            const path = urlObj.pathname
            if (path.startsWith('/statics')) {
                return path // 返回相对路径，将通过代理访问
            }
        }
    } catch (e) {
        console.warn('URL 解析失败:', e)
    }
    return url // 如果不是目标服务器或解析失败，返回原始 URL
}

// 监听fileUrl变化
watch(() => props.fileUrl, (newVal) => {
    if (newVal) {
        loadDocument()
    }
})

// 加载文档
const loadDocument = async () => {
    if (!viewerRef.value || !props.fileUrl) return

    loading.value = true
    try {
        // 清空容器
        if (viewerRef.value) {
            viewerRef.value.innerHTML = ''
        }

        // 获取文档内容
        const proxyUrl = getProxyUrl(props.fileUrl)
        const response = await fetch(proxyUrl)
        const blob = await response.blob()

        // 渲染文档
        await renderAsync(blob, viewerRef.value, viewerRef.value, {
            className: 'docx-viewer',
            inWrapper: true,
            useBase64URL: true,
        })

        loading.value = false
    } catch (error) {
        console.error('合同预览加载失败:', error)
        Message.error('合同预览加载失败')
        loading.value = false
    }
}

// 下载合同
const handleDownload = () => {
    if (!props.fileUrl) {
        Message.warning('合同文件地址不存在')
        return
    }

    // 使用代理 URL 下载
    const proxyUrl = getProxyUrl(props.fileUrl)
    const link = document.createElement('a')
    link.href = proxyUrl
    link.download = props.title || '合同文档.docx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
}

// 打印合同
const handlePrint = () => {
    if (!viewerRef.value) return

    const printContent = viewerRef.value.innerHTML
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
        Message.warning('请允许打开新窗口')
        return
    }

    // 注入打印样式和内容
    const printHtml = `
        <!DOCTYPE html>
        <html>
            <head>
                <title>合同打印预览</title>
                <style>
                    body { margin: 0; padding: 20px; }
                    .docx-viewer { max-width: 100%; margin: 0 auto; }
                </style>
            </head>
            <body>
                ${printContent}
                <script>
                    window.onload = function() {
                        window.print();
                        window.close();
                    }
                <\/script>
            </body>
        </html>
    `
    printWindow.document.write(printHtml)
    printWindow.document.close()
}

// 组件挂载时加载文档
onMounted(() => {
    if (props.fileUrl) {
        loadDocument()
    }
})

defineExpose({
    reload: loadDocument
})
</script>

<style scoped lang="less">
.contract-preview-container {
    position: relative;
    height: calc(100vh - 200px);
    min-height: 600px;
    background: #f5f5f5;

    .loading-container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .viewer-content {
        height: calc(100% - 60px); // 减去工具栏高度
        overflow: auto;
        padding: 20px;

        :deep(.docx-viewer) {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 40px;
            border-radius: 8px;
        }
    }

    .toolbar {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 60px;
        padding: 12px 20px;
        background: #fff;
        border-top: 1px solid var(--color-neutral-3);
        display: flex;
        justify-content: flex-end; // 直接右对齐
        align-items: center;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

        .toolbar-right {
            display: flex;
            align-items: center;
        }
    }
}
</style>