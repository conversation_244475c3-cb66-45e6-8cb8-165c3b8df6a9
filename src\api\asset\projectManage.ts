import http from '../index';

export interface ProjectVo {
    id: string;
    type: number;
    mdmProjectId: string;
    mdmName: string;
    mdmSaleName: string;
    code: string;
    name: string;
    provinceCode: string;
    provinceName: string;
    cityCode: string;
    cityName: string;
    countryCode: string;
    countryName: string;
    mdmTypeName: string;
    assetType: number;
    propertyUnit: string;
    projectAddress: string;
    longitude: string;
    latitude: string;
    totalSelfArea: string;
    createByName: string;
    updateByName: string;
    isDel: boolean;
}

// 查询项目选择列表
export function getProjectSelectList(params?: any) {
    return http.get<ProjectVo[]>('/business-asset/project/selectList', params);
}

// 查询项目列表
export function getProjectList(params: any) {
    return http.get('/business-asset/project/list', {
        // params: {
            mdmName: params.projectName || '',
            assetType: params.assetType || '',
            pageNum: params.pageNum || 1,
            pageSize: params.pageSize || 10
        // }
    });
}

// 查询项目详细信息
export function getProject(id: string) {
    return http.get(`/business-asset/project/${id}`);
}

// 获取项目详情信息（包含分期和地块信息）
export function getProjectDetail(id: string) {
    return http.get('/business-asset/project/basic/detail?id=' + id);
}

// 新增项目
export function addProject(data: any) {
    return http.post('/business-asset/project/basic/save', data);
}

// 删除项目
export function deleteProject(id: string) {
    return http.delete(`/business-asset/project/delete?id=${id}`);
}

// 导出项目
export function exportProject(params: any) {
    return http.post('/business-asset/project/export', params, {
        responseType: 'blob',
    });
}

// 查询楼栋树形结构
export function getBuildingTree(projectId: string, buildingName?: string) {
    return http.get('/business-asset/project/building/tree', {
        params: {
            项目ID: projectId,
            楼栋名称: buildingName,
        },
    });
}

// 查询楼栋列表
export function getBuildingList(params: any) {
    return http.get('/business-asset/project/building/list', params);
}

// 保存楼栋信息
export function saveBuilding(data: any) {
    return http.post('/business-asset/project/building/save', data);
}

// 删除楼栋
export function deleteBuilding(buildingId: string) {
    return http.delete(
        '/business-asset/project/building/delete?id=' + buildingId
    );
}

// 查询房间树形结构
export function getRoomTree(data: any) {
    return http.post('/business-asset/project/room/tree', data);
}

// 查询房间列表
export function getRoomList(params: any) {
    return http.get('/business-asset/project/room/list', params);
}

// 保存房间信息
export function saveRoom(data: any) {
    return http.post('/business-asset/project/room/save', data);
}

// 删除房间
export function deleteRoom(ids: string) {
    return http.delete('/business-asset/project/room/delete?ids=' + ids);
}

// 下载房间导入模板
export function downloadRoomTemplate() {
    return http.download('/business-asset/project/room/template/download');
}

// 查询地块下拉列表
export function getParcelList(projectId: any) {
    return http.get(
        '/business-asset/project/parcel/list?projectId=' + projectId
    );
}

// 查询楼栋下拉列表
export function getBuildingDropdownList(parcelId: any) {
    return http.get(
        '/business-asset/project/building/listByParcel?parcelId=' + parcelId
    );
}

// 查询楼层下拉列表
export function getFloorDropdownList(buildingId: any) {
    return http.get(
        '/business-asset/project/floor/list?buildingId=' + buildingId
    );
}

// 根据房间id查询房间详情
export function getProjectRoomDetail(roomId: any) {
    return http.get('/business-asset/project/room/detail?id=' + roomId);
}
