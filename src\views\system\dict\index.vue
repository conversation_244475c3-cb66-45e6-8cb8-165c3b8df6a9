<template>
    <div class="common-page">
        <!-- 搜索区域 -->
        <div class="common-search">
            <a-form :model="searchForm" layout="inline">
                <a-form-item field="dictName" label="字典名称">
                    <a-input v-model="searchForm.dictName" placeholder="请输入字典名称" />
                </a-form-item>
                <a-form-item field="dictType" label="字典类型">
                    <a-input v-model="searchForm.dictType" placeholder="请输入字典类型" />
                </a-form-item>
                <a-form-item field="status" label="状态">
                    <a-select v-model="searchForm.status" placeholder="字典状态" style="width: 200px">
                        <a-option value="">全部</a-option>
                        <a-option value="0">正常</a-option>
                        <a-option value="1">停用</a-option>
                    </a-select>
                </a-form-item>
                <a-form-item field="dateRange" label="创建时间">
                    <a-range-picker v-model="searchForm.dateRange" style="width: 240px" />
                </a-form-item>
                <a-form-item>
                    <a-space>
                        <a-button type="primary" @click="handleSearch">
                            <template #icon><icon-search /></template>
                            搜索
                        </a-button>
                        <a-button @click="handleReset">
                            <template #icon><icon-refresh /></template>
                            重置
                        </a-button>
                    </a-space>
                </a-form-item>
            </a-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="common-operation">
            <a-space>
                <a-button type="primary" @click="handleAdd">
                    <template #icon><icon-plus /></template>
                    新增
                </a-button>
                <a-button type="primary" status="warning" @click="handleModify"
                    :disabled="!selectedKeys.length || selectedKeys.length > 1">
                    <template #icon><icon-edit /></template>
                    修改
                </a-button>
                <a-button type="primary" status="danger" @click="handleDelete" :disabled="!selectedKeys.length">
                    <template #icon><icon-delete /></template>
                    删除
                </a-button>
                <a-button type="primary" status="success" @click="handleExport">
                    <template #icon><icon-download /></template>
                    导出
                </a-button>
                <a-button type="primary" status="warning" @click="handleRefresh">
                    <template #icon><icon-sync /></template>
                    刷新缓存
                </a-button>
            </a-space>
        </div>

        <!-- 表格区域 -->
        <a-table :columns="columns" :data="tableData" :row-selection="rowSelection" :pagination="pagination"
            :loading="loading" @page-change="onPageChange" @page-size-change="onPageSizeChange"
            @selection-change="onSelectionChange" row-key="dictId" :scroll="{x: 1}">
            <template #status="{ record }">
                <a-tag :color="record.status === '0' ? 'green' : 'red'">
                    {{ record.status === '1' ? '停用' : '正常' }}
                </a-tag>
            </template>
            <template #dictType="{ record }">
                <a-link @click="handleDictData(record)">{{ record.dictType }}</a-link>
            </template>
            <template #operations="{ record }">
                <a-space>
                    <a-button type="text" size="small" @click="handleEdit(record)">修改</a-button>
                    <a-button type="text" size="small" @click="handleDelete(record)">删除</a-button>
                </a-space>
            </template>
        </a-table>

        <!-- 新增/修改弹窗 -->
        <a-modal v-model:visible="dialogVisible" :title="dialogTitle" @ok="handleDialogConfirm"
            @cancel="handleDialogCancel">
            <a-form :model="formData" :rules="rules" ref="formRef">
                <a-form-item field="dictName" label="字典名称">
                    <a-input v-model="formData.dictName" placeholder="请输入字典名称" />
                </a-form-item>
                <a-form-item field="dictType" label="字典类型">
                    <a-input v-model="formData.dictType" placeholder="请输入字典类型" />
                </a-form-item>
                <a-form-item field="status" label="状态">
                    <a-radio-group v-model="formData.status">
                        <a-radio value="0">正常</a-radio>
                        <a-radio value="1">停用</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item field="remark" label="备注">
                    <a-textarea v-model="formData.remark" placeholder="请输入备注" />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import type { TableColumnData } from '@arco-design/web-vue'
// import { listType, DictType, getType, delType, addType, updateType, refreshCache } from "@/api/dict";
import { useRouter, useRoute } from 'vue-router';
import useDictStore from '@/store/modules/dict';
import { exportExcel } from '@/utils/exportUtil';
import { exportDict,listType, DictType, getType, delType, addType, updateType, refreshCache } from '@/api/system/dict'

const router = useRouter();

// 跳转到字典数据页面
// 修改路由跳转路径
const handleDictData = (record: DictType) => {
    const { dictType } = record;
    router.push({
        name: 'DictDataIndex',  // 修正路由路径
        query: { dictType }
    });
}

// 表格列定义
const columns: TableColumnData[] = [
    { type: 'selection', width: 40, fixed: 'left', multiple: true }, // 设置为多选
    { title: '字典编号', dataIndex: 'dictId', width: 100 },
    { title: '字典名称', dataIndex: 'dictName', width: 150 },
    {
        title: '字典类型',
        dataIndex: 'dictType',
        slotName: 'dictType',
        width: 150
    },
    { title: '状态', dataIndex: 'status', slotName: 'status', width: 100 },
    { title: '备注', dataIndex: 'remark', width: 120 },
    { title: '创建时间', dataIndex: 'createTime', width: 180 },
    {
        title: '操作',
        slotName: 'operations',
        width: 120,
        fixed: 'right'
    }
]

const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
});

// 搜索表单
const searchForm = reactive({
    dictName: '',
    dictType: '',
    status: '',
    dateRange: [],
    pageNum: 1,
    pageSize: 10
})

// 表格数据
const tableData = ref<DictType[]>([])
const loading = ref(false)
const selectedKeys = ref<string[]>([])
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10
})

// 弹窗相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增字典')
const formRef = ref()
const formData = reactive({
    dictId: undefined,
    dictName: '',
    dictType: '',
    status: '0',
    remark: ''
})

// 表单校验规则
const rules = {
    dictName: [{ required: true, message: '请输入字典名称' }],
    dictType: [{ required: true, message: '请输入字典类型' }]
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    const params = {
        pageNum: searchForm.pageNum,
        pageSize: searchForm.pageSize,
        dictName: searchForm.dictName,
        dictType: searchForm.dictType,
        status: searchForm.status,
    }

    // 处理时间范围
    if (searchForm.dateRange?.length === 2) {
        // 复制原有params并创建新的参数
        const newParams = {
            beginTime: searchForm.dateRange[0],
            endTime: searchForm.dateRange[1]
        }
        Object.assign(params, newParams)
    }
    const { rows, total, code } = await listType(params)
    if (code === 200) {
        tableData.value = rows as DictType[]
        pagination.total = total
    }
    loading.value = false
}

// 搜索方法
const handleSearch = () => {
    fetchData()
}

// 重置搜索
const handleReset = () => {
    searchForm.dictName = ''
    searchForm.dictType = ''
    searchForm.status = ''
    searchForm.dateRange = []
    fetchData()
}

// 新增字典
const handleAdd = () => {
    dialogTitle.value = '新增字典'
    dialogVisible.value = true
    formData.dictName = ''
    formData.dictType = ''
    formData.status = '1'
    formData.remark = ''
}

// 修改字典
const handleModify = async () => {
    if (!selectedKeys.value.length) {
        Message.warning('请选择要修改的数据')
        return
    }
    const record = tableData.value.find(item => item.dictId === Number(selectedKeys.value[0]))
    if (record) {
        dialogTitle.value = '修改字典'
        dialogVisible.value = true
        Object.assign(formData, record)
    }
}

// 删除字典
const handleDelete = async (record?: any) => {
    console.log(record)
    console.log(selectedKeys.value)
    let ids: string[] = []
    if (selectedKeys.value.length > 0) {
        ids = selectedKeys.value
    } else {
        ids = [record.dictId]
    }
    if (!ids.length) {
        Message.warning('请选择要删除的数据')
        return
    }

    const handleConfirm = async () => {
        try {
            const res = await delType(ids)
            if (res.code === 200) {
                Message.success('删除成功')
                fetchData()
            } else {
                Message.error(res.msg || '删除失败')
            }
        } catch (error) {
            console.error('删除失败', error)
            Message.error('删除失败')
        }
    }

    Modal.confirm({
        title: '系统提示',
        content: `是否确认删除字典编号为"${ids.join(',')}"的数据项？`,
        onOk: handleConfirm
    })
}
// 修改字典
const handleEdit = (record: any) => {
    dialogTitle.value = '修改字典'
    dialogVisible.value = true
    Object.assign(formData, record)

}

// 导出
const handleExport = async () => {
    const params = {
        pageNum: searchForm.pageNum,
        pageSize: searchForm.pageSize,
        dictName: searchForm.dictName,
        dictType: searchForm.dictType,
        status: searchForm.status,
    }

    // 处理时间范围
    if (searchForm.dateRange?.length === 2) {
        // 复制原有params并创建新的参数
        const newParams = {
            beginTime: searchForm.dateRange[0],
            endTime: searchForm.dateRange[1]
        }
        Object.assign(params, newParams)
    }
    exportExcel(exportDict, params,`dict_${new Date().getTime()}`)
}

// 刷新缓存
const handleRefresh = async () => {
    try {
        const res = await refreshCache()
        if (res.code === 200) {
            Message.success('刷新缓存成功')
            const dictStore = useDictStore()
            dictStore.cleanDict()
        } else {
            Message.error(res.msg || '刷新缓存失败')
        }
    } catch (error) {
        console.error('刷新缓存失败', error)
        Message.error('刷新缓存失败')
    }
}

// 弹窗确认
const handleDialogConfirm = async () => {
    try {
        await formRef.value?.validate()

        const res = formData.dictId
            ? await updateType(formData)  // 修改操作
            : await addType(formData)     // 新增操作
        if (res.code === 200) {
            Message.success(formData.dictId ? '修改成功' : '新增成功')
            dialogVisible.value = false
            fetchData()
        } else {
            Message.error(res.msg || (formData.dictId ? '修改失败' : '新增失败'))
        }
    } catch (error) {
        console.error('操作失败', error)
        Message.error('操作失败，请检查表单数据')
    }
}

// 弹窗取消
const handleDialogCancel = () => {
    dialogVisible.value = false
}

// 分页相关方法
const onPageChange = (current: number) => {
    pagination.current = current
    searchForm.pageNum = current
    fetchData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    fetchData()
}

const onSelectionChange = (rowKeys: string[]) => {
    selectedKeys.value = rowKeys
}

// 初始化
fetchData()

</script>

<style scoped lang="less">
.common-page {
    padding: 16px;
}

.common-search {
    background-color: var(--color-bg-2);
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 16px;
}

.common-operation {
    margin-bottom: 16px;
}
</style>