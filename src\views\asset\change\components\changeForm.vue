<template>
    <a-drawer :visible="dialogVisible" title="批量资产变更" :mask-closable="true" class="common-drawer" @cancel="handleCancel">
        <template #footer>
            <a-space>
                <!-- <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" @click="handleSubmit">确认</a-button> -->
                <a-button @click="handleCancel">{{ readOnlyMode ? '关闭' : '取消' }}</a-button>
                <template v-if="!readOnlyMode">
                    <a-button type="primary" status="success" @click="handleSave">暂存</a-button>
                    <a-button type="primary" @click="handleSubmit">提交</a-button>
                </template>
            </a-space>
        </template>
        <div class="change-form">
            <!-- 项目信息 -->
            <div class="project-info">
                <a-form :model="projectForm" layout="inline">
                    <a-form-item field="project" label="项目" required>
                        <a-input v-model="projectForm.projectName" disabled></a-input>


                        <!-- {{projectForm.projectName}} -->
                        <!-- <a-select v-model="projectForm.project" placeholder="请选择项目" :disabled="true"
                            style="width: 240px;">
                            <a-option :value="projectForm.project">{{ projectForm.projectName }}</a-option>
                        </a-select> -->
                    </a-form-item>
                </a-form>
            </div>

            <!-- 变更资产清单 -->
            <div class="change-list">
                <SectionTitle title="变更资产清单" style="margin-bottom: 16px;"/>
                <div class="list-header">
                    <!-- <div class="list-title">变更资产清单</div> -->
                    <div class="list-actions">
                        <div class="search-box">
                            <a-input-search v-model="searchKeyword" placeholder="请输入楼栋/房源名称搜索" allow-clear 
                                style="width: 360px;" @search="handleSearch" @clear="handleClearSearch" />
                        </div>
                        <a-space v-if="!readOnlyMode">
                            <a-button type="primary" @click="handleAddAsset">
                                <template #icon><icon-plus /></template>
                                添加资产
                            </a-button>
                            <a-button @click="handleBatchRemove" :disabled="!hasSelected">
                                <template #icon><icon-delete /></template>
                                批量移除
                            </a-button>
                        </a-space>
                    </div>
                </div>

                <a-table :columns="columns" :data="tableData" :pagination="false" row-key="id" :bordered="{ cell: true }"
                    :row-selection="readOnlyMode ? undefined : rowSelection" @selection-change="handleSelectionChange" :scroll="{ x: 1 }">
                    <template #productType="{ record }">
                        {{ getProductTypeText(record.productType) }}
                    </template>
                    <template #operation="{ record }">
                        <a-button v-if="!readOnlyMode" type="text" @click="handleRemove(record)" size="mini">移除</a-button>
                    </template>
                </a-table>
            </div>

            <!-- 变更信息 -->
            <div class="change-info">
                <SectionTitle title="变更信息" style="margin-bottom: 16px;"/>
                <a-form ref="formRef" :model="formData" :rules="formRules" :label-col-props="{ span: 6 }"
                    :wrapper-col-props="{ span: 18 }" auto-label-width>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item field="changeDate" label="变更日期" required>
                                <a-date-picker v-model="formData.changeDate" style="width: 240px;"
                                    :disabled="readOnlyMode" />
                            </a-form-item>
                        </a-col>

                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="16">
                            <a-form-item field="changeDescription" label="变更原因">
                                <a-textarea v-model="formData.changeDescription" placeholder="请输入变更原因" 
                                    :disabled="readOnlyMode" :max-length="100" allow-clear show-word-limit/>
                            </a-form-item>
                        </a-col>
                        <a-col :span="16">
                            <a-form-item field="remark" label="备注">
                                <a-textarea v-model="formData.remark" placeholder="请输入备注" :disabled="readOnlyMode" :max-length="500" allow-clear show-word-limit/>
                            </a-form-item>
                        </a-col>
                        <a-col :span="16">
                            <a-form-item field="attachment" label="附件">
                                <upload-file v-model="formData.attachment" :disabled="readOnlyMode" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </div>



        <!-- <div class="modal-footer">
            <a-space>
                <a-button @click="handleCancel">{{ readOnlyMode ? '关闭' : '取消' }}</a-button>
                <template v-if="!readOnlyMode">
                    <a-button type="primary" @click="handleSubmit">提交</a-button>
                    <a-button @click="handleSave">暂存</a-button>
                </template>
            </a-space>
        </div> -->
    </a-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconPlus, IconDelete } from '@arco-design/web-vue/es/icon'
import { 
    addAssetChange, 
    updateAssetChange, 
    getChangeDetail,
    getChangeRoomList,
    type AssetChangeAddDTO,
    type AssetChangeRoomVo
} from '@/api/asset/projectChange'
import uploadFile from '@/components/upload/uploadFile.vue'
import SectionTitle from '@/components/sectionTitle/index.vue'
import { useDict } from '@/utils/dict'

interface Props {
    visible: boolean
    projectId: string
    projectName?: string
    selectedRooms?: string[]
    selectedRoomData?: any[]
    mode?: 'add' | 'edit' | 'detail'
    formData?: any
}

interface Emits {
    (e: 'update:visible', visible: boolean): void
    (e: 'submit', formData: any): void
    (e: 'cancel'): void
    (e: 'add-asset'): void
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    projectName: '',
    selectedRooms: () => [],
    selectedRoomData: () => [],
    mode: 'add',
    formData: null
})

const emit = defineEmits<Emits>()

// 字典支持
const { product_type } = useDict('product_type')

// 产品类型转换
const getProductTypeText = (value: string | number) => {
    if (!value) return '-'
    const item = product_type.value.find((item: any) => item.value === String(value))
    return item ? item.label : value
}

const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

// 只读模式 - 支持动态设置
const readOnlyMode = ref(false)

const isEdit = ref(false); // 用响应式变量接收 props 的值

// 监听mode变化，更新只读状态
watch(() => props.mode, (newMode) => {
    readOnlyMode.value = newMode === 'detail'
}, { immediate: true })

// 监听项目ID和名称变化
watch(() => props.projectId, (newVal) => {
    if (newVal) {
        projectForm.project = newVal
    }
})

watch(() => props.projectName, (newVal) => {
    if (newVal) {
        projectForm.projectName = newVal
    }
})

// 监听选中的房源ID变化（兼容编辑模式）
watch(() => props.selectedRooms, (newRooms) => {
    // 只有在没有传入完整房源数据时才调用接口
    if (newRooms && newRooms.length > 0 && (!props.selectedRoomData || props.selectedRoomData.length === 0)) {
        loadSelectedRooms(newRooms)
    }
}, { immediate: true })

// 监听选中的房源数据变化（优先使用完整数据）
watch(() => props.selectedRoomData, (newRoomData) => {
    if (newRoomData && newRoomData.length > 0) {
        // 如果当前表格已有数据，说明是在添加更多资产，需要合并
        if (tableData.value.length > 0) {
            // 合并新数据，避免重复
            const existingIds = new Set(tableData.value.map(item => item.id))
            const newRooms = newRoomData.filter((room: any) => !existingIds.has(room.id))
            
            if (newRooms.length > 0) {
                const startIndex = tableData.value.length
                const roomsWithIndex = newRooms.map((room: any, index: number) => ({
                    ...room,
                    index: startIndex + index + 1
                }))
                
                tableData.value = [...tableData.value, ...roomsWithIndex]
                originalTableData.value = [...originalTableData.value, ...roomsWithIndex]
                updateRoomIds()
            }
        } else {
            // 首次加载数据
            loadRoomDataDirectly(newRoomData)
        }
    }
}, { immediate: true, deep: true })

// 监听表单数据变化（编辑模式）
watch(() => props.formData, (newData) => {
    if (newData && props.mode === 'edit') {
        loadFormData(newData)
    }
}, { immediate: true })

// 项目表单数据
const projectForm = reactive({
    project: props.projectId || '',
    projectName: props.projectName || '未知项目'
})

// 表单校验规则
const formRef = ref()
const formRules = {
    changeDate: [
        { required: true, message: '请选择变更日期', trigger: ['change', 'blur'] }
    ]
}

// 表格列定义
const columns = [
    { title: '序号', dataIndex: 'index', width: 70 },
    { title: '房源名称', dataIndex: 'roomName', width: 120, ellipsis: true,tooltip: true, align: 'center' },
    { title: '所属地块', dataIndex: 'parcelName', width: 160, ellipsis: true,tooltip: true, align: 'center' },
    { title: '楼栋', dataIndex: 'buildingName', width: 120, ellipsis: true,tooltip: true, align: 'center' },
    { title: '产品类型', dataIndex: 'productType', width: 100, ellipsis: true,tooltip: true, align: 'center', slotName: 'productType' },
    { title: '原面积类型', dataIndex: 'mdmAreaTypeName', width: 140, ellipsis: true,tooltip: true, align: 'center' },
    { title: '原建筑面积', dataIndex: 'mdmBuildingArea', width: 140, ellipsis: true,tooltip: true, align: 'center' },
    { title: '原套内面积', dataIndex: 'mdmInsideArea', width: 140, ellipsis: true,tooltip: true, align: 'center' },
    { title: '调整后面积类型', dataIndex: 'areaTypeName', width: 140, ellipsis: true,tooltip: true, align: 'center' },
    { title: '调整后建筑面积', dataIndex: 'buildArea', width: 140, ellipsis: true,tooltip: true, align: 'center' },
    { title: '调整后套内面积', dataIndex: 'innerArea', width: 140, ellipsis: true,tooltip: true, align: 'center' },
    { title: '建筑面积差', dataIndex: 'buildingAreaDiff', width: 140, ellipsis: true,tooltip: true, align: 'center' },
    { title: '操作', slotName: 'operation', width: 100, fixed: 'right', align: 'center' }
]

// 表格数据
const tableData = ref<AssetChangeRoomVo[]>([])
const originalTableData = ref<AssetChangeRoomVo[]>([])
const searchKeyword = ref('')

// 选择行配置
const selectedKeys = ref<string[]>([])
const rowSelection = {
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
}

// 多选框选中数据
const handleSelectionChange = (rowKeys: string[]) => {
    selectedKeys.value = rowKeys
}

// 是否有选中行
const hasSelected = computed(() => {
    return selectedKeys.value && selectedKeys.value.length > 0
})

// 表单数据
const formData = reactive<AssetChangeAddDTO>({
    projectId: props.projectId,
    changeType: '1',
    changeDate: '',
    changeDescription: '',
    remark: '',
    attachment: '',
    roomIds: []
})

// 搜索功能
const handleSearch = (value: string) => {
    searchKeyword.value = value
    if (!value.trim()) {
        tableData.value = [...originalTableData.value]
    } else {
        const keyword = value.trim().toLowerCase()
        tableData.value = originalTableData.value.filter(item => {
            const buildingMatch = item.buildingName && item.buildingName.toLowerCase().includes(keyword)
            const roomMatch = item.roomName && item.roomName.toLowerCase().includes(keyword)
            return buildingMatch || roomMatch
        })
    }
    selectedKeys.value = []
}

// 清空搜索
const handleClearSearch = () => {
    searchKeyword.value = ''
    tableData.value = [...originalTableData.value]
    selectedKeys.value = []
}

// 添加资产
const handleAddAsset = () => {
    // 触发添加资产事件，让父组件处理选择房源逻辑
    emit('add-asset')
}

// 移除单个资产
const handleRemove = (record: AssetChangeRoomVo) => {
    const index = tableData.value.findIndex(item => item.id === record.id)
    if (index !== -1) {
        tableData.value.splice(index, 1)
        originalTableData.value.splice(originalTableData.value.findIndex(item => item.id === record.id), 1)
        // 更新序号
        updateTableIndex()
        // 更新房间ID列表
        updateRoomIds()
    }
}

// 批量移除
const handleBatchRemove = () => {
    if (selectedKeys.value.length === 0) return
    
    // 移除选中的行
    tableData.value = tableData.value.filter(item => !selectedKeys.value.includes(item.id!))
    originalTableData.value = originalTableData.value.filter(item => !selectedKeys.value.includes(item.id!))
    
    // 更新序号
    updateTableIndex()
    // 清空选中状态
    selectedKeys.value = []
    // 更新房间ID列表
    updateRoomIds()
}

// 更新表格序号
const updateTableIndex = () => {
    tableData.value.forEach((item, index) => {
        item.index = index + 1
    })
    originalTableData.value.forEach((item, index) => {
        item.index = index + 1
    })
}

// 更新房间ID列表
const updateRoomIds = () => {
    formData.roomIds = tableData.value.map(item => item.id!).filter(Boolean)
}

// 直接加载传入的房源数据
const loadRoomDataDirectly = (roomData: any[]) => {
    try {
        // 添加序号
        const rooms = roomData.map((room, index) => ({
            ...room,
            index: index + 1
        }))
        
        tableData.value = rooms
        originalTableData.value = [...rooms]
        updateRoomIds()
        
        console.log('直接加载房源数据:', rooms)
    } catch (error) {
        console.error('直接加载房源数据失败:', error)
    }
}

// 加载选中的房源（通过接口）
const loadSelectedRooms = async (roomIds: string[]) => {
    if (!roomIds || roomIds.length === 0) return
    
    try {
        // 调用接口获取房源详情
        const response = await getChangeRoomList({
            pageNum: 1,
            pageSize: 1000,
            projectId: props.projectId,
            roomIds: roomIds
        })
        
        if (response && response.rows) {
            let rooms: AssetChangeRoomVo[] = []
            if (response.rows && Array.isArray(response.rows)) {
                rooms = response.rows
            }
            
            // 添加序号
            rooms.forEach((room, index) => {
                room.index = index + 1
            })
            
            tableData.value = rooms
            originalTableData.value = [...rooms]
            updateRoomIds()
        }
    } catch (error) {
        console.error('加载房源数据失败:', error)
    }
}

// 加载表单数据（编辑模式）
const loadFormData = (data: any) => {
    Object.assign(formData, {
        id: data.id,
        projectId: data.projectId,
        changeType: data.changeType || '1',
        changeDate: data.changeDate,
        changeDescription: data.changeDescription,
        remark: data.remark,
        attachment: data.attachment,
        roomIds: data.roomIds || []
    })
    
    // 如果有房间ID，加载房源数据
    if (data.roomIds && data.roomIds.length > 0) {
        loadSelectedRooms(data.roomIds)
    }
}

// 提交
const handleSubmit = async () => {
    try {
        await formRef.value?.validate()
        
        if (tableData.value.length === 0) {
            Message.warning('请至少添加一个变更资产')
            return
        }

        const errors = await formRef.value?.validate()
        if (errors) {
            return
        }
        
        // 确保roomIds是最新的
        updateRoomIds()
        
        const submitData: AssetChangeAddDTO = {
            ...formData,
            operationType: '2', // 提交
            changeRoomCount: tableData.value.length,
            changeBuilding: getChangeBuildingNames()
        }
        if (submitData.attachment === '') {
            delete submitData.attachment
        }
        
        console.log('提交数据:', submitData)
        console.log('房间ID列表:', submitData.roomIds)
        
        if (props.mode === 'edit'||isEdit.value) {
            await updateAssetChange(submitData)
            Message.success('修改成功')
        } else {
            await addAssetChange(submitData)
            Message.success('提交成功')
        }
        
        emit('submit', submitData)
    } catch (error) {
        console.error('提交失败:', error)
        // Message.error('提交失败')
    }
}

// 暂存
const handleSave = async () => {
    try {
        if (tableData.value.length === 0) {
            Message.warning('请至少添加一个变更资产')
            return
        }

        const errors = await formRef.value?.validate()
        if (errors) {
            return
        }
        
        // 确保roomIds是最新的
        updateRoomIds()
        
        const submitData: AssetChangeAddDTO = {
            ...formData,
            operationType: '1', // 暂存
            changeRoomCount: tableData.value.length,
            changeBuilding: getChangeBuildingNames()
        }
        if (submitData.attachment === '') {
            delete submitData.attachment
        }
        
        console.log('暂存数据:', submitData)
        console.log('房间ID列表:', submitData.roomIds)
        
        if (props.mode === 'edit'||isEdit.value) {
            await updateAssetChange(submitData)
            Message.success('保存成功')
        } else {
            await addAssetChange(submitData)
            Message.success('暂存成功')
        }
        
        emit('submit', submitData)
    } catch (error) {
        // console.error('保存失败:', error)
    }
}

// 获取变更楼栋名称
const getChangeBuildingNames = () => {
    const buildingNames = [...new Set(tableData.value.map(item => item.buildingName).filter(Boolean))]
    return buildingNames.join('、')
}

// 取消
const handleCancel = () => {
    emit('cancel')
}

// 重置表单
const resetForm = () => {
    formRef.value?.resetFields()
    tableData.value = []
    originalTableData.value = []
    selectedKeys.value = []
    searchKeyword.value = ''
    isEdit.value = false // 重置编辑状态
    
    Object.assign(formData, {
        projectId: props.projectId,
        changeType: '1',
        changeDate: '',
        changeDescription: '',
        remark: '',
        attachment: '',
        roomIds: []
    })
}

// 打开详情弹窗
const openDetail = async (detailProps: any) => {
    console.log('打开详情弹窗，参数:', detailProps)

    // 先重置所有数据，避免残留上次的数据
    resetForm()

    // 设置项目信息
    if (detailProps.projectId) {
        projectForm.project = detailProps.projectId
        formData.projectId = detailProps.projectId
    }

    if (detailProps.projectName) {
        projectForm.projectName = detailProps.projectName
    }

    // 设置只读状态
    if (detailProps.readOnly !== undefined) {
        readOnlyMode.value = detailProps.readOnly
    }
    if(detailProps.isEdit){
        isEdit.value = true
    }else{
        isEdit.value = false
    }
    // 显示弹窗
    dialogVisible.value = true

    // 获取详情数据
    if (detailProps.id) {
        try {
            const response = await getChangeDetail(detailProps.id)

            if (response && response.code === 200 && response.data) {
                const detailData = response.data
                
                // 填充表单数据
                Object.assign(formData, {
                    id: detailData.changeInfo.id,
                    projectId: detailData.changeInfo.projectId,
                    changeType: detailData.changeInfo.changeType || '1',
                    changeDate: detailData.changeInfo.changeDate,
                    changeDescription: detailData.changeInfo.changeDescription,
                    remark: detailData.changeInfo.remark,
                    attachment: detailData.changeInfo.attachment,
                    roomIds: detailData.changeInfo.roomIds || []
                })

                // 加载房间数据
                if (detailData.roomList && Array.isArray(detailData.roomList)) {
                    const roomsData = detailData.roomList.map((room: any, index: number) => ({
                        ...room,
                        index: index + 1
                    }))

                    // 同时更新原始数据和显示数据
                    originalTableData.value = roomsData
                    tableData.value = [...roomsData]

                    // 确保roomIds与表格数据同步
                    updateRoomIds()

                    // 重置选中状态和搜索关键词
                    selectedKeys.value = []
                    searchKeyword.value = ''
                }

                console.log('加载资产变更详情成功')
            } else {
                Message.error('加载资产变更详情失败: ' + (response?.msg || '未知错误'))
            }
        } catch (error) {
            console.error('加载资产变更详情失败:', error)
            Message.error('加载资产变更详情失败，请稍后重试')
        }
    }
}

// 暴露方法给父组件
defineExpose({
    openDetail
})

// 监听弹窗显示状态
watch(() => dialogVisible.value, (newVisible) => {
    if (newVisible) {
        if (props.mode === 'add') {
            resetForm()
        }
    }
})
</script>

<style scoped lang="less">
.change-form {
    // padding: 16px;
}

.project-info {
    margin-bottom: 16px;
    // padding: 16px;
    // background-color: var(--color-fill-1);
    // border-radius: 4px;
}

.change-list {
    margin-bottom: 24px;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.list-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--color-text-1);
}

.list-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.change-info {
    margin-bottom: 24px;
}

.info-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--color-text-1);
    margin-bottom: 16px;
}

.modal-footer {
    text-align: center;
    padding: 16px;
    border-top: 1px solid var(--color-border-2);
}

:deep(.arco-table-th) {
    background-color: var(--color-fill-2);
}

:deep(.arco-table-td) {
    border-bottom: 1px solid var(--color-border-2);
}
</style>