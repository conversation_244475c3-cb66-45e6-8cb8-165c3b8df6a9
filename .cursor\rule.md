# 生成页面和组件代码时，请遵循以下规则：
    - 使用 Vue 3 的 Composition API 语法
    - 使用 arco design Vue 组件库
    - 使用 TypeScript 类型系统
    - 遵循 Vue 的最佳实践
    - css 使用 less 语法
    - 结束不需要添加分号
    - padding 使用 16px
    - 筛选条件超过 6 个 使用 高级搜索 参考页面 src/views/financeManage/billManage.vue
    - 标题使用组件‘import sectionTitle from '@/components/sectionTitle/index.vue'’
    - 文件和组件的命名都使用‘小驼峰’命名
    - 在页面引用组件默认都使用 ‘a-drawer’ 组件，除非指定方式
    - a-drawer的 width 需要添加 ‘px’ 单位，除非使用:width
    - 被a-drawer包裹的组件，不需要在单独添加 ‘padding: 16px;’
    - 使用 a-form 组件时，使用label-col-props 和 wrapper-col-props 代替 label-col 和 wrapper-col
    - 使用 a-steps 组件时，使用 :current 数字从 1 开始
    - 当筛选条件只有 1-2 个时， 查询和重置按钮 使用 内联方式 不需要 使用 direction="vertical"
    - 使用 a-table 组件时，根据字段中文名称推断合适的宽度，最低保证表头不换行
    - 使用 a-table 组件时，使用 ellipsis: true, tooltip: true, align: 'center'

# 生成api代码时，请遵循以下规则：
    - 使用 axios 库
    - 使用 TypeScript 类型系统
    - 遵循 Vue 的最佳实践
    - 结束不需要添加分号
    - 文件和组件的命名都使用‘小驼峰’命名
    - 以文件 ‘src/api/org.ts’ 为例，生成api代码时，请遵循以下规则

# form表单验证规则
    - const errors = await voidFormRef.value.validate() 
    - if (errors) return
    