---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/房源

<a id="opIdimportRooms"></a>

## POST 通过模版导入房源

POST /room/room/template/import

通过模版导入房源

> Body 请求参数

```json
{
  "file": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetRoomStatistics"></a>

## POST 资产构成统计接口

POST /room/statistics

统计自持面积、已接收资产、房源总面积、房源数量、接收资产构成、房源业态构成

> Body 请求参数

```json
{
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomStatisticsQueryDTO](#schemaroomstatisticsquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "selfHoldArea": 0,
  "receivedAssetArea": 0,
  "totalRoomArea": 0,
  "roomCount": 0,
  "receivedAssetComposition": [
    {
      "typeCode": "string",
      "typeName": "string",
      "buildArea": 0,
      "count": 0
    }
  ],
  "roomBizTypeComposition": [
    {
      "typeCode": "string",
      "typeName": "string",
      "buildArea": 0,
      "count": 0
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RoomStatisticsVo](#schemaroomstatisticsvo)|

<a id="opIdsplitRoom"></a>

## POST 房源拆分接口

POST /room/split

房源拆分功能，支持暂存和提交两种模式。提交时会自动生成目标房源并处理房源状态变更

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "applicationNo": "string",
  "operationType": 0,
  "areaId": "string",
  "buildingId": "string",
  "sourceRoomId": "string",
  "sourceRoomVo": {
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "propertyTypeName": "string",
    "projectId": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "roomCode": "string",
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "houseTypeName": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "targetRooms": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "roomName": "string",
      "type": 0,
      "propertyType": "string",
      "projectId": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomId": "string",
      "status": 0,
      "isLock": true,
      "isDirty": true,
      "isMaintain": true,
      "roomCode": "string",
      "remark": "string",
      "mergeSplitId": "string",
      "planEffectDate": "2019-08-24T14:15:22Z",
      "actualEffectDate": "2019-08-24T14:15:22Z",
      "projectPriceId": "string",
      "tablePrice": 0,
      "bottomPrice": 0,
      "areaType": 0,
      "buildArea": 0,
      "innerArea": 0,
      "rentAreaType": 0,
      "rentArea": 0,
      "storey": 0,
      "value": 0,
      "orientation": "string",
      "houseTypeId": "string",
      "smartWaterMeter": "string",
      "smartLock": "string",
      "assetOperationMode": "string",
      "assetOperationType": "string",
      "propertyStatus": "string",
      "paymentStatus": 0,
      "specialTag": "string",
      "latestRentPrice": 0,
      "latestRentStartDate": "2019-08-24T14:15:22Z",
      "latestRentEndDate": "2019-08-24T14:15:22Z",
      "firstRentPrice": 0,
      "firstRentStartDate": "2019-08-24T14:15:22Z",
      "firstRentEndDate": "2019-08-24T14:15:22Z",
      "operationSubject": 0,
      "rentalStartDate": "2019-08-24T14:15:22Z",
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "isSelfUse": true,
      "selfUseSubject": 0,
      "selfUsePurpose": "string",
      "djBindRoomId": "string",
      "djBindName": "string",
      "defaultProcessFlag": true,
      "isDel": true,
      "sysRoomIds": [
        "string"
      ]
    }
  ],
  "approvalStatus": 0,
  "effectType": 0,
  "effectDate": "2019-08-24T14:15:22Z",
  "attachments": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "submitType": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomSplitAddDTO](#schemaroomsplitadddto)| 否 |none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdsubmitSplit"></a>

## POST 拆分提交接口

POST /room/split/submit

根据拆分ID将草稿状态的拆分记录提交，生成目标房源并处理房源状态变更

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |拆分ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdapprovalSplit"></a>

## POST 拆分审批接口

POST /room/split/approval

根据拆分ID将草稿状态的拆分记录提交，生成目标房源并处理房源状态变更

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "approveResult": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomMergeSplitApprovalDTO](#schemaroommergesplitapprovaldto)| 否 |none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdgetSimpleDiagram"></a>

## POST 房态简图接口

POST /room/simple/diagram

根据入参查询房源信息，生成房态简图数据，包含楼层名称和房间房态信息

> Body 请求参数

```json
{
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomStatus": 0,
  "propertyType": "string",
  "diagramDate": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomDiagramQueryDTO](#schemaroomdiagramquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "floorId": "string",
    "floorName": "string",
    "rooms": [
      {
        "roomId": "string",
        "roomName": "string",
        "propertyType": "string",
        "rentArea": 0,
        "orientation": "string",
        "isSelfUse": true,
        "needCheckOut": true,
        "roomStatusName": "string",
        "roomStatus": 0,
        "tags": [
          "string"
        ],
        "houseTypeId": "string",
        "tablePrice": 0,
        "rentAreaType": 0,
        "emptyDays": 0,
        "selfUseSubject": 0,
        "rentalStartDate": "2019-08-24T14:15:22Z",
        "externalRentStartDate": "2019-08-24T14:15:22Z",
        "isDirty": true,
        "isLock": true,
        "isMaintain": true,
        "bookingVo": {
          "customerName": "string",
          "companyName": "string",
          "bookingAmount": 0,
          "bookingTime": "2019-08-24T14:15:22Z",
          "canRefund": true,
          "roomStatus": "string"
        },
        "bookings": [
          {
            "customerName": "string",
            "companyName": "string",
            "bookingAmount": 0,
            "bookingTime": "2019-08-24T14:15:22Z",
            "canRefund": true,
            "roomStatus": "string"
          }
        ],
        "contractVo": {
          "roomId": "string",
          "contractId": "string",
          "contractNo": "string",
          "contractNumber": "string",
          "contractCategory": "string",
          "contractType": 0,
          "signType": 0,
          "rentPrice": 0,
          "tenantType": "string",
          "tenantName": "string",
          "tenantIdCard": "string",
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "rentTerm": "string",
          "status": 0,
          "roomStatus": "string",
          "approveStatus": 0
        },
        "contracts": [
          {
            "roomId": "string",
            "contractId": "string",
            "contractNo": "string",
            "contractNumber": "string",
            "contractCategory": "string",
            "contractType": 0,
            "signType": 0,
            "rentPrice": 0,
            "tenantType": "string",
            "tenantName": "string",
            "tenantIdCard": "string",
            "startDate": "2019-08-24T14:15:22Z",
            "endDate": "2019-08-24T14:15:22Z",
            "rentTerm": "string",
            "status": 0,
            "roomStatus": "string",
            "approveStatus": 0
          }
        ]
      }
    ]
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[FloorDiagramVo](#schemafloordiagramvo)]|false|none||none|
|» floorId|string|false|none|楼层ID|楼层ID|
|» floorName|string|false|none|楼层名称|楼层名称|
|» rooms|[[RoomDiagramVo](#schemaroomdiagramvo)]|false|none|房间信息列表|该楼层下的房间房态信息列表|
|»» 房间信息列表|[RoomDiagramVo](#schemaroomdiagramvo)|false|none|房间信息列表|该楼层下的房间房态信息列表|
|»»» roomId|string|false|none|房间ID|房间ID|
|»»» roomName|string|false|none|房间名称|房间名称|
|»»» propertyType|string|false|none|用途|物业类型，二级字典|
|»»» rentArea|number|false|none|计租面积|计租面积|
|»»» orientation|string|false|none|朝向|朝向|
|»»» isSelfUse|boolean|false|none|是否自用|是否自用（0否 1是）|
|»»» needCheckOut|boolean|false|none|出场标识|出场标识，true表示需要出场|
|»»» roomStatusName|string|false|none|状态|房间状态|
|»»» roomStatus|integer(int32)|false|none|状态编码|房源状态(1.空置，2.在租，3.待生效/签约中/已预定， 4.不可招商)|
|»»» tags|[string]|false|none|标识数组|房源标识列表，如：已预订、签约中、待生效、未进场、即将到期、未出场|
|»»»» 标识数组|string|false|none|标识数组|房源标识列表，如：已预订、签约中、待生效、未进场、即将到期、未出场|
|»»» houseTypeId|string|false|none|户型|户型ID|
|»»» tablePrice|number|false|none|单价|表价|
|»»» rentAreaType|integer(int32)|false|none|计租单位|计租面积类型（1建筑面积 2套内面积）|
|»»» emptyDays|integer(int32)|false|none|空置天数|空置天数|
|»»» selfUseSubject|integer(int32)|false|none|自用主体|自用主体（1运营 2商服 3众创 4其他）|
|»»» rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|»»» externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|»»» isDirty|boolean|false|none|是否脏房|是否脏房（0否 1是）|
|»»» isLock|boolean|false|none|是否锁房|是否锁房（0否 1是）|
|»»» isMaintain|boolean|false|none|是否维修|是否维修（0否 1是）|
|»»» bookingVo|[BookDiagramVo](#schemabookdiagramvo)|false|none|订单信息列表|该房间的订单信息列表|
|»»»» customerName|string|false|none|客户名称|客户名称|
|»»»» companyName|string|false|none|公司名称|公司名称|
|»»»» bookingAmount|number|false|none|预定单金额|预定单金额|
|»»»» bookingTime|string(date-time)|false|none|预定时间|预定时间|
|»»»» canRefund|boolean|false|none|是否可退|是否可退（0否 1是）|
|»»»» roomStatus|string|false|none|房间状态|房间状态|
|»»» bookings|[[BookDiagramVo](#schemabookdiagramvo)]|false|none|订单信息列表|该房间的订单信息列表|
|»»»» 订单信息列表|[BookDiagramVo](#schemabookdiagramvo)|false|none|订单信息列表|该房间的订单信息列表|
|»»»»» customerName|string|false|none|客户名称|客户名称|
|»»»»» companyName|string|false|none|公司名称|公司名称|
|»»»»» bookingAmount|number|false|none|预定单金额|预定单金额|
|»»»»» bookingTime|string(date-time)|false|none|预定时间|预定时间|
|»»»»» canRefund|boolean|false|none|是否可退|是否可退（0否 1是）|
|»»»»» roomStatus|string|false|none|房间状态|房间状态|
|»»» contractVo|[ContractDiagramVo](#schemacontractdiagramvo)|false|none|合同信息列表|该房间的合同信息列表|
|»»»» roomId|string|false|none|房间id|房间id|
|»»»» contractId|string|false|none|合同id|合同id|
|»»»» contractNo|string|false|none|合同号|合同号|
|»»»» contractNumber|string|false|none|合同编号|合同编号|
|»»»» contractCategory|string|false|none|合同类别|合同类别|
|»»»» contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|»»»» signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|»»»» rentPrice|number|false|none|租金|租金|
|»»»» tenantType|string|false|none|承租类型|承租类型|
|»»»» tenantName|string|false|none|承租人|承租人|
|»»»» tenantIdCard|string|false|none|承租人证件号|承租人证件号|
|»»»» startDate|string(date-time)|false|none|合同开始时间|合同开始时间|
|»»»» endDate|string(date-time)|false|none|合同结束时间|合同结束时间|
|»»»» rentTerm|string|false|none|合同租期|合同租期|
|»»»» status|integer(int32)|false|none|合同一级状态|合同一级状态|
|»»»» roomStatus|string|false|none|房间状态|房间状态|
|»»»» approveStatus|integer(int32)|false|none|审批状态|审批状态|
|»»» contracts|[[ContractDiagramVo](#schemacontractdiagramvo)]|false|none|合同信息列表|该房间的合同信息列表|
|»»»» 合同信息列表|[ContractDiagramVo](#schemacontractdiagramvo)|false|none|合同信息列表|该房间的合同信息列表|
|»»»»» roomId|string|false|none|房间id|房间id|
|»»»»» contractId|string|false|none|合同id|合同id|
|»»»»» contractNo|string|false|none|合同号|合同号|
|»»»»» contractNumber|string|false|none|合同编号|合同编号|
|»»»»» contractCategory|string|false|none|合同类别|合同类别|
|»»»»» contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|»»»»» signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|»»»»» rentPrice|number|false|none|租金|租金|
|»»»»» tenantType|string|false|none|承租类型|承租类型|
|»»»»» tenantName|string|false|none|承租人|承租人|
|»»»»» tenantIdCard|string|false|none|承租人证件号|承租人证件号|
|»»»»» startDate|string(date-time)|false|none|合同开始时间|合同开始时间|
|»»»»» endDate|string(date-time)|false|none|合同结束时间|合同结束时间|
|»»»»» rentTerm|string|false|none|合同租期|合同租期|
|»»»»» status|integer(int32)|false|none|合同一级状态|合同一级状态|
|»»»»» roomStatus|string|false|none|房间状态|房间状态|
|»»»»» approveStatus|integer(int32)|false|none|审批状态|审批状态|

<a id="opIdgetRoomTree"></a>

## POST 查询用户有权限的房间树

POST /room/roomOptions

返回项目-地块-楼栋-房间层级树结构

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "contractType": "string",
  "buildingType": "string",
  "buildingId": "string",
  "rentStatus": "string",
  "roomName": "string",
  "projectId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomTreeQueryDTO](#schemaroomtreequerydto)| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": "string",
    "name": "string",
    "parentId": "string",
    "roomId": "string",
    "roomName": "string",
    "projectId": "string",
    "projectName": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "propertyType": "string",
    "rentStatus": "string",
    "rentAreaType": 0,
    "rentArea": 0,
    "price": 0,
    "bottomPrice": 0,
    "priceUnit": 0,
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "depositType": 0,
    "depositAmount": 0,
    "level": 0,
    "children": [
      {
        "id": "string",
        "name": "string",
        "parentId": "string",
        "roomId": "string",
        "roomName": "string",
        "projectId": "string",
        "projectName": "string",
        "parcelId": "string",
        "parcelName": "string",
        "buildingId": "string",
        "buildingName": "string",
        "propertyType": "string",
        "rentStatus": "string",
        "rentAreaType": 0,
        "rentArea": 0,
        "price": 0,
        "bottomPrice": 0,
        "priceUnit": 0,
        "externalRentStartDate": "2019-08-24T14:15:22Z",
        "depositType": 0,
        "depositAmount": 0,
        "level": 0,
        "children": [
          {
            "id": "string",
            "name": "string",
            "parentId": "string",
            "roomId": "string",
            "roomName": "string",
            "projectId": "string",
            "projectName": "string",
            "parcelId": "string",
            "parcelName": "string",
            "buildingId": "string",
            "buildingName": "string",
            "propertyType": "string",
            "rentStatus": "string",
            "rentAreaType": 0,
            "rentArea": 0,
            "price": 0,
            "bottomPrice": 0,
            "priceUnit": 0,
            "externalRentStartDate": "2019-08-24T14:15:22Z",
            "depositType": 0,
            "depositAmount": 0,
            "level": 0,
            "children": [
              null
            ]
          }
        ]
      }
    ]
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomTreeVo](#schemaroomtreevo)]|false|none||none|
|» id|string|false|none|树结构id|none|
|» name|string|false|none|树结构名称|none|
|» parentId|string|false|none|父级id|none|
|» roomId|string|false|none|房源roomId|none|
|» roomName|string|false|none|房源名称|none|
|» projectId|string|false|none|项目id|none|
|» projectName|string|false|none|项目名称|none|
|» parcelId|string|false|none|地块id|none|
|» parcelName|string|false|none|地块名称|none|
|» buildingId|string|false|none|楼栋id|none|
|» buildingName|string|false|none|楼栋名称|none|
|» propertyType|string|false|none|物业类型|none|
|» rentStatus|string|false|none|租控状态 0-可租 1-已租（字段待确认）|none|
|» rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|none|
|» rentArea|number|false|none|计租面积|none|
|» price|number|false|none|单价|none|
|» bottomPrice|number|false|none|底价|none|
|» priceUnit|integer(int32)|false|none|单价单位(1元/平方米/月 2元/月 3元/日)|none|
|» externalRentStartDate|string(date-time)|false|none|对外出租起始日期|none|
|» depositType|integer(int32)|false|none|保证金类型|none|
|» depositAmount|number|false|none|保证金金额|none|
|» level|integer(int32)|false|none| 1：项目 2：地块 3：楼栋 4：房间|none|
|» children|[[RoomTreeVo](#schemaroomtreevo)]|false|none|子节点列表|none|
|»» id|string|false|none|树结构id|none|
|»» name|string|false|none|树结构名称|none|
|»» parentId|string|false|none|父级id|none|
|»» roomId|string|false|none|房源roomId|none|
|»» roomName|string|false|none|房源名称|none|
|»» projectId|string|false|none|项目id|none|
|»» projectName|string|false|none|项目名称|none|
|»» parcelId|string|false|none|地块id|none|
|»» parcelName|string|false|none|地块名称|none|
|»» buildingId|string|false|none|楼栋id|none|
|»» buildingName|string|false|none|楼栋名称|none|
|»» propertyType|string|false|none|物业类型|none|
|»» rentStatus|string|false|none|租控状态 0-可租 1-已租（字段待确认）|none|
|»» rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|none|
|»» rentArea|number|false|none|计租面积|none|
|»» price|number|false|none|单价|none|
|»» bottomPrice|number|false|none|底价|none|
|»» priceUnit|integer(int32)|false|none|单价单位(1元/平方米/月 2元/月 3元/日)|none|
|»» externalRentStartDate|string(date-time)|false|none|对外出租起始日期|none|
|»» depositType|integer(int32)|false|none|保证金类型|none|
|»» depositAmount|number|false|none|保证金金额|none|
|»» level|integer(int32)|false|none| 1：项目 2：地块 3：楼栋 4：房间|none|
|»» children|[[RoomTreeVo](#schemaroomtreevo)]|false|none|子节点列表|none|

<a id="opIdquickEffect"></a>

## POST 立即生效接口

POST /room/quick/effect

立即生效功能，默认处理待生效房源。注意一个拆分单的房源要一起生效，并作废原房源；同时生成一条房源生效变更记录

> Body 请求参数

```json
[
  "string"
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|array[string]| 否 |none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdpendingCancel"></a>

## POST 待生效作废接口

POST /room/pending/cancel

将待生效状态的房源批量作废，查出同一批次的房源一起作废

> Body 请求参数

```json
[
  "string"
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|array[string]| 否 |none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdimportMultipleRooms"></a>

## POST 通过模版导入多经

POST /room/multiple/template/import

通过模版导入多经

> Body 请求参数

```json
{
  "file": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdmergeRoom"></a>

## POST 房源合并接口

POST /room/merge

房源合并功能，支持暂存和提交两种模式。提交时会自动生成目标房源并处理房源状态变更

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "applicationNo": "string",
  "operationType": 0,
  "areaId": "string",
  "buildingId": "string",
  "sourceRoomIds": [
    "string"
  ],
  "sourceRooms": [
    {
      "id": "string",
      "roomName": "string",
      "type": 0,
      "propertyType": "string",
      "propertyTypeName": "string",
      "projectId": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomId": "string",
      "status": 0,
      "isLock": true,
      "isDirty": true,
      "isMaintain": true,
      "roomCode": "string",
      "remark": "string",
      "mergeSplitId": "string",
      "planEffectDate": "2019-08-24T14:15:22Z",
      "actualEffectDate": "2019-08-24T14:15:22Z",
      "projectPriceId": "string",
      "tablePrice": 0,
      "bottomPrice": 0,
      "areaType": 0,
      "buildArea": 0,
      "innerArea": 0,
      "rentAreaType": 0,
      "rentArea": 0,
      "storey": 0,
      "value": 0,
      "orientation": "string",
      "houseTypeId": "string",
      "houseTypeName": "string",
      "smartWaterMeter": "string",
      "smartLock": "string",
      "assetOperationMode": "string",
      "assetOperationType": "string",
      "propertyStatus": "string",
      "paymentStatus": 0,
      "specialTag": "string",
      "latestRentPrice": 0,
      "latestRentStartDate": "2019-08-24T14:15:22Z",
      "latestRentEndDate": "2019-08-24T14:15:22Z",
      "firstRentPrice": 0,
      "firstRentStartDate": "2019-08-24T14:15:22Z",
      "firstRentEndDate": "2019-08-24T14:15:22Z",
      "operationSubject": 0,
      "rentalStartDate": "2019-08-24T14:15:22Z",
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "isSelfUse": true,
      "selfUseSubject": 0,
      "selfUsePurpose": "string",
      "djBindRoomId": "string",
      "djBindName": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "targetRoom": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "projectId": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "roomCode": "string",
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "defaultProcessFlag": true,
    "isDel": true,
    "sysRoomIds": [
      "string"
    ]
  },
  "approvalStatus": 0,
  "effectType": 0,
  "effectDate": "2019-08-24T14:15:22Z",
  "attachments": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "submitType": 0,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomMergeAddDTO](#schemaroommergeadddto)| 否 |none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdmergeSplitList"></a>

## POST 拆分合并列表接口

POST /room/mergeSplit/list

根据查询条件分页获取房源拆分合并列表数据，包含审批状态、调整类型、申请编号、地块名称、楼栋名称、原房源、调整后房源

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "applicationNo": "string",
  "operationType": 0,
  "areaId": "string",
  "buildingId": "string",
  "roomName": "string",
  "sourceRoomId": "string",
  "sourceRoomInfo": "string",
  "targetRoomId": "string",
  "targetRoomInfo": "string",
  "approvalStatus": 0,
  "effectType": 0,
  "effectDate": "2019-08-24T14:15:22Z",
  "createTimeStart": "2019-08-24T14:15:22Z",
  "createTimeEnd": "2019-08-24T14:15:22Z",
  "attachments": "string",
  "roomInfo": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomMergeSplitQueryDTO](#schemaroommergesplitquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": "string",
    "applicationNo": "string",
    "operationType": 0,
    "areaId": "string",
    "areaName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "sourceRoomId": "string",
    "sourceRoomInfo": "string",
    "targetRoomId": "string",
    "targetRoomInfo": "string",
    "approvalStatus": 0,
    "effectType": 0,
    "effectDate": "2019-08-24T14:15:22Z",
    "attachments": "string",
    "roomInfo": "string",
    "adjustmentReason": "string",
    "remark": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateByName": "string",
    "isDel": true
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomMergeSplitVo](#schemaroommergesplitvo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» applicationNo|string|false|none|申请编号|申请编号|
|» operationType|integer(int32)|false|none|操作类型（3拆分 4合并）|操作类型（3拆分 4合并）|
|» areaId|string|false|none|地块ID|地块ID|
|» areaName|string|false|none|地块名称|地块名称|
|» buildingId|string|false|none|楼栋ID|楼栋ID|
|» buildingName|string|false|none|楼栋名称|楼栋名称|
|» sourceRoomId|string|false|none|原房源id|原房源id|
|» sourceRoomInfo|string|false|none|原房源名称|原房源名称|
|» targetRoomId|string|false|none|调整后房源id|调整后房源id|
|» targetRoomInfo|string|false|none|调整后房源名称|调整后房源名称|
|» approvalStatus|integer(int32)|false|none|审批状态（0草稿 1待审批 2已审批 3驳回）|审批状态（0草稿 1待审批 2已审批 3驳回）|
|» effectType|integer(int32)|false|none|生效方式（1立即生效 2到期生效）|生效方式（1立即生效 2到期生效）|
|» effectDate|string(date-time)|false|none|生效日期|生效日期|
|» attachments|string|false|none|附件|附件|
|» roomInfo|string|false|none|房间草稿信息|房间草稿信息|
|» adjustmentReason|string|false|none|调整原因|调整原因|
|» remark|string|false|none|备注|备注|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» createTime|string(date-time)|false|none|创建时间|创建时间|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIdgetMaxContractEndDate"></a>

## POST 拆分合并合同结束日期

POST /room/mergeSplit/getMaxContractEndDate

获取房源的最大合同结束日期，用于拆分合并操作时判断生效日期

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
"2019-08-24T14:15:22Z"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

<a id="opIdsubmitMerge"></a>

## POST 合并提交接口

POST /room/merge/submit

根据合并ID将草稿状态的合并记录提交

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |合并ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdapprovalMerge"></a>

## POST 合并审批接口

POST /room/merge/approval

根据拆分ID审批合并房源，生成目标房源并处理房源状态变更

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "approveResult": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomMergeSplitApprovalDTO](#schemaroommergesplitapprovaldto)| 否 |none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdintroduceReceivedRooms"></a>

## POST 房源引入已接收资产

POST /room/introduce

创建新的房源记录，引入资产房源

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true,
  "roomCode": "string",
  "remark": "string",
  "mergeSplitId": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "projectPriceId": "string",
  "tablePrice": 0,
  "bottomPrice": 0,
  "areaType": 0,
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": 0,
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": 0,
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": true,
  "selfUseSubject": 0,
  "selfUsePurpose": "string",
  "djBindRoomId": "string",
  "djBindName": "string",
  "defaultProcessFlag": true,
  "isDel": true,
  "sysRoomIds": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomAddDTO](#schemaroomadddto)| 否 | 目标房间信息|none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdexport_2"></a>

## POST 导出房源列表

POST /room/export

将房源列表数据导出为Excel文件

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true,
  "roomCode": "string",
  "remark": "string",
  "mergeSplitId": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "projectPriceId": "string",
  "tablePrice": 0,
  "bottomPrice": 0,
  "areaType": 0,
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": 0,
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": 0,
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": true,
  "selfUseSubject": 0,
  "selfUsePurpose": "string",
  "djBindRoomId": "string",
  "djBindName": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "priceFlag": "string",
  "parcelOrBuildingName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomQueryDTO](#schemaroomquerydto)| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIdeffectRooms"></a>

## POST 房源生效接口

POST /room/effect

将草稿状态的房源批量生效为生效中状态，并生成变更记录

> Body 请求参数

```json
[
  "string"
]
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|array[string]| 否 ||none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdeffectCancel"></a>

## POST 生效中作废接口

POST /room/effect/cancel

将生效中状态的房源批量作废，校验是否存在履约合同

> Body 请求参数

```json
[
  "string"
]
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|array[string]| 否 ||none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdedit_11"></a>

## POST 修改房源

POST /room/edit

根据房源ID更新房源信息

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true,
  "roomCode": "string",
  "remark": "string",
  "mergeSplitId": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "projectPriceId": "string",
  "tablePrice": 0,
  "bottomPrice": 0,
  "areaType": 0,
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": 0,
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": 0,
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": true,
  "selfUseSubject": 0,
  "selfUsePurpose": "string",
  "djBindRoomId": "string",
  "djBindName": "string",
  "defaultProcessFlag": true,
  "isDel": true,
  "sysRoomIds": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomAddDTO](#schemaroomadddto)| 否 | 目标房间信息|none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdgetDetailDiagram"></a>

## POST 房态详图接口

POST /room/detail/diagram

根据入参查询房源信息，生成房态详图数据，包含楼层名称和房间详细房态信息，包括标识数组、空置天数、承租方等详细信息

> Body 请求参数

```json
{
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomStatus": 0,
  "propertyType": "string",
  "diagramDate": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomDiagramQueryDTO](#schemaroomdiagramquerydto)| 否 ||none|

> 返回示例

> 200 Response

```json
[
  {
    "floorId": "string",
    "floorName": "string",
    "rooms": [
      {
        "roomId": "string",
        "roomName": "string",
        "propertyType": "string",
        "rentArea": 0,
        "orientation": "string",
        "isSelfUse": true,
        "needCheckOut": true,
        "roomStatusName": "string",
        "roomStatus": 0,
        "tags": [
          "string"
        ],
        "houseTypeId": "string",
        "tablePrice": 0,
        "rentAreaType": 0,
        "emptyDays": 0,
        "selfUseSubject": 0,
        "rentalStartDate": "2019-08-24T14:15:22Z",
        "externalRentStartDate": "2019-08-24T14:15:22Z",
        "isDirty": true,
        "isLock": true,
        "isMaintain": true,
        "bookingVo": {
          "customerName": "string",
          "companyName": "string",
          "bookingAmount": 0,
          "bookingTime": "2019-08-24T14:15:22Z",
          "canRefund": true,
          "roomStatus": "string"
        },
        "bookings": [
          {
            "customerName": "string",
            "companyName": "string",
            "bookingAmount": 0,
            "bookingTime": "2019-08-24T14:15:22Z",
            "canRefund": true,
            "roomStatus": "string"
          }
        ],
        "contractVo": {
          "roomId": "string",
          "contractId": "string",
          "contractNo": "string",
          "contractNumber": "string",
          "contractCategory": "string",
          "contractType": 0,
          "signType": 0,
          "rentPrice": 0,
          "tenantType": "string",
          "tenantName": "string",
          "tenantIdCard": "string",
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "rentTerm": "string",
          "status": 0,
          "roomStatus": "string",
          "approveStatus": 0
        },
        "contracts": [
          {
            "roomId": "string",
            "contractId": "string",
            "contractNo": "string",
            "contractNumber": "string",
            "contractCategory": "string",
            "contractType": 0,
            "signType": 0,
            "rentPrice": 0,
            "tenantType": "string",
            "tenantName": "string",
            "tenantIdCard": "string",
            "startDate": "2019-08-24T14:15:22Z",
            "endDate": "2019-08-24T14:15:22Z",
            "rentTerm": "string",
            "status": 0,
            "roomStatus": "string",
            "approveStatus": 0
          }
        ]
      }
    ]
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[FloorDiagramVo](#schemafloordiagramvo)]|false|none||none|
|» floorId|string|false|none|楼层ID|楼层ID|
|» floorName|string|false|none|楼层名称|楼层名称|
|» rooms|[[RoomDiagramVo](#schemaroomdiagramvo)]|false|none|房间信息列表|该楼层下的房间房态信息列表|
|»» 房间信息列表|[RoomDiagramVo](#schemaroomdiagramvo)|false|none|房间信息列表|该楼层下的房间房态信息列表|
|»»» roomId|string|false|none|房间ID|房间ID|
|»»» roomName|string|false|none|房间名称|房间名称|
|»»» propertyType|string|false|none|用途|物业类型，二级字典|
|»»» rentArea|number|false|none|计租面积|计租面积|
|»»» orientation|string|false|none|朝向|朝向|
|»»» isSelfUse|boolean|false|none|是否自用|是否自用（0否 1是）|
|»»» needCheckOut|boolean|false|none|出场标识|出场标识，true表示需要出场|
|»»» roomStatusName|string|false|none|状态|房间状态|
|»»» roomStatus|integer(int32)|false|none|状态编码|房源状态(1.空置，2.在租，3.待生效/签约中/已预定， 4.不可招商)|
|»»» tags|[string]|false|none|标识数组|房源标识列表，如：已预订、签约中、待生效、未进场、即将到期、未出场|
|»»»» 标识数组|string|false|none|标识数组|房源标识列表，如：已预订、签约中、待生效、未进场、即将到期、未出场|
|»»» houseTypeId|string|false|none|户型|户型ID|
|»»» tablePrice|number|false|none|单价|表价|
|»»» rentAreaType|integer(int32)|false|none|计租单位|计租面积类型（1建筑面积 2套内面积）|
|»»» emptyDays|integer(int32)|false|none|空置天数|空置天数|
|»»» selfUseSubject|integer(int32)|false|none|自用主体|自用主体（1运营 2商服 3众创 4其他）|
|»»» rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|»»» externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|»»» isDirty|boolean|false|none|是否脏房|是否脏房（0否 1是）|
|»»» isLock|boolean|false|none|是否锁房|是否锁房（0否 1是）|
|»»» isMaintain|boolean|false|none|是否维修|是否维修（0否 1是）|
|»»» bookingVo|[BookDiagramVo](#schemabookdiagramvo)|false|none|订单信息列表|该房间的订单信息列表|
|»»»» customerName|string|false|none|客户名称|客户名称|
|»»»» companyName|string|false|none|公司名称|公司名称|
|»»»» bookingAmount|number|false|none|预定单金额|预定单金额|
|»»»» bookingTime|string(date-time)|false|none|预定时间|预定时间|
|»»»» canRefund|boolean|false|none|是否可退|是否可退（0否 1是）|
|»»»» roomStatus|string|false|none|房间状态|房间状态|
|»»» bookings|[[BookDiagramVo](#schemabookdiagramvo)]|false|none|订单信息列表|该房间的订单信息列表|
|»»»» 订单信息列表|[BookDiagramVo](#schemabookdiagramvo)|false|none|订单信息列表|该房间的订单信息列表|
|»»»»» customerName|string|false|none|客户名称|客户名称|
|»»»»» companyName|string|false|none|公司名称|公司名称|
|»»»»» bookingAmount|number|false|none|预定单金额|预定单金额|
|»»»»» bookingTime|string(date-time)|false|none|预定时间|预定时间|
|»»»»» canRefund|boolean|false|none|是否可退|是否可退（0否 1是）|
|»»»»» roomStatus|string|false|none|房间状态|房间状态|
|»»» contractVo|[ContractDiagramVo](#schemacontractdiagramvo)|false|none|合同信息列表|该房间的合同信息列表|
|»»»» roomId|string|false|none|房间id|房间id|
|»»»» contractId|string|false|none|合同id|合同id|
|»»»» contractNo|string|false|none|合同号|合同号|
|»»»» contractNumber|string|false|none|合同编号|合同编号|
|»»»» contractCategory|string|false|none|合同类别|合同类别|
|»»»» contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|»»»» signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|»»»» rentPrice|number|false|none|租金|租金|
|»»»» tenantType|string|false|none|承租类型|承租类型|
|»»»» tenantName|string|false|none|承租人|承租人|
|»»»» tenantIdCard|string|false|none|承租人证件号|承租人证件号|
|»»»» startDate|string(date-time)|false|none|合同开始时间|合同开始时间|
|»»»» endDate|string(date-time)|false|none|合同结束时间|合同结束时间|
|»»»» rentTerm|string|false|none|合同租期|合同租期|
|»»»» status|integer(int32)|false|none|合同一级状态|合同一级状态|
|»»»» roomStatus|string|false|none|房间状态|房间状态|
|»»»» approveStatus|integer(int32)|false|none|审批状态|审批状态|
|»»» contracts|[[ContractDiagramVo](#schemacontractdiagramvo)]|false|none|合同信息列表|该房间的合同信息列表|
|»»»» 合同信息列表|[ContractDiagramVo](#schemacontractdiagramvo)|false|none|合同信息列表|该房间的合同信息列表|
|»»»»» roomId|string|false|none|房间id|房间id|
|»»»»» contractId|string|false|none|合同id|合同id|
|»»»»» contractNo|string|false|none|合同号|合同号|
|»»»»» contractNumber|string|false|none|合同编号|合同编号|
|»»»»» contractCategory|string|false|none|合同类别|合同类别|
|»»»»» contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|»»»»» signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|»»»»» rentPrice|number|false|none|租金|租金|
|»»»»» tenantType|string|false|none|承租类型|承租类型|
|»»»»» tenantName|string|false|none|承租人|承租人|
|»»»»» tenantIdCard|string|false|none|承租人证件号|承租人证件号|
|»»»»» startDate|string(date-time)|false|none|合同开始时间|合同开始时间|
|»»»»» endDate|string(date-time)|false|none|合同结束时间|合同结束时间|
|»»»»» rentTerm|string|false|none|合同租期|合同租期|
|»»»»» status|integer(int32)|false|none|合同一级状态|合同一级状态|
|»»»»» roomStatus|string|false|none|房间状态|房间状态|
|»»»»» approveStatus|integer(int32)|false|none|审批状态|审批状态|

<a id="opIdcronEffect_1"></a>

## POST 定时生效接口

POST /room/cron/effect

查询预计生效日期是当天时间，且状态为待生效的房间数据，自动处理生效逻辑

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdbatchUpdateRooms"></a>

## POST 批量更新房源接口

POST /room/batchUpdate

批量更新房源信息，支持草稿和生效中状态的房源，生效中房源会生成变更记录

> Body 请求参数

```json
{
  "roomIds": [
    "string"
  ],
  "roomInfo": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "projectId": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "roomCode": "string",
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "defaultProcessFlag": true,
    "isDel": true,
    "sysRoomIds": [
      "string"
    ]
  }
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomBatchUpdateDTO](#schemaroombatchupdatedto)| 否 ||none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdroomDownloadTemplate"></a>

## GET 下载房源导入模版

GET /room/room/template/download

下载房源导入模版

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdadd_10"></a>

## POST 新增房源

POST /room/add

创建新的房源记录，支持外部房源新增

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true,
  "roomCode": "string",
  "remark": "string",
  "mergeSplitId": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "projectPriceId": "string",
  "tablePrice": 0,
  "bottomPrice": 0,
  "areaType": 0,
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": 0,
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": 0,
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": true,
  "selfUseSubject": 0,
  "selfUsePurpose": "string",
  "djBindRoomId": "string",
  "djBindName": "string",
  "defaultProcessFlag": true,
  "isDel": true,
  "sysRoomIds": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomAddDTO](#schemaroomadddto)| 否 | 目标房间信息|none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdroomDownloadTemplate_1"></a>

## GET 下载房源导入模版

GET /room/template/download

下载房源导入模版

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIdgetSplitDetail"></a>

## GET 拆分详情接口

GET /room/split/detail

根据拆分ID查询拆分详情，包含原房源信息和目标房源信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||拆分ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "applicationNo": "string",
  "operationType": 0,
  "areaId": "string",
  "buildingId": "string",
  "sourceRoomId": "string",
  "sourceRoomVo": {
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "propertyTypeName": "string",
    "projectId": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "roomCode": "string",
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "houseTypeName": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "targetRooms": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "roomName": "string",
      "type": 0,
      "propertyType": "string",
      "projectId": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomId": "string",
      "status": 0,
      "isLock": true,
      "isDirty": true,
      "isMaintain": true,
      "roomCode": "string",
      "remark": "string",
      "mergeSplitId": "string",
      "planEffectDate": "2019-08-24T14:15:22Z",
      "actualEffectDate": "2019-08-24T14:15:22Z",
      "projectPriceId": "string",
      "tablePrice": 0,
      "bottomPrice": 0,
      "areaType": 0,
      "buildArea": 0,
      "innerArea": 0,
      "rentAreaType": 0,
      "rentArea": 0,
      "storey": 0,
      "value": 0,
      "orientation": "string",
      "houseTypeId": "string",
      "smartWaterMeter": "string",
      "smartLock": "string",
      "assetOperationMode": "string",
      "assetOperationType": "string",
      "propertyStatus": "string",
      "paymentStatus": 0,
      "specialTag": "string",
      "latestRentPrice": 0,
      "latestRentStartDate": "2019-08-24T14:15:22Z",
      "latestRentEndDate": "2019-08-24T14:15:22Z",
      "firstRentPrice": 0,
      "firstRentStartDate": "2019-08-24T14:15:22Z",
      "firstRentEndDate": "2019-08-24T14:15:22Z",
      "operationSubject": 0,
      "rentalStartDate": "2019-08-24T14:15:22Z",
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "isSelfUse": true,
      "selfUseSubject": 0,
      "selfUsePurpose": "string",
      "djBindRoomId": "string",
      "djBindName": "string",
      "defaultProcessFlag": true,
      "isDel": true,
      "sysRoomIds": [
        "string"
      ]
    }
  ],
  "approvalStatus": 0,
  "effectType": 0,
  "effectDate": "2019-08-24T14:15:22Z",
  "attachments": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "submitType": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RoomSplitAddDTO](#schemaroomsplitadddto)|

<a id="opIdmultipleDownloadTemplate"></a>

## GET 下载多经导入模版

GET /room/multiple/template/download

下载多经导入模版

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIdgetMergeDetail"></a>

## GET 合并详情接口

GET /room/merge/detail

根据合并ID查询合并详情，包含原房源信息列表和目标房源信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||合并ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "applicationNo": "string",
  "operationType": 0,
  "areaId": "string",
  "buildingId": "string",
  "sourceRoomIds": [
    "string"
  ],
  "sourceRooms": [
    {
      "id": "string",
      "roomName": "string",
      "type": 0,
      "propertyType": "string",
      "propertyTypeName": "string",
      "projectId": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomId": "string",
      "status": 0,
      "isLock": true,
      "isDirty": true,
      "isMaintain": true,
      "roomCode": "string",
      "remark": "string",
      "mergeSplitId": "string",
      "planEffectDate": "2019-08-24T14:15:22Z",
      "actualEffectDate": "2019-08-24T14:15:22Z",
      "projectPriceId": "string",
      "tablePrice": 0,
      "bottomPrice": 0,
      "areaType": 0,
      "buildArea": 0,
      "innerArea": 0,
      "rentAreaType": 0,
      "rentArea": 0,
      "storey": 0,
      "value": 0,
      "orientation": "string",
      "houseTypeId": "string",
      "houseTypeName": "string",
      "smartWaterMeter": "string",
      "smartLock": "string",
      "assetOperationMode": "string",
      "assetOperationType": "string",
      "propertyStatus": "string",
      "paymentStatus": 0,
      "specialTag": "string",
      "latestRentPrice": 0,
      "latestRentStartDate": "2019-08-24T14:15:22Z",
      "latestRentEndDate": "2019-08-24T14:15:22Z",
      "firstRentPrice": 0,
      "firstRentStartDate": "2019-08-24T14:15:22Z",
      "firstRentEndDate": "2019-08-24T14:15:22Z",
      "operationSubject": 0,
      "rentalStartDate": "2019-08-24T14:15:22Z",
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "isSelfUse": true,
      "selfUseSubject": 0,
      "selfUsePurpose": "string",
      "djBindRoomId": "string",
      "djBindName": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "targetRoom": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "projectId": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "roomCode": "string",
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "defaultProcessFlag": true,
    "isDel": true,
    "sysRoomIds": [
      "string"
    ]
  },
  "approvalStatus": 0,
  "effectType": 0,
  "effectDate": "2019-08-24T14:15:22Z",
  "attachments": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "submitType": 0,
  "isDel": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RoomMergeAddDTO](#schemaroommergeadddto)|

<a id="opIdlist_10"></a>

## GET 查询房源列表

GET /room/list

根据查询条件分页获取房源列表数据

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|roomName|query|string| 否 | 房源名称|房源名称|
|type|query|integer(int32)| 否 | 房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|query|string| 否 | 物业类型，二级字典|物业类型，二级字典|
|projectId|query|string| 否 | 项目id|项目id|
|parcelId|query|string| 否 | 地块id|地块id|
|parcelName|query|string| 否 | 地块名称|地块名称|
|buildingId|query|string| 否 | 楼栋id|楼栋id|
|buildingName|query|string| 否 | 楼栋名称|楼栋名称|
|floorId|query|string| 否 | 楼层id|楼层id|
|floorName|query|string| 否 | 楼层名称|楼层名称|
|roomId|query|string| 否 | 房间id， sys_room表的id|房间id， sys_room表的id|
|status|query|integer(int32)| 否 | 状态（0草稿 10待生效 20生效中 30已失效 ）|状态（0草稿 10待生效 20生效中 30已失效 ）|
|isLock|query|boolean| 否 | 是否锁房（0否 1是）|是否锁房（0否 1是）|
|isDirty|query|boolean| 否 | 是否脏房（0否 1是）|是否脏房（0否 1是）|
|isMaintain|query|boolean| 否 | 是否维修（0否 1是）|是否维修（0否 1是）|
|roomCode|query|string| 否 | 合成编码|合成编码|
|remark|query|string| 否 | 描述|描述|
|mergeSplitId|query|string| 否 | 拆分合并id|拆分合并id|
|planEffectDate|query|string(date-time)| 否 | 预计生效日期|预计生效日期|
|actualEffectDate|query|string(date-time)| 否 | 实际生效日期|实际生效日期|
|projectPriceId|query|string| 否 | 最新的立项定价id|最新的立项定价id|
|tablePrice|query|number| 否 | 表价|表价|
|bottomPrice|query|number| 否 | 底价|底价|
|areaType|query|integer(int32)| 否 | 面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|buildArea|query|number| 否 | 建筑面积|建筑面积|
|innerArea|query|number| 否 | 套内面积|套内面积|
|rentAreaType|query|integer(int32)| 否 | 计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|rentArea|query|number| 否 | 计租面积|计租面积|
|storey|query|number| 否 | 层高|层高|
|value|query|number| 否 | 荷载值|荷载值|
|orientation|query|string| 否 | 朝向|朝向|
|houseTypeId|query|string| 否 | 户型id|户型id|
|smartWaterMeter|query|string| 否 | 智能水电表|智能水电表|
|smartLock|query|string| 否 | 智能锁|智能锁|
|assetOperationMode|query|string| 否 | 资产运营模式（自持、可售、代招商）|资产运营模式（自持、可售、代招商）|
|assetOperationType|query|string| 否 | 资产运营分类|资产运营分类|
|propertyStatus|query|string| 否 | 产权情况|产权情况|
|paymentStatus|query|integer(int32)| 否 | 交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|specialTag|query|string| 否 | 特殊标签（1保障房）|特殊标签（1保障房）|
|latestRentPrice|query|number| 否 | 商管承租成本单价（最新）|商管承租成本单价（最新）|
|latestRentStartDate|query|string(date-time)| 否 | 商管承租起计日期（最新）|商管承租起计日期（最新）|
|latestRentEndDate|query|string(date-time)| 否 | 商管承租到期日期（最新）|商管承租到期日期（最新）|
|firstRentPrice|query|number| 否 | 商管承租成本单价第一次|商管承租成本单价第一次|
|firstRentStartDate|query|string(date-time)| 否 | 商管承租起计日期第一次|商管承租起计日期第一次|
|firstRentEndDate|query|string(date-time)| 否 | 商管承租到期日期第一次|商管承租到期日期第一次|
|operationSubject|query|integer(int32)| 否 | 运营主体（1商服 2众创城）,字典|运营主体（1商服 2众创城）,字典|
|rentalStartDate|query|string(date-time)| 否 | 可招商日期|可招商日期|
|externalRentStartDate|query|string(date-time)| 否 | 对外出租起始日期|对外出租起始日期|
|isSelfUse|query|boolean| 否 | 是否自用（0否 1是）|是否自用（0否 1是）|
|selfUseSubject|query|integer(int32)| 否 | 自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|selfUsePurpose|query|string| 否 | 自用用途|自用用途|
|djBindRoomId|query|string| 否 | 多经绑定房源id|多经绑定房源id|
|djBindName|query|string| 否 | 多经绑定房源名称|多经绑定房源名称|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|priceFlag|query|string| 否 | 是否定价|是否定价: 1是 0否|
|parcelOrBuildingName|query|string| 否 | 地块或楼栋名称|地块或楼栋名称|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
[
  {
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "propertyTypeName": "string",
    "projectId": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "roomCode": "string",
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "houseTypeName": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomVo](#schemaroomvo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» roomName|string|false|none|房源名称|房源名称|
|» type|integer(int32)|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|» propertyType|string|false|none|物业类型，二级字典|物业类型，二级字典|
|» propertyTypeName|string|false|none|物业类型名称|物业类型名称|
|» projectId|string|false|none|项目id|项目id|
|» parcelId|string|false|none|地块id|地块id|
|» parcelName|string|false|none|地块名称|地块名称|
|» buildingId|string|false|none|楼栋id|楼栋id|
|» buildingName|string|false|none|楼栋名称|楼栋名称|
|» floorId|string|false|none|楼层id|楼层id|
|» floorName|string|false|none|楼层名称|楼层名称|
|» roomId|string|false|none|房间id， sys_room表的id|房间id， sys_room表的id|
|» status|integer(int32)|false|none|状态（0草稿 10待生效 20生效中 30已失效 ）|状态（0草稿 10待生效 20生效中 30已失效 ）|
|» isLock|boolean|false|none|是否锁房（0否 1是）|是否锁房（0否 1是）|
|» isDirty|boolean|false|none|是否脏房（0否 1是）|是否脏房（0否 1是）|
|» isMaintain|boolean|false|none|是否维修（0否 1是）|是否维修（0否 1是）|
|» roomCode|string|false|none|合成编码|合成编码|
|» remark|string|false|none|描述|描述|
|» mergeSplitId|string|false|none|拆分合并id|拆分合并id|
|» planEffectDate|string(date-time)|false|none|预计生效日期|预计生效日期|
|» actualEffectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|» projectPriceId|string|false|none|最新的立项定价id|最新的立项定价id|
|» tablePrice|number|false|none|表价|表价|
|» bottomPrice|number|false|none|底价|底价|
|» areaType|integer(int32)|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|» buildArea|number|false|none|建筑面积|建筑面积|
|» innerArea|number|false|none|套内面积|套内面积|
|» rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|» rentArea|number|false|none|计租面积|计租面积|
|» storey|number|false|none|层高|层高|
|» value|number|false|none|荷载值|荷载值|
|» orientation|string|false|none|朝向|朝向|
|» houseTypeId|string|false|none|户型id|户型id|
|» houseTypeName|string|false|none|户型名称|户型名称|
|» smartWaterMeter|string|false|none|智能水电表|智能水电表|
|» smartLock|string|false|none|智能锁|智能锁|
|» assetOperationMode|string|false|none|资产运营模式（自持、可售、代招商）|资产运营模式（自持、可售、代招商）|
|» assetOperationType|string|false|none|资产运营分类|资产运营分类|
|» propertyStatus|string|false|none|产权情况|产权情况|
|» paymentStatus|integer(int32)|false|none|交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|» specialTag|string|false|none|特殊标签（1保障房）|特殊标签（1保障房）|
|» latestRentPrice|number|false|none|商管承租成本单价（最新）|商管承租成本单价（最新）|
|» latestRentStartDate|string(date-time)|false|none|商管承租起计日期（最新）|商管承租起计日期（最新）|
|» latestRentEndDate|string(date-time)|false|none|商管承租到期日期（最新）|商管承租到期日期（最新）|
|» firstRentPrice|number|false|none|商管承租成本单价第一次|商管承租成本单价第一次|
|» firstRentStartDate|string(date-time)|false|none|商管承租起计日期第一次|商管承租起计日期第一次|
|» firstRentEndDate|string(date-time)|false|none|商管承租到期日期第一次|商管承租到期日期第一次|
|» operationSubject|integer(int32)|false|none|运营主体（1商服 2众创城）,字典|运营主体（1商服 2众创城）,字典|
|» rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|» externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|» isSelfUse|boolean|false|none|是否自用（0否 1是）|是否自用（0否 1是）|
|» selfUseSubject|integer(int32)|false|none|自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|» selfUsePurpose|string|false|none|自用用途|自用用途|
|» djBindRoomId|string|false|none|多经绑定房源id|多经绑定房源id|
|» djBindName|string|false|none|多经绑定房源名称|多经绑定房源名称|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIdgetInfo_2"></a>

## GET 获取房源详细信息

GET /room/detail

根据房源ID获取房源的详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||房源ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "id": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "propertyTypeName": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true,
  "roomCode": "string",
  "remark": "string",
  "mergeSplitId": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "projectPriceId": "string",
  "tablePrice": 0,
  "bottomPrice": 0,
  "areaType": 0,
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": 0,
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "houseTypeName": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": 0,
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": true,
  "selfUseSubject": 0,
  "selfUsePurpose": "string",
  "djBindRoomId": "string",
  "djBindName": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RoomVo](#schemaroomvo)|

<a id="opIdgetRoomChangeHistory"></a>

## GET 房源变更历史接口

GET /room/change/list

根据房间ID查询该房间的历史变更信息列表，包括生效、信息变更、拆分、合并、作废等记录

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|roomId|query|string| 是 ||房间ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
[
  {
    "id": "string",
    "roomId": "string",
    "changeType": 0,
    "mergeSplitId": "string",
    "mergeSplitType": "string",
    "changeTime": "2019-08-24T14:15:22Z",
    "changeContent": "string",
    "remark": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomChangeRecordVo](#schemaroomchangerecordvo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» roomId|string|false|none|房源ID|房源ID|
|» changeType|integer(int32)|false|none|变更类型（1生效 2信息变更 3拆分 4合并 5作废）|变更类型（1生效 2信息变更 3拆分 4合并 5作废）|
|» mergeSplitId|string|false|none|拆分合并表ID，仅当变更类型为拆分或合并时有效|拆分合并表ID，仅当变更类型为拆分或合并时有效|
|» mergeSplitType|string|false|none|拆分合并类型（source target）|拆分合并类型（source target）|
|» changeTime|string(date-time)|false|none|变更时间|变更时间|
|» changeContent|string|false|none|变更内容|变更内容|
|» remark|string|false|none|备注|备注|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIddeleteMergeSplit"></a>

## DELETE 拆分合并删除接口

DELETE /room/mergeSplit/delete

根据拆分合并ID删除记录，只有草稿和驳回状态的记录才能删除

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||拆分合并ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

<a id="opIdremoveById"></a>

## DELETE 删除单个房源

DELETE /room/deleteById

根据房源ID删除单个房源，删除前会校验房源状态和合同关联情况

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||房源ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|boolean|

# 数据模型

<h2 id="tocS_RoomAddDTO">RoomAddDTO</h2>

<a id="schemaroomadddto"></a>
<a id="schema_RoomAddDTO"></a>
<a id="tocSroomadddto"></a>
<a id="tocsroomadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true,
  "roomCode": "string",
  "remark": "string",
  "mergeSplitId": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "projectPriceId": "string",
  "tablePrice": 0,
  "bottomPrice": 0,
  "areaType": 0,
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": 0,
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": 0,
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": true,
  "selfUseSubject": 0,
  "selfUsePurpose": "string",
  "djBindRoomId": "string",
  "djBindName": "string",
  "defaultProcessFlag": true,
  "isDel": true,
  "sysRoomIds": [
    "string"
  ]
}

```

目标房间信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|roomName|string|false|none|房源名称|房源名称|
|type|integer(int32)|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|string|false|none|物业类型，二级字典|物业类型，二级字典|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|地块id|地块id|
|parcelName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋id|楼栋id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层id|楼层id|
|floorName|string|false|none|楼层名称|楼层名称|
|roomId|string|false|none|房间id， sys_room表的id|房间id， sys_room表的id|
|status|integer(int32)|false|none|状态（0草稿 10待生效 20生效中 30已失效 ）|状态（0草稿 10待生效 20生效中 30已失效 ）|
|isLock|boolean|false|none|是否锁房（0否 1是）|是否锁房（0否 1是）|
|isDirty|boolean|false|none|是否脏房（0否 1是）|是否脏房（0否 1是）|
|isMaintain|boolean|false|none|是否维修（0否 1是）|是否维修（0否 1是）|
|roomCode|string|false|none|合成编码|合成编码|
|remark|string|false|none|描述|描述|
|mergeSplitId|string|false|none|拆分合并id|拆分合并id|
|planEffectDate|string(date-time)|false|none|预计生效日期|预计生效日期|
|actualEffectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|projectPriceId|string|false|none|最新的立项定价id|最新的立项定价id|
|tablePrice|number|false|none|表价|表价|
|bottomPrice|number|false|none|底价|底价|
|areaType|integer(int32)|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|rentArea|number|false|none|计租面积|计租面积|
|storey|number|false|none|层高|层高|
|value|number|false|none|荷载值|荷载值|
|orientation|string|false|none|朝向|朝向|
|houseTypeId|string|false|none|户型id|户型id|
|smartWaterMeter|string|false|none|智能水电表|智能水电表|
|smartLock|string|false|none|智能锁|智能锁|
|assetOperationMode|string|false|none|资产运营模式（自持、可售、代招商）|资产运营模式（自持、可售、代招商）|
|assetOperationType|string|false|none|资产运营分类|资产运营分类|
|propertyStatus|string|false|none|产权情况|产权情况|
|paymentStatus|integer(int32)|false|none|交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|specialTag|string|false|none|特殊标签（1保障房）|特殊标签（1保障房）|
|latestRentPrice|number|false|none|商管承租成本单价（最新）|商管承租成本单价（最新）|
|latestRentStartDate|string(date-time)|false|none|商管承租起计日期（最新）|商管承租起计日期（最新）|
|latestRentEndDate|string(date-time)|false|none|商管承租到期日期（最新）|商管承租到期日期（最新）|
|firstRentPrice|number|false|none|商管承租成本单价第一次|商管承租成本单价第一次|
|firstRentStartDate|string(date-time)|false|none|商管承租起计日期第一次|商管承租起计日期第一次|
|firstRentEndDate|string(date-time)|false|none|商管承租到期日期第一次|商管承租到期日期第一次|
|operationSubject|integer(int32)|false|none|运营主体（1商服 2众创城）,字典|运营主体（1商服 2众创城）,字典|
|rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|isSelfUse|boolean|false|none|是否自用（0否 1是）|是否自用（0否 1是）|
|selfUseSubject|integer(int32)|false|none|自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|selfUsePurpose|string|false|none|自用用途|自用用途|
|djBindRoomId|string|false|none|多经绑定房源id|多经绑定房源id|
|djBindName|string|false|none|多经绑定房源名称|多经绑定房源名称|
|defaultProcessFlag|boolean|false|none|是否需要设置默认值|是否需要设置默认值|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|sysRoomIds|[string]|false|none|引入资产的房源id列表|引入资产的房源id列表|
|» 引入资产的房源id列表|string|false|none|引入资产的房源id列表|引入资产的房源id列表|

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_RoomVo">RoomVo</h2>

<a id="schemaroomvo"></a>
<a id="schema_RoomVo"></a>
<a id="tocSroomvo"></a>
<a id="tocsroomvo"></a>

```json
{
  "id": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "propertyTypeName": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true,
  "roomCode": "string",
  "remark": "string",
  "mergeSplitId": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "projectPriceId": "string",
  "tablePrice": 0,
  "bottomPrice": 0,
  "areaType": 0,
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": 0,
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "houseTypeName": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": 0,
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": true,
  "selfUseSubject": 0,
  "selfUsePurpose": "string",
  "djBindRoomId": "string",
  "djBindName": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|roomName|string|false|none|房源名称|房源名称|
|type|integer(int32)|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|string|false|none|物业类型，二级字典|物业类型，二级字典|
|propertyTypeName|string|false|none|物业类型名称|物业类型名称|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|地块id|地块id|
|parcelName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋id|楼栋id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层id|楼层id|
|floorName|string|false|none|楼层名称|楼层名称|
|roomId|string|false|none|房间id， sys_room表的id|房间id， sys_room表的id|
|status|integer(int32)|false|none|状态（0草稿 10待生效 20生效中 30已失效 ）|状态（0草稿 10待生效 20生效中 30已失效 ）|
|isLock|boolean|false|none|是否锁房（0否 1是）|是否锁房（0否 1是）|
|isDirty|boolean|false|none|是否脏房（0否 1是）|是否脏房（0否 1是）|
|isMaintain|boolean|false|none|是否维修（0否 1是）|是否维修（0否 1是）|
|roomCode|string|false|none|合成编码|合成编码|
|remark|string|false|none|描述|描述|
|mergeSplitId|string|false|none|拆分合并id|拆分合并id|
|planEffectDate|string(date-time)|false|none|预计生效日期|预计生效日期|
|actualEffectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|projectPriceId|string|false|none|最新的立项定价id|最新的立项定价id|
|tablePrice|number|false|none|表价|表价|
|bottomPrice|number|false|none|底价|底价|
|areaType|integer(int32)|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|rentArea|number|false|none|计租面积|计租面积|
|storey|number|false|none|层高|层高|
|value|number|false|none|荷载值|荷载值|
|orientation|string|false|none|朝向|朝向|
|houseTypeId|string|false|none|户型id|户型id|
|houseTypeName|string|false|none|户型名称|户型名称|
|smartWaterMeter|string|false|none|智能水电表|智能水电表|
|smartLock|string|false|none|智能锁|智能锁|
|assetOperationMode|string|false|none|资产运营模式（自持、可售、代招商）|资产运营模式（自持、可售、代招商）|
|assetOperationType|string|false|none|资产运营分类|资产运营分类|
|propertyStatus|string|false|none|产权情况|产权情况|
|paymentStatus|integer(int32)|false|none|交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|specialTag|string|false|none|特殊标签（1保障房）|特殊标签（1保障房）|
|latestRentPrice|number|false|none|商管承租成本单价（最新）|商管承租成本单价（最新）|
|latestRentStartDate|string(date-time)|false|none|商管承租起计日期（最新）|商管承租起计日期（最新）|
|latestRentEndDate|string(date-time)|false|none|商管承租到期日期（最新）|商管承租到期日期（最新）|
|firstRentPrice|number|false|none|商管承租成本单价第一次|商管承租成本单价第一次|
|firstRentStartDate|string(date-time)|false|none|商管承租起计日期第一次|商管承租起计日期第一次|
|firstRentEndDate|string(date-time)|false|none|商管承租到期日期第一次|商管承租到期日期第一次|
|operationSubject|integer(int32)|false|none|运营主体（1商服 2众创城）,字典|运营主体（1商服 2众创城）,字典|
|rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|isSelfUse|boolean|false|none|是否自用（0否 1是）|是否自用（0否 1是）|
|selfUseSubject|integer(int32)|false|none|自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|selfUsePurpose|string|false|none|自用用途|自用用途|
|djBindRoomId|string|false|none|多经绑定房源id|多经绑定房源id|
|djBindName|string|false|none|多经绑定房源名称|多经绑定房源名称|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_RoomStatisticsQueryDTO">RoomStatisticsQueryDTO</h2>

<a id="schemaroomstatisticsquerydto"></a>
<a id="schema_RoomStatisticsQueryDTO"></a>
<a id="tocSroomstatisticsquerydto"></a>
<a id="tocsroomstatisticsquerydto"></a>

```json
{
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|地块id|地块id|
|buildingId|string|false|none|楼栋id|楼栋id|

<h2 id="tocS_AssetCompositionVo">AssetCompositionVo</h2>

<a id="schemaassetcompositionvo"></a>
<a id="schema_AssetCompositionVo"></a>
<a id="tocSassetcompositionvo"></a>
<a id="tocsassetcompositionvo"></a>

```json
{
  "typeCode": "string",
  "typeName": "string",
  "buildArea": 0,
  "count": 0
}

```

资产构成

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|typeCode|string|false|none|类型编码|产品类型或物业类型编码|
|typeName|string|false|none|类型名称|产品类型或物业类型名称|
|buildArea|number|false|none|建筑面积|该类型下的建筑面积总和|
|count|integer(int32)|false|none|数量|该类型下的数量|

<h2 id="tocS_RoomTreeVo">RoomTreeVo</h2>

<a id="schemaroomtreevo"></a>
<a id="schema_RoomTreeVo"></a>
<a id="tocSroomtreevo"></a>
<a id="tocsroomtreevo"></a>

```json
{
  "id": "string",
  "name": "string",
  "parentId": "string",
  "roomId": "string",
  "roomName": "string",
  "projectId": "string",
  "projectName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "propertyType": "string",
  "rentStatus": "string",
  "rentAreaType": 0,
  "rentArea": 0,
  "price": 0,
  "bottomPrice": 0,
  "priceUnit": 0,
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "depositType": 0,
  "depositAmount": 0,
  "level": 0,
  "children": [
    {
      "id": "string",
      "name": "string",
      "parentId": "string",
      "roomId": "string",
      "roomName": "string",
      "projectId": "string",
      "projectName": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "propertyType": "string",
      "rentStatus": "string",
      "rentAreaType": 0,
      "rentArea": 0,
      "price": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "depositType": 0,
      "depositAmount": 0,
      "level": 0,
      "children": [
        {
          "id": "string",
          "name": "string",
          "parentId": "string",
          "roomId": "string",
          "roomName": "string",
          "projectId": "string",
          "projectName": "string",
          "parcelId": "string",
          "parcelName": "string",
          "buildingId": "string",
          "buildingName": "string",
          "propertyType": "string",
          "rentStatus": "string",
          "rentAreaType": 0,
          "rentArea": 0,
          "price": 0,
          "bottomPrice": 0,
          "priceUnit": 0,
          "externalRentStartDate": "2019-08-24T14:15:22Z",
          "depositType": 0,
          "depositAmount": 0,
          "level": 0,
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|树结构id|none|
|name|string|false|none|树结构名称|none|
|parentId|string|false|none|父级id|none|
|roomId|string|false|none|房源roomId|none|
|roomName|string|false|none|房源名称|none|
|projectId|string|false|none|项目id|none|
|projectName|string|false|none|项目名称|none|
|parcelId|string|false|none|地块id|none|
|parcelName|string|false|none|地块名称|none|
|buildingId|string|false|none|楼栋id|none|
|buildingName|string|false|none|楼栋名称|none|
|propertyType|string|false|none|物业类型|none|
|rentStatus|string|false|none|租控状态 0-可租 1-已租（字段待确认）|none|
|rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|none|
|rentArea|number|false|none|计租面积|none|
|price|number|false|none|单价|none|
|bottomPrice|number|false|none|底价|none|
|priceUnit|integer(int32)|false|none|单价单位(1元/平方米/月 2元/月 3元/日)|none|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|none|
|depositType|integer(int32)|false|none|保证金类型|none|
|depositAmount|number|false|none|保证金金额|none|
|level|integer(int32)|false|none| 1：项目 2：地块 3：楼栋 4：房间|none|
|children|[[RoomTreeVo](#schemaroomtreevo)]|false|none|子节点列表|none|

<h2 id="tocS_RoomSplitAddDTO">RoomSplitAddDTO</h2>

<a id="schemaroomsplitadddto"></a>
<a id="schema_RoomSplitAddDTO"></a>
<a id="tocSroomsplitadddto"></a>
<a id="tocsroomsplitadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "applicationNo": "string",
  "operationType": 0,
  "areaId": "string",
  "buildingId": "string",
  "sourceRoomId": "string",
  "sourceRoomVo": {
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "propertyTypeName": "string",
    "projectId": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "roomCode": "string",
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "houseTypeName": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "targetRooms": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "roomName": "string",
      "type": 0,
      "propertyType": "string",
      "projectId": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomId": "string",
      "status": 0,
      "isLock": true,
      "isDirty": true,
      "isMaintain": true,
      "roomCode": "string",
      "remark": "string",
      "mergeSplitId": "string",
      "planEffectDate": "2019-08-24T14:15:22Z",
      "actualEffectDate": "2019-08-24T14:15:22Z",
      "projectPriceId": "string",
      "tablePrice": 0,
      "bottomPrice": 0,
      "areaType": 0,
      "buildArea": 0,
      "innerArea": 0,
      "rentAreaType": 0,
      "rentArea": 0,
      "storey": 0,
      "value": 0,
      "orientation": "string",
      "houseTypeId": "string",
      "smartWaterMeter": "string",
      "smartLock": "string",
      "assetOperationMode": "string",
      "assetOperationType": "string",
      "propertyStatus": "string",
      "paymentStatus": 0,
      "specialTag": "string",
      "latestRentPrice": 0,
      "latestRentStartDate": "2019-08-24T14:15:22Z",
      "latestRentEndDate": "2019-08-24T14:15:22Z",
      "firstRentPrice": 0,
      "firstRentStartDate": "2019-08-24T14:15:22Z",
      "firstRentEndDate": "2019-08-24T14:15:22Z",
      "operationSubject": 0,
      "rentalStartDate": "2019-08-24T14:15:22Z",
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "isSelfUse": true,
      "selfUseSubject": 0,
      "selfUsePurpose": "string",
      "djBindRoomId": "string",
      "djBindName": "string",
      "defaultProcessFlag": true,
      "isDel": true,
      "sysRoomIds": [
        "string"
      ]
    }
  ],
  "approvalStatus": 0,
  "effectType": 0,
  "effectDate": "2019-08-24T14:15:22Z",
  "attachments": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "submitType": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|applicationNo|string|false|none|申请编号|申请编号|
|operationType|integer(int32)|false|none|操作类型|操作类型，拆分固定为3|
|areaId|string|false|none|地块ID|地块ID|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|sourceRoomId|string|false|none|来源房间ID|来源房间ID|
|sourceRoomVo|[RoomVo](#schemaroomvo)|false|none||none|
|targetRooms|[[RoomAddDTO](#schemaroomadddto)]|false|none|目标房间信息列表|目标房间信息列表|
|approvalStatus|integer(int32)|false|none|审批状态|审批状态（0草稿 1待审批 2已审批 3驳回）|
|effectType|integer(int32)|false|none|生效方式|生效方式（1立即生效 2到期生效）|
|effectDate|string(date-time)|false|none|生效日期|生效日期|
|attachments|string|false|none|附件|附件|
|adjustmentReason|string|false|none|调整原因|调整原因|
|remark|string|false|none|备注|备注|
|submitType|integer(int32)|false|none|提交类型|提交类型（1暂存 2提交）|

<h2 id="tocS_RoomStatisticsVo">RoomStatisticsVo</h2>

<a id="schemaroomstatisticsvo"></a>
<a id="schema_RoomStatisticsVo"></a>
<a id="tocSroomstatisticsvo"></a>
<a id="tocsroomstatisticsvo"></a>

```json
{
  "selfHoldArea": 0,
  "receivedAssetArea": 0,
  "totalRoomArea": 0,
  "roomCount": 0,
  "receivedAssetComposition": [
    {
      "typeCode": "string",
      "typeName": "string",
      "buildArea": 0,
      "count": 0
    }
  ],
  "roomBizTypeComposition": [
    {
      "typeCode": "string",
      "typeName": "string",
      "buildArea": 0,
      "count": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|selfHoldArea|number|false|none|自持面积|查询sys_building下面的总自持面积|
|receivedAssetArea|number|false|none|已接收资产面积|查询sys_room下的建筑面积求和|
|totalRoomArea|number|false|none|房源总面积|查询t_room下的建筑面积求和|
|roomCount|integer(int32)|false|none|房源数量|查询t_room的个数|
|receivedAssetComposition|[[AssetCompositionVo](#schemaassetcompositionvo)]|false|none|接收资产构成|sys_room根据产品类型分组的建筑面积|
|roomBizTypeComposition|[[AssetCompositionVo](#schemaassetcompositionvo)]|false|none|房源业态构成|t_room根据物业类型分组的建筑面积|

<h2 id="tocS_RoomTreeQueryDTO">RoomTreeQueryDTO</h2>

<a id="schemaroomtreequerydto"></a>
<a id="schema_RoomTreeQueryDTO"></a>
<a id="tocSroomtreequerydto"></a>
<a id="tocsroomtreequerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "contractType": "string",
  "buildingType": "string",
  "buildingId": "string",
  "rentStatus": "string",
  "roomName": "string",
  "projectId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|contractType|string|false|none|合同类型，0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型，0-非宿舍,1-宿舍,2-多经,3-日租房|
|buildingType|string|false|none|业态|业态|
|buildingId|string|false|none|楼栋id|楼栋id|
|rentStatus|string|false|none|租控状态 0-可租 1-已租|租控状态 0-可租 1-已租|
|roomName|string|false|none|房源名称|房源名称|
|projectId|string|false|none|项目id|项目id|

<h2 id="tocS_RoomMergeSplitVo">RoomMergeSplitVo</h2>

<a id="schemaroommergesplitvo"></a>
<a id="schema_RoomMergeSplitVo"></a>
<a id="tocSroommergesplitvo"></a>
<a id="tocsroommergesplitvo"></a>

```json
{
  "id": "string",
  "applicationNo": "string",
  "operationType": 0,
  "areaId": "string",
  "areaName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "sourceRoomId": "string",
  "sourceRoomInfo": "string",
  "targetRoomId": "string",
  "targetRoomInfo": "string",
  "approvalStatus": 0,
  "effectType": 0,
  "effectDate": "2019-08-24T14:15:22Z",
  "attachments": "string",
  "roomInfo": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|applicationNo|string|false|none|申请编号|申请编号|
|operationType|integer(int32)|false|none|操作类型（3拆分 4合并）|操作类型（3拆分 4合并）|
|areaId|string|false|none|地块ID|地块ID|
|areaName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|sourceRoomId|string|false|none|原房源id|原房源id|
|sourceRoomInfo|string|false|none|原房源名称|原房源名称|
|targetRoomId|string|false|none|调整后房源id|调整后房源id|
|targetRoomInfo|string|false|none|调整后房源名称|调整后房源名称|
|approvalStatus|integer(int32)|false|none|审批状态（0草稿 1待审批 2已审批 3驳回）|审批状态（0草稿 1待审批 2已审批 3驳回）|
|effectType|integer(int32)|false|none|生效方式（1立即生效 2到期生效）|生效方式（1立即生效 2到期生效）|
|effectDate|string(date-time)|false|none|生效日期|生效日期|
|attachments|string|false|none|附件|附件|
|roomInfo|string|false|none|房间草稿信息|房间草稿信息|
|adjustmentReason|string|false|none|调整原因|调整原因|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_RoomMergeSplitQueryDTO">RoomMergeSplitQueryDTO</h2>

<a id="schemaroommergesplitquerydto"></a>
<a id="schema_RoomMergeSplitQueryDTO"></a>
<a id="tocSroommergesplitquerydto"></a>
<a id="tocsroommergesplitquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "applicationNo": "string",
  "operationType": 0,
  "areaId": "string",
  "buildingId": "string",
  "roomName": "string",
  "sourceRoomId": "string",
  "sourceRoomInfo": "string",
  "targetRoomId": "string",
  "targetRoomInfo": "string",
  "approvalStatus": 0,
  "effectType": 0,
  "effectDate": "2019-08-24T14:15:22Z",
  "createTimeStart": "2019-08-24T14:15:22Z",
  "createTimeEnd": "2019-08-24T14:15:22Z",
  "attachments": "string",
  "roomInfo": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目ID|项目ID|
|applicationNo|string|false|none|申请编号|申请编号|
|operationType|integer(int32)|false|none|操作类型（3拆分 4合并）|操作类型（3拆分 4合并）|
|areaId|string|false|none|地块ID|地块ID|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|roomName|string|false|none|房源名称|房源名称（模糊搜索）|
|sourceRoomId|string|false|none|原房源id|原房源id|
|sourceRoomInfo|string|false|none|原房源名称|原房源名称|
|targetRoomId|string|false|none|调整后房源id|调整后房源id|
|targetRoomInfo|string|false|none|调整后房源名称|调整后房源名称|
|approvalStatus|integer(int32)|false|none|审批状态（0草稿 1待审批 2已审批 3驳回）|审批状态（0草稿 1待审批 2已审批 3驳回）|
|effectType|integer(int32)|false|none|生效方式（1立即生效 2到期生效）|生效方式（1立即生效 2到期生效）|
|effectDate|string(date-time)|false|none|生效日期|生效日期|
|createTimeStart|string(date-time)|false|none|创建日期起始|创建日期起始|
|createTimeEnd|string(date-time)|false|none|创建日期结束|创建日期结束|
|attachments|string|false|none|附件|附件|
|roomInfo|string|false|none|房间草稿信息|房间草稿信息|
|adjustmentReason|string|false|none|调整原因|调整原因|
|remark|string|false|none|备注|备注|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_RoomDiagramQueryDTO">RoomDiagramQueryDTO</h2>

<a id="schemaroomdiagramquerydto"></a>
<a id="schema_RoomDiagramQueryDTO"></a>
<a id="tocSroomdiagramquerydto"></a>
<a id="tocsroomdiagramquerydto"></a>

```json
{
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomStatus": 0,
  "propertyType": "string",
  "diagramDate": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|false|none|项目ID|项目ID|
|parcelId|string|false|none|地块ID|地块ID|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|floorId|string|false|none|楼层ID|楼层ID|
|roomStatus|integer(int32)|false|none|状态|房源状态(1.空置，2.在租，3.待生效/签约中/已预定， 4.不可招商)|
|propertyType|string|false|none|用途|房源用途|
|diagramDate|string(date-time)|false|none|指定房态时间|指定房态时间，默认为当前时间|

<h2 id="tocS_RoomBatchUpdateDTO">RoomBatchUpdateDTO</h2>

<a id="schemaroombatchupdatedto"></a>
<a id="schema_RoomBatchUpdateDTO"></a>
<a id="tocSroombatchupdatedto"></a>
<a id="tocsroombatchupdatedto"></a>

```json
{
  "roomIds": [
    "string"
  ],
  "roomInfo": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "projectId": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "roomCode": "string",
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "defaultProcessFlag": true,
    "isDel": true,
    "sysRoomIds": [
      "string"
    ]
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roomIds|[string]|false|none|房间id数组|需要批量更新的房间id列表|
|» 房间id数组|string|false|none|房间id数组|需要批量更新的房间id列表|
|roomInfo|[RoomAddDTO](#schemaroomadddto)|false|none||目标房间信息|

<h2 id="tocS_BookDiagramVo">BookDiagramVo</h2>

<a id="schemabookdiagramvo"></a>
<a id="schema_BookDiagramVo"></a>
<a id="tocSbookdiagramvo"></a>
<a id="tocsbookdiagramvo"></a>

```json
{
  "customerName": "string",
  "companyName": "string",
  "bookingAmount": 0,
  "bookingTime": "2019-08-24T14:15:22Z",
  "canRefund": true,
  "roomStatus": "string"
}

```

订单信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|customerName|string|false|none|客户名称|客户名称|
|companyName|string|false|none|公司名称|公司名称|
|bookingAmount|number|false|none|预定单金额|预定单金额|
|bookingTime|string(date-time)|false|none|预定时间|预定时间|
|canRefund|boolean|false|none|是否可退|是否可退（0否 1是）|
|roomStatus|string|false|none|房间状态|房间状态|

<h2 id="tocS_RoomMergeSplitApprovalDTO">RoomMergeSplitApprovalDTO</h2>

<a id="schemaroommergesplitapprovaldto"></a>
<a id="schema_RoomMergeSplitApprovalDTO"></a>
<a id="tocSroommergesplitapprovaldto"></a>
<a id="tocsroommergesplitapprovaldto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "approveResult": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|拆分合并主键ID|拆分合并主键ID|
|approveResult|string|false|none|审批结果(1:拒绝，2:审批通过)|审批结果(1:拒绝，2:审批通过)|

<h2 id="tocS_RoomMergeAddDTO">RoomMergeAddDTO</h2>

<a id="schemaroommergeadddto"></a>
<a id="schema_RoomMergeAddDTO"></a>
<a id="tocSroommergeadddto"></a>
<a id="tocsroommergeadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "applicationNo": "string",
  "operationType": 0,
  "areaId": "string",
  "buildingId": "string",
  "sourceRoomIds": [
    "string"
  ],
  "sourceRooms": [
    {
      "id": "string",
      "roomName": "string",
      "type": 0,
      "propertyType": "string",
      "propertyTypeName": "string",
      "projectId": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomId": "string",
      "status": 0,
      "isLock": true,
      "isDirty": true,
      "isMaintain": true,
      "roomCode": "string",
      "remark": "string",
      "mergeSplitId": "string",
      "planEffectDate": "2019-08-24T14:15:22Z",
      "actualEffectDate": "2019-08-24T14:15:22Z",
      "projectPriceId": "string",
      "tablePrice": 0,
      "bottomPrice": 0,
      "areaType": 0,
      "buildArea": 0,
      "innerArea": 0,
      "rentAreaType": 0,
      "rentArea": 0,
      "storey": 0,
      "value": 0,
      "orientation": "string",
      "houseTypeId": "string",
      "houseTypeName": "string",
      "smartWaterMeter": "string",
      "smartLock": "string",
      "assetOperationMode": "string",
      "assetOperationType": "string",
      "propertyStatus": "string",
      "paymentStatus": 0,
      "specialTag": "string",
      "latestRentPrice": 0,
      "latestRentStartDate": "2019-08-24T14:15:22Z",
      "latestRentEndDate": "2019-08-24T14:15:22Z",
      "firstRentPrice": 0,
      "firstRentStartDate": "2019-08-24T14:15:22Z",
      "firstRentEndDate": "2019-08-24T14:15:22Z",
      "operationSubject": 0,
      "rentalStartDate": "2019-08-24T14:15:22Z",
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "isSelfUse": true,
      "selfUseSubject": 0,
      "selfUsePurpose": "string",
      "djBindRoomId": "string",
      "djBindName": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "targetRoom": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "projectId": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "roomCode": "string",
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "defaultProcessFlag": true,
    "isDel": true,
    "sysRoomIds": [
      "string"
    ]
  },
  "approvalStatus": 0,
  "effectType": 0,
  "effectDate": "2019-08-24T14:15:22Z",
  "attachments": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "submitType": 0,
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|applicationNo|string|false|none|申请编号|申请编号|
|operationType|integer(int32)|false|none|操作类型|操作类型，合并固定为4|
|areaId|string|false|none|地块ID|地块ID|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|sourceRoomIds|[string]|false|none|来源房间ID列表|来源房间ID列表|
|» 来源房间ID列表|string|false|none|来源房间ID列表|来源房间ID列表|
|sourceRooms|[[RoomVo](#schemaroomvo)]|false|none|来源房间信息列表|来源房间信息列表|
|targetRoom|[RoomAddDTO](#schemaroomadddto)|false|none||目标房间信息|
|approvalStatus|integer(int32)|false|none|审批状态|审批状态（0草稿 1待审批 2已审批 3驳回）|
|effectType|integer(int32)|false|none|生效方式|生效方式（1立即生效 2到期生效）|
|effectDate|string(date-time)|false|none|生效日期|生效日期|
|attachments|string|false|none|附件|附件|
|adjustmentReason|string|false|none|调整原因|调整原因|
|remark|string|false|none|备注|备注|
|submitType|integer(int32)|false|none|提交类型|提交类型（1暂存 2提交）|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractDiagramVo">ContractDiagramVo</h2>

<a id="schemacontractdiagramvo"></a>
<a id="schema_ContractDiagramVo"></a>
<a id="tocScontractdiagramvo"></a>
<a id="tocscontractdiagramvo"></a>

```json
{
  "roomId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractNumber": "string",
  "contractCategory": "string",
  "contractType": 0,
  "signType": 0,
  "rentPrice": 0,
  "tenantType": "string",
  "tenantName": "string",
  "tenantIdCard": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "rentTerm": "string",
  "status": 0,
  "roomStatus": "string",
  "approveStatus": 0
}

```

合同信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roomId|string|false|none|房间id|房间id|
|contractId|string|false|none|合同id|合同id|
|contractNo|string|false|none|合同号|合同号|
|contractNumber|string|false|none|合同编号|合同编号|
|contractCategory|string|false|none|合同类别|合同类别|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|rentPrice|number|false|none|租金|租金|
|tenantType|string|false|none|承租类型|承租类型|
|tenantName|string|false|none|承租人|承租人|
|tenantIdCard|string|false|none|承租人证件号|承租人证件号|
|startDate|string(date-time)|false|none|合同开始时间|合同开始时间|
|endDate|string(date-time)|false|none|合同结束时间|合同结束时间|
|rentTerm|string|false|none|合同租期|合同租期|
|status|integer(int32)|false|none|合同一级状态|合同一级状态|
|roomStatus|string|false|none|房间状态|房间状态|
|approveStatus|integer(int32)|false|none|审批状态|审批状态|

<h2 id="tocS_FloorDiagramVo">FloorDiagramVo</h2>

<a id="schemafloordiagramvo"></a>
<a id="schema_FloorDiagramVo"></a>
<a id="tocSfloordiagramvo"></a>
<a id="tocsfloordiagramvo"></a>

```json
{
  "floorId": "string",
  "floorName": "string",
  "rooms": [
    {
      "roomId": "string",
      "roomName": "string",
      "propertyType": "string",
      "rentArea": 0,
      "orientation": "string",
      "isSelfUse": true,
      "needCheckOut": true,
      "roomStatusName": "string",
      "roomStatus": 0,
      "tags": [
        "string"
      ],
      "houseTypeId": "string",
      "tablePrice": 0,
      "rentAreaType": 0,
      "emptyDays": 0,
      "selfUseSubject": 0,
      "rentalStartDate": "2019-08-24T14:15:22Z",
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "isDirty": true,
      "isLock": true,
      "isMaintain": true,
      "bookingVo": {
        "customerName": "string",
        "companyName": "string",
        "bookingAmount": 0,
        "bookingTime": "2019-08-24T14:15:22Z",
        "canRefund": true,
        "roomStatus": "string"
      },
      "bookings": [
        {
          "customerName": "string",
          "companyName": "string",
          "bookingAmount": 0,
          "bookingTime": "2019-08-24T14:15:22Z",
          "canRefund": true,
          "roomStatus": "string"
        }
      ],
      "contractVo": {
        "roomId": "string",
        "contractId": "string",
        "contractNo": "string",
        "contractNumber": "string",
        "contractCategory": "string",
        "contractType": 0,
        "signType": 0,
        "rentPrice": 0,
        "tenantType": "string",
        "tenantName": "string",
        "tenantIdCard": "string",
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "rentTerm": "string",
        "status": 0,
        "roomStatus": "string",
        "approveStatus": 0
      },
      "contracts": [
        {
          "roomId": "string",
          "contractId": "string",
          "contractNo": "string",
          "contractNumber": "string",
          "contractCategory": "string",
          "contractType": 0,
          "signType": 0,
          "rentPrice": 0,
          "tenantType": "string",
          "tenantName": "string",
          "tenantIdCard": "string",
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "rentTerm": "string",
          "status": 0,
          "roomStatus": "string",
          "approveStatus": 0
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|floorId|string|false|none|楼层ID|楼层ID|
|floorName|string|false|none|楼层名称|楼层名称|
|rooms|[[RoomDiagramVo](#schemaroomdiagramvo)]|false|none|房间信息列表|该楼层下的房间房态信息列表|

<h2 id="tocS_RoomDiagramVo">RoomDiagramVo</h2>

<a id="schemaroomdiagramvo"></a>
<a id="schema_RoomDiagramVo"></a>
<a id="tocSroomdiagramvo"></a>
<a id="tocsroomdiagramvo"></a>

```json
{
  "roomId": "string",
  "roomName": "string",
  "propertyType": "string",
  "rentArea": 0,
  "orientation": "string",
  "isSelfUse": true,
  "needCheckOut": true,
  "roomStatusName": "string",
  "roomStatus": 0,
  "tags": [
    "string"
  ],
  "houseTypeId": "string",
  "tablePrice": 0,
  "rentAreaType": 0,
  "emptyDays": 0,
  "selfUseSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isDirty": true,
  "isLock": true,
  "isMaintain": true,
  "bookingVo": {
    "customerName": "string",
    "companyName": "string",
    "bookingAmount": 0,
    "bookingTime": "2019-08-24T14:15:22Z",
    "canRefund": true,
    "roomStatus": "string"
  },
  "bookings": [
    {
      "customerName": "string",
      "companyName": "string",
      "bookingAmount": 0,
      "bookingTime": "2019-08-24T14:15:22Z",
      "canRefund": true,
      "roomStatus": "string"
    }
  ],
  "contractVo": {
    "roomId": "string",
    "contractId": "string",
    "contractNo": "string",
    "contractNumber": "string",
    "contractCategory": "string",
    "contractType": 0,
    "signType": 0,
    "rentPrice": 0,
    "tenantType": "string",
    "tenantName": "string",
    "tenantIdCard": "string",
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "rentTerm": "string",
    "status": 0,
    "roomStatus": "string",
    "approveStatus": 0
  },
  "contracts": [
    {
      "roomId": "string",
      "contractId": "string",
      "contractNo": "string",
      "contractNumber": "string",
      "contractCategory": "string",
      "contractType": 0,
      "signType": 0,
      "rentPrice": 0,
      "tenantType": "string",
      "tenantName": "string",
      "tenantIdCard": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "rentTerm": "string",
      "status": 0,
      "roomStatus": "string",
      "approveStatus": 0
    }
  ]
}

```

房间信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roomId|string|false|none|房间ID|房间ID|
|roomName|string|false|none|房间名称|房间名称|
|propertyType|string|false|none|用途|物业类型，二级字典|
|rentArea|number|false|none|计租面积|计租面积|
|orientation|string|false|none|朝向|朝向|
|isSelfUse|boolean|false|none|是否自用|是否自用（0否 1是）|
|needCheckOut|boolean|false|none|出场标识|出场标识，true表示需要出场|
|roomStatusName|string|false|none|状态|房间状态|
|roomStatus|integer(int32)|false|none|状态编码|房源状态(1.空置，2.在租，3.待生效/签约中/已预定， 4.不可招商)|
|tags|[string]|false|none|标识数组|房源标识列表，如：已预订、签约中、待生效、未进场、即将到期、未出场|
|» 标识数组|string|false|none|标识数组|房源标识列表，如：已预订、签约中、待生效、未进场、即将到期、未出场|
|houseTypeId|string|false|none|户型|户型ID|
|tablePrice|number|false|none|单价|表价|
|rentAreaType|integer(int32)|false|none|计租单位|计租面积类型（1建筑面积 2套内面积）|
|emptyDays|integer(int32)|false|none|空置天数|空置天数|
|selfUseSubject|integer(int32)|false|none|自用主体|自用主体（1运营 2商服 3众创 4其他）|
|rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|isDirty|boolean|false|none|是否脏房|是否脏房（0否 1是）|
|isLock|boolean|false|none|是否锁房|是否锁房（0否 1是）|
|isMaintain|boolean|false|none|是否维修|是否维修（0否 1是）|
|bookingVo|[BookDiagramVo](#schemabookdiagramvo)|false|none||该房间的订单信息列表|
|bookings|[[BookDiagramVo](#schemabookdiagramvo)]|false|none|订单信息列表|该房间的订单信息列表|
|contractVo|[ContractDiagramVo](#schemacontractdiagramvo)|false|none||该房间的合同信息列表|
|contracts|[[ContractDiagramVo](#schemacontractdiagramvo)]|false|none|合同信息列表|该房间的合同信息列表|

<h2 id="tocS_RoomQueryDTO">RoomQueryDTO</h2>

<a id="schemaroomquerydto"></a>
<a id="schema_RoomQueryDTO"></a>
<a id="tocSroomquerydto"></a>
<a id="tocsroomquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomId": "string",
  "status": 0,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true,
  "roomCode": "string",
  "remark": "string",
  "mergeSplitId": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "projectPriceId": "string",
  "tablePrice": 0,
  "bottomPrice": 0,
  "areaType": 0,
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": 0,
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": 0,
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": true,
  "selfUseSubject": 0,
  "selfUsePurpose": "string",
  "djBindRoomId": "string",
  "djBindName": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "priceFlag": "string",
  "parcelOrBuildingName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|roomName|string|false|none|房源名称|房源名称|
|type|integer(int32)|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|string|false|none|物业类型，二级字典|物业类型，二级字典|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|地块id|地块id|
|parcelName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋id|楼栋id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层id|楼层id|
|floorName|string|false|none|楼层名称|楼层名称|
|roomId|string|false|none|房间id， sys_room表的id|房间id， sys_room表的id|
|status|integer(int32)|false|none|状态（0草稿 10待生效 20生效中 30已失效 ）|状态（0草稿 10待生效 20生效中 30已失效 ）|
|isLock|boolean|false|none|是否锁房（0否 1是）|是否锁房（0否 1是）|
|isDirty|boolean|false|none|是否脏房（0否 1是）|是否脏房（0否 1是）|
|isMaintain|boolean|false|none|是否维修（0否 1是）|是否维修（0否 1是）|
|roomCode|string|false|none|合成编码|合成编码|
|remark|string|false|none|描述|描述|
|mergeSplitId|string|false|none|拆分合并id|拆分合并id|
|planEffectDate|string(date-time)|false|none|预计生效日期|预计生效日期|
|actualEffectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|projectPriceId|string|false|none|最新的立项定价id|最新的立项定价id|
|tablePrice|number|false|none|表价|表价|
|bottomPrice|number|false|none|底价|底价|
|areaType|integer(int32)|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|rentArea|number|false|none|计租面积|计租面积|
|storey|number|false|none|层高|层高|
|value|number|false|none|荷载值|荷载值|
|orientation|string|false|none|朝向|朝向|
|houseTypeId|string|false|none|户型id|户型id|
|smartWaterMeter|string|false|none|智能水电表|智能水电表|
|smartLock|string|false|none|智能锁|智能锁|
|assetOperationMode|string|false|none|资产运营模式（自持、可售、代招商）|资产运营模式（自持、可售、代招商）|
|assetOperationType|string|false|none|资产运营分类|资产运营分类|
|propertyStatus|string|false|none|产权情况|产权情况|
|paymentStatus|integer(int32)|false|none|交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|specialTag|string|false|none|特殊标签（1保障房）|特殊标签（1保障房）|
|latestRentPrice|number|false|none|商管承租成本单价（最新）|商管承租成本单价（最新）|
|latestRentStartDate|string(date-time)|false|none|商管承租起计日期（最新）|商管承租起计日期（最新）|
|latestRentEndDate|string(date-time)|false|none|商管承租到期日期（最新）|商管承租到期日期（最新）|
|firstRentPrice|number|false|none|商管承租成本单价第一次|商管承租成本单价第一次|
|firstRentStartDate|string(date-time)|false|none|商管承租起计日期第一次|商管承租起计日期第一次|
|firstRentEndDate|string(date-time)|false|none|商管承租到期日期第一次|商管承租到期日期第一次|
|operationSubject|integer(int32)|false|none|运营主体（1商服 2众创城）,字典|运营主体（1商服 2众创城）,字典|
|rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|isSelfUse|boolean|false|none|是否自用（0否 1是）|是否自用（0否 1是）|
|selfUseSubject|integer(int32)|false|none|自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|selfUsePurpose|string|false|none|自用用途|自用用途|
|djBindRoomId|string|false|none|多经绑定房源id|多经绑定房源id|
|djBindName|string|false|none|多经绑定房源名称|多经绑定房源名称|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|priceFlag|string|false|none|是否定价|是否定价: 1是 0否|
|parcelOrBuildingName|string|false|none|地块或楼栋名称|地块或楼栋名称|

<h2 id="tocS_RoomChangeRecordVo">RoomChangeRecordVo</h2>

<a id="schemaroomchangerecordvo"></a>
<a id="schema_RoomChangeRecordVo"></a>
<a id="tocSroomchangerecordvo"></a>
<a id="tocsroomchangerecordvo"></a>

```json
{
  "id": "string",
  "roomId": "string",
  "changeType": 0,
  "mergeSplitId": "string",
  "mergeSplitType": "string",
  "changeTime": "2019-08-24T14:15:22Z",
  "changeContent": "string",
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|roomId|string|false|none|房源ID|房源ID|
|changeType|integer(int32)|false|none|变更类型（1生效 2信息变更 3拆分 4合并 5作废）|变更类型（1生效 2信息变更 3拆分 4合并 5作废）|
|mergeSplitId|string|false|none|拆分合并表ID，仅当变更类型为拆分或合并时有效|拆分合并表ID，仅当变更类型为拆分或合并时有效|
|mergeSplitType|string|false|none|拆分合并类型（source target）|拆分合并类型（source target）|
|changeTime|string(date-time)|false|none|变更时间|变更时间|
|changeContent|string|false|none|变更内容|变更内容|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

