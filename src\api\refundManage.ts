import http from './index'

// 财务退款新增/修改DTO
export interface FinancialRefundAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  projectId?: string
  refundType?: number // 退款类型：0-退租退款、1-退定退款、2-未明流水退款
  refundNo?: string
  bizId?: string
  refundTarget?: string
  applyTime?: string
  refundAmount?: number
  recordAmount?: number // 已记账金额
  feeType?: string
  refundWay?: number // 退款方式: 0-原路退回、1-银行转账
  receiverName?: string
  receiverBank?: string
  receiverAccount?: string
  refundRemark?: string
  refundTime?: string
  refundStatus?: number // 退款单状态:0-草稿、1-待退款、2-已退款、3-作废
  approveStatus?: number // 审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回
  approveTime?: string
  recordStatus?: number // 记账状态: 0-未记账,1-已记账
  confirmStatus?: number // 确认状态: 0-待确认, 1-已确认
  roomYardStatus?: number // 一房一码退款状态
  attachments?: string
  cancelTime?: string
  cancelBy?: string
  cancelByName?: string
  cancelReason?: string
  isDel?: boolean
  isSubmit?: number // 0-暂存,1-提交
  refundId?: string // 退款单ID
  isThirdAgent?: boolean // 是否第三方代收: 0-否,1-是
  agentAttachments?: string // 退款申请单
  agentName?: string // 第三方代收名称
  agentAccount?: string // 第三方代收账号
  agentBank?: string // 第三方代收开户行
}

// 财务退款查询DTO
export interface FinancialRefundQueryDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  id?: string
  projectId?: string
  refundType?: number // 退款类型：0-退租退款、1-退定退款、2-未明流水退款
  refundNo?: string
  refundTypes?: string // 退款类型（多选逗号拼接）
  bizId?: string
  refundTarget?: string
  applyTime?: string
  refundAmount?: number
  recordAmount?: number // 已记账金额
  feeType?: string
  refundWay?: number // 退款方式: 0-原路退回、1-银行转账
  receiverName?: string
  receiverBank?: string
  receiverAccount?: string
  refundRemark?: string
  refundTime?: string
  refundStatus?: number // 退款单状态:0-草稿、1-待退款、2-已退款、3-作废
  refundStatuses?: string // 退款单状态（多选逗号拼接）
  approveStatus?: number // 审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回
  recordStatus?: number // 记账状态: 0-未记账,1-已记账
  confirmStatus?: number // 确认状态: 0-待确认, 1-已确认
  roomYardStatus?: number // 一房一码退款状态
  attachments?: string
  cancelTime?: string
  cancelBy?: string
  cancelByName?: string
  cancelReason?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  isDel?: boolean
  applyTimeStart?: string
  applyTimeEnd?: string
  isThirdAgent?: boolean // 是否第三方代收: 0-否,1-是
  agentAttachments?: string // 退款申请单
  agentName?: string // 第三方代收名称
  agentAccount?: string // 第三方代收账号
  agentBank?: string // 第三方代收开户行
}

// 财务退款VO
export interface FinancialRefundVo {
  id?: string
  projectId?: string
  projectName?: string
  refundType?: number // 退款类型：0-退租退款、1-退定退款、2-未明流水退款
  refundNo?: string
  bizId?: string
  refundTarget?: string
  applyTime?: string
  refundAmount?: number
  recordAmount?: number // 已记账金额
  feeType?: string
  refundWay?: number // 退款方式: 0-原路退回、1-银行转账
  receiverName?: string
  receiverBank?: string
  receiverAccount?: string
  refundRemark?: string
  refundTime?: string
  refundStatus?: number // 退款单状态:0-草稿、1-待退款、2-已退款、3-作废
  approveStatus?: number // 审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回
  approveTime?: string
  recordStatus?: number // 记账状态: 0-未记账,1-已记账
  confirmStatus?: number // 确认状态: 0-待确认, 1-已确认
  roomYardStatus?: number // 一房一码退款状态
  attachments?: string
  cancelTime?: string
  cancelBy?: string
  cancelByName?: string
  cancelReason?: string
  createByName?: string
  updateByName?: string
  isDel?: boolean
  isThirdAgent?: boolean // 是否第三方代收: 0-否,1-是
  agentAttachments?: string // 退款申请单
  agentName?: string // 第三方代收名称
  agentAccount?: string // 第三方代收账号
  agentBank?: string // 第三方代收开户行
}

// 合同终止信息VO
export interface ContractTerminateVo {
  id?: string
  contractId?: string
  unionId?: string
  approveStatus?: number // 审核状态:0-待审核,1-审核中,2-审核通过,3-审核拒绝
  isExit?: boolean // 是否同时办理出场:0-否,1-是
  isPart?: boolean // 是否部分提前退租:0-否,1-是
  bondReceivedAmount?: number // 已收保证金（元）
  rentReceivedAmount?: number // 已收租金（元）
  rentOverdueAmount?: number // 逾期租金（元）
  receivedPeriod?: string // 已收账期文字描述
  overduePeriod?: string // 逾期账期文字描述
  terminateType?: number // 退租类型:0-到期退租,1-提前退租
  terminateDate?: string
  terminateReason?: string // 退租原因,字典逗号拼接
  otherReasonDesc?: string // 其他原因说明
  hasOtherDeduction?: boolean // 是否有其他扣款
  otherDeductionDesc?: string // 其他扣款描述
  terminateRemark?: string // 退租说明
  terminateAttachments?: string // 退租附件
  signAttachments?: string // 签署附件
  createByName?: string
  updateByName?: string
  isDel?: boolean
  isThirdAgent?: boolean // 是否第三方代收: 0-否,1-是
  agentAttachments?: string // 退款申请单
  agentName?: string // 第三方代收名称
  agentAccount?: string // 第三方代收账号
  agentBank?: string // 第三方代收开户行
}

// 订单信息VO
export interface BookingVo {
  id?: string
  projectId?: string
  projectName?: string
  customerName?: string
  propertyType?: string
  roomId?: string
  roomName?: string
  bookingNo?: string
  bookingAmount?: number
  unpaidAmount?: number // 待收金额
  receivableDate?: string
  expectSignDate?: string
  isRefundable?: number // 定单金额是否可退:0-否,1-是
  cancelTime?: string
  cancelBy?: string
  cancelByName?: string
  cancelReason?: number // 作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废
  isRefund?: number // 是否退款:0-否,1-是
  cancelEnclosure?: string // 退定附件
  cancelRemark?: string // 退定说明
  status?: number // 状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废
  contractId?: string
  refundId?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  isDel?: boolean
  contractNo?: string
  signDate?: string
  contractLeaseUnit?: string
  lesseeName?: string
  receivedAmount?: number // 定单已收金额
  receivedDate?: string // 实收日期
  payMethod?: string
  isThirdAgent?: boolean // 是否第三方代收: 0-否,1-是
  agentAttachments?: string // 退款申请单
  agentName?: string // 第三方代收名称
  agentAccount?: string // 第三方代收账号
  agentBank?: string // 第三方代收开户行
}

// 财务流水信息VO
export interface FinancialFlowVo {
  id?: string
  projectId?: string
  orgName?: string // 组织名称(线下支付使用)
  projectName?: string
  orderNo?: string
  payDirection?: number // 支付方向: 0-收入, 1-支出
  payType?: number
  payMethod?: string
  targetType?: number
  target?: string
  targetId?: string
  entryTime?: string
  status?: number // 状态: 0-未记账, 1-部分记账, 2-已记账
  amount?: number
  usedAmount?: number
  payerName?: string
  payerPhone?: string
  payerAccount?: string
  payerCard?: string // 支付人身份证/统一社会信用代码
  payRemark?: string
  merchant?: string
  payChannel?: string
  sourceNo?: string // 退款流水的原单号
  isOtherIncome?: boolean // 是否是其他收入: 0-否,1-是
  otherIncomeDesc?: string // 其他收入说明
  createByName?: string
  createTime?: string
  updateByName?: string
  updateTime?: string
  isDel?: boolean
  bizId?: string // 业务id: 根据退款类型退租申请单id,定单id,流水id
  bizType?: number // 业务类型: 1-退租申请单id,2-定单id,3-流水id
  refundId?: string // 退款单ID
  isThirdAgent?: boolean // 是否第三方代收: 0-否,1-是
  agentAttachments?: string // 退款申请单
  agentName?: string // 第三方代收名称
  agentAccount?: string // 第三方代收账号
  agentBank?: string // 第三方代收开户行
}

// 记账记录DTO
export interface CostFlowRelAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  costId?: string // 账单id
  refundId?: string // 退款单id
  flowId?: string // 流水id
  flowNo?: string // 流水单号
  type?: number // 账单类型：1-收款 2-转入 3-转出 4-退款
  confirmStatus?: number // 确认状态: 0-未确认、1-自动确认、2-手动确认
  confirmTime?: string
  confirmUserId?: string
  confirmUserName?: string
  payAmount?: number // 支付金额
  pendingAmount?: number // 账单待收金额
  acctAmount?: number // 本次记账金额
  remark?: string
  isDel?: boolean
  isThirdAgent?: boolean // 是否第三方代收: 0-否,1-是
  agentAttachments?: string // 退款申请单
  agentName?: string // 第三方代收名称
  agentAccount?: string // 第三方代收账号
  agentBank?: string // 第三方代收开户行  
}

// 账单流水关系VO
export interface CostFlowRelVo {
  id?: string
  costId?: string // 账单id
  refundId?: string // 退款单id
  flowId?: string // 流水id
  flowNo?: string // 流水单号
  type?: number // 账单类型：1-收款 2-转入 3-转出 4-退款
  confirmStatus?: number // 确认状态: 0-未确认、1-自动确认、2-手动确认
  confirmTime?: string
  confirmUserId?: string
  confirmUserName?: string
  payAmount?: number // 支付金额
  pendingAmount?: number // 账单待收金额
  acctAmount?: number // 本次记账金额
  remark?: string
  createByName?: string
  createTime?: string
  updateByName?: string
  isDel?: boolean
  projectId?: string
  projectName?: string
  entryTime?: string
  payType?: number
  payMethod?: string
  orderNo?: string
  usedAmount?: number
  payerName?: string
  target?: string
  merchant?: string
  cumulativeAcctAmount?: number // 累计记账金额
  isThirdAgent?: boolean // 是否第三方代收: 0-否,1-是
  agentAttachments?: string // 退款申请单
  agentName?: string // 第三方代收名称
  agentAccount?: string // 第三方代收账号
  agentBank?: string // 第三方代收开户行  
}

// 出场管理信息VO
export interface ExitManageVo {
  exitInfo?: {
    id?: string
    projectId?: string
    contractId?: string
    contractNo?: string
    contractUnionId?: string
    terminateId?: string
    refundId?: string
    customerId?: string
    customerName?: string
    processType?: number
    progressStatus?: number
    isDiscount?: boolean
    discountAmount?: number
    discountReason?: string
    finalAmount?: number
    refundProcessType?: number
    payeeName?: string
    payeeAccount?: string
    bankName?: string
    licenseStatus?: number
    taxCertStatus?: number
    refundApplyType?: number
    signType?: number
    signAttachments?: string
    signTime?: string
    copyTime?: string
    copyBy?: string
    copyByName?: string
    settleTime?: string
    settleBy?: string
    settleByName?: string
    createByName?: string
    updateByName?: string
    isDel?: boolean
    contractPurpose?: string
    terminateType?: number
    terminateDate?: string
    terminateRoomName?: string
    terminateRoomCount?: number
    engineeringCount?: number
    financeCount?: number
    refundStatus?: number
  }
  contractTerminateInfo?: ContractTerminateVo
  exitRoomList?: Array<{
    id?: string
    exitId?: string
    roomId?: string
    roomName?: string
    propertyType?: number
    parcelName?: string
    buildingName?: string
    exitDate?: string
    rentControl?: number
    doorWindowStatus?: number
    doorWindowPenalty?: number
    keyHandoverStatus?: number
    keyPenalty?: number
    cleaningStatus?: number
    cleaningPenalty?: number
    elecMeterReading?: number
    coldWaterReading?: number
    hotWaterReading?: number
    elecFee?: number
    waterFee?: number
    pmFee?: number
    roomPhotos?: string
    assetsSituation?: string
    remark?: string
    isBusinessConfirmed?: boolean
    businessConfirmBy?: string
    businessConfirmByName?: string
    businessConfirmTime?: string
    isFinanceConfirmed?: boolean
    financeConfirmBy?: string
    financeConfirmByName?: string
    financeConfirmTime?: string
    financeConfirmSignature?: string
    isEngineeringConfirmed?: boolean
    engineeringConfirmBy?: string
    engineeringConfirmByName?: string
    engineeringConfirmTime?: string
    engineeringConfirmSignature?: string
    createByName?: string
    updateByName?: string
    isDel?: boolean
    exitRoomAssetsList?: any[]
  }>
  exitCostList?: Array<{
    id?: string
    exitId?: string
    costId?: string
    startDate?: string
    endDate?: string
    payType?: number
    subjectId?: string
    subjectName?: string
    receivableDate?: string
    amount?: number
    type?: number
    remark?: string
    createByName?: string
    updateByName?: string
    isDel?: boolean
  }>
}

// 财务退款详情VO
export interface FinancialRefundDetailVo {
  refund?: FinancialRefundVo
  terminate?: ContractTerminateVo // 退租申请单信息，退款类型为0时有值
  booking?: BookingVo // 定单基本信息
  flow?: FinancialFlowVo // 未明流水信息，退款类型为2时有值
  exit?: ExitManageVo // 出场管理信息，退款类型为0时有值
  flowRels?: CostFlowRelVo[] // 退款流水信息列表
  flowLogList?: any[] // 流水日志列表
}

// 退款记账DTO
export interface RefundSaveRecordDTO {
  refundId?: string // 退款ID
  flowRelList?: CostFlowRelAddDTO[] // 记账流水列表
}

// 退款确认记账DTO
export interface RefundConfirmRecordDTO {
  refundId?: string // 退款单ID
  isOneKeyConfirm?: boolean // 是否一键确认
  flowRelId?: string // 记账记录ID（单条确认时使用）
}

// 作废退款单DTO
export interface FinancialFlowCancelDTO {
  refundId: string // 退款单id
  cancelReason?: string // 作废原因
}

/**
 * 查询财务退款列表
 * @param params 查询参数
 * @returns Promise<FinancialRefundVo[]>
 */
export function getRefundList(params: FinancialRefundQueryDTO) {
  return http.post<FinancialRefundVo[]>('/business-rent-admin/refund/list', params)
}

/**
 * 查询财务退款列表（新接口）
 * @param params 查询参数
 * @returns Promise<FinancialRefundVo[]>
 */
export function getFinancialRefundList(params: FinancialRefundQueryDTO) {
  return http.post<FinancialRefundVo[]>('/business-rent-admin/financialRefund/list', params)
}

/**
 * 获取财务退款详细信息
 * @param refundId 财务退款ID
 * @returns Promise<FinancialRefundVo>
 */
export function getRefundDetail(refundId: string) {
  return http.get<FinancialRefundVo>('/business-rent-admin/refund/detail', { refundId })
}

/**
 * 获取退款单详情接口
 * @param refundId 退款单ID
 * @param refundType 退款类型
 * @param bizId 业务ID
 * @returns Promise<FinancialRefundDetailVo>
 */
export function getFinancialRefundDetail(refundId?: string, refundType?: number, bizId?: string) {
  return http.get<FinancialRefundDetailVo>('/business-rent-admin/financialRefund/detail', {
    refundId,
    refundType,
    bizId
  })
}

/**
 * 新增财务退款
 * @param data 财务退款数据
 * @returns Promise<any>
 */
export function addRefund(data: FinancialRefundAddDTO) {
  return http.post('/business-rent-admin/refund', data)
}

/**
 * 修改财务退款
 * @param data 财务退款数据
 * @returns Promise<any>
 */
export function updateRefund(data: FinancialRefundAddDTO) {
  return http.put('/business-rent-admin/refund', data)
}

/**
 * 保存退款单接口
 * @param data 财务退款数据
 * @returns Promise<any>
 */
export function saveFinancialRefund(data: FinancialRefundAddDTO) {
  return http.post('/business-rent-admin/financialRefund/save', data)
}

/**
 * 退款记账接口
 * @param data 记账数据
 * @returns Promise<any>
 */
export function saveRefundRecord(data: RefundSaveRecordDTO) {
  return http.post('/business-rent-admin/financialRefund/saveRecord', data)
}

/**
 * 退款确认记账接口
 * @param data 确认记账数据
 * @returns Promise<any>
 */
export function confirmRefundRecord(data: RefundConfirmRecordDTO) {
  return http.post('/business-rent-admin/financialRefund/confirmRecord', data)
}

/**
 * 删除财务退款
 * @param ids 财务退款ID列表
 * @returns Promise<any>
 */
export function deleteRefund(ids: string[]) {
  return http.delete('/business-rent-admin/refund/delete', { ids })
}

/**
 * 删除退款单接口
 * @param refundId 退款单ID
 * @returns Promise<any>
 */
export function deleteFinancialRefund(refundId: string) {
  return http.delete('/business-rent-admin/financialRefund/delete', { id: refundId })
}

/**
 * 作废退款单接口
 * @param data 作废数据
 * @returns Promise<any>
 */
export function cancelFinancialRefund(data: FinancialFlowCancelDTO) {
  return http.post('/business-rent-admin/financialRefund/cancel', data)
}

/**
 * 取消退款记账接口
 * @param flowRelId 退款流水关联id
 * @returns Promise<any>
 */
export function cancelRefundRecord(flowRelId: string) {
  return http.post('/business-rent-admin/financialRefund/cancelRecord', {
    id: flowRelId,
  })
}

/**
 * 导出财务退款列表
 * @param params 导出参数
 * @returns Promise<any>
 */
export function exportRefundList(params: FinancialRefundQueryDTO) {
  return http.download('/business-rent-admin/refund/export', params)
}

/**
 * 导出财务退款列表（新接口）
 * @param params 导出参数
 * @returns Promise<any>
 */
export function exportFinancialRefundList(params: FinancialRefundQueryDTO) {
  return http.download('/business-rent-admin/financialRefund/export', params)
} 

/**
 * 下载退款申请单-代收模板
 * @returns Promise<any>
 */
export function downloadTemplate() {
  return http.get('/business-rent-admin/financialRefund/downloadTemplate')
}
