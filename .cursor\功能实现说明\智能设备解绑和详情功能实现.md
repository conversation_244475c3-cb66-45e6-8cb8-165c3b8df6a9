# 智能设备解绑和详情功能实现

## 需求描述
在 `src/views/smartDevices/index.vue` 页面中实现：
1. 操作列的解绑和详情按钮功能
2. 解绑和批量解绑都调用 `unbindRoomDevice` 接口，需要二次确认提示
3. 点击详情打开详情抽屉，显示设备信息和密码记录

## 实现方案

### 1. 新增组件
创建了 `src/views/smartDevices/components/DeviceDetailDrawer.vue` 详情抽屉组件。

### 2. 主要功能实现

#### 2.1 导入相关依赖
```typescript
import { Message, Modal } from '@arco-design/web-vue'
import DeviceDetailDrawer from './components/DeviceDetailDrawer.vue'
import { unbindRoomDevice } from '@/api/smartLock'
```

#### 2.2 添加详情抽屉状态管理
```typescript
// 子组件引用
const deviceDetailDrawerRef = ref()

// 详情抽屉状态
const detailVisible = ref(false)
```

#### 2.3 批量解绑功能
```typescript
const handleUnbindDevice = () => {
  if (selectedKeys.value.length === 0) {
    Message.warning('请选择要解绑的设备')
    return
  }

  if (activeTab.value !== 'door-lock') {
    Message.warning('批量解绑功能仅支持智能门锁')
    return
  }

  Modal.confirm({
    title: '确认解绑',
    content: `确定要解绑选中的 ${selectedKeys.value.length} 个设备吗？解绑后将无法恢复。`,
    onOk: async () => {
      try {
        await unbindRoomDevice(selectedKeys.value)
        Message.success('解绑成功')
        selectedKeys.value = []
        loadData()
      } catch (error) {
        console.error('解绑失败:', error)
      }
    }
  })
}
```

#### 2.4 单个设备解绑功能
```typescript
const handleEditDevice = (record: any) => {
  if (activeTab.value !== 'door-lock') {
    Message.warning('解绑功能仅支持智能门锁')
    return
  }

  Modal.confirm({
    title: '确认解绑',
    content: `确定要解绑设备"${record.roomName}"吗？解绑后将无法恢复。`,
    onOk: async () => {
      try {
        await unbindRoomDevice([record.roomId])
        Message.success('解绑成功')
        loadData()
      } catch (error) {
        console.error('解绑失败:', error)
      }
    }
  })
}
```

#### 2.5 查看详情功能
```typescript
const handleViewDetail = async (record: any) => {
  if (activeTab.value !== 'door-lock') {
    Message.warning('详情功能仅支持智能门锁')
    return
  }

  detailVisible.value = true
  await nextTick()
  deviceDetailDrawerRef.value?.show(record)
}

const handleDetailCancel = () => {
  detailVisible.value = false
}
```

### 3. 详情抽屉组件 (DeviceDetailDrawer.vue)

#### 3.1 主要功能
- **基本信息展示**：房源名称、地块、楼栋、物业类型、设备品牌、绑定日期
- **设备信息展示**：锁名称、锁别名、锁ID、设备ID、MAC地址、电量
- **密码记录管理**：显示操作记录表格，支持时间筛选和分页
- **临时密码生成**：调用生成临时密码接口

#### 3.2 核心方法
```typescript
// 显示抽屉
const show = async (record: TTLockDeviceVo) => {
    visible.value = true
    loading.value = true
    
    try {
        const response = await getTTLockDetail(record.id)
        if (response && response.data) {
            deviceDetail.value = { ...record, ...response.data }
        } else {
            deviceDetail.value = record
        }
        await loadOperateLog()
    } catch (error) {
        console.error('获取设备详情失败:', error)
        deviceDetail.value = record
    } finally {
        loading.value = false
    }
}

// 加载操作记录
const loadOperateLog = async () => {
    if (!deviceDetail.value) return
    
    logLoading.value = true
    try {
        const params = {
            pageNum: logPagination.current,
            pageSize: logPagination.pageSize,
            deviceId: deviceDetail.value.id,
            startDate: dateRange.value[0] || undefined,
            endDate: dateRange.value[1] || undefined
        }
        
        const response = await getLockOperateLog(params)
        if (response && response.rows) {
            operateLogList.value = response.rows
            logPagination.total = response.total
        }
    } catch (error) {
        console.error('获取操作记录失败:', error)
    } finally {
        logLoading.value = false
    }
}

// 生成临时密码
const handleGeneratePassword = async () => {
    if (!deviceDetail.value) return
    
    try {
        await generateTempPassword(deviceDetail.value.id)
        Message.success('临时密码生成成功')
        await loadOperateLog()
    } catch (error) {
        console.error('生成临时密码失败:', error)
    }
}
```

### 4. 数据结构调整

#### 4.1 修改表格行键
将 `DoorLockTab.vue` 中的 `row-key` 从 `"id"` 改为 `"roomId"`，确保批量解绑时传递正确的房间ID。

#### 4.2 API字段映射
根据 `TTLockDeviceVo` 和 `TTLockOperateLogVo` 接口定义，调整了详情抽屉中的字段映射：
- 操作记录中的密码类型字段：`passwordType` → `type`
- 操作记录中的时间字段：`validPeriod` → `startDate` 和 `endDate`
- 查询参数中的时间字段：`startTime/endTime` → `startDate/endDate`

### 5. 用户体验优化

#### 5.1 二次确认
- 单个解绑和批量解绑都使用 `Modal.confirm` 进行二次确认
- 确认对话框显示具体的设备名称和数量

#### 5.2 功能限制
- 解绑和详情功能仅支持智能门锁标签页
- 在智能水电标签页时会显示相应的提示信息

#### 5.3 数据刷新
- 解绑成功后自动刷新列表数据
- 清空已选中的设备列表

### 6. 技术实现要点

1. **组件通信**：使用 `v-if` 控制详情抽屉的渲染，通过 `ref` 调用子组件方法
2. **API调用**：正确使用 `unbindRoomDevice` 接口，传递房间ID数组
3. **错误处理**：所有API调用都包含错误处理逻辑
4. **类型安全**：使用TypeScript类型定义确保数据结构正确
5. **用户反馈**：提供清晰的成功/失败消息提示

## 功能特点

- ✅ 支持单个设备解绑和批量解绑
- ✅ 二次确认防止误操作
- ✅ 详情抽屉展示完整设备信息
- ✅ 密码记录查询和临时密码生成
- ✅ 时间筛选和分页功能
- ✅ 响应式设计和加载状态管理
- ✅ 完整的错误处理和用户反馈
