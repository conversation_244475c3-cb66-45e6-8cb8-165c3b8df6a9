
// 内存监控脚本
setInterval(() => {
    const mem = process.memoryUsage();
    const heapUsed = Math.round(mem.heapUsed / 1024 / 1024);
    const heapTotal = Math.round(mem.heapTotal / 1024 / 1024);
    const external = Math.round(mem.external / 1024 / 1024);
    
    console.log(`📊 内存: 堆 ${heapUsed}/${heapTotal}MB, 外部 ${external}MB`);
    
    // 内存使用超过阈值时触发垃圾回收
    if (heapUsed > 2048 && global.gc) {
        console.log('🗑️  触发垃圾回收...');
        global.gc();
    }
}, 10000);
