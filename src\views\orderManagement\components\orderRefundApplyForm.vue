<template>
    <a-drawer v-model:visible="visible" title="退款申请单" width="800px" :mask-closable="true" @cancel="handleCancel"
        class="common-drawer">
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" status="success" @click="handleSave">暂存</a-button>
                <a-button type="primary" @click="handleSubmit">发起审批</a-button>
                <!-- <a-button type="primary" @click="handlePrint">预览&打印</a-button> -->
            </a-space>
        </template>

        <a-form ref="formRef" :model="formData" :label-col-props="{ span: 9 }" :wrapper-col-props="{ span: 15 }"
            :rules="rules">
            <!-- <a-divider>退款申请信息</a-divider> -->
            <section-title title="退款申请信息" style="margin-bottom: 16px;" />
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item field="refundNo" label="退款单号">
                        <a-input v-model="formData.refundNo" disabled />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item field="refundType" label="退款类型">
                        <a-select v-model="formData.refundType" disabled>
                            <a-option :value="1">退定退款</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>

                <a-col :span="8">
                    <a-form-item field="applyTime" label="退款申请日期" required>
                        <a-date-picker v-model="formData.applyTime" style="width: 100%" />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item field="refundAmount" label="退款金额" required>
                        <a-input-number v-model="formData.refundAmount" :precision="2" :min="0"
                            :max="formData.bookingAmount" style="width: 100%" placeholder="请输入退款金额">
                            <template #append>元</template>
                        </a-input-number>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item field="feeType" label="退款费用类型">
                        <a-input v-model="formData.feeType" disabled value="定金" />
                    </a-form-item>
                </a-col>
                <!-- </a-row> -->

                <!-- <a-row :gutter="16"> -->

                <a-col :span="8">
                    <a-form-item field="" label="" :label-col-props="{ span: 9 }" :wrapper-col-props="{ span: 15 }">
                        <!-- <a-date-picker v-model="formData.applyTime" style="width: 100%" show-time /> -->
                    </a-form-item>
                </a-col>

                <a-col :span="8">
                    <a-form-item field="isThirdAgent" label="是否第三方代收" required>
                        <a-radio-group v-model="formData.isThirdAgent" @change="handleIsThirdAgentChange">
                            <a-radio :value="true">是</a-radio>
                            <a-radio :value="false">否</a-radio>
                        </a-radio-group>
                    </a-form-item>
                </a-col>

                <a-col :span="8">
                    <a-form-item field="refundWay" label="退款方式">
                        <a-select v-model="formData.refundWay" disabled>
                            <a-option :value="0">原路退回</a-option>
                            <a-option :value="1">银行转账</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item field="" label="" :label-col-props="{ span: 9 }" :wrapper-col-props="{ span: 15 }">
                        <!-- <a-date-picker v-model="formData.applyTime" style="width: 100%" show-time /> -->
                    </a-form-item>
                </a-col>


                <!-- </a-row>

            <a-row :gutter="16"> -->
                <a-col :span="8">
                    <a-form-item field="receiverName" label="收款方姓名" :rules="{ required: true, message: '请输入收款方姓名' }">
                        <a-input v-model="formData.receiverName" placeholder="请输入收款方姓名" />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item field="receiverBank" label="收款方开户行" :rules="{ required: true, message: '请输入收款方开户行' }">
                        <a-input v-model="formData.receiverBank" placeholder="请输入收款方开户行" />
                    </a-form-item>
                </a-col>
                <!-- </a-row> -->

                <!-- <a-row :gutter="16"> -->
                <a-col :span="8">
                    <a-form-item field="receiverAccount" label="收款方银行账号"
                        :rules="{ required: true, message: '请输入收款方银行账号' }">
                        <a-input v-model="formData.receiverAccount" placeholder="请输入收款方银行账号" />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item field="refundRemark" label="退款申请说明" :label-col-props="{ span: 3 }"
                        :wrapper-col-props="{ span: 21 }">
                        <a-textarea v-model="formData.refundRemark" placeholder="请输入退款申请说明"
                            :auto-size="{ minRows: 3, maxRows: 5 }" />
                    </a-form-item>
                </a-col>
            </a-row>

            <div v-if="formData.isThirdAgent">
                <a-row :gutter="16">
                    <a-col :span="24">
                        <a-form-item label="退款申请单-代收" :label-col-props="{ span: 3 }" :wrapper-col-props="{ span: 21 }">
                            <div class="tip-text" style="margin-bottom: 8px;">请下载《退款申请单-代收》模板空字上传</div>
                            <a-button type="text" @click="handleAgentAttachmentsDownload"
                                style="margin-bottom: 10px;">《退款申请单-代收》模板下载</a-button>
                            <upload-file v-model="formData.agentAttachments" :limit="5" :max-size="10" />
                        </a-form-item>
                    </a-col>
                </a-row>
            </div>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item label="附件" :label-col-props="{ span: 3 }" :wrapper-col-props="{ span: 21 }">
                        <upload-file v-model="formData.attachments" :limit="5" :max-size="10" />
                    </a-form-item>
                </a-col>
            </a-row>



            <section-title title="定单信息" style="margin-bottom: 16px;" />

            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item field="customerName" label="客户名称">
                        <a-input v-model="formData.customerName" disabled />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item field="intentRoom" label="意向房源">
                        <a-input v-model="formData.intentRoom" disabled />
                    </a-form-item>
                </a-col>
                <!-- </a-row>

            <a-row :gutter="16"> -->
                <a-col :span="8">
                    <a-form-item field="bookingAmount" label="定金应收金额">
                        <a-input-number v-model="formData.bookingAmount" disabled style="width: 100%">
                            <template #append>元</template>
                        </a-input-number>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item field="receivedAmount" label="定金已收金额">
                        <a-input-number v-model="formData.receivedAmount" disabled style="width: 100%">
                            <template #append>元</template>
                        </a-input-number>
                    </a-form-item>
                </a-col>
                <!-- </a-row>

            <a-row :gutter="16"> -->
                <a-col :span="8">
                    <a-form-item field="receivableDate" label="缴纳日期">
                        <a-input v-model="formData.receivableDate" disabled />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item field="payMethod" label="收款方式">
                        <a-input v-model="formData.payMethod" disabled />
                    </a-form-item>
                </a-col>
                <!-- </a-row> -->
                <!-- <a-row :gutter="16"> -->
                <a-col :span="8">
                    <a-form-item field="isRefundDeposit" label="是否退定金">
                        <a-radio-group v-model="formData.isRefundDeposit" disabled>
                            <a-radio value="1">是</a-radio>
                            <a-radio value="0">否</a-radio>
                        </a-radio-group>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item field="refundDepositRemark" label="退定说明" :label-col-props="{ span: 3 }"
                        :wrapper-col-props="{ span: 21 }">
                        <a-textarea v-model="formData.refundDepositRemark" placeholder="请输入退定说明"
                            :auto-size="{ minRows: 3, maxRows: 5 }" disabled />
                    </a-form-item>
                </a-col>
            </a-row>


            <!-- 退款流水信息 -->
            <section-title title="退款流水信息" style="margin-bottom: 16px;" />
            <a-table :data="flowLogList" :columns="flowLogColumns" :pagination="false" :bordered="{ cell: true }">
                <template #type="{ record }">
                    <span>
                        <!-- 类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转 -->
                        {{ getTypeText(record.type) }}
                    </span>
                </template>
                <template #payTime="{ record }">
                    {{ record.payTime }}
                </template>
                <template #amount="{ record }">
                    {{ formatMoney(record.amount) }}
                </template>
                <template #operation="{ record }">
                    <!-- <a-button type="text" @click="handleCancelRecord(record)" v-if="!isViewMode">取消记账</a-button> -->
                </template>
            </a-table>
        </a-form>
    </a-drawer>

    <!-- Word文档预览组件 -->
    <WordViewer v-model="previewVisible" :file-url="previewFileUrl" :title="previewTitle" />
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import UploadFile from '@/components/upload/uploadFile.vue'
import WordViewer from '@/components/WordViewer/index.vue'
import {
    saveFinancialRefund,
    getFinancialRefundDetail,
    downloadTemplate,
    type FinancialRefundAddDTO
} from '@/api/refundManage'
import { printBooking, type BookingPrintDto } from '@/api/orderManagement'
import type { TableColumnData } from '@arco-design/web-vue'
import dayjs from 'dayjs'

interface Emits {
    (e: 'success'): void
    (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

const visible = ref(false)
const formRef = ref()
const loading = ref(false)
const isViewMode = ref(false)

const formData = reactive({
    applyTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    refundNo: '',
    bookingNo: '', // 订单号
    id: '',
    refundId: '', // 退款单ID
    projectId: '',
    refundType: 1, // 退定退款
    bizId: '', // 订单ID
    refundTarget: '', // 退款对象：客户名称 + 房源
    refundAmount: 0,
    feeType: '定金',
    refundWay: 0, // 默认原路退回
    receiverName: '',
    receiverBank: '',
    receiverAccount: '',
    refundRemark: '',
    attachments: '',
    refundStatus: 0, // 草稿状态
    approveStatus: 0, // 草稿状态
    isSubmit: false, // 是否提交
    // 订单信息（只读）
    customerName: '',
    intentRoom: '',
    depositAmount: 0,
    paidAmount: 0,
    paymentDate: '',
    payMethod: '',
    isRefundDeposit: '1',
    refundDepositRemark: '',
    // 新增字段
    bookingAmount: 0,
    receivedAmount: 0,
    receivableDate: '',
    agentAttachments: '',
    isThirdAgent: false as boolean,
    agentName: '',
    agentAccount: '',
    agentBank: ''
})

// 表单验证规则
const rules = {
    refundAmount: [
        { required: true, message: '请输入退款金额' },
        {
            validator: (value: number, callback: (error?: string) => void) => {
                if (value <= 0) {
                    callback('退款金额必须大于0')
                } else if (value > formData.bookingAmount) {
                    callback('退款金额不能超过定单实际收款金额')
                } else {
                    callback()
                }
            }
        }
    ],
    receiverName: [
        { required: true, message: '请输入收款人姓名' }
    ],
}

// 打开抽屉
const open = async (options?: { record?: any; mode?: 'create' | 'edit' | 'view' }) => {
    try {
        // 设置模式
        isViewMode.value = options?.mode === 'view';

        console.log('options', options)
        console.log('options?.record', options?.record)
        // 先查询是否已有该订单的退款申请记录
        const refundDetailRes = await getFinancialRefundDetail(options?.record?.refundId, 1, options?.record?.id)

        if (refundDetailRes.code === 200) {
            // 已存在退款记录，显示现有信息
            visible.value = true
            await initFormDataFromRefund(refundDetailRes.data)
        } else {
            // Message.error('获取定单详情失败')
        }
    } catch (error) {
        console.error('获取退款申请信息失败:', error)
        // Message.error('获取退款申请信息失败')
    }
}

// 初始化表单数据
// const initFormData = (data: any) => {
//     Object.assign(formData, {
//         projectId: data.projectId || '',
//         bizId: data.bizId || '',
//         customerName: data.customerName || '',
//         intentRoom: data.roomName || data.intentRoom || '',
//         bookingAmount: data.bookingAmount || 0,
//         receivedAmount: data.receivedAmount || 0,
//         receivableDate: data.receivableDate || '',
//         refundAmount: data.bookingAmount || 0,
//         refundWay: 0,
//         refundTarget: `${data.customerName || ''} ${data.roomName || data.intentRoom || ''}`.trim(),
//         receiverName: data.lesseeName || '',
//         payMethod: data.payMethod || '',
//         isRefundDeposit: '1',
//         refundDepositRemark: data.refundDepositRemark || '',
//         isThirdAgent: false as boolean,
//         agentName: '',
//         agentAccount: '',
//         agentBank: ''
//     })
// }

// 退款流水表格列定义
const flowLogColumns = ref<TableColumnData[]>([
    {
        title: '账单类型',
        dataIndex: 'type',
        align: 'center'
    },
    {
        title: '实际支付时间',
        dataIndex: 'entryTime',
        align: 'center',
        slotName: 'entryTime'
    },
    {
        title: '支付类型',
        dataIndex: 'payType',
        align: 'center'
    },
    {
        title: '支付方式',
        dataIndex: 'payMethod',
        align: 'center'
    },
    {
        title: '支付单号',
        dataIndex: 'orderNo',
        align: 'center'
    },
    {
        title: '实际记账金额',
        dataIndex: 'acctAmount',
        align: 'center',
        slotName: 'acctAmount'
    },
    {
        title: '记账人',
        dataIndex: 'createByName',
        align: 'center'
    },
    {
        title: '记账时间',
        dataIndex: 'createTime',
        align: 'center'
    },
    {
        title: '确认人',
        dataIndex: 'confirmUserName',
        align: 'center'
    },
    {
        title: '确认日期',
        dataIndex: 'confirmTime',
        align: 'center'
    },
    {
        title: '备注',
        dataIndex: 'remark',
        align: 'center'
    },
    {
        title: '操作',
        dataIndex: 'operation',
        align: 'center',
        slotName: 'operation',
        width: 100
    }
]);

// 退款流水数据
const flowLogList = ref<any[]>([]);

// 格式化金额
const formatMoney = (amount: number) => {
    return amount?.toFixed(2) || '0.00';
};

// 取消记账
const handleCancelRecord = (record: any) => {
    // TODO: 实现取消记账逻辑
    console.log('取消记账', record);
    Message.warning('功能开发中');
};
// 记账类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转
const getTypeText = (type: number | undefined) => {
    console.log(type)
    const typeMap = new Map([
        [1, '手动记账（收款）'],
        [2, '自动记账（收款）'],
        [3, '手动记账（退款）'],
        [4, '自动记账（退款）'],
        [5, '结转（转入）'],
        [6, '结转（转出）'],
        [7, '取消记账']
    ])
    return typeMap.get(Number(type)) || '未知'
}

// 从已有退款记录初始化表单数据
const initFormDataFromRefund = (refundDetail: any) => {
    console.log('refundDetail', refundDetail)
    const refund = refundDetail.refund || {}
    const booking = refundDetail.booking || {}

    // 处理流水数据
    if (refundDetail.flowRels && Array.isArray(refundDetail.flowRels)) {
        flowLogList.value = JSON.parse(JSON.stringify(refundDetail.flowRels))
        // refundDetail.flowRels.map((log: any) => ({
        //     // billType: log.billType === 1 ? '自动记账（退款）' : '手动记账（退款）',
        //     payTime: log.payTime,
        //     payType: log.payType === 1 ? '线上支付' : '银行转账',
        //     payMethod: log.payMethod,
        //     payNo: log.payNo,
        //     amount: log.amount,
        //     operator: log.operator || '系统自动',
        //     operateTime: log.operateTime,
        //     confirmUser: log.confirmUser || '系统自动',
        //     confirmDate: log.confirmDate,
        //     remark: log.remark
        // }));
    } else {
        flowLogList.value = [];
    }

    Object.assign(formData, {
        // 退款申请信息
        applyTime: refund.applyTime || dayjs().format('YYYY-MM-DD HH:mm:ss'),
        refundNo: refund.refundNo || '',
        bookingNo: booking.bookingNo || '',
        id: booking?.id || '',
        refundId: booking?.refundId || '',
        bizId: booking?.id || '',
        projectId: booking?.projectId || '',
        refundType: 1,
        refundAmount: refund.refundAmount || booking?.bookingAmount || 0,
        feeType: '定金',
        refundWay: refund.refundWay ?? 0,
        receiverName: refund.receiverName || booking?.customerName || '',
        receiverBank: refund.receiverBank || '',
        receiverAccount: refund.receiverAccount || '',
        refundRemark: refund.refundRemark || booking?.cancelRemark || '',
        attachments: refund.attachments || '',
        refundStatus: refund.refundStatus || 0,
        approveStatus: refund.approveStatus || 0,
        isSubmit: refund.approveStatus > 0,

        // 订单信息
        customerName: booking?.customerName || '',
        intentRoom: booking?.roomName || booking?.intentRoom || '',
        bookingAmount: booking?.bookingAmount || 0,
        receivedAmount: booking?.receivedAmount || 0,
        receivableDate: booking?.receivedDate || '',
        payMethod: booking?.payMethod || '',

        // 退款目标
        refundTarget: refund.refundTarget || `${booking?.customerName || ''} ${booking?.roomName || booking?.intentRoom || ''}`.trim(),

        // 退定相关
        isRefundDeposit: '1',
        refundDepositRemark: booking?.cancelRemark || '',
        isThirdAgent: Boolean(refund.isThirdAgent),
        agentAttachments: refund.agentAttachments || '',
        agentName: refund.agentName || '',
        agentAccount: refund.agentAccount || '',
        agentBank: refund.agentBank || '',
    })
}

// 取消
const handleCancel = () => {
    visible.value = false
    emit('cancel')
}

// 暂存
const handleSave = async () => {
    const validResult = await formRef.value?.validate();
    if (!validResult) {
        try {
            // formData.isSubmit = 0;
            // attachments为空时传undefined，避免JSON转换错误
            const requestData: FinancialRefundAddDTO = {
                id: formData.refundId, // 这里就是要这么处理，不要再改了
                bizId: formData.bizId,
                projectId: formData.projectId,
                refundType: formData.refundType,
                refundTarget: formData.refundTarget,
                applyTime: formData.applyTime,
                refundAmount: formData.refundAmount,
                feeType: '定金',
                refundWay: formData.refundWay,
                receiverName: formData.receiverName,
                receiverBank: formData.receiverBank,
                receiverAccount: formData.receiverAccount,
                refundRemark: formData.refundRemark,
                attachments: formData.attachments || undefined,
                agentAttachments: formData.agentAttachments || undefined,
                isThirdAgent: Boolean(formData.isThirdAgent),
                agentName: formData.agentName || undefined,
                agentAccount: formData.agentAccount || undefined,
                agentBank: formData.agentBank || undefined,
                refundStatus: 0,
                approveStatus: 0,
                isSubmit: 0
            };

            // 如果isThirdAgent为true，则需要传agentAttachments
            if (formData.isThirdAgent && !formData.agentAttachments) {
                Message.error('请上传退款申请单-代收');
                return;
            }

            await saveFinancialRefund(requestData);
            // Message.success('保存成功');
            visible.value = false;
            emit('success');
        } catch (error) {
            console.error('保存失败:', error);
            // Message.error('保存失败');
        }
    }
}

// 提交
const handleSubmit = async () => {
    const validResult = await formRef.value?.validate();
    if (!validResult) {
        try {
            // formData.isSubmit = 1;
            // attachments为空时传undefined，避免JSON转换错误
            const requestData: FinancialRefundAddDTO = {
                id: formData.refundId,
                bizId: formData.bizId,
                projectId: formData.projectId,
                refundType: formData.refundType,
                refundTarget: formData.refundTarget,
                applyTime: formData.applyTime,
                refundAmount: formData.refundAmount,
                feeType: '定金',
                refundWay: formData.refundWay,
                receiverName: formData.receiverName,
                receiverBank: formData.receiverBank,
                receiverAccount: formData.receiverAccount,
                refundRemark: formData.refundRemark,
                attachments: formData.attachments || undefined,
                agentAttachments: formData.agentAttachments || undefined,
                isThirdAgent: Boolean(formData.isThirdAgent),
                agentName: formData.agentName || undefined,
                agentAccount: formData.agentAccount || undefined,
                agentBank: formData.agentBank || undefined,
                refundStatus: 1,
                approveStatus: 1,
                isSubmit: 1
            };

            // 如果isThirdAgent为true，则需要传agentAttachments
            if (formData.isThirdAgent && !formData.agentAttachments) {
                Message.error('请上传退款申请单-代收');
                return;
            }

            await saveFinancialRefund(requestData);
            // Message.success('提交成功');
            visible.value = false;
            emit('success');
        } catch (error) {
            console.error('提交失败:', error);
            // Message.error('提交失败');
        }
    }
}

// 文档预览相关
const previewVisible = ref(false)
const previewFileUrl = ref('')
const previewTitle = ref('退款申请单预览')

// 修改打印预览方法
const handlePrint = async () => {
    try {
        const printData: BookingPrintDto = {
            id: formData.bizId,
            type: 10, // 定单类型
        }

        const response = await printBooking(printData)

        if (response.data?.fileUrl) {
            // 打开预览
            previewFileUrl.value = response.data.fileUrl
            previewVisible.value = true
            Message.success('打印文件生成成功')
        } else {
            Message.error('打印文件生成失败')
        }
    } catch (error) {
        console.error('打印失败:', error)
        Message.error('打印失败')
    }
}

// 修改模板下载方法
const handleAgentAttachmentsDownload = () => {
    downloadTemplate().then(res => {
        if (res.templateUrl) {
            // 打开预览
            previewFileUrl.value = res.templateUrl
            previewTitle.value = '退款申请单-代收模板'
            previewVisible.value = true
        } else {
            Message.error('获取模板失败')
        }
    }).catch(err => {
        console.error('下载退款申请单-代收模板失败:', err)
        Message.error('获取模板失败')
    })
}

// 添加第三方代收相关方法
const handleIsThirdAgentChange = (value: boolean) => {
    if (value) {
        formData.refundWay = 1; // 第三方代收时，强制设置为银行转账
    } else {
        formData.refundWay = 0; // 非第三方代收时，恢复为原路退回
    }
}

// 暴露方法给父组件
defineExpose({
    open,
    // initFormData,
    initFormDataFromRefund,
    print
})
</script>

<style scoped lang="less">
.upload-tip {
    margin-top: 8px;
    color: var(--color-text-3);
    font-size: 12px;
}

.tip-text {
    color: #F53F3F;
    font-size: 12px;
    line-height: 20px;
}
</style>