---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/减免缓

<a id="opIdeditSplit"></a>

## POST 编辑分期

POST /reduction/split/edit

编辑分期

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "applicationCode": "string",
  "applicationName": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "reason": "string",
  "remark": "string",
  "status": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "adjustList": [
    {
      "id": "string",
      "reductionId": "string",
      "contractId": "string",
      "costId": "string",
      "costType": 0,
      "period": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "reductionAmount": 0,
      "delayTime": "2019-08-24T14:15:22Z",
      "currActualReceivable": 0,
      "isDel": true
    }
  ],
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReductionPostponedAddDTO](#schemareductionpostponedadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdaddSplit"></a>

## POST 新增分期

POST /reduction/split/add

新增分期

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "applicationCode": "string",
  "applicationName": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "reason": "string",
  "remark": "string",
  "status": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "adjustList": [
    {
      "id": "string",
      "reductionId": "string",
      "contractId": "string",
      "costId": "string",
      "costType": 0,
      "period": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "reductionAmount": 0,
      "delayTime": "2019-08-24T14:15:22Z",
      "currActualReceivable": 0,
      "isDel": true
    }
  ],
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReductionPostponedAddDTO](#schemareductionpostponedadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdeditPostponed"></a>

## POST 编辑缓交

POST /reduction/postponed/edit

编辑缓交

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "applicationCode": "string",
  "applicationName": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "reason": "string",
  "remark": "string",
  "status": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "adjustList": [
    {
      "id": "string",
      "reductionId": "string",
      "contractId": "string",
      "costId": "string",
      "costType": 0,
      "period": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "reductionAmount": 0,
      "delayTime": "2019-08-24T14:15:22Z",
      "currActualReceivable": 0,
      "isDel": true
    }
  ],
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReductionPostponedAddDTO](#schemareductionpostponedadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdaddPostponed"></a>

## POST 新增缓交

POST /reduction/postponed/add

新增缓交

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "applicationCode": "string",
  "applicationName": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "reason": "string",
  "remark": "string",
  "status": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "adjustList": [
    {
      "id": "string",
      "reductionId": "string",
      "contractId": "string",
      "costId": "string",
      "costType": 0,
      "period": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "reductionAmount": 0,
      "delayTime": "2019-08-24T14:15:22Z",
      "currActualReceivable": 0,
      "isDel": true
    }
  ],
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReductionPostponedAddDTO](#schemareductionpostponedadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_4"></a>

## POST 导出询减免缓列表

POST /reduction/export

导出询减免缓列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|projectId|query|string| 否 | 项目ID|项目ID|
|type|query|integer(int32)| 否 | 类型（1减免 2缓交 3分期）|类型（1减免 2缓交 3分期）|
|status|query|integer(int32)| 否 | 状态(0草稿 1待生效 2生效 4作废)|状态(0草稿 1待生效 2生效 4作废)|
|approveStatus|query|integer(int32)| 否 | 状态(0草稿 1审批中 2已通过 3已驳回 4作废)|状态(0草稿 1审批中 2已通过 3已驳回 4作废)|
|contractNo|query|string| 否 | 合同编号|合同编号|
|roomName|query|string| 否 | 租赁单元|租赁单元|
|customerName|query|string| 否 | 客户名称|客户名称|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdeditReduction"></a>

## POST 编辑减免

POST /reduction/edit

编辑减免

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "applicationCode": "string",
  "applicationName": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "reason": "string",
  "remark": "string",
  "status": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "adjustList": [
    {
      "id": "string",
      "reductionId": "string",
      "contractId": "string",
      "costId": "string",
      "costType": 0,
      "period": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "reductionAmount": 0,
      "delayTime": "2019-08-24T14:15:22Z",
      "currActualReceivable": 0,
      "isDel": true
    }
  ],
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ReductionPostponedAddDTO](#schemareductionpostponedadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdunpaidCost"></a>

## POST 获取合同未收齐账单

POST /reduction/contract/unpaidCost

根据合同ID查询未收齐账单信息

> Body 请求参数

```json
{
  "projectId": "string",
  "contractNo": "string",
  "customerName": "string",
  "type": 0,
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ContractCostReductionDTO](#schemacontractcostreductiondto)| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","projectName":"string","chargeType":0,"bizId":"string","refundId":"string","refundCostId":"string","bizNo":"string","customerId":"string","customerType":0,"customerName":"string","customerPhone":"string","costType":0,"costTypeName":"string","period":0,"subjectId":"string","subjectName":"string","startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","rentPeriod":"string","receivableDate":"2019-08-24T14:15:22Z","status":0,"canPay":true,"isRevenueBill":true,"isRevenueGenerated":true,"isFromContract":true,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"pendingAmount":0,"carryoverAmount":0,"confirmStatus":0,"createByName":"string","updateByName":"string","isDel":true,"contractType":0,"roomName":"string","contractStatus":0,"contractStatusTwo":0}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[CostVo](#schemacostvo)]|false|none||[账单信息]|
|» 账单信息|[CostVo](#schemacostvo)|false|none|账单信息|账单信息|
|»» id|string|false|none|主键ID|none|
|»» projectId|string|false|none|项目id|项目id|
|»» projectName|string|false|none|项目名称|项目名称|
|»» chargeType|integer(int32)|false|none|收费类别: 0-定单, 1-合同|收费类别: 0-定单, 1-合同|
|»» bizId|string|false|none|业务id, 定单类别对应定单id, 合同类别对应合同id|业务id, 定单类别对应定单id, 合同类别对应合同id|
|»» refundId|string|false|none|退款单id|退款单id|
|»» refundCostId|string|false|none|退款关联的收费单Cost表id|退款关联的收费单Cost表id|
|»» bizNo|string|false|none|业务单号|业务单号|
|»» customerId|string|false|none|承租人id|承租人id|
|»» customerType|integer(int32)|false|none|客户类型:1-个人1,2-企业|客户类型:1-个人1,2-企业|
|»» customerName|string|false|none|承租人名称|承租人名称|
|»» customerPhone|string|false|none|客户手机号|客户手机号|
|»» costType|integer(int32)|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|»» costTypeName|string|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|»» period|integer(int32)|false|none|账单期数|账单期数|
|»» subjectId|string|false|none|收款用途id|收款用途id|
|»» subjectName|string|false|none|收款用途名称|收款用途名称|
|»» startDate|string(date-time)|false|none|开始日期|开始日期|
|»» endDate|string(date-time)|false|none|结束日期|结束日期|
|»» rentPeriod|string|false|none|账单周期|账单周期|
|»» receivableDate|string(date-time)|false|none|应收日期|应收日期|
|»» status|integer(int32)|false|none|账单状态: 0-待收、1-待付、2-已收、3-已付|账单状态: 0-待收、1-待付、2-已收、3-已付|
|»» canPay|boolean|false|none|是否可收/付 0-否,1-是|是否可收/付 0-否,1-是|
|»» isRevenueBill|boolean|false|none|是否是营收抽点账单 0-否,1-是|是否是营收抽点账单 0-否,1-是|
|»» isRevenueGenerated|boolean|false|none|营收抽点金额是否已生成 0-否,1-是|营收抽点金额是否已生成 0-否,1-是|
|»» isFromContract|boolean|false|none|是否来自合同生成 0-否,1-是|是否来自合同生成 0-否,1-是|
|»» totalAmount|number|false|none|账单总额|账单总额|
|»» discountAmount|number|false|none|优惠金额|优惠金额|
|»» actualReceivable|number|false|none|实际应收金额|实际应收金额|
|»» receivedAmount|number|false|none|已收金额|已收金额|
|»» pendingAmount|number|false|none|待收/付金额|待收/付金额|
|»» carryoverAmount|number|false|none|结转金额|结转金额|
|»» confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|»» createByName|string|false|none|创建人姓名|创建人姓名|
|»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|»» contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|»» roomName|string|false|none|租赁资源|租赁资源|
|»» contractStatus|integer(int32)|false|none|合同一级状态|合同一级状态|
|»» contractStatusTwo|integer(int32)|false|none|合同二级状态|合同二级状态|

<a id="opIdgetContractList"></a>

## POST 获取符合条件的合同

POST /reduction/contract/list

获取符合条件的合同

> Body 请求参数

```json
{
  "projectId": "string",
  "contractNo": "string",
  "customerName": "string",
  "type": 0,
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ContractCostReductionDTO](#schemacontractcostreductiondto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdaddReduction"></a>

## POST 新增减免

POST /reduction/add

新增减免

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "applicationCode": "string",
  "applicationName": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "reason": "string",
  "remark": "string",
  "status": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "adjustList": [
    {
      "id": "string",
      "reductionId": "string",
      "contractId": "string",
      "costId": "string",
      "costType": 0,
      "period": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "reductionAmount": 0,
      "delayTime": "2019-08-24T14:15:22Z",
      "currActualReceivable": 0,
      "isDel": true
    }
  ],
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ReductionPostponedAddDTO](#schemareductionpostponedadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetSplitDetail_1"></a>

## GET 分期详情

GET /reduction/split/detail

分期详情

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||减免ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_15"></a>

## GET 查询减免缓列表

GET /reduction/list

查询减免缓列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 否 | 项目ID|项目ID|
|type|query|integer(int32)| 否 | 类型（1减免 2缓交 3分期）|类型（1减免 2缓交 3分期）|
|status|query|integer(int32)| 否 | 状态(0草稿 1待生效 2生效 4作废)|状态(0草稿 1待生效 2生效 4作废)|
|approveStatus|query|integer(int32)| 否 | 状态(0草稿 1审批中 2已通过 3已驳回 4作废)|状态(0草稿 1审批中 2已通过 3已驳回 4作废)|
|contractNo|query|string| 否 | 合同编号|合同编号|
|roomName|query|string| 否 | 租赁单元|租赁单元|
|customerName|query|string| 否 | 客户名称|客户名称|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgetReductionDetail"></a>

## GET 减免详情

GET /reduction/detail

减免详情

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||减免ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddeleteReduction"></a>

## DELETE 删除减免

DELETE /reduction/delete

删除减免

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||减免ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_ReductionPostponedAddDTO">ReductionPostponedAddDTO</h2>

<a id="schemareductionpostponedadddto"></a>
<a id="schema_ReductionPostponedAddDTO"></a>
<a id="tocSreductionpostponedadddto"></a>
<a id="tocsreductionpostponedadddto"></a>

```json
{
  "id": "string",
  "type": 0,
  "applicationCode": "string",
  "applicationName": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "reason": "string",
  "remark": "string",
  "status": 0,
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "adjustList": [
    {
      "id": "string",
      "reductionId": "string",
      "contractId": "string",
      "costId": "string",
      "costType": 0,
      "period": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "reductionAmount": 0,
      "delayTime": "2019-08-24T14:15:22Z",
      "currActualReceivable": 0,
      "isDel": true
    }
  ],
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|type|integer(int32)|false|none|类型（1减免 2缓交 3分期）|类型（1减免 2缓交 3分期）|
|applicationCode|string|false|none|立项定价申请编号|立项定价申请编号|
|applicationName|string|false|none|立项定价申请名称|立项定价申请名称|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|customerId|string|false|none|客户ID|客户ID|
|customerName|string|false|none|客户名称|客户名称|
|contractId|string|false|none|合同ID|合同ID|
|contractNo|string|false|none|合同编号|合同编号|
|roomName|string|false|none|房间名称|房间名称，多个按照逗号分开|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|contractPurpose|integer(int32)|false|none|合同用途|合同用途|
|reason|string|false|none|申请原因|申请原因|
|remark|string|false|none|申请说明|申请说明|
|status|integer(int32)|false|none|状态(0草稿 1待生效 2生效 4作废)|状态(0草稿 1待生效 2生效 4作废)|
|approveStatus|integer(int32)|false|none|审批状态(0草稿 1审批中 2已通过 3已驳回 4作废)|状态(0草稿 1审批中 2已通过 3已驳回 4作废)|
|approveTime|string(date-time)|false|none|审批通过时间|审批通过时间|
|adjustList|[[ReductionPostponedAdjustAddDTO](#schemareductionpostponedadjustadddto)]|false|none|调整列表|调整列表|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_ReductionPostponedAdjustAddDTO">ReductionPostponedAdjustAddDTO</h2>

<a id="schemareductionpostponedadjustadddto"></a>
<a id="schema_ReductionPostponedAdjustAddDTO"></a>
<a id="tocSreductionpostponedadjustadddto"></a>
<a id="tocsreductionpostponedadjustadddto"></a>

```json
{
  "id": "string",
  "reductionId": "string",
  "contractId": "string",
  "costId": "string",
  "costType": 0,
  "period": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "reductionAmount": 0,
  "delayTime": "2019-08-24T14:15:22Z",
  "currActualReceivable": 0,
  "isDel": true
}

```

调整列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|reductionId|string|false|none|减免缓id|减免缓id|
|contractId|string|false|none|合同ID|合同ID|
|costId|string|false|none|账单ID|账单ID|
|costType|integer(int32)|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|period|integer(int32)|false|none|账单期数|账单期数|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|reductionAmount|number|false|none|减免金额|减免金额|
|delayTime|string(date-time)|false|none|缓交日期|缓交日期|
|currActualReceivable|number|false|none|调整后实际应收|调整后实际应收|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractCostReductionDTO">ContractCostReductionDTO</h2>

<a id="schemacontractcostreductiondto"></a>
<a id="schema_ContractCostReductionDTO"></a>
<a id="tocScontractcostreductiondto"></a>
<a id="tocscontractcostreductiondto"></a>

```json
{
  "projectId": "string",
  "contractNo": "string",
  "customerName": "string",
  "type": 0,
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|false|none|项目id|项目id|
|contractNo|string|false|none|合同号|合同号|
|customerName|string|false|none|承租方名称|承租方名称|
|type|integer(int32)|false|none|类型（1减免 2缓交 3分期）|类型（1减免 2缓交 3分期）|
|contractId|string|false|none|合同id|合同id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|

<h2 id="tocS_CostVo">CostVo</h2>

<a id="schemacostvo"></a>
<a id="schema_CostVo"></a>
<a id="tocScostvo"></a>
<a id="tocscostvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "chargeType": 0,
  "bizId": "string",
  "refundId": "string",
  "refundCostId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerType": 0,
  "customerName": "string",
  "customerPhone": "string",
  "costType": 0,
  "costTypeName": "string",
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "rentPeriod": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "isRevenueBill": true,
  "isRevenueGenerated": true,
  "isFromContract": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "pendingAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractType": 0,
  "roomName": "string",
  "contractStatus": 0,
  "contractStatusTwo": 0
}

```

账单信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|chargeType|integer(int32)|false|none|收费类别: 0-定单, 1-合同|收费类别: 0-定单, 1-合同|
|bizId|string|false|none|业务id, 定单类别对应定单id, 合同类别对应合同id|业务id, 定单类别对应定单id, 合同类别对应合同id|
|refundId|string|false|none|退款单id|退款单id|
|refundCostId|string|false|none|退款关联的收费单Cost表id|退款关联的收费单Cost表id|
|bizNo|string|false|none|业务单号|业务单号|
|customerId|string|false|none|承租人id|承租人id|
|customerType|integer(int32)|false|none|客户类型:1-个人1,2-企业|客户类型:1-个人1,2-企业|
|customerName|string|false|none|承租人名称|承租人名称|
|customerPhone|string|false|none|客户手机号|客户手机号|
|costType|integer(int32)|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|costTypeName|string|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|period|integer(int32)|false|none|账单期数|账单期数|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途名称|收款用途名称|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|rentPeriod|string|false|none|账单周期|账单周期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|status|integer(int32)|false|none|账单状态: 0-待收、1-待付、2-已收、3-已付|账单状态: 0-待收、1-待付、2-已收、3-已付|
|canPay|boolean|false|none|是否可收/付 0-否,1-是|是否可收/付 0-否,1-是|
|isRevenueBill|boolean|false|none|是否是营收抽点账单 0-否,1-是|是否是营收抽点账单 0-否,1-是|
|isRevenueGenerated|boolean|false|none|营收抽点金额是否已生成 0-否,1-是|营收抽点金额是否已生成 0-否,1-是|
|isFromContract|boolean|false|none|是否来自合同生成 0-否,1-是|是否来自合同生成 0-否,1-是|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|pendingAmount|number|false|none|待收/付金额|待收/付金额|
|carryoverAmount|number|false|none|结转金额|结转金额|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|roomName|string|false|none|租赁资源|租赁资源|
|contractStatus|integer(int32)|false|none|合同一级状态|合同一级状态|
|contractStatusTwo|integer(int32)|false|none|合同二级状态|合同二级状态|

