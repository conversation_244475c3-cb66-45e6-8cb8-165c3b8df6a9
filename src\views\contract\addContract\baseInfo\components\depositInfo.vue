<template>
    <section>
        <sectionTitle title="保证金信息" />
        <div class="content">
            <a-form :disabled="isDetailMode" :model="contractData" :rules="isDetailMode ? {} : rules" ref="formRef"
                auto-label-width layout="vertical">
                <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                    <a-grid-item>
                        <a-form-item label="应收日期" field="bondReceivableDate">
                            <a-input-group>
                                <a-select v-model="contractData.bondReceivableType"
                                    style="width: 120px; flex-shrink: 0;" @change="handleReceivableTypeChange">
                                    <a-option :value="0">合同签订后</a-option>
                                    <a-option :value="1">指定日期</a-option>
                                </a-select>
                                <template v-if="contractData.bondReceivableType === 0">
                                    <a-input v-model="contractData.bondReceivableDate" placeholder="请输入">
                                        <template #suffix>天</template>
                                    </a-input>
                                </template>
                                <template v-else>
                                    <a-date-picker v-model="contractData.bondReceivableDate" style="flex: 1;" />
                                </template>
                            </a-input-group>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="保证金金额" field="bondPriceType">
                            <a-select v-model="contractData.bondPriceType" :options="bondPriceTypeOptions"
                                @change="handleDepositTypeChange">
                            </a-select>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="应收金额" field="bondPrice">
                            <a-input-number v-model="contractData.bondPrice" :disabled="contractData.bondPriceType !== 0" :precision="2" placeholder=""
                                :formatter="formatter" :parser="parser" :min="0">
                                <template #suffix>元</template>
                            </a-input-number>
                        </a-form-item>
                    </a-grid-item>
                </a-grid>
            </a-form>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, toRef, inject, watch } from 'vue'
import type { FormInstance } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const formatter = (value: string) => {
    const values = value.split('.');
    values[0] = values[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return values.join('.')
};

const parser = (value: string) => {
    return value.replace(/,/g, '')
};

/**
 * @description 编辑类型
 * 1. 详情
 *    - 展示文本，不校验必填
 */
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || (editType.value === 'change' && !changeType.value.includes(3)) || editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo')

const bondPriceTypeOptions = [
    { label: '自定义', value: 0 },
    { label: '押1个月', value: 1 },
    { label: '押2个月', value: 2 },
    { label: '押3个月', value: 3 },
    { label: '押4个月', value: 4 },
    { label: '押5个月', value: 5 },
    { label: '押6个月', value: 6 },
    { label: '押7个月', value: 7 },
    { label: '押8个月', value: 8 },
    { label: '押9个月', value: 9 },
    { label: '押10个月', value: 10 },
    { label: '押11个月', value: 11 },
    { label: '押12个月', value: 12 },
    { label: '按房源保证金', value: 30 },
]

// 统一表单校验规则
const rules = computed(() => {
    if (isDetailMode.value) {
        return {}
    }
    return {
        bondReceivableDate: [{ required: true, message: '请完善应收日期信息' }],
        bondPriceType: [{ required: true, message: '请选择保证金金额' }],
        bondPrice: [{ required: true, message: '应收金额不能为空' }]
    }
})

// 处理应收类型变化
const handleReceivableTypeChange = () => {
    // 重置相关字段
    contractData.value.bondReceivableDate = ''
}

// 处理保证金类型变化
const handleDepositTypeChange = () => {
    if(contractData.value.bondPriceType !== 0){
        calculateReceivableAmount()
    }
}

/**
 * 计算应收金额
 * 1. 如果bondPriceType不是房源保证金，则应收金额 = 房间签约总价之和 * bondPriceType月数
 * 2. 如果bondPriceType是房源保证金，则应收金额计算遵循以下规则
 *    a. 分别计算每个房间的金额
 *    b. 如果房间中bondPriceType为0，则应收金额 = 各房间bondPrice金额之和
 *    c. 如果房间中bondPriceType不为0，则应收金额为各房间签约总价乘以bondPriceType之和
 */
const calculateReceivableAmount = () => {
    // 如果没有选择保证金类型或没有房间数据，则重置应收金额
    if (!contractData.value.bondPriceType || !contractData.value.rooms || contractData.value.rooms.length === 0) {
        contractData.value.bondPrice = 0
        return
    }

    const bondPriceType = contractData.value.bondPriceType

    // 情况1：不是房源保证金（1-12个月）
    if (bondPriceType !== 30) {
        // 计算所有房间签约总价之和
        const totalMonthlyPrice = contractData.value.rooms.reduce((sum, room) => {
            return sum + (room.signedMonthlyPrice || 0)
        }, 0)

        // 应收金额 = 房间签约总价之和 * bondPriceType月数
        contractData.value.bondPrice = Number((totalMonthlyPrice * bondPriceType).toFixed(2))
    }
    // 情况2：房源保证金
    else {
        let totalReceivableAmount = 0

        // 分别计算每个房间的金额
        contractData.value.rooms.forEach(room => {
            // 如果房间的depositType为0（自定义），则使用房间的depositAmount
            if (room.bondPriceType === 0) {
                totalReceivableAmount += room.bondPrice || 0
            }
            // 如果房间的depositType不为0（1-12个月），则使用房间签约总价乘以月数
            else if (room.bondPriceType && room.bondPriceType >= 1 && room.bondPriceType <= 12) {
                totalReceivableAmount += (room.signedMonthlyPrice || 0) * room.bondPriceType
            }
        })

        contractData.value.bondPrice = Number(totalReceivableAmount.toFixed(2))
    }
}

/**
 * 房间数据发生变化时，需要重新计算应收金额
 */
watch(
    () => contractData.value.rooms,
    () => {
        if(contractData.value.bondPriceType !== 0){
            calculateReceivableAmount()
        }
    },
    { deep: true }
)


const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('保证金信息', errors);
            reject()
        } else {
            resolve(true)
        }
    })
}

defineExpose({
    validate
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.detail-text {
    padding: 4px 0;
    color: #333;
}
</style>