<template>
  <div class="account-record">
    <section-title title="记账记录" style="margin-bottom: 16px;" />
    <a-table :columns="columns" :data="records" row-key="id" :pagination="false" :scroll="{ x: 1 }">
      <template #operation="{ record }">
        <a-button type="text" @click="handleEdit(record)">取消记账</a-button>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'

const records = ref([
  {
    id: 1,
    project: '广东（博罗）万洋众创城',
    unit: 'F230、F393',
    contractNo: 'WYSF-GD-BL-SS-2025-0059',
    billType: '保证金账单',
    billPeriod: '2024-03-12 至 2025-03-11',
    amount: 10000.00,
    recordTime: '2024-03-12 09:00:00',
    recorder: '张三',
    confirmationStatus: '自动确认',
    confirmer: '系统自动',
    confirmationTime: '2024-03-12 09:00:00'
  },
  {
    id: 2,
    project: '广东（博罗）万洋众创城',
    unit: 'F230、F393',
    contractNo: 'WYSF-GD-BL-SS-2025-0059',
    billType: '租金第1期',
    billPeriod: '2024-03-12 至 2025-03-11',
    amount: 10000.00,
    recordTime: '2024-03-12 09:00:00',
    recorder: '张三',
    confirmationStatus: '手动确认',
    confirmer: 'admin',
    confirmationTime: '2024-03-22 09:00:00'
  },
  {
    id: 3,
    project: '广东（博罗）万洋众创城',
    unit: 'F230、F393',
    contractNo: 'WYSF-GD-BL-SS-2025-0059',
    billType: '租金第1期',
    billPeriod: '2024-03-12 至 2025-03-11',
    amount: 10000.00,
    recordTime: '2024-03-12 09:00:00',
    recorder: '张三',
    confirmationStatus: '未确认',
    confirmer: '',
    confirmationTime: ''
  },
  // 其他记录...
])

const columns = [
  { title: '项目', dataIndex: 'project', width: 200 , ellipsis: true, tooltip: true},
  { title: '租赁单元', dataIndex: 'unit', width: 200 , ellipsis: true, tooltip: true},
  { title: '合同编号', dataIndex: 'contractNo', width: 200 , ellipsis: true, tooltip: true},
  { title: '账单类型', dataIndex: 'billType', width: 200 , ellipsis: true, tooltip: true},
  { title: '账单起始周期', dataIndex: 'billPeriod', width: 200 , ellipsis: true, tooltip: true},
  { title: '记账金额', dataIndex: 'amount', width: 200 , ellipsis: true, tooltip: true},
  { title: '记账时间', dataIndex: 'recordTime', width: 200 , ellipsis: true, tooltip: true},
  { title: '记账人', dataIndex: 'recorder', width: 200 , ellipsis: true, tooltip: true},
  { title: '确认状态', dataIndex: 'confirmationStatus', width: 200 , ellipsis: true, tooltip: true},
  { title: '确认人', dataIndex: 'confirmer', width: 200 , ellipsis: true, tooltip: true},
  { title: '确认时间', dataIndex: 'confirmationTime', width: 200 , ellipsis: true, tooltip: true},
  { title: '操作', slotName: 'operation', width: 150, ellipsis: true, tooltip: true, fixed: 'right'}
]

const handleEdit = (record: any) => {
  console.log('取消记账:', record)
  // 处理取消记账逻辑
}
</script>

<style scoped lang="less">
.account-record {
  .ant-table {
    margin-top: 16px;
  }
}
</style> 