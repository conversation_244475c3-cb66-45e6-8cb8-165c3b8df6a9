// 出场管理相关类型定义

// 出场单基础信息
export interface ExitInfo {
  id?: string
  projectId: string
  contractId: string
  contractNo: string
  contractUnionId: string
  terminateId: string
  refundId?: string
  customerId: string
  customerName: string
  processType: number // 办理流程: 1-先交割后结算, 2-交割并结算
  progressStatus: number // 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废
  isDiscount?: boolean
  discountAmount?: number
  discountReason?: string
  finalAmount?: number
  refundProcessType?: number // 退款处理方式: 1-退款, 2-暂存客户账户
  payeeName?: string
  payeeAccount?: string
  bankName?: string
  licenseStatus?: number
  taxCertStatus?: number
  refundApplyType?: number
  signType?: number
  signAttachments?: string
  signTime?: string
  copyTime?: string
  copyBy?: string
  copyByName?: string
  settleTime?: string
  settleBy?: string
  settleByName?: string
  createByName?: string
  updateByName?: string
  isDel?: boolean
  contractPurpose?: number
  terminateType?: number
  terminateDate?: string
  terminateRoomName?: string
  terminateRoomCount?: number
}

// 出场费用结算项
export interface ExitCost {
  id?: string
  exitId?: string
  costId?: string
  startDate?: string
  endDate?: string
  payType: number // 支付方向: 1-收, 2-支
  subjectId?: string
  subjectName?: string
  receivableDate?: string
  amount: number
  remark?: string // 费用说明
  type?: number // 账单类型: 0-退租结算, 1-交割单生成, 2-手动添加
  createByName?: string
  updateByName?: string
  isDel?: boolean
}

// 房间资产信息
export interface ExitRoomAssets {
  id?: string
  exitId?: string
  exitRoomId?: string
  category?: number
  name: string
  specification?: string
  count: number
  status: number // 现状: 1-完好, 2-损坏, 3-丢失
  penalty?: number
  isAdd?: boolean
  remark?: string
  isDel?: boolean
}

// 出场房间信息
export interface ExitRoom {
  id?: string
  exitId?: string
  roomId: string
  roomName: string
  propertyType?: number
  parcelName?: string
  buildingName?: string
  exitDate?: string
  rentControl?: number // 租控管理: 1-当前空置已做断电、锁门处理, 2-二次出租无需断电处理,开门设置更新
  doorWindowStatus?: number // 门、窗、墙体及其他情况: 1-完好, 2-损坏
  doorWindowPenalty?: number
  keyHandoverStatus?: number // 钥匙交接情况: 1-已交齐, 2-未交齐
  keyPenalty?: number
  cleaningStatus?: number // 清洁卫生: 1-自行打扫完毕、洁净, 2-保洁及垃圾清理收费
  cleaningPenalty?: number
  elecMeterReading?: number
  coldWaterReading?: number
  hotWaterReading?: number
  elecFee?: number
  waterFee?: number
  pmFee?: number
  roomPhotos?: string
  assetsSituation?: string
  remark?: string
  isBusinessConfirmed?: boolean
  businessConfirmBy?: string
  businessConfirmByName?: string
  businessConfirmTime?: string
  isFinanceConfirmed?: boolean
  financeConfirmBy?: string
  financeConfirmByName?: string
  financeConfirmTime?: string
  financeConfirmSignature?: string
  isEngineeringConfirmed?: boolean
  engineeringConfirmBy?: string
  engineeringConfirmByName?: string
  engineeringConfirmTime?: string
  engineeringConfirmSignature?: string
  exitRoomAssetsList?: ExitRoomAssets[]
  isSubmit?: boolean
  isDel?: boolean
}

// 出场新增/编辑DTO
export interface ExitAddDTO extends ExitInfo {
  exitCostList?: ExitCost[]
  isSubmit?: boolean
}

// 出场房间新增/编辑DTO
export interface ExitRoomAddDTO extends ExitRoom {
  exitRoomAssetsList?: ExitRoomAssets[]
}

// 出场详情VO
export interface ExitDetailVo {
  exitInfo: ExitInfo
  contractTerminateInfo?: any
  exitRoomList?: ExitRoom[]
  exitCostList?: ExitCost[]
  exitRoomAssetsList?: ExitRoomAssets[]
}

// 出场列表查询参数
export interface ExitListParams {
  pageNum: number
  pageSize: number
  id?: string
  projectId?: string
  contractId?: string
  contractNo?: string
  contractUnionId?: string
  terminateId?: string
  refundId?: string
  customerId?: string
  customerName?: string
  processType?: number
  progressStatus?: number
  isDiscount?: boolean
  discountAmount?: number
  discountReason?: string
  finalAmount?: number
  refundProcessType?: number
  payeeName?: string
  payeeAccount?: string
  bankName?: string
  licenseStatus?: number
  taxCertStatus?: number
  refundApplyType?: number
  signType?: number
  signAttachments?: string
  signTime?: string
  copyTime?: string
  copyBy?: string
  copyByName?: string
  settleTime?: string
  settleBy?: string
  settleByName?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  isDel?: boolean
  status?: number
  tenantName?: string
  buildingOrRoomName?: string
  terminateStartDate?: string
  terminateEndDate?: string
}

// 出场签字上传DTO
export interface ExitUploadSignatureDTO {
  exitId: string
  signatureFiles: string
}

// 出场单作废DTO
export interface ExitCancelDTO {
  exitId: string
}

// 出场办理类型选择DTO
export interface ExitProcessTypeDTO {
  exitId: string
  processType: number // 办理流程: 1-先交割后结算, 2-交割并结算
}

// 出场房间批量更新DTO
export interface ExitRoomBatchUpdateDTO {
  exitRoomIds: string[]
  type: number // 操作类型: 1-批量确认, 2-批量设置出场日期
  exitDate?: string // 出场日期，当type=2时必填
}

// 通用响应结果
export interface AjaxResult<T = any> {
  code?: number
  msg?: string
  data?: T
  success?: boolean
  error?: boolean
  warn?: boolean
  empty?: boolean
}

// 分页响应结果
export interface PageResult<T = any> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
} 