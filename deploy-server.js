const express = require('express');
const { spawn, execSync } = require('child_process');
const path = require('path');

const app = express();
const PORT = 3000;

// 全局部署状态管理
let deploymentState = {
    isDeploying: false,
    environment: null,
    branch: null,
    startTime: null,
    deployId: null,
    progress: '',
    user: null // 可以扩展为用户标识
};

// 生成部署ID
function generateDeployId() {
    return `deploy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 设置部署状态
function setDeploymentState(isDeploying, environment = null, branch = null, progress = '') {
    if (isDeploying && !deploymentState.isDeploying) {
        deploymentState = {
            isDeploying: true,
            environment,
            branch,
            startTime: new Date().toISOString(),
            deployId: generateDeployId(),
            progress,
            user: null
        };
    } else if (!isDeploying) {
        deploymentState = {
            isDeploying: false,
            environment: null,
            branch: null,
            startTime: null,
            deployId: null,
            progress: '',
            user: null
        };
    } else {
        // 更新进度信息
        deploymentState.progress = progress;
    }
    
    console.log('部署状态更新:', deploymentState);
}

// 中间件
app.use(express.json());

// 获取所有分支
function getAllBranches() {
    try {
        // 先执行 git fetch 获取最新的远程分支信息
        execSync('git fetch --all', { encoding: 'utf8' });
        
        // 获取所有远程分支
        const remoteBranches = execSync('git branch -r', { encoding: 'utf8' }).trim();
        
        // 获取当前分支
        const currentBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
        
        const branches = [];
        if (remoteBranches) {
            const branchLines = remoteBranches.split('\n');
            branchLines.forEach(line => {
                const branch = line.trim();
                // 过滤掉 HEAD 和空行
                if (branch && !branch.includes('HEAD ->')) {
                    // 移除 origin/ 前缀
                    const branchName = branch.replace(/^origin\//, '');
                    if (branchName) {
                        branches.push({
                            name: branchName,
                            displayName: branchName,
                            isCurrent: branchName === currentBranch
                        });
                    }
                }
            });
        }
        
        // 去重并排序
        const uniqueBranches = branches.filter((branch, index, self) => 
            index === self.findIndex(b => b.name === branch.name)
        );
        
        // 将当前分支排在前面
        uniqueBranches.sort((a, b) => {
            if (a.isCurrent) return -1;
            if (b.isCurrent) return 1;
            return a.name.localeCompare(b.name);
        });
        
        return {
            currentBranch: currentBranch,
            branches: uniqueBranches
        };
    } catch (error) {
        console.warn('获取分支信息失败:', error.message);
        return {
            currentBranch: 'unknown',
            branches: []
        };
    }
}

// 获取今天的Git提交信息（复用upload.server.js中的函数）
function getTodayGitCommits() {
    try {
        // 获取当前分支名
        const branchName = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim()
        
        // 获取今天的日期（格式：YYYY-MM-DD）
        const today = new Date().toISOString().split('T')[0]
        
        // 获取今天的所有提交记录，格式：哈希|作者|时间|消息
        const gitLogCommand = `git log --since="${today} 00:00:00" --until="${today} 23:59:59" --pretty=format:"%h|%an|%cd|%s" --date=format:"%H:%M:%S"`
        const commitLogs = execSync(gitLogCommand, { encoding: 'utf8' }).trim()
        
        if (!commitLogs) {
            return {
                branch: branchName,
                commits: [],
                totalCount: 0
            }
        }
        
        // 解析提交记录
        const commits = commitLogs.split('\n').map(line => {
            const [hash, author, time, message] = line.split('|')
            return {
                hash: hash,
                author: author,
                time: time,
                message: message
            }
        })
        
        return {
            branch: branchName,
            commits: commits,
            totalCount: commits.length
        }
    } catch (error) {
        console.warn('获取Git信息失败:', error.message)
        return {
            branch: 'unknown',
            commits: [],
            totalCount: 0
        }
    }
}

// 路由：提供主页
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'deploy-web.html'));
});

// 路由：获取部署状态
app.get('/deployment-status', (req, res) => {
    res.json(deploymentState);
});

// 路由：获取所有分支
app.get('/branches', (req, res) => {
    try {
        const branchInfo = getAllBranches();
        res.json(branchInfo);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 路由：获取Git信息
app.get('/git-info', (req, res) => {
    try {
        const gitInfo = getTodayGitCommits();
        res.json(gitInfo);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 路由：执行部署
app.post('/deploy', (req, res) => {
    // 检查是否已有部署在进行
    if (deploymentState.isDeploying) {
        const duration = Math.floor((Date.now() - new Date(deploymentState.startTime)) / 1000);
        return res.status(409).json({
            error: '系统正在部署中，请稍后再试',
            deploymentInfo: {
                environment: deploymentState.environment,
                branch: deploymentState.branch,
                duration: `${duration}秒`,
                deployId: deploymentState.deployId
            }
        });
    }
    
    // 设置响应头，支持流式输出
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.setHeader('Transfer-Encoding', 'chunked');
    
    const { environment = 'test', branch } = req.body; // 添加分支参数
    console.log(`开始执行${environment === 'uat' ? 'UAT' : '测试'}环境部署命令...`);
    if (branch) {
        console.log(`目标分支: ${branch}`);
    }
    
    // 设置部署状态
    setDeploymentState(true, environment, branch, '准备开始部署...');
    
    // 执行分支切换和代码拉取
    const envText = environment === 'uat' ? 'UAT' : '测试';
    
    if (branch) {
        res.write(`🔄 正在切换到分支 ${branch} 并拉取最新代码...\n`);
        setDeploymentState(true, environment, branch, `正在切换到分支 ${branch}...`);
        
        // 执行分支切换
        const checkoutProcess = spawn('git', ['checkout', branch], {
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });
        
        // 输出分支切换的标准输出
        checkoutProcess.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('GIT CHECKOUT STDOUT:', output);
            res.write(`Git切换: ${output}`);
            setDeploymentState(true, environment, branch, `分支切换: ${output.trim()}`);
        });
        
        // 输出分支切换的错误信息
        checkoutProcess.stderr.on('data', (data) => {
            const output = data.toString();
            console.log('GIT CHECKOUT STDERR:', output);
            res.write(`Git切换信息: ${output}`);
        });
        
        // 分支切换结束后执行 git pull
        checkoutProcess.on('close', (code) => {
            console.log(`Git checkout 进程结束，退出码: ${code}`);
            if (code !== 0) {
                res.write(`\n⚠️ 分支切换退出码: ${code}，继续执行 git pull...\n`);
            } else {
                res.write(`\n✅ 成功切换到分支 ${branch}！\n`);
                setDeploymentState(true, environment, branch, `成功切换到分支 ${branch}`);
            }
            
            // 执行 git pull
            executeGitPull();
        });
        
        // 分支切换错误处理
        checkoutProcess.on('error', (error) => {
            console.error('Git checkout 进程错误:', error);
            res.write(`\n❌ 分支切换失败: ${error.message}\n`);
            res.write('继续执行 git pull...\n');
            executeGitPull();
        });
    } else {
        res.write(`🔄 正在拉取最新代码并部署到${envText}环境...\n`);
        setDeploymentState(true, environment, branch, `正在拉取最新代码...`);
        executeGitPull();
    }
    
    function executeGitPull() {
        setDeploymentState(true, environment, branch, '正在拉取最新代码...');
        
        const gitPullProcess = spawn('git', ['pull'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });
        
        // 输出 git pull 的标准输出
        gitPullProcess.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('GIT PULL STDOUT:', output);
            res.write(`Git: ${output}`);
            setDeploymentState(true, environment, branch, `代码拉取: ${output.trim()}`);
        });
        
        // 输出 git pull 的错误信息
        gitPullProcess.stderr.on('data', (data) => {
            const output = data.toString();
            console.log('GIT PULL STDERR:', output);
            res.write(`Git 信息: ${output}`);
        });
        
        // git pull 结束后执行部署
        gitPullProcess.on('close', (code) => {
            console.log(`Git pull 进程结束，退出码: ${code}`);
            if (code !== 0) {
                res.write(`\n⚠️ Git pull 退出码: ${code}，继续部署...\n`);
            } else {
                res.write('\n✅ 代码拉取完成！\n');
                setDeploymentState(true, environment, branch, '代码拉取完成，开始构建...');
            }
            
            executeDeploy();
        });
        
        // git pull 进程错误处理
        gitPullProcess.on('error', (error) => {
            console.error('Git pull 进程错误:', error);
            res.write(`\n❌ Git pull 失败: ${error.message}\n`);
            res.write('继续执行部署...\n');
            executeDeploy();
        });
    }
    
    function executeDeploy() {
        // 开始执行部署命令
        const deployCommand = environment === 'uat' ? 'buildAndupload:uat' : 'buildAndupload';
        res.write(`\n🚀 开始构建和部署到${envText}环境...\n`);
        setDeploymentState(true, environment, branch, `正在构建和部署到${envText}环境...`);
        
        const deployProcess = spawn('npm', ['run', deployCommand], {
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });
        
        // 输出标准输出
        deployProcess.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('STDOUT:', output);
            res.write(output);
            
            // 提取关键进度信息
            if (output.includes('building')) {
                setDeploymentState(true, environment, branch, '正在构建项目...');
            } else if (output.includes('upload') || output.includes('上传')) {
                setDeploymentState(true, environment, branch, '正在上传文件...');
            }
        });
        
        // 输出错误信息
        deployProcess.stderr.on('data', (data) => {
            const output = data.toString();
            console.log('STDERR:', output);
            res.write(`错误: ${output}`);
        });
        
        // 进程结束
        deployProcess.on('close', (code) => {
            console.log(`部署进程结束，退出码: ${code}`);
            if (code === 0) {
                res.write('\n✅ 部署成功完成！');
                setDeploymentState(false); // 清除部署状态
            } else {
                res.write(`\n❌ 部署失败，退出码: ${code}`);
                setDeploymentState(false); // 清除部署状态
            }
            res.end();
        });
        
        // 进程错误
        deployProcess.on('error', (error) => {
            console.error('部署进程错误:', error);
            res.write(`\n❌ 进程启动失败: ${error.message}`);
            setDeploymentState(false); // 清除部署状态
            res.end();
        });
    }
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({ error: '服务器内部错误' });
});

// 404 处理
app.use((req, res) => {
    res.status(404).json({ error: '页面未找到' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 部署控制台服务器已启动`);
    console.log(`📱 访问地址: http://localhost:${PORT}`);
    console.log(`🔧 当前工作目录: ${process.cwd()}`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
    console.log(`🔒 并发控制: 已启用`);
    
    // 自动打开浏览器（可选）
    const open = require('child_process').exec;
    open(`open http://localhost:${PORT}`, (error) => {
        if (error) {
            console.log('💡 请手动打开浏览器访问: http://localhost:3000');
        }
    });
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n👋 服务器正在关闭...');
    setDeploymentState(false); // 清除部署状态
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 服务器正在关闭...');
    setDeploymentState(false); // 清除部署状态
    process.exit(0);
}); 