# 房源树选择组件 (RoomTreeSelect)

## 概述

房源树选择组件是一个基于 Arco Design 的树形选择器，用于选择房源。组件支持项目-地块-楼栋-房间的层级结构展示。

## 最新更新

### v2.1.0 - 数据处理优化

- 🐛 **修复数据显示问题**: 处理API返回数据中`id`和`name`为`null`的情况
- 🔧 **智能字段映射**: 当`id`为`null`时自动使用`roomId`，当`name`为`null`时自动使用`roomName`
- 🚀 **去重优化**: 添加基于`roomId`的重复数据去重逻辑
- ✅ **字段扩展**: 支持更多API返回字段，如`propertyType`、`price`、`bottomPrice`等

### v2.0.0 - API接口优化

- ✅ **接口更新**: 从 `getRoomTreeStructure` 更新为 `getRoomTree`
- ✅ **入参优化**: 使用 `RoomTreeQueryDTO` 替代 `SysRoomSimpleDTO`
- ✅ **出参优化**: 使用 `RoomTreeVo[]` 替代 `SysBuildingSimpleVo[]`
- ✅ **数据结构**: 支持标准的树形结构，包含 level 字段标识层级
- ✅ **性能优化**: 增强缓存机制和虚拟滚动支持

## 数据处理说明

### 字段映射逻辑

组件会智能处理API返回的数据：

```typescript
// 当API返回的数据中id和name为null时
{
  "id": null,           // ← 为null
  "name": null,         // ← 为null
  "roomId": "e89e9bc3-af51-4036-9f82-c595fc78c643",
  "roomName": "2-01",
  "level": 4
}

// 组件会自动映射为
{
  "id": "e89e9bc3-af51-4036-9f82-c595fc78c643",  // ← 使用roomId
  "name": "2-01",                                // ← 使用roomName
  "level": 4
}
```

### 去重处理

组件会自动去除基于`roomId`的重复数据：

```typescript
// 原始数据（有重复）
[
  { "roomId": "room1", "roomName": "2-01", "level": 4 },
  { "roomId": "room1", "roomName": "2-01", "level": 4 }, // 重复
  { "roomId": "room2", "roomName": "屋面-01", "level": 4 }
]

// 去重后
[
  { "roomId": "room1", "roomName": "2-01", "level": 4 },
  { "roomId": "room2", "roomName": "屋面-01", "level": 4 }
]
```

## 使用方法

### 基础用法

```vue
<template>
  <room-tree-select 
    v-model="selectedRoom"
    placeholder="请选择房源"
  />
</template>

<script setup>
import { ref } from 'vue'
import RoomTreeSelect from '@/components/roomTreeSelect/index.vue'

const selectedRoom = ref('')
</script>
```

### 高级用法

```vue
<template>
  <room-tree-select 
    v-model="selectedRoom"
    :contract-type="'0'"
    :building-type="'办公'"
    :rent-status="'0'"
    :value-type="'object'"
    :enable-cache="true"
    :enable-virtual-list="true"
    @change="handleRoomChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import RoomTreeSelect from '@/components/roomTreeSelect/index.vue'

const selectedRoom = ref(null)

const handleRoomChange = (value, roomData) => {
  console.log('选中房源:', value)
  console.log('房源数据:', roomData)
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | `string \| number \| object \| null` | `null` | 选中的值 |
| contractType | `string` | - | 合同类型，0-非宿舍,1-宿舍,2-多经,3-日租房 |
| buildingType | `string` | - | 业态 |
| buildingId | `string` | - | 楼栋id |
| rentStatus | `string` | - | 租控状态 0-可租 1-已租 |
| roomName | `string` | - | 房源名称（用于搜索） |
| projectId | `string` | - | 项目ID |
| valueType | `'id' \| 'name' \| 'object'` | `'id'` | 返回值类型 |
| enableVirtualList | `boolean` | `true` | 是否启用虚拟滚动 |
| enableCache | `boolean` | `true` | 是否启用缓存 |
| pageSize | `number` | `50` | 分页大小 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `value` | 值变化时触发 |
| change | `(value, roomData)` | 选择变化时触发 |

## 返回值类型

### valueType: 'id'
返回房源ID字符串
```javascript
"e89e9bc3-af51-4036-9f82-c595fc78c643"
```

### valueType: 'name'
返回房源名称字符串
```javascript
"2-01"
```

### valueType: 'object'
返回包含ID和名称的对象
```javascript
{
  id: "e89e9bc3-af51-4036-9f82-c595fc78c643",
  name: "2-01"
}
```

## 数据结构

### RoomTreeVo
```typescript
interface RoomTreeVo {
  id?: string;                    // 树结构id
  name?: string;                  // 树结构名称
  parentId?: string;              // 父级id
  roomId?: string;                // 房源roomId
  roomName?: string;              // 房源名称
  projectId?: string;             // 项目id
  projectName?: string;           // 项目名称
  parcelId?: string;              // 地块id
  parcelName?: string;            // 地块名称
  buildingId?: string;            // 楼栋id
  buildingName?: string;          // 楼栋名称
  propertyType?: string;          // 物业类型
  rentStatus?: string;            // 租控状态
  rentAreaType?: number;          // 计租面积类型
  rentArea?: number;              // 计租面积
  rentAmount?: number;            // 标准租金
  price?: number;                 // 价格
  bottomPrice?: number;           // 底价
  priceUnit?: number;             // 价格单位
  level?: number;                 // 层级：1-项目 2-地块 3-楼栋 4-房间
  children?: RoomTreeVo[];        // 子节点列表
}
```

## 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| clearCache | - | 清理缓存 |
| refreshData | - | 刷新数据 |

### 使用方法
```vue
<template>
  <room-tree-select ref="roomSelectRef" />
  <a-button @click="clearCache">清理缓存</a-button>
</template>

<script setup>
import { ref } from 'vue'

const roomSelectRef = ref()

const clearCache = () => {
  roomSelectRef.value?.clearCache()
}
</script>
```

## 特性

- 🚀 **性能优化**: 支持虚拟滚动，处理大量数据
- 💾 **智能缓存**: 5分钟缓存机制，减少重复请求
- 🔍 **搜索过滤**: 支持房源名称模糊搜索
- 🎯 **精确选择**: 只允许选择叶子节点（房间）
- 📱 **响应式**: 支持参数变化时自动刷新数据
- 🛡️ **类型安全**: 完整的 TypeScript 类型定义
- 🔧 **智能映射**: 自动处理API数据字段映射
- 🚫 **去重处理**: 自动去除重复的房源数据

## 问题修复记录

### v2.1.0 修复的问题

1. **数据显示问题**: 
   - 问题：API返回的房间节点`id`和`name`字段为`null`，导致无法正确显示
   - 解决：添加智能字段映射，使用`roomId`和`roomName`作为替代

2. **重复数据问题**:
   - 问题：API返回相同`roomId`的重复数据
   - 解决：添加基于`roomId`的去重逻辑

3. **字段缺失问题**:
   - 问题：TypeScript类型定义与实际API返回字段不匹配
   - 解决：扩展`RoomTreeVo`接口，添加`propertyType`、`price`等字段

## 注意事项

1. 组件只允许选择叶子节点（level=4的房间节点）
2. 首次打开下拉框时才会加载数据，提升性能
3. 缓存有效期为5分钟，过期后自动清理
4. 支持防抖搜索，避免频繁请求
5. 当参数变化时，会自动重新加载数据
6. **重要**: 组件会自动处理API数据中的`null`值和重复数据，无需手动处理

## 故障排除

### 常见问题

1. **房源不显示**
   - 检查API返回的数据结构是否正确
   - 确认`level`字段值为4的是房间节点
   - 查看控制台是否有错误信息

2. **选择无效**
   - 确认选择的是叶子节点（房间）
   - 检查`roomId`字段是否存在且不为空

3. **重复数据**
   - 组件会自动去重，如果仍有问题请检查`roomId`字段

4. **缓存问题**
   - 使用`clearCache()`方法清理缓存
   - 或者设置`enableCache: false`禁用缓存 