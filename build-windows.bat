@echo off
title 万样GPT - Windows快速构建
echo 🚀 开始 Windows 优化构建...
echo.

:: 设置优化的环境变量
set NODE_OPTIONS=--max-old-space-size=16384 --expose-gc --max-semi-space-size=2048 --optimize-for-size --memory-reducer --gc-interval=100 --incremental-marking --concurrent-marking
set UV_THREADPOOL_SIZE=128

:: 清理缓存
echo 🧹 清理构建缓存...
if exist dist rmdir /s /q dist
if exist node_modules\.vite-win rmdir /s /q node_modules\.vite-win

:: 开始构建
echo 📦 开始构建...
npm run build:win:turbo

:: 显示结果
echo.
echo ✅ 构建完成！
echo 📊 查看 dist 目录获取构建结果
pause
