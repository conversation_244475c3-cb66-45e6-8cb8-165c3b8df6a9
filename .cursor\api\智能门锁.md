---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/通通锁设备

<a id="opIdgetRoomList_1"></a>

## POST 房源信息列表

POST /ttlock/room/list

根据条件查询房源信息列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "roomName": "string",
  "bindFlag": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomSimpleQueryDTO](#schemaroomsimplequerydto)| 否 |none|

> 返回示例

> 200 Response

```
[{"roomId":"string","roomName":"string","parcelName":"string","buildingName":"string","fullName":"string","ttlockDeviceId":"string","ttlockId":0,"ttlockName":"string"}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomSimpleVo](#schemaroomsimplevo)]|false|none||none|
|» roomId|string|false|none|房间ID|none|
|» roomName|string|false|none|房间名称|none|
|» parcelName|string|false|none|地块名称|none|
|» buildingName|string|false|none|楼栋名称|none|
|» fullName|string|false|none|房间全称|none|
|» ttlockDeviceId|string|false|none|通通锁设备ID|none|
|» ttlockId|integer(int32)|false|none|通通锁ID|none|
|» ttlockName|string|false|none|通通锁名称|none|

<a id="opIdimportRoomBind"></a>

## POST 房源设备绑定导入

POST /ttlock/room/import

通过Excel文件导入房源设备绑定关系

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|string(binary)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexportRoomList"></a>

## POST 导出房源信息列表

POST /ttlock/room/export

导出房源信息列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "roomName": "string",
  "bindFlag": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomSimpleQueryDTO](#schemaroomsimplequerydto)| 否 |none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdgetLockOperateLog"></a>

## POST 智能门锁密码记录

POST /ttlock/log/detail

根据设备id、类型、时间范围查询操作日志

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "deviceId": "string",
  "roomId": "string",
  "password": "string",
  "type": 0,
  "dateType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[TTLockOperateLogQueryDTO](#schemattlockoperatelogquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgenerateTempPassword"></a>

## POST 获取临时密码

POST /ttlock/generate/password

根据设备id获取临时密码

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|deviceId|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdsyncDeviceData"></a>

## POST 同步设备数据

POST /ttlock/device/sync

同步设备数据

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "brandType": 0,
  "username": "string",
  "lockUsername": "string",
  "password": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "accountIdList": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[TTLockAccountQueryDTO](#schemattlockaccountquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdbindRoomDevice"></a>

## POST 保存关联关系

POST /ttlock/device/room/bind

保存房间与通通锁设备的绑定关系

> Body 请求参数

```json
[
  {
    "roomId": "string",
    "roomName": "string",
    "parcelName": "string",
    "buildingName": "string",
    "fullName": "string",
    "ttlockDeviceId": "string",
    "ttlockId": 0,
    "ttlockName": "string"
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomSimpleVo](#schemaroomsimplevo)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdunbindRoomDevice"></a>

## POST 解绑

POST /ttlock/device/room/bind/delete

解绑房间与通通锁设备的绑定关系

> Body 请求参数

```json
[
  "string"
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|array[string]| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdautoBindRoomDevice"></a>

## POST 自动匹配

POST /ttlock/device/room/auto/bind

自动匹配房间与通通锁设备的绑定关系

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetDeviceInfo"></a>

## POST 获取设备信息

POST /ttlock/device/info

根据账号名和设备名称获取设备信息

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "accountList": [
    "string"
  ],
  "deviceName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[TTLockDeviceInfoQueryDTO](#schemattlockdeviceinfoquerydto)| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","projectName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","roomName":"string","propertyType":"string","username":"string","lockId":0,"lockName":"string","lockAlias":"string","lockMac":"string","electricQuantity":0,"roomId":"string","bindTime":"2019-08-24T14:15:22Z","createByName":"string","updateByName":"string","isDel":true,"operateLogList":[{"id":"string","deviceId":"string","roomId":"string","password":"string","type":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","isDel":true}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[TTLockDeviceVo](#schemattlockdevicevo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» projectId|string|false|none|项目ID|none|
|» projectName|string|false|none|项目名称|none|
|» parcelId|string|false|none|地块id|none|
|» parcelName|string|false|none|地块名称|none|
|» buildingId|string|false|none|楼栋ID|none|
|» buildingName|string|false|none|楼栋名称|none|
|» roomName|string|false|none|房间名称|none|
|» propertyType|string|false|none|用途|none|
|» username|string|false|none|账号|账号|
|» lockId|integer(int32)|false|none|锁ID|锁ID|
|» lockName|string|false|none|锁的名称|锁的名称|
|» lockAlias|string|false|none|锁的别名|锁的别名|
|» lockMac|string|false|none|锁mac地址|锁mac地址|
|» electricQuantity|integer(int32)|false|none|锁电量|锁电量|
|» roomId|string|false|none|绑定的房间id|绑定的房间id|
|» bindTime|string(date-time)|false|none|绑定时间|绑定时间|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|» operateLogList|[[TTLockOperateLogVo](#schemattlockoperatelogvo)]|false|none|操作日志列表|none|
|»» 操作日志列表|[TTLockOperateLogVo](#schemattlockoperatelogvo)|false|none|操作日志列表|none|
|»»» id|string|false|none|主键ID|none|
|»»» deviceId|string|false|none|通通锁id|通通锁id|
|»»» roomId|string|false|none|房间id|房间id|
|»»» password|string|false|none|密码|密码|
|»»» type|integer(int32)|false|none|密码类型|密码类型|
|»»» startDate|string(date-time)|false|none|有效期开始时间|有效期开始时间|
|»»» endDate|string(date-time)|false|none|有效期结束时间|有效期结束时间|
|»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»» createTime|string(date-time)|false|none|生成时间|生成时间|
|»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIdexport_1"></a>

## POST 导出询通通锁设备列表

POST /ttlock/device/export

导出询通通锁设备列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|accountList|query|array[string]| 否 | 账号名列表|账号名列表|
|deviceName|query|string| 否 | 设备名称|设备名称|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdadd_8"></a>

## POST 新增通通锁账号

POST /ttlock/account/save

新增通通锁账号

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "brandType": 0,
  "username": "string",
  "password": "string",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[TTLockAccountAddDTO](#schemattlockaccountadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddeviceList"></a>

## GET 查询通通锁设备列表

GET /ttlock/device/list

查询通通锁设备列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|roomOrBuildingName|query|string| 否 | 楼栋/房间名称|楼栋/房间名称|
|username|query|string| 否 | 账号|账号|
|lockId|query|integer(int32)| 否 | 锁ID|锁ID|
|lockName|query|string| 否 | 锁的名称|锁的名称|
|lockAlias|query|string| 否 | 锁的别名|锁的别名|
|lockMac|query|string| 否 | 锁mac地址|锁mac地址|
|electricQuantity|query|integer(int32)| 否 | 锁电量|锁电量|
|roomId|query|string| 否 | 绑定的房间id|绑定的房间id|
|bindTime|query|string(date-time)| 否 | 绑定时间|绑定时间|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgetTTLockDetail"></a>

## GET 智能门锁详情

GET /ttlock/device/detail

根据设备ID查询智能门锁详情

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|deviceId|query|string| 是 ||设备ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_16"></a>

## GET 查询通通锁账号列表

GET /ttlock/account/list

查询通通锁账号列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|brandType|query|integer(int32)| 否 | 品牌名称：1通通锁|品牌名称：1通通锁|
|username|query|string| 否 | 账号|账号|
|lockUsername|query|string| 否 | 账号|账号|
|password|query|string| 否 | 密码|密码|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|accountIdList|query|array[string]| 否 | 主键idList|主键idList|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdaccountRemove"></a>

## DELETE 删除通通锁账号

DELETE /ttlock/account/delete

删除通通锁账号

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||通通锁账号ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_TTLockAccountAddDTO">TTLockAccountAddDTO</h2>

<a id="schemattlockaccountadddto"></a>
<a id="schema_TTLockAccountAddDTO"></a>
<a id="tocSttlockaccountadddto"></a>
<a id="tocsttlockaccountadddto"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "brandType": 0,
  "username": "string",
  "password": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|brandType|integer(int32)|false|none|品牌名称：1通通锁|品牌名称：1通通锁|
|username|string|false|none|账号|账号|
|password|string|false|none|密码|密码|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_RoomSimpleQueryDTO">RoomSimpleQueryDTO</h2>

<a id="schemaroomsimplequerydto"></a>
<a id="schema_RoomSimpleQueryDTO"></a>
<a id="tocSroomsimplequerydto"></a>
<a id="tocsroomsimplequerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "roomName": "string",
  "bindFlag": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|projectId|string|false|none|项目ID|项目ID|
|parcelId|string|false|none|地块ID|地块ID|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|roomName|string|false|none|房间名称|房间名称|
|bindFlag|integer(int32)|false|none|绑定标识|0-未绑定，1-已绑定|

<h2 id="tocS_RoomSimpleVo">RoomSimpleVo</h2>

<a id="schemaroomsimplevo"></a>
<a id="schema_RoomSimpleVo"></a>
<a id="tocSroomsimplevo"></a>
<a id="tocsroomsimplevo"></a>

```json
{
  "roomId": "string",
  "roomName": "string",
  "parcelName": "string",
  "buildingName": "string",
  "fullName": "string",
  "ttlockDeviceId": "string",
  "ttlockId": 0,
  "ttlockName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roomId|string|false|none|房间ID|none|
|roomName|string|false|none|房间名称|none|
|parcelName|string|false|none|地块名称|none|
|buildingName|string|false|none|楼栋名称|none|
|fullName|string|false|none|房间全称|none|
|ttlockDeviceId|string|false|none|通通锁设备ID|none|
|ttlockId|integer(int32)|false|none|通通锁ID|none|
|ttlockName|string|false|none|通通锁名称|none|

<h2 id="tocS_TTLockAccountQueryDTO">TTLockAccountQueryDTO</h2>

<a id="schemattlockaccountquerydto"></a>
<a id="schema_TTLockAccountQueryDTO"></a>
<a id="tocSttlockaccountquerydto"></a>
<a id="tocsttlockaccountquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "brandType": 0,
  "username": "string",
  "lockUsername": "string",
  "password": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "accountIdList": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|brandType|integer(int32)|false|none|品牌名称：1通通锁|品牌名称：1通通锁|
|username|string|false|none|账号|账号|
|lockUsername|string|false|none|账号|账号|
|password|string|false|none|密码|密码|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|accountIdList|[string]|false|none|主键idList|主键idList|
|» 主键idList|string|false|none|主键idList|主键idList|

<h2 id="tocS_TTLockOperateLogQueryDTO">TTLockOperateLogQueryDTO</h2>

<a id="schemattlockoperatelogquerydto"></a>
<a id="schema_TTLockOperateLogQueryDTO"></a>
<a id="tocSttlockoperatelogquerydto"></a>
<a id="tocsttlockoperatelogquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "deviceId": "string",
  "roomId": "string",
  "password": "string",
  "type": 0,
  "dateType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|deviceId|string|false|none|通通锁id|通通锁id|
|roomId|string|false|none|房间id|房间id|
|password|string|false|none|密码|密码|
|type|integer(int32)|false|none|密码类型|密码类型|
|dateType|integer(int32)|false|none|日期类型：1-近7天，2-近半月，3-近一月，4-近三月，5-近半年，6-近一年|日期类型：1-近7天，2-近半月，3-近一月，4-近三月，5-近半年，6-近一年|
|startDate|string(date-time)|false|none|有效期开始时间|有效期开始时间|
|endDate|string(date-time)|false|none|有效期结束时间|有效期结束时间|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_TTLockDeviceInfoQueryDTO">TTLockDeviceInfoQueryDTO</h2>

<a id="schemattlockdeviceinfoquerydto"></a>
<a id="schema_TTLockDeviceInfoQueryDTO"></a>
<a id="tocSttlockdeviceinfoquerydto"></a>
<a id="tocsttlockdeviceinfoquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "accountList": [
    "string"
  ],
  "deviceName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|accountList|[string]|false|none|账号名列表|账号名列表|
|» 账号名列表|string|false|none|账号名列表|账号名列表|
|deviceName|string|false|none|设备名称|设备名称|

<h2 id="tocS_TTLockDeviceVo">TTLockDeviceVo</h2>

<a id="schemattlockdevicevo"></a>
<a id="schema_TTLockDeviceVo"></a>
<a id="tocSttlockdevicevo"></a>
<a id="tocsttlockdevicevo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "roomName": "string",
  "propertyType": "string",
  "username": "string",
  "lockId": 0,
  "lockName": "string",
  "lockAlias": "string",
  "lockMac": "string",
  "electricQuantity": 0,
  "roomId": "string",
  "bindTime": "2019-08-24T14:15:22Z",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "operateLogList": [
    {
      "id": "string",
      "deviceId": "string",
      "roomId": "string",
      "password": "string",
      "type": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目ID|none|
|projectName|string|false|none|项目名称|none|
|parcelId|string|false|none|地块id|none|
|parcelName|string|false|none|地块名称|none|
|buildingId|string|false|none|楼栋ID|none|
|buildingName|string|false|none|楼栋名称|none|
|roomName|string|false|none|房间名称|none|
|propertyType|string|false|none|用途|none|
|username|string|false|none|账号|账号|
|lockId|integer(int32)|false|none|锁ID|锁ID|
|lockName|string|false|none|锁的名称|锁的名称|
|lockAlias|string|false|none|锁的别名|锁的别名|
|lockMac|string|false|none|锁mac地址|锁mac地址|
|electricQuantity|integer(int32)|false|none|锁电量|锁电量|
|roomId|string|false|none|绑定的房间id|绑定的房间id|
|bindTime|string(date-time)|false|none|绑定时间|绑定时间|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|operateLogList|[[TTLockOperateLogVo](#schemattlockoperatelogvo)]|false|none|操作日志列表|none|

<h2 id="tocS_TTLockOperateLogVo">TTLockOperateLogVo</h2>

<a id="schemattlockoperatelogvo"></a>
<a id="schema_TTLockOperateLogVo"></a>
<a id="tocSttlockoperatelogvo"></a>
<a id="tocsttlockoperatelogvo"></a>

```json
{
  "id": "string",
  "deviceId": "string",
  "roomId": "string",
  "password": "string",
  "type": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "isDel": true
}

```

操作日志列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|deviceId|string|false|none|通通锁id|通通锁id|
|roomId|string|false|none|房间id|房间id|
|password|string|false|none|密码|密码|
|type|integer(int32)|false|none|密码类型|密码类型|
|startDate|string(date-time)|false|none|有效期开始时间|有效期开始时间|
|endDate|string(date-time)|false|none|有效期结束时间|有效期结束时间|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|生成时间|生成时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

