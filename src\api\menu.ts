import http from './index';
import type { RouteRecordNormalized } from 'vue-router';
const systemUrl = '/business-platform'
export function getMenuTreeList() {
    return http.get(`${systemUrl}/menu/treeselect`);
}
export function getMenuList(params: any) {
    return http.get(`${systemUrl}/menu/list`, { params });
}
export function addMenu(data: any) {
    return http.post(`${systemUrl}/menu`, data);
}
export function updateMenu(data: any) {
    return http.put(`${systemUrl}/menu`, data);
}
export function deleteMenu(data: any) {
    return http.delete(`${systemUrl}/menu/${data}`);
}
export function getMenuById(data: any) {
    return http.get(`${systemUrl}/menu/${data}`);
}
export function getMenuDetails(menuId: string) {
    return http.get(`${systemUrl}/menu/details/${menuId}`);
}
export function getMenuPermissions(menuId: string) {
    return http.get(`${systemUrl}/menu/permissions/${menuId}`);
}
