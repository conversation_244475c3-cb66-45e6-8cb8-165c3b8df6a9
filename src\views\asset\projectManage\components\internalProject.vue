<template>
    <MapDialog :visibleDialog="mapDialogVisible" :mapCenter="{ lng: formData.lng, lat: formData.lat }"
        @confirmDialog="handleMapDialogConfirm" @closeDialog="handleMapDialogClose" />
    <a-drawer class="common-drawer" v-model:visible="visible" title="内部项目管理" @cancel="handleCancel">
        <div class="internal-project-container">
            <!-- 顶部菜单 -->
            <a-menu :style="{ width: '160px', height: '100%' }" mode="vertical" :selected-keys="[currentTab]"
                @menu-item-click="handleMenuClick">
                <a-menu-item key="basic">基本信息</a-menu-item>
                <a-menu-item key="building">设置楼栋</a-menu-item>
                <a-menu-item key="room">设置房源</a-menu-item>
            </a-menu>

            <!-- 基本信息内容 -->
            <div v-show="currentTab === 'basic'" class="content-section">
                <div class="basic-info">
                    <a-form :model="formData" layout="horizontal" auto-label-width ref="formRef">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item label="主数据对应项目">
                                    <a-input v-model="formData.mainProject" disabled />
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item label="项目名称" :required="modalType !== 'view'">
                                    <a-input v-model="formData.projectName" disabled />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="projectCode" label="项目编码"
                                    :rules="[{ required: modalType !== 'view', message: '请输入项目编码' }]">
                                    <a-input v-model="formData.projectCode" placeholder="请输入项目编码"
                                        :disabled="modalType === 'view'" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="name" label="项目简称"
                                    :rules="[{ required: modalType !== 'view', message: '请输入项目简称' }]">
                                    <a-input v-model="formData.name" placeholder="请输入项目简称"
                                        :disabled="modalType === 'view'" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item label="省市区" field="area"
                                    :rules="[{ required: modalType !== 'view', message: '请选择省市区' }]">
                                    <a-select v-model="formData.area" disabled>
                                        <a-option :value="formData.area">{{ formData.area }}</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item label="项目定位" :rules="[{ required: modalType !== 'view', message: '请输入项目定位' }]">
                                    <a-input v-model="formData.projectLocation" disabled>
                                        <!-- <template #suffix>
                                            <icon-location style="cursor:pointer" @click="handleLocationClick" />
                                        </template> -->
                                    </a-input>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item label="项目地址">
                                    <a-input v-model="formData.address" disabled placeholder="请输入项目地址" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>

                    <!-- 分期信息 -->
                    <div class="phase-info">
                        <SectionTitle title="分期信息" />
                        <div class="phase-buttons" v-if="formData.phase">
                            <div class="phase-btn-item" v-for="item in formData.phase.split(',')">
                                <div class="phase-btn">{{ item }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="basic-info">
                        <SectionTitle title="产权信息" />
                        <a-form :model="formData" layout="horizontal" class="three-column-form" auto-label-width>
                            <a-row :gutter="16">
                                <a-col :span="8">
                                    <a-form-item label="主数据项目类型">
                                        <a-select v-model="formData.projectType" :disabled="true"
                                            class="disabled-select">
                                            <a-option :value="formData.projectType">{{ formData.projectType
                                                }}</a-option>
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item label="项目资产分类">
                                        <a-select v-model="formData.assetType" :disabled="modalType === 'view'">
                                            <a-option v-for="dict in asset_type" :key="dict.value"
                                                :value="dict.value">
                                                {{ dict.label }}
                                            </a-option>
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item label="产权单位">
                                        <a-input v-model="formData.propertyUnit" :disabled="true" />
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </a-form>
                    </div>

                    <SectionTitle title="地块信息" />
                    <a-table :columns="landColumns" :data="landData" :pagination="false" :bordered="{cell: true}">
                        <template #mainUsage="{ record }">
                            {{ record.mainUsage === '1' || record.mainUsage === 1 ? '工业用地' :
                                record.mainUsage === '2' || record.mainUsage === 2 ? '商业用地' :
                                    record.mainUsage }}
                        </template>
                        <template #usage="{ record }">
                            <a-select v-model="record.usage" :disabled="modalType === 'view'" size="mini">
                                <a-option v-for="item in landUsageTypes" :key="item.value" :value="item.value">
                                    {{ item.label }}
                                </a-option>
                            </a-select>
                        </template>
                        <template #address="{ record }">
                            <a-input v-model="record.address" placeholder="请输入合同签约地址"
                                :disabled="modalType === 'view'"  size="mini"/>
                        </template>
                        <template #blockName="{ record }">
                            <a-input v-model="record.blockName" :disabled="modalType === 'view'"  size="mini" />
                        </template>
                    </a-table>
                </div>
            </div>
            <!-- 楼栋设置内容 -->
            <div v-show="currentTab === 'building'" class="content-section">
                <div class="building-settings">
                    <SectionTitle title="楼栋信息" />
                    <div style="color: #f5222d; margin-bottom: .5rem; font-size: .875rem;">
                        (说明：若车位所在楼栋在主数据未建立，请在下方单独添加)
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                        <a-select v-model="selectedBlock" style="width: 220px" placeholder="请选择地块"
                            @change="handleBuildingTabParcelChange" allow-clear>
                            <a-option v-for="item in parcelOptions" :key="item.value" :value="item.value">{{ item.label
                                }}</a-option>
                        </a-select>
                        <a-input v-model="searchBuilding" placeholder="请输入楼栋/房源名称搜索" style="width: 220px">
                            <template #suffix>
                                <icon-search style="cursor:pointer" @click="handleSearchBuilding" />
                            </template>
                        </a-input>
                        <a-button v-if="modalType !== 'view'" type="primary" style="margin-left:auto"
                            @click="handleAddBuilding">添加楼栋</a-button>
                    </div>
                    <a-table :columns="buildingColumns" :data="filteredBuildingData" :pagination="buildingPagination" :bordered="{cell: true}"
                        @selection-change="handleBuildingSelectionChange" @page-change="handleBuildingPageChange"
                        @page-size-change="handleBuildingPageSizeChange">
                        <template #operations="{ record }">
                            <a-space>
                                <a-button v-if="record.source === '手动添加' && modalType !== 'view'" type="text"
                                    status="danger" @click="handleDeleteBuilding(record)" size="mini">删除</a-button>
                            </a-space>
                        </template>
                        <!-- Make sure we use the block column correctly -->
                        <template #block="{ record }">
                            {{ record.block }}
                        </template>
                    </a-table>
                </div>
            </div>
            <!-- 车位设置内容 -->
            <div v-show="currentTab === 'room'" class="content-section">
                <div class="parking-settings">
                    <SectionTitle title="房源信息" />
                    <div style="color: #f5222d; margin-bottom: .5rem; font-size: .875rem;">
                        (说明：可在下方单独添加车位)
                    </div>
                    <div class="room-info">
                        <a-select v-model="selectedBlock" style="width: 220px" placeholder="请选择地块" allow-clear
                            @change="handleRoomTabParcelChange">
                            <a-option v-for="item in parcelOptions" :key="item.value" :value="item.value">{{ item.label
                                }}</a-option>
                        </a-select>
                        <a-input v-model="searchRoom" placeholder="请输入楼栋/房源名称搜索" style="width: 220px">
                            <template #suffix>
                                <icon-search style="cursor:pointer" @click="handleSearchRoom" />
                            </template>
                        </a-input>
                        <div v-if="modalType !== 'view'" class="button-group button-group2">
                            <a-button type="primary" @click="openAddParkingDialog">添加车位</a-button>
                            <a-button @click="handleDownloadTemplate">下载模板</a-button>
                            <a-button @click="handleImportParking">导入车位</a-button>
                            <a-button @click="handleBatchDeleteParking"
                                :disabled="!hasValidSelectedRooms">批量删除</a-button>
                        </div>
                    </div>

                    <a-table :columns="parkingColumns" :data="parkingData" row-key="index"
                        :row-selection="modalType === 'view' ? undefined : rowSelection"
                        v-model:selectedKeys="selectedParkingKeys"
                        @selection-change="handleParkingSelectionChange" :pagination="roomPagination"
                        @page-change="handleRoomPageChange" @page-size-change="handleRoomPageSizeChange" :scroll="{ x: 1 }">
                        <template #areaType="{ record }">
                            {{ getAreaTypeText(record.areaType) }}
                        </template>
                        <template #productType="{ record }">
                            {{ getProductTypeText(record.productType) }}
                        </template>
                        <template #operations="{ record }" >
                            <a-space>
                                <a-button v-if="record.source !== '主数据' && modalType !== 'view'" type="text"
                                    @click="openEditParkingDialog(record)" size="mini">编辑</a-button>
                                <a-button v-if="record.source !== '主数据' && modalType !== 'view'" type="text"
                                    status="danger" @click="handleDeleteParking(record)" size="mini">删除</a-button>
                            </a-space>
                        </template>
                    </a-table>
                </div>
            </div>
        </div>

        <template #footer>
            <a-space>
                <a-button @click="handleCancel">{{ modalType === 'view' ? '关闭' : '取消' }}</a-button>
                <a-button v-if="currentTab === 'basic' && modalType !== 'view'" type="primary" @click="handleSave"
                    :loading="saveLoading">保存</a-button>
            </a-space>
        </template>
    </a-drawer>

    <!-- 车位弹窗 -->
    <a-modal v-model:visible="parkingDialogVisible" :title=dialogTitle :width="'800px'"
        @cancel="handleParkingDialogCancel">
        <div class="internal-project-container">
            <a-form ref="parkingDialogFormRef" :model="parkingDialogForm" layout="horizontal"
                :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" :style="{ width: '100%' }"
                auto-label-width>
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="房源名称" field="name" required
                            :rules="[
                              { required: true, message: '请输入房源名称' },
                              { max: 30, message: '房源名称不能超过30个字符' }
                            ]">
                            <a-input v-model="parkingDialogForm.name" :max-length="30" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="地块" field="block" required :rules="[{ required: true, message: '请选择地块' }]">
                            <a-select v-model="parkingDialogForm.block" @change="handleParcelChange">
                                <a-option v-for="item in parcelOptions" :key="item.value" :value="item.value">{{
                                    item.label
                                    }}</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="楼栋" field="building" required
                            :rules="[{ required: true, message: '请选择楼栋' }]">
                            <a-select v-model="parkingDialogForm.building" @change="handleBuildingChange">
                                <a-option v-for="item in buildingOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="楼层" field="floor" required :rules="[{ required: true, message: '请选择楼层' }]">
                            <a-select v-model="parkingDialogForm.floor" :disabled="!parkingDialogForm.building">
                                <a-option v-for="item in floorOptions" :key="item.value" :value="item.value">{{
                                    item.label
                                    }}</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <!-- {{ productTypeOptions }} -->
                        <a-form-item label="产品类型" field="productType" required
                            :rules="[{ required: true, message: '请选择产品类型' }]" disabled>
                            <a-select v-model="parkingDialogForm.productType">
                                <a-option v-for="item in productTypeOptions" :key="item.value" :value="item.value">
                                    {{ item.label }}
                                </a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="面积类型" field="areaType" required
                            :rules="[{ required: true, message: '请选择面积类型' }]">
                            <a-select v-model="parkingDialogForm.areaType">
                                <a-option value="1">实测</a-option>
                                <a-option value="2">预测</a-option>
                                <a-option value="3">设计</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="建筑面积(㎡)" field="buildingArea" required
                            :rules="[{ required: true, message: '请输入建筑面积' }]">
                            <a-input-number v-model="parkingDialogForm.buildingArea">
                                <template #append>㎡</template>
                            </a-input-number>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="套内面积(㎡)" field="innerArea" required
                            :rules="[{ required: true, message: '请输入套内面积' }]">
                            <a-input-number v-model="parkingDialogForm.innerArea">
                                <template #append>㎡</template>
                            </a-input-number>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="是否可售" field="saleable" required
                            :rules="[{ required: true, message: '请选择是否可售' }]">
                            <a-select v-model="parkingDialogForm.saleable">
                                <a-option value="是">是</a-option>
                                <a-option value="否">否</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="是否公司自持" field="shared" required
                            :rules="[{ required: true, message: '请选择是否公司自持' }]">
                            <a-select v-model="parkingDialogForm.shared">
                                <a-option value="是">是</a-option>
                                <a-option value="否">否</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="自持物业类型" field="selfHeldType" required
                            :rules="[{ required: true, message: '请选择自持物业类型' }]">
                            <a-select v-model="parkingDialogForm.selfHeldType" @change="handleSelfHeldTypeChange">
                                <a-option value="非自持">非自持</a-option>
                                <a-option value="自持">自持</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-if="parkingDialogForm.selfHeldType === '自持'">
                        <a-form-item label="自持到期日期" field="selfHeldExpireDate" required>
                            <a-date-picker v-model="parkingDialogForm.selfHeldExpireDate" style="width: 100%" />
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </div>

        <template #footer>
            <a-space>
                <a-button @click="handleParkingDialogCancel">取消</a-button>
                <a-button type="primary" @click="handleParkingDialogOk" :loading="saveLoading">保存</a-button>
            </a-space>
        </template>
    </a-modal>

    <!-- 新增楼栋弹窗 -->
    <a-modal v-model:visible="buildingDialogVisible" :title="buildingDialogTitle" :width="'580px'"
        @cancel="handleBuildingDialogCancel">
        <div class="internal-project-container">
            <a-form ref="buildingDialogFormRef" :model="buildingDialogForm" layout="horizontal"
                :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" :style="{ width: '100%' }">
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="楼栋名称" field="buildingName" required
                            :rules="[
                              { required: true, message: '请输入楼栋名称' },
                              { max: 30, message: '楼栋名称不能超过30个字符' }
                            ]">
                            <a-input v-model="buildingDialogForm.buildingName" placeholder="请输入楼栋名称" :max-length="30" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="所属地块" field="block" required
                            :rules="[{ required: true, message: '请选择地块' }]">
                            <a-select v-model="buildingDialogForm.block" placeholder="请选择地块">
                                <a-option v-for="item in parcelOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="地上楼层数" field="upFloorNums" required
                            :rules="[{ required: true, message: '请输入地上楼层数' }]">
                            <a-input-number v-model="buildingDialogForm.upFloorNums" :min="0" placeholder="请输入地上楼层数"
                                style="width: 100%" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="地下楼层数" field="underFloorNums" required
                            :rules="[{ required: true, message: '请输入地下楼层数' }]">
                            <a-input-number v-model="buildingDialogForm.underFloorNums" :min="0" placeholder="请输入地下楼层数"
                                style="width: 100%" />
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </div>

        <template #footer>
            <a-space>
                <a-button @click="handleBuildingDialogCancel">取消</a-button>
                <a-button type="primary" @click="handleBuildingDialogOk" :loading="saveLoading">保存</a-button>
            </a-space>
        </template>
    </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import MapDialog from '@/components/MapDialog/mapDialog.vue';
import SectionTitle from '@/components/sectionTitle/index.vue';
import { getMdmProjectDetail } from '@/api/asset/masterData';
import {
    addProject, saveBuilding, saveRoom, getProjectDetail, getBuildingList, getRoomList, downloadRoomTemplate,
    getParcelList, getBuildingDropdownList, getFloorDropdownList, deleteRoom, deleteBuilding
} from '@/api/asset/projectManage';
import { useDict } from '@/utils/dict';
// 导入房源模板下载和导入接口
import { downloadParkTemplate, importParkTemplate } from '@/api/project';
// 引入导出工具函数
import { exportExcel, importExcelFile } from '@/utils/exportUtil';

// 面积类型转换函数
const getAreaTypeText = (areaType: string | number | undefined): string => {
	if (areaType === undefined || areaType === null || areaType === '') {
		return '';
	}
	
	const areaTypeMap: Record<string, string> = {
		'1': '实测',
		'2': '预测',
		'3': '设计'
	};
	
	const typeStr = String(areaType);
	return areaTypeMap[typeStr] || typeStr;
};

// 产品类型转换函数
const getProductTypeText = (productType: string | undefined): string => {
	if (productType === undefined || productType === null || productType === '') {
		return '';
	}
	
	// 从productTypeOptions中查找对应的标签
	const productTypeItem = productTypeOptions.value?.find((item: any) => item.value === productType);
	return productTypeItem ? productTypeItem.label : productType;
};

const { asset_type, product_type } = useDict('asset_type', 'product_type');
// 用地性质字典
const landUsageTypes = [
    { value: '1', label: '工业用地' },
    { value: '2', label: '商业用地' }
];

const visible = ref(false);
const currentTab = ref('basic');
const basicInfoSaved = ref(false);
const formRef = ref<any>(null);
const saveLoading = ref(false);
const deleteLoading = ref(false);

// 存储完整的项目详情数据
const completeProjectData = reactive<any>({
    project: {},
    stages: [],
    parcels: [],
    buildings: [],
    floors: [],
    rooms: []
});

// 标识当前是新增还是编辑模式
const isEditMode = ref(false);

// 地图弹窗控制
const mapDialogVisible = ref(false);
const mapLocation = ref({ lng: undefined, lat: undefined });

const handleMenuClick = (key: string) => {
    // 如果是查看模式，允许自由切换标签
    if (modalType.value === 'view') {
        currentTab.value = key;

        // 如果切换到房源标签，清空选中状态并加载房源数据
        if (key === 'room') {
            selectedParkingKeys.value = [];
            if (formData.projectId) {
                loadRoomData();
            }
        }

        // 如果切换到楼栋标签，加载楼栋数据
        if (key === 'building' && formData.projectId) {
            loadBuildingData(formData.projectId);
        }
        return;
    }

    // 如果基本信息未保存，且要切换到其他标签，阻止切换并提示
    if (key !== 'basic' && !basicInfoSaved.value) {
        Message.warning('请先保存基本信息');
        return;
    }

    // 切换到相应标签
    currentTab.value = key;

    // 如果切换到房源标签，清空选中状态并加载房源数据
    if (key === 'room') {
        selectedParkingKeys.value = [];
        if (formData.projectId) {
            loadRoomData();
        }
    }

    // 如果切换到楼栋标签，加载楼栋数据
    if (key === 'building' && formData.projectId) {
        loadBuildingData(formData.projectId);
    }
};

// 添加分期数据到表单
const formData = reactive({
    mainProject: '',
    projectName: '',
    projectCode: '',
    name: '',
    area: '',
    lng: 120.6994,
    lat: 27.9949,
    projectLocation: '',
    address: '',
    projectType: '',
    assetType: '',
    propertyUnit: '',
    phase: '',
    projectId: '',
    mdmId: ''
});

// 地块表格列定义
const landColumns = [
    { title: '序号', dataIndex: 'index', width: 50, align: 'center', ellipsis: true, tooltip: true },
    { title: '主数据地块名称', dataIndex: 'mdmParcelName', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '地块名称', dataIndex: 'blockName', slotName: 'blockName', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '主数据用地性质', dataIndex: 'mdmNatureName', width: 110, align: 'center', ellipsis: true, tooltip: true },
    { title: '用地性质', dataIndex: 'usage', slotName: 'usage', width: 90, align: 'center'},
    { title: '主数据地块地址', dataIndex: 'mdmAddress', width: 110, align: 'center', ellipsis: true, tooltip: true },
    { title: '合同签约地址', dataIndex: 'address', slotName: 'address', width: 110, align: 'center'},
    { title: '所属分期', dataIndex: 'phase', width: 90, align: 'center', ellipsis: true, tooltip: true },
    // { title: '操作', slotName: 'operations', width: 150 }
];

// 地块数据类型定义
interface LandRecord {
    index: number;
    id?: string;
    blockName: string;
    mainUsage: string;
    usage: string;
    mdmNatureName?: string; // 主数据用地性质名称
    mdmAddress: string;
    address: string;
    phase: string;
    mdmParcelName: string;
}

// 地块数据
const landData = reactive<LandRecord[]>([]);

// 原始数据，用于跟踪删除的项目
const originalParcels = ref<any[]>([]);
const originalStages = ref<any[]>([]);

// 车位表单数据
const parkingForm = reactive({
    blockName: '',
    upFloorNums: 1,
    underFloorNums: 0
});

// 选中的地块
const selectedBlock = ref('');

// 楼栋表格列定义
const buildingColumns = [
    { title: '序号', dataIndex: 'index', width: 70, align: 'center' },
    {
        title: '楼栋名称',
        dataIndex: 'buildingName',
        align: 'center',
        width: 100,
        ellipsis: true,
        tooltip: true
    },
    {
        title: '所属地块',
        dataIndex: 'block',
        slotName: 'block',
        align: 'center',
        width: 100,
        ellipsis: true,
        tooltip: true
    },
    {
        title: '地上楼层数',
        dataIndex: 'upFloorNums',
        align: 'center',
        width: 100,
        ellipsis: true,
        tooltip: true
    },
    {
        title: '地下楼层数',
        dataIndex: 'underFloorNums',
        align: 'center',
        width: 100,
        ellipsis: true,
        tooltip: true
    },
    { title: '来源', dataIndex: 'source', align: 'center', width: 100, ellipsis: true, tooltip: true },
    { title: '操作', slotName: 'operations', width: 110, fixed: 'right', align: 'center' }
];

// 楼栋数据类型
interface BuildingRecord {
    index: number;
    buildingName: string;
    block: string;
    blockId: string; // Add this to store the block ID
    upFloorNums: number;
    underFloorNums: number;
    source?: string;
    [key: string]: any;
}

// 楼栋数据
const buildingData = ref<BuildingRecord[]>([]);

// 车位表格列定义
const parkingColumns = [
    { title: '序号', dataIndex: 'index', width: 70, align: 'center' },
    { title: '房源名称', dataIndex: 'name', width: 140, align: 'center', ellipsis: true, tooltip: true},
    { title: '所属地块', dataIndex: 'block', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '楼栋', dataIndex: 'building', width: 130, align: 'center', ellipsis: true, tooltip: true },
    { title: '楼层', dataIndex: 'floor', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '面积类型', dataIndex: 'areaType', slotName: 'areaType', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '建筑面积(㎡)', dataIndex: 'buildingArea', width: 120, align: 'center', ellipsis: true, tooltip: true },
    { title: '套内面积(㎡)', dataIndex: 'innerArea', width: 120, align: 'center', ellipsis: true, tooltip: true },
    { title: '产品类型', dataIndex: 'productType', slotName: 'productType', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '是否可售', dataIndex: 'saleable', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '是否公司自持', dataIndex: 'shared', width: 120, align: 'center', ellipsis: true, tooltip: true },
    { title: '自持物业类型', dataIndex: 'selfHeldType', width: 120, align: 'center', ellipsis: true, tooltip: true },
    { title: '来源', dataIndex: 'source', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '操作', slotName: 'operations', width: 120, fixed: 'right', align: 'center' }
];

// 车位数据
const parkingData = ref<any[]>([]);

// 分页配置
const roomPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100]
});

const buildingPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100]
});

// 加载房源数据
const loadRoomData = async () => {
    if (!formData.projectId) return;

    try {
        // 清空选中状态，确保重新加载数据后状态正确
        selectedParkingKeys.value = [];
        const params = {
            projectId: formData.projectId,
            pageNum: roomPagination.current,
            pageSize: roomPagination.pageSize,
            parcelId: selectedBlock.value || undefined,
            buildingOrRoomName: searchRoom.value || undefined
        };

        const res = await getRoomList(params);
        console.log('房源列表响应:', res);

        if (res && res.rows && Array.isArray(res.rows)) {
            roomPagination.total = res.total || 0;

                        // 设置房源数据
            parkingData.value = res.rows.map((room: any, index: number) => {
                // 调试日志：查看面积类型数据
                console.log(`房源 ${room.roomName} 面积类型数据:`, {
                    areaType: room.areaType,
                    areaTypeName: room.areaTypeName,
                    areaTypeType: typeof room.areaType,
                    finalAreaType: room.areaType || room.areaTypeName || ''
                });
                
                return {
                    index: ((roomPagination.current - 1) * roomPagination.pageSize + index + 1).toString(),
                    id: room.id || '',
                    roomId: room.roomId || '',
                    name: room.roomName || '',
                    block: room.parcelName || '',
                    blockId: room.parcelId || '', // 保存地块ID
                    building: room.buildingName || '',
                    buildingId: room.buildingId || '', // 保存楼栋ID
                    floor: room.floorName || '',
                    floorId: room.floorId || '', // 保存楼层ID
                    areaType: room.areaType || room.areaTypeName || '',
                    buildingArea: room.buildArea || 0,
                    innerArea: room.innerArea || 0,
                    productType: room.productType || '',
                    saleable: room.isSale === true || room.isSale === 1 ? '是' : '否',
                    shared: room.isCompanySelf === true || room.isCompanySelf === 1 ? '是' : '否',
                    selfHeldType: room.propertyType === 2 ? '自持' : '非自持',
                    selfHeldExpireDate: room.selfHoldingTime || '',
                    source: room.mdmRoomId ? '主数据' : '手动添加'
                };
            });
        } else {
            parkingData.value = [];
            roomPagination.total = 0;
        }
    } catch (error) {
        console.error('获取房源列表失败:', error);
        // API调用失败，错误信息由拦截器处理
        parkingData.value = [];
    }
};

// 房源分页变化
const handleRoomPageChange = (current: number) => {
    roomPagination.current = current;
    // 清空选中状态
    selectedParkingKeys.value = [];
    loadRoomData();
};

// 房源每页条数变化
const handleRoomPageSizeChange = (pageSize: number) => {
    roomPagination.pageSize = pageSize;
    roomPagination.current = 1;
    // 清空选中状态
    selectedParkingKeys.value = [];
    loadRoomData();
};

// 地块下拉选项
const blockOptions = computed(() => {
    const set = new Set<string>();
    landData.forEach(item => set.add(item.blockName));
    return Array.from(set);
});

// 楼栋搜索
const searchBuilding = ref('');

const searchRoom = ref('');

// 楼栋过滤
const filteredBuildingData = computed<BuildingRecord[]>(() => {
    let data = buildingData.value;
    if (selectedBlock.value) {
        console.log('当前选中地块ID:', selectedBlock.value);
        console.log('过滤前楼栋数据:', data.map(item => ({
            buildingName: item.buildingName,
            blockId: item.blockId,
            block: item.block
        })));

        data = data.filter(item => item.blockId === selectedBlock.value);

        console.log('过滤后楼栋数据:', data.map(item => ({
            buildingName: item.buildingName,
            blockId: item.blockId,
            block: item.block
        })));
    }
    return data;
});

const handleSearchBuilding = () => {
    // 触发
    loadBuildingData(formData.projectId);
};
const handleSearchRoom = () => {
    // 清空选中状态
    selectedParkingKeys.value = [];
    // 处理方法
    loadRoomData();
};
const handleCancel = () => {
    visible.value = false;
    // 清除所有数据
    resetAllData();
};

// 处理保存楼栋
const handleSaveBuilding = async () => {
    if (!formData.projectId) {
        Message.warning('请先保存基本信息');
        return;
    }

    saveLoading.value = true;
    try {
        // 验证是否有楼栋数据
        if (buildingData.value.length === 0) {
            Message.warning('请添加楼栋信息');
            return;
        }

        // 循环调用保存楼栋接口
        for (const building of buildingData.value) {
            const saveData = {
                projectId: formData.projectId,
                buildingName: building.buildingName,
                buildingId: building.buildingId || '',
                parcelId: building.block,
                upFloorNums: building.upFloorNums,
                underFloorNums: building.underFloorNums
            };

            // 逐个保存楼栋信息
            await saveBuilding(saveData);
        }

        console.log('楼栋保存成功');

        // 显示保存成功消息
        Message.success('楼栋保存成功');

        // 切换到房源标签
        currentTab.value = 'room';
    } catch (error) {
        console.error('楼栋保存失败:', error);
        // API调用失败，错误信息由拦截器处理
    } finally {
        saveLoading.value = false;
    }
};

// 处理保存房源
const handleSaveRoom = async () => {
    if (!formData.projectId) {
        Message.warning('请先保存基本信息');
        return;
    }

    saveLoading.value = true;
    try {
        // 验证是否有房源数据
        if (parkingData.value.length === 0) {
            Message.warning('请添加房源信息');
            return;
        }

        // 循环调用保存房源接口
        for (const room of parkingData.value) {
            // 处理自持到期日期，有些房源可能没有这个属性
            const selfHoldingTime = (room as any).selfHeldExpireDate || null;

            const saveData = {
                projectId: formData.projectId,
                roomId: (room as any).roomId || '',
                roomName: room.name,
                parcelName: room.block,
                buildingName: room.building,
                floorName: room.floor,
                areaType: room.areaType,
                buildArea: room.buildingArea,
                innerArea: room.innerArea,
                productType: room.productType,
                isSale: room.saleable === '是' ? true : false,
                isCompanySelf: room.shared === '是' ? true : false,
                propertyType: room.selfHeldType === '自持' ? '2' : '1',
                selfHoldingTime
            };

            // 逐个保存房源信息
            await saveRoom(saveData);
        }

        console.log('房源保存成功');

        // 显示保存成功消息
        Message.success('房源保存成功');
    } catch (error) {
        console.error('房源保存失败:', error);
        // API调用失败，错误信息由拦截器处理
    } finally {
        saveLoading.value = false;
    }
};

const emits = defineEmits(['refresh']);
// 全局保存处理
const handleSave = async () => {
    saveLoading.value = true;
    try {
        if (currentTab.value === 'basic') {
            // 保存基本信息
            await handleSaveBasic();
        } else if (currentTab.value === 'building') {
            // 验证手动添加的楼栋信息是否完整
            const manualBuildings = buildingData.value.filter(item => item.source === '手动添加');
            let hasError = false;

            // 检查每个必填字段
            for (const building of manualBuildings) {
                if (!building.buildingName ||
                    !building.block ||
                    building.upFloorNums === undefined || building.upFloorNums === null ||
                    building.underFloorNums === undefined || building.underFloorNums === null) {
                    hasError = true;
                    break;
                }
            }

            if (hasError) {
                Message.error('请完善楼栋信息');
                saveLoading.value = false;
                return;
            }

            // 保存楼栋信息
            await handleSaveBuilding();
        } else if (currentTab.value === 'room') {
            // 保存房源信息
            await handleSaveRoom();
        }
    } catch (error) {
        console.error('保存失败:', error);
        // API调用失败，错误信息由拦截器处理
    } finally {
        saveLoading.value = false;
        emits('refresh');
    }
};

// 加载项目详情数据
const loadProjectDetails = async (projectId: string) => {
    try {
        // 显示加载中状态
        let response;
        currentTab.value = 'basic';
        if (isEditMode.value || modalType.value === 'view') {
            // 编辑模式或查看模式，调用getProjectDetail接口
            response = await getProjectDetail(projectId);
            // 编辑模式或查看模式下加载楼栋数据
            await loadBuildingData(projectId);
        } else {
            // 新增模式，调用getMdmProjectDetail接口
            response = await getMdmProjectDetail(projectId);
        }

        console.log('获取到的项目详情:', response);

        if (response && response.data) {
            // 保存完整的项目详情数据
            Object.assign(completeProjectData, response.data);
            // 设置表单显示数据
            setProjectData(response.data);

            // 如果是编辑模式或查看模式，标记基本信息已保存
            if (isEditMode.value || modalType.value === 'view') {
                basicInfoSaved.value = true;

                // 如果当前标签是房源，加载房源列表
                if (currentTab.value === 'room') {
                    loadRoomData();
                }
            }
        }
    } catch (error) {
        console.error('获取项目详情失败:', error);
        // API调用失败，错误信息由拦截器处理
    }
};

// 设置项目数据
const setProjectData = (data: any) => {
    const { project, stages, parcels, buildings, floors, rooms } = data;

    if (project) {
        // 设置基本信息
        formData.projectId = project.id || '';
        formData.mdmId = project.mdmId || project.id || '';
        formData.mainProject = project.mdmName || '';
        formData.projectName = project.mdmName || '';
        formData.projectCode = project.code || '';
        formData.name = project.name || '';
        formData.area = project.provinceName || project.cityName || project.countryName ?
            `${project.provinceName}/${project.cityName}/${project.countryName}` : '';
        formData.address = project.projectAddress || '';
        formData.projectType = project.mdmTypeName || '';
        formData.assetType = project.assetType.toString() || '';
        formData.propertyUnit = project.propertyUnit || '';
        console.log('formData', formData)
        // 设置经纬度
        if (project.longitude && project.latitude) {
            formData.lng = parseFloat(project.longitude);
            formData.lat = parseFloat(project.latitude);
            formData.projectLocation = project.projectAddress || '';
        }
    }

    // 设置分期信息
    if (stages && stages.length > 0) {
        formData.phase = stages.map((stage: any) => stage.stageName).join(',');
    }

    // 设置地块信息
    if (parcels && parcels.length > 0) {
        landData.length = 0; // 清空数组
        parcels.forEach((parcel: any, index: number) => {
            // 获取原始landUsage值
            const landUsageValue = parcel.landUsage || '';

            // 从landUsage值判断地块类型: 1=工业用地, 2=商业用地
            const landUsageCode = landUsageValue === '工业用地' || landUsageValue === '工业用地M1' ||
                landUsageValue === '1' || landUsageValue === 1 ? '1' :
                landUsageValue === '商业用地' || landUsageValue === '商业' ||
                    landUsageValue === '2' || landUsageValue === 2 ? '2' : '';

            // 查找对应的stageName
            let stageName = '';
            if (parcel.stageId && stages && stages.length > 0) {
                const matchedStage = stages.find((stage: any) => stage.id === parcel.stageId);
                if (matchedStage) {
                    stageName = matchedStage.stageName || '';
                }
            }

            landData.push({
                index: index + 1,
                id: parcel.id || '', // 添加地块ID，用于保存时正确匹配
                blockName: parcel.parcelName || '',
                mainUsage: landUsageValue,
                usage: landUsageCode,
                mdmNatureName: parcel.mdmNatureName || '', // 保存主数据用地性质名称
                mdmAddress: parcel.mdmAddress || '',
                address: parcel.address || parcel.mdmAddress || '',
                phase: stageName || parcel.stageName || '',
                mdmParcelName: parcel.mdmParcelName || ''
            });
        });
    }

    // 设置楼栋信息
    if (buildings && buildings.length > 0) {
        buildingData.value = buildings.map((building: any, index: number) => {
            // 使用辅助函数获取地块名称
            const parcelName = getParcelNameById(building.parcelId, parcels) || building.parcelName || '';

            return {
                index: index + 1,
                id: building.id || '',
                buildingId: building.id || building.buildingId || '',
                buildingName: building.buildingName || '',
                block: parcelName,
                blockId: building.parcelId || '',
                upFloorNums: building.upFloorNums || 0,
                underFloorNums: building.underFloorNums || 0,
                source: building.source == '1' ? '主数据' : '手动添加'
            };
        });

        // 添加调试日志
        console.log('楼栋数据映射:', buildings.map((b: any) => ({
            id: b.id,
            buildingName: b.buildingName,
            parcelName: b.parcelName,
            parcelId: b.parcelId
        })));
    }

    // 设置房间信息
    if (rooms && rooms.length > 0) {
        parkingData.value = rooms.map((room: any, index: number) => ({
            index: (index + 1).toString(),
            id: room.id || '',
            roomId: room.roomId || '',
            name: room.roomName || '',
            block: room.parcelName || '',
            building: room.buildingName || '',
            floor: room.floorName || '',
            areaType: room.areaType,
            buildingArea: room.buildArea || 0,
            innerArea: room.innerArea || 0,
            productType: room.productType || '',
            saleable: room.isSale === true || room.isSale === 1 ? '是' : '否',
            shared: room.isCompanySelf === true || room.isCompanySelf === 1 ? '是' : '否',
            selfHeldType: room.propertyType === '2' ? '自持' : '非自持',
            selfHeldExpireDate: room.selfHoldingTime || '',
            source: room.mdmRoomId ? '主数据' : '手动添加'
        }));
    }

    loadParcelOptions(formData.projectId);
};

// 重置表单
const resetForm = () => {
    if (!formRef.value) return;

    formRef.value.resetFields();
    formData.mainProject = '';
    formData.projectName = '';
    formData.projectCode = '';
    formData.name = '';
    formData.area = '';
    formData.lng = 120.6994;
    formData.lat = 27.9949;
    formData.projectLocation = '';
    formData.address = '';
    formData.projectType = '';
    formData.assetType = '';
    formData.propertyUnit = '';
    formData.phase = '';
    landData.length = 0;
    buildingData.value = [];
    parkingData.value = [];
    selectedBlock.value = '';
    searchBuilding.value = '';
    searchRoom.value = '';
};

// 重置所有数据（关闭弹窗时调用）
const resetAllData = () => {
    // 重置基本表单
    resetForm();

    // 重置标签状态
    currentTab.value = 'basic';
    basicInfoSaved.value = false;
    isEditMode.value = false;

    // 重置分页状态
    roomPagination.current = 1;
    roomPagination.total = 0;
    buildingPagination.current = 1;
    buildingPagination.total = 0;

    // 重置选择状态
    selectedParkingKeys.value = [];

    // 重置下拉选项
    parcelOptions.value = [];
    buildingOptions.value = [];
    floorOptions.value = [];

    // 重置弹窗状态
    parkingDialogVisible.value = false;
    buildingDialogVisible.value = false;

    // 重置完整项目数据
    Object.assign(completeProjectData, {
        project: {},
        stages: [],
        parcels: [],
        buildings: [],
        floors: [],
        rooms: []
    });

    // 重置表单引用状态
    if (formRef.value) {
        formRef.value.resetFields();
    }
};

// 根据parcelId查找对应的parcel数据，获取正确的parcelName
const getParcelNameById = (parcelId: string | null | undefined, parcels: any[]): string => {
    if (!parcelId || !parcels || !parcels.length) {
        return '';
    }

    const matchedParcel = parcels.find(p => p.id === parcelId);
    return matchedParcel ? (matchedParcel.parcelName || '') : '';
};

// 加载楼栋数据
const loadBuildingData = async (projectId: string) => {
    if (!projectId) return;

    try {
        const params = {
            projectId: projectId,
            pageNum: buildingPagination.current,
            pageSize: buildingPagination.pageSize,
            parcelId: selectedBlock.value || '',
            buildingOrRoomName: searchBuilding.value || ''
        };

        const res = await getBuildingList(params);
        console.log('楼栋列表响应:', res);

        if (res && Array.isArray(res.rows)) {
            console.log('res.rows', res.rows)
            buildingPagination.total = res.total || 0;

            // 设置楼栋数据，同时保留source属性，默认为'主数据'
            buildingData.value = res.rows.map((item: any, index: number) => {
                // 使用辅助函数获取地块名称
                const parcelName = getParcelNameById(item.parcelId, completeProjectData.parcels) || item.parcelName || '';

                if (parcelName) {
                    console.log(`已匹配到地块: parcelId=${item.parcelId}, parcelName=${parcelName}`);
                } else if (item.parcelId) {
                    console.log(`未找到地块: parcelId=${item.parcelId}`);
                }

                return {
                    index: ((buildingPagination.current - 1) * buildingPagination.pageSize + index + 1),
                    id: item.id || '',
                    buildingId: item.id || item.buildingId || '',
                    buildingName: item.buildingName || '',
                    block: parcelName, // 使用查找到的parcelName
                    blockId: item.parcelId || '',
                    upFloorNums: item.upFloorNums || 0,
                    underFloorNums: item.underFloorNums || 0,
                    source: item.source == '1' ? '主数据' : '手动添加'
                };
            });
        } else {
            console.log('楼栋列表为空')
            console.log('res', res)
            buildingData.value = [];
            buildingPagination.total = 0;
        }
    } catch (error) {
        console.error('获取楼栋列表失败:', error);
        // API调用失败，错误信息由拦截器处理
        buildingData.value = [];
    }
};

// 楼栋分页变化
const handleBuildingPageChange = (current: number) => {
    buildingPagination.current = current;
    loadBuildingData(formData.projectId);
};

// 楼栋每页条数变化
const handleBuildingPageSizeChange = (pageSize: number) => {
    buildingPagination.pageSize = pageSize;
    buildingPagination.current = 1;
    loadBuildingData(formData.projectId);
};

// 处理楼栋表格选择变化
const handleBuildingSelectionChange = (selection: any) => {
    console.log('选中的楼栋:', selection);
};

// 车位表格多选
const selectedParkingKeys = ref<string[]>([]);
const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false
});

// 计算当前页是否有可删除的选中项
const hasValidSelectedRooms = computed(() => {
    if (selectedParkingKeys.value.length === 0) {
        return false;
    }
    
    // 检查选中的房源是否在当前页面数据中，且可以删除
    const currentPageDeletableRooms = parkingData.value.filter(room =>
        selectedParkingKeys.value.includes(room.index) && room.source !== '主数据'
    );
    
    return currentPageDeletableRooms.length > 0;
});
const handleParkingSelectionChange = (rowKeys: string[]) => {
    selectedParkingKeys.value = rowKeys;
};

const handleAddBuilding = () => {
    openAddBuildingDialog();
};

const handleBatchDeleteParking = () => {
    if (selectedParkingKeys.value.length === 0) {
        Message.warning('请先选择要删除的房源');
        return;
    }

    // 过滤出可删除的房源（只有系统新增的房源可以删除）
    const deletableRooms = parkingData.value.filter(room =>
        selectedParkingKeys.value.includes(room.index) && room.source !== '主数据'
    );

    if (deletableRooms.length === 0) {
        Message.warning('选中的房源中没有可删除的项目（主数据房源不可删除）');
        return;
    }

    if (deletableRooms.length !== selectedParkingKeys.value.length) {
        Message.warning(`选中的 ${selectedParkingKeys.value.length} 个房源中，只有 ${deletableRooms.length} 个可以删除（主数据房源不可删除）`);
        return;
    }

    Modal.confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${deletableRooms.length} 个房源吗？`,
        onOk: async () => {
            try {
                deleteLoading.value = true;
                // 获取可删除房源的ID
                const deletableIds = deletableRooms.map(room => room.id).filter(id => id);
                console.log('可删除的房源IDs:', deletableIds);

                if (deletableIds.length > 0) {
                    await deleteRoom(deletableIds.join(','));
                }
                deleteLoading.value = false;

                Message.success('批量删除房源成功');
                // 清空选中项
                selectedParkingKeys.value = [];
                // 重新加载房源数据
                loadRoomData();
            } catch (error) {
                console.error('批量删除房源失败:', error);
                // API调用失败，错误信息由拦截器处理
                deleteLoading.value = false;
            }
        }
    });
};

// 车位弹窗相关
const parkingDialogVisible = ref(false);
const parkingDialogMode = ref<'add' | 'edit'>('add');
const dialogTitle = ref('新增车位');
const parkingDialogForm = reactive({
    name: '',
    building: '',
    productType: '40',
    buildingArea: undefined,
    saleable: '',
    selfHeldType: '',
    block: '',
    floor: '',
    areaType: '1',
    innerArea: undefined,
    shared: '',
    selfHeldExpireDate: null,
    roomId: '',  // 房源编码字段
    id: ''       // 房源ID字段
});
const parkingDialogFormRef = ref(); // 新增表单ref
let editingParkingIndex = '';

// 楼栋弹窗相关
const buildingDialogVisible = ref(false);
const buildingDialogMode = ref<'add' | 'edit'>('add');
const buildingDialogTitle = ref('新增楼栋');
const buildingDialogForm = reactive({
    buildingName: '',
    block: '',
    upFloorNums: 1,
    underFloorNums: 0,
    buildingId: ''
});
const buildingDialogFormRef = ref(); // 楼栋表单ref
let editingBuildingIndex = '';

const openAddBuildingDialog = () => {
    // 重置表单数据
    buildingDialogForm.buildingName = '';
    buildingDialogForm.block = selectedBlock.value || ''; // 如果有选中地块，自动带出
    buildingDialogForm.upFloorNums = 1;
    buildingDialogForm.underFloorNums = 0;
    buildingDialogForm.buildingId = '';

    // 设置弹窗为添加模式
    buildingDialogMode.value = 'add';
    buildingDialogTitle.value = '新增楼栋';
    buildingDialogVisible.value = true;
};
const openEditBuildingDialog = (record: BuildingRecord) => {
    // 加载表单数据
    buildingDialogForm.buildingName = record.buildingName;
    buildingDialogForm.block = record.blockId || record.block; // 使用blockId而不是block名称
    buildingDialogForm.upFloorNums = record.upFloorNums;
    buildingDialogForm.underFloorNums = record.underFloorNums;
    buildingDialogForm.buildingId = record.id || '';

    // 设置编辑索引
    editingBuildingIndex = record.index.toString();

    // 设置弹窗为编辑模式
    buildingDialogMode.value = 'edit';
    buildingDialogTitle.value = '编辑楼栋';
    buildingDialogVisible.value = true;
};

// 处理楼栋弹窗的取消
const handleBuildingDialogCancel = () => {
    buildingDialogVisible.value = false;
};

// 处理楼栋弹窗的确认
const handleBuildingDialogOk = async () => {
    try {
        // 触发表单校验
        const errors = await buildingDialogFormRef.value.validate();
        if (errors) return;

        // 创建保存数据对象
        const saveData = {
            projectId: formData.projectId,
            buildingName: buildingDialogForm.buildingName,
            buildingId: buildingDialogForm.buildingId || '',
            parcelId: buildingDialogForm.block, // 使用parcelId而不是parcelName
            upFloorNums: buildingDialogForm.upFloorNums,
            underFloorNums: buildingDialogForm.underFloorNums
        };

        saveLoading.value = true;
        // 调用保存楼栋接口
        await saveBuilding(saveData);
        saveLoading.value = false;

        // 成功提示
        Message.success(`${buildingDialogMode.value === 'add' ? '添加' : '编辑'}楼栋成功`);

        // 重新加载楼栋数据
        await loadBuildingData(formData.projectId);

        // 关闭弹窗
        buildingDialogVisible.value = false;
    } catch (error) {
        console.error('楼栋保存失败:', error);
        // API调用失败，错误信息由拦截器处理
        saveLoading.value = false;
    }
};

// 打开添加房源弹窗
const openAddParkingDialog = () => {
    // 重置表单数据
    parkingDialogForm.name = '';
    parkingDialogForm.building = '';
    parkingDialogForm.buildingArea = undefined;
    parkingDialogForm.saleable = '';
    parkingDialogForm.selfHeldType = '';
    parkingDialogForm.block = '';
    parkingDialogForm.floor = '';
    parkingDialogForm.areaType = '1';
    parkingDialogForm.innerArea = undefined;
    parkingDialogForm.shared = '';
    parkingDialogForm.selfHeldExpireDate = null;
    parkingDialogForm.roomId = '';
    parkingDialogForm.id = '';
    
    // 设置产品类型默认值
    parkingDialogForm.productType = '40';

    // if (product_type.value && product_type.value.length > 0) {
    //     parkingDialogForm.productType = product_type.value[0].value;
    // } else {
    //     parkingDialogForm.productType = '40';
    // }

    // 加载地块数据和产品类型选项
    loadParcelOptions();
    loadProductTypeOptions();

    // 设置弹窗标题和显示
    dialogTitle.value = '新增房源';
    parkingDialogVisible.value = true;
};

// 根据名称查找对应的ID
const findIdByName = (options: any[], name: string) => {
    if (!name || !options || options.length === 0) return '';
    const item = options.find(opt => opt.label === name);
    return item ? item.value : '';
};

// 打开编辑房源弹窗
const openEditParkingDialog = (record: any) => {
    parkingDialogMode.value = 'edit';

    // 初始化编辑索引
    editingParkingIndex = record.index;

    // 异步加载数据并设置表单
    (async () => {
        try {
            // 先加载地块数据和产品类型选项
            await loadParcelOptions();
            await loadProductTypeOptions();

            // 根据名称查找对应的ID
            const parcelId = record.blockId || findIdByName(parcelOptions.value, record.block);
            let buildingId = record.buildingId;
            let floorId = record.floorId;

            // 如果有地块ID，加载楼栋选项并查找楼栋ID
            if (parcelId) {
                await loadBuildingOptions(parcelId);
                buildingId = buildingId || findIdByName(buildingOptions.value, record.building);

                // 如果有楼栋ID，加载楼层选项并查找楼层ID
                if (buildingId) {
                    await loadFloorOptions(buildingId);
                    floorId = floorId || findIdByName(floorOptions.value, record.floor);
                }
            }

            // 加载表单数据，确保使用ID值
            Object.assign(parkingDialogForm, {
                ...record,
                block: parcelId, // 使用地块ID
                building: buildingId, // 使用楼栋ID
                floor: floorId // 使用楼层ID
            });
        } catch (error) {
            console.error('加载编辑数据失败:', error);
            // 如果加载失败，仍然设置基本数据
            Object.assign(parkingDialogForm, record);
        }
    })();

    // 设置弹窗标题和显示
    dialogTitle.value = '编辑房源';
    parkingDialogVisible.value = true;
};

// 处理房源弹窗确认
const handleParkingDialogOk = async () => {
    try {
        // 触发表单校验
        const errors = await parkingDialogFormRef.value.validate();
        if (errors) return;

        // 创建保存数据对象，按照图片中所示的字段结构
        const saveData: any = {
            projectId: formData.projectId,
            parcelId: parkingDialogForm.block,          // 地块ID
            buildingId: parkingDialogForm.building,     // 楼栋ID
            floorId: parkingDialogForm.floor,           // 楼层ID
            roomName: parkingDialogForm.name,           // 房间名称
            productType: parkingDialogForm.productType, // 产品类型
            areaType: parkingDialogForm.areaType, // 面积类型 (1实测 2预测 3设计)
            buildArea: Number(parkingDialogForm.buildingArea || 0),      // 建筑面积
            innerArea: Number(parkingDialogForm.innerArea || 0),         // 套内面积
            isSale: parkingDialogForm.saleable === '是' ? true : false,         // 是否可售 (false否 true是)
            isCompanySelf: parkingDialogForm.shared === '是' ? true : false,    // 是否公司自持 (false否 true是)
            propertyType: parkingDialogForm.selfHeldType === '自持' ? '2' : '1', // 物业类型 (1非自持 2自持)
            selfHoldingTime: parkingDialogForm.selfHeldExpireDate || null // 自持到期时间
        };

        // 如果是编辑模式，添加房源ID
        if (parkingDialogMode.value === 'edit') {
            // 优先使用id字段，如果没有则使用roomId
            saveData.id = parkingDialogForm.id || parkingDialogForm.roomId || '';
        }

        saveLoading.value = true;
        // 调用保存房源接口
        await saveRoom(saveData);
        saveLoading.value = false;

        // 成功提示
        Message.success(`${parkingDialogMode.value === 'add' ? '添加' : '编辑'}房源成功`);

        // 重新加载房源数据
        await loadRoomData();

        // 关闭弹窗
        parkingDialogVisible.value = false;
    } catch (error) {
        console.error('房源保存失败:', error);
        // API调用失败，错误信息由拦截器处理
        saveLoading.value = false;
    }
};

// 处理房源弹窗取消
const handleParkingDialogCancel = () => {
    parkingDialogVisible.value = false;
};

// 处理自持物业类型变化
const handleSelfHeldTypeChange = () => {
    if (parkingDialogForm.selfHeldType === '自持') {
        parkingDialogForm.selfHeldExpireDate = null;
    }
};

// 处理删除楼栋
const handleDeleteBuilding = (record: BuildingRecord) => {
    if (record.source === '手动添加') {
        console.log('Delete Building - Full Record:', record);

        // 确保使用有效的ID - 优先级: id > buildingId
        const buildingId = record.id || record.buildingId || '';

        if (!buildingId) {
            console.error('楼栋记录缺少ID:', record);
            // API调用失败，错误信息由拦截器处理
            return;
        }

        Modal.confirm({
            title: '确认删除',
            content: `确定要删除楼栋"${record.buildingName}"吗？`,
            onOk: async () => {
                try {
                    deleteLoading.value = true;
                    console.log('正在删除楼栋, ID:', buildingId);
                    await deleteBuilding(buildingId);
                    deleteLoading.value = false;

                    Message.success('删除楼栋成功');
                    // 重新加载楼栋数据
                    await loadBuildingData(formData.projectId);
                } catch (error) {
                    console.error('删除楼栋失败:', error);
                    // API调用失败，错误信息由拦截器处理
                    deleteLoading.value = false;
                }
            }
        });
    }
};

// 处理删除房源
const handleDeleteParking = (record: any) => {
    console.log('Delete Room - Full Record:', record);

    // 确保使用有效的ID
    const roomId = record.id || '';

    if (!roomId) {
        console.error('房源记录缺少ID:', record);
        // API调用失败，错误信息由拦截器处理
        return;
    }

    Modal.confirm({
        title: '确认删除',
        content: `确定要删除房源"${record.name}"吗？`,
        onOk: async () => {
            try {
                deleteLoading.value = true;
                console.log('正在删除房源, ID:', roomId);
                await deleteRoom(roomId);
                deleteLoading.value = false;

                Message.success('删除房源成功');
                // 重新加载房源数据
                loadRoomData();
            } catch (error) {
                console.error('删除房源失败:', error);
                // API调用失败，错误信息由拦截器处理
                deleteLoading.value = false;
            }
        }
    });
};

// 处理地图弹窗确认
const handleMapDialogConfirm = (point: { lng: number; lat: number, address: string }) => {
    formData.lat = point.lat;
    formData.lng = point.lng;
    formData.projectLocation = point.address;
    mapDialogVisible.value = false;
};

// 处理地图弹窗关闭
const handleMapDialogClose = () => {
    mapDialogVisible.value = false;
};

// 处理下载模板
const handleDownloadTemplate = () => {
    if (!formData.projectId) {
		Message.warning('请先保存基本信息');
		return;
	}
	
	try {
		// 使用exportExcel方法下载模板
		exportExcel(downloadParkTemplate, formData.projectId, '车位导入模板')
			.then(() => {
				Message.success('模板下载成功');
			})
	} catch (error) {
		console.error('下载模板失败:', error);
	}
};

// 处理导入车位
const handleImportParking = () => {
    if (!formData.projectId) {
		Message.warning('请先保存基本信息');
		return;
	}

	// 创建文件选择器
	const input = document.createElement('input');
	input.type = 'file';
	input.accept = '.xlsx,.xls';
	input.style.display = 'none';

	// 监听文件选择事件
	input.onchange = (event) => {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			const file = target.files[0];
			
			// 检查文件类型
			if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
				Message.error('请上传Excel文件(.xlsx或.xls)');
				return;
			}

			// 显示加载状态
			const loadingInstance = Message.loading({
				content: '正在导入车位数据...',
				duration: 0
			});
			
			// 使用importExcelFile方法导入文件
			importExcelFile(file, importParkTemplate)
				.then((res: any) => {
					if (res && res.code === 200) {
						Message.success('车位导入成功');
						// 刷新房源列表
						loadRoomData();
					}
				})
				.catch((error: any) => {
					console.error('车位导入失败:', error);
				})
				.finally(() => {
					// 关闭加载状态
					loadingInstance.close();
					// 移除input元素
					document.body.removeChild(input);
				});
		}
	};
	
	// 添加到DOM并触发点击
	document.body.appendChild(input);
	input.click();
};
// 地块选项数据
const parcelOptions = ref<any[]>([]);
// 楼栋选项数据
const buildingOptions = ref<any[]>([]);
// 楼层选项数据
const floorOptions = ref<any[]>([]);
// 产品类型选项数据
const productTypeOptions = ref<{ value: string; label: string }[]>([]);

// 加载地块数据
const loadParcelOptions = async (projectId?: string) => {
    if (!projectId && !formData.projectId) return;
    try {
        const res = await getParcelList(projectId || formData.projectId);
        console.log('地块列表响应:', res);
        if (res && Array.isArray(res.data)) {
            parcelOptions.value = res.data.map(item => ({
                value: item.id,
                label: item.parcelName
            }));
        } else {
            parcelOptions.value = [];
        }
    } catch (error) {
        console.error('获取地块列表失败:', error);
        // API调用失败，错误信息由拦截器处理
        parcelOptions.value = [];
    }
};

// 加载楼栋数据
const loadBuildingOptions = async (parcelId: string,) => {
    if (!parcelId) return;
    try {
        const res = await getBuildingDropdownList(parcelId);
        if (res && Array.isArray(res.data)) {
            buildingOptions.value = res.data.map(item => ({
                value: item.id,
                label: item.buildingName
            }));
        } else {
            buildingOptions.value = [];
        }
    } catch (error) {
        console.error('获取楼栋列表失败:', error);
        // API调用失败，错误信息由拦截器处理
        buildingOptions.value = [];
    }
};

// 加载楼层数据
const loadFloorOptions = async (buildingId: string) => {
    if (!buildingId) return;
    try {
        const res = await getFloorDropdownList(buildingId);
        if (res && Array.isArray(res.data)) {
            floorOptions.value = res.data.map(item => ({
                value: item.id,
                label: item.floorName
            }));
        } else {
            floorOptions.value = [];
        }
    } catch (error) {
        console.error('获取楼层列表失败:', error);
        // API调用失败，错误信息由拦截器处理
        floorOptions.value = [];
    }
};

// 加载产品类型选项
const loadProductTypeOptions = async (): Promise<void> => {
    try {
        // 直接使用字典数据
        if (product_type.value && Array.isArray(product_type.value)) {
            productTypeOptions.value = product_type.value.map((item: any) => ({
                value: item.value,
                label: item.label
            }));
        } else {
            productTypeOptions.value = [];
        }
    } catch (error) {
        console.error('获取产品类型字典失败:', error);
        productTypeOptions.value = [];
    }
};

// 地块选择变化处理
const handleParcelChange = (value: string) => {
    parkingDialogForm.building = '';
    parkingDialogForm.floor = '';
    if (value) {
        loadBuildingOptions(value);
    } else {
        buildingOptions.value = [];
        floorOptions.value = [];
    }
};

// 楼栋选择变化处理
const handleBuildingChange = (value: string) => {
    parkingDialogForm.floor = '';
    if (value && parkingDialogForm.block) {
        loadFloorOptions(value);
    } else {
        floorOptions.value = [];
    }
};

// 处理楼栋信息页面的地块选择变化
const handleBuildingTabParcelChange = (value: string) => {
    buildingPagination.current = 1;
    selectedBlock.value = value; // 更新选中的地块

    // 如果value为空，应该显示所有地块的楼栋
    console.log('地块选择变化:', value);

    // 构建参数，确保在value为空时不传parcelId
    const params: any = {
        projectId: formData.projectId,
        pageNum: buildingPagination.current,
        pageSize: buildingPagination.pageSize,
    };

    // 只有当value有值时才添加parcelId参数
    if (value) {
        params.parcelId = value;
    }

    console.log('查询楼栋的参数:', params);

    // 确保projectId存在
    if (!params.projectId) {
        console.warn('projectId不存在，无法查询楼栋');
        return;
    }

    getBuildingList(params).then(res => {
        console.log('改变地块楼栋列表响应:', res);
        if (res && res.rows && Array.isArray(res.rows)) {
            buildingPagination.total = res.total || 0;

            if (res.rows.length === 0) {
                console.log('当前地块没有楼栋数据');
                buildingData.value = [];
            } else {
                // 设置楼栋数据
                buildingData.value = res.rows.map((item: any, index: number) => {
                    // 使用辅助函数获取地块名称
                    const parcelName = getParcelNameById(item.parcelId, completeProjectData.parcels) || item.parcelName || '';

                    return {
                        index: ((buildingPagination.current - 1) * buildingPagination.pageSize + index + 1),
                        id: item.id || '',
                        buildingId: item.id || item.buildingId || '',
                        buildingName: item.buildingName || '',
                        block: parcelName, // 使用查找到的parcelName
                        blockId: item.parcelId || '',
                        upFloorNums: item.upFloorNums || 0,
                        underFloorNums: item.underFloorNums || 0,
                        source: item.source == '1' ? '主数据' : '手动添加'
                    };
                });
            }
        } else {
            console.warn('楼栋列表数据格式异常:', res);
            buildingData.value = [];
            buildingPagination.total = 0;
        }
    }).catch(error => {
        console.error('获取楼栋列表失败:', error);
        // API调用失败，错误信息由拦截器处理
        buildingData.value = [];
    });
};

// 处理房源信息页面的地块选择变化
const handleRoomTabParcelChange = (value: string) => {
    // 重置分页并加载房源数据
    roomPagination.current = 1;
    // 清空选中状态
    selectedParkingKeys.value = [];
    loadRoomData();
};

// 保存基本信息
const handleSaveBasic = async () => {
    if (!formRef.value) return;

    saveLoading.value = true;
    try {
        // 表单验证 - 只校验项目编码和项目简称字段
        if (!formData.projectCode?.trim()) {
            Message.error('项目编码不能为空');
            saveLoading.value = false;
            return;
        }

        if (!formData.name?.trim()) {
            Message.error('项目简称不能为空');
            saveLoading.value = false;
            return;
        }

        // 计算删除的分期ID列表
        const deleteStageIdList: string[] = [];
        if (originalStages.value.length > 0) {
            const currentStageIds = completeProjectData.stages?.map((stage: any) => stage.id).filter((id: any) => id) || [];
            originalStages.value.forEach(originalStage => {
                if (originalStage.id && !currentStageIds.includes(originalStage.id)) {
                    deleteStageIdList.push(originalStage.id);
                }
            });
        }

        // 计算删除的地块ID列表
        const deleteParcelIdList: string[] = [];
        if (originalParcels.value.length > 0) {
            const currentParcelIds = landData.map(parcel => parcel.id).filter(id => id);
            originalParcels.value.forEach(originalParcel => {
                if (originalParcel.id && !currentParcelIds.includes(originalParcel.id)) {
                    deleteParcelIdList.push(originalParcel.id);
                }
            });
        }

        // 构建SysProjectAddDTO格式的数据
        const projectAddData = {
            // 基本信息，从project中获取
            id: completeProjectData.project?.id || '',
            mdmProjectId: completeProjectData.project?.mdmProjectId || '',
            mdmName: completeProjectData.project?.mdmName || '',
            mdmSaleName: completeProjectData.project?.mdmSaleName || '',
            code: formData.projectCode.trim(),
            name: formData.name.trim(),
            type: "1", // 项目类型 (1内部 2外部)

            // 区域信息
            provinceCode: completeProjectData.project?.provinceCode || '',
            provinceName: completeProjectData.project?.provinceName || '',
            cityCode: completeProjectData.project?.cityCode || '',
            cityName: completeProjectData.project?.cityName || '',
            countryCode: completeProjectData.project?.countryCode || '',
            countryName: completeProjectData.project?.countryName || '',

            // 项目信息
            mdmTypeName: completeProjectData.project?.mdmTypeName || '',
            assetType: formData.assetType || completeProjectData.project?.assetType || '',
            propertyUnit: completeProjectData.project?.propertyUnit || '',
            projectAddress: completeProjectData.project?.projectAddress || '',

            // 位置信息
            longitude: completeProjectData.project?.longitude || '',
            latitude: completeProjectData.project?.latitude || '',
            totalSelfArea: completeProjectData.project?.totalSelfArea || '',

            // 同步landData中的地址信息到completeProjectData.parcels
            stageList: completeProjectData.stages || [],
            parcelList: (() => {
                // 打印日志以便调试
                console.log('原始地块数据:', completeProjectData.parcels);
                console.log('表单地块数据:', landData);

                const updatedParcels = landData.map((land) => {
                    // 查找对应的原始parcel数据，优先使用ID匹配，其次使用名称匹配
                    const originalParcel = completeProjectData.parcels?.find((p: any) =>
                        land.id ? p.id === land.id : p.parcelName === land.blockName
                    ) || {};

                    // 合并并返回更新后的parcel数据
                    const updatedParcel = {
                        ...originalParcel,
                        id: originalParcel.id || '',
                        mdmParcelId: originalParcel.mdmParcelId || '',
                        projectId: formData.projectId || originalParcel.projectId || '',
                        stageId: originalParcel.stageId || '',
                        stageName: originalParcel.stageName || '',
                        parcelName: land.blockName || originalParcel.parcelName || '',
                        mdmNatureName: land.mdmNatureName || originalParcel.mdmNatureName || '', // 优先使用landData中保存的值
                        landUsage: land.usage === '1' ? 1 : land.usage === '2' ? 2 : (originalParcel.landUsage || ''), // 用户可编辑的用地性质
                        mdmAddress: land.mdmAddress || originalParcel.mdmAddress || '',
                        address: land.address || '', // 确保使用最新的合同签约地址
                        totalSelfArea: originalParcel.totalSelfArea || '',
                        isDel: 0,
                        mdmParcelName: originalParcel.mdmParcelName || ''
                    };

                    console.log(`地块 ${land.blockName} 的地址由 "${originalParcel.address || '空'}" 更新为 "${land.address || '空'}"`);
                    console.log(`地块 ${land.blockName} 的主数据用地性质: "${land.mdmNatureName || '空'}" (原始: "${originalParcel.mdmNatureName || '空'}")`);
                    console.log(`地块 ${land.blockName} 的用地性质: "${land.usage}" -> ${updatedParcel.landUsage}`);
                    return updatedParcel;
                });

                console.log('更新后的地块数据:', updatedParcels);
                return updatedParcels;
            })(),
            buildingList: completeProjectData.buildings || [],

            // 删除的ID列表
            deleteStageIdList: deleteStageIdList.length > 0 ? deleteStageIdList : undefined,
            deleteParcelIdList: deleteParcelIdList.length > 0 ? deleteParcelIdList : undefined,

            // 其他字段
            isDel: completeProjectData.project?.isDel || 0
        };

        // 调试：打印完整的项目数据
        console.log('发送到API的项目数据:', projectAddData);
        console.log('删除的分期ID列表:', deleteStageIdList);
        console.log('删除的地块ID列表:', deleteParcelIdList);

        // 使用SysProjectAddDTO格式的数据调用接口
        const response = await addProject(projectAddData);
        console.log('项目保存成功:', response);

        // 保存返回的projectId
        if (response && response.data) {
            formData.projectId = response.data;
            // 更新全局变量中的projectId
            if (completeProjectData.project) {
                completeProjectData.project.id = response.data;
            }
        }

        // 标记基本信息已保存
        basicInfoSaved.value = true;

        // 显示保存成功消息
        Message.success('项目保存成功');

        // 先加载地块数据
        await loadParcelOptions(formData.projectId);

        // 加载房源数据
        await loadRoomData();

        // 保存基本信息后停留在基本信息页面，不自动跳转
        // 注释掉自动跳转到房源页面的逻辑
        // if (!isEditMode.value) {
        //     currentTab.value = 'room';
        // }
    } catch (error) {
        console.error('项目保存失败:', error);
        // API调用失败，错误信息由拦截器处理
    } finally {
        saveLoading.value = false;
    }
};

const modalType = ref('add');
// 暴露方法给父组件
defineExpose({
    show: (projectData: any, type: string) => {
        visible.value = true;
        modalType.value = type;
        if (type == 'add') {
            // 从主数据选择进来
            isEditMode.value = false;
            loadProjectDetails(projectData);
            setProjectData(projectData);
        } else if (type == 'edit') {
            // 从列表进入编辑模式
            isEditMode.value = true;
            loadProjectDetails(projectData);
        } else if (type == 'view') {
            // 从列表进入编辑模式
            isEditMode.value = false;
            loadProjectDetails(projectData);
        }
    }
});

// 监听assetType变化，同步到completeProjectData
watch(() => formData.assetType, (newValue) => {
    if (completeProjectData.project) {
        completeProjectData.project.assetType = newValue;
    }
});

// 监听product_type字典变化，加载产品类型选项
watch(() => product_type.value, () => {
    loadProductTypeOptions();
}, { immediate: true });

onMounted(() => {
    if (formData.projectId) {
        loadParcelOptions(formData.projectId);
    }
});

</script>

<style scoped lang="less">
.internal-project-container {
    display: flex;
    height: 100%;

    :deep(.arco-modal) {
        .arco-modal-header {
            white-space: nowrap !important;
            overflow: hidden !important;

            .arco-modal-title {
                width: 18.75rem !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                display: inline-block !important;
                padding-right: 0 !important;
            }
        }
    }

    .content-section {
        flex: 1;
        padding: 0 16px;
        overflow-y: auto;
    }

    :deep(.disabled-select) {
        .arco-select-view {
            background-color: var(--color-fill-2);
            cursor: not-allowed;
            color: var(--color-text-3);
        }
    }

    :deep(.arco-input-disabled) {
        background-color: var(--color-fill-2);
        cursor: not-allowed;
        color: var(--color-text-3);
    }

    .basic-info {
        flex: 1;
        min-width: 0;
    }

    .parking-settings {
        flex: 1;
        min-width: 0;
    }

    .section-title {
        margin: 16px 0;
        // font-size: .875rem;
        // font-weight: 500;
        // color: var(--color-text-1);
        // margin: 1rem 0;
        // padding-left: .75rem;
        // position: relative;
        // display: flex;
        // align-items: center;

        // &::before {
        //     content: '';
        //     position: absolute;
        //     left: 0;
        //     top: 50%;
        //     transform: translateY(-50%);
        //     width: .25rem;
        //     height: 1rem;
        //     background-color: rgb(var(--primary-6));
        //     border-radius: .125rem;
        // }
    }

    .button-group {
        margin-bottom: 1rem;

        .arco-btn {
            margin-right: .5rem;
        }
    }

    .button-group2 {
        margin-bottom: 0rem;
    }

    .phase-info {
        width: 100%;
        .phase-buttons {
            width: 100%;
            display: flex;
            gap: 8px;
            // gap: .75rem;
            margin-top: 16px;
            // margin-left: 1.875rem;
            .phase-btn-item {
                width: calc(25% - 8px);
                .phase-btn {
                    width: 100%;
                    box-sizing: border-box;
                // min-width: 7.5rem;
                background-color: var(--color-fill-1);
                color: var(--color-text-1);
                text-align: center;
                padding: 8px;
            }

            }


        }
    }

    .phase-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .room-info {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        gap: 1rem;

    }

    :deep(.arco-form-item-label-col) {
        padding-right: .75rem;
    }

    :deep(.arco-table-th) {
        background-color: var(--color-fill-2);

        & span,
        & {
            .arco-table-th-title {
                // &::first-letter {
                //   color: #f5222d;
                // }
            }
        }
    }

    .form-row {
        display: flex;
        gap: 1.5rem;

        .form-col {
            flex: 1;
        }
    }

    :deep(.arco-form) {
        .arco-form-item {
            margin-bottom: 1rem;
        }

        .arco-form-item-label {
            white-space: nowrap;
        }
    }
}
</style>