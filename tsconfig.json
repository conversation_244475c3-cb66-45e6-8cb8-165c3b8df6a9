{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "strict": true, "jsx": "preserve", "jsxImportSource": "vue", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "lib": ["es2020", "dom"], "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.txs", "src/**/*.vue", "src/auto-imports.d.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}