import { mergeConfig } from 'vite';
import eslint from 'vite-plugin-eslint';
import baseConfig from './vite.config.base';
import { defineConfig } from 'vite';

export default mergeConfig(
    {
        mode: 'development',
        server: {
            open: false,
            proxy: {
                '/prod-api': {
                    // target: 'http://127.0.0.1:13491',
                    // target: 'https://nas.spjgzs.com',
                    // target: 'https://13490f.spjgzs.com:13491',
                    target: 'http://************:8570',
                    // target: 'http://************:8580',
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/api/, '/'),
                },
                '/statics': {
                    target: 'http://************:8570',
                    changeOrigin: true,
                }
            },
            port: 5551,
            host: true,
            strictPort: true,
            // fs: {
            // 	strict: true,
            // },
        },
        plugins: [
            // eslint({
            //     cache: false,
            //     include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
            //     exclude: ['node_modules'],
            // }),
        ],
    },
    baseConfig
);
