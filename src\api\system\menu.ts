import http from '../index';

export const getMenuList = (params: any) => {
    return http.get(`/business-platform/menu/list`, params);
};
export const addMenu = (data: any) => {
    return http.post(`/business-platform/menu`, data);
};
export const updateMenu = (data: any) => {
    return http.put(`/business-platform/menu`, data);
};
export const deleteMenu = (data: any) => {
    return http.delete(`/business-platform/menu/${data}`);
};
export const getMenuById = (data: any) => {
    return http.get(`/business-platform/menu/${data}`);
};
