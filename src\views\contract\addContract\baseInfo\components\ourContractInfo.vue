<template>
    <section>
        <sectionTitle title="甲方签约信息" />
        <div class="content">
            <a-form ref="formRef" :model="contractData" :rules="isDetailMode ? {} : rules"
                :disabled="editType === 'change' || editType === 'changeDetail'" layout="vertical" auto-label-width>
                <a-form-item label="甲方签约主体公司" field="ourSigningId">
                    <a-select v-model="contractData.ourSigningId" placeholder="请选择签约主体公司" @change="handleOurSigningChange">
                        <a-option v-for="item in merchantList" :key="item.id" :value="item.id"
                            :label="item.orgCompanyName">
                        </a-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="签约方式" field="signWay">
                    <a-select
                        :disabled="disabledFields.includes('signWay')"
                        v-model="contractData.signWay"
                        placeholder="请选择签约方式">
                        <a-option v-for="item in signTypeOptions" :key="item.value" :value="item.value">
                            {{ item.label }}
                        </a-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="用地性质" field="landUsage">
                    <a-select :disabled="disabledFields.includes('landUsage')" v-model="contractData.landUsage" placeholder="请选择用地性质">
                        <a-option v-for="item in landUsageList" :key="item.value" :value="item.label">
                            {{ item.label }}
                        </a-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="合同签约人" field="signerName">
                    <a-select :disabled="disabledFields.includes('signerName')" v-model="contractData.signerName" placeholder="请选择签约人" allow-search :loading="loading"
                        @search="handleSignerSearch" :filter-option="false" @change="handleSignerChange">
                        <a-option v-for="item in signerOptions" :key="item.value" :value="item.label">
                            {{ item.label }}
                        </a-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="制单人">
                    <div class="detail-text">{{ contractData.createByName || userName }}</div>
                </a-form-item>
                <a-form-item label="制单日期">
                    <div class="detail-text">{{ contractData.createTime || createDate }}</div>
                </a-form-item>
            </a-form>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { ref, computed, } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { useContractStore } from '@/store/modules/contract'
import { FormInstance } from '@arco-design/web-vue';
import { useUserStore } from '@/store';
import dayjs from 'dayjs';
import { getEhrUserList } from '@/api/ehr'

const userStore = useUserStore()
const userName = computed(() => userStore.nickName)
const createDate = computed(() => dayjs().format('YYYY-MM-DD'))

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)
const landUsageList = computed(() => contractStore.landUsageList)
const merchantList = computed(() => contractStore.merchantList)

/**
 * @description 编辑类型
 * 1. 详情
 *    - 所有字段不可修改，且展示成文本
 * 2. 变更
 *    - 都不可修改
 */
const editType = computed(() => contractStore.editType)
const isDetailMode = computed(() =>
  editType.value === 'detail' ||
  editType.value === 'changeDetail' ||
  editType.value === 'updateBank' ||
  editType.value === 'updateContact' ||
  editType.value === 'updateRentNo'
)
const formItemMargin = computed(() => isDetailMode.value ? '16px' : '16px')
const disabledFields = computed(() => {
    if (editType.value === 'change') {
        return ['ourSigningId', 'signWay', 'landUsage', 'signerName']
    }
    if (editType.value === 'updateSignMethod') {
        // 只禁用主体、用地性质、签约人，签约方式可编辑
        return ['ourSigningParty', 'landUsage', 'signerName']
    }
    return []
})

// 表单验证规则
const rules = ref({
    ourSigningId: [{ required: true, message: '请选择甲方签约主体公司' }],
    signWay: [{ required: true, message: '请选择签约方式' }],
    landUsage: [{ required: true, message: '用地性质不能为空' }],
    signerName: [{ required: true, message: '请选择合同签约人' }],
})

// 签约方式选项
const signTypeOptions = [
    { label: '电子合同', value: 0 },
    { label: '纸质合同（甲方电子章）', value: 1 },
    { label: '纸质合同（双方实体章）', value: 2 }
]

// 签约主体公司选项
interface CompanyOption {
    name: string;
    value: string;
}
// 签约人搜索
interface SignerOption {
    label: string;
    value: string;
}
const signerOptions = ref<SignerOption[]>([])
const loading = ref(false)
const handleSignerSearch = async (value: string) => {
    if (value) {
        loading.value = true
        const res = await getEhrUserList({
            keyword: value,
            pageNum: 1,
            pageSize: 10
        })
        loading.value = false
        signerOptions.value = res.rows.map((item: any) => ({
            label: item.name,
            value: item.id
        }))
    } else {
        signerOptions.value = []
    }
}

const handleSignerChange = (value: string) => {
    const signer = signerOptions.value.find(item => item.label === value)
    contractData.value.signerId = signer?.value || ''
}

const handleOurSigningChange = (value: string) => {
    const merchant = merchantList.value.find(item => item.id === value)
    contractData.value.ourSigningParty = merchant?.orgCompanyName || ''
}

const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('甲方签约信息', errors);
            reject()
        } else {
            resolve(true)
        }
    })
}
defineExpose({
    validate
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.detail-text {
    padding: 4px 0;
    color: #333;
}

.arco-form-item {
    margin-bottom: v-bind(formItemMargin);
}
</style>