import http from '../index';

// 字典类型接口返回值类型定义
export interface DictType {
  dictId: number;          // 字典ID
  dictName: string;        // 字典名称
  dictType: string;        // 字典类型
  status: string;          // 状态（0停用 1正常）
  createBy?: string;       // 创建者
  createByName?: string;   // 创建者名称
  createTime?: string;     // 创建时间
  updateBy?: string;       // 更新者
  updateTime?: string;     // 更新时间
  remark?: string;         // 备注
}

export interface ListTypeResponse {
  total: number;           // 总记录数
  rows: DictType[];       // 字典类型列表数据
  code: number;           // 响应状态码
  msg: string;            // 响应消息
}

export interface ListOptionResponse {
  data: DictType[];       // 字典类型列表数据
  code: number;           // 响应状态码
  msg: string;            // 响应消息
}

export interface Response {
  code: number;           // 响应状态码
  msg: string;            // 响应消息
  data: DictType[];       // 字典类型列表数据
}

// 字典数据相关接口
export const listData = (params?: any) =>  {
  return http.get('/business-platform/dict/data/list', params);
}

export const getData = (dictCode: string) => {
  return http.get(`/business-platform/dict/data/${dictCode}`);
}

export const getDicts = (dictType: string) => {
  return http.get(`/business-platform/dict/data/type/${dictType}`);
}

export const getDictsTree = (dictType: string) => {
  return http.get(`/business-platform/dict/data/type/tree/${dictType}`);
}

export const addData = (data: any) => {
  return http.post('/business-platform/dict/data', data);
}

export const updateData = (data: any) => {
  return http.put('/business-platform/dict/data', data);
}

export const delData = (dictCode: number) => {
  return http.delete(`/business-platform/dict/data/${dictCode}`);
}

// 字典类型相关接口
export const listType = (params?: any) => {
  return http.get('/business-platform/dict/type/list', params);
}

export const getType = (dictIds: number) => {
  return http.get(`/business-platform/dict/type/${dictIds}`);
}

export const addType = (data: any) => {
  return http.post('/business-platform/dict/type', data);
}

export const updateType = (data: any) => {
  return http.put('/business-platform/dict/type', data);
}

export const delType = (dictIds: Array<string>) => {
  return http.delete(`/business-platform/dict/type/${dictIds}`);
}

export const refreshCache = () => {
  return http.delete('/business-platform/dict/type/refreshCache');
}

export const optionselect = () => {
  return http.get('/business-platform/dict/type/optionselect');
}

export const exportDict = (data: any) => {
  return http.download('/business-platform/dict/type/export', data);
}