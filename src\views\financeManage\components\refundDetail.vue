<template>
    <a-drawer :visible="drawerVisible" :title="drawerTitle" unmount-on-close @cancel="handleCancel" class="common-drawer" width="1200px">
        <refundDetailForm ref="refundDetailFormRef" :refundData="refundData" :isEditMode="isEditMode" />
        <!-- 底部按钮 - 仅编辑模式显示 -->
        <template #footer>
            <div class="drawer-footer">
                <a-space>
                    <a-button @click="handleCancelEdit">取消</a-button>
                    <a-button type="primary" status="success" @click="handleSave" v-if="isEditMode">保存</a-button>
                    <a-button type="primary" @click="handleSubmit" v-if="isEditMode">提交审批</a-button>
                </a-space>
            </div>
        </template>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import type { TableColumnData } from '@arco-design/web-vue'
import { FormInstance } from '@arco-design/web-vue'
import refundDetailForm from './refundDetailForm.vue'
import {
    getFinancialRefundDetail,
    saveFinancialRefund,
    type FinancialRefundVo,
    type FinancialRefundAddDTO,
    type FinancialRefundDetailVo,
    type ContractTerminateVo,
    type BookingVo,
    type FinancialFlowVo,
    type CostFlowRelVo
} from '@/api/refundManage'

// 抽屉控制
const drawerVisible = ref(false)
const isEditMode = ref(false)
const formRef = ref<FormInstance>()
const refundDetailFormRef = ref<any>(null)

// 计算标题
const drawerTitle = computed(() => {
    return isEditMode.value ? '编辑退款详情' : '查看退款详情'
})

// 退款数据
const refundData = reactive<FinancialRefundVo>({
    id: '',
    projectName: '',
    refundType: 0,
    bizId: '',
    refundTarget: '',
    applyTime: '',
    refundAmount: 0,
    feeType: '',
    refundWay: 0,
    receiverName: '',
    receiverBank: '',
    receiverAccount: '',
    refundRemark: '',
    refundTime: '',
    refundStatus: 0,
    approveStatus: 0,
    attachments: ''
})

// 表单数据
const formData = reactive<FinancialRefundAddDTO>({
    id: '',
    projectId: '',
    refundType: 0,
    bizId: '',
    refundTarget: '',
    applyTime: '',
    refundAmount: 0,
    feeType: '',
    refundWay: 0,
    receiverName: '',
    receiverBank: '',
    receiverAccount: '',
    refundRemark: '',
    attachments: ''
})

// 判断是否可编辑（仅草稿状态可编辑）
const canEdit = computed(() => {
    return refundData.refundStatus === 0
})

// 合同信息（退租退款时显示）
const contractInfo = reactive<ContractTerminateVo>({
    id: '',
    contractId: '',
    unionId: '',
    bondReceivedAmount: 0,
    rentReceivedAmount: 0,
    rentOverdueAmount: 0,
    receivedPeriod: '',
    overduePeriod: '',
    terminateType: 0,
    terminateDate: '',
    terminateReason: '',
    otherReasonDesc: '',
    hasOtherDeduction: false,
    otherDeductionDesc: '',
    terminateRemark: '',
    terminateAttachments: '',
    createByName: '',
    updateByName: '',
    isDel: false
})

// 订单信息（退定退款时显示）
const orderInfo = reactive<BookingVo>({
    id: '',
    projectId: '',
    projectName: '',
    customerName: '',
    propertyType: '',
    roomId: '',
    roomName: '',
    bookingNo: '',
    bookingAmount: 0,
    receivableDate: '',
    expectSignDate: '',
    isRefundable: 0,
    cancelTime: '',
    cancelBy: '',
    cancelByName: '',
    cancelReason: 0,
    isRefund: 0,
    cancelEnclosure: '',
    cancelRemark: '',
    status: 0,
    contractId: '',
    refundId: '',
    createBy: '',
    createByName: '',
    createTime: '',
    updateBy: '',
    updateByName: '',
    updateTime: '',
    isDel: false,
    contractNo: '',
    signDate: '',
    contractLeaseUnit: '',
    lesseeName: '',
    receivedAmount: 0,
    receivedDate: '',
    payMethod: ''
})

// 流水信息（未明流水退款时显示）
const flowInfo = reactive<FinancialFlowVo>({
    id: '',
    projectId: '',
    orderNo: '',
    payDirection: 0,
    payType: 0,
    payMethod: 0,
    targetType: 0,
    target: '',
    entryTime: '',
    status: 0,
    amount: 0,
    usedAmount: 0,
    payerName: '',
    payerPhone: '',
    payerAccount: '',
    payRemark: '',
    merchant: '',
    payChannel: '',
    sourceNo: '',
    isOtherIncome: false,
    otherIncomeDesc: '',
    createByName: '',
    updateByName: '',
    isDel: false
})

// 退款流水数据
const refundFlowData = ref<Array<{
    flowId: string
    operationType: string
    amount: number
    operationTime: string
    operatorName: string
    flowStatus: string
    remark: string
}>>([])

// 退款结算明细数据（退租退款专用）
const settlementDetailData = ref<Array<{
    incomeType: string
    feeCategory: string
    amount: number
    feePeriod: string
    feeDescription: string
}>>([])

// 退款结算明细表格列定义
const settlementDetailColumns = ref<TableColumnData[]>([
    {
        title: '收支类型',
        dataIndex: 'incomeType',
        width: 120,
        align: 'center'
    },
    {
        title: '费用科目',
        dataIndex: 'feeCategory',
        width: 150,
        align: 'center'
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 120,
        align: 'center',
        slotName: 'settlementAmount'
    },
    {
        title: '费用周期',
        dataIndex: 'feePeriod',
        width: 180,
        align: 'center'
    },
    {
        title: '费用说明',
        dataIndex: 'feeDescription',
        align: 'center',
        ellipsis: true,
        tooltip: true
    }
])

// 退租结算信息
const settlementInfo = reactive({
    discountAmount: 0,        // 减免金额
    finalAmount: 0,           // 最终费用金额
    discountReason: '',       // 减免原因
    businessLicense: '',      // 营业执照
    taxRegistration: ''       // 税务登记证
})

// 附件列表
const attachmentList = ref<any[]>([])
const fileList = ref<any[]>([])

// 退款类型名称映射
const getRefundTypeName = (type: number | undefined) => {
    const typeMap: Record<number, string> = {
        0: '退租退款',
        1: '退定退款',
        2: '未明流水退款'
    }
    return typeMap[type || 0] || ''
}

// 退款状态名称映射
const getRefundStatusName = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '草稿',
        1: '待退款',
        2: '已退款',
        3: '作废'
    }
    return statusMap[status || 0] || ''
}

// 退款方式名称映射
const getRefundMethodName = (method: number | undefined) => {
    const methodMap: Record<number, string> = {
        0: '原路退回',
        1: '银行转账'
    }
    return methodMap[method || 0] || ''
}

// 获取退款类型颜色
const getRefundTypeColor = (type: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'purple',    // 退租退款
        1: 'blue',      // 退定退款
        2: 'cyan'       // 未明流水退款
    }
    return colorMap[type || 0] || 'default'
}

// 获取退款状态颜色
const getRefundStatusColor = (status: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'gray',      // 草稿
        1: 'orange',    // 待退款
        2: 'green',     // 已退款
        3: 'red'        // 作废
    }
    return colorMap[status || 0] || 'default'
}

// 获取退款方式颜色
const getRefundMethodColor = (method: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'green',     // 原路退回
        1: 'orange'     // 银行转账
    }
    return colorMap[method || 0] || 'default'
}

// 获取流水状态颜色
const getFlowStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        '已完成': 'green',
        '处理中': 'orange',
        '已取消': 'red'
    }
    return colorMap[status] || 'default'
}

// 金额格式化
const formatMoney = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return '0.00'
    return amount.toFixed(2)
}

// 获取订单状态名称
const getOrderStatusName = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '草稿',
        1: '待收费',
        2: '已生效',
        3: '已转签',
        4: '已作废'
    }
    return statusMap[status || 0] || ''
}

// 获取支付方式名称
const getPayMethodName = (method: number | undefined) => {
    const methodMap: Record<number, string> = {
        0: '微信支付',
        1: '支付宝',
        2: '银行转账',
        3: '现金'
    }
    return methodMap[method || 0] || ''
}

// 获取流水状态名称
const getFlowStatusName = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '未记账',
        1: '部分记账',
        2: '已记账'
    }
    return statusMap[status || 0] || ''
}

// 加载退款详情数据
const loadRefundDetail = async (refundId: string, refundType?: number, bizId?: string, mode?: string) => {
    try {
        const response = await getFinancialRefundDetail(refundId, refundType, bizId)
        if (response.code === 200 && response.data) {
            const detailData = response.data

            // 更新退款基本信息
            if (detailData.refund) {
                Object.assign(refundData, detailData.refund)
            }

            // 解析附件
            if (refundData.attachments) {
                try {
                    attachmentList.value = JSON.parse(refundData.attachments)
                } catch (e) {
                    attachmentList.value = []
                }
            }

            console.log('refundData', refundData)
            console.log('refundRemark值:', refundData.refundRemark)

            // 根据退款类型加载相关信息
            if (refundData.refundType === 0 && detailData.terminate) {
                // 退租退款 - 加载合同终止信息
                Object.assign(contractInfo, detailData.terminate)

                // 加载退款结算明细数据（模拟数据，实际应从API获取）
                settlementDetailData.value = [
                    {
                        incomeType: '收入',
                        feeCategory: '保证金',
                        amount: 5000,
                        feePeriod: '2024-01-01 至 2024-12-31',
                        feeDescription: '房屋保证金退还'
                    },
                    {
                        incomeType: '支出',
                        feeCategory: '水电费',
                        amount: -200,
                        feePeriod: '2024-12-01 至 2024-12-31',
                        feeDescription: '水电费扣除'
                    },
                    {
                        incomeType: '支出',
                        feeCategory: '维修费',
                        amount: -300,
                        feePeriod: '2024-12-15',
                        feeDescription: '房屋维修费用'
                    }
                ]

                // 加载退租结算信息（模拟数据，实际应从API获取）
                Object.assign(settlementInfo, {
                    discountAmount: 100,
                    finalAmount: 4500,
                    discountReason: '首次租户优惠',
                    businessLicense: '已提供',
                    taxRegistration: '已提供'
                })
            } else if (refundData.refundType === 1 && detailData.booking) {
                // 退定退款 - 加载订单信息
                Object.assign(orderInfo, detailData.booking)
            } else if (refundData.refundType === 2 && detailData.flow) {
                // 未明流水退款 - 加载流水信息
                Object.assign(flowInfo, detailData.flow)
            }

            // 加载退款流水信息
            if (detailData.flowRels && detailData.flowRels.length > 0) {
                refundFlowData.value = detailData.flowRels.map(item => ({
                    flowId: item.flowNo || '',
                    operationType: getOperationTypeName(item.type),
                    amount: item.payAmount || 0,
                    operationTime: item.createTime || '',
                    operatorName: item.createByName || '',
                    flowStatus: getConfirmStatusName(item.confirmStatus),
                    remark: item.target || ''
                }))
            }
            if (mode === 'edit') {
                handleEdit()
            }
        }
    } catch (error) {
        console.error('加载退款详情失败:', error)
        Message.error('加载退款详情失败')
    }
}

// 获取操作类型名称
const getOperationTypeName = (type: number | undefined) => {
    const typeMap: Record<number, string> = {
        1: '收款',
        2: '转入',
        3: '转出',
        4: '退款'
    }
    return typeMap[type || 0] || ''
}

// 获取确认状态名称
const getConfirmStatusName = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '未确认',
        1: '自动确认',
        2: '手动确认'
    }
    return statusMap[status || 0] || ''
}

// 切换到编辑模式
const handleEdit = () => {
    isEditMode.value = true
    // 复制数据到表单
    Object.assign(formData, {
        id: refundData.id,
        projectId: refundData.projectId,
        refundType: refundData.refundType,
        bizId: refundData.bizId,
        refundTarget: refundData.refundTarget,
        applyTime: refundData.applyTime,
        refundAmount: refundData.refundAmount,
        feeType: refundData.feeType,
        refundWay: refundData.refundWay,
        receiverName: refundData.receiverName,
        receiverBank: refundData.receiverBank,
        receiverAccount: refundData.receiverAccount,
        refundRemark: refundData.refundRemark,
        attachments: refundData.attachments
    })

    console.log('----formData.refundRemark值:', formData)

    // 复制附件到文件列表
    fileList.value = [...attachmentList.value]
}

// 取消编辑
const handleCancelEdit = () => {
    isEditMode.value = false
    drawerVisible.value = false
    fileList.value = []
}

// 保存
const handleSave = async () => {
    try {
        const errors = await formRef.value?.validate()
        if (errors) return

        // 构建保存数据
        const saveData: FinancialRefundAddDTO = {
            ...formData,
            refundType: typeof formData.refundType === 'string' ? Number(formData.refundType) : formData.refundType,
            refundWay: typeof formData.refundWay === 'string' ? Number(formData.refundWay) : formData.refundWay,
            attachments: JSON.stringify(fileList.value),
            isSubmit: false // 暂存
        }

        const response = await saveFinancialRefund(saveData)
        if (response.code === 200) {
            Message.success('保存成功')
            isEditMode.value = false
            drawerVisible.value = false
            // 重新加载数据
            if (refundData.id) {
                loadRefundDetail(refundData.id, refundData.refundType, refundData.bizId, 'view')
            }
        } else {
            Message.error(response.msg || '保存失败')
        }
    } catch (error) {
        console.error('保存失败:', error)
    }
}

// 提交审批
const handleSubmit = async () => {
    try {
        const errors = await formRef.value?.validate()
        if (errors) return

        // 构建提交数据
        const submitData: FinancialRefundAddDTO = {
            ...formData,
            refundType: typeof formData.refundType === 'string' ? Number(formData.refundType) : formData.refundType,
            refundWay: typeof formData.refundWay === 'string' ? Number(formData.refundWay) : formData.refundWay,
            attachments: JSON.stringify(fileList.value),
            isSubmit: true // 提交审批
        }

        const response = await saveFinancialRefund(submitData)
        if (response.code === 200) {
            Message.success('提交成功')
            isEditMode.value = false
            drawerVisible.value = false
            // 通知父组件刷新数据
            emit('refresh')
        } else {
            Message.error(response.msg || '提交失败')
        }
    } catch (error) {
        console.error('提交失败:', error)
    }
}

// 取消
const handleCancel = () => {
    drawerVisible.value = false
    isEditMode.value = false
    fileList.value = []
}

// 查看合同详情
const handleViewContractDetail = () => {
    console.log('查看合同详情:', contractInfo.contractId)
    // TODO: 实现查看合同详情功能
}

// 查看订单详情
const handleViewOrderDetail = () => {
    console.log('查看订单详情:', orderInfo.bookingNo)
    // TODO: 实现查看订单详情功能
}

// 查看流水详情
const handleViewFlowDetail = () => {
    console.log('查看流水详情:', flowInfo.orderNo)
    // TODO: 实现查看流水详情功能
}

// 下载附件
const handleDownloadFile = (file: any) => {
    console.log('下载文件:', file)
    // TODO: 实现文件下载功能
}

// 文件上传处理
const handleFileChange = (fileList: any[]) => {
    console.log('文件变更:', fileList)
}

// 自定义上传请求
const customUploadRequest = (options: any) => {
    const { onProgress, onSuccess, onError, file } = options

    // 模拟上传过程
    const timer = setInterval(() => {
        const percent = Math.floor(Math.random() * 10) + 10
        onProgress(percent)
    }, 300)

    // 模拟上传完成
    setTimeout(() => {
        clearInterval(timer)
        onProgress(100)
        onSuccess()
    }, 1000)
}

// 定义事件
const emit = defineEmits<{
    refresh: []
}>()

// 暴露方法给父组件
defineExpose({
    open(record: FinancialRefundVo, mode: 'view' | 'edit' = 'view') {
        // 先设置基础数据
        Object.assign(refundData, record)
        // console.log('直接设置的refundData:', refundData)
        // console.log('直接设置的refundRemark:', refundData.refundRemark)

        console.log('record:', record, 'mode:', mode, 'refundData:', refundData)

        // 如果有ID，再通过API加载详细数据
        if (record.id) {
            nextTick(() => {
                setTimeout(() => {
                console.log('record.id:', record.id, 'mode:', mode, 'refundData:', refundData, refundDetailFormRef.value)
                refundDetailFormRef.value?.open(record.id, mode, refundData)
                }, 333)
            })
        }

        isEditMode.value = mode === 'edit'
        drawerVisible.value = true
    },
    
    // 直接通过 refundId 调用接口的方法
    async openByRefundId(refundId: string, mode: 'view' | 'edit' = 'view') {
        console.log('通过 refundId 打开:', refundId, mode)
        
        // 重置数据
        Object.assign(refundData, {
            id: '',
            projectName: '',
            refundType: 0,
            bizId: '',
            refundTarget: '',
            applyTime: '',
            refundAmount: 0,
            feeType: '',
            refundWay: 0,
            receiverName: '',
            receiverBank: '',
            receiverAccount: '',
            refundRemark: '',
            refundTime: '',
            refundStatus: 0,
            approveStatus: 0,
            attachments: ''
        })
        nextTick(() => {
            setTimeout(() => {
                refundDetailFormRef.value?.open(refundId, mode, refundData)
            }, 333)
        })
        
        // 直接调用详情接口
        // await loadRefundDetail(refundId, undefined, undefined, mode)
        
        isEditMode.value = mode === 'edit'
        drawerVisible.value = true
    }
})

// 退款流水表格列定义
const refundFlowColumns = ref<TableColumnData[]>([
    {
        title: '流水编号',
        dataIndex: 'flowId',
        width: 180,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '操作类型',
        dataIndex: 'operationType',
        width: 120,
        align: 'center'
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 120,
        align: 'center',
        slotName: 'amount'
    },
    {
        title: '操作时间',
        dataIndex: 'operationTime',
        width: 180,
        align: 'center',
        ellipsis: true, tooltip: true
    },
    {
        title: '操作人',
        dataIndex: 'operatorName',
        width: 120,
        align: 'center'
    },
    {
        title: '流水状态',
        dataIndex: 'flowStatus',
        width: 120,
        align: 'center',
        slotName: 'flowStatus'
    },
    {
        title: '备注',
        dataIndex: 'remark',
        align: 'center',
        ellipsis: true,
        tooltip: true
    }
])
</script>

<style scoped lang="less">
.refund-detail-container {
    padding: 0 16px;

    .form-section {
        margin-bottom: 24px;

        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .section-marker {
                width: 4px;
                height: 16px;
                background-color: #1890ff;
                margin-right: 8px;
                border-radius: 2px;
            }

            span {
                font-size: 16px;
                font-weight: 500;
                flex: 1;
            }
        }

        .view-info {
            .info-row {
                display: flex;
                margin-bottom: 16px;

                &:last-child {
                    margin-bottom: 0;
                }

                .info-item {
                    flex: 1;
                    display: flex;
                    align-items: center;

                    &.full-width {
                        flex: 3;
                        align-items: flex-start;
                    }

                    .info-label {
                        color: #646a73;
                        margin-right: 8px;
                        width: 120px;
                        text-align: right;
                    }

                    .info-value {
                        width: 0;
                        color: #1d2129;
                        flex: 1;

                        &.money {
                            font-weight: 600;
                            color: #f56c6c;
                        }

                        &.remark-content {
                            line-height: 1.5;
                            word-break: break-all;
                            white-space: pre-wrap;
                        }
                    }
                }
            }
        }

        .base-info {
            background-color: #f9f9f9;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 16px;

            .info-row {
                display: flex;
                margin-bottom: 12px;

                &:last-child {
                    margin-bottom: 0;
                }

                .info-item {
                    flex: 1;
                    display: flex;

                    .info-label {
                        color: #646a73;
                        margin-right: 8px;
                        // min-width: 90px;
                        width: 120px;
                        text-align: right;
                    }

                    .info-value {
                        width: 0;
                        color: #1d2129;
                        flex: 1;

                        &.money {
                            font-weight: 600;
                            color: #f56c6c;
                        }
                    }
                }
            }
        }

        .attachment-list {
            .attachment-item {
                display: flex;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                .file-name {
                    flex: 1;
                    margin-left: 8px;
                    color: #1d2129;
                }
            }
        }

        .no-attachment {
            color: #86909c;
            text-align: center;
            padding: 32px 0;
        }
    }

    .money {
        font-weight: 600;
        color: #f56c6c;

        &.negative {
            color: #52c41a;
        }

        &.total-amount {
            color: #1890ff;
            font-size: 14px;
        }
    }
}

.drawer-footer {
    display: flex;
    justify-content: flex-end;
}
</style>