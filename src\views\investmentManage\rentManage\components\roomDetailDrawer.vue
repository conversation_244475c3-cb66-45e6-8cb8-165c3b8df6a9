<template>
  <div class="room-detail-drawer-modal">
    <a-modal v-model:visible="visible" :header="false" :closable="false" :footer="false" width="800px"
      class="room-detail-drawer-modal" :body-style="{ padding: 0 }">
      <div class="room-detail-drawer">
        <!-- 左侧内容 -->
        <div class="room-detail-left">
          <div class="room-title-bg">
            <div class="room-title-main">
              <div class="room-title-number">
                <img src="@/assets/images/rent/title-bg.png" class="title-bg-img" />
                <span class="number">{{ room?.roomName || '-' }}</span>
              </div>
              <div class="room-tags">
                <div v-for="tag in roomTags" :key="tag.type" v-show="!!tag.type" :class="['room-tag', tag.class]">{{ tag.type }}</div>
              </div>
            </div>
          </div>
          <section-title title="房源信息" class="room-section-title"/>
          <div class="info-block">
            <div class="info-row">
              <span class="label">房源名称</span>
              <span class="value">{{ room?.roomName || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">用途</span>
              <span class="value">{{ room?.propertyTypeName || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">计租面积</span>
              <span class="value">{{ room?.rentArea ? room.rentArea + 'm²' : '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">朝向</span>
              <span class="value">{{ room?.orientationName || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">户型</span>
              <span class="value">{{ getHouseTypeName(room?.houseTypeId) || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">表价</span>
              <span class="value">{{ getTablePriceText(room) }}</span>
            </div>
          </div>
          <!-- 租约信息 - 在租、待生效/签约中状态显示 -->
          <template v-if="showContractInfo">
            <section-title title="租约信息" class="room-section-title"/>
            <div class="info-block">
              <div class="info-row">
                <span class="label">合同编号</span>
                <span class="value">{{ room?.contractVo?.contractNo || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="label">合同类别</span>
                <span class="value">{{ getContractTypeText(room?.contractVo?.contractType) }}</span>
              </div>
              <div class="info-row">
                <span class="label">签约类型</span>
                <span class="value">{{ getSignTypeText(room?.contractVo?.signType) }}</span>
              </div>
              <div class="info-row">
                <span class="label">承租类型</span>
                <span class="value">{{ getTenantTypeText(room?.contractVo?.tenantType) }}</span>
              </div>
              <div class="info-row">
                <span class="label">承租人</span>
                <span class="value">{{ room?.contractVo?.tenantName || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="label">承租人证件号</span>
                <span class="value">{{ room?.contractVo?.tenantIdCard || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="label">合同租期</span>
                <span class="value">{{ getContractPeriodText(room?.contractVo) }}</span>
              </div>
              <div class="info-row">
                <span class="label">租金</span>
                <span class="value">{{ room?.contractVo?.monthlyPrice ? room.contractVo.monthlyPrice + '元/月' : '-' }}</span>
              </div>
            </div>
          </template>
          
          <!-- 预定信息 - 已预定状态显示 -->
          <template v-if="showBookingInfo">
            <section-title title="预定信息" class="room-section-title"/>
            <div class="info-block">
              <div class="info-row">
                <span class="label">客户名称</span>
                <span class="value">{{ room?.bookingVo?.customerName || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="label">定单金额</span>
                <span class="value">{{ room?.bookingVo?.bookingAmount ? room.bookingVo.bookingAmount + '元' : '-' }}</span>
              </div>
              <div class="info-row">
                <span class="label">预定时间</span>
                <span class="value">{{ formatDate(room?.bookingVo?.bookingTime) || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="label">是否可退</span>
                <span class="value">{{ room?.bookingVo?.canRefund ? '是' : '否' }}</span>
              </div>
            </div>
          </template>
          <section-title title="房源租控" class="room-section-title"/>
          <div class="info-block">
            <a-checkbox-group v-permission="['rent:room:setCondition']" v-model="roomStatus" direction="horizontal" @change="handleRoomStatusChange">
              <a-checkbox value="locked">锁房</a-checkbox>
              <a-checkbox value="dirty">脏房</a-checkbox>
              <a-checkbox value="maintenance">维修</a-checkbox>
            </a-checkbox-group>
          </div>
        </div>
        <!-- 右侧快捷操作区 -->
        <div class="room-detail-right">
          <div class="quick-section">
            <div class="quick-title">房源</div>
            <a-button v-for="btn in getRightButtons.房源" :key="btn" type="text" class="quick-btn" @click="handleButtonClick('房源', btn)">{{ btn }}</a-button>
          </div>
          <div v-if="getRightButtons.租约.length > 0" class="quick-section">
            <div class="quick-title">租约</div>
            <a-button v-for="btn in getRightButtons.租约" :key="btn" type="text" class="quick-btn" @click="handleButtonClick('租约', btn)">{{ btn }}</a-button>
          </div>
          

        </div>
      </div>
    </a-modal>
    
    <!-- 房源详情抽屉 -->
    <add-property-drawer v-if="addPropertyDrawerVisible" ref="addPropertyDrawerRef" @cancel="handleAddPropertyCancel" />
    
    <!-- 合同详情抽屉 -->
    <contract-detail-drawer ref="contractDetailRef" @submit="handleContractDetailSubmit" />
    
    <!-- 合同退租抽屉 -->
    <ContractTerminationMain ref="contractTerminationRef" v-model="contractTerminationVisible"
      @save="handleTerminationSave" @submit="handleTerminationSubmit" @close="handleTerminationClose" />
    
    <!-- 新增合同抽屉 -->
    <add-contract-drawer ref="addContractRef" @submit="handleContractSubmit" />
    
    <!-- 新增订单抽屉 -->
    <add-order-form ref="addOrderFormRef" @success="handleOrderSuccess" />
    
    <!-- 合同类型选择弹窗 -->
    <a-modal 
        v-model:visible="contractTypeModalVisible" 
        title="选择合同类型" 
        width="400px"
        @cancel="handleContractTypeCancel"
        :footer="false">
        <div class="contract-type-selection">
            <p class="selection-tip">该预定未确认具体房源，请选择要创建的合同类型：</p>
            <div class="contract-type-options">
                <div 
                    v-for="option in contractTypeOptions" 
                    :key="option.value"
                    class="contract-type-option"
                    @click="handleContractTypeConfirm(option.value)">
                    <div class="option-content">
                        <div class="option-icon">
                            <icon-file />
                        </div>
                        <div class="option-text">
                            <span class="option-label">{{ option.label }}</span>
                        </div>
                        <div class="option-arrow">
                            <icon-right />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick, onMounted, h } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconFile, IconRight } from '@arco-design/web-vue/es/icon'
import sectionTitle from '@/components/sectionTitle/index.vue'
import addPropertyDrawer from '@/views/property/properties/components/addPropertyDrawer.vue'
import { getHouseTypeList } from '@/api/project'
import { setRoomCondition } from '@/api/room'
// 引入合同相关组件
import contractDetailDrawer from '@/views/contract/contractDetailDrawer.vue'
import ContractTerminationMain from '@/views/contractManage/contractTerminationMain.vue'
import addContractDrawer from '@/views/contract/addContractDrawer.vue'
// 引入订单管理组件
import addOrderForm from '@/views/orderManagement/components/addForm.vue'

const props = defineProps<{ visible: boolean, room: any, roomTags: any[], selectionInfo: any }>()
const emit = defineEmits(['update:visible', 'refresh'])

const roomStatus = ref<string[]>([])
const addPropertyDrawerRef = ref()
const addPropertyDrawerVisible = ref(false)

// 合同相关组件引用
const contractDetailRef = ref()
const contractTerminationRef = ref()
const addContractRef = ref()
const contractTerminationVisible = ref(false)

// 订单相关组件引用
const addOrderFormRef = ref()

// 开发环境标识


// 合同类型选择相关（仅用于暂未确认房源的预定转签约）
const contractTypeModalVisible = ref(false)
const currentContractRecord = ref<any>(null)
const currentContractOperation = ref<'booking-to-contract'>('booking-to-contract')

// 合同类型选项
const contractTypeOptions = [
  { label: '非宿舍合同', value: 0 },
  { label: '宿舍合同', value: 1 },
  { label: '多经合同', value: 2 }
]

// 价格单位映射
const priceUnitMap: Record<number, string> = {
  1: '元/平方米/月',
  2: '元/月',
  3: '元/日'
}

// 根据房源类型判断合同类型
const getContractTypeByRoom = (room: any): number => {
  if (!room) return 0
  
  // 如果房源类型为2（多经），直接返回多经合同类型
  if (room.type === 2) {
    return 2 // 多经合同
  }
  
  // 如果房源类型为1（普通），则根据物业类型判断
  if (room.type === 1) {
    // 如果物业类型为"10"（宿舍），返回宿舍合同
    if (room.propertyType === '10') {
      return 1 // 宿舍合同
    } else {
      return 0 // 非宿舍合同
    }
  }
  
  // 默认返回非宿舍合同
  return 0
}

// 户型数据
const houseTypeList = ref<any[]>([])
const houseTypeMap = ref<Record<string, string>>({})

// 加载户型列表
const loadHouseTypeList = async (projectId: string) => {
    if (!projectId) {
        houseTypeList.value = []
        houseTypeMap.value = {}
        return
    }
    
    try {
        const response = await getHouseTypeList(projectId)
        if (response.code === 200) {
            houseTypeList.value = response.data || []
            // 创建户型ID到名称的映射
            const map: Record<string, string> = {}
            houseTypeList.value.forEach((item: any) => {
                if (item.id && item.houseTypeName) {
                    map[item.id] = item.houseTypeName
                }
            })
            houseTypeMap.value = map
        }
    } catch (error) {
        console.error('加载户型列表失败:', error)
    }
}

// 根据户型ID获取户型名称
const getHouseTypeName = (houseTypeId: string) => {
    return houseTypeMap.value[houseTypeId] || ''
}

// 获取表价文本
const getTablePriceText = (room: any) => {
  if (!room) return '-'
  if (room.baseRent) {
    return `${room.baseRent}${priceUnitMap[room.priceUnit] || '元/月'}`
  }
  return '未定价'
}

// 格式化日期
const formatDate = (dateStr: string | null | undefined) => {
  if (!dateStr) return ''
  if (dateStr.includes('T')) {
    return dateStr.split('T')[0]
  }
  return dateStr
}

// 获取签约类型文本
const getSignTypeText = (signType: number | undefined) => {
  const signTypeMap: Record<number, string> = {
    0: '新签',
    1: '续签'
  }
  return signType !== undefined ? signTypeMap[signType] || '-' : '-'
}

// 获取合同类别文本
const getContractTypeText = (contractType: number | undefined) => {
  const contractTypeMap: Record<number, string> = {
    0: '非宿舍',
    1: '宿舍',
    2: '多经',
    3: '日租房'
  }
  return contractType !== undefined ? contractTypeMap[contractType] || '-' : '-'
}

// 获取承租类型文本
const getTenantTypeText = (tenantType: string | number | undefined) => {
  const tenantTypeMap: Record<string, string> = {
    '1': '个人',
    '2': '企业'
  }
  return tenantType !== undefined ? tenantTypeMap[String(tenantType)] || '-' : '-'
}

// 获取合同租期文本
const getContractPeriodText = (contract: any) => {
  if (!contract || !contract.startDate || !contract.endDate) return '-'
  return `${formatDate(contract.startDate)} 至 ${formatDate(contract.endDate)}`
}

// 判断是否显示租约信息
const showContractInfo = computed(() => {
  return props.room?.roomStatus === 2 || props.room?.roomStatus === 3
})

// 判断是否显示预定信息
const showBookingInfo = computed(() => {
  return props.room?.bookingVo && (props.room?.roomStatus === 3 || props.room?.tags?.includes('已预定'))
})

// 获取右侧按钮配置
const getRightButtons = computed(() => {
  const roomStatus = props.room?.roomStatus
  const hasBooking = props.room?.bookingVo
  const hasContract = props.room?.contractVo
  
  const buttons: { 房源: string[], 租约: string[] } = {
    房源: ['房源档案'],
    租约: []
  }
  
  switch (roomStatus) {
    case 1: // 空置
      buttons.租约 = ['租客预定', '租客签约']
      break
    case 2: // 在租
      buttons.租约 = ['查看合同', '租客续约', '租客换房', '租客退租']
      break
    case 3: // 待生效/签约中
      // 如果既有合同又有预定，显示所有相关按钮
      if (hasContract && hasBooking) {
        buttons.租约 = ['查看合同', '租客预定', '租客签约', '预定转签约']
      } else if (hasBooking) {
        // 只有预定
        buttons.租约 = ['预定转签约']
      } else if (hasContract) {
        // 只有合同
        buttons.租约 = ['查看合同', '租客预定', '租客签约']
      } else {
        // 都没有
        buttons.租约 = ['租客预定', '租客签约']
      }
      break
    case 4: // 不可招商
      buttons.租约 = []
      break
    default:
      buttons.租约 = ['租客预定', '租客签约']
  }
  
  return buttons
})
// 处理房源新增/编辑取消事件
const handleAddPropertyCancel = () => {
    addPropertyDrawerVisible.value = false
}
// 处理按钮点击
const handleButtonClick = (category: string, buttonText: string) => {

  
  if (category === '房源' && buttonText === '房源档案') {
    // 打开房源详情页面（只读模式）
    if (props.room?.roomId) {
      // 构建项目选择信息
      const selection = {
        projectId: props.selectionInfo.projectId || '',
        projectName: props.selectionInfo.projectName || '',
        blockId: props.selectionInfo.blockId || '',
        blockName: props.selectionInfo.blockName || '',
        buildingId: props.selectionInfo.buildingId || '',
        buildingName: props.selectionInfo.buildingName || ''
      }
      

      addPropertyDrawerVisible.value = true
      nextTick(() => {
        addPropertyDrawerRef.value?.show('view', props.room.roomId, selection)
      })
    } else {
      Message.warning('房源信息不完整，无法查看详情')
    }
  } else if (category === '租约') {
    handleRentalButtonClick(buttonText)
  }
}

// 处理租约相关按钮点击
const handleRentalButtonClick = (buttonText: string) => {
  const room = props.room
  if (!room) {
    Message.warning('房间信息不完整')
    return
  }

  switch (buttonText) {
    case '查看合同':
      viewContract()
      break
    case '租客续约':
      renewContract()
      break
    case '租客退租':
      quitContract()
      break
    case '租客预定':
      handleBooking()
      break
    case '租客签约':
      handleDirectContract()
      break
    case '租客换房':
      handleRoomChange()
      break
    case '预定转签约':
      handleBookingToContract()
      break
    default:
    Message.info(`${buttonText}功能开发中...`)
  }
}

// 查看合同
const viewContract = () => {
  const contract = props.room?.contractVo
  if (!contract) {
    Message.warning('当前房间没有合同信息')
    return
  }
  
  // 构建合同记录对象，参照 contract/list.vue 的格式
  const contractRecord = {
    id: contract.contractId || '',
    contractNo: contract.contractNo || '',
    customerName: contract.tenantName || '',
    roomName: props.room?.roomName || '',
    projectId: props.selectionInfo.projectId || '',
    projectName: props.selectionInfo.projectName || '',
    ...contract
  }
  

  
  contractDetailRef.value?.open(contractRecord)
}

// 续签合同
const renewContract = () => {
  const contract = props.room?.contractVo
  if (!contract) {
    Message.warning('当前房间没有合同信息')
    return
  }
  
  // 构建合同记录对象
  const contractRecord = {
    id: contract.contractId || '',
    contractNo: contract.contractNo || '',
    customerName: contract.tenantName || '',
    roomName: props.room?.roomName || '',
    projectId: props.selectionInfo.projectId || '',
    projectName: props.selectionInfo.projectName || '',
    ...contract
  }
  

  
  addContractRef.value?.open('renew', '', contractRecord)
}

// 退租
const quitContract = () => {
  const contract = props.room?.contractVo
  if (!contract) {
    Message.warning('当前房间没有合同信息')
    return
  }
  
  // 只有生效中状态的合同才能退租
  if (contract.status !== 30) {
    Message.warning('只有生效中状态的合同才能退租')
    return
  }
  

  
  // 参照 contract/list.vue 的退租逻辑
  const modal = Modal.open({
    title: '退租',
    content: () => {
      return h('div', {}, [
        h('div', {
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            marginBottom: '16px'
          }
        }, [
          h('span', {
            style: {
              width: '20px',
              height: '20px',
              borderRadius: '50%',
              backgroundColor: '#ff7d00',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              fontWeight: 'bold'
            }
          }, '!'),
          h('span', {
            style: {
              width: '0',
              flex: 1
            }
          }, [
            h('span', { style: { fontWeight: 'bold' } }, '先申请退租，后续结账：'),
            '先发起退租申请，确认退租日期，暂时不做出场和结算，仅提醒该房源预计释放日期'
          ])
        ]),
        h('div', {
          style: {
            marginLeft: '28px'
          }
        }, [
          h('span', { style: { fontWeight: 'bold' } }, '退租并出场结算：'),
          '实际退租时发起，完成退租流程后，房源释放'
        ])
      ])
    },
    width: 520,
    footer: () => {
      return h('div', {
        style: {
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '8px'
        }
      }, [
        h('button', {
          class: 'arco-btn arco-btn-secondary',
          onClick: () => {
            modal.close()
          }
        }, '取消'),
        h('button', {
          class: 'arco-btn arco-btn-primary',
          onClick: () => {
            // 先申请退租，后续结账
            modal.close()
            if (contractTerminationRef.value) {
              contractTerminationRef.value.open({ 
                contractId: contract.contractId, 
                terminateId: contract.terminateId || '',
                mode: 'apply-only'
              })
            }
          }
        }, '先申请退租，后续结账'),
        h('button', {
          class: 'arco-btn arco-btn-primary',
          onClick: () => {
            // 退租并出场结算
            modal.close()
            if (contractTerminationRef.value) {
              contractTerminationRef.value.open({ 
                contractId: contract.contractId, 
                terminateId: contract.terminateId || '',
                mode: 'apply-and-exit'
              })
            }
          }
        }, '退租并出场结算')
      ])
    }
  })
}

// 合同相关事件处理函数
const handleContractDetailSubmit = () => {
  // 合同详情提交后的处理（查看类操作，不需要刷新）
}

const handleTerminationSave = (data: any) => {
  Message.success('退租申请暂存成功')
  // 暂存操作需要刷新房间状态
  emit('refresh')
}

const handleTerminationSubmit = (data: any) => {
  Message.success('退租申请提交成功')
  // 提交操作需要刷新房间状态
  emit('refresh')
}

const handleTerminationClose = () => {
  contractTerminationVisible.value = false
}

const handleContractSubmit = () => {
  // 合同提交处理
  Message.success('合同操作成功')
  // 合同保存/提交操作需要刷新房间状态
  emit('refresh')
}

// 订单提交成功回调
const handleOrderSuccess = () => {
  Message.success('订单创建成功')
  // 订单创建成功后刷新房间状态
  emit('refresh')
}

// 租客预定 - 参照 orderManagementList.vue 的新增逻辑
const handleBooking = () => {
  const projectId = props.selectionInfo?.projectId
  if (!projectId) {
    Message.warning('请先选择项目')
    return
  }
  
  // 构建房源信息
  const roomInfo = {
    roomId: props.room?.roomId || '',
    roomName: props.room?.roomName || '',
    propertyType: props.room?.propertyType || '',
    propertyTypeName: props.room?.propertyTypeName || '',
    projectId: projectId,
    projectName: props.selectionInfo?.projectName || ''
  }
  
  console.log('租客预定传递房源信息:', roomInfo)
  
  // 打开订单新增抽屉，传入项目ID和房源信息
  addOrderFormRef.value?.open(projectId, roomInfo)
}

// 租客签约 - 参照 contract/list.vue 的新增合同逻辑
const handleDirectContract = () => {
  const projectId = props.selectionInfo?.projectId
  if (!projectId) {
    Message.warning('请选择项目')
    return
  }

  // 根据房源类型自动判断合同类型
  const contractType = getContractTypeByRoom(props.room)
  
  // 构建项目信息
  const project = {
    id: projectId,
    name: props.selectionInfo?.projectName || ''
  }

  // 构建房间信息作为参数
  const roomInfo = {
    roomId: props.room?.roomId || '',
    roomName: props.room?.roomName || '',
    projectId: project.id,
    projectName: project.name,
    isDirectContract: true, // 添加直接签约标识
    // 可以添加更多房间相关信息
    ...props.room
  }
  
  console.log('直接签约，合同类型:', contractType, '房间信息:', roomInfo)
  
  // 直接打开对应类型的合同新增页面
  addContractRef.value?.open('create', contractType, roomInfo, project)
}

// 租客换房
const handleRoomChange = () => {
  // TODO: 实现租客换房功能
  Message.info('租客换房功能开发中...')
}

// 预定转签约处理
const handleBookingToContract = () => {
  try {
    const room = props.room
    const booking = room?.bookingVo
    
    if (!booking) {
      Message.warning('当前房间没有预定信息')
      return
    }
    
    // 保存当前预定记录，构建类似订单的数据结构
    const bookingRecord = {
      id: booking.bookingId || '', // 如果预定没有id
      projectId: props.selectionInfo?.projectId || '',
      projectName: props.selectionInfo?.projectName || '',
      customerName: booking.customerName || '',
      roomName: room.roomName || '',
      roomId: room.roomId || '',
      propertyType: room.propertyType || '',
      type: room.type || 1, // 添加房源类型
      bookingAmount: booking.bookingAmount || 0,
      isRenewalContract: true,
      ...booking
    }
    
    console.log('预定转签约记录:', bookingRecord)
    
    // 判断是否为暂未确认房源
    const isUnknownSource = !bookingRecord.roomId
    
    if (isUnknownSource) {
      // 暂未确认房源的预定仍需要选择合同类型
      currentContractRecord.value = bookingRecord
      currentContractOperation.value = 'booking-to-contract'
      contractTypeModalVisible.value = true
    } else {
      // 已确认房源的预定根据房源类型自动判断合同类型
      const contractType = getContractTypeByRoom(room)
      
      // 构建项目信息
      const project = {
        id: bookingRecord.projectId,
        name: bookingRecord.projectName
      }
      
      console.log('预定转签约，合同类型:', contractType)
      
      // 直接打开对应类型的合同新增页面
      addContractRef.value?.open('create', contractType, bookingRecord, project)
    }
  } catch (error) {
    console.error('预定转签约失败:', error)
  }
}

// 确认选择合同类型（仅用于暂未确认房源的预定转签约）
const handleContractTypeConfirm = (contractType: number) => {
  try {
    if (currentContractOperation.value === 'booking-to-contract' && currentContractRecord.value) {
      // 预定转签约：传入预定信息
      const project = {
        id: currentContractRecord.value.projectId,
        name: currentContractRecord.value.projectName
      }
      
      console.log('暂未确认房源预定转签约，选择合同类型:', contractType)
      
      // 打开合同新增页面，传入预定信息和选择的合同类型
      addContractRef.value?.open('create', contractType, currentContractRecord.value, project)
    }
    
    // 关闭选择弹窗
    contractTypeModalVisible.value = false
    currentContractRecord.value = null
  } catch (error) {
    console.error('合同类型选择失败:', error)
  }
}

// 取消选择合同类型
const handleContractTypeCancel = () => {
  contractTypeModalVisible.value = false
  currentContractRecord.value = null
}

// 监听项目ID变化，加载户型数据
watch(() => props.selectionInfo?.projectId, (newProjectId) => {
  if (newProjectId) {
    loadHouseTypeList(newProjectId)
  }
}, { immediate: true })

watch(() => props.visible, v => { 
  if (!v) {
    roomStatus.value = []
  } else {
    // 根据房间状态设置租控状态
    const room = props.room
    if (room) {
      const status = []
      if (room.isLock) status.push('locked')
      if (room.isDirty) status.push('dirty')
      if (room.isMaintain) status.push('maintenance')
      roomStatus.value = status
    }

    // 当抽屉打开时，如果有项目ID，加载户型数据
    if (props.selectionInfo?.projectId) {
      loadHouseTypeList(props.selectionInfo.projectId)
    }
  }
})

const visible = computed({
  get: () => props.visible,
  set: v => emit('update:visible', v)
})

const roomTags = computed(() => props.roomTags || [])



// 组件挂载时初始化数据
onMounted(() => {
  if (props.selectionInfo?.projectId) {
    loadHouseTypeList(props.selectionInfo.projectId)
  }
  

})

// 房源租控状态变化处理
const handleRoomStatusChange = async (value: string[]) => {
  if (!props.room?.roomId) {
    Message.warning('房间信息不完整')
    return
  }

  try {
    // 确定状态变化 - 勾选传true，未勾选传false
    const isLock = value.includes('locked')
    const isDirty = value.includes('dirty')
    const isMaintain = value.includes('maintenance')

    // 调用接口更新房间状态
    const response = await setRoomCondition({
      roomId: props.room.roomId,
      isLock,
      isDirty,
      isMaintain
    })

    if (response.code === 200) {
      Message.success('房源状态更新成功')
      // 通知父组件刷新房态详图数据
      emit('refresh')
    } else {
      // 如果更新失败，恢复之前的状态
      const room = props.room
      if (room) {
        const status = []
        if (room.isLock) status.push('locked')
        if (room.isDirty) status.push('dirty')
        if (room.isMaintain) status.push('maintenance')
        roomStatus.value = status
      }
    }
  } catch (error) {
    console.error('房源状态更新失败:', error)
    // 如果更新失败，恢复之前的状态
    const room = props.room
    if (room) {
      const status = []
      if (room.isLock) status.push('locked')
      if (room.isDirty) status.push('dirty')
      if (room.isMaintain) status.push('maintenance')
      roomStatus.value = status
    }
  }
}
</script>

<style scoped lang="less">
.room-detail-drawer-modal :deep(.arco-modal-content) {
  border-radius: 14px;
  box-shadow: 0 8px 32px 0 rgba(22, 93, 255, 0.10);
  padding: 0;
  background: #fff;
}
.room-detail-drawer {
  display: flex;
  flex-direction: row;
  max-height: 80vh;
  min-height: 50vh;
  overflow: auto;
  margin-bottom: 16px;
}
.room-detail-left {
  flex: 1;
  padding: 0 0px 24px 0px;
  border-right: 1px solid #f2f3f5;
}
.room-title-bg {
  width: 100%;
  min-height: 72px;
  background: linear-gradient(270deg, #F3F8FD 1%, #EDF5FC 100%);
  border-radius: 14px 0 0 0;
  margin-bottom: 12px;
  display: flex;
  align-items: flex-end;
}
.room-title-main {
  width: 100%;
  padding: 18px 0 8px 24px;
}
.room-title-number {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
  position: relative;
}
.title-bg-img {
  // display: none;
  width: 128px;
  height: 30px;
  position: relative;
}
.number {
  color: #1d2129;
  font-size: 16px;
  font-weight: 600;
  position: absolute;
  left: 60px;
}
.room-tags {
  display: flex;
  gap: 8px;
  margin-top: 18px;
  margin-bottom: 16px;
}
.room-tag {
  font-size: 13px;
  padding: 2px 10px;
  border-radius: 4px;
  font-weight: 500;
}
.room-type {
  background-color: #E2F7FF;
  color: #449CBE;
}
.room-area {
  background: #E5E9F0;
  color: #657D9E;
}
.room-occupancy {
  background: #E5E9F0;
  color: #657D9E;
}
.room-detail-right {
  width: 140px;
  padding: 32px 0 0 24px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.quick-section {
  margin-bottom: 24px;
}

.quick-title {
  font-size: 15px;
  color: #1d2129;
  font-weight: 600;
  margin-bottom: 8px;
}

.quick-btn {
  display: block;
  width: 100%;
  text-align: left;
  color: #1677ff;
  font-size: 15px;
  margin-bottom: 6px;
  padding: 0;
  background: none;
}

.info-block {
  margin-bottom: 18px;
  margin-top: 10px;
  margin-left: 24px;
}
.room-section-title {
  margin-left: 24px;
}
.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 15px;
}

.label {
  color: #86909c;
  min-width: 130px;
  text-align: right;
}

.value {
  color: #1d2129;
  font-weight: 500;
  margin-left: 16px;
}

// 合同类型选择弹窗样式
.contract-type-selection {
    padding: 0 0 16px 0;

    .selection-tip {
        color: #86909c;
        font-size: 14px;
        margin-top: 0 !important;
        margin-bottom: 20px;
        text-align: center;
    }

    .contract-type-options {
        .contract-type-option {
            margin-bottom: 12px;
            border: 1px solid #e5e6eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                border-color: #4080ff;
                background-color: #f8faff;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(64, 128, 255, 0.15);
            }

            &:last-child {
                margin-bottom: 0;
            }

            .option-content {
                display: flex;
                align-items: center;
                padding: 16px 20px;

                .option-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 8px;
                    background: linear-gradient(135deg, #4080ff 0%, #2e5bff 100%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 16px;

                    :deep(.arco-icon) {
                        color: white;
                        font-size: 18px;
                    }
                }

                .option-text {
                    flex: 1;

                    .option-label {
                        font-size: 16px;
                        font-weight: 500;
                        color: #1d2129;
                    }
                }

                .option-arrow {
                    color: #86909c;
                    transition: all 0.3s ease;

                    :deep(.arco-icon) {
                        font-size: 16px;
                    }
                }
            }

            &:hover .option-content .option-arrow {
                color: #4080ff;
                transform: translateX(2px);
            }
        }
    }
}
</style>