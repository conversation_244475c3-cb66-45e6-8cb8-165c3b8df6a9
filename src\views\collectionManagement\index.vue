<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form label-align="right" :model="filterForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
                        auto-label-width>
                        <a-row :gutter="16">
                            <a-col :span="6">
                                <a-form-item field="projectId" label="项目">
                                    <project-tree-select
                                        v-model="filterForm.projectId"
                                        placeholder="请选择项目"
                                        @change="handleProjectChange"
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="contractNo" label="合同号">
                                    <a-input
                                        v-model="filterForm.contractNo"
                                        placeholder="请输入合同号"
                                        allow-clear
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="customerName" label="承租方">
                                    <a-input
                                        v-model="filterForm.customerName"
                                        placeholder="请输入承租方"
                                        allow-clear
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="sendDateRange" label="发送日期">
                                    <a-range-picker
                                        v-model="sendDateRange"
                                        style="width: 100%"
                                        @change="handleSendDateChange"
                                    />
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="16">
                            <a-col :span="6">
                                <a-form-item field="type" label="通知类型">
                                    <a-select
                                        v-model="filterForm.type"
                                        placeholder="请选择通知类型"
                                        allow-clear
                                    >
                                        <a-option :value="1">催缴函</a-option>
                                        <a-option :value="2">催缴通知单</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="viewStatus" label="客户查看状态">
                                    <a-select
                                        v-model="filterForm.viewStatus"
                                        placeholder="请选择客户查看状态"
                                        allow-clear
                                    >
                                        <a-option :value="0">未查看</a-option>
                                        <a-option :value="1">已查看</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="collectFlag" label="当前缴费状态">
                                    <a-select
                                        v-model="filterForm.collectFlag"
                                        placeholder="请选择当前缴费状态"
                                        allow-clear
                                    >
                                        <a-option :value="0">未缴</a-option>
                                        <a-option :value="1">部分缴</a-option>
                                        <a-option :value="2">已缴</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin-top: 0" />

            <!-- 表格 -->
            <a-table
                row-key="id"
                :loading="loading"
                :pagination="pagination"
                :columns="columns"
                :data="tableData"
                :bordered="{ cell: true }"
                :stripe="true"
                @page-change="onPageChange"
                @page-size-change="onPageSizeChange"
            >
                <template #type="{ record }">
                    {{ getCollectionTypeText(record.type) }}
                </template>
                <template #totalMoney="{ record }">
                    {{ formatAmount(record.totalMoney) }}
                </template>
                <template #collectFlag="{ record }">
                     {{ getCollectFlagText(record.collectFlag) }}
                </template>
                <template #viewStatus="{ record }">
                    {{ record.viewStatus === 1 ? '已查看' : '未查看' }}
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <a-button type="text" size="mini" @click="handleView(record)">
                            详情
                        </a-button>
                        <a-button type="text" size="mini" @click="handleDownload(record)">
                            下载
                        </a-button>
                    </a-space>
                </template>
            </a-table>
        </a-card>

        <!-- 催缴详情抽屉 -->
        <collection-detail-drawer
            v-if="showDrawer"
            ref="collectionDetailDrawerRef"
            @cancel="handleDrawerCancel"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { IconSearch, IconRefresh } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import CollectionDetailDrawer from './components/CollectionDetailDrawer.vue'
import {
    getContractBillList,
    downloadContractBill,
    type ContractBillQueryDTO,
    type ContractBillVo
} from '@/api/collectionManagement'



// 筛选表单数据
const filterForm = reactive<ContractBillQueryDTO>({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    contractNo: '',
    customerName: '',
    type: undefined,
    sendTimeStart: '',
    sendTimeEnd: '',
    viewStatus: undefined,
    collectFlag: undefined
})

// 发送时间范围
const sendDateRange = ref<string[]>([])

// 表格数据
const tableData = ref<ContractBillVo[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100]
})

// 表格列配置
const columns = [
    {
        title: '项目',
        dataIndex: 'projectName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 150
    },
    {
        title: '合同号',
        dataIndex: 'contractNo',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 120
    },
    {
        title: '承租方',
        dataIndex: 'customerName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 120
    },
    {
        title: '通知类型',
        dataIndex: 'type',
        slotName: 'type',
        align: 'center',
        width: 100
    },
    {
        title: '催款金额(元)',
        dataIndex: 'totalMoney',
        slotName: 'totalMoney',
        align: 'center',
        width: 120
    },
    {
        title: '当前缴费状态',
        dataIndex: 'collectFlag',
        slotName: 'collectFlag',
        align: 'center',
        width: 120
    },
    {
        title: '发送时间',
        dataIndex: 'sendTime',
        align: 'center',
        width: 150
    },
    {
        title: '客户查看状态',
        dataIndex: 'viewStatus',
        slotName: 'viewStatus',
        align: 'center',
        width: 120
    },
    {
        title: '操作',
        slotName: 'operations',
        align: 'center',
        width: 120,
        fixed: 'right'
    }
]

// 抽屉相关
const collectionDetailDrawerRef = ref()
const showDrawer = ref(false)

// 项目变更控制
const isInit = ref(false)

// 存储当前选中的项目信息
const currentProject = ref<any>({
    projectId: '',
    projectName: ''
})

// 方法
const search = async () => {
    if (!filterForm.projectId) {
        Message.warning('请先选择项目')
        return
    }

    pagination.current = 1
    loadData()
}

// 数据加载
const loadData = async () => {
    if (!filterForm.projectId) {
        return
    }

    try {
        loading.value = true
        const params: ContractBillQueryDTO = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            projectId: filterForm.projectId,
            contractNo: filterForm.contractNo || undefined,
            customerName: filterForm.customerName || undefined,
            type: filterForm.type || undefined,
            sendTimeStart: filterForm.sendTimeStart || undefined,
            sendTimeEnd: filterForm.sendTimeEnd || undefined,
            viewStatus: filterForm.viewStatus || undefined,
            collectFlag: filterForm.collectFlag || undefined
        }

        const response = await getContractBillList(params)

        if (response) {
            tableData.value = response.rows || []
            pagination.total = response.total || 0
        }
    } catch (error) {
        console.error('获取催缴管理列表失败:', error)
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 重置
const reset = () => {
    filterForm.projectId = undefined
    filterForm.contractNo = ''
    filterForm.customerName = ''
    filterForm.type = undefined
    filterForm.sendTimeStart = ''
    filterForm.sendTimeEnd = ''
    filterForm.viewStatus = undefined
    filterForm.collectFlag = undefined
    sendDateRange.value = []
    tableData.value = []
    pagination.current = 1
    pagination.total = 0
}

// 项目变更
const handleProjectChange = (value: string | number, selectedOrg: any) => {
    console.log('项目变化，检查是否有项目ID再调用列表', value, selectedOrg)

    // 存储项目信息
    currentProject.value = {
        projectId: value,
        projectName: selectedOrg?.name || ''
    }

    // 只有在有项目ID时才自动触发搜索
    if (value && !isInit.value) {
        isInit.value = true
        pagination.current = 1
        loadData()
    }
}

// 发送时间变更
const handleSendDateChange = (dateStrings: string[]) => {
    if (dateStrings && dateStrings.length === 2) {
        filterForm.sendTimeStart = dateStrings[0]
        filterForm.sendTimeEnd = dateStrings[1]
    } else {
        filterForm.sendTimeStart = ''
        filterForm.sendTimeEnd = ''
    }
}

// 分页变更
const onPageChange = (current: number) => {
    pagination.current = current
    loadData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    loadData()
}

// 查看详情
const handleView = async (record: ContractBillVo) => {
    showDrawer.value = true
    await nextTick()
    collectionDetailDrawerRef.value?.show(record)
}

// 下载催缴通知单
const handleDownload = async (record: ContractBillVo) => {
    try {
        const response = await downloadContractBill(record.id!)
        if (response.code === 200 && response.msg) {
            // 创建a标签下载
            const link = document.createElement('a')
            link.href = response.msg
            link.target = '_blank'
            link.rel = 'noopener noreferrer'
            // 尝试设置下载属性
            link.setAttribute('download', `催缴通知单_${record.contractNo || record.id}.pdf`)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            Message.success('下载成功')
        }
    } catch (error) {
        console.error('下载失败:', error)
    }
}

// 抽屉取消回调
const handleDrawerCancel = () => {
    showDrawer.value = false
}

// 通知类型文本
const getCollectionTypeText = (type: number | undefined) => {
    const typeMap: Record<number, string> = {
        1: '催缴函',
        2: '催缴通知单'
    }
    return typeMap[type || 1] || '-'
}

// 缴费状态文本
const getCollectFlagText = (flag: number | undefined) => {
    const flagMap: Record<number, string> = {
        0: '未缴',
        1: '部分缴',
        2: '已缴'
    }
    return flagMap[flag || 0] || '-'
}

// 缴费状态颜色
const getCollectFlagColor = (flag: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'red',
        1: 'orange',
        2: 'green'
    }
    return colorMap[flag || 0] || 'gray'
}

// 格式化金额
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return '-'
    return amount.toFixed(2)
}
</script>

<style scoped lang="less">
.container {
    padding: 0 16px 16px 16px;
}

.general-card {
    border-radius: 4px;

    :deep(.arco-card-body) {
        padding: 16px;
    }
}

.action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
}
</style>
