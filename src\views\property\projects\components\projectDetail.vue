<template>
  <a-drawer v-model:visible="visible" :title="title" class="common-drawer" @cancel="handleCancel">
    <div class="project-detail-container">
      <!-- 左侧菜单 -->
      <a-menu
        :style="{ width: '200px', height: '100%' }"
        mode="vertical"
        :selected-keys="[currentTab]"
        @menu-item-click="handleMenuClick"
      >
        <a-menu-item key="basic">基本信息</a-menu-item>
        <a-menu-item key="building">楼栋信息</a-menu-item>
      </a-menu>

      <!-- 内容区域 -->
      <div class="content-section">
        <!-- 基本信息 -->
        <div v-show="currentTab === 'basic'" class="tab-content">
          <!-- 项目信息 -->
          <div class="basic-info">
            <section-title title="项目信息" />
            <a-form 
              label-align="right"
              :model="formData" 
              layout="horizontal" 
              class="three-column-form basic-form"
            >
              <a-form-item label="项目名称">
                <a-input v-model="formData.mdmName" disabled/>
              </a-form-item>
              <a-form-item label="项目编码">
                <a-input v-model="formData.code" disabled/>
              </a-form-item>
              <a-form-item label="项目资产分类" class="long-label-item">
                <a-input v-model="assetTypeName" disabled/>
              </a-form-item>
              <a-form-item label="项目简称">
                <a-input v-model="formData.name" disabled/>
              </a-form-item>
              <a-form-item label="省市区">
                <a-input v-model="formData.areaText" disabled/>
              </a-form-item>
              <a-form-item label="项目定位" class="long-label-item">
                <a-input v-model="formData.projectAddress" disabled/>
              </a-form-item>
              <a-form-item label="项目地址" class="address-item">
                <a-input v-model="formData.projectAddress" disabled/>
              </a-form-item>
              <a-form-item label="主数据对应项目名称" v-if="formData.type === 1" class="long-label-item">
                <a-input v-model="formData.mdmName" disabled />
              </a-form-item>
            </a-form>
          </div>

          <!-- 地块信息 -->
          <div class="block-info">
            <section-title title="地块信息"/>
            <a-table :columns="blockColumns" :bordered="{ cell: true }" :data="blockData" :pagination="false" :loading="blockLoading">
              <template #landUsage="{ record }">
                {{ record.landUsage === 1 ? '工业用地' : '商业用地' }}
              </template>
            </a-table>
          </div>

          <!-- 面积统计 -->
          <div class="area-statistics">
            <section-title title="面积统计" />
            <div class="statistics-cards">
              <div class="stat-card">
                <div class="label">已接收资产</div>
                <div class="value">{{ statisticsData.receivedArea }}<span class="unit">万㎡</span></div>
              </div>
              <div class="stat-card">
                <div class="label">房源总面积</div>
                <div class="value">{{ statisticsData.totalRoomArea }}<span class="unit">万㎡</span></div>
              </div>
              <div class="stat-card">
                <div class="label">房源数</div>
                <div class="value">{{ statisticsData.roomCount || 0 }}<span class="unit">个</span></div>
              </div>
              <div class="stat-card">
                <div class="label">未交付房源数</div>
                <div class="value">{{ statisticsData.pendingDeliveryCount || 0 }}<span class="unit">个</span></div>
              </div>
            </div>
          </div>

          <!-- 项目归属 -->
          <div class="project-affiliation">
            <section-title title="项目归属" />
            <a-form 
              label-align="right"
              :model="formData" 
              layout="horizontal" 
              class="three-column-form basic-form"
              ref="editFormRef"
            >
              <a-form-item label="签约商业公司" field="merchantIds" :rules="[{ required: true, message: '请选择签约商业公司' }]" class="business-item" required>
                <a-select v-model="formData.merchantIds" multiple placeholder="请选择签约商业公司">
                    <a-option v-for="item in merchantOptions" :value="item.id" :key="item.id">{{ item.orgCompanyName }}</a-option>
                </a-select>
              </a-form-item>
              <a-form-item label="所在片区" field="parentId" :rules="[{ required: true, message: '请选择所在片区' }]" required>
                <a-select v-model="formData.parentId" placeholder="请选择所在片区">
                  <a-option v-for="item in areaOptions" :value="item.id" :key="item.id">{{ item.name }}</a-option>
                </a-select>
              </a-form-item>
            </a-form>
          </div>
        </div>

        <!-- 楼栋信息 -->
        <div v-show="currentTab === 'building'" class="tab-content">
          <div class="building-info">
            <section-title title="楼栋信息" class="first-child" />
            <a-table 
              :columns="buildingColumns" 
              :bordered="{ cell: true }" 
              :scroll="{x: 1200}" 
              :data="buildingData" 
              :pagination="buildingPagination"
              :loading="buildingLoading"
              @page-change="onBuildingPageChange"
              @page-size-change="onBuildingPageSizeChange"
            >
              <template #buildingName="{ record }">
                {{ record.buildingName }}
              </template>
              <template #operation="{ record }">
                <a-button type="text" size="mini" @click="handleEditBuildingName(record)">
                  修改楼栋名称
                </a-button>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 修改楼栋名称弹窗 -->
        <a-modal
          v-model:visible="editBuildingNameVisible"
          title="修改楼栋名称"
          @ok="handleEditBuildingNameConfirm"
          @cancel="editBuildingNameVisible = false"
          :confirm-loading="editBuildingNameLoading"
        >
          <a-form label-align="right" :model="editBuildingForm">
            <a-form-item label="楼栋名称" required>
              <a-input v-model="editBuildingForm.buildingName" placeholder="请输入楼栋名称" :max-length="30" />
            </a-form-item>
          </a-form>
        </a-modal>

      </div>
    </div>

    <template #footer>
      <a-space v-show="currentTab === 'basic'">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSave" :loading="saveLoading">保存</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { 
  getProjectDetail, 
  editProject, 
  updateBuildingName, 
  getBuildingList,
  getProjectStatistics,
  type SysProjectVo,
  type SysBuildingVo,
  type SysProjectEditDTO,
  type SysBuildingUpdateNameDTO,
  type SysProjectStatisticsVo,
  type MerchantInfo
} from '@/api/project'
import { getMerchantList, type MerchantAddDTO } from '@/api/company'
import { getOrgTree } from '@/api/org'
import { getDicts } from '@/api/system/dict'

interface Props {
  mode?: 'edit' | 'view'
  projectId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'edit',
  projectId: ''
})

const emit = defineEmits<{
  refresh: []
}>()

const visible = ref(false)
const currentTab = ref('basic')
const title = computed(() => props.mode === 'edit' ? '完善信息' : '项目详情')

// 计算属性：项目资产分类名称
const assetTypeName = computed(() => getAssetTypeName(formData.assetType))
const editFormRef = ref()

// 加载状态
const loading = ref(false)
const saveLoading = ref(false)
const blockLoading = ref(false)
const buildingLoading = ref(false)
const editBuildingNameLoading = ref(false)

// 表单数据
const formData = reactive<Partial<SysProjectVo & { merchantIds: string[], areaText: string, projectLocation: string }>>({
  id: '',
  name: '',
  code: '',
  mdmSaleName: '',
  assetType: undefined,
  projectAddress: '',
  projectLocation: '', // 项目定位（不可编辑）
  mdmName: '',
  provinceCode: '',
  provinceName: '',
  cityCode: '',
  cityName: '',
  countryCode: '',
  countryName: '',
  longitude: '',
  latitude: '',
  areaText: '',
  merchantIds: [], // 签约商业公司（可编辑）
  parentId: '', // 所属片区（可编辑）
  type: undefined
})

// 统计数据
const statisticsData = reactive<SysProjectStatisticsVo>({
  receivedArea: 0,
  totalRoomArea: 0,
  roomCount: 0,
  pendingDeliveryCount: 0
})

// 选项数据
const merchantOptions = ref<MerchantAddDTO[]>([])
const areaOptions = ref<Array<{ id: string, name: string }>>([])
const assetCategoryOptions = ref<Array<{ dictValue: string, dictLabel: string }>>([])

// 地块表格列定义
const blockColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '地块名称', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '用地性质', dataIndex: 'landUsage', slotName: 'landUsage', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '合同签约地址', dataIndex: 'address', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '所属分期', dataIndex: 'stageName', width: 120, ellipsis: true, tooltip: true, align: 'center' }
]

// 地块数据
const blockData = ref<any[]>([])

// 楼栋表格列定义
const buildingColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '楼栋名称', dataIndex: 'buildingName', slotName: 'buildingName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '地上楼层数', dataIndex: 'upFloorNums', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '地下楼层数', dataIndex: 'underFloorNums', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '资产面积(㎡)', dataIndex: 'assetArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '房源总面积(㎡)', dataIndex: 'roomArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '房源数', dataIndex: 'roomCount', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '产证楼栋名称', dataIndex: 'certificateBuildingName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '主数据楼栋名称', dataIndex: 'mdmBuildingName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '操作', slotName: 'operation', width: 140, ellipsis: false, tooltip: false, align: 'center', fixed: 'right' }
]

// 楼栋数据和分页
const buildingData = ref<SysBuildingVo[]>([])
const buildingPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
})

// 修改楼栋名称相关
const editBuildingNameVisible = ref(false)
const editBuildingForm = reactive({
  id: '',
  buildingName: ''
})

// 加载商业公司列表
const loadMerchantList = async () => {
  try {
    const response = await getMerchantList({
      pageNum: 1,
      pageSize: 10000 // 获取所有商业公司
    })
    
    if (response && response.rows) {
      merchantOptions.value = response.rows
    }
  } catch (error) {
    console.error('加载商业公司列表失败:', error)
    merchantOptions.value = []
  }
}

// 加载片区选项
const loadAreaOptions = async () => {
  try {
    const response = await getOrgTree()
    if (response && response.data) {
      const areas: Array<{ id: string, name: string }> = []
      
      // 递归遍历组织树，找到level=3的节点（片区）
      const findAreas = (nodes: any[]) => {
        nodes.forEach(node => {
          if (node.level === 3 && node.id && node.name) {
            areas.push({
              id: node.id,
              name: node.name
            })
          }
          if (node.children && node.children.length > 0) {
            findAreas(node.children)
          }
        })
      }
      
      findAreas(response.data)
      areaOptions.value = areas
    }
  } catch (error) {
    console.error('获取片区选项失败:', error)
    areaOptions.value = []
  }
}

// 加载项目资产分类数据字典
const loadAssetCategoryOptions = async () => {
  try {
    const response = await getDicts('asset_category')
    if (response && response.data) {
      assetCategoryOptions.value = response.data
    }
  } catch (error) {
    console.error('加载项目资产分类失败:', error)
    assetCategoryOptions.value = []
  }
}

// 加载项目详情
const loadProjectDetail = async () => {
  if (!props.projectId) return
  
  try {
    loading.value = true
    const response = await getProjectDetail(String(props.projectId))
    
    if (response.data) {
      const project = response.data
      Object.assign(formData, {
        ...project,
        areaText: `${project.provinceName || ''}/${project.cityName || ''}/${project.countryName || ''}`,
        projectLocation: project.projectAddress || '', // 项目定位使用原始的项目地址
        merchantIds: project.merchantList ? project.merchantList.map((item: MerchantInfo) => item.id) : [], // 从merchantList提取id数组
        parentId: project.parentId || '' // 反显所属片区
      })
      
      // 处理地块数据
      if (project.parcelList) {
        blockData.value = project.parcelList.map((item, index) => ({
          ...item,
          index: index + 1,
          stageName: '总体规划一期' // 需要根据实际数据调整
        }))
      }
      
      // 加载统计数据
      Object.assign(statisticsData, {
        receivedArea: project.receivedArea || 0,
        totalRoomArea: project.totalRoomArea || 0,
        roomCount: project.roomCount || 0,
        pendingDeliveryCount: project.pendingDeliveryCount || 0
      })
    }
  } catch (error) {
    console.error('加载项目详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载楼栋列表
const loadBuildingList = async () => {
  if (!props.projectId) return
  
  try {
    buildingLoading.value = true
    const response = await getBuildingList({
      projectId: String(props.projectId),
      pageNum: buildingPagination.current,
      pageSize: buildingPagination.pageSize
    })
    
    // 处理不同的响应数据结构
    let buildingList: SysBuildingVo[] = []
    let total = 0
    
    if (response.rows && Array.isArray(response.rows)) {
      // 处理直接返回rows的情况
      buildingList = response.rows
      total = response.total || 0
    }
    
    buildingData.value = buildingList.map((item, index) => ({
      ...item,
      index: (buildingPagination.current - 1) * buildingPagination.pageSize + index + 1
    }))
    
    buildingPagination.total = total
  } catch (error) {
    console.error('加载楼栋列表失败:', error)
  } finally {
    buildingLoading.value = false
  }
}

// 楼栋分页处理
const onBuildingPageChange = (current: number) => {
  buildingPagination.current = current
  loadBuildingList()
}

const onBuildingPageSizeChange = (pageSize: number) => {
  buildingPagination.pageSize = pageSize
  buildingPagination.current = 1
  loadBuildingList()
}

// 获取项目资产分类显示名称
const getAssetTypeName = (assetType?: number) => {
  if (!assetType) return ''
  
  // 先尝试从数据字典中查找
  const option = assetCategoryOptions.value.find(item => Number(item.dictValue) === assetType)
  if (option) {
    return option.dictLabel
  }
  
  // 如果数据字典中没有，使用默认映射
  const defaultMapping: Record<number, string> = {
    1: '众创城',
    2: '科技城', 
    3: '产城',
    4: '外部项目'
  }
  
  return defaultMapping[assetType] || `未知类型(${assetType})`
}

// 方法
const handleMenuClick = (key: string) => {
  currentTab.value = key
  if (key === 'building') {
    loadBuildingList()
  }
}

const handleCancel = () => {
  visible.value = false
}

const handleSave = async () => {
  try {
    const errors = await editFormRef.value.validate() 
    if (errors) return
    saveLoading.value = true
    
    // 只保存可编辑的字段：签约商业公司和所属片区
    const editData: SysProjectEditDTO = {
      id: formData.id,
      merchantIds: formData.merchantIds,
      parentId: formData.parentId
    }
    
    await editProject(editData)
    Message.success('保存成功')
    visible.value = false
    
    // 触发父组件刷新项目列表
    emit('refresh')
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saveLoading.value = false
  }
}

const handleEditBuildingName = (record: SysBuildingVo) => {
  editBuildingForm.id = record.id || ''
  editBuildingForm.buildingName = record.buildingName || ''
  editBuildingNameVisible.value = true
}

const handleEditBuildingNameConfirm = async () => {
  if (!editBuildingForm.buildingName.trim()) {
    Message.error('楼栋名称不能为空')
    return
  }
  
  try {
    editBuildingNameLoading.value = true
    
    const updateData: SysBuildingUpdateNameDTO = {
      id: editBuildingForm.id,
      buildingName: editBuildingForm.buildingName
    }
    
    await updateBuildingName(updateData)
    
    editBuildingNameVisible.value = false
    Message.success('修改成功')
    
    // 刷新楼栋列表
    await loadBuildingList()
  } catch (error) {
    console.error('修改楼栋名称失败:', error)
  } finally {
    editBuildingNameLoading.value = false
  }
}

// 监听projectId变化，重新加载数据
watch(() => props.projectId, (newId) => {
  if (newId && visible.value) {
    loadProjectDetail()
  }
}, { immediate: true })

// 初始化数据
onMounted(() => {
  loadMerchantList()
  loadAreaOptions()
  loadAssetCategoryOptions()
})

// 暴露方法给父组件
defineExpose({
  show: () => {
    visible.value = true
    if (props.projectId) {
      loadProjectDetail()
    }
  }
})
</script>

<style scoped lang="less">
.project-detail-container {
  display: flex;
  height: 100%;

  .content-section {
    flex: 1;
    padding: 0 16px;
    overflow-y: auto;
  }

  .section-title {
    margin: 16px 0;
    &.first-child{
      margin-top: 8px;
    }
  }

  :deep(.arco-form) {
    &.basic-form {
      .arco-form-item {
        display: flex !important;
        
        .arco-form-item-label-col {
          flex: 0 0 112px !important;
          max-width: 112px !important;
          box-sizing: content-box;

          .arco-form-item-label {
            width: 100px !important;
            text-align: right;
            padding-right: 12px;
          }
        }

        .arco-form-item-wrapper-col {
          flex: 1;
          max-width: 100%;
        }
      }

      // 长标签项特殊处理
      .long-label-item {
        .arco-form-item-label-col {
          flex: 0 0 140px !important;
          max-width: 140px !important;

          .arco-form-item-label {
            width: 128px !important;
          }
        }
      }
    }

    &.three-column-form {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;

      .address-item {
        grid-column: span 2;
      }
      .business-item{
        grid-column: span 2;
      }
    }

    .arco-form-item {
      margin-bottom: 16px;

      .arco-form-item-label {
        white-space: nowrap;
      }
    }
  }

  .statistics-cards {
    display: flex;
    gap: 16px;
    margin-top: 16px;

    .stat-card {
      flex: 1;
      padding: 16px;
      background: linear-gradient(180deg, #47d8b1, #66dac4 97%);
      border-radius: 4px;
      color: #fff;

      &:nth-child(2) {
        background: linear-gradient(180deg, #349bff, #7abdff 95%);
      }

      &:nth-child(3) {
        background: linear-gradient(180deg, #6d89ff, #90a2ff 97%);
      }

      &:nth-child(4) {
        background: linear-gradient(180deg, #ff9a61, #ef9a70 97%);
      }

      .label {
        font-size: 14px;
        margin-bottom: 8px;
      }

      .value {
        font-size: 22px;
        font-weight: 600;

        .unit {
          font-size: 14px;
          font-weight: normal;
          margin-left: 4px;
        }
      }
    }
  }
}
</style> 