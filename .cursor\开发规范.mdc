---
description: 
globs: 
alwaysApply: true
---
# 公共规则
    - 使用 Vue 3 的 Composition API 语法
    - 使用 arco design Vue 组件库
    - 使用 TypeScript 类型系统
    - 遵循 Vue 的最佳实践
    - js 一行 结束不需要添加结束分号
    - 文件和组件的命名都使用‘小驼峰’命名
    - 缩进为 4 个空格
    - 如果在try{}catch{}中调用接口，不需要在catch中提示报错信息
    - 简单的功能不需要跑 npm run build 或者 npm run dev 等检测
    - 接口调用的回调，不需要加 Message.error 提示，有统一处理
# 文件夹规则
    - 公共组件都写在 src/components
    - api 接口都在 src/api
    - 页面都在 src/views
# css规则
    - padding 使用 16px
    - css 使用 less 语法
# 使用 arco design Vue 组件库要遵循的规范
    - a-form使用靠右 label-align="right"
    - 使用 a-steps 组件时，使用 :current 数字从 1 开始
    - 使用 a-table 组件时，columns使用 ellipsis: true, tooltip: true, align: 'center'
    - 使用 a-table 组件时，根据字段中文名称推断合适的宽度，最低保证表头不换行
    - 使用 a-table 组件时，如果设置了固定列，需要在a-table上配置属性`:scroll="{x: 1}"`
    - 使用 a-form 组件时，使用label-col-props 和 wrapper-col-props 代替 label-col 和 wrapper-col
# a-form 表单验证规则
    - const errors = await voidFormRef.value.validate() 
    - if (errors) return
# api 规范
 - get接口示例
    // 查询打印模板列表
    export function getTemplateList(params: TemplateQueryParams) {
        return http.get<ApiResponse<PaginationData<TemplateData>>>('/rent-admin/template/list', params)
    }
 - post接口示例
    /**
    * 查询财务退款列表（新接口）
    * @param params 查询参数
    * @returns Promise<FinancialRefundVo[]>
    */
    export function getFinancialRefundList(params: FinancialRefundQueryDTO) {
    return http.post<FinancialRefundVo[]>('/rent-admin/financialRefund/list', params)
    }

 # 测试
    - 每次不需要跑 npm run dev 默认是在启动的
    - 不需要生成测试页面
    - 功能实现说明都放在文件夹 .cursor/功能实现说明