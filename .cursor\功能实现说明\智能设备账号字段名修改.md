# 智能设备账号字段名修改

## 需求描述
在 `src/views/smartDevices/components/BatchBindDrawer.vue` 页面中，`getTTLockAccountList` 接口调用时需要将 `username` 字段换成 `lockUsername`。

## 修改内容

### 修改位置
文件：`src/views/smartDevices/components/BatchBindDrawer.vue`
行数：第331行

### 修改前
```typescript
const response = await getTTLockAccountList(params)
if (response && response.rows) {
    brandList.value = response.rows.map((item: any) => ({
        id: item.id,
        label: item.brandType === 1 ? '通通锁' : '未知品牌',
        username: item.username  // 修改前
    }))
}
```

### 修改后
```typescript
const response = await getTTLockAccountList(params)
if (response && response.rows) {
    brandList.value = response.rows.map((item: any) => ({
        id: item.id,
        label: item.brandType === 1 ? '通通锁' : '未知品牌',
        username: item.lockUsername  // 修改后
    }))
}
```

## 技术说明

### 字段映射逻辑
- **API返回字段**：`item.lockUsername` - 接口返回的用户名字段
- **本地数据结构**：`username` - 组件内部使用的字段名
- **映射关系**：将API的 `lockUsername` 字段映射到本地的 `username` 字段

### 不需要修改的部分
1. **模板显示**（第23行）：
   ```vue
   <span class="brand-label">{{ brand.label }}({{ brand.username }})</span>
   ```
   - 这里使用的是本地数据结构中的 `username` 字段，无需修改

2. **类型定义**（第209行）：
   ```typescript
   const brandList = ref<Array<{id: string, label: string, username: string}>>([])
   ```
   - 本地数据结构保持不变，仍使用 `username` 字段名

## 影响范围
- ✅ 仅影响 `fetchBrandList` 方法中的数据映射逻辑
- ✅ 不影响组件的显示和其他功能
- ✅ 保持与现有代码的兼容性

## 验证要点
1. 品牌列表能正常加载显示
2. 品牌卡片中的用户名正确显示
3. 删除账号功能正常工作
4. 不影响其他相关功能

此修改确保了组件能够正确读取API返回的用户名字段，保证品牌账号信息的正确显示。
