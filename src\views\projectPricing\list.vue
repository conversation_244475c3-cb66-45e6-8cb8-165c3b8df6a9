<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form label-align="right" :model="filterForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
                        auto-label-width>
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="project" label="项目">
                                    <ProjectTreeSelect 
                                        v-model="filterForm.projectId" 
                                        :min-level="4"
                                        @change="handleProjectChange"
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="pricingType" label="定价类型">
                                <a-select v-model="filterForm.pricingType" placeholder="请选择类型" allow-clear>
                                    <a-option :value="1">首次定价</a-option>
                                    <a-option :value="2">过程调价</a-option>
                                </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="buildingName" label="楼栋名称">
                                    <a-input v-model="filterForm.buildingName" placeholder="请输入楼栋名称" allow-clear />
                                </a-form-item>
                            </a-col>
   
                        </a-row>
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="status" label="审批状态">
                                <a-select v-model="filterForm.status" placeholder="请选择状态" allow-clear>
                                    <a-option :value="0">草稿</a-option>
                                    <a-option :value="1">审批中</a-option>
                                    <a-option :value="2">已通过</a-option>
                                    <a-option :value="3">已驳回</a-option>
                                </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin-top: 0" />

            <!-- 操作按钮 -->
            <div class="action-bar">
                <a-space>
                    <a-button type="primary" @click="handleAdd">
                        <template #icon><icon-plus /></template>
                        新增立项定价申请
                    </a-button>
                </a-space>
            </div>
            <!-- 表格区域 -->
            <a-table
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :bordered="{ cell: true }"
            :scroll="{ x: 1 }"
            :stripe="true"
            :loading="loading"
            row-key="id"
            @page-change="onPageChange"
            @page-size-change="onPageSizeChange"
            >

                <template #operations="{ record }">
                    <template v-if="record.status === 0">
                        <a-space>
                            <a-button type="text" size="mini" @click="handleEdit(record)">
                                编辑
                            </a-button>
                            <a-button type="text" size="mini" @click="handleDelete(record)">
                                删除
                            </a-button>
                            <a-button type="text" size="mini" @click="handleSubmit(record)">
                                提交
                            </a-button>
                            <a-button type="text" size="mini" @click="handleView(record)">
                                详情
                            </a-button>
                        </a-space>
                    </template>
                    <template v-else>
                        <a-space>
                            <a-button type="text" size="mini" @click="handleView(record)">
                                详情
                            </a-button>
                        </a-space>
                    </template>
                </template>
            </a-table>
        </a-card>
        <select-building-modal v-if="showSelectBuildingModal" ref="selectBuildingModalRef" @next="handleSelectBuildingNext" @cancel="handleSelectBuildingCancel" />
        <select-rooms-modal v-if="showSelectRoomsModal" ref="selectRoomsModalRef" @prev="handleSelectRoomsPrev" @next="handleSelectRoomsNext" @cancel="handleSelectRoomsCancel" />
        <project-pricing-drawer v-if="showProjectPricingDrawer" ref="projectPricingDrawerRef" @success="handleProjectPricingSuccess" @cancel="handleProjectPricingCancel" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { IconSearch, IconRefresh, IconPlus } from '@arco-design/web-vue/es/icon'
import { Message, Modal } from '@arco-design/web-vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import SelectBuildingModal from './components/SelectBuildingModal.vue'
import SelectRoomsModal from './components/SelectRoomsModal.vue'
import ProjectPricingDrawer from './components/ProjectPricingDrawer.vue'
import { 
    getPricingList, 
    deletePricing, 
    submitPricing,
    type PricingQueryDTO, 
    type PricingVo,
    type PricingListResponse
} from '@/api/projectPricing'

// 筛选表单数据
const filterForm = reactive<PricingQueryDTO>({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    buildingName: '',
    pricingType: undefined,
    status: undefined
})

const showSelectBuildingModal = ref(false)
const showSelectRoomsModal = ref(false)
const showProjectPricingDrawer = ref(false)
const selectBuildingModalRef = ref()
const selectRoomsModalRef = ref()
const projectPricingDrawerRef = ref()

// 表格数据
const tableData = ref<PricingVo[]>([])

const loading = ref(false)
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
})


// 表格列配置
const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        align: 'center',
        width: 80,
        render: ({ rowIndex }: any) => {
            return (pagination.current - 1) * pagination.pageSize + rowIndex + 1
        }
    },
    {
        title: '项目',
        dataIndex: 'projectName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 150
    },
    {
        title: '定价类型',
        dataIndex: 'pricingType',
        align: 'center',
        width: 100,
        render: ({ record }: any) => {
            const typeMap: Record<number, string> = {
                1: '首次定价',
                2: '过程调价'
            }
            return typeMap[record.pricingType] || '-'
        }
    },
    {
        title: '定价楼栋',
        dataIndex: 'buildingName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 150
    },
    {
        title: '定价房源数',
        dataIndex: 'roomCount',
        align: 'center',
        width: 120
    },
    {
        title: '审批状态',
        dataIndex: 'status',
        align: 'center',
        width: 100,
        render: ({ record }: any) => {
            const statusMap: Record<number, string> = {
                0: '草稿',
                1: '审批中',
                2: '已通过',
                3: '已驳回'
            }
            return statusMap[record.status] || '-'
        }
    },
    {
        title: '创建人',
        dataIndex: 'createByName',
        align: 'center',
        width: 100
    },
    {
        title: '创建日期',
        dataIndex: 'createTime',
        align: 'center',
        width: 100
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 200,
        ellipsis: false,
        tooltip: false,
        fixed: 'right',
        align: 'center'
    }
]

// 抽屉相关
const pricingDrawerRef = ref(projectPricingDrawerRef)

// 方法
const search = async () => {
    try {
        loading.value = true
        const params: PricingQueryDTO = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            projectId: filterForm.projectId,
            buildingName: filterForm.buildingName || undefined,
            pricingType: filterForm.pricingType,
            status: filterForm.status
        }
        const response = await getPricingList(params)
        
        if (response && response.rows) {
            tableData.value = response.rows || []
            pagination.total = response.total || 0
        }
    } catch (error) {
        console.error('获取立项定价列表失败:', error)
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 分页变化
const onPageChange = (current: number) => {
    pagination.current = current
    search()
}

// 每页条数变化
const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    search()
}

const reset = () => {
    Object.assign(filterForm, {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        buildingName: '',
        pricingType: undefined,
        status: undefined
    })
    pagination.current = 1
    search()
}

const handleAdd = () => {
    // 立项定价：选择楼栋 -> 选择房源 -> 立项定价申请单页面
    showSelectBuildingModal.value = true
    nextTick(() => {
        selectBuildingModalRef.value?.show({ projectId: currentSelectedOrg.value.projectId })
    })
}

// 选择楼栋相关处理函数
const handleSelectBuildingNext = (buildingData: any) => {
    showSelectRoomsModal.value = true
    nextTick(() => {
        selectRoomsModalRef.value?.show(buildingData)
    })
}

const handleSelectBuildingCancel = () => {
    // 取消选择楼栋
    showSelectBuildingModal.value = false
    console.log('取消选择楼栋')
}

// 选择房源相关处理函数
const handleSelectRoomsPrev = (prevData: any) => {
    console.log('从房源选择返回到楼栋选择，传递数据:', prevData)
    
    // 确保数据存在且格式正确
    if (prevData && typeof prevData === 'object') {
        // 传递上一步的数据，包括已选择的楼栋等信息
        showSelectBuildingModal.value = true
        nextTick(() => {
            selectBuildingModalRef.value?.show(prevData)
        })
    } else {
        console.warn('未从房源选择页面获取到有效数据，使用空对象初始化楼栋选择页面')
        showSelectBuildingModal.value = true
        nextTick(() => {
            selectBuildingModalRef.value?.show({ projectId: currentSelectedOrg.value.projectId })
        })
    }
}

const handleSelectRoomsNext = (roomsData: any) => {
    showProjectPricingDrawer.value = true
    const projectPricingData = {
        selectedRooms: roomsData.selectedRooms,
        selectedRoomIds: roomsData.selectedRoomIds,
        projectId: currentSelectedOrg.value.projectId,
        projectName: currentSelectedOrg.value.projectName,
        buildingIds: roomsData.buildingIds
    }
    nextTick(() => {
        projectPricingDrawerRef.value?.show('add', projectPricingData)
    })
}

const handleSelectRoomsCancel = () => {
    // 取消选择房源
    showSelectRoomsModal.value = false
    console.log('取消选择房源')
}

// 立项定价申请相关处理函数
const handleProjectPricingSuccess = () => {
    search()
}

const handleProjectPricingCancel = () => {
    // 取消立项定价申请
    showProjectPricingDrawer.value = false
    console.log('取消立项定价申请')
}




// 删除立项定价申请
const handleDelete = (record: PricingVo) => {
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除当前单据？删除后不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
            try {
                if (record.id) {
                    await deletePricing(record.id)
                    Message.success('删除成功')
                    search()
                }
            } catch (error) {
                console.error('删除失败:', error)
            }
        }
    })
}

// 提交立项定价申请
const handleSubmit = (record: PricingVo) => {
    Modal.confirm({
        title: '确认提交',
        content: `确定要提交当前单据？提交后将进入审批流程。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
            try {
                if (record.id) {
                    await submitPricing(record.id)
                    Message.success('提交成功')
                    search()
                }
            } catch (error) {
                console.error('提交失败:', error)
            }
        }
    })
}

const handleView = (record: PricingVo) => {
    showProjectPricingDrawer.value = true
    nextTick(() => {
        projectPricingDrawerRef.value?.show('view', record)
    })
}

const handleEdit = (record: PricingVo) => {
    showProjectPricingDrawer.value = true
    nextTick(() => {
        projectPricingDrawerRef.value?.show('edit', record)
    })
}


const currentSelectedOrg = ref({
    projectId: undefined,
    projectName: ''
})
const isInit = ref(false)
const handleProjectChange = (value: string | number, selectedOrg: any) => {
    console.log('项目变化时只更新表单数据，不自动触发搜索', value, selectedOrg)
    // 项目变化时只更新表单数据，不自动触发搜索
    // 用户需要点击查询按钮才执行查询
    currentSelectedOrg.value.projectId = selectedOrg.id
    currentSelectedOrg.value.projectName = selectedOrg.name
    // 项目选择后自动触发搜索
    if (!isInit.value) {
        isInit.value = true
        pagination.current = 1
        search()
    }
}

</script>

<style scoped lang="less">
:deep(.section-title) {
    margin: 16px 0;
}

.container {
    padding: 0 16px 16px 16px;
}

.general-card {
    border-radius: 4px;

    :deep(.arco-card-body) {
        padding: 16px;
    }
}

.action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
}
</style>