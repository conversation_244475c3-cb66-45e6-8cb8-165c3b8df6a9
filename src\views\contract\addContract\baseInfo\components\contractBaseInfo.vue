<template>
    <section>
        <sectionTitle title="合同基本信息">
            <template #right>
                <template v-if="isDetailMode">
                    <div class="detail-text">{{ contractData.contractMode === 0 ? '标准合同' : '非标合同' }}</div>
                </template>
                <template v-else>
                    <a-select :disabled="disabledFields.includes('contractMode')" v-model="contractData.contractMode"
                        style="width: 200px;">
                        <a-option :value="0">标准合同</a-option>
                        <a-option v-if="contractStore.contractPermission.canSelectNonStandard"
                            :value="1">非标合同</a-option>
                    </a-select>
                </template>
            </template>
        </sectionTitle>
        <div class="content">
            <a-form :disabled="isDetailMode" :model="contractData" :rules="isDetailMode ? {} : rules" ref="formRef" auto-label-width
                layout="vertical">
                <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                    <a-grid-item>
                        <a-form-item field="contractNo" label="合同号">
                            <!-- :disabled="disabledFields.includes('contractNo')" -->
                            <a-input :disabled="contractData.status != 10" v-model="contractData.contractNo"
                                placeholder="系统自动生成" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item field="paperContractNo" label="纸质合同编号">
                            <a-input v-model="contractData.paperContractNo" placeholder="默认等于合同号" disabled />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item field="signDate" label="签订日期">
                            <a-date-picker :disabled="disabledFields.includes('signDate')"
                                v-model="contractData.signDate" style="width: 100%" placeholder="请选择日期"
                                format="YYYY-MM-DD" :disabled-date="disabledDate" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item field="handoverDate" label="交房日期">
                            <a-date-picker :disabled="disabledFields.includes('handoverDate')"
                                v-model="contractData.handoverDate" style="width: 100%" placeholder="请选择日期"
                                format="YYYY-MM-DD" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item field="contractPurpose"
                            :label="contractData.contractType === 2 ? '多经合同用途' : '合同用途'">
                            <a-input disabled v-model="contractPurposeLabel" placeholder=""> </a-input>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item field="dealChannel" label="成交渠道">
                            <a-select :disabled="disabledFields.includes('dealChannel')"
                                v-model="contractData.dealChannel" placeholder="请选择" @change="handleDealChannelChange">
                                <a-option :value="0">中介推荐</a-option>
                                <a-option :value="1">自拓客户</a-option>
                                <a-option :value="2">招商代理</a-option>
                                <a-option :value="3">全员招商</a-option>
                            </a-select>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item v-show="contractData.dealChannel === 3">
                        <a-form-item field="assistantName" label="协助人">
                            <a-select :disabled="disabledFields.includes('assistantName')"
                                v-model="contractData.assistantName" placeholder="请输入姓名/手机号搜索" allow-search
                                :filter-option="false" :loading="assistantLoading" @search="handleAssistantSearch"
                                @change="handleAssistantChange">
                                <a-option v-for="item in assistantOptions" :key="item.id" :value="item.name">
                                    {{ item.name }}（{{ item.tel }}）
                                </a-option>
                            </a-select>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item field="startDate" label="开始日期">
                            <a-date-picker :disabled="disabledFields.includes('startDate')"
                                v-model="contractData.startDate" style="width: 100%" placeholder="请选择日期"
                                @change="handleStartDateChange" format="YYYY-MM-DD" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item field="rentDate" label="租赁日期">
                            <a-space>
                                <a-input-number :disabled="disabledFields.includes('rentYear')"
                                    v-model="contractData.rentYear" placeholder="年数" :min="0"
                                    @change="calculateEndDate">
                                    <template #suffix>年</template>
                                </a-input-number>
                                <a-input-number :disabled="disabledFields.includes('rentMonth')"
                                    v-model="contractData.rentMonth" placeholder="月数" :min="0"
                                    @change="calculateEndDate">
                                    <template #suffix>月</template>
                                </a-input-number>
                                <a-input-number :disabled="disabledFields.includes('rentDay')"
                                    v-model="contractData.rentDay" placeholder="天数" :min="0" :max="31"
                                    @change="calculateEndDate">
                                    <template #suffix>天</template>
                                </a-input-number>
                            </a-space>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item field="endDate" label="结束日期">
                            <a-date-picker :disabled="disabledFields.includes('endDate')" v-model="contractData.endDate"
                                style="width: 100%" placeholder="请选择日期" @change="calculateLeaseDuration"
                                format="YYYY-MM-DD" />
                        </a-form-item>
                    </a-grid-item>
                </a-grid>
            </a-form>
        </div>
    </section>
</template>

<script lang="ts" setup>
/**
 * @description: 合同基本信息
 * 字段说明：全部必填
 * 1，合同号：系统自动生成
 * 2，纸质合同编号：默认等于合同号 不可改
 * 3，签订日期：默认当天，可以改 YYYY-MM-DD
 * 4，交房日期：默认当天，可以改 YYYY-MM-DD
 * 5，合同用途：下拉选择 默认房源的立项定价的【物业类型】，不许改
 * 6，成交渠道：下拉选择 中介推荐/自拓客户/招商代理/全员招商
 * 7，协助人：下拉选择 客户姓名 当成交渠道=全员招商，展示&必填，其他不展示，不填 下拉输入姓名/手机号，模糊匹配OA人员，下拉框展示人员姓名（手机号）
 * 8，开始日期：YYYY-MM-DD
 * 9，租赁日期：填写开始日期，结束日期，自动计算几月几天，填写几月几天，自动计算开始日期或结束日期
 * 10，结束日期：YYYY-MM-DD
 */
import { ref, onMounted, computed } from 'vue'
import type { FormInstance } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import dayjs from 'dayjs'
import { useContractStore } from '@/store/modules/contract/index'
import { getDictLabel } from '@/dict'
import { getEhrUserList } from '@/api/ehr'
import { Message } from '@arco-design/web-vue';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

const contractPurposeLabel = computed(() => {
    return getDictLabel('diversification_purpose', Number(contractData.value.contractPurpose))
})

/**
 * @description 编辑类型
 * 1. 续签
 *    - 合同类型、合同号、纸质合同编号、合同用途、开始日期不可修改
 *    - 开始日期 = 上一份合同的结束日期 + 1、租赁期限/结束日期清空，填写
 * 2. 详情
 *    - 所有字段不可修改，且展示成文本
 * 3. 变更
 *    - 变更类型包含条款变更时，交房日期、成交渠道、协助人可以修改，其他字段都不可修改
 *    - 变更类型不包含条款变更，所有字段都不可修改
 */
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const disabledFields = computed(() => {
    if (editType.value === 'renew') {
        return ['contractMode', 'contractNo', 'paperContractNo', 'contractPurpose', 'startDate']
    } else if (editType.value === 'change') {
        // 变更类型包含条款变更时，交房日期、成交渠道、协助人可以修改，其他字段都不可修改
        if (changeType.value.includes(2)) {
            // 条款变更：只有交房日期、成交渠道、协助人可以修改
            return ['contractMode', 'contractNo', 'paperContractNo', 'signDate', 'contractPurpose', 'startDate', 'rentYear', 'rentMonth', 'rentDay', 'endDate']
        } else {
            // 变更类型不包含条款变更，所有字段都不可修改
            return ['contractMode', 'contractNo', 'paperContractNo', 'signDate', 'handoverDate', 'contractPurpose', 'dealChannel', 'assistantName', 'startDate', 'rentYear', 'rentMonth', 'rentDay', 'endDate']
        }
    }
    return []
})
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo')
const formItemMargin = computed(() => isDetailMode.value ? '16px' : '16px')

// 协助人搜索相关
const assistantLoading = ref(false)
const assistantOptions = ref<Array<{ id: string; name: string; tel: string }>>([])

// 设置签订日期和交房日期不可选
const disabledDate = (date: Date) => {
    return !contractStore.contractPermission.notRestrictedBySignTime ? dayjs(date).isBefore(dayjs().subtract(contractStore.contractPermission.solidificationDays, 'day')) : false
}

// 统一表单校验规则
const rules = computed(() => {
    return {
        signDate: [{ required: !disabledFields.value.includes('signDate'), message: '请选择签订日期' }],
        handoverDate: [{ required: !disabledFields.value.includes('handoverDate'), message: '请选择交房日期' }],
        contractPurpose: [{ required: !disabledFields.value.includes('contractPurpose'), message: '请选择合同用途' }],
        dealChannel: [{ required: !disabledFields.value.includes('dealChannel'), message: '请选择成交渠道' }],
        assistantName: [{ required: !disabledFields.value.includes('assistantName') && contractData.value.dealChannel === 3, message: '请选择协助人' }],
        startDate: [{ required: !disabledFields.value.includes('startDate'), message: '请选择开始日期' }],
        endDate: [
            { required: !disabledFields.value.includes('endDate'), message: '请选择结束日期' },
            {
                validator: (value: string, callback: (error?: string) => void) => {
                    if (value && contractData.value.startDate) {
                        if (dayjs(value).isBefore(dayjs(contractData.value.startDate))) {
                            callback('结束日期不能小于开始日期')
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                }
            }
        ]
    }
})

// 处理成交渠道变化
const handleDealChannelChange = () => {
    if (contractData.value.dealChannel !== 3) {
        contractData.value.assistantId = ''
        contractData.value.assistantName = ''
    }
}

// 搜索协助人
const handleAssistantSearch = async (keyword: string) => {
    if (!keyword) return

    assistantLoading.value = true
    try {
        const res = await getEhrUserList({
            keyword: keyword,
            pageNum: 1,
            pageSize: 10
        })
        assistantOptions.value = res.rows.map((item: any) => ({
            id: item.id,
            name: item.name,
            tel: item.tel
        }))
    } finally {
        assistantLoading.value = false
    }
}

const handleAssistantChange = (value: string) => {
    const assistant = assistantOptions.value.find(item => item.name === value)
    contractData.value.assistantId = assistant?.id || ''
}

// 计算租赁时长
const calculateLeaseDuration = () => {
    if (contractData.value.startDate && contractData.value.endDate) {
        const start = dayjs(contractData.value.startDate)
        const end = dayjs(contractData.value.endDate)

        // 计算总年数
        const years = end.diff(start, 'year')
        // 计算剩余月数（减去年数后的月数）
        const months = end.diff(start.add(years, 'year'), 'month')
        // 计算剩余天数（减去年数和月数后的天数）
        const remainingDays = end.diff(start.add(years, 'year').add(months, 'month'), 'day')

        contractData.value.rentYear = years
        contractData.value.rentMonth = months
        contractData.value.rentDay = remainingDays
    }
}

// 根据租赁时长计算结束日期
const calculateEndDate = () => {
    if (contractData.value.startDate && (contractData.value.rentYear || contractData.value.rentMonth || contractData.value.rentDay)) {
        const endDate = dayjs(contractData.value.startDate)
            .add(contractData.value.rentYear || 0, 'year')
            .add(contractData.value.rentMonth || 0, 'month')
            .add(contractData.value.rentDay || 0, 'day')
            .subtract(1, 'day')
        contractData.value.endDate = endDate.format('YYYY-MM-DD')
        nextTick(() => {
            // 更新结束日期的校验
            formRef.value?.validateField?.('endDate')
        })
    }
}

// 处理开始日期变化
const handleStartDateChange = (value: string) => {
    if (value && contractData.value.endDate) {
        if (dayjs(contractData.value.endDate).isBefore(dayjs(value))) {
            Message.error('结束日期不能小于开始日期')
            // 清空结束日期
            contractData.value.endDate = ''
            // 清空租赁期限
            contractData.value.rentYear = undefined
            contractData.value.rentMonth = undefined
            contractData.value.rentDay = undefined
            return
        }
    }
    calculateLeaseDuration()
}

// 表单验证
const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('合同基本信息', errors);
            reject()
        } else {
            resolve(true)
        }
    })
}

// 暴露方法给父组件
defineExpose({
    validate,
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.arco-form-item {
    margin-bottom: v-bind(formItemMargin);
}

.detail-text {
    padding: 4px 0;
    color: #333;
}
</style>