{"openapi": "3.0.1", "info": {"title": "系统模块接口文档", "description": "系统模块接口描述", "contact": {"name": "<PERSON>.Ma", "url": ""}, "version": "1.0.0"}, "servers": [{"url": "http://************:8570/prod-api/system"}], "security": [{"apikey": []}], "paths": {"/user/resetPwd": {"put": {"tags": ["用户管理接口"], "summary": "重置密码", "description": "重置密码", "operationId": "resetPwd", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUser"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/user/recordlogin": {"put": {"tags": ["用户管理接口"], "operationId": "recordlogin", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUser"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/user/profile": {"get": {"tags": ["sys-profile-controller"], "operationId": "profile", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "put": {"tags": ["sys-profile-controller"], "operationId": "updateProfile", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUser"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/user/profile/updatePwd": {"put": {"tags": ["sys-profile-controller"], "operationId": "updatePwd", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role": {"put": {"tags": ["角色管理接口"], "summary": "角色修改", "description": "角色修改", "operationId": "edit", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRoleModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "post": {"tags": ["角色管理接口"], "summary": "角色新增", "description": "角色新增", "operationId": "add", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRoleModel"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/notice": {"put": {"tags": ["sys-notice-controller"], "operationId": "edit_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysNotice"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "post": {"tags": ["sys-notice-controller"], "operationId": "add_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysNotice"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/menu": {"put": {"tags": ["菜单管理接口"], "operationId": "edit_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysMenu"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "post": {"tags": ["菜单管理接口"], "operationId": "add_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysMenu"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/type": {"put": {"tags": ["sys-dict-type-controller"], "operationId": "edit_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictType"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "post": {"tags": ["sys-dict-type-controller"], "operationId": "add_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictType"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/data": {"put": {"tags": ["sys-dict-data-controller"], "operationId": "edit_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictData"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "post": {"tags": ["sys-dict-data-controller"], "operationId": "add_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictData"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/config": {"put": {"tags": ["sys-config-controller"], "operationId": "edit_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysConfig"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "post": {"tags": ["sys-config-controller"], "operationId": "add_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysConfig"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/user/profile/avatar": {"post": {"tags": ["sys-profile-controller"], "operationId": "avatar", "requestBody": {"content": {"application/json": {"schema": {"required": ["avatarfile"], "type": "object", "properties": {"avatarfile": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/user/list": {"post": {"tags": ["用户管理接口"], "summary": "用户列表查询", "description": "用户列表查询", "operationId": "list", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserBo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/user/export": {"post": {"tags": ["用户管理接口"], "summary": "用户列表导出", "description": "用户列表导出", "operationId": "export", "parameters": [{"name": "user", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysUserBo"}}], "responses": {"200": {"description": "OK"}}}}, "/user/changeStatus": {"post": {"tags": ["用户管理接口"], "summary": "用户状态修改", "description": "用户状态修改", "operationId": "changeStatus", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserChangeStatusBo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/user/authRole": {"post": {"tags": ["用户管理接口"], "summary": "用户授权角色", "description": "用户授权角色", "operationId": "insertAuthRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUserAuthRoleBo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role/user/list": {"post": {"tags": ["角色管理接口"], "summary": "查询已分配用户角色列表", "description": "查询已分配用户角色列表", "operationId": "allocatedList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRoleUserQueryBo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/role/user/cancel/bind": {"post": {"tags": ["角色管理接口"], "summary": "取消授权用户", "description": "取消授权用户", "operationId": "cancelAuthUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRoleUserCancelBo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role/user/bind": {"post": {"tags": ["角色管理接口"], "summary": "选择用户授权角色", "description": "选择用户授权角色", "operationId": "selectAuthUserAll", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRoleUserBo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role/org/bind": {"post": {"tags": ["角色管理接口"], "summary": "角色绑定组织", "description": "角色绑定组织", "operationId": "bindOrg", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRoleOrgBo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role/menu/bind": {"post": {"tags": ["角色管理接口"], "summary": "角色绑定菜单", "description": "角色绑定菜单", "operationId": "bindMenu", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRoleMenuBo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/operlog": {"post": {"tags": ["sys-operlog-controller"], "operationId": "add_6", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysOperLog"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/operlog/export": {"post": {"tags": ["sys-operlog-controller"], "operationId": "export_1", "parameters": [{"name": "operLog", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysOperLog"}}], "responses": {"200": {"description": "OK"}}}}, "/logininfor": {"post": {"tags": ["sys-logininfor-controller"], "operationId": "add_7", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysLogininfor"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/logininfor/export": {"post": {"tags": ["sys-logininfor-controller"], "operationId": "export_2", "parameters": [{"name": "logininfor", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysLogininfor"}}], "responses": {"200": {"description": "OK"}}}}, "/ehr/userList": {"post": {"tags": ["ehr-org-user-controller"], "operationId": "idmUserList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EhrUserQueryBO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/dict/type/export": {"post": {"tags": ["sys-dict-type-controller"], "operationId": "export_3", "parameters": [{"name": "dictType", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysDictType"}}], "responses": {"200": {"description": "OK"}}}}, "/dict/data/export": {"post": {"tags": ["sys-dict-data-controller"], "operationId": "export_4", "parameters": [{"name": "dictData", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysDictData"}}], "responses": {"200": {"description": "OK"}}}}, "/config/export": {"post": {"tags": ["sys-config-controller"], "operationId": "export_5", "parameters": [{"name": "config", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysConfig"}}], "responses": {"200": {"description": "OK"}}}}, "/user/info/{username}": {"get": {"tags": ["用户管理接口"], "operationId": "info", "parameters": [{"name": "username", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RLoginUser"}}}}}}}, "/user/getInfo": {"get": {"tags": ["用户管理接口"], "summary": "获取用户信息", "description": "获取用户信息", "operationId": "getInfo", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/user/authRole/{userId}": {"get": {"tags": ["用户管理接口"], "summary": "用户详情", "description": "根据用户编号获取授权角色", "operationId": "authRole", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role/{roleId}": {"get": {"tags": ["角色管理接口"], "summary": "角色详情", "description": "根据角色id获取详细信息", "operationId": "getInfo_1", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role/org/list": {"get": {"tags": ["角色管理接口"], "summary": "角色组织", "description": "根据角色获取组织id", "operationId": "getOrgByRoleId", "parameters": [{"name": "roleId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role/menu/list": {"get": {"tags": ["角色管理接口"], "summary": "角色菜单", "description": "根据角色获取菜单id", "operationId": "getByRoleId", "parameters": [{"name": "roleId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role/list": {"get": {"tags": ["角色管理接口"], "summary": "角色列表查询", "description": "角色列表查询", "operationId": "list_1", "parameters": [{"name": "role", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysRoleQueryBo"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/org/tree": {"get": {"tags": ["组织管理接口"], "summary": "获取当前人拥有的组织树", "description": "获取当前人拥有的组织树", "operationId": "tree", "parameters": [{"name": "sysOrgBo", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysOrgBo"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/operlog/list": {"get": {"tags": ["sys-operlog-controller"], "operationId": "list_2", "parameters": [{"name": "operLog", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysOperLog"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/online/list": {"get": {"tags": ["sys-user-online-controller"], "operationId": "list_3", "parameters": [{"name": "ipaddr", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "userName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/notice/{noticeId}": {"get": {"tags": ["sys-notice-controller"], "operationId": "getInfo_2", "parameters": [{"name": "noticeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/notice/list": {"get": {"tags": ["sys-notice-controller"], "operationId": "list_4", "parameters": [{"name": "notice", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysNotice"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/menu/{menuId}": {"get": {"tags": ["菜单管理接口"], "summary": "菜单详情", "description": "根据菜单编号获取详细信息", "operationId": "getInfo_3", "parameters": [{"name": "menuId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "delete": {"tags": ["菜单管理接口"], "operationId": "remove", "parameters": [{"name": "menuId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/menu/treeselect": {"get": {"tags": ["菜单管理接口"], "operationId": "treeselect", "parameters": [{"name": "menu", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysMenu"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/menu/roleMenuTreeselect/{roleId}": {"get": {"tags": ["菜单管理接口"], "operationId": "roleMenuTreeselect", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/menu/list": {"get": {"tags": ["菜单管理接口"], "summary": "菜单列表查询", "description": "菜单列表查询", "operationId": "list_5", "parameters": [{"name": "menu", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysMenu"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/menu/getRouters": {"get": {"tags": ["菜单管理接口"], "operationId": "getRouters", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/menu/getByRoleId/{roleId}": {"get": {"tags": ["菜单管理接口"], "operationId": "getByRoleId_1", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListLong"}}}}}}}, "/logininfor/unlock/{userName}": {"get": {"tags": ["sys-logininfor-controller"], "operationId": "unlock", "parameters": [{"name": "userName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/logininfor/list": {"get": {"tags": ["sys-logininfor-controller"], "operationId": "list_6", "parameters": [{"name": "logininfor", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysLogininfor"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/esb/sync/mdm/org": {"get": {"tags": ["sync-mdm-controller"], "operationId": "getOrgList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/esb/sync/ehr/user": {"get": {"tags": ["sync-ehr-controller"], "operationId": "getUserList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/esb/sync/ehr/post": {"get": {"tags": ["sync-ehr-controller"], "operationId": "getPostList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/esb/sync/ehr/org": {"get": {"tags": ["sync-ehr-controller"], "operationId": "getOrgList_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/ehr/org": {"get": {"tags": ["ehr-org-user-controller"], "operationId": "idmOrgList", "parameters": [{"name": "orgId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "keyword", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/type/{dictId}": {"get": {"tags": ["sys-dict-type-controller"], "operationId": "getInfo_4", "parameters": [{"name": "dictId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/type/optionselect": {"get": {"tags": ["sys-dict-type-controller"], "operationId": "optionselect", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/type/list": {"get": {"tags": ["sys-dict-type-controller"], "operationId": "list_7", "parameters": [{"name": "dictType", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysDictType"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/dict/data/{dictCode}": {"get": {"tags": ["sys-dict-data-controller"], "operationId": "getInfo_5", "parameters": [{"name": "dictCode", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/data/type/{dictType}": {"get": {"tags": ["sys-dict-data-controller"], "operationId": "dictType", "parameters": [{"name": "dictType", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/data/list": {"get": {"tags": ["sys-dict-data-controller"], "operationId": "list_8", "parameters": [{"name": "dictData", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysDictData"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/config/{configId}": {"get": {"tags": ["sys-config-controller"], "operationId": "getInfo_6", "parameters": [{"name": "configId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/config/list": {"get": {"tags": ["sys-config-controller"], "operationId": "list_9", "parameters": [{"name": "config", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysConfig"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/config/configKey/{configKey}": {"get": {"tags": ["sys-config-controller"], "operationId": "getConfigKey", "parameters": [{"name": "config<PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/role/{roleIds}": {"delete": {"tags": ["角色管理接口"], "summary": "角色删除", "description": "角色删除", "operationId": "remove_1", "parameters": [{"name": "roleIds", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/operlog/{operIds}": {"delete": {"tags": ["sys-operlog-controller"], "operationId": "remove_2", "parameters": [{"name": "operIds", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/operlog/clean": {"delete": {"tags": ["sys-operlog-controller"], "operationId": "clean", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/online/{tokenId}": {"delete": {"tags": ["sys-user-online-controller"], "operationId": "forceLogout", "parameters": [{"name": "tokenId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/notice/{noticeIds}": {"delete": {"tags": ["sys-notice-controller"], "operationId": "remove_3", "parameters": [{"name": "noticeIds", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/logininfor/{infoIds}": {"delete": {"tags": ["sys-logininfor-controller"], "operationId": "remove_4", "parameters": [{"name": "infoIds", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/logininfor/clean": {"delete": {"tags": ["sys-logininfor-controller"], "operationId": "clean_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/type/{dictIds}": {"delete": {"tags": ["sys-dict-type-controller"], "operationId": "remove_5", "parameters": [{"name": "dictIds", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/type/refreshCache": {"delete": {"tags": ["sys-dict-type-controller"], "operationId": "refreshCache", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/dict/data/{dictCodes}": {"delete": {"tags": ["sys-dict-data-controller"], "operationId": "remove_6", "parameters": [{"name": "dictCodes", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/config/{configIds}": {"delete": {"tags": ["sys-config-controller"], "operationId": "remove_7", "parameters": [{"name": "configIds", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/config/refreshCache": {"delete": {"tags": ["sys-config-controller"], "operationId": "refreshCache_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}}, "components": {"schemas": {"SysRole": {"required": ["<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"createBy": {"type": "string"}, "createByName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateByName": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "roleId": {"type": "integer", "format": "int64"}, "roleName": {"maxLength": 30, "minLength": 0, "type": "string"}, "type": {"type": "integer", "format": "int32"}, "remark": {"maxLength": 500, "minLength": 0, "type": "string"}, "roomPermissions": {"type": "string"}, "contractPermissions": {"type": "string"}, "menuIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "deptIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "permissions": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "admin": {"type": "boolean"}}}, "SysUser": {"required": ["userName"], "type": "object", "properties": {"createBy": {"type": "string"}, "createByName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateByName": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "userId": {"type": "integer", "format": "int64"}, "deptId": {"type": "integer", "format": "int64"}, "userName": {"maxLength": 30, "minLength": 0, "type": "string"}, "nickName": {"maxLength": 30, "minLength": 0, "type": "string"}, "email": {"type": "string"}, "phonenumber": {"type": "string"}, "sex": {"type": "string"}, "avatar": {"type": "string"}, "password": {"type": "string"}, "status": {"type": "string"}, "delFlag": {"type": "string"}, "loginIp": {"type": "string"}, "loginDate": {"type": "string", "format": "date-time"}, "remark": {"type": "string"}, "syncId": {"type": "string"}, "postName": {"type": "string"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/SysRole"}}, "roleIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "admin": {"type": "boolean"}}}, "AjaxResult": {"type": "object", "properties": {"error": {"type": "boolean"}, "success": {"type": "boolean"}, "warn": {"type": "boolean"}, "empty": {"type": "boolean"}}, "additionalProperties": {"type": "object"}}, "RBoolean": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"type": "boolean"}}}, "SysRoleModel": {"required": ["<PERSON><PERSON><PERSON>", "type"], "type": "object", "properties": {"roleId": {"type": "integer", "format": "int64"}, "roleName": {"title": "角色名称", "maxLength": 30, "minLength": 0, "type": "string"}, "type": {"title": "角色类型,1:全部，2:菜单，3:数据", "type": "integer", "format": "int32"}, "remark": {"title": "角色说明", "maxLength": 500, "minLength": 0, "type": "string"}}}, "SysNotice": {"required": ["noticeTitle"], "type": "object", "properties": {"createBy": {"type": "string"}, "createByName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateByName": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "noticeId": {"type": "integer", "format": "int64"}, "noticeTitle": {"maxLength": 50, "minLength": 0, "type": "string"}, "noticeType": {"type": "string"}, "noticeContent": {"type": "string"}, "status": {"type": "string"}}}, "SysMenu": {"required": ["menuName", "menuType", "orderNum"], "type": "object", "properties": {"createBy": {"type": "string"}, "createByName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateByName": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "menuId": {"type": "integer", "format": "int64"}, "menuName": {"maxLength": 50, "minLength": 0, "type": "string"}, "parentName": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}, "orderNum": {"type": "integer", "format": "int32"}, "path": {"maxLength": 200, "minLength": 0, "type": "string"}, "component": {"maxLength": 200, "minLength": 0, "type": "string"}, "query": {"type": "string"}, "routeName": {"type": "string"}, "isFrame": {"type": "string"}, "isCache": {"type": "string"}, "menuType": {"type": "string"}, "visible": {"type": "string"}, "status": {"type": "string"}, "perms": {"maxLength": 100, "minLength": 0, "type": "string"}, "icon": {"type": "string"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/SysMenu"}}}}, "SysDictType": {"required": ["dictName", "dictType"], "type": "object", "properties": {"createBy": {"type": "string"}, "createByName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateByName": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "dictId": {"type": "integer", "format": "int64"}, "dictName": {"maxLength": 100, "minLength": 0, "type": "string"}, "dictType": {"maxLength": 100, "minLength": 0, "pattern": "^[a-z][a-z0-9_]*$", "type": "string"}, "status": {"type": "string"}}}, "SysDictData": {"required": ["dict<PERSON><PERSON>l", "dictType", "dict<PERSON><PERSON>ue"], "type": "object", "properties": {"createBy": {"type": "string"}, "createByName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateByName": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "dictCode": {"type": "integer", "format": "int64"}, "dictSort": {"type": "integer", "format": "int64"}, "dictLabel": {"maxLength": 100, "minLength": 0, "type": "string"}, "dictValue": {"maxLength": 100, "minLength": 0, "type": "string"}, "dictType": {"maxLength": 100, "minLength": 0, "type": "string"}, "cssClass": {"maxLength": 100, "minLength": 0, "type": "string"}, "listClass": {"type": "string"}, "isDefault": {"type": "string"}, "status": {"type": "string"}, "default": {"type": "boolean"}}}, "SysConfig": {"required": ["config<PERSON><PERSON>", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"createBy": {"type": "string"}, "createByName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateByName": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "configId": {"type": "integer", "format": "int64"}, "configName": {"maxLength": 100, "minLength": 0, "type": "string"}, "configKey": {"maxLength": 100, "minLength": 0, "type": "string"}, "configValue": {"maxLength": 500, "minLength": 0, "type": "string"}, "configType": {"type": "string"}}}, "SysUserBo": {"type": "object", "properties": {"nickName": {"title": "人员名称", "type": "string"}, "userName": {"title": "账号", "type": "string"}, "phonenumber": {"title": "手机号", "type": "string"}, "menuRoles": {"title": "功能角色", "type": "array", "items": {"title": "功能角色", "type": "integer", "format": "int64"}}, "dataRoles": {"title": "数据角色", "type": "array", "items": {"title": "数据角色", "type": "integer", "format": "int64"}}, "status": {"title": "状态", "type": "integer", "format": "int32"}}}, "TableDataInfo": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "rows": {"type": "array", "items": {"type": "object"}}, "code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}}, "SysUserChangeStatusBo": {"type": "object", "properties": {"userId": {"title": "用户id", "type": "integer", "format": "int64"}, "status": {"title": "状态", "type": "string"}}}, "SysUserAuthRoleBo": {"required": ["roleIdList"], "type": "object", "properties": {"ehrUserIdList": {"title": "ehr用户id", "type": "array", "items": {"title": "ehr用户id", "type": "string"}}, "roleIdList": {"title": "角色id", "type": "array", "items": {"title": "角色id", "type": "integer", "format": "int64"}}, "userIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SysRoleUserQueryBo": {"required": ["roleId", "type"], "type": "object", "properties": {"type": {"title": "角色类型", "type": "integer", "format": "int32"}, "roleId": {"title": "角色id", "type": "integer", "format": "int64"}, "userName": {"title": "用户姓名", "type": "string"}, "userPhone": {"title": "用户手机号", "type": "string"}, "status": {"title": "用户状态", "type": "integer", "format": "int32"}}}, "SysRoleUserCancelBo": {"required": ["roleId", "userIdList"], "type": "object", "properties": {"roleId": {"title": "角色id", "type": "integer", "format": "int64"}, "userIdList": {"title": "用户id", "type": "array", "items": {"title": "用户id", "type": "integer", "format": "int64"}}}}, "SysRoleUserBo": {"required": ["ehrUserIdList", "roleId"], "type": "object", "properties": {"roleId": {"title": "角色id", "type": "integer", "format": "int64"}, "ehrUserIdList": {"title": "ehr用户id", "type": "array", "items": {"title": "ehr用户id", "type": "string"}}, "userIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SysRoleOrgBo": {"required": ["contractPermission", "orgIdList", "roleId", "roomPermissions"], "type": "object", "properties": {"roleId": {"title": "角色id", "type": "integer", "format": "int64"}, "roomPermissions": {"title": "房源权限,all,business,zhongchuang", "type": "array", "items": {"title": "房源权限,all,business,zhongchuang", "type": "string"}}, "contractPermission": {"title": "合同权限, all,personal", "type": "string"}, "orgIdList": {"title": "组织id", "type": "array", "items": {"title": "组织id", "type": "string"}}}}, "SysRoleMenuBo": {"required": ["menuIdList", "roleId"], "type": "object", "properties": {"roleId": {"title": "角色id", "type": "integer", "format": "int64"}, "menuIdList": {"title": "菜单id", "type": "array", "items": {"title": "菜单id", "type": "integer", "format": "int64"}}}}, "SysOperLog": {"type": "object", "properties": {"createBy": {"type": "string"}, "createByName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateByName": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "operId": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "businessType": {"type": "integer", "format": "int32"}, "businessTypes": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "method": {"type": "string"}, "requestMethod": {"type": "string"}, "operatorType": {"type": "integer", "format": "int32"}, "operName": {"type": "string"}, "deptName": {"type": "string"}, "operUrl": {"type": "string"}, "operIp": {"type": "string"}, "operParam": {"type": "string"}, "jsonResult": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "errorMsg": {"type": "string"}, "operTime": {"type": "string", "format": "date-time"}, "costTime": {"type": "integer", "format": "int64"}}}, "SysLogininfor": {"type": "object", "properties": {"createBy": {"type": "string"}, "createByName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateByName": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "infoId": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}, "status": {"type": "string"}, "ipaddr": {"type": "string"}, "msg": {"type": "string"}, "accessTime": {"type": "string", "format": "date-time"}}}, "EhrUserQueryBO": {"type": "object", "properties": {"orgList": {"type": "array", "items": {"type": "string"}}, "keyword": {"type": "string"}}}, "LoginUser": {"type": "object", "properties": {"token": {"type": "string"}, "userid": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "nickName": {"type": "string"}, "loginTime": {"type": "integer", "format": "int64"}, "expireTime": {"type": "integer", "format": "int64"}, "ipaddr": {"type": "string"}, "permissions": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "projectPermissions": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "roomPermissions": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "contractPermission": {"type": "string"}, "roles": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "sysUser": {"$ref": "#/components/schemas/SysUserDTO"}, "admin": {"type": "boolean"}}}, "RLoginUser": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/LoginUser"}}}, "SysRoleDTO": {"type": "object", "properties": {"roleId": {"type": "integer", "format": "int64"}, "roleName": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "remark": {"maxLength": 500, "minLength": 0, "type": "string"}, "roomPermissions": {"type": "string"}, "contractPermissions": {"type": "string"}, "admin": {"type": "boolean"}}}, "SysUserDTO": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "deptId": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}, "nickName": {"type": "string"}, "email": {"type": "string"}, "phonenumber": {"type": "string"}, "sex": {"type": "string"}, "avatar": {"type": "string"}, "password": {"type": "string"}, "status": {"type": "string"}, "delFlag": {"type": "string"}, "loginIp": {"type": "string"}, "loginDate": {"type": "string", "format": "date-time"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/SysRoleDTO"}}, "admin": {"type": "boolean"}}}, "SysRoleQueryBo": {"required": ["type"], "type": "object", "properties": {"type": {"title": "角色类型", "type": "integer", "format": "int32"}}}, "SysOrgBo": {"type": "object", "properties": {"name": {"title": "组织名称", "type": "string"}}}, "RListLong": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "securitySchemes": {"apikey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "scheme": "Bearer"}}}}