---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/定单管理

<a id="opIdsaveBooking"></a>

## POST 保存定单

POST /booking/save

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingNo": "string",
  "bookingAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "isDel": true,
  "isSubmit": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingAddDTO](#schemabookingadddto)| 否 | 定单数据|none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[AjaxResult](#schemaajaxresult)|

<a id="opIdprintBooking"></a>

## POST 定单套打接口

POST /booking/print

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "bookingData": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "projectId": "string",
    "customerName": "string",
    "propertyType": "string",
    "roomId": "string",
    "roomName": "string",
    "bookingNo": "string",
    "bookingAmount": 0,
    "receivableDate": "2019-08-24T14:15:22Z",
    "expectSignDate": "2019-08-24T14:15:22Z",
    "isRefundable": 0,
    "cancelTime": "2019-08-24T14:15:22Z",
    "cancelBy": "string",
    "cancelByName": "string",
    "cancelReason": 0,
    "isRefund": 0,
    "cancelEnclosure": "string",
    "cancelRemark": "string",
    "status": 0,
    "contractId": "string",
    "refundId": "string",
    "isDel": true,
    "isSubmit": 0
  }
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingPrintDto](#schemabookingprintdto)| 否 ||none|

> 返回示例

> 200 Response

```
{"fileName":"string","fileUrl":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[EnclosureInfo](#schemaenclosureinfo)|

<a id="opIdlist_9"></a>

## POST 定单列表查询接口

POST /booking/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "searchType": 0,
  "projectId": "string",
  "customerName": "string",
  "roomName": "string",
  "status": 0,
  "createTimeStart": "string",
  "createTimeEnd": "string",
  "createByName": "string",
  "actualReceiveTimeStart": "string",
  "actualReceiveTimeEnd": "string",
  "contractNo": "string",
  "signDateStart": "string",
  "signDateEnd": "string",
  "contractLeaseUnit": "string",
  "lesseeName": "string",
  "cancelReason": 0,
  "cancelByName": "string",
  "cancelTimeStart": "string",
  "cancelTimeEnd": "string",
  "bookingNo": "string",
  "bookingType": 0,
  "roomIds": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingQueryDTO](#schemabookingquerydto)| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","projectName":"string","customerName":"string","propertyType":"string","roomId":"string","roomName":"string","bookingNo":"string","bookingAmount":0,"unpaidAmount":0,"receivableDate":"2019-08-24T14:15:22Z","expectSignDate":"2019-08-24T14:15:22Z","isRefundable":0,"cancelTime":"2019-08-24T14:15:22Z","cancelBy":"string","cancelByName":"string","cancelReason":0,"isRefund":0,"cancelEnclosure":"string","cancelRemark":"string","status":0,"contractId":"string","refundId":"string","createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"contractNo":"string","signDate":"2019-08-24T14:15:22Z","contractLeaseUnit":"string","lesseeName":"string","receivedAmount":0,"receivedDate":"2019-08-24T14:15:22Z","payMethod":"string"}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[BookingVo](#schemabookingvo)]|false|none||[定单基本信息]|
|» 定单基本信息|[BookingVo](#schemabookingvo)|false|none|定单基本信息|定单基本信息|
|»» id|string|false|none|$column.columnComment|none|
|»» projectId|string|false|none|项目id|项目id|
|»» projectName|string|false|none|项目名称|项目名称|
|»» customerName|string|false|none|客户名称|客户名称|
|»» propertyType|string|false|none|意向物业类型|意向物业类型|
|»» roomId|string|false|none|房源id|房源id|
|»» roomName|string|false|none|房源名称|房源名称|
|»» bookingNo|string|false|none|定单号|定单号|
|»» bookingAmount|number|false|none|定单金额|定单金额|
|»» unpaidAmount|number|false|none|待收金额|待收金额|
|»» receivableDate|string(date-time)|false|none|应收日期|应收日期|
|»» expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|»» isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|»» cancelTime|string(date-time)|false|none|作废时间|作废时间|
|»» cancelBy|string|false|none|作废人|作废人|
|»» cancelByName|string|false|none|作废人姓名|作废人姓名|
|»» cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|»» isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|»» cancelEnclosure|string|false|none|退定附件|退定附件|
|»» cancelRemark|string|false|none|退定说明|退定说明|
|»» status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|»» contractId|string|false|none|转签约合同id|转签约合同id|
|»» refundId|string|false|none|退款单id|退款单id|
|»» createBy|string|false|none|创建人|创建人|
|»» createByName|string|false|none|创建人姓名|创建人姓名|
|»» createTime|string(date-time)|false|none|创建日期|创建日期|
|»» updateBy|string|false|none|更新人|更新人|
|»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»» updateTime|string(date-time)|false|none|更新日期|更新日期|
|»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|»» contractNo|string|false|none|合同号|合同号|
|»» signDate|string(date-time)|false|none|合同签订日期|合同签订日期|
|»» contractLeaseUnit|string|false|none|租赁单元|租赁单元|
|»» lesseeName|string|false|none|承租人名称|承租人名称|
|»» receivedAmount|number|false|none|定单已收金额|定单已收金额|
|»» receivedDate|string(date-time)|false|none|实收日期|实收日期|
|»» payMethod|string|false|none|支付方式|支付方式|

<a id="opIdinvalidBooking"></a>

## POST 定单作废接口

POST /booking/invalid

> Body 请求参数

```json
{
  "id": "string",
  "cancelRemark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingInvalidDto](#schemabookinginvaliddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[AjaxResult](#schemaajaxresult)|

<a id="opIdcancelBooking"></a>

## POST 退定接口

POST /booking/cancel

> Body 请求参数

```json
{
  "id": "string",
  "cancelRemark": "string",
  "cancelEnclosure": "string",
  "isRefund": 0,
  "isSubmit": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingCancelDto](#schemabookingcanceldto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddetail_8"></a>

## GET 定单详情接口

GET /booking/detail/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||定单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","projectName":"string","customerName":"string","propertyType":"string","roomId":"string","roomName":"string","bookingNo":"string","bookingAmount":0,"unpaidAmount":0,"receivableDate":"2019-08-24T14:15:22Z","expectSignDate":"2019-08-24T14:15:22Z","isRefundable":0,"cancelTime":"2019-08-24T14:15:22Z","cancelBy":"string","cancelByName":"string","cancelReason":0,"isRefund":0,"cancelEnclosure":"string","cancelRemark":"string","status":0,"contractId":"string","refundId":"string","createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"contractNo":"string","signDate":"2019-08-24T14:15:22Z","contractLeaseUnit":"string","lesseeName":"string","receivedAmount":0,"receivedDate":"2019-08-24T14:15:22Z","payMethod":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[BookingVo](#schemabookingvo)|

<a id="opIdcostDetail"></a>

## GET 查看定单记账流水详情接口

GET /booking/costDetail/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||定单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"booking":{"id":"string","projectId":"string","projectName":"string","customerName":"string","propertyType":"string","roomId":"string","roomName":"string","bookingNo":"string","bookingAmount":0,"unpaidAmount":0,"receivableDate":"2019-08-24T14:15:22Z","expectSignDate":"2019-08-24T14:15:22Z","isRefundable":0,"cancelTime":"2019-08-24T14:15:22Z","cancelBy":"string","cancelByName":"string","cancelReason":0,"isRefund":0,"cancelEnclosure":"string","cancelRemark":"string","status":0,"contractId":"string","refundId":"string","createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"contractNo":"string","signDate":"2019-08-24T14:15:22Z","contractLeaseUnit":"string","lesseeName":"string","receivedAmount":0,"receivedDate":"2019-08-24T14:15:22Z","payMethod":"string"},"cost":{"id":"string","projectId":"string","projectName":"string","chargeType":0,"bizId":"string","refundId":"string","refundCostId":"string","bizNo":"string","customerId":"string","customerName":"string","costType":0,"period":0,"subjectId":"string","subjectName":"string","startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","receivableDate":"2019-08-24T14:15:22Z","status":0,"canPay":true,"isRevenueBill":true,"isRevenueGenerated":true,"isFromContract":true,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"carryoverAmount":0,"confirmStatus":0,"createByName":"string","updateByName":"string","isDel":true,"contractType":0,"roomName":"string","contractStatus":0,"contractStatusTwo":0},"flowRelList":[{"id":"string","costId":"string","refundId":"string","flowId":"string","flowNo":"string","type":0,"confirmStatus":0,"confirmTime":"2019-08-24T14:15:22Z","confirmUserId":"string","confirmUserName":"string","payAmount":0,"pendingAmount":0,"acctAmount":0,"remark":"string","createByName":"string","createTime":"string","updateByName":"string","isDel":true,"projectId":"string","projectName":"string","entryTime":"2019-08-24T14:15:22Z","payType":0,"payMethod":"string","orderNo":"string","usedAmount":0,"payerName":"string","target":"string","merchant":"string","cumulativeAcctAmount":0}],"flowLogList":[{"id":"string","costId":"string","refundId":"string","flowId":"string","flowNo":"string","type":0,"carryoverId":"string","carryoverNo":"string","amount":0,"createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[BookingDetailVo](#schemabookingdetailvo)|

<a id="opIddeleteBooking"></a>

## DELETE 删除定单接口

DELETE /booking/delete/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||定单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_BookingAddDTO">BookingAddDTO</h2>

<a id="schemabookingadddto"></a>
<a id="schema_BookingAddDTO"></a>
<a id="tocSbookingadddto"></a>
<a id="tocsbookingadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingNo": "string",
  "bookingAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "isDel": true,
  "isSubmit": 0
}

```

定单数据

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|customerName|string|false|none|客户名称|客户名称|
|propertyType|string|false|none|意向物业类型|意向物业类型|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房源名称|房源名称|
|bookingNo|string|false|none|定单号|定单号|
|bookingAmount|number|false|none|定单金额|定单金额|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|cancelEnclosure|string|false|none|退定附件|退定附件|
|cancelRemark|string|false|none|退定说明|退定说明|
|status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|contractId|string|false|none|转签约合同id|转签约合同id|
|refundId|string|false|none|退款单id|退款单id|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|isSubmit|integer(int32)|false|none|0-暂存,1-提交|0-暂存,1-提交|

<h2 id="tocS_BookingInvalidDto">BookingInvalidDto</h2>

<a id="schemabookinginvaliddto"></a>
<a id="schema_BookingInvalidDto"></a>
<a id="tocSbookinginvaliddto"></a>
<a id="tocsbookinginvaliddto"></a>

```json
{
  "id": "string",
  "cancelRemark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|定单ID|需要作废的定单ID|
|cancelRemark|string|false|none|作废说明|定单作废的说明|

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_BookingCancelDto">BookingCancelDto</h2>

<a id="schemabookingcanceldto"></a>
<a id="schema_BookingCancelDto"></a>
<a id="tocSbookingcanceldto"></a>
<a id="tocsbookingcanceldto"></a>

```json
{
  "id": "string",
  "cancelRemark": "string",
  "cancelEnclosure": "string",
  "isRefund": 0,
  "isSubmit": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|订单ID|需要退定的订单ID|
|cancelRemark|string|false|none|退定说明|退定的具体说明|
|cancelEnclosure|string|false|none|退定附件|退定相关的附件|
|isRefund|integer(int32)|false|none|是否退款|是否退款，0-否，1-是|
|isSubmit|integer(int32)|false|none|是否提交|使用该字段判断是暂存还是提交，0-暂存，1-提交|

<h2 id="tocS_BookingQueryDTO">BookingQueryDTO</h2>

<a id="schemabookingquerydto"></a>
<a id="schema_BookingQueryDTO"></a>
<a id="tocSbookingquerydto"></a>
<a id="tocsbookingquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "searchType": 0,
  "projectId": "string",
  "customerName": "string",
  "roomName": "string",
  "status": 0,
  "createTimeStart": "string",
  "createTimeEnd": "string",
  "createByName": "string",
  "actualReceiveTimeStart": "string",
  "actualReceiveTimeEnd": "string",
  "contractNo": "string",
  "signDateStart": "string",
  "signDateEnd": "string",
  "contractLeaseUnit": "string",
  "lesseeName": "string",
  "cancelReason": 0,
  "cancelByName": "string",
  "cancelTimeStart": "string",
  "cancelTimeEnd": "string",
  "bookingNo": "string",
  "bookingType": 0,
  "roomIds": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|searchType|integer(int32)|false|none|页签类型|页签类型:1-待生效 2生效中 3已转签 4已作废|
|projectId|string|false|none|项目id|项目id|
|customerName|string|false|none|客户名称|客户名称|
|roomName|string|false|none|意向房源|意向房源|
|status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|createTimeStart|string|false|none|创建日期开始|创建日期开始|
|createTimeEnd|string|false|none|创建日期结束|创建日期结束|
|createByName|string|false|none|创建人姓名|创建人姓名|
|actualReceiveTimeStart|string|false|none|实收日期开始|实收日期开始|
|actualReceiveTimeEnd|string|false|none|实收日期结束|实收日期结束|
|contractNo|string|false|none|合同编号|合同编号|
|signDateStart|string|false|none|合同签订日期开始|合同签订日期开始|
|signDateEnd|string|false|none|合同签订日期结束|合同签订日期结束|
|contractLeaseUnit|string|false|none|合同租赁单元|合同租赁单元|
|lesseeName|string|false|none|承租人名称|承租人名称|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelTimeStart|string|false|none|作废日期开始|作废日期开始|
|cancelTimeEnd|string|false|none|作废日期结束|作废日期结束|
|bookingNo|string|false|none|定单号|定单号|
|bookingType|integer(int32)|false|none|定单类型：1-确认房源，0-暂不确认房源|定单类型：1-确认房源，0-暂不确认房源|
|roomIds|string|false|none|房源id，多个|房源id，多个|

<h2 id="tocS_BookingVo">BookingVo</h2>

<a id="schemabookingvo"></a>
<a id="schema_BookingVo"></a>
<a id="tocSbookingvo"></a>
<a id="tocsbookingvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingNo": "string",
  "bookingAmount": 0,
  "unpaidAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "contractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "contractLeaseUnit": "string",
  "lesseeName": "string",
  "receivedAmount": 0,
  "receivedDate": "2019-08-24T14:15:22Z",
  "payMethod": "string"
}

```

定单基本信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|customerName|string|false|none|客户名称|客户名称|
|propertyType|string|false|none|意向物业类型|意向物业类型|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房源名称|房源名称|
|bookingNo|string|false|none|定单号|定单号|
|bookingAmount|number|false|none|定单金额|定单金额|
|unpaidAmount|number|false|none|待收金额|待收金额|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|cancelEnclosure|string|false|none|退定附件|退定附件|
|cancelRemark|string|false|none|退定说明|退定说明|
|status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|contractId|string|false|none|转签约合同id|转签约合同id|
|refundId|string|false|none|退款单id|退款单id|
|createBy|string|false|none|创建人|创建人|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建日期|创建日期|
|updateBy|string|false|none|更新人|更新人|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新日期|更新日期|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractNo|string|false|none|合同号|合同号|
|signDate|string(date-time)|false|none|合同签订日期|合同签订日期|
|contractLeaseUnit|string|false|none|租赁单元|租赁单元|
|lesseeName|string|false|none|承租人名称|承租人名称|
|receivedAmount|number|false|none|定单已收金额|定单已收金额|
|receivedDate|string(date-time)|false|none|实收日期|实收日期|
|payMethod|string|false|none|支付方式|支付方式|

<h2 id="tocS_CostFlowLogVo">CostFlowLogVo</h2>

<a id="schemacostflowlogvo"></a>
<a id="schema_CostFlowLogVo"></a>
<a id="tocScostflowlogvo"></a>
<a id="tocscostflowlogvo"></a>

```json
{
  "id": "string",
  "costId": "string",
  "refundId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "carryoverId": "string",
  "carryoverNo": "string",
  "amount": 0,
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "isDel": true
}

```

账单流水记录列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|costId|string|false|none|账单id|账单id|
|refundId|string|false|none|退款单id|退款单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转|类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转|
|carryoverId|string|false|none|结转单id|结转单id|
|carryoverNo|string|false|none|结转单号|结转单号|
|amount|number|false|none|记账金额|记账金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_CostFlowRelVo">CostFlowRelVo</h2>

<a id="schemacostflowrelvo"></a>
<a id="schema_CostFlowRelVo"></a>
<a id="tocScostflowrelvo"></a>
<a id="tocscostflowrelvo"></a>

```json
{
  "id": "string",
  "costId": "string",
  "refundId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "confirmStatus": 0,
  "confirmTime": "2019-08-24T14:15:22Z",
  "confirmUserId": "string",
  "confirmUserName": "string",
  "payAmount": 0,
  "pendingAmount": 0,
  "acctAmount": 0,
  "remark": "string",
  "createByName": "string",
  "createTime": "string",
  "updateByName": "string",
  "isDel": true,
  "projectId": "string",
  "projectName": "string",
  "entryTime": "2019-08-24T14:15:22Z",
  "payType": 0,
  "payMethod": "string",
  "orderNo": "string",
  "usedAmount": 0,
  "payerName": "string",
  "target": "string",
  "merchant": "string",
  "cumulativeAcctAmount": 0
}

```

账单流水关系列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|costId|string|false|none|账单id|账单id|
|refundId|string|false|none|退款单id|退款单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|账单类型：1-收款 2-转入 3-转出 4-退款|账单类型：1-收款 2-转入 3-转出 4-退款|
|confirmStatus|integer(int32)|false|none|确认状态: 0-未确认、1-自动确认、2-手动确认|确认状态: 0-未确认、1-自动确认、2-手动确认|
|confirmTime|string(date-time)|false|none|确认时间|确认时间|
|confirmUserId|string|false|none|确认人id|确认人id|
|confirmUserName|string|false|none|确认人姓名|确认人姓名|
|payAmount|number|false|none|支付金额|支付金额|
|pendingAmount|number|false|none|账单待收金额|账单待收金额|
|acctAmount|number|false|none|本次记账金额|本次记账金额|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|entryTime|string(date-time)|false|none|入账时间|入账时间|
|payType|integer(int32)|false|none|支付类型|支付类型|
|payMethod|string|false|none|支付方式|支付方式|
|orderNo|string|false|none|订单号|订单号|
|usedAmount|number|false|none|已使用金额|已使用金额|
|payerName|string|false|none|支付人姓名|支付人姓名|
|target|string|false|none|支付对象|支付对象|
|merchant|string|false|none|收款商户|收款商户|
|cumulativeAcctAmount|number|false|none|累计记账金额|累计记账金额|

<h2 id="tocS_CostVo">CostVo</h2>

<a id="schemacostvo"></a>
<a id="schema_CostVo"></a>
<a id="tocScostvo"></a>
<a id="tocscostvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "chargeType": 0,
  "bizId": "string",
  "refundId": "string",
  "refundCostId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerName": "string",
  "costType": 0,
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "isRevenueBill": true,
  "isRevenueGenerated": true,
  "isFromContract": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractType": 0,
  "roomName": "string",
  "contractStatus": 0,
  "contractStatusTwo": 0
}

```

账单信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|chargeType|integer(int32)|false|none|收费类别: 0-定单, 1-合同|收费类别: 0-定单, 1-合同|
|bizId|string|false|none|业务id, 定单类别对应定单id, 合同类别对应合同id|业务id, 定单类别对应定单id, 合同类别对应合同id|
|refundId|string|false|none|退款单id|退款单id|
|refundCostId|string|false|none|退款关联的收费单Cost表id|退款关联的收费单Cost表id|
|bizNo|string|false|none|业务单号|业务单号|
|customerId|string|false|none|承租人id|承租人id|
|customerName|string|false|none|承租人名称|承租人名称|
|costType|integer(int32)|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|period|integer(int32)|false|none|账单期数|账单期数|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途名称|收款用途名称|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|status|integer(int32)|false|none|账单状态: 0-待收、1-待付、2-已收、3-已付|账单状态: 0-待收、1-待付、2-已收、3-已付|
|canPay|boolean|false|none|是否可收/付 0-否,1-是|是否可收/付 0-否,1-是|
|isRevenueBill|boolean|false|none|是否是营收抽点账单 0-否,1-是|是否是营收抽点账单 0-否,1-是|
|isRevenueGenerated|boolean|false|none|营收抽点金额是否已生成 0-否,1-是|营收抽点金额是否已生成 0-否,1-是|
|isFromContract|boolean|false|none|是否来自合同生成 0-否,1-是|是否来自合同生成 0-否,1-是|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|carryoverAmount|number|false|none|结转金额|结转金额|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|roomName|string|false|none|租赁资源|租赁资源|
|contractStatus|integer(int32)|false|none|合同一级状态|合同一级状态|
|contractStatusTwo|integer(int32)|false|none|合同二级状态|合同二级状态|

<h2 id="tocS_BookingDetailVo">BookingDetailVo</h2>

<a id="schemabookingdetailvo"></a>
<a id="schema_BookingDetailVo"></a>
<a id="tocSbookingdetailvo"></a>
<a id="tocsbookingdetailvo"></a>

```json
{
  "booking": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "customerName": "string",
    "propertyType": "string",
    "roomId": "string",
    "roomName": "string",
    "bookingNo": "string",
    "bookingAmount": 0,
    "unpaidAmount": 0,
    "receivableDate": "2019-08-24T14:15:22Z",
    "expectSignDate": "2019-08-24T14:15:22Z",
    "isRefundable": 0,
    "cancelTime": "2019-08-24T14:15:22Z",
    "cancelBy": "string",
    "cancelByName": "string",
    "cancelReason": 0,
    "isRefund": 0,
    "cancelEnclosure": "string",
    "cancelRemark": "string",
    "status": 0,
    "contractId": "string",
    "refundId": "string",
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "isDel": true,
    "contractNo": "string",
    "signDate": "2019-08-24T14:15:22Z",
    "contractLeaseUnit": "string",
    "lesseeName": "string",
    "receivedAmount": 0,
    "receivedDate": "2019-08-24T14:15:22Z",
    "payMethod": "string"
  },
  "cost": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "chargeType": 0,
    "bizId": "string",
    "refundId": "string",
    "refundCostId": "string",
    "bizNo": "string",
    "customerId": "string",
    "customerName": "string",
    "costType": 0,
    "period": 0,
    "subjectId": "string",
    "subjectName": "string",
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "receivableDate": "2019-08-24T14:15:22Z",
    "status": 0,
    "canPay": true,
    "isRevenueBill": true,
    "isRevenueGenerated": true,
    "isFromContract": true,
    "totalAmount": 0,
    "discountAmount": 0,
    "actualReceivable": 0,
    "receivedAmount": 0,
    "carryoverAmount": 0,
    "confirmStatus": 0,
    "createByName": "string",
    "updateByName": "string",
    "isDel": true,
    "contractType": 0,
    "roomName": "string",
    "contractStatus": 0,
    "contractStatusTwo": 0
  },
  "flowRelList": [
    {
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "pendingAmount": 0,
      "acctAmount": 0,
      "remark": "string",
      "createByName": "string",
      "createTime": "string",
      "updateByName": "string",
      "isDel": true,
      "projectId": "string",
      "projectName": "string",
      "entryTime": "2019-08-24T14:15:22Z",
      "payType": 0,
      "payMethod": "string",
      "orderNo": "string",
      "usedAmount": 0,
      "payerName": "string",
      "target": "string",
      "merchant": "string",
      "cumulativeAcctAmount": 0
    }
  ],
  "flowLogList": [
    {
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "carryoverId": "string",
      "carryoverNo": "string",
      "amount": 0,
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|booking|[BookingVo](#schemabookingvo)|false|none||定单基本信息|
|cost|[CostVo](#schemacostvo)|false|none||账单信息|
|flowRelList|[[CostFlowRelVo](#schemacostflowrelvo)]|false|none|账单流水关系列表|账单流水关系列表|
|flowLogList|[[CostFlowLogVo](#schemacostflowlogvo)]|false|none|账单流水记录列表|账单流水记录列表|

<h2 id="tocS_BookingPrintDto">BookingPrintDto</h2>

<a id="schemabookingprintdto"></a>
<a id="schema_BookingPrintDto"></a>
<a id="tocSbookingprintdto"></a>
<a id="tocsbookingprintdto"></a>

```json
{
  "id": "string",
  "type": 0,
  "bookingData": {
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "id": "string",
    "projectId": "string",
    "customerName": "string",
    "propertyType": "string",
    "roomId": "string",
    "roomName": "string",
    "bookingNo": "string",
    "bookingAmount": 0,
    "receivableDate": "2019-08-24T14:15:22Z",
    "expectSignDate": "2019-08-24T14:15:22Z",
    "isRefundable": 0,
    "cancelTime": "2019-08-24T14:15:22Z",
    "cancelBy": "string",
    "cancelByName": "string",
    "cancelReason": 0,
    "isRefund": 0,
    "cancelEnclosure": "string",
    "cancelRemark": "string",
    "status": 0,
    "contractId": "string",
    "refundId": "string",
    "isDel": true,
    "isSubmit": 0
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|定单ID|定单ID|
|type|integer(int32)|true|none|套打类型|套打类型：10-定单, 100-退订单|
|bookingData|[BookingAddDTO](#schemabookingadddto)|false|none||定单保存所有字段，由于在保存前可以调用，因此需要传所有字段|

<h2 id="tocS_EnclosureInfo">EnclosureInfo</h2>

<a id="schemaenclosureinfo"></a>
<a id="schema_EnclosureInfo"></a>
<a id="tocSenclosureinfo"></a>
<a id="tocsenclosureinfo"></a>

```json
{
  "fileName": "string",
  "fileUrl": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|fileName|string|false|none||none|
|fileUrl|string|false|none||none|

