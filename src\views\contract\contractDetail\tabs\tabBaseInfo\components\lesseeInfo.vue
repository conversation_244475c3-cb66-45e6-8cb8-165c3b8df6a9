<template>
    <section>
        <sectionTitle title="承租人信息"></sectionTitle>
        <div class="content">
            <a-grid :cols="1" :col-gap="16" :row-gap="0">
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">承租类型</label>
                        <div class="detail-text">{{ contractData.customer.customerType === 1 ? '个人' : '企业' }}</div>
                    </div>
                    <template v-if="contractData.customer.customerType === 2">
                        <div class="form-item">
                            <label class="form-label">承租人</label>
                            <div class="detail-text">{{ contractData.customer.customerName || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">统一社会信用代码</label>
                            <div class="detail-text">{{ contractData.customer.creditCode || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">委托代理人</label>
                            <div class="detail-text">{{ contractData.customer.contactName || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">委托代理人手机号</label>
                            <div class="detail-text">{{ contractData.customer.contactPhone || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">委托代理人身份证</label>
                            <div class="detail-text">{{ contractData.customer.contactIdNumber || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">法人</label>
                            <div class="detail-text">{{ contractData.customer.legalName || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">法人手机号</label>
                            <div class="detail-text">{{ contractData.customer.phone || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">法人身份证</label>
                            <div class="detail-text">{{ contractData.customer.idNumber || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">付款银行名称</label>
                            <div class="detail-text">{{ contractData.customer.paymentBank || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">付款银行账号</label>
                            <div class="detail-text">{{ contractData.customer.paymentAccount || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">地址</label>
                            <div class="detail-text">{{ contractData.customer.address || '' }}</div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="form-item">
                            <label class="form-label">承租人</label>
                            <div class="detail-text">{{ contractData.customer.customerName || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">手机号</label>
                            <div class="detail-text">{{ contractData.customer.phone || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">证件类型</label>
                            <div class="detail-text">{{idTypeOptions.find(item => item.value ===
                                contractData.customer.idType)?.label || ''}}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">证件号码</label>
                            <div class="detail-text">{{ contractData.customer.idNumber || '' }}</div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">通讯地址</label>
                            <div class="detail-text">{{ contractData.customer.address || '' }}</div>
                        </div>
                    </template>
                </a-grid-item>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { useContractStore } from '@/store/modules/contract/index'
import { ContractAddDTO } from '@/api/contract';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

// 证件类型选项
const idTypeOptions = [
    { label: '身份证', value: '1' },
    { label: '护照', value: '2' },
    { label: '军官证', value: '3' },
    { label: '其他', value: '4' },
]
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.form-item {
    margin-bottom: 16px;

    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>