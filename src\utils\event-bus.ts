/**
 * 全局事件总线
 * 用于组件间通信
 */
import mitt, { Handler } from 'mitt';

const emitter = mitt();

// 事件类型定义
export interface ContractSupplementDeletedEvent {
    contractId: string;
    supplementType: string;
}

// 事件名称常量
export const EVENT_NAMES = {
    CONTRACT_SUPPLEMENT_DELETED: 'contract-supplement-deleted'
} as const;

// 导出事件总线实例
export { emitter };

// 便捷方法
export function emitContractSupplementDeleted(data: ContractSupplementDeletedEvent) {
    emitter.emit(EVENT_NAMES.CONTRACT_SUPPLEMENT_DELETED, data);
}

export function onContractSupplementDeleted(handler: Handler<ContractSupplementDeletedEvent>) {
    emitter.on(EVENT_NAMES.CONTRACT_SUPPLEMENT_DELETED, handler);
}

export function offContractSupplementDeleted(handler: Handler<ContractSupplementDeletedEvent>) {
    emitter.off(EVENT_NAMES.CONTRACT_SUPPLEMENT_DELETED, handler);
} 