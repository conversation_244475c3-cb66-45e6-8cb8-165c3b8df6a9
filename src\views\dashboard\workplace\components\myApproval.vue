<template>
    <div class="my-approval">
        <div class="approval-title">
            <div class="title-decorator"></div>
            我发起的审批
        </div>
        <div class="approval-cards">
            <div class="approval-card" v-for="item in cards" :key="item.status">
                <img class="approval-img" :src="item.img" :alt="item.status" />
                <div class="approval-status">
                    <div class="status-text">{{ item.status }}</div>
                    <div class="status-num">{{ item.num }}</div>
                </div>
                <button class="approval-btn">立即查看</button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import approval1 from '@/assets/images/dashboard/approval-1.png'
import approval2 from '@/assets/images/dashboard/approval-2.png'
import approval3 from '@/assets/images/dashboard/approval-3.png'

const cards = ref([
    {
        img: approval1,
        status: '退回',
        num: 4,
    },
    {
        img: approval2,
        status: '审批中',
        num: 1,
    },
    {
        img: approval3,
        status: '已审批',
        num: 10,
    },
])
</script>

<style lang="less" scoped>
.my-approval {
    background: #fff;
    border-radius: 10px;
    padding: 20px 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .approval-title {
        flex-shrink: 0;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
        color: #000;
        position: relative;
        display: flex;
        align-items: center;

        .title-decorator {
            width: 5px;
            height: 19px;
            background-color: #0071ff;
            border-radius: 4px 0px 4px 0px;
            margin-right: 10px;
        }
    }

    .approval-cards {
        flex: 1;
        display: flex;
        gap: 4px;
        justify-content: space-between;
    }

    .approval-card {
        background: linear-gradient(180deg, #fff 0%, #36a1f61a 100%);
        border-radius: 10px;
        flex: 1;
        // height: 147px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);

        .approval-img {
            width: 45px;
            height: 45px;
            margin-top: 18px;
            margin-bottom: 10px;
            object-fit: contain;
        }

        .approval-status {
            text-align: center;
            margin-bottom: 10px;

            .status-text {
                font-size: 14px;
                color: #3a4252;
                margin-bottom: 2px;
            }

            .status-num {
                font-size: 20px;
                font-weight: bold;
                color: #3a4252;
            }
        }

        .approval-btn {
            flex-shrink: 0;
            margin-top: auto;
            margin-bottom: 14px;
            width: 70px;
            height: 28px;
            background: transparent;
            color: #0071ff;
            border: 1px solid #0071ff;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
                background: #0071ff;
                color: #fff;
            }
        }
    }
}
</style>