<template>
    <a-drawer title="历史版本" class="common-drawer-small" v-model:visible="visible">
        <a-space direction="vertical" :size="16" fill>
            <a-card hoverable v-for="item in versionList" :key="item.id" :body-style="{ cursor: 'pointer' }" @click="handleClick(item)">
                <div class="current-version-card" v-if="item.isCurrent">
                    <icon-check class="current-version-icon" />
                </div>
                <a-descriptions :column="1" :data="versionList" :label-style="{ width: '120px', textAlign: 'right' }">
                    <a-descriptions-item label="版本号：">{{ item.version }} <a-tag
                            v-if="[20, 30].includes(item.status as number)" type="success">{{
                                getDictLabel('contract_status', item.status as number) }}</a-tag></a-descriptions-item>
                    <a-descriptions-item label="动作类型：">{{ getDictLabel('contract_operate_type', item.operateType as
                        number)
                    }}</a-descriptions-item>
                    <a-descriptions-item label="提交时间：">{{ item.createTime }}</a-descriptions-item>
                    <a-descriptions-item label="提交人：">{{ item.createByName }}</a-descriptions-item>
                    <a-descriptions-item label="审批状态：">
                        <a-typography-text :type="item.approveStatus === 2 ? 'success' : ''">{{
                            getDictLabel('contract_approve_status',
                                item.approveStatus as
                                number)
                            }}</a-typography-text>
                    </a-descriptions-item>
                </a-descriptions>
            </a-card>
        </a-space>
        <history-version-detail ref="historyVersionDetailRef" />
    </a-drawer>
</template>

<script setup lang="ts">
import { ContractVo, getContractVersionList } from '@/api/contract'
import { useContractStore } from '@/store/modules/contract'
import { getDictLabel } from '@/dict'
import { IconCheck } from '@arco-design/web-vue/es/icon'
import historyVersionDetail from './historyVersionDetail.vue'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractVo)

const visible = ref(false)

const open = () => {
    visible.value = true
    fetchVersionList()
}

const versionList = ref<ContractVo[]>([])
const fetchVersionList = async () => {
    const { data } = await getContractVersionList(contractData.value.id as string)
    versionList.value = data
}

const historyVersionDetailRef = ref()
const handleClick = (item: ContractVo) => {
    historyVersionDetailRef.value.open(item)
}

defineExpose({
    open
})
</script>

<style lang="less" scoped>
.current-version-card {
    position: relative;
    .current-version-icon {
        position: absolute;
        top: -12px;
        right: -12px;
        font-size: 18px;
        color: #fff;
        z-index: 22;
    }
    &::before {
        content: '';
        position: absolute;
        top: -16px;
        right: -16px;
        width: 0;
        height: 0;
        border-left: 40px solid transparent;
        border-top: 40px solid #1890ff;
        z-index: 1;
    }
}
</style>