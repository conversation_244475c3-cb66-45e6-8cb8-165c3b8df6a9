<template>
  <a-modal 
    v-model:visible="visible" 
    title="批量设置免租期" 
    :width="800"
    @ok="handleConfirm" 
    @cancel="handleCancel"
    :confirm-loading="loading"
  >
    <a-form ref="formRef" :model="formData" :rules="formRules" label-align="right" layout="horizontal"
      :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>
      
      <!-- 免租期设置 -->
      <a-row>
        <a-col :span="24">
          <a-form-item label="参考因素" field="freeRentType">
            <a-radio-group v-model="formData.freeRentType">
              <a-radio :value="1">不限</a-radio>
              <a-radio :value="2">租赁期限</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 不限时的免租期 -->
      <a-row v-if="formData.freeRentType === 1">
        <a-col :span="12">
          <a-form-item label="免租期" field="freeRentPeriod">
            <a-input-number v-model="formData.freeRentPeriod" placeholder="请输入" :min="0" style="width: 100%">
              <template #append>月</template>
            </a-input-number>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 租赁期限时的阶梯免租期 -->
      <div v-if="formData.freeRentType === 2">
        <a-row>
          <a-col :span="24">
            <a-form-item label="租赁期限N" required class="rental-period-form-item">
              <!-- 租赁期限阶梯内容区域 -->
              <div v-for="(item, index) in formData.freeRentSteps" :key="index" style="min-height: 52px;width: 100%;">
                <!-- 最后一条记录的特殊布局 -->
                <a-row v-if="index === formData.freeRentSteps.length - 1" style="align-items: flex-start;">
                  <a-col :span="10">
                    <a-input-number v-model="item.start" disabled style="width: 100%">
                      <template #append>年及以上</template>
                    </a-input-number>
                  </a-col>
                  <a-col :span="4" style="text-align: center;line-height: 32px;">
                    <span>免租期</span>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item
                      :field="`freeRentSteps.${index}.discount`"
                      :show-colon="false"
                      hide-label
                    >
                      <a-input-number v-model="item.discount" placeholder="请输入" :min="0" style="width: 100%">
                        <template #append>月</template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2">
                    <!-- 占位，保持对齐 -->
                  </a-col>
                </a-row>

                <!-- 其他记录的正常布局 -->
                <a-row v-else style="align-items: flex-start;">
                  <a-col :span="4">
                    <a-input-number v-model="item.start" disabled style="width: 100%" />
                  </a-col>
                  <a-col :span="2" style="text-align: center;line-height: 32px;">
                    <span>≤N<</span>
                  </a-col>
                  <a-col :span="4">
                    <a-form-item
                      :field="`freeRentSteps.${index}.end`"
                      :rules="[{ required: true, message: '请输入租赁期限', trigger: 'blur' }]"
                      :show-colon="false"
                      hide-label
                    >
                      <a-input-number v-model="item.end" placeholder="请输入" :min="getMinValue(index)"
                        style="width: 100%" @change="updateNextMinValue(index)">
                        <template #append>年</template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                  <a-col :span="4" style="text-align: center;">
                    <span>免租期</span>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item
                      :field="`freeRentSteps.${index}.discount`"
                      :rules="[{ required: true, message: '请输入免租期', trigger: 'blur' }]"
                      :show-colon="false"
                      hide-label
                    >
                      <a-input-number v-model="item.discount" placeholder="请输入" :min="0" style="width: 100%">
                        <template #append>月</template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2">
                    <IconDelete v-if="index > 0" @click="removeFreeRentStep(index)"
                      style="color: #ff4d4f; cursor: pointer; font-size: 16px;margin-left: 16px;" />
                  </a-col>
                </a-row>
              </div>

              <a-row>
                <a-col :span="24">
                  <a-button type="text" @click="addFreeRentStep" style="padding: 0; color: #1890ff;">
                    添加阶梯
                  </a-button>
                </a-col>
              </a-row>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconDelete } from '@arco-design/web-vue/es/icon'

// 事件定义
const emit = defineEmits(['success', 'cancel'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  freeRentType: 1, // 免租期参考因素(1不限 2租赁期限)
  freeRentPeriod: null as number | null, // 免租期(月)
  freeRentSteps: [
    { start: 0, end: 1, discount: 0 },
    { start: 1, end: null, discount: 0 }
  ] as Array<{ start: number | null, end: number | null, discount: number | null }>
})

// 表单验证规则
const formRules = {
  freeRentType: [
    { required: true, message: '请选择参考因素', trigger: 'change' }
  ],
  freeRentPeriod: [
    {
      required: true,
      message: '请输入免租期',
      trigger: 'blur',
      validator: (value: any, callback: any) => {
        if (formData.freeRentType === 1 && !value) {
          callback('请输入免租期')
        } else {
          callback()
        }
      }
    }
  ]
}

// 显示弹框
const show = (selectedCount: number) => {
  visible.value = true
  // 重置表单数据
  formData.freeRentType = 1
  formData.freeRentPeriod = null
  formData.freeRentSteps = [
    { start: 0, end: 1, discount: 0 },
    { start: 1, end: null, discount: 0 }
  ]
}

// 确认设置
const handleConfirm = async () => {
  try {
    const errors = await formRef.value?.validate()
    if (errors) return

    loading.value = true

    // 构建免租期数据
    const freeRentData = {
      freeRentType: formData.freeRentType,
      freeRentPeriod: formData.freeRentPeriod,
      freeRentSteps: formData.freeRentSteps,
      freeRentTerm: formData.freeRentType === 2 ? JSON.stringify(formData.freeRentSteps) : undefined
    }

    emit('success', freeRentData)
    visible.value = false
    Message.success('批量设置免租期成功')

  } catch (error) {
    console.error('设置失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  visible.value = false
  emit('cancel')
}

// 获取当前步骤的最小值
const getMinValue = (index: number) => {
  if (index === 0) {
    return 0
  }
  const previousStep = formData.freeRentSteps[index - 1]
  return previousStep.end || 0
}

// 更新下一个步骤的最小值
const updateNextMinValue = async (index: number) => {
  // 等待当前输入完成
  await nextTick()

  const currentStep = formData.freeRentSteps[index]
  if (currentStep.end !== null && currentStep.end !== undefined) {
    // 检查是否有下一条记录需要更新
    if (index + 1 < formData.freeRentSteps.length) {
      // 更新下一个步骤的最小值
      formData.freeRentSteps[index + 1].start = currentStep.end

      // 触发表单验证更新
      await nextTick()
      formRef.value?.validateField(`freeRentSteps.${index + 1}.start`)
    }
  }
}

// 添加免租期阶梯
const addFreeRentStep = () => {
  const lastIndex = formData.freeRentSteps.length - 1
  const previousStep = formData.freeRentSteps[lastIndex - 1]

  // 在最后一个之前插入新的阶梯
  const newStep = {
    start: previousStep?.end || null, // 前一条的end值作为新阶梯的start值
    end: null,
    discount: 0
  }

  formData.freeRentSteps.splice(lastIndex, 0, newStep)
}

// 删除免租期阶梯
const removeFreeRentStep = (index: number) => {
  if (index > 0 && index < formData.freeRentSteps.length - 1) {
    formData.freeRentSteps.splice(index, 1)
  }
}

// 监听免租期阶梯数据变化，自动更新起始值
watch(() => formData.freeRentSteps.map(step => step.end), async (newEndValues, oldEndValues) => {
  // 检查哪个步骤的结束值发生了变化
  for (let i = 0; i < newEndValues.length - 1; i++) {
    if (newEndValues[i] !== oldEndValues?.[i] && newEndValues[i] !== null && newEndValues[i] !== undefined) {
      // 更新下一个步骤的起始值，包括最后一条记录
      if (i + 1 < formData.freeRentSteps.length) {
        formData.freeRentSteps[i + 1].start = newEndValues[i]

        // 等待 DOM 更新后触发验证
        await nextTick()
        formRef.value?.validateField(`freeRentSteps.${i + 1}.start`)
      }
    }
  }
}, { deep: true })

// 暴露方法
defineExpose({
  show
})
</script>

<style scoped lang="less">
:deep(.arco-form-item-label) {
  font-weight: 500;
}

// 修复租赁期限阶梯的布局问题
:deep(.rental-period-form-item) {
  .arco-form-item-content {
    flex-direction: column !important;
    align-items: flex-start !important;
  }

  .arco-form-item-content-wrapper {
    width: 100%;
  }
  .arco-form-item{
    margin-bottom: 0;
  }
}
</style>
