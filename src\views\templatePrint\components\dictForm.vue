<template>
    <a-drawer :visible="drawerVisible" :title="drawerTitle" :mask-closable="true" @cancel="handleCancel" class="common-drawer-small">
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" @click="handleSubmit">确认</a-button>
            </a-space>
        </template>
        <a-form 
            ref="formRef"
            :model="formData"
            :rules="rules"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
        >
            <section-title title="字典信息" style="margin-bottom: 16px;" />
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="name"
                        label="字段名称"
                        required
                        :rules="[{ required: true, message: '请输入字段名称' }]"
                    >
                        <a-input
                            v-model="formData.name"
                            placeholder="请输入字段名称"
                            allow-clear
                        />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="type"
                        label="字典类型"
                        required
                        :rules="[{ required: true, message: '请选择字典类型' }]"
                    >
                        <a-select v-model="formData.type" placeholder="请选择字典类型">
                            <a-option :value="0">字段</a-option>
                            <a-option :value="1">表格</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="code"
                        label="模板对应标识"
                        required
                        :rules="[{ required: true, message: '请输入模板对应标识' }]"
                    >
                        <a-input
                            v-model="formData.code"
                            placeholder="请输入模板对应标识"
                            allow-clear
                        />
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="remark"
                        label="字典解释说明"
                    >
                        <a-textarea
                            v-model="formData.remark"
                            placeholder="请输入字典解释说明"
                            :auto-size="{ minRows: 3, maxRows: 5 }"
                        />
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="dbField"
                        label="数据库字段"
                        required
                        :rules="[{ required: true, message: '请输入数据库字段' }]"
                    >
                        <a-input
                            v-model="formData.dbField"
                            placeholder="请输入数据库字段"
                            allow-clear
                        />
                    </a-form-item>
                </a-col>
            </a-row> -->

            <section-title title="SQL语句" style="margin-bottom: 16px;" />


            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="sqlText"
                        label="字典查询SQL"
                        required
                        :rules="[{ required: true, message: '请输入字典查询SQL' }]"
                    >
                        <a-textarea
                            v-model="formData.sqlText"
                            placeholder="请输入字典查询SQL"
                            :auto-size="{ minRows: 5, maxRows: 15 }"
                        />
                    </a-form-item>
                </a-col>
            </a-row>
<!-- 
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="attachment"
                        label="附件"
                    >
                        <upload-file
                            v-model="uploadedFiles"
                            :accept="'.pdf,.doc,.docx,.xls,.xlsx'"
                            :maxSize="10"
                            :limit="3"
                            :uploadApi="uploadDictFile"
                            @success="handleUploadSuccess"
                        />
                    </a-form-item>
                </a-col>
            </a-row> -->


        </a-form>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import uploadFile from '@/components/upload/uploadFile.vue'
import { createTemplateDict, updateTemplateDict, getTemplateDictDetail, commonUpload } from '@/api/templatePrint'
import { DictData } from '@/views/templatePrint/types'

const emit = defineEmits(['success', 'cancel'])
const drawerVisible = ref(false)
const drawerTitle = ref('新增字段')
const formRef = ref()

// 表单数据
const formData = reactive<Partial<DictData>>({
    id: '',
    parentId: '',
    name: '',
    type: 0, // 默认字段类型
    code: '',
    dbField: '',
    sqlText: '',
    remark: ''
})

// 表单校验规则
const rules = {
    name: [{ required: true, message: '请输入字段名称' }],
    type: [{ required: true, message: '请选择字典类型' }],
    code: [{ required: true, message: '请输入模板标识符' }],
    dbField: [{ required: true, message: '请输入数据库字段' }]
}

// 添加上传附件相关表单项
const uploadedFiles = ref<any[]>([])

// 文件上传API
const uploadDictFile = async (formData: FormData) => {
    try {
        const res = await commonUpload('dict', formData)
        return res
    } catch (error) {
        throw error
    }
}

// 上传成功处理
const handleUploadSuccess = (response: any, fileItem: any) => {
    // 可以在这里处理上传成功后的逻辑
    console.log('文件上传成功:', response)
}

// 获取字段详情
const getFieldDetail = async (id: string) => {
    try {
        const res = await getTemplateDictDetail(id)
        if (res.data && res.data.data) {
            return res.data.data
        }
        return null
    } catch (error) {
        console.error('获取字段详情失败:', error)
        Message.error('获取字段详情失败')
        return null
    }
}

// 初始化表单数据
const initFormData = async (record?: Partial<DictData>) => {
    if (record && record.id) {
        // 编辑模式，获取详细信息
        const detail = await getFieldDetail(record.id)
        if (detail) {
            Object.assign(formData, {
                id: detail.id,
                parentId: detail.parentId || '',
                name: detail.name,
                type: detail.type,
                code: detail.code,
                dbField: detail.dbField,
                sqlText: detail.sqlText || '',
                remark: detail.remark || ''
            })
            
            // 如果有附件信息，设置附件列表
            if (detail.attachments && Array.isArray(detail.attachments)) {
                uploadedFiles.value = detail.attachments.map((item: any) => ({
                    fileId: item.fileId || item.id,
                    name: item.name || item.fileName,
                    url: item.url || item.fileUrl
                }))
            } else {
                uploadedFiles.value = []
            }
        } else {
            // 如果获取详情失败，使用传入的记录
            Object.assign(formData, record)
            uploadedFiles.value = []
        }
    } else {
        // 新增时的默认值
        Object.assign(formData, {
            id: '',
            parentId: '',
            name: '',
            type: 0,
            code: '',
            dbField: '',
            sqlText: '',
            remark: ''
        })
        uploadedFiles.value = []
    }
    
    if (formRef.value) {
        formRef.value.clearValidate()
    }
}

// 取消
const handleCancel = () => {
    drawerVisible.value = false
    emit('cancel')
}

// 提交
const handleSubmit = async () => {
    try {
        // await formRef.value.validate()
        const error = await formRef.value.validate()
        if (error) {
            return
        }
        
        // 准备提交数据
        const submitData = {
            ...formData,
            attachments: uploadedFiles.value
        }
        
        if (formData.id) {
            // 编辑
            await updateTemplateDict(submitData)
            Message.success('更新成功')
        } else {
            // 新增
            await createTemplateDict(submitData)
            Message.success('新增成功')
        }
        
        emit('success')
        drawerVisible.value = false
    } catch (error) {
        console.error('提交失败:', error)
        Message.error('提交失败')
    }
}

// 暴露方法给父组件
defineExpose({
    open() {
        drawerTitle.value = '新增字段'
        drawerVisible.value = true
        initFormData()
    },
    async edit(record: Partial<DictData>) {
        drawerTitle.value = '编辑字段'
        drawerVisible.value = true
        await initFormData(record)
    }
})
</script>

<style scoped>
.common-drawer-small {
  width: 520px !important;
}
</style> 