<template>
    <div class="common-page page-layout-column">
        <div class="page-layout-column-left">
            <function-roles-list @change="handleRoleChange" />
        </div>
        <div class="page-layout-column-right">
            <div class="common-card">
                <a-tabs default-active-key="1" v-model:active-key="activeKey" @change="handleTabChange" height="100%">
                    <a-tab-pane v-permission="['system:menu:role:list']" key="1" title="功能角色权限设置" height="100%">
                        <function-role-set-component :role-id="currentRole?.roleId" ref="functionRoleSetComponentRef" v-if="currentRole?.roleId"/>
                        <div v-else>
                            <!-- nodata -->
                            <div class="common-flex common-flex-center common-no-data">
                                <a-empty description="请选择一个功能角色" />
                            </div>
                        </div>
                    </a-tab-pane>
                    <a-tab-pane v-permission="['system:menu:user:list']" key="2" title="功能角色用户列表" height="100%">
                        <function-role-user-component :role-id="currentRole?.roleId" ref="functionRoleUserComponentRef" v-if="currentRole?.roleId"/>
                        <div v-else>
                            <!-- nodata -->
                            <div class="common-flex common-flex-center common-no-data">
                                <a-empty description="请选择一个功能角色" />
                            </div>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { computed, ref, reactive, watch, nextTick } from 'vue'

import functionRolesList from '@/components/functionRolesList/index.vue'

import functionRoleUserComponent from './user.vue'
import functionRoleSetComponent from './set.vue'

const currentRole = ref<any>(null)
const activeKey = ref<string>('1')
const functionRoleUserComponentRef = ref<any>(null)
const functionRoleSetComponentRef = ref<any>(null)
const handleRoleChange = async (item: any) => {
    currentRole.value = item
    nextTick(() => {
        setComponentData()
    })
}
const handleTabChange = (key: string) => {
    activeKey.value = key
    nextTick(() => {
        setComponentData()
    })
}
const setComponentData = () => {
    if (!currentRole.value) return
    if (activeKey.value === '1') {
        functionRoleSetComponentRef.value?.fetchData?.()
    } else {
        functionRoleUserComponentRef.value?.fetchData?.()
    }
}
</script>
<script lang="ts">
export default { name: 'functionRoleRoleRouter' }
</script>

<style scoped lang="less">
.page-layout-column {
    width: 100%;
    // height: 100%;
    // box-sizing: border-box;
    // padding: 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .page-layout-column-left {
        width: 280px;
        height: 100%;
    }

    .page-layout-column-right {
        flex: 1;
        height: 100%;
        margin-left: 12px;
    }
}

.data-role-setting-item-group {
    padding: 12px;
    border-radius: 4px;
    background-color: rgb(var(--gray-1));

    .data-role-setting-item {
        display: flex;
        align-items: center;
        // justify-content: space-between;
        margin-right: 12px;

        .data-role-setting-item-title {
            font-size: 14px;
            font-weight: 600;
            color: rgb(var(--gray-9));
            margin-right: 12px;
        }

        .data-role-setting-item-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
}
.data-role-user-list {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 12px;
    // border-radius: 4px;
    // background-color: rgb(var(--gray-1));
}

:deep(.arco-table-th) {
    &:last-child {
        .arco-table-th-item-title {
            margin-left: 16px;
        }
    }
}

.action-icon {
    margin-left: 12px;
    cursor: pointer;
}

.active {
    color: #0960bd;
    background-color: #e3f4fc;
}

.setting {
    display: flex;
    align-items: center;
    width: 200px;

    .title {
        margin-left: 12px;
        cursor: pointer;
    }
}
</style>
