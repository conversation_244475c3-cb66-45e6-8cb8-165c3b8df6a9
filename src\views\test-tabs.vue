<template>
  <div class="test-tabs-container">
    <h1>Tab滚动功能测试页面</h1>
    <p>这个页面用于测试tab-bar的滚动功能。</p>
    
    <div class="test-buttons">
      <a-button @click="openMultipleTabs" type="primary">
        打开多个标签页测试滚动
      </a-button>
      <a-button @click="clearTabs" style="margin-left: 10px;">
        清除测试标签
      </a-button>
    </div>
    
    <div class="instructions">
      <h3>测试说明：</h3>
      <ul>
        <li>点击"打开多个标签页测试滚动"按钮会打开多个测试页面</li>
        <li>当标签数量超过容器宽度时，会显示左右滚动按钮</li>
        <li>点击左右箭头按钮可以滚动标签栏</li>
        <li>使用 Ctrl + 左/右箭头键也可以滚动</li>
        <li>切换标签时会自动滚动到当前活跃标签</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useTabBarStore } from '@/store'

const router = useRouter()
const tabBarStore = useTabBarStore()

const testRoutes = [
  { path: '/dashboard/workplace', name: 'Workplace', title: '工作台' },
  { path: '/system/user', name: 'User', title: '用户管理' },
  { path: '/system/role', name: 'Role', title: '角色管理' },
  { path: '/system/menu', name: 'Menu', title: '菜单管理' },
  { path: '/system/dept', name: 'Dept', title: '部门管理' },
  { path: '/system/post', name: 'Post', title: '岗位管理' },
  { path: '/system/dict', name: 'Dict', title: '字典管理' },
  { path: '/system/config', name: 'Config', title: '参数设置' },
  { path: '/system/notice', name: 'Notice', title: '通知公告' },
  { path: '/system/log', name: 'Log', title: '日志管理' }
]

const openMultipleTabs = () => {
  testRoutes.forEach((route, index) => {
    setTimeout(() => {
      // 模拟添加标签到tab-bar
      const routeInfo = {
        fullPath: route.path,
        name: route.name,
        title: route.title,
        meta: { title: route.title }
      }
      tabBarStore.updateTabList(routeInfo as any)
    }, index * 100) // 延迟添加，模拟真实场景
  })
}

const clearTabs = () => {
  // 重置标签栏，只保留首页
  tabBarStore.resetTabList()
}
</script>

<style scoped>
.test-tabs-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-buttons {
  margin: 20px 0;
}

.instructions {
  margin-top: 30px;
  padding: 20px;
  background-color: var(--color-fill-1);
  border-radius: 6px;
}

.instructions h3 {
  margin-top: 0;
  color: var(--color-text-1);
}

.instructions ul {
  margin: 10px 0;
  padding-left: 20px;
}

.instructions li {
  margin: 8px 0;
  color: var(--color-text-2);
}
</style> 