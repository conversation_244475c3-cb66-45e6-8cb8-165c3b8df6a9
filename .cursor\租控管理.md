# 租控管理
## 整体样式和交互
 1. 文件位于 /src/views/investmentManage/rentManage/index.vue 简图和详图分别位于components目录下
 2. 房态简图Figma地址 https://www.figma.com/design/UsjopKcNLqjz0KHIxS7sSg/%E4%B8%87%E6%B4%8B%E8%B5%84%E7%AE%A1%E5%B9%B3%E5%8F%B0?node-id=27-847&t=dgLkm3qSTmejKThp-0
 3. 房态详图Figma地址 https://www.figma.com/design/UsjopKcNLqjz0KHIxS7sSg/%E4%B8%87%E6%B4%8B%E8%B5%84%E7%AE%A1%E5%B9%B3%E5%8F%B0?node-id=27-156&t=dgLkm3qSTmejKThp-0
 4. 房态简图和房态详图的tab背景色渐变，点击文字变色
 5. 房态简图全部切换的按钮背景色蓝色，（空置，在租，待生效/签约中/已预订,不可招租）背景色统一 其中空置文字绿色 在租文字红色 待生效/签约中/已预订 文字橙色 不可招租文字灰色 和（自用,未出场页面中间用分割线分割开），自用和未出场没有背景色 且三角形大小注意按照UI
 6. 房态简图选择区域整体有个灰色的背景色 第一个区域（选择项目，选择地块，选择楼栋，选择楼层）大一点的间隙 第二个区域用途（宿舍，商铺，广告位）用途的选择框用方框表示  间隙 查看远期房态的时间选择
 7.房态简图 - 楼层展示区域
       - 每一个楼层有一个背景色，楼层号竖直居中展示
       - 给部分房间右上角分别打上自用和未出场的标签，注意不同的标签颜色
       - 房源可以滚动鼠标放大缩小，当房间占满整个屏幕的时候，房间换行展示
       - 左侧和右侧背景色一致，不要用分割线
 8. 点击房间弹窗
     - 弹窗背景色白色，右上角有个状态标签，注意切角
     - 第一行房间号展示，元素包括图标背景楼栋号-房间号
     - 第二行展示合同租期: 2025-05-01 至 2027-05-01
     - 第三行 租客预定 和 租客签约按钮
   