import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import useAppStore from './modules/app';
import useUserStore from './modules/user';
import useTabBarStore from './modules/tab-bar';
import useProjectStore from './modules/project';
import useCenterStore from './modules/center';

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate)

export { useAppStore, useUserStore, useTabBarStore, useProjectStore, useCenterStore };
export default pinia;
