<template>
    <section>
        <sectionTitle title="租金信息"></sectionTitle>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-row justify="end" style="margin-bottom: 16px;" v-if="!isDetailMode">
                <a-space>
                    <a-button type="primary" @click="handleCalculateMoney" :loading="calculateLoading">计算金额</a-button>
                    <a-button type="primary" @click="handleQuickAdd">快速添加</a-button>
                    <a-button type="primary" @click="handleAddBill">添加账单</a-button>
                </a-space>
            </a-row>
            <a-table :scroll="{ x: 1800 }" :data="tableData" :pagination="false" :bordered="{ cell: true }" summary
                summary-text="合计" :loading="calculateLoading">
                <template #columns>
                    <a-table-column title="租期" data-index="rentDate" :width="240" align="center">
                        <template #cell="{ record }">
                            <span v-if="isDetailMode || isRentPeriodDisabled(record)">{{
                                dayjs(record.startDate).format('YYYY-MM-DD')
                                }} 至 {{
                                    dayjs(record.endDate).format('YYYY-MM-DD') }}</span>
                            <a-range-picker v-else v-model="record.rentDate" format="YYYY-MM-DD"
                                @change="handleRentDateChange(record, $event)" />
                        </template>
                    </a-table-column>
                    <a-table-column title="商户" data-index="customerName" :width="100" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="费项" data-index="subjectName" :width="100" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="应收日期" data-index="receivableDate" :width="140" align="center">
                        <template #cell="{ record }">
                            <span v-if="isDetailMode || isRentPeriodDisabled(record)">{{ record.receivableDate }}</span>
                            <a-date-picker v-else v-model="record.receivableDate" style="width: 100%"
                                placeholder="请选择日期" format="YYYY-MM-DD"
                                @change="handleReceivableDateChange(record, $event)" />
                        </template>
                    </a-table-column>
                    <a-table-column title="账单总额（元）" data-index="totalAmount" :width="110" align="right">
                        <template #cell="{ record }">
                            <span v-if="isDetailMode || isRentPeriodDisabled(record)">{{ formatAmount(record.totalAmount) }}</span>
                            <a-input-number v-else v-model="record.totalAmount" :min="0" :precision="2"
                                :formatter="formatter" :parser="parser"
                                @change="handleTotalAmountChange(record, $event)">
                                <template #suffix>元</template>
                            </a-input-number>
                        </template>
                    </a-table-column>
                    <a-table-column title="优惠金额（元）" data-index="discountAmount" :width="110" align="right">
                        <template #cell="{ record }">
                            <span v-if="isDetailMode || isRentPeriodDisabled(record)">{{ formatAmount(record.discountAmount) }}</span>
                            <a-input-number v-else v-model="record.discountAmount" :min="0" :max="record.totalAmount" :precision="2"
                                :formatter="formatter" :parser="parser"
                                @change="handleDiscountAmountChange(record, $event)">
                                <template #suffix>元</template>
                            </a-input-number>
                        </template>
                    </a-table-column>
                    <a-table-column title="账单应收金额（元）" data-index="actualReceivable" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单已收金额（元）" data-index="receivedAmount" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.receivedAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单剩余应收金额（元）" data-index="remainingAmount" :width="160" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable - record.receivedAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column v-if="!isDetailMode" title="操作" data-index="action" :width="100" align="center" fixed="right">
                        <template #cell="{ record, rowIndex }">
                            <a-button type="text" size="mini" @click="handleDelete(record)" :disabled="isRentPeriodDisabled(record)">删除</a-button>
                        </template>
                    </a-table-column>
                </template>
                <template #summary-cell="{ column }">
                    <template v-if="column.dataIndex === 'rentDate'">合计</template>
                    <template v-if="column.dataIndex === 'totalAmount'">{{ formatAmount(totalAmountSum) }}</template>
                    <template v-if="column.dataIndex === 'discountAmount'">{{ formatAmount(discountAmountSum) }}</template>
                    <template v-if="column.dataIndex === 'actualReceivable'">{{ formatAmount(actualReceivableSum) }}</template>
                    <template v-if="column.dataIndex === 'receivedAmount'">{{ formatAmount(receivedAmountSum) }}</template>
                    <template v-if="column.dataIndex === 'remainingAmount'">{{ formatAmount(remainingAmountSum) }}</template>
                </template>
            </a-table>
            
            <!-- 快速添加 -->
            <a-modal v-model:visible="quickAddModalVisible" title="快速添加" :on-before-ok="handleQuickAddBeforeOk"
                @ok="handleQuickAddOk" @cancel="handleQuickAddCancel">
                <a-form ref="quickAddFormRef" :model="quickAddForm" :rules="quickAddFormRules" auto-label-width>
                    <a-form-item label="租期" field="month">
                        <a-input-number v-model="quickAddForm.month" :min="1" placeholder="请输入每期账单几个月">
                            <template #suffix>月</template>
                        </a-input-number>
                    </a-form-item>
                </a-form>
            </a-modal>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import dayjs from 'dayjs';
import { FormInstance } from '@arco-design/web-vue';
import { Message } from '@arco-design/web-vue';
import { ContractCostDTO, contractCalculateMoney } from '@/api/contract';
import { nextTick } from 'vue';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const formatter = (value: string) => {
    const values = value.split('.');
    values[0] = values[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return values.join('.')
};

const parser = (value: string) => {
    return value.replace(/,/g, '')
};

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractCosts)
const contractData = computed(() => contractStore.contractData)
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || (editType.value === 'change' && !changeType.value.includes(3)))

// 判断租期是否应该被禁用（变更合同且租期结束日期小于变更执行日期）
const isRentPeriodDisabled = (record: any) => {
    // 只有在变更合同时才进行判断
    if (editType.value !== 'change') {
        return false
    }
    
    // 获取变更执行日期
    const changeDate = contractData.value.changeDate
    if (!changeDate) {
        return false
    }
    
    // 获取租期结束日期
    const endDate = record.endDate
    if (!endDate) {
        return false
    }
    
    // 方法1：检查是否在历史数据中
    const isInHistoricalData = contractStore.preChangeRentCosts.some(historicalRecord => 
        historicalRecord.period === record.period && 
        historicalRecord.startDate === record.startDate &&
        historicalRecord.endDate === record.endDate
    )
    
    // 方法2：基于日期判断
    const isBeforeChangeDate = dayjs(endDate).isBefore(dayjs(changeDate), 'day')
    
    // 如果在历史数据中，或者租期结束日期小于变更执行日期，则禁用编辑和删除
    return isInHistoricalData || isBeforeChangeDate
}

const tableData = ref<any[]>([])
const baseData = ref({
    customerId: '',
    customerName: '',
    subjectId: '',
    subjectName: '租金',
})

// 标识是否已经调用过计算金额接口（使用store中的状态）
const hasCalculatedMoney = computed({
    get: () => contractStore.hasCalculatedMoney,
    set: (value) => { contractStore.hasCalculatedMoney = value }
})
// 计算金额loading状态
const calculateLoading = ref(false)
// 防止无限循环的标识
const isUpdatingData = ref(false)

// 快速添加相关
const quickAddModalVisible = ref(false)
const quickAddForm = ref({
    month: undefined
})
const quickAddFormRules = {
    month: [{ required: true, message: '请输入每期账单几个月' }]
}
const quickAddFormRef = ref()

const handleQuickAdd = () => {
    quickAddModalVisible.value = true
    quickAddForm.value.month = undefined
}

const handleQuickAddBeforeOk = async () => {
    const errors = await quickAddFormRef.value?.validate()
    if (errors) return false
    return true
}

const handleQuickAddOk = () => {
    // 按照month将合同周期分割，每期的商户、收款用途取自baseData，costType = 2, period从1开始自增，直接操作store中的costs数据
    const monthsPerPeriod = quickAddForm.value.month!
    let contractStart = dayjs(contractData.value.startDate)
    const contractEnd = dayjs(contractData.value.endDate)

    // 如果是变更合同，租期开始日期不能小于变更执行日期
    if (editType.value === 'change' && contractData.value.changeDate) {
        const changeDate = dayjs(contractData.value.changeDate)
        if (contractStart.isBefore(changeDate, 'day')) {
            contractStart = changeDate
        }
    }

    if (!contractStart.isValid() || !contractEnd.isValid()) {
        Message.error('请先设置合同开始和结束日期')
        return
    }

    // 清除现有的租金账单（costType = 2）
    const existingCosts = contractCosts.value.costs?.filter(item => item.costType !== 2) || []

    // 生成新的租金账单
    const newRentCosts: any[] = []
    let currentStart = contractStart
    let period = 1

    while (currentStart.isBefore(contractEnd) || currentStart.isSame(contractEnd)) {
        // 根据rentTicketPeriod计算当前期次的结束日期
        let currentEnd: dayjs.Dayjs

        if (contractData.value.rentTicketPeriod === 2) {
            // 2-自然月：第一期结束日期是从开始月份算起指定月数后的那个月的最后一天
            // 例如：开始日期2025-01-04，周期3个月，结束日期就是2025-03-31
            currentEnd = currentStart.add(monthsPerPeriod - 1, 'month').endOf('month')

            // 确保不超过合同结束日期
            if (currentEnd.isAfter(contractEnd)) {
                currentEnd = contractEnd
            }
        } else {
            // 1-租赁月（默认）：按租赁周期计算，每期固定天数
            currentEnd = currentStart.add(monthsPerPeriod, 'month').subtract(1, 'day')

            // 如果计算出的结束日期超过合同结束日期，则以合同结束日期为准
            if (currentEnd.isAfter(contractEnd)) {
                currentEnd = contractEnd
            }
        }

        // 每期生成一条汇总的租金账单
        const rentCost = {
            costType: 2, // 租金
            startDate: currentStart.format('YYYY-MM-DD'),
            endDate: currentEnd.format('YYYY-MM-DD'),
            period: period,
            customerId: baseData.value.customerId || contractData.value.customer?.id,
            customerName: baseData.value.customerName || contractData.value.customer?.customerName,
            subjectId: baseData.value.subjectId,
            subjectName: baseData.value.subjectName,
            receivableDate: calculateReceivableDate(currentStart, currentEnd), // 计算应收日期
            totalAmount: 0, // 所有房间月总价之和 × 月数
            discountAmount: 0,
            actualReceivable: 0,
            receivedAmount: 0,
        }
        newRentCosts.push(rentCost)

        // 计算下一期的开始日期
        if (contractData.value.rentTicketPeriod === 2) {
            // 自然月：下一期从当前期结束日期的下一天开始
            currentStart = currentEnd.add(1, 'day')
        } else {
            // 租赁月：下一期从当前期结束日期的下一天开始
            currentStart = currentEnd.add(1, 'day')
        }

        period++

        // 防止无限循环
        if (period > 1000) {
            Message.error('生成期数过多，请检查合同日期和月数设置')
            return
        }
    }

    // 更新store中的costs数据
    contractCosts.value.costs = [...existingCosts, ...newRentCosts]
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
    // 重置计算金额状态，因为数据已经改变
    hasCalculatedMoney.value = false
    // 标记为用户自定义数据
    contractStore.hasUserCustomData = true
    quickAddModalVisible.value = false
}

const handleQuickAddCancel = () => {
    quickAddModalVisible.value = false
}

const handleAddBill = () => {
    const existingCosts = contractCosts.value.costs?.filter(item => item.costType === 2) || []
    const feeCosts = contractCosts.value.costs?.filter(item => item.costType !== 2) || []
    
    // 计算默认的开始日期：如果有现有数据，则从最后一期的结束日期开始，否则使用当前日期
    let defaultStartDate = dayjs().format('YYYY-MM-DD')
    let defaultEndDate = dayjs().add(1, 'month').format('YYYY-MM-DD')
    
    if (existingCosts.length > 0) {
        const lastCost = existingCosts[existingCosts.length - 1]
        if (lastCost.endDate) {
            defaultStartDate = dayjs(lastCost.endDate).add(1, 'day').format('YYYY-MM-DD')
            defaultEndDate = dayjs(defaultStartDate).add(1, 'month').subtract(1, 'day').format('YYYY-MM-DD')
        }
    }
    
    // 如果是变更合同，租期开始日期不能小于变更执行日期
    if (editType.value === 'change' && contractData.value.changeDate) {
        const changeDate = dayjs(contractData.value.changeDate)
        const startDate = dayjs(defaultStartDate)
        if (startDate.isBefore(changeDate, 'day')) {
            defaultStartDate = changeDate.format('YYYY-MM-DD')
            defaultEndDate = changeDate.add(1, 'month').subtract(1, 'day').format('YYYY-MM-DD')
        }
    }
    const rentCost: any = {
        costType: 2, // 租金
        startDate: defaultStartDate,
        endDate: defaultEndDate,
        period: existingCosts.length > 0 ? existingCosts[existingCosts.length - 1].period + 1 : 1,
        customerId: baseData.value.customerId || contractData.value.customer?.id,
        customerName: baseData.value.customerName || contractData.value.customer?.customerName,
        subjectId: baseData.value.subjectId,
        subjectName: baseData.value.subjectName,
        receivableDate: defaultStartDate, // 默认使用开始日期作为应收日期
        totalAmount: 0, // 所有房间月总价之和 × 月数
        discountAmount: 0,
        actualReceivable: 0,
        receivedAmount: 0,
    }
    existingCosts.push(rentCost)
    contractCosts.value.costs = [...feeCosts || [], ...existingCosts]
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
    // 重置计算金额状态，因为数据已经改变
    hasCalculatedMoney.value = false
    // 标记为用户自定义数据
    contractStore.hasUserCustomData = true
}

const handleDelete = (record: any) => {
    // 检查是否应该禁用删除操作
    if (isRentPeriodDisabled(record)) {
        Message.warning('租期结束日期小于变更执行日期的数据不能删除')
        return
    }
    
    contractCosts.value.costs = contractCosts.value.costs?.filter(item => item.costType !== 2 || (item.costType === 2 && item.period !== record.period))
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
    // 重置计算金额状态，因为数据已经改变
    hasCalculatedMoney.value = false
    // 删除操作也标记为用户自定义数据，因为改变了原有结构
    contractStore.hasUserCustomData = true
}

const handleRentDateChange = (record: any, value: any) => {
    setContractCosts(record)
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
    // 重置计算金额状态，因为数据已经改变
    hasCalculatedMoney.value = false
    // 标记为用户自定义数据
    contractStore.hasUserCustomData = true
}

const handleReceivableDateChange = (record: any, value: any) => {
    setContractCosts(record)
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
    // 重置计算金额状态，因为数据已经改变
    hasCalculatedMoney.value = false
    // 标记为用户自定义数据
    contractStore.hasUserCustomData = true
}

const handleTotalAmountChange = (record: any, value: any) => {
    // 如果账单总额小于优惠金额，自动调整优惠金额
    if (record.discountAmount > value) {
        Message.warning(`账单总额小于优惠金额，已自动调整优惠金额为${formatAmount(value)}元`)
        record.discountAmount = value
    }
    
    calcAmount(record)
    setContractCosts(record)
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
    // 重置计算金额状态，因为数据已经改变
    hasCalculatedMoney.value = false
    // 标记为用户自定义数据
    contractStore.hasUserCustomData = true
}

const handleDiscountAmountChange = (record: any, value: any) => {
    // 校验优惠金额不能大于账单总额
    if (value > record.totalAmount) {
        Message.error(`优惠金额不能大于账单总额（${formatAmount(record.totalAmount)}元）`)
        // 重置为账单总额
        record.discountAmount = record.totalAmount
        return
    }
    
    // 校验优惠金额不能为负数
    if (value < 0) {
        Message.error('优惠金额不能为负数')
        record.discountAmount = 0
        return
    }
    
    calcAmount(record)
    setContractCosts(record)
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
    // 重置计算金额状态，因为数据已经改变
    hasCalculatedMoney.value = false
    // 标记为用户自定义数据
    contractStore.hasUserCustomData = true
}

const setContractCosts = (record: any) => {
    if (isUpdatingData.value) {
        return // 防止无限循环
    }
    
    isUpdatingData.value = true
    
    try {
        const data = JSON.parse(JSON.stringify(record))
        data.startDate = data.rentDate[0]
        data.endDate = data.rentDate[1]
        delete data.rentDate
        
        // 重新计算应收金额
        data.actualReceivable = data.totalAmount - data.discountAmount
        
        // 获取同一个period的所有原始记录
        const samePeriodRecords = contractCosts.value.costs?.filter(item => 
            item.costType === 2 && item.period === data.period
        ) || []
        
        if (samePeriodRecords.length === 1) {
            // 如果只有一条记录，直接更新
            contractCosts.value.costs = contractCosts.value.costs?.map(item => {
                if (item.costType === 2 && item.period === data.period) {
                    return {
                        ...item,
                        ...data,
                        costType: 2,
                        period: data.period,
                        customerId: data.customerId || item.customerId,
                        customerName: data.customerName || item.customerName,
                        subjectId: data.subjectId || item.subjectId,
                        subjectName: data.subjectName || item.subjectName,
                    }
                }
                return item
            })
        } else if (samePeriodRecords.length > 1) {
            // 如果有多条记录，需要按比例分配
            const originalTotalAmount = samePeriodRecords.reduce((sum, item) => sum + (item.totalAmount || 0), 0)
            const originalDiscountAmount = samePeriodRecords.reduce((sum, item) => sum + (item.discountAmount || 0), 0)
            
            contractCosts.value.costs = contractCosts.value.costs?.map(item => {
                if (item.costType === 2 && item.period === data.period) {
                    // 按原始金额比例分配新的金额
                    const ratio = originalTotalAmount > 0 ? (item.totalAmount || 0) / originalTotalAmount : 1 / samePeriodRecords.length
                    const newTotalAmount = data.totalAmount * ratio
                    const newDiscountAmount = data.discountAmount * ratio
                    const newActualReceivable = newTotalAmount - newDiscountAmount
                    
                    return {
                        ...item,
                        startDate: data.startDate,
                        endDate: data.endDate,
                        receivableDate: data.receivableDate,
                        totalAmount: newTotalAmount,
                        discountAmount: newDiscountAmount,
                        actualReceivable: newActualReceivable,
                        customerId: data.customerId || item.customerId,
                        customerName: data.customerName || item.customerName,
                        subjectId: data.subjectId || item.subjectId,
                        subjectName: data.subjectName || item.subjectName,
                    }
                }
                return item
            })
        }
        
        // 如果是变更合同，也需要同步更新历史数据中对应的记录
        if (editType.value === 'change' && contractStore.preChangeRentCosts.length > 0) {
            const historicalSamePeriodRecords = contractStore.preChangeRentCosts.filter(item => 
                item.period === data.period
            )
            
            if (historicalSamePeriodRecords.length === 1) {
                contractStore.preChangeRentCosts = contractStore.preChangeRentCosts.map(item => {
                    if (item.period === data.period) {
                        return {
                            ...item,
                            ...data,
                            costType: 2,
                            period: data.period,
                            customerId: data.customerId || item.customerId,
                            customerName: data.customerName || item.customerName,
                            subjectId: data.subjectId || item.subjectId,
                            subjectName: data.subjectName || item.subjectName,
                        }
                    }
                    return item
                })
            } else if (historicalSamePeriodRecords.length > 1) {
                const originalTotalAmount = historicalSamePeriodRecords.reduce((sum, item) => sum + (item.totalAmount || 0), 0)
                
                contractStore.preChangeRentCosts = contractStore.preChangeRentCosts.map(item => {
                    if (item.period === data.period) {
                        const ratio = originalTotalAmount > 0 ? (item.totalAmount || 0) / originalTotalAmount : 1 / historicalSamePeriodRecords.length
                        const newTotalAmount = data.totalAmount * ratio
                        const newDiscountAmount = data.discountAmount * ratio
                        const newActualReceivable = newTotalAmount - newDiscountAmount
                        
                        return {
                            ...item,
                            startDate: data.startDate,
                            endDate: data.endDate,
                            receivableDate: data.receivableDate,
                            totalAmount: newTotalAmount,
                            discountAmount: newDiscountAmount,
                            actualReceivable: newActualReceivable,
                            customerId: data.customerId || item.customerId,
                            customerName: data.customerName || item.customerName,
                            subjectId: data.subjectId || item.subjectId,
                            subjectName: data.subjectName || item.subjectName,
                        }
                    }
                    return item
                })
            }
        }
        
        // 强制触发contractCosts的深度监听，确保信息总览能及时更新
        nextTick(() => {
            // 触发一个微小的变化来确保响应式更新
            const currentCosts = contractStore.contractCosts
            contractStore.contractCosts = { ...currentCosts }
        })
    } finally {
        // 使用 nextTick 确保数据更新完成后再重置标识
        nextTick(() => {
            isUpdatingData.value = false
        })
    }
}

const calcAmount = (record: any) => {
    // 1. 计算应收金额
    record.actualReceivable = record.totalAmount - record.discountAmount
    // 2. 计算剩余应收金额
    record.remainingAmount = record.actualReceivable - record.receivedAmount
}

const calculateReceivableDate = (startDate: dayjs.Dayjs, endDate: dayjs.Dayjs): string => {
    // 根据合同的rentReceivableType和rentReceivableDate计算应收日期
    const rentReceivableType = contractData.value.rentReceivableType
    const rentReceivableDate = contractData.value.rentReceivableDate

    if (!rentReceivableType || !rentReceivableDate) {
        // 如果没有设置，默认返回租期开始日期
        return startDate.format('YYYY-MM-DD')
    }

    const days = Number(rentReceivableDate)
    if (isNaN(days)) {
        // 如果rentReceivableDate不是数字，默认返回租期开始日期
        return startDate.format('YYYY-MM-DD')
    }

    let receivableDate: dayjs.Dayjs

    if (rentReceivableType === 1) {
        // 1-租期开始前：应收日期 = 租期开始日期 - 天数
        receivableDate = startDate.subtract(days, 'day')
    } else if (rentReceivableType === 2) {
        // 2-租期开始后：应收日期 = 租期开始日期 + 天数
        receivableDate = startDate.add(days, 'day')
    } else {
        // 其他情况默认返回租期开始日期
        receivableDate = startDate
    }

    return receivableDate.format('YYYY-MM-DD')
}

/**
 * 监听应收计划计算结果 - 非标准合同处理
 * 简化逻辑：直接处理contractCosts中的租金数据，确保历史数据正确保留
 */
watch(contractCosts, (newVal) => {
    if (isUpdatingData.value) {
        return;
    }
    
    // 获取所有租金数据，过滤掉没有日期的无效数据
    let rentCosts = (newVal.costs || [])
        .filter(item => Number(item.costType) === 2)
        .filter(item => item.startDate && item.endDate); // 只保留有完整日期的数据
        
    // 对于变更合同，确保历史数据被正确包含
    if (editType.value === 'change' && contractStore.preChangeRentCosts.length > 0 && contractData.value.changeDate) {
        const changeDate = contractData.value.changeDate;
        
        // 获取历史数据中变更执行日期之前的数据
        const validHistoricalData = contractStore.preChangeRentCosts.filter(h => 
            h.startDate && h.endDate && new Date(h.endDate) < new Date(changeDate)
        );
        
        // 获取当前rentCosts中的period
        const currentPeriods = new Set(rentCosts.map(item => item.period));
        
        // 只保留period不重复的历史数据
        const nonDuplicateHistoricalData = validHistoricalData.filter(h => !currentPeriods.has(h.period));
        
        
        // 合并历史数据和新数据，历史数据在前
        rentCosts = [...nonDuplicateHistoricalData, ...rentCosts];
        
    }
    
    // 修复：确保所有costs中的租金数据都被包含
    // 检查是否有遗漏的租金数据
    const allRentCostsInStore = (newVal.costs || []).filter(item => Number(item.costType) === 2);
    const includedPeriods = new Set(rentCosts.map(item => item.period));
    const missingRentCosts = allRentCostsInStore.filter(item => !includedPeriods.has(item.period));
    
    if (missingRentCosts.length > 0) {
        rentCosts = [...rentCosts, ...missingRentCosts];
    }
    
    // 按period分组合并数据（处理同一期数的多条数据）
    const rentCostsGroup = rentCosts.reduce((acc: any, item: any) => {
        if (!acc[item.period]) {
            acc[item.period] = [];
        }
        acc[item.period].push(item);
        return acc;
    }, {} as Record<number, any[]>);

    // 转换为表格数据格式
    const newTableData = Object.values(rentCostsGroup).map((items: any) => {
        // 取第一条数据作为基础
        const baseItem = items[0];
        
        // 合并同期数据的金额
        const totalAmount = items.reduce((acc: any, item: any) => acc + (item.totalAmount || 0), 0);
        const discountAmount = items.reduce((acc: any, item: any) => acc + (item.discountAmount || 0), 0);
        const actualReceivable = items.reduce((acc: any, item: any) => acc + (item.actualReceivable || 0), 0);
        const receivedAmount = items.reduce((acc: any, item: any) => acc + (item.receivedAmount || 0), 0);
        
        // 确保endDate正确取值（取该period内最大的endDate）
        const endDate = items.reduce((maxDate: string, item: any) => {
            return dayjs(item.endDate).isAfter(dayjs(maxDate)) ? item.endDate : maxDate;
        }, baseItem.endDate);
        
        return {
            startDate: baseItem.startDate,
            endDate, // 使用正确的endDate
            customerName: baseItem.customerName || '',
            subjectName: baseItem.subjectName || '',
            receivableDate: baseItem.receivableDate,
            totalAmount,
            discountAmount,
            actualReceivable,
            receivedAmount,
            period: baseItem.period,
            rentDate: [baseItem.startDate, endDate] // 更新rentDate
        };
    }).sort((a, b) => a.period - b.period); // 按期数排序
    
    tableData.value = newTableData;

}, { deep: true, immediate: true });

// 专门监听变更合同的历史数据变化
watch(() => contractStore.preChangeRentCosts, (newVal) => {
    if (editType.value === 'change') {
        // 触发contractCosts的watch重新计算
        const currentCosts = contractCosts.value;
        if (currentCosts && currentCosts.costs) {
            // 不直接操作tableData，而是让contractCosts的watch来处理
            // 这里只是为了触发重新计算
            nextTick(() => {
            
            });
        }
    }
}, { deep: true, immediate: true });

// 监听tableData变化（调试用）
// watch(tableData, (newVal) => {

// }, { deep: true })

// 计算合计
const totalAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.totalAmount || 0), 0))
const discountAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.discountAmount || 0), 0))
const actualReceivableSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.actualReceivable || 0), 0))
const receivedAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.receivedAmount || 0), 0))
const remainingAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + ((item.actualReceivable || 0) - (item.receivedAmount || 0)), 0))

// 校验数据完整性
const validateDataIntegrity = () => {
    try {
    
        
        // 1. 检查是否有租金数据
        if (!contractCosts.value) {
            Message.error('合同成本信息不存在，请重新生成应收计划')
            return false
        }
        
        if (!contractCosts.value.costs) {
            Message.error('租金信息不能为空，请添加租金账单')
            return false
        }
        

        
        const rentCosts = contractCosts.value.costs.filter(item => {
            try {
                return Number(item.costType) === 2
            } catch (e) {
                return false
            }
        }) || []
        

        
        if (rentCosts.length === 0) {
            Message.error('租金信息不能为空，请添加租金账单')
            return false
        }
        
        // 2. 检查表格显示数据是否为空

        if (!Array.isArray(tableData.value) || tableData.value.length === 0) {
            Message.error('租金信息表格数据为空，请重新生成或添加租金账单')
            return false
        }
        

        return true
    } catch (error: any) {
        Message.error(`数据校验异常: ${error.message}`)
        return false
    }
    
}

// 校验字段完整性
const validateFieldIntegrity = () => {
    try {
        // 检查每条数据的完整性
        for (let i = 0; i < tableData.value.length; i++) {
            const item = tableData.value[i]
            
            if (!item || typeof item !== 'object') {
                Message.error(`第${i + 1}期租金账单数据格式错误`)
                return false
            }
            
            // 检查租期
            if (!item.startDate || !item.endDate) {
                Message.error(`第${item.period || (i + 1)}期租金账单的租期不能为空`)
                return false
            }
            
            // 检查应收日期
            if (!item.receivableDate) {
                Message.error(`第${item.period || (i + 1)}期租金账单的应收日期不能为空`)
                return false
            }
            
            // 检查金额字段
            if (item.totalAmount === undefined || item.totalAmount === null || item.totalAmount === '') {
                Message.error(`第${item.period || (i + 1)}期租金账单的总额不能为空`)
                return false
            }
            
            if (item.discountAmount === undefined || item.discountAmount === null || item.discountAmount === '') {
                Message.error(`第${item.period || (i + 1)}期租金账单的优惠金额不能为空`)
                return false
            }
            
            if (item.actualReceivable === undefined || item.actualReceivable === null || item.actualReceivable === '') {
                Message.error(`第${item.period || (i + 1)}期租金账单的应收金额不能为空`)
                return false
            }
            
            // 检查金额的合理性
            try {
                if (Number(item.totalAmount) < 0) {
                    Message.error(`第${item.period || (i + 1)}期租金账单的总额不能为负数`)
                    return false
                }
                
                // 校验账单总额不能为0
                if (Number(item.totalAmount) === 0) {
                    Message.error(`第${item.period || (i + 1)}期租金账单的总额不能为0`)
                    return false
                }
                
                if (Number(item.discountAmount) < 0) {
                    Message.error(`第${item.period || (i + 1)}期租金账单的优惠金额不能为负数`)
                    return false
                }
                
                if (Number(item.actualReceivable) < 0) {
                    Message.error(`第${item.period || (i + 1)}期租金账单的应收金额不能为负数`)
                    return false
                }
                
                // 校验优惠金额不能大于账单总额
                if (Number(item.discountAmount) > Number(item.totalAmount)) {
                    Message.error(`第${item.period || (i + 1)}期租金账单的优惠金额不能大于账单总额`)
                    return false
                }
            } catch (numError: any) {
                Message.error(`第${item.period || (i + 1)}期租金账单的金额数据格式错误`)
                return false
            }
        }
        
        return true
    } catch (error: any) {
        Message.error(`字段校验异常: ${error.message}`)
        return false
    }
}

// 校验是否已经调用过计算金额接口
const validateCalculateMoney = () => {
    try {
        // 移除对用户自定义数据的校验，不再强制要求点击计算金额
        // 只要有基本的租金数据就可以保存
        return true
    } catch (error) {
        console.error('计算金额校验异常:', error)
        Message.error('计算金额校验异常')
        return false
    }
}

// 综合校验方法
const validate = () => {
    try {
        // 1. 先校验基础数据结构
        const dataIntegrityResult = validateDataIntegrity()
        if (!dataIntegrityResult) {
            return false
        }
        
        // 2. 校验字段完整性
        const fieldIntegrityResult = validateFieldIntegrity()
        if (!fieldIntegrityResult) {
            return false
        }
        
        // 3. 校验是否调用过计算金额接口
        const calculateMoneyResult = validateCalculateMoney()
        if (!calculateMoneyResult) {
            return false
        }
        
        return true
    } catch (error: any) {
        Message.error(`数据校验异常: ${error.message}`)
        return false
    }
}

// 暴露校验方法给父组件
defineExpose({
    validateCalculateMoney,
    validateDataIntegrity,
    validateFieldIntegrity,
    validate
})

/**
 * 计算金额
 */
const handleCalculateMoney = async () => {

    
    // 校验：如果没有租金数据，不能计算金额
    if (!tableData.value || tableData.value.length === 0) {
        Message.error('请先添加租金信息');
        return;
    }
    
    // 校验：检查是否有必要的数据
    const hasValidData = tableData.value.some(item => 
        item.startDate && item.endDate && item.customerName && item.subjectName
    );
    if (!hasValidData) {
        Message.error('请完善租金信息的基本数据');
        return;
    }
    
    calculateLoading.value = true;
    
    try {
        // 对于变更合同，在调用接口前保存当前的历史数据
        let historicalRentCosts: any[] = [];
        if (editType.value === 'change' && contractData.value.changeDate) {
            const changeDate = contractData.value.changeDate;

            
            // 从当前表格数据中获取变更执行日期之前的数据
            const currentRentCosts = contractStore.contractCosts.costs?.filter(item => Number(item.costType) === 2) || [];
            historicalRentCosts = currentRentCosts.filter(item => {
                if (!item.endDate) return false;
                return new Date(item.endDate) < new Date(changeDate);
            });
            

        }
        
        // 构建API请求参数
        const apiData = {
            ...contractData.value,
            costs: contractCosts.value.costs || [],
            bookings: contractCosts.value.bookings || []
        };
        

        
        // 调用计算金额接口
        const { data: result, code, msg } = await contractCalculateMoney(apiData);
        

        
        if (code === 200 && result) {
            // 保留原有的非租金数据
            const originalCosts = contractStore.contractCosts.costs || [];
            const originalBookings = contractStore.contractCosts.bookings || [];
            const nonRentCosts = originalCosts.filter(item => Number(item.costType) !== 2);
            
            // 处理接口返回的数据
            let newRentCosts: any[] = [];
            
            // 根据接口实际返回的数据结构处理
            if (Array.isArray(result)) {
                // 如果直接返回数组，过滤出租金数据
                newRentCosts = result.filter((item: any) => Number(item.costType) === 2);
            } else if (result && typeof result === 'object') {
                // 如果返回对象，尝试从 costs 字段获取
                if (Array.isArray(result.costs)) {
                    newRentCosts = result.costs.filter((item: any) => Number(item.costType) === 2);
                } else if (result.costs) {
                    // 如果 costs 不是数组，尝试其他处理
                    console.warn('接口返回的 costs 不是数组:', result.costs);
                    newRentCosts = [];
                }
            }
            

            
            // 对于变更合同，合并历史数据和新数据
            if (editType.value === 'change' && historicalRentCosts.length > 0) {

                
                // 获取历史数据的period集合
                const historicalPeriods = new Set(historicalRentCosts.map(cost => cost.period));

                
                // 过滤掉与历史数据period重复的新数据
                const filteredNewRentCosts = newRentCosts.filter(cost => !historicalPeriods.has(cost.period));

                
                // 合并历史数据和新数据，历史数据在前
                newRentCosts = [...historicalRentCosts, ...filteredNewRentCosts];
                

            }
            
            // 更新store中的数据
            contractStore.contractCosts = {
                costs: [...nonRentCosts, ...newRentCosts],
                bookings: originalBookings
            };
            

            
            // 重新执行定金结转逻辑
            contractStore.transferBookingAmount();
            
            // 标记已经调用过计算金额接口
            hasCalculatedMoney.value = true;
            // 重置用户自定义数据标识
            contractStore.hasUserCustomData = false;
            
            Message.success('计算金额成功');
        } else {
            Message.error(msg || '计算金额失败');
        }
    } catch (error) {
        console.error('计算金额失败:', error);
        Message.error('计算金额失败');
    } finally {
        calculateLoading.value = false;
    }
};
</script>

<style lang="less" scoped></style> 