<template>
    <div class="receivable-plan">
        <a-card :bordered="false" :body-style="{ padding: '0 16px 16px' }">
            <infoOverview />
            <earnestInfo v-if="contractCosts.bookings && contractCosts.bookings.length > 0" />
            <depositInfo />
            <rentInfo />
            <otherFee />
        </a-card>
    </div>
</template>

<script setup>
import infoOverview from './components/infoOverview.vue';
import earnestInfo from './components/earnestInfo.vue';
import depositInfo from './components/depositInfo.vue';
import rentInfo from './components/rentInfo.vue';
import otherFee from './components/otherFee.vue';
import { useContractStore } from '@/store/modules/contract'

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractDetailCosts)
</script>

<style lang="less" scoped></style>