<template>
    <div class="container">
        <div class="content">
            <!-- 顶部状态切换 -->
            <a-tabs v-model:activeKey="activeType" size="large" hide-content @change="handleChangeType"
                style="margin-bottom: 16px;">
                <a-tab-pane v-for="item in typeOptions" :key="item.value" :title="item.label" />
            </a-tabs>
            <a-card class="general-card">
                <!-- 搜索区域 -->
                <a-row>
                    <a-col :flex="1">
                        <a-form :model="formModel" auto-label-width label-align="right">
                            <a-row :gutter="16">
                                <a-col :span="8">
                                    <a-form-item field="projectId" label="项目">
                                        <ProjectTreeSelect v-model="formModel.projectId"
                                            @change="handleProjectChange" />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="tenantName" label="承租方">
                                        <a-input v-model="formModel.tenantName" placeholder="请输入承租方名称" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="buildingOrHouseName" label="楼栋/房源">
                                        <a-input v-model="formModel.buildingOrHouseName" placeholder="请输入楼栋或房源名称"
                                            allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="leaseStartDateRange" label="租期起始日期">
                                        <a-range-picker v-model="formModel.leaseStartDateRange" style="width: 100%" />
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </a-form>
                    </a-col>
                    <a-divider style="height: 84px" direction="vertical" />
                    <a-col :flex="'86px'" style="text-align: right">
                        <a-space direction="vertical" :size="18">
                            <a-button type="primary" @click="search">
                                <template #icon>
                                    <icon-search />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="reset">
                                <template #icon>
                                    <icon-refresh />
                                </template>
                                重置
                            </a-button>
                        </a-space>
                    </a-col>
                </a-row>
                <a-divider style="margin: 0 0 16px 0;" />
                <!-- 表格区域 -->
                <a-row style="margin-bottom: 12px" v-if="activeType === '0'">
                    <a-col :span="24" style="text-align: right">
                        <a-button v-permission="['rent:enter:batch']" type="primary" @click="openBatchAutoEntryModal">
                            批量自动进场
                        </a-button>
                    </a-col>
                </a-row>
                <a-table row-key="contractId" :loading="loading" :pagination="pagination" :columns="columns"
                    :data="tableData" :scroll="{ x: 1600 }" :bordered="{ cell: true }" @page-change="onPageChange"
                    @page-size-change="onPageSizeChange" :row-selection="rowSelection"
                    @selection-change="handleSelectionChange">
                    <template #index="{ rowIndex }">
                        {{
                            rowIndex +
                            1 +
                            (pagination.current - 1) * pagination.pageSize
                        }}
                    </template>

                    <template #operations="{ record }">
                        <a-space>
                            <a-button v-permission="['rent:enter:handle']" v-if="activeType === '0'" type="text" size="mini" @click="handleEntry(record)">
                                办理进场 </a-button>
                            <a-button v-permission="['rent:enter:detail']" v-if="activeType === '1'" type="text" size="mini" @click="handleDetail(record)">
                                详情 </a-button>
                            <a-button v-permission="['rent:enter:change']" v-if="activeType === '1'" type="text" size="mini" @click="handleAdjust(record)">
                                调整 </a-button>
                            <a-button v-permission="['rent:enter:notice']" v-if="activeType === '1'" type="text" size="mini" @click="handleNotify(record)">
                                通知客户 </a-button>
                        </a-space>
                    </template>
                </a-table>
            </a-card>
        </div>

        <!-- 办理进场抽屉 -->
        <a-drawer v-model:visible="entryDrawerVisible" title="办理进场" width="1200px" :footer="editType === 'detail' ? false : true">
            <entry-process ref="entryProcessRef" :editType="editType" v-if="entryDrawerVisible"
                :data="currentEntryData || {}" />
            <template #footer>
                <a-space>
                    <a-button @click="handleEntryCancel">取消</a-button>
                    <a-button type="primary" @click="handleEntrySave">确定</a-button>
                </a-space>
            </template>
        </a-drawer>

        <!-- 选择要进场房源模态框 -->
        <a-modal v-model:visible="selectHousesModalVisible" title="选择要进场房源" width="900px" :footer="false"
            @cancel="handleSelectHousesCancel">
            <div class="select-houses-content">
                <select-entry-houses v-if="selectHousesModalVisible" :contractId="currentRecord?.contractId"
                    @select="handleHousesSelected" ref="selectHousesRef" />
                <div class="modal-footer">
                    <a-space>
                        <a-button @click="handleSelectHousesCancel">取消</a-button>
                        <a-button type="primary" @click="handleSelectHousesNext">下一步</a-button>
                    </a-space>
                </div>
            </div>
        </a-modal>

        <!-- 批量自动进场 -->
        <a-modal v-model:visible="batchModalVisible" title="批量自动进场" width="400px">
            <a-form ref="batchEnterFormRef" :model="batchEnterData" auto-label-width label-align="right"
                :rules="batchEnterRules">
                <a-form-item label="进场时间" field="enterDate">
                    <a-date-picker v-model="batchEnterData.enterDate" style="width: 100%;" placeholder="请选择进场日期" />
                </a-form-item>
                <a-checkbox v-model="batchEnterData.isNotify">给承租方发送进场通知单</a-checkbox>
                <a-alert style="margin-top: 16px;" type="normal">
                    <a-typography-text type="secondary"
                        style="font-style: italic;">进场提示：若选中发送，系统自动将进场通知单链接地址以短信方式发送给承租方，承租方将看到进场房源的进场日期及房间配套情况</a-typography-text>
                </a-alert>
            </a-form>
            <template #footer>
                <a-space>
                    <a-button @click="batchModalVisible = false">取消</a-button>
                    <a-button type="primary" @click="handleBatchAutoEntry">确认</a-button>
                </a-space>
            </template>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getEnterList, batchAutoEnter, saveEnter, getUnenteredRooms, initEnter, notifyCustomer, getEnterDetailWithRooms } from '@/api/entry'
import sectionTitle from '@/components/sectionTitle/index.vue'
import EntryDetail from './components/entryDetail.vue'
import EntryProcess from './components/entryProcess.vue'
import SelectEntryHouses from './components/selectEntryHouses.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import { getDictLabel } from '@/dict'
import dayjs from 'dayjs'

// 类型定义
interface TableItem {
    id?: string
    projectId?: string
    contractId?: string
    contractNo?: string
    contractPurpose?: number
    tenantName?: string
    roomName?: string
    rentStartDate?: string
    rentEndDate?: string
    unenterNum?: number
    projectName?: string
    createTime?: string
    checked?: boolean
    [key: string]: any
}

// 表格数据
const loading = ref(false)
const tableData = ref<TableItem[]>([])
const currentProject = ref<any>({})
const selectionRowKeys = ref<string[]>([])
const rowSelection = computed(() => {
    if (activeType.value === '0') {
        return { type: 'checkbox', showCheckedAll: true }
    } else {
        return null
    }
})


const activeType = ref('0')
const columns = computed(() => {
    return [
        {
            title: '序号',
            dataIndex: 'index',
            slotName: 'index',
            width: 60,
            align: 'center',
        },
        {
            title: '合同编号',
            dataIndex: 'contractNo',
            width: 160,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '合同用途',
            dataIndex: 'contractPurpose',
            width: 80,
            align: 'center',
            render: ({ record }: { record: any }) => {
                return getDictLabel('diversification_purpose', record.contractPurpose?.toString())
            }
        },
        {
            title: '承租方',
            dataIndex: 'tenantName',
            width: 140,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '租期',
            dataIndex: 'rentStartDate',
            width: 180,
            align: 'center',
            ellipsis: true,
            tooltip: true,
            render: ({ record }: { record: any }) => {
                return record.rentStartDate && record.rentEndDate
                    ? `${record.rentStartDate} 至 ${record.rentEndDate}`
                    : ''
            }
        },
        {
            title: activeType.value === '0' ? '未进场房源' : '已进场房源',
            dataIndex: 'roomName',
            width: 220,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: activeType.value === '0' ? '未进场房源数' : '已进场房源数',
            dataIndex: activeType.value === '0' ? 'unenterNum' : 'enteredRoomCount',
            width: 80,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '操作',
            slotName: 'operations',
            width: activeType.value === '0' ? 80 : 140,
            fixed: 'right',
            align: 'center'
        }
    ]
})
const typeOptions = [
    {
        value: '0',
        label: '待办理'
    },
    {
        value: '1',
        label: '已办理'
    }
]
// 分页
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
    showTotal: true,
    showPageSize: true
})

// 表单
const formModel = reactive({
    projectId: undefined as string | undefined,
    tenantName: '',
    buildingOrHouseName: '',
    leaseStartDateRange: [] as any[]
})

// 办理进场抽屉
const entryDrawerVisible = ref(false)
const currentEntryData = ref<TableItem | null>(null)

// 选择要进场房源模态框
const selectHousesModalVisible = ref(false)
const selectHousesRef = ref()
const currentRecord = ref<TableItem | null>(null)
const selectedHouses = ref<any[]>([])

// 切换状态
const handleChangeType = (key: string) => {
    activeType.value = key
    fetchTableData()
}

// 项目变化处理
const handleProjectChange = (value: string, selectedOrg: any) => {
    formModel.projectId = value
    currentProject.value = selectedOrg
    search()
}

const editType = ref('')

// 获取表格数据
const fetchTableData = async () => {
    loading.value = true
    try {
        const params = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            projectId: formModel.projectId,
            tenantName: formModel.tenantName,
            roomName: formModel.buildingOrHouseName,
            rentStartDateBegin: formModel.leaseStartDateRange[0] ? formModel.leaseStartDateRange[0] : null,
            rentStartDateEnd: formModel.leaseStartDateRange[1] ? formModel.leaseStartDateRange[1] : null,
            type: activeType.value
        }

        const res = await getEnterList(params)
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        console.error('获取进场管理列表失败', error)
    } finally {
        loading.value = false
    }
}

// 查询
const search = () => {
    pagination.current = 1
    fetchTableData()
}

// 重置
const reset = () => {
    formModel.tenantName = ''
    formModel.buildingOrHouseName = ''
    formModel.leaseStartDateRange = []
    pagination.current = 1
    fetchTableData()
}

// 分页变化
const onPageChange = (page: number) => {
    pagination.current = page
    fetchTableData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    fetchTableData()
}

// 批量自动进场
const batchModalVisible = ref(false)
const batchEnterData = ref({
    contractIds: [] as string[],
    isNotify: true,
    enterDate: dayjs().format('YYYY-MM-DD')
})
const batchEnterFormRef = ref()
const batchEnterRules = {
    enterDate: [{ required: true, message: '请选择进场日期' }]
}
const openBatchAutoEntryModal = () => {
    if (selectionRowKeys.value.length === 0) {
        Message.warning('请选择要批量进场的合同')
        return
    }
    batchModalVisible.value = true
}
const handleBatchAutoEntry = async () => {
    const errors = await batchEnterFormRef.value.validate()
    if (errors) {
        return
    }
    try {
        batchEnterData.value.contractIds = selectionRowKeys.value
        await batchAutoEnter(batchEnterData.value)
        Message.success('批量自动进场成功')
        batchModalVisible.value = false
        fetchTableData()
    } catch (error) {
        console.error('批量自动进场失败', error)
    }
}

// 办理进场
const handleEntry = async (record: TableItem) => {
    currentRecord.value = { ...record, isMultiple: false }
    
    // 如果未进场房源数为1，直接跳转办理进场页面
    if (record.unenterNum === 1) {
        try {
            // 获取该合同的未进场房源列表
            const params = {
                contractId: record.contractId!
            }
            const roomsRes = await getUnenteredRooms(params)
            
            if (roomsRes && roomsRes.code === 200) {
                const roomIds = roomsRes.data?.map((room: any) => room.id) || []
                
                if (roomIds.length === 1) {
                    // 准备进场数据
                    const res = await initEnter(record.contractId!, roomIds)
                    currentEntryData.value = {
                        id: record.id || '',
                        projectId: record.projectId || '',
                        contractId: record.contractId || '',
                        contractUnionId: record.contractUnionId || '',
                        contractNo: record.contractNo || '',
                        contractPurpose: record.contractPurpose || undefined,
                        tenantName: record.tenantName || '',
                        rentStartDate: record.rentStartDate || '',
                        rentEndDate: record.rentEndDate || '',
                        roomList: res.data.enterRoomList,
                    }
                    editType.value = 'create'
                    entryDrawerVisible.value = true
                    return
                }
            }
        } catch (error) {
            console.error('获取房源信息失败', error)
        }
    }
    
    // 其他情况显示选择房源弹框
    selectHousesModalVisible.value = true
}

// 选择房源处理
const handleHousesSelected = (houses: any[]) => {
    selectedHouses.value = houses
}

const handleSelectHousesCancel = () => {
    selectHousesModalVisible.value = false
    currentRecord.value = null
    selectedHouses.value = []
}

const handleSelectHousesNext = async () => {
    const roomIds = selectHousesRef.value?.getSelectedHouses() || []
    if (roomIds.length === 0) {
        Message.warning('请选择要进场的房源')
        return
    }

    selectHousesModalVisible.value = false

    // 准备进场数据
    const res = await initEnter(currentRecord.value?.contractId!, roomIds)
    currentEntryData.value = {
        id: currentRecord.value?.id || '',
        projectId: currentRecord.value?.projectId || '',
        contractId: currentRecord.value?.contractId || '',
        contractUnionId: currentRecord.value?.contractUnionId || '',
        contractNo: currentRecord.value?.contractNo || '',
        contractPurpose: currentRecord.value?.contractPurpose || undefined,
        tenantName: currentRecord.value?.tenantName || '',
        rentStartDate: currentRecord.value?.rentStartDate || '',
        rentEndDate: currentRecord.value?.rentEndDate || '',
        roomList: res.data.enterRoomList,
    }
    editType.value = 'create'
    entryDrawerVisible.value = true
}

// 取消办理进场
const handleEntryCancel = () => {
    entryDrawerVisible.value = false
    currentEntryData.value = null
}

// 保存办理进场
const entryProcessRef = ref()
const handleEntrySave = async () => {
    try {
        const data = await entryProcessRef.value?.handleSave()
        const res = await saveEnter(data)
        if (res && res.code === 200) {
            Message.success('办理进场成功')
            entryDrawerVisible.value = false
            fetchTableData()
        }
    } catch (error) {
        console.error('办理进场失败', error)
    }
}

const handleDetail = async (record: TableItem) => {
    editType.value = 'detail'
    const res = await getEnterDetailWithRooms(record.id as string)
    currentEntryData.value = {
        id: record?.id || '',
        projectId: record?.projectId || '',
        contractId: record?.contractId || '',
        contractUnionId: record?.contractUnionId || '',
        contractNo: record?.contractNo || '',
        contractPurpose: record?.contractPurpose || undefined,
        tenantName: record?.tenantName || '',
        rentStartDate: record?.rentStartDate || '',
        rentEndDate: record?.rentEndDate || '',
        roomList: res.data.enterRoomList?.map((item: any) => {
            item.elecMeterReading = item.elecMeterReading ? Number(item.elecMeterReading) : undefined
            item.coldWaterReading = item.coldWaterReading ? Number(item.coldWaterReading) : undefined
            item.hotWaterReading = item.hotWaterReading ? Number(item.hotWaterReading) : undefined
            item.assetList = item.assetList || []
            return item
        }),
    }
    entryDrawerVisible.value = true
}

const handleAdjust = async (record: TableItem) => {
    editType.value = 'edit'
    const res = await getEnterDetailWithRooms(record.id as string)
    currentEntryData.value = {
        id: record?.id || '',
        projectId: record?.projectId || '',
        contractId: record?.contractId || '',
        contractUnionId: record?.contractUnionId || '',
        contractNo: record?.contractNo || '',
        contractPurpose: record?.contractPurpose || undefined,
        tenantName: record?.tenantName || '',
        rentStartDate: record?.rentStartDate || '',
        rentEndDate: record?.rentEndDate || '',
        roomList: res.data.enterRoomList?.map((item: any) => {
            item.elecMeterReading = item.elecMeterReading ? Number(item.elecMeterReading) : undefined
            item.coldWaterReading = item.coldWaterReading ? Number(item.coldWaterReading) : undefined
            item.hotWaterReading = item.hotWaterReading ? Number(item.hotWaterReading) : undefined
            item.assetList = item.assetList || []
            return item
        }),
    }
    entryDrawerVisible.value = true
}

const handleNotify = async (record: TableItem) => {
    console.log('通知客户', record)
    try {
        await notifyCustomer(record.id as string)
        Message.success('通知客户成功')
    } catch (error) {
        console.error('通知客户失败', error)
    }
}

const handleSelectionChange = (selectedRowKeys: string[]) => {
    selectionRowKeys.value = selectedRowKeys
}
</script>

<style scoped>
.container {
    padding: 0 16px;

    .content {
        background-color: #fff;
        border-radius: 4px;
    }
}

:deep(.arco-table-th) {
    background-color: var(--color-fill-2);
}

:deep(.arco-card) {
    height: 100%;
}

.modal-footer {
    padding-top: 16px;
    display: flex;
    justify-content: flex-end;
}

.link-cell {
    color: rgb(var(--primary-6));
    cursor: pointer;
}
</style>