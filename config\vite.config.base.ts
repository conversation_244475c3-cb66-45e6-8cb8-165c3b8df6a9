import { resolve } from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import svgLoader from 'vite-svg-loader';
import configArcoStyleImportPlugin from './plugin/arcoStyleImport';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import path from 'path';
import AutoImport from 'unplugin-auto-import/vite';
import configPerformancePlugin from './plugin/performance';

export default defineConfig({
    ...configPerformancePlugin({
        enableCache: true,
        enableMultiThread: true,
        maxMemory: 8192,
        enableFsCache: true,
    }),
    plugins: [
        vue(),
        vueJsx(),
        svgLoader({ svgoConfig: {} }),
        configArcoStyleImportPlugin(),
        createSvgIconsPlugin({
            iconDirs: [path.resolve(process.cwd(), 'src/assets/icons/svg')], // SVG 文件目录
            symbolId: 'icon-[dir]-[name]', // 生成的 symbolId 格式
        }),
        AutoImport({
            imports: ['vue', 'vue-router', 'pinia'],
            dts: 'src/auto-imports.d.ts', // 生成类型声明文件
        }),
    ],
    resolve: {
        alias: [
            {
                find: '@',
                replacement: resolve(__dirname, '../src'),
            },
            {
                find: 'assets',
                replacement: resolve(__dirname, '../src/assets'),
            },
            {
                find: 'vue-i18n',
                replacement: 'vue-i18n/dist/vue-i18n.cjs.js', // Resolve the i18n warning issue
            },
            {
                find: 'vue',
                replacement: 'vue/dist/vue.esm-bundler.js', // compile template
            },
        ],
        extensions: ['.ts', '.js'],
    },
    define: {
        'process.env': {},
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false', // 关闭生产环境 hydration 不匹配的详细警告
    },
    css: {
        preprocessorOptions: {
            less: {
                modifyVars: {
                    hack: `true; @import (reference) "${resolve(
                        'src/assets/style/breakpoint.less'
                    )}";`,
                },
                javascriptEnabled: true,
            },
        },
    },
});
