import http from './index';

// 进场记录相关接口

// 进场记录数据类型定义
export interface EnterRoomAssetsAddDTO {
    id?: string;
    enterRoomId?: string;
    category?: number;
    name?: string;
    specification?: string;
    count?: number;
    isMissing?: boolean;
    isAdd?: boolean;
    isDel?: boolean;
}

export interface EnterRoomAddDTO {
    id?: string;
    enterId?: string;
    roomId?: string;
    roomName?: string;
    propertyType?: number;
    parcelName?: string;
    buildingName?: string;
    enterDate?: string;
    elecMeterReading?: number;
    coldWaterReading?: number;
    hotWaterReading?: number;
    remark?: string;
    assetList?: EnterRoomAssetsAddDTO[];
    isDel?: boolean;
}

export interface EnterAddDTO {
    id?: string;
    projectId?: string;
    contractId?: string;
    contractUnionId?: string;
    enteredRoomCount?: number;
    isNotify?: boolean;
    isDel?: boolean;
    roomList?: EnterRoomAddDTO[];
}

export interface BatchAutoEnterDTO {
    contractIds?: string[];
    isNotify?: boolean;
    enterDate?: string;
}

export interface EnterQueryParams {
    id?: string;
    projectId?: string;
    contractId?: string;
    contractUnionId?: string;
    enteredRoomCount?: number;
    isNotify?: boolean;
    createBy?: string;
    createByName?: string;
    createTime?: string;
    updateBy?: string;
    updateByName?: string;
    updateTime?: string;
    isDel?: boolean;
    type?: string;
    tenantName?: string;
    roomName?: string;
    rentStartDateBegin?: string;
    rentStartDateEnd?: string;
    pageNum: number;
    pageSize: number;
}

export interface EnterVo {
    id?: string;
    projectId?: string;
    contractId?: string;
    contractUnionId?: string;
    enteredRoomCount?: number;
    isNotify?: boolean;
    createByName?: string;
    updateByName?: string;
    isDel?: boolean;
    projectName?: string;
    contractNo?: string;
    contractPurpose?: number;
    tenantName?: string;
    roomName?: string;
    rentStartDate?: string;
    rentEndDate?: string;
    unenterNum?: number;
    createTime?: string;
}

// API接口函数

/**
 * 查询入场记录列表
 * 支持按项目id, 类型(待办理/已办理)，承租方（模糊匹配），楼栋/房源（模糊搜索），租期开始日期，租期结束日期进行查询
 */
export function getEnterList(params: EnterQueryParams) {
    return http.post<EnterVo[]>('/business-rent-admin/enter/list', params);
}

/**
 * 获取入场记录详细信息
 */
export function getEnterDetail(id: string) {
    return http.get('/business-rent-admin/enter/detail', { id });
}

/**
 * 进场单详情（包含房间和资产信息）
 * 根据进场单id查询进场单详情信息，包括合同信息、房间列表和资产列表
 */
export function getEnterDetailWithRooms(enterId: string) {
    return http.get('/business-rent-admin/enter/enterDetail', { enterId });
}

/**
 * 新增入场记录
 */
export function addEnter(data: EnterAddDTO) {
    return http.post('/business-rent-admin/enter', data);
}

/**
 * 修改入场记录
 */
export function updateEnter(data: EnterAddDTO) {
    return http.put('/business-rent-admin/enter', data);
}

/**
 * 进场单保存
 */
export function saveEnter(data: EnterAddDTO) {
    return http.post('/business-rent-admin/enter/save', data);
}

/**
 * 删除入场记录
 */
export function deleteEnter(ids: string[]) {
    return http.delete('/business-rent-admin/enter/delete', { id: ids });
}

/**
 * 进场单初始化
 */
export function initEnter(contractId: string, roomIds?: string[]) {
    return http.post('/business-rent-admin/enter/init', {
        contractId,
        roomIds,
    });
}

/**
 * 根据合同id查询未进场房源列表
 */
export function getUnenteredRooms(data: any) {
    return http.post('/business-rent-admin/enter/unenteredRooms', data);
}

/**
 * 通知客户
 * 根据进场单id发送进场通知给客户
 */
export function notifyCustomer(enterId: string) {
    return http.get(`/business-rent-admin/enter/notifyCustomer?enterId=${enterId}`);
}

/**
 * 批量自动进场
 */
export function batchAutoEnter(data: BatchAutoEnterDTO) {
    return http.post('/business-rent-admin/enter/batchAutoEnter', data);
}

/**
 * 导出入场记录列表
 */
export function exportEnterList(params: EnterQueryParams) {
    return http.download('/business-rent-admin/enter/export', params);
}
