<template>
    <div class="common-page">
        <contractDetail v-if="isLoaded" />
    </div>
</template>

<script setup lang="ts">
import contractDetail from '@/views/contract/contractDetail/index.vue'
import { useContractStore } from '@/store/modules/contract/index';
import { getContractById, ContractAddDTO, ContractVo, getContractDetailBill } from '@/api/contract';
import customerApi from '@/api/customer'
import { Message } from '@arco-design/web-vue';

const route = useRoute()

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

// 获取合同详情
const fetchContractDetail = async (id?: string) => {
    try {
        const { data } = await getContractById(id || contractData.value.id as string);
        if (data) {
            // 更新合同数据
            const contractInfo = data as ContractVo;

            contractInfo.fees = contractInfo.fees || []
            contractInfo.costs = contractInfo.costs || []
            contractInfo.rooms = contractInfo.rooms || []
            contractInfo.rentReceivableDate = Number(contractInfo.rentReceivableDate)

            contractStore.contractDetail = { ...contractStore.contractDetail, ...contractInfo as any };
            fetchBill()
            fetchLesseeInfo()
        }
    } catch (error) {
        console.error(error);
    }
};

// 获取账单
const fetchBill = async () => {
    const { data } = await getContractDetailBill(contractData.value.id as string)
    contractStore.contractDetailCosts = data
}

// 获取承租人信息
const fetchLesseeInfo = async () => {
    const { data } = await customerApi.getCustomerDetail(contractData.value.customer.customerId as string)
    contractStore.contractLesseeInfo = data
}

const isLoaded = ref(false)
onMounted(() => {
    contractStore.resetContractDetail()
    contractStore.isApproval = true
    isLoaded.value = true
    if (route.query.id) {
        fetchContractDetail(route.query.id as string)
    } else {
        Message.error('合同ID不存在')
    }
})
</script>

<style scoped lang="less">
.common-page {
    height: 100vh;
    overflow: hidden;
    padding: 0;
}
</style>