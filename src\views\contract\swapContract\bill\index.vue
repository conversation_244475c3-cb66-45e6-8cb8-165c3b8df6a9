<template>
    <div class="expand-bill">
        <section>
            <sectionTitle title="换房前有效账单" :large="true" />
            <a-card :bordered="false" :body-style="{ padding: '0' }">
                <div class="table-container">
                    <sectionTitle class="margin-top-16" title="保证金信息列表" />
                    <div class="table-content">
                        <a-table :data="beforeDepositTableData" :columns="depositColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }">
                            <template #actualReceivable="{ record }">
                                {{ formatAmount(record.actualReceivable) }}
                            </template>
                            <template #receivedAmount="{ record }">
                                {{ formatAmount(record.receivedAmount) }}
                            </template>
                            <template #remainingReceivable="{ record }">
                                {{ formatAmount(record.remainingReceivable) }}
                            </template>
                        </a-table>
                    </div>
                </div>
                <div class="table-container">
                    <sectionTitle title="租金信息列表" />
                    <div class="table-content">
                        <a-table :data="beforeRentTableData" :columns="rentColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }">
                            <template #actualReceivable="{ record }">
                                {{ formatAmount(record.actualReceivable) }}
                            </template>
                            <template #freeRent="{ record }">
                                {{ formatAmount(record.freeRent) }}
                            </template>
                            <template #receivedAmount="{ record }">
                                {{ formatAmount(record.receivedAmount) }}
                            </template>
                        </a-table>
                    </div>
                </div>
                <div class="table-container">
                    <sectionTitle title="换房房源退款信息" />
                    <div class="table-content">
                        <a-table :data="beforeRefundTableData" :columns="refundColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }">
                            <template #terminateReceivableTip="{ record }">
                                <span class="header-tips">截止换房日期应收-已收</span>
                            </template>
                            <template #refundAmountTip="{ record }">
                                <span class="header-tips">已收-截止换房日期应收</span>
                            </template>
                            <template #arrearsAmount="{ record }">
                                {{ record.arrearsAmount }}
                            </template>
                            <template #refundAmount="{ record }">
                                {{ record.refundAmount }}
                            </template>
                        </a-table>
                    </div>
                </div>
            </a-card>
        </section>
        <section class="margin-top-16">
            <sectionTitle title="换房后最新账单" :large="true" />
            <a-card :bordered="false" :body-style="{ padding: '0' }">
                <div class="table-container">
                    <sectionTitle title="保证金信息列表" />
                    <div class="table-content">
                        <a-table :data="afterDepositTableData" :columns="depositColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }">
                            <template #actualReceivable="{ record }">
                                {{ formatAmount(record.actualReceivable) }}
                            </template>
                            <template #receivedAmount="{ record }">
                                {{ formatAmount(record.receivedAmount) }}
                            </template>
                            <template #remainingReceivable="{ record }">
                                {{ formatAmount(record.remainingReceivable) }}
                            </template>
                        </a-table>
                    </div>
                </div>
                <div class="table-content">
                    <sectionTitle title="租金信息列表" />
                    <div class="table-content">
                        <a-table :data="afterRentTableData" :columns="rentColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }">
                            <template #actualReceivable="{ record }">
                                {{ formatAmount(record.actualReceivable) }}
                            </template>
                            <template #freeRent="{ record }">
                                {{ formatAmount(record.freeRent) }}
                            </template>
                            <template #receivedAmount="{ record }">
                                {{ formatAmount(record.receivedAmount) }}
                            </template>
                        </a-table>
                    </div>
                </div>
                <div class="table-content">
                    <sectionTitle title="账单结转信息" />
                    <div class="table-content">
                        <a-table :data="forwardTableData" :columns="forwardColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }" />
                    </div>
                </div>
            </a-card>
        </section>
    </div>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue';
import { ref, watch } from 'vue';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 定义props接收账单数据
const props = defineProps({
    changeCosts: {
        type: Array,
        default: () => []
    },
    oldCosts: {
        type: Array,
        default: () => []
    },
    newCosts: {
        type: Array,
        default: () => []
    },
    transferCosts: {
        type: Array,
        default: () => []
    },
    isViewMode: {
        type: Boolean,
        default: false
    }
});

const beforeDepositTableData = ref<any[]>([]);
const beforeRentTableData = ref<any[]>([]);
const beforeRefundTableData = ref<any[]>([]);
const afterDepositTableData = ref<any[]>([]);
const afterRentTableData = ref<any[]>([]);

const depositColumns = [
    {
        title: '租期',
        dataIndex: 'startDate',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '租户',
        dataIndex: 'customerName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '款项',
        dataIndex: 'subjectName',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '应收日期',
        dataIndex: 'receivableDate',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '账单应收金额（元）',
        dataIndex: 'actualReceivable',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'actualReceivable'
    },
    {
        title: '账单已收金额（元）',
        dataIndex: 'receivedAmount',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'receivedAmount'
    },
    {
        title: '账单剩余应收金额（元）',
        dataIndex: 'remainingReceivable',
        width: 170,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'remainingReceivable'
    }
];

const rentColumns = [
    {
        title: '租期',
        dataIndex: 'startDate',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '租户',
        dataIndex: 'customerName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '款项',
        dataIndex: 'subjectName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '应收日期',
        dataIndex: 'receivableDate',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '账单总额（元）',
        dataIndex: 'actualReceivable',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'actualReceivable'
    },
    {
        title: '免租优惠（元）',
        dataIndex: 'freeRent',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'freeRent'
    },
    {
        title: '账单已收金额（元）',
        dataIndex: 'receivedAmount',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'receivedAmount'
    },
];
const refundColumns = [
    {
        title: '款项',
        dataIndex: 'subjectName',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '租期',
        dataIndex: 'startDate',
        width: 220,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '应收日期',
        dataIndex: 'receivableDate',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '账单应收金额（元）',
        dataIndex: 'actualReceivable',
        width: 160,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '截止换房日应收（元）',
        dataIndex: 'terminateReceivable',
        width: 180,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '账单已收金额（元）',
        dataIndex: 'receivedAmount',
        width: 160,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '欠款总额（元）',
        dataIndex: 'terminateReceivable',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'terminateReceivable',
        children: [{
            width: 160,
            titleSlotName: 'terminateReceivableTip',
            dataIndex: 'arrearsAmount',
            align: 'center',
        }]
    },
    {
        title: '预计退款金额（元）',
        dataIndex: 'refundAmount',
        width: 160,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'refundAmount',
        children: [{
            width: 160,
            titleSlotName: 'refundAmountTip',
            dataIndex: 'refundAmount',
            align: 'center',
        }]
    },
];
const forwardTableData = ref<any[]>([]);

// 处理账单数据映射
const processBillData = () => {
    console.log('换房bill组件接收到的数据:', {
        changeCosts: props.changeCosts,
        oldCosts: props.oldCosts,
        newCosts: props.newCosts,
        transferCosts: props.transferCosts
    });
    
    console.log('原始oldCosts数据:', props.oldCosts);
    console.log('原始newCosts数据:', props.newCosts);
    console.log('原始changeCosts数据:', props.changeCosts);
    console.log('原始transferCosts数据:', props.transferCosts);

    // 处理换房前有效账单 - oldCosts
    if (props.oldCosts && props.oldCosts.length > 0) {
        // 保证金信息 (costType1)
        beforeDepositTableData.value = props.oldCosts
            .filter((item: any) => item.costType === 1)
            .map((item: any) => ({
                startDate: item.startDate && item.endDate ? `${item.startDate} 至 ${item.endDate}` : '',
                customerName: item.customerName || '',
                subjectName: '保证金',
                receivableDate: item.receivableDate || '',
                actualReceivable: item.actualReceivable || 0,
                receivedAmount: item.receivedAmount || 0,
                remainingReceivable: (item.actualReceivable || 0) - (item.receivedAmount || 0),
            }));

        // 租金信息 (costType2)
        beforeRentTableData.value = props.oldCosts
            .filter((item: any) => item.costType === 2)
            .map((item: any) => ({
                startDate: item.startDate && item.endDate ? `${item.startDate} 至 ${item.endDate}` : '',
                customerName: item.customerName || '',
                subjectName: '租金',
                receivableDate: item.receivableDate || '',
                actualReceivable: item.actualReceivable || 0,
                freeRent: item.discountAmount || 0, // 使用discountAmount字段作为免租优惠
                receivedAmount: item.receivedAmount || 0,
            }));
    }

    // 处理换房房源退款信息 - changeCosts (type为2的)
    if (props.changeCosts && props.changeCosts.length > 0) {
        beforeRefundTableData.value = props.changeCosts
            .filter((item: any) => item.type === 2)
            .map((item: any) => ({
                subjectName: item.subjectName || '',
                startDate: item.startDate && item.endDate ? `${item.startDate} 至 ${item.endDate}` : '',
                receivableDate: item.receivableDate || '',
                actualReceivable: item.actualReceivable || 0,
                terminateReceivable: item.terminateReceivable || 0,
                receivedAmount: item.receivedAmount || 0,
                refundAmount: item.refundAmount || 0,
                arrearsAmount: (item.terminateReceivable || 0) - (item.receivedAmount || 0),
            }));
    }

    // 处理换房后最新账单 - newCosts（按period合并）
    if (props.newCosts && props.newCosts.length > 0) {
        // 按period分组合并账单
        const groupedByPeriod = new Map();
        
        props.newCosts.forEach((cost: any) => {
            const period = cost.period;
            if (!groupedByPeriod.has(period)) {
                groupedByPeriod.set(period, {
                    period: period,
                    startDate: cost.startDate,
                    endDate: cost.endDate,
                    customerName: cost.customerName,
                    depositCosts: [],
                    rentCosts: []
                });
            }
            
            const group = groupedByPeriod.get(period);
            
            // 根据costType分类
            if (cost.costType === 1) { // 保证金
                group.depositCosts.push(cost);
            } else if (cost.costType === 2) { // 租金
                group.rentCosts.push(cost);
            }
        });
        
        // 转换为表格数据
        const depositData: any[] = [];
        const rentData: any[] = [];
        
        groupedByPeriod.forEach((group: any, period) => {
            // 处理保证金数据 - 按period合并
            if (group.depositCosts.length > 0) {
                // 合并同一period的保证金数据
                const depositCosts = group.depositCosts;
                const startDate = depositCosts[0].startDate;
                const endDate = depositCosts[depositCosts.length - 1].endDate;
                const customerName = depositCosts[0].customerName;
                const subjectName = depositCosts[0].subjectName || '保证金';
                const receivableDate = depositCosts[0].receivableDate;
                
                // 合并金额
                const actualReceivable = depositCosts.reduce((sum: number, cost: any) => sum + (cost.actualReceivable || 0), 0);
                const receivedAmount = depositCosts.reduce((sum: number, cost: any) => sum + (cost.receivedAmount || 0), 0);
                const remainingReceivable = actualReceivable - receivedAmount;
                
                depositData.push({
                    startDate: `${startDate} 至 ${endDate}`,
                    customerName,
                    subjectName,
                    receivableDate,
                    actualReceivable,
                    receivedAmount,
                    remainingReceivable,
                    period
                });
            }
            
            // 处理租金数据 - 按period合并
            if (group.rentCosts.length > 0) {
                // 合并同一period的租金数据
                const rentCosts = group.rentCosts;
                const startDate = rentCosts[0].startDate;
                const endDate = rentCosts[rentCosts.length - 1].endDate;
                const customerName = rentCosts[0].customerName;
                const subjectName = rentCosts[0].subjectName || '租金';
                const receivableDate = rentCosts[0].receivableDate;
                
                // 合并金额
                const actualReceivable = rentCosts.reduce((sum: number, cost: any) => sum + (cost.actualReceivable || 0), 0);
                const discountAmount = rentCosts.reduce((sum: number, cost: any) => sum + (cost.discountAmount || 0), 0);
                const receivedAmount = rentCosts.reduce((sum: number, cost: any) => sum + (cost.receivedAmount || 0), 0);
                
                rentData.push({
                    startDate: `${startDate} 至 ${endDate}`,
                    customerName,
                    subjectName,
                    receivableDate,
                    actualReceivable,
                    freeRent: discountAmount, // 使用discountAmount作为免租优惠
                    receivedAmount,
                    period
                });
            }
        });
        
        // 更新表格数据
        afterDepositTableData.value = depositData;
        afterRentTableData.value = rentData;
    }

    // 处理账单结转信息 - transferCosts
    if (props.transferCosts && props.transferCosts.length > 0) {
        forwardTableData.value = props.transferCosts.map((item: any) => ({
            startDate: item.startDate && item.endDate ? `${item.startDate} 至 ${item.endDate}` : '',
            customerName: item.customerName || '',
            subjectName: item.subjectName || '',
            receivableDate: item.receivableDate || '',
            actualReceivable: item.actualReceivable || 0,
            carryoverAmount: item.carryoverAmount || 0,
        }));
    }
};

// 监听props变化，重新处理数据
watch(() => [props.changeCosts, props.oldCosts, props.newCosts, props.transferCosts], () => {
    processBillData();
}, { deep: true, immediate: true });
const forwardColumns = [{
    title: '租期',
    dataIndex: 'startDate',
    width: 200,
    align: 'center',
    ellipsis: true,
    tooltip: true,
},
{
    title: '租户',
    dataIndex: 'customerName',
    width: 150,
    align: 'center',
    ellipsis: true,
    tooltip: true,
},
{
    title: '款项',
    dataIndex: 'subjectName',
    width: 150,
    align: 'center',
    ellipsis: true,
    tooltip: true,
},
{
    title: '应收日期',
    dataIndex: 'receivableDate',
    width: 150,
    align: 'center',
    ellipsis: true,
    tooltip: true,
},
{
    title: '账单应收金额（元）',
    dataIndex: 'actualReceivable',
    width: 150,
    align: 'center',
    ellipsis: true,
    tooltip: true,
},
{
    title: '账单结转金额（元）',
    dataIndex: 'carryoverAmount',
    width: 150,
    align: 'center',
    ellipsis: true,
    tooltip: true,
},
];
</script>

<style lang="less" scoped>
section+section {
    margin-top: 16px;
}

.table-container {
    margin-top: 16px;
}

.table-content {
    margin-top: 16px;
}

sectionTitle+.table-content,
.table-content+.table-content {
    margin-top: 16px;
}

.header-tips {
    color: red;
    font-size: 12px;
}
</style>