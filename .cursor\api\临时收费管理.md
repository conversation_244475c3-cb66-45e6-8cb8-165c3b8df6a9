---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/临时收费

<a id="opIdedit_1"></a>

## PUT 修改临时收费

PUT /temporary/edit

修改临时收费记录

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "subjectId": "string",
  "subjectName": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "remark": "string",
  "attachments": "string",
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[TemporaryCostAddDTO](#schematemporarycostadddto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdsubmit"></a>

## POST 发起审批

POST /temporary/submit

发起临时收费审批，只有草稿，退回状态的记录可以发起审批

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |临时收费ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdexport"></a>

## POST 导出临时收费列表

POST /temporary/export

导出临时收费列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|projectId|query|string| 否 | 项目ID|项目ID|
|costType|query|integer(int32)| 否 | 账单类型: 3-其他费用|账单类型: 3-其他费用|
|approveStatus|query|array[integer]| 否 | 审批状态(0草稿 1审批中 2已通过 3已驳回 4作废)|审批状态(0草稿 1审批中 2已通过 3已驳回 4作废)|
|contractNo|query|string| 否 | 合同编号|合同编号|
|roomName|query|string| 否 | 房间名称，多个按照逗号分开|房间名称，多个按照逗号分开|
|customerName|query|string| 否 | 客户名称|客户名称|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdgetContractList"></a>

## POST 获取符合条件的合同

POST /temporary/contract/list

获取符合条件的合同

> Body 请求参数

```json
{
  "projectId": "string",
  "contractNo": "string",
  "customerName": "string",
  "roomId": "string",
  "customerId": "string",
  "type": 0,
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ContractCostReductionDTO](#schemacontractcostreductiondto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdapprove_1"></a>

## POST 审批通过临时收费

POST /temporary/approve

审批通过临时收费

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||临时收费ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_11"></a>

## POST 新增临时收费

POST /temporary/add

新增临时收费

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "subjectId": "string",
  "subjectName": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "remark": "string",
  "attachments": "string",
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[TemporaryCostAddDTO](#schematemporarycostadddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_12"></a>

## GET 查询临时收费列表

GET /temporary/list

查询临时收费列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 否 | 项目ID|项目ID|
|costType|query|integer(int32)| 否 | 账单类型: 3-其他费用|账单类型: 3-其他费用|
|approveStatus|query|array[integer]| 否 | 审批状态(0草稿 1审批中 2已通过 3已驳回 4作废)|审批状态(0草稿 1审批中 2已通过 3已驳回 4作废)|
|contractNo|query|string| 否 | 合同编号|合同编号|
|roomName|query|string| 否 | 房间名称，多个按照逗号分开|房间名称，多个按照逗号分开|
|customerName|query|string| 否 | 客户名称|客户名称|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgetInfo_1"></a>

## GET 获取临时收费详细信息

GET /temporary/detail

根据临时收费ID获取详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||临时收费ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","projectName":"string","costType":0,"subjectId":"string","subjectName":"string","costPeriod":"string","customerId":"string","customerName":"string","contractId":"string","contractNo":"string","roomName":"string","startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","contractStartDate":"2019-08-24T14:15:22Z","contractEndDate":"2019-08-24T14:15:22Z","contractPeriod":"string","receivableDate":"2019-08-24T14:15:22Z","totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"remark":"string","attachments":"string","approveStatus":0,"approveTime":"2019-08-24T14:15:22Z","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","isDel":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[TemporaryCostVo](#schematemporarycostvo)|

<a id="opIdremove_1"></a>

## DELETE 删除临时收费

DELETE /temporary/delete

删除临时收费记录，只有草稿状态的记录可以删除

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||临时收费ID列表|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

# 数据模型

<h2 id="tocS_TemporaryCostAddDTO">TemporaryCostAddDTO</h2>

<a id="schematemporarycostadddto"></a>
<a id="schema_TemporaryCostAddDTO"></a>
<a id="tocStemporarycostadddto"></a>
<a id="tocstemporarycostadddto"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "subjectId": "string",
  "subjectName": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "remark": "string",
  "attachments": "string",
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|customerId|string|false|none|客户ID|客户ID|
|customerName|string|false|none|客户名称|客户名称|
|contractId|string|false|none|合同ID|合同ID|
|contractNo|string|false|none|合同编号|合同编号|
|roomName|string|false|none|房间名称，多个按照逗号分开|房间名称，多个按照逗号分开|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途名称|收款用途名称|
|costType|integer(int32)|false|none|账单类型: 3-其他费用|账单类型: 3-其他费用|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|remark|string|false|none|说明|说明|
|attachments|string|false|none|附件|附件|
|approveStatus|integer(int32)|false|none|状态(0草稿 1审批中 2已通过 3已驳回 4作废)|状态(0草稿 1审批中 2已通过 3已驳回 4作废)|
|approveTime|string(date-time)|false|none|审批通过时间|审批通过时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_ContractCostReductionDTO">ContractCostReductionDTO</h2>

<a id="schemacontractcostreductiondto"></a>
<a id="schema_ContractCostReductionDTO"></a>
<a id="tocScontractcostreductiondto"></a>
<a id="tocscontractcostreductiondto"></a>

```json
{
  "projectId": "string",
  "contractNo": "string",
  "customerName": "string",
  "roomId": "string",
  "customerId": "string",
  "type": 0,
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|false|none|项目id|项目id|
|contractNo|string|false|none|合同号|合同号|
|customerName|string|false|none|承租方名称|承租方名称|
|roomId|string|false|none|房间id|房间id|
|customerId|string|false|none|承租方id|承租方id|
|type|integer(int32)|false|none|类型（1减免 2缓交 3分期）|类型（1减免 2缓交 3分期）|
|contractId|string|false|none|合同id|合同id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|

<h2 id="tocS_TemporaryCostVo">TemporaryCostVo</h2>

<a id="schematemporarycostvo"></a>
<a id="schema_TemporaryCostVo"></a>
<a id="tocStemporarycostvo"></a>
<a id="tocstemporarycostvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "costType": 0,
  "subjectId": "string",
  "subjectName": "string",
  "costPeriod": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractStartDate": "2019-08-24T14:15:22Z",
  "contractEndDate": "2019-08-24T14:15:22Z",
  "contractPeriod": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "remark": "string",
  "attachments": "string",
  "approveStatus": 0,
  "approveTime": "2019-08-24T14:15:22Z",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|costType|integer(int32)|false|none|账单类型: 3-其他费用|账单类型: 3-其他费用|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途名称|收款用途名称|
|costPeriod|string|false|none|账单周期|账单周期|
|customerId|string|false|none|客户ID|客户ID|
|customerName|string|false|none|客户名称|客户名称|
|contractId|string|false|none|合同ID|合同ID|
|contractNo|string|false|none|合同编号|合同编号|
|roomName|string|false|none|房间名称，多个按照逗号分开|房间名称，多个按照逗号分开|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|contractStartDate|string(date-time)|false|none|合同开始日期|合同开始日期|
|contractEndDate|string(date-time)|false|none|合同结束日期|合同结束日期|
|contractPeriod|string|false|none|合同周期|合同周期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|remark|string|false|none|说明|说明|
|attachments|string|false|none|附件|附件|
|approveStatus|integer(int32)|false|none|状态(0草稿 1审批中 2已通过 3已驳回 4作废)|状态(0草稿 1审批中 2已通过 3已驳回 4作废)|
|approveTime|string(date-time)|false|none|审批通过时间|审批通过时间|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

