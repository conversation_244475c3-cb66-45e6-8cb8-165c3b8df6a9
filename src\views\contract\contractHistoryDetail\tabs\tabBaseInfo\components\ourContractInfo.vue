<template>
    <section>
        <sectionTitle title="甲方签约信息" />
        <div class="content">
            <a-grid :cols="1" :col-gap="16" :row-gap="0">
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">甲方签约主体公司</label>
                        <div class="detail-text">{{ contractData.ourSigningParty || '' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">签约方式</label>
                        <div class="detail-text">{{signTypeOptions.find(item => item.value ===
                            contractData.signWay)?.label || ''}}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">用地性质</label>
                        <div class="detail-text">{{ contractData.landUsage || '' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">合同签约人</label>
                        <div class="detail-text">{{ contractData.signerName || '' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">制单人</label>
                        <div class="detail-text">{{ contractData.createByName || userName }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">制单日期</label>
                        <div class="detail-text">{{ contractData.createTime || createDate }}</div>
                    </div>
                </a-grid-item>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { useContractStore } from '@/store/modules/contract'
import { useUserStore } from '@/store';
import dayjs from 'dayjs';
import { ContractAddDTO } from '@/api/contract';

const userStore = useUserStore()
const userName = computed(() => userStore.nickName)
const createDate = computed(() => dayjs().format('YYYY-MM-DD'))

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)

// 签约方式选项
const signTypeOptions = [
    { label: '电子合同', value: 0 },
    { label: '纸质合同（甲方电子章）', value: 1 },
    { label: '纸质合同（双方实体章）', value: 2 }
]
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.form-item {
    margin-bottom: 16px;

    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>