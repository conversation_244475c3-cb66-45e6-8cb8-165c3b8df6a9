import http from '../index'

// 资产变更相关接口

// 查询资产变更列表
export function getChangeList(params: AssetChangeQueryParams) {
  return http.get('/business-asset/change/list', params)
}

// 获取资产变更详细信息
export function getChangeDetail(id: string) {
  return http.get('/business-asset/change/detail?id='+id)
}

// 新增资产变更
export function addAssetChange(data: AssetChangeAddDTO) {
  return http.post('/business-asset/change/add', data)
}

// 修改资产变更
export function updateAssetChange(data: AssetChangeAddDTO) {
  return http.put('/business-asset/change/edit', data)
}

// 删除资产变更
export function deleteAssetChange(id: string) {
  return http.delete('/business-asset/change/delete?id='+id)
}

// 查询需要变更的房源列表
export function getChangeRoomList(params: AssetChangeRoomQueryDTO) {
  return http.get('/business-asset/change/room/list', params)
}

// 查询项目列表
export function getProjectList(params: any) {
  return http.get('/business-asset/project/list', params)
}

// 根据项目ID获取楼栋树结构
export function getBuildingTree(projectId: string, buildingName?: string) {
  const params: Record<string, any> = { projectId }
  
  if (buildingName) {
    params.buildingName = buildingName
  }
  
  return http.get('/business-asset/project/building/tree', params)
}

// 查询房间列表
export function getRoomList(params: any) {
  return http.get('/business-asset/project/room/list', params)
}

// 提交审批
export function submitOA(data: any) {
  return http.post('/business-asset/change/submitOA?id='+data)
}

// TypeScript 类型定义

// 资产变更查询参数
export interface AssetChangeQueryParams {
  id?: string // 主键ID
  projectId?: string // 项目id
  changeOrderCode?: string // 变更单编码
  changeType?: string // 变更类型（1面积变更）
  changeBuilding?: string // 变更楼栋
  changeRoomCount?: number // 变更房源数
  changeDate?: string // 变更日期
  changeDescription?: string // 变更说明
  approvalStatus?: string // 审批状态（0草稿 1审批中 2审批通过 3审批拒绝）
  approvalTime?: string // 审批通过时间
  remark?: string // 备注
  attachment?: string // 上传附件
  createBy?: string // 创建人账号
  createByName?: string // 创建人姓名
  createTime?: string // 创建时间
  updateBy?: string // 更新人账号
  updateByName?: string // 更新人姓名
  updateTime?: string // 更新时间
  isDel?: boolean // 0 否 1是
  pageNum: number
  pageSize: number
}

// 资产变更新增/修改DTO
export interface AssetChangeAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string // 主键ID
  projectId?: string // 项目id
  changeOrderCode?: string // 变更单编码
  changeType?: string // 变更类型（1面积变更）
  changeBuilding?: string // 变更楼栋
  changeRoomCount?: number // 变更房源数
  changeDate?: string // 变更日期
  changeDescription?: string // 变更说明
  approvalStatus?: string // 审批状态（0草稿 1审批中 2审批通过 3审批拒绝）
  operationType?: string // 操作类型（1暂存 2提交）
  roomIds?: string[] // 房间ID列表
  remark?: string // 备注
  attachment?: string // 上传附件
}

// 资产变更房源查询DTO
export interface AssetChangeRoomQueryDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  projectId?: string // 项目id
  buildingName?: string // 楼栋名称
  roomName?: string // 房间名称
  roomIds?: string[] // 房间ID列表
}

// 资产变更房源VO
export interface AssetChangeRoomVo {
  id?: string // 房间ID
  parcelId?: string // 地块ID
  parcelName?: string // 地块名称
  buildingId?: string // 楼栋ID
  buildingName?: string // 楼栋名称
  roomName?: string // 房间名称
  productType?: string // 产品类型
  areaType?: string // 面积类型（1预测 2实测 3设计）
  areaTypeName?: string // 面积类型名称（1预测 2实测 3设计）
  buildArea?: number // 建筑面积
  innerArea?: number // 套内面积
  mdmAreaTypeName?: string // 主数据面积类型
  mdmBuildingArea?: number // 主数据建筑面积
  mdmInsideArea?: number // 主数据套内面积
  buildingAreaDiff?: number // 建筑面积差
  index?: number // 表格序号
}

// 资产变更详情VO
export interface AssetChangeDetailVo extends AssetChangeAddDTO {
  approvalTime?: string // 审批通过时间
  roomList?: AssetChangeRoomVo[] // 关联房间列表
}

// 资产变更列表VO
export interface AssetChangeVo extends AssetChangeAddDTO {
  approvalTime?: string // 审批通过时间
  projectName?: string // 项目名称
}

// 项目信息VO
export interface ProjectVo {
  id: string
  projectName: string
  projectCode?: string
  companyId?: string
  companyName?: string
}

// 楼栋树节点
export interface BuildingTreeNode {
  id: string
  name: string
  type: 'plot' | 'building' // 地块或楼栋
  children?: BuildingTreeNode[]
  buildings?: BuildingTreeNode[] // 地块下的楼栋列表
}

// 房间信息VO
export interface RoomVo {
  id: string
  roomName: string
  buildingId: string
  buildingName: string
  floor?: string
  productType?: string
  buildArea?: number
  innerArea?: number
  status?: string
}
