<template>
    <section>
        <sectionTitle title="定金信息"></sectionTitle>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table :columns="columns" :data="contractCosts.bookings" :pagination="false" :bordered="{ cell: true }"
                :scroll="{ x: 1000 }">
            </a-table>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { TableData } from '@arco-design/web-vue/es/table/interface';
import { useContractStore } from '@/store/modules/contract'
import { ContractVo } from '@/api/contract';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractDetailCosts as ContractVo)

const columns: TableData = [
    {
        title: '定单',
        dataIndex: 'bookerName',
        width: 100,
        align: 'center',
        render: ({ record }: { record: any }) => {
            return record.bookerName + record.bookedRoom
        },
        ellipsis: true,
        tooltip: true
    },
    {
        title: '费项',
        dataIndex: 'field2',
        width: 100,
        align: 'center',
        render: ({ record }: { record: any }) => {
            return '定金'
        }
    },
    {
        title: '实收日期',
        dataIndex: 'bookingPaymentDate',
        width: 100,
        align: 'center'
    },
    {
        title: '实收金额（元）',
        dataIndex: 'bookingReceivedAmount',
        width: 140,
        align: 'right',
        render: ({ record }: { record: any }) => {
            return formatAmount(record.bookingReceivedAmount)
        }
    },
    {
        title: '转保证金（元）',
        dataIndex: 'transferBondAmount',
        width: 140,
        align: 'right',
        render: ({ record }: { record: any }) => {
            return formatAmount(record.transferBondAmount)
        }
    },
    {
        title: '结转租金（元）',
        dataIndex: 'transferRentAmount',
        width: 140,
        align: 'right',
        render: ({ record }: { record: any }) => {
            return formatAmount(record.transferRentAmount)
        }
    }
]
</script>

<style lang="less" scoped>
.detail-text {
    color: #333;
}
</style>