---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁后台/商业公司

<a id="opIdedit_4"></a>

## PUT 编辑商业公司

PUT /merchant

编辑商业公司

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "orgCompanyName": "string",
  "merchantNo": "string",
  "bankName": "string",
  "bankAccount": "string",
  "unifiedSocialCreditCode": "string",
  "phoneNumber": "string",
  "registeredAddress": "string",
  "legalPersonName": "string",
  "legalPersonIdCard": "string",
  "agentName": "string",
  "agentPhoneNumber": "string",
  "agentIdCard": "string",
  "sealId": "string",
  "processId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[MerchantAddDTO](#schemamerchantadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdcancelProjectRel"></a>

## POST 取消商业公司项目关联

POST /merchant/project/cancel

取消商业公司项目关联

> Body 请求参数

```json
{
  "merchantId": "string",
  "projectIds": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[MerchantProjectDTO](#schemamerchantprojectdto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdaddProjectRel"></a>

## POST 新增商业公司项目关联

POST /merchant/project/add

新增商业公司项目关联

> Body 请求参数

```json
{
  "merchantId": "string",
  "projectIds": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[MerchantProjectDTO](#schemamerchantprojectdto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdprojectList"></a>

## GET 获取商业公司关联项目列表

GET /merchant/project/list

获取商业公司关联项目列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|merchantId|query|string| 否 |商业公司ID|
|projectName|query|string| 否 |项目名称|
|pageNum|query|integer(int32)| 是 |none|
|pageSize|query|integer(int32)| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdlist_11"></a>

## GET 查询商业公司列表

GET /merchant/list

查询商业公司列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|orgCompanyName|query|string| 否 | 商业公司名称，从一房一码同步|商业公司名称，从一房一码同步|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgetInfo_5"></a>

## GET 获取商业公司详细信息

GET /merchant/detail

获取商业公司详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_MerchantAddDTO">MerchantAddDTO</h2>

<a id="schemamerchantadddto"></a>
<a id="schema_MerchantAddDTO"></a>
<a id="tocSmerchantadddto"></a>
<a id="tocsmerchantadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "orgCompanyName": "string",
  "merchantNo": "string",
  "bankName": "string",
  "bankAccount": "string",
  "unifiedSocialCreditCode": "string",
  "phoneNumber": "string",
  "registeredAddress": "string",
  "legalPersonName": "string",
  "legalPersonIdCard": "string",
  "agentName": "string",
  "agentPhoneNumber": "string",
  "agentIdCard": "string",
  "sealId": "string",
  "processId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|orgCompanyName|string|false|none|商业公司名称，从一房一码同步|商业公司名称，从一房一码同步|
|merchantNo|string|false|none|商户号|商户号|
|bankName|string|false|none|开户银行|开户银行|
|bankAccount|string|false|none|银行账号|银行账号|
|unifiedSocialCreditCode|string|false|none|统一社会信用代码|统一社会信用代码|
|phoneNumber|string|false|none|电话号码|电话号码|
|registeredAddress|string|false|none|注册地址|注册地址|
|legalPersonName|string|false|none|法人名称，从一房一码同步|法人名称，从一房一码同步|
|legalPersonIdCard|string|false|none|法人身份证|法人身份证|
|agentName|string|false|none|经办人姓名|经办人姓名|
|agentPhoneNumber|string|false|none|经办人电话号码|经办人电话号码|
|agentIdCard|string|false|none|经办人身份证|经办人身份证|
|sealId|string|false|none|印章ID|印章ID|
|processId|string|false|none|流程ID|流程ID|

<h2 id="tocS_MerchantProjectDTO">MerchantProjectDTO</h2>

<a id="schemamerchantprojectdto"></a>
<a id="schema_MerchantProjectDTO"></a>
<a id="tocSmerchantprojectdto"></a>
<a id="tocsmerchantprojectdto"></a>

```json
{
  "merchantId": "string",
  "projectIds": [
    "string"
  ]
}

```

商业公司项目关联DTO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|merchantId|string|true|none||商业公司ID|
|projectIds|[string]|true|none||项目ID列表|

