<template>
    <a-modal v-model:visible="visible" title="上传附件" :on-before-ok="handleBeforeOk" @ok="handleOk">
        <upload-file v-model="fileList"> </upload-file>
    </a-modal>
</template>

<script setup lang="ts">
import uploadFile from '@/components/upload/uploadFile.vue'
import { Message } from '@arco-design/web-vue'
import { uploadSignAttachment } from '@/api/contract'
import { useContractStore } from '@/store/modules/contract/index';
import { ContractVo } from '@/api/contract';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractVo)

const signAttachments = computed(() => JSON.parse(contractData.value.signAttachments || '[]') as any[])
const visible = ref(false)

const open = (id: string) => {
    fileList.value = ''
    contractId.value = id
    visible.value = true
}

const fileList = ref('')
const contractId = ref('')

const handleBeforeOk = () => {
    if (fileList.value === '[]' || fileList.value === '') {
        Message.warning('请上传附件')
        return false
    }
    return true
}

const emit = defineEmits(['submit'])
const handleOk = async () => {
    const list = JSON.parse(fileList.value).map((item: any) => ({
        ...item,
        status: 0
    }))
    const params = {
        contractId: contractId.value,
        attachments: [ ...list]
    }
    await uploadSignAttachment(params)
    emit('submit')
    visible.value = false
}

defineExpose({
    open
})
</script>

<style scoped lang="less"></style>