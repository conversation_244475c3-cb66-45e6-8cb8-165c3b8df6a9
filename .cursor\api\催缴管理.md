---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/催缴

<a id="opIdgenerate"></a>

## POST 手动生成催缴通知单和催缴函

POST /contract/bill/generate

手动生成催缴通知单和催缴函

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|contractId|query|string| 是 |合同ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdedit_12"></a>

## PUT 修改催缴

PUT /contract/bill

修改催缴

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "type": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalMoney": 0,
  "lastReceivedMoney": 0,
  "collectFlag": 0,
  "collectTime": "2019-08-24T14:15:22Z",
  "sendTime": "2019-08-24T14:15:22Z",
  "receivePerson": "string",
  "receivePhone": "string",
  "viewStatus": 0,
  "viewTime": "2019-08-24T14:15:22Z",
  "previewUrl": "string",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractBillAddDTO](#schemacontractbilladddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_9"></a>

## POST 新增催缴

POST /contract/bill

新增催缴

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "type": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalMoney": 0,
  "lastReceivedMoney": 0,
  "collectFlag": 0,
  "collectTime": "2019-08-24T14:15:22Z",
  "sendTime": "2019-08-24T14:15:22Z",
  "receivePerson": "string",
  "receivePhone": "string",
  "viewStatus": 0,
  "viewTime": "2019-08-24T14:15:22Z",
  "previewUrl": "string",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractBillAddDTO](#schemacontractbilladddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdcronJobGen"></a>

## POST 定时任务生成催缴通知单和催缴函

POST /contract/bill/cronJobGen

定时任务生成催缴通知单和催缴函

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdsave_9"></a>

## POST 保存催缴配置

POST /contract/bill/config/save

保存催缴配置

> Body 请求参数

```json
[
  {
    "id": "string",
    "status": true,
    "type": 0,
    "propertyType": 0,
    "customerType": 0,
    "payPeriod": 0,
    "ruleConfig": "string",
    "isDel": true
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractBillConfigAddDTO](#schemacontractbillconfigadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_16"></a>

## POST 导出询催缴列表

POST /contract/bill/export

导出询催缴列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目ID|项目ID|
|projectName|query|string| 否 | 项目名称|项目名称|
|customerId|query|string| 否 | 客户ID|客户ID|
|customerName|query|string| 否 | 客户名称|客户名称|
|contractId|query|string| 否 | 合同ID|合同ID|
|contractNo|query|string| 否 | 合同编号|合同编号|
|roomName|query|string| 否 | 房间名称，多个按照逗号分开|房间名称，多个按照逗号分开|
|type|query|integer(int32)| 否 | 催缴类型(1催缴函 2催缴通知单)|催缴类型(1催缴函 2催缴通知单)|
|receivableDate|query|string(date-time)| 否 | 应收日期|应收日期|
|totalMoney|query|number| 否 | 催缴金额|催缴金额|
|lastReceivedMoney|query|number| 否 | 账单生成时，除保证金外的已收金额|账单生成时，除保证金外的已收金额|
|collectFlag|query|integer(int32)| 否 | 缴费状态(0未缴 1部分缴 2已缴)|缴费状态(0未缴 1部分缴 2已缴)|
|collectTime|query|string(date-time)| 否 | 收齐时间|收齐时间|
|sendTime|query|string(date-time)| 否 | 发送时间|发送时间|
|receivePerson|query|string| 否 | 接收人|接收人|
|receivePhone|query|string| 否 | 接收人手机号|接收人手机号|
|viewStatus|query|integer(int32)| 否 | 查看状态(0未查看 1已查看)|查看状态(0未查看 1已查看)|
|viewTime|query|string(date-time)| 否 | 查看时间|查看时间|
|previewUrl|query|string| 否 | 预览地址|预览地址|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdpreview"></a>

## GET 下载催缴函

GET /contract/bill/preview

根据催缴ID下载催缴函PDF文件

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||催缴ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|string|

<a id="opIdlist_24"></a>

## GET 查询催缴列表

GET /contract/bill/list

查询催缴列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 否 | 项目ID|项目ID|
|type|query|integer(int32)| 否 | 催缴类型(1催缴函 2催缴通知单)|催缴类型(1催缴函 2催缴通知单)|
|collectFlags|query|array[integer]| 否 | 缴费状态(0未缴 1部分缴 2已缴)|缴费状态(0未缴 1部分缴 2已缴)|
|viewStatus|query|integer(int32)| 否 | 查看状态(0未查看 1已查看)|查看状态(0未查看 1已查看)|
|contractNo|query|string| 否 | 合同编号|合同编号|
|customerName|query|string| 否 | 承租方|承租方|
|sendTimeStart|query|string(date-time)| 否 | 发送日期起始时间|发送日期起始时间|
|sendTimeEnd|query|string(date-time)| 否 | 发送日期结束时间|发送日期结束时间|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgetInfo_13"></a>

## GET 获取催缴详细信息

GET /contract/bill/detail

获取催缴详细信息，包含主表信息和催缴金额列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||催缴ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"contractBill":{"id":"string","projectId":"string","projectName":"string","customerId":"string","customerName":"string","contractId":"string","contractNo":"string","roomCount":0,"roomName":"string","type":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","receivableDate":"2019-08-24T14:15:22Z","totalMoney":0,"lastReceivedMoney":0,"collectFlag":0,"collectTime":"2019-08-24T14:15:22Z","sendTime":"2019-08-24T14:15:22Z","receivePerson":"string","receivePhone":"string","viewStatus":0,"viewTime":"2019-08-24T14:15:22Z","previewUrl":"string","createByName":"string","updateByName":"string","isDel":true},"contractBillMoneyList":[{"id":"string","billId":"string","costType":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","receivableDate":"2019-08-24T14:15:22Z","totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"unreceivedAmount":0,"createByName":"string","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[ContractBillDetailVo](#schemacontractbilldetailvo)|

<a id="opIdgetConfigInfo"></a>

## GET 获取催缴配置详细信息

GET /contract/bill/config/detail

获取催缴配置详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_11"></a>

## DELETE 删除催缴

DELETE /contract/bill/delete

删除催缴

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|query|array[string]| 是 ||催缴ID列表|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_ContractBillAddDTO">ContractBillAddDTO</h2>

<a id="schemacontractbilladddto"></a>
<a id="schema_ContractBillAddDTO"></a>
<a id="tocScontractbilladddto"></a>
<a id="tocscontractbilladddto"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomName": "string",
  "type": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalMoney": 0,
  "lastReceivedMoney": 0,
  "collectFlag": 0,
  "collectTime": "2019-08-24T14:15:22Z",
  "sendTime": "2019-08-24T14:15:22Z",
  "receivePerson": "string",
  "receivePhone": "string",
  "viewStatus": 0,
  "viewTime": "2019-08-24T14:15:22Z",
  "previewUrl": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|customerId|string|false|none|客户ID|客户ID|
|customerName|string|false|none|客户名称|客户名称|
|contractId|string|false|none|合同ID|合同ID|
|contractNo|string|false|none|合同编号|合同编号|
|roomName|string|false|none|房间名称，多个按照逗号分开|房间名称，多个按照逗号分开|
|type|integer(int32)|false|none|催缴类型(1催缴函 2催缴通知单)|催缴类型(1催缴函 2催缴通知单)|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalMoney|number|false|none|催缴金额|催缴金额|
|lastReceivedMoney|number|false|none|账单生成时，除保证金外的已收金额|账单生成时，除保证金外的已收金额|
|collectFlag|integer(int32)|false|none|缴费状态(0未缴 1部分缴 2已缴)|缴费状态(0未缴 1部分缴 2已缴)|
|collectTime|string(date-time)|false|none|收齐时间|收齐时间|
|sendTime|string(date-time)|false|none|发送时间|发送时间|
|receivePerson|string|false|none|接收人|接收人|
|receivePhone|string|false|none|接收人手机号|接收人手机号|
|viewStatus|integer(int32)|false|none|查看状态(0未查看 1已查看)|查看状态(0未查看 1已查看)|
|viewTime|string(date-time)|false|none|查看时间|查看时间|
|previewUrl|string|false|none|预览地址|预览地址|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractBillConfigAddDTO">ContractBillConfigAddDTO</h2>

<a id="schemacontractbillconfigadddto"></a>
<a id="schema_ContractBillConfigAddDTO"></a>
<a id="tocScontractbillconfigadddto"></a>
<a id="tocscontractbillconfigadddto"></a>

```json
{
  "id": "string",
  "status": true,
  "type": 0,
  "propertyType": 0,
  "customerType": 0,
  "payPeriod": 0,
  "ruleConfig": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|status|boolean|false|none|开启状态(0-否,1-是)|开启状态(0-否,1-是)|
|type|integer(int32)|false|none|类型(1营收抽点 2固定金额)）|类型(1营收抽点 2固定金额)）|
|propertyType|integer(int32)|false|none|业态（0不限 1宿舍 2非宿舍）|业态（0不限 1宿舍 2非宿舍）|
|customerType|integer(int32)|false|none|客户类型（0不限 1个人 2企业）|客户类型（0不限 1个人 2企业）|
|payPeriod|integer(int32)|false|none|支付周期（0不限 1月付 2年付）|支付周期（0不限 1月付 2年付）|
|ruleConfig|string|false|none|固定金额规则配置：[{"type": 1, "days":2}]，type(1前 2后)", description = "固定金额规则配置：[{"type": 1, "days":2}]，type(1前 2后)|none|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractBillDetailVo">ContractBillDetailVo</h2>

<a id="schemacontractbilldetailvo"></a>
<a id="schema_ContractBillDetailVo"></a>
<a id="tocScontractbilldetailvo"></a>
<a id="tocscontractbilldetailvo"></a>

```json
{
  "contractBill": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "customerId": "string",
    "customerName": "string",
    "contractId": "string",
    "contractNo": "string",
    "roomCount": 0,
    "roomName": "string",
    "type": 0,
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "receivableDate": "2019-08-24T14:15:22Z",
    "totalMoney": 0,
    "lastReceivedMoney": 0,
    "collectFlag": 0,
    "collectTime": "2019-08-24T14:15:22Z",
    "sendTime": "2019-08-24T14:15:22Z",
    "receivePerson": "string",
    "receivePhone": "string",
    "viewStatus": 0,
    "viewTime": "2019-08-24T14:15:22Z",
    "previewUrl": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "contractBillMoneyList": [
    {
      "id": "string",
      "billId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "unreceivedAmount": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|contractBill|[ContractBillVo](#schemacontractbillvo)|false|none||催缴主表信息|
|contractBillMoneyList|[[ContractBillMoneyVo](#schemacontractbillmoneyvo)]|false|none|催缴金额列表|催缴金额列表|

<h2 id="tocS_ContractBillMoneyVo">ContractBillMoneyVo</h2>

<a id="schemacontractbillmoneyvo"></a>
<a id="schema_ContractBillMoneyVo"></a>
<a id="tocScontractbillmoneyvo"></a>
<a id="tocscontractbillmoneyvo"></a>

```json
{
  "id": "string",
  "billId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "unreceivedAmount": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

催缴金额列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|billId|string|false|none|催收id|催收id|
|costType|integer(int32)|false|none|账单类型|账单类型|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|unreceivedAmount|number|false|none|未收金额|未收金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractBillVo">ContractBillVo</h2>

<a id="schemacontractbillvo"></a>
<a id="schema_ContractBillVo"></a>
<a id="tocScontractbillvo"></a>
<a id="tocscontractbillvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomCount": 0,
  "roomName": "string",
  "type": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalMoney": 0,
  "lastReceivedMoney": 0,
  "collectFlag": 0,
  "collectTime": "2019-08-24T14:15:22Z",
  "sendTime": "2019-08-24T14:15:22Z",
  "receivePerson": "string",
  "receivePhone": "string",
  "viewStatus": 0,
  "viewTime": "2019-08-24T14:15:22Z",
  "previewUrl": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

催缴主表信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|customerId|string|false|none|客户ID|客户ID|
|customerName|string|false|none|客户名称|客户名称|
|contractId|string|false|none|合同ID|合同ID|
|contractNo|string|false|none|合同编号|合同编号|
|roomCount|integer(int32)|false|none|租赁房源数|租赁房源数|
|roomName|string|false|none|房间名称，多个按照逗号分开|房间名称，多个按照逗号分开|
|type|integer(int32)|false|none|催缴类型(1催缴函 2催缴通知单)|催缴类型(1催缴函 2催缴通知单)|
|startDate|string(date-time)|false|none|合同开始日期|合同开始日期|
|endDate|string(date-time)|false|none|合同结束日期|合同结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalMoney|number|false|none|催缴金额|催缴金额|
|lastReceivedMoney|number|false|none|账单生成时，除保证金外的已收金额|账单生成时，除保证金外的已收金额|
|collectFlag|integer(int32)|false|none|缴费状态(0未缴 1部分缴 2已缴)|缴费状态(0未缴 1部分缴 2已缴)|
|collectTime|string(date-time)|false|none|收齐时间|收齐时间|
|sendTime|string(date-time)|false|none|发送时间|发送时间|
|receivePerson|string|false|none|接收人|接收人|
|receivePhone|string|false|none|接收人手机号|接收人手机号|
|viewStatus|integer(int32)|false|none|查看状态(0未查看 1已查看)|查看状态(0未查看 1已查看)|
|viewTime|string(date-time)|false|none|查看时间|查看时间|
|previewUrl|string|false|none|预览地址|预览地址|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

