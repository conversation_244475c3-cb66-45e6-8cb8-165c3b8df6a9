import http from './index';

export const listCarryover = (params: any) => {
  return http.post('/business-rent-admin/carryover/list', params);
}

export const getCarryoverDetail = (id: string) => {
  return http.get('/business-rent-admin/carryover/detail?id=' + id);
}

export const exportCarryover = (params: any) => {
  return http.download('/business-rent-admin/carryover/export', params);
}

export const addCarryover = (data: any) => {
  return http.post('/business-rent-admin/carryover', data);
}

export const updateCarryover = (data: any) => {
  return http.put('/business-rent-admin/carryover', data);
}

export const deleteCarryover = (ids: string[]) => {
  return http.delete('/business-rent-admin/carryover/delete', { params: { ids } });
} 