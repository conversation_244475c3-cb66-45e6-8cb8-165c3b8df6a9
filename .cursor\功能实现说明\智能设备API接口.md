# 智能设备API接口实现说明

## 概述
根据 `.cursor/api/智能门锁.md` 文档，生成了完整的智能设备API接口文件，包括智能门锁和智能水电两个模块。

## 文件结构
```
src/api/smartDevices/
├── index.ts           # 统一导出文件
├── types.ts           # 类型定义文件
├── doorLock.ts        # 智能门锁API接口
└── waterElectric.ts   # 智能水电API接口
```

## 智能门锁API接口 (doorLock.ts)

### 主要功能
1. **房源管理**
   - `getRoomList()` - 查询房源信息列表
   - `exportRoomList()` - 导出房源信息列表

2. **设备管理**
   - `getTTLockDeviceList()` - 查询通通锁设备列表
   - `getTTLockDetail()` - 获取智能门锁详情
   - `exportTTLockDeviceList()` - 导出设备列表
   - `getDeviceInfo()` - 获取设备信息

3. **设备绑定**
   - `bindRoomDevice()` - 保存房间与设备的绑定关系
   - `unbindRoomDevice()` - 解绑房间与设备
   - `autoBindRoomDevice()` - 自动匹配绑定关系

4. **密码管理**
   - `generateTempPassword()` - 获取临时密码
   - `getLockOperateLog()` - 查询密码操作记录

5. **账号管理**
   - `getTTLockAccountList()` - 查询通通锁账号列表
   - `addTTLockAccount()` - 新增通通锁账号
   - `deleteTTLockAccount()` - 删除通通锁账号

6. **数据同步**
   - `syncDeviceData()` - 同步设备数据

## 智能水电API接口 (waterElectric.ts)

### 主要功能
1. **设备管理**
   - `getWaterElectricDeviceList()` - 查询智能水电设备列表
   - `getWaterElectricDetail()` - 获取设备详情
   - `exportWaterElectricDeviceList()` - 导出设备列表

2. **设备绑定**
   - `bindWaterElectricDevice()` - 绑定房间与设备
   - `unbindWaterElectricDevice()` - 解绑房间与设备
   - `autoBindWaterElectricDevice()` - 自动匹配绑定
   - `getWaterElectricRoomList()` - 获取房源列表

3. **读数管理**
   - `getDeviceReadingList()` - 获取设备读数记录
   - `addDeviceReading()` - 手动录入设备读数

4. **数据同步**
   - `syncWaterElectricData()` - 同步设备数据

## 类型定义 (types.ts)

### 核心类型
1. **基础类型**
   - `BasePageParams` - 分页参数
   - `AjaxResult` - 通用响应结果
   - `TableDataInfo` - 分页响应结果

2. **智能门锁相关**
   - `RoomSimpleQueryDTO` - 房源查询参数
   - `RoomSimpleVo` - 房源信息
   - `TTLockDeviceQueryParams` - 设备查询参数
   - `TTLockDeviceVo` - 设备信息
   - `TTLockOperateLogQueryDTO` - 操作日志查询参数
   - `TTLockOperateLogVo` - 操作日志信息
   - `TTLockAccountQueryDTO` - 账号查询参数
   - `TTLockAccountAddDTO` - 账号新增参数

3. **智能水电相关**
   - `WaterElectricDeviceQueryParams` - 设备查询参数
   - `WaterElectricDeviceVo` - 设备信息
   - `WaterElectricBindDTO` - 设备绑定参数
   - `DeviceReadingVo` - 设备读数记录
   - `DeviceReadingQueryDTO` - 读数查询参数

## 开发规范遵循

### API规范
- ✅ 使用 `const systemUrl = '/business-rent-admin'` 作为URL前缀
- ✅ GET接口使用 `http.get<ResponseType>(url, params)` 格式
- ✅ POST接口使用 `http.post<ResponseType>(url, data)` 格式
- ✅ 所有接口都有完整的JSDoc注释
- ✅ 使用TypeScript类型系统，所有参数和返回值都有类型定义

### 命名规范
- ✅ 文件和组件使用小驼峰命名
- ✅ 接口函数使用动词开头的驼峰命名
- ✅ 类型定义使用大驼峰命名

### 代码规范
- ✅ 缩进使用4个空格
- ✅ 行结束不添加分号
- ✅ 使用TypeScript类型系统

## 使用示例

```typescript
import { 
    getTTLockDeviceList, 
    bindRoomDevice,
    getWaterElectricDeviceList 
} from '@/api/smartDevices'

// 查询智能门锁设备列表
const deviceList = await getTTLockDeviceList({
    pageNum: 1,
    pageSize: 10,
    projectId: 'project123'
})

// 绑定房间与设备
await bindRoomDevice([{
    roomId: 'room123',
    roomName: '101',
    // ... 其他字段
}])

// 查询智能水电设备列表
const waterElectricList = await getWaterElectricDeviceList({
    pageNum: 1,
    pageSize: 10,
    projectId: 'project123',
    deviceType: 'water'
})
```

## 注意事项
1. 智能水电的API接口是基于智能门锁的结构推断生成的，实际使用时可能需要根据后端实际接口进行调整
2. 所有接口都遵循项目的统一错误处理机制，不需要在catch中添加错误提示
3. 导出功能使用 `responseType: 'blob'` 处理文件下载
4. 分页参数 `pageNum` 和 `pageSize` 为必填项
