<template>
  <a-drawer
    v-model:visible="visible"
    class="common-drawer"
    :title="drawerTitle"
    :footer="!isReadOnly"
    @cancel="handleCancel"
  >
    <a-spin :loading="loading" style="width: 100%;">
    <div class="add-operation-container">
      <!-- 左侧菜单 - 仅在查看详情时显示 -->
      <template v-if="isReadOnly">
        <a-menu
          :selected-keys="[currentTab]"
          mode="vertical"
          :style="{ width: '160px', height: '100%' }"
          @menu-item-click="handleMenuClick"
        >
          <a-menu-item key="base">点位信息</a-menu-item>
          <a-menu-item key="pricing" style="display: none;">定价信息</a-menu-item>
          <a-menu-item key="history">变更历史</a-menu-item>
        </a-menu>
      </template>

      <!-- 右侧内容区 -->
      <div class="content-section scrollable-content" :class="{ 'full-width': !isReadOnly }">
        <!-- 点位信息 -->
        <div v-show="isReadOnly ? currentTab === 'base' : true">
          <a-form
            ref="baseFormRef"
            label-align="right"
            :model="baseForm"
            layout="horizontal"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            auto-label-width
            :validate-trigger="['']"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                  <a-form-item field="roomName" label="多经点位名称" :rules="[{ required: true, message: '请输入多经点位名称' }]">
                    <a-input v-model="baseForm.roomName" placeholder="请输入多经点位名称" :disabled="isReadOnly" :max-length="30" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="propertyType" label="多经用途" :rules="[{ required: true, message: '请选择多经用途' }]">
                    <a-tree-select
                      v-model="baseForm.propertyType"
                      :data="diversificationPurposeOptions"
                      placeholder="请选择多经用途"
                      allow-clear
                      :disabled="isReadOnly"
                      :field-names="{
                        key: 'dictValue',
                        title: 'dictLabel',
                        children: 'childList'
                      }"
                    />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                  <a-form-item field="projectId" label="项目" :rules="[{ required: true, message: '请选择项目' }]">
                    <a-input v-model="projectName" placeholder="请选择项目" :disabled="true" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="djBindRoomId" label="对应房源">
                  <div class="input-link-row">
                      <a-select v-model="baseForm.djBindRoomId" placeholder="请选择房源" :disabled="true">
                        <a-option :value="baseForm.djBindRoomId">{{ baseForm.djBindName }}</a-option>
                    </a-select>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="8" style="display: flex; align-items: center;height: 32px;">
                <a-link style="margin-left: 8px;" @click="handleSelectHouse" v-if="!isReadOnly">绑定房源</a-link>
              </a-col>
            </a-row>
            <section-title title="基本信息" />
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="parcelId" label="地块" :rules="[{ required: true, message: '请选择地块' }]">
                    <a-select v-model="baseForm.parcelId" placeholder="请选择地块" :disabled="isReadOnly">
                      <a-option v-for="item in parcelOptions" :key="item.id" :value="item.id">
                        {{ item.parcelName }}
                      </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="buildingId" label="楼栋" :rules="[{ required: true, message: '请选择楼栋' }]">
                    <a-select v-model="baseForm.buildingId" placeholder="请选择楼栋" :disabled="isReadOnly">
                      <a-option v-for="item in buildingOptions" :key="item.id" :value="item.id">
                        {{ item.buildingName }}
                      </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="floorId" label="楼层">
                    <a-select v-model="baseForm.floorId" placeholder="请选择楼层" :disabled="isReadOnly">
                      <a-option v-for="item in floorOptions" :key="item.id" :value="item.id" :label="item.floorName">
                        {{ item.floorName || '未命名楼层' }}
                      </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="rentArea" label="计租面积">
                  <a-input v-model="baseForm.rentArea" @blur="handleBlur('rentArea')" placeholder="请输入计租面积" :disabled="isReadOnly">
                    <template #append>㎡</template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item field="remark" label="点位描述">
                    <a-textarea v-model="baseForm.remark" placeholder="请输入点位描述" :disabled="isReadOnly" allow-clear :max-length="200" show-word-limit/>
                </a-form-item>
              </a-col>
            </a-row>
            <section-title title="房型信息" v-if="checkIsDormitory(baseForm.propertyType)"/>
            <a-row :gutter="16">
              <a-col :span="8" v-if="checkIsDormitory(baseForm.propertyType)">
                  <a-form-item field="houseTypeId" label="户型">
                    <a-select v-model="baseForm.houseTypeId" placeholder="请选择户型" :disabled="isReadOnly">
                      <a-option v-for="item in houseTypeOptions" :key="item.id" :value="item.id">
                        {{ item.houseTypeName }}
                      </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8" v-if="checkIsDormitory(baseForm.propertyType)" style="display: flex; align-items: center;height: 32px;">
              <a-link style="margin-left: 8px;" @click="handleViewRoomType">户型信息展开</a-link>
              </a-col>
            </a-row>
            <section-title title="智能设备" />
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="智能水电表" field="smartWaterMeter">
                  <div class="input-link-row">
                      <a-input v-model="baseForm.smartWaterMeter" placeholder="请输入智能水电表" :disabled="isReadOnly" />
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12" v-if="checkIsDormitory(baseForm.propertyType)">
                <a-form-item label="智能锁" field="smartLock">
                  <div class="input-link-row">
                      <a-input v-model="baseForm.smartLock" placeholder="请输入智能锁" :disabled="isReadOnly" />
                  </div>
                </a-form-item>
              </a-col>
            </a-row>

            <section-title title="运营现状" />
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="recruitDate" label="可招商日期">
                  <a-date-picker v-model="otherForm.recruitDate" style="width: 100%" :disabled="isReadOnly" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="leaseStartDate" label="对外出租起始日期">
                  <a-date-picker v-model="otherForm.leaseStartDate" style="width: 100%" :disabled="isReadOnly" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 定价信息 -->
        <div v-show="currentTab === 'pricing'" v-if="isReadOnly">
          <section-title title="定价信息" class="first-child" />
          <div class="pricing-info">
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="pricing-item">
                  <span class="label">规划业态：</span>
                  <span class="value">{{ pricingForm.planType }}</span>
                </div>
              </a-col>
              <a-col :span="10">
                <div class="pricing-item">
                  <span class="label">基础租金：</span>
                  <span class="value">{{ pricingForm.baseRent }}元</span>
                </div>
              </a-col>
              <a-col :span="6">
                <div class="pricing-item">
                  <span class="label">附加费用：</span>
                  <span class="value">{{ pricingForm.additionalFee }}元</span>
                </div>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="pricing-item">
                  <span class="label">表价：</span>
                  <span class="value">{{ pricingForm.listPrice }}元</span>
                  <span class="price-note">（表价=基础租金+附加费用）</span>
                </div>
              </a-col>
              <a-col :span="10">
                <div class="pricing-item">
                  <span class="label">底价(不含免租期)：</span>
                  <span class="value">{{ pricingForm.basePrice }}元</span>
                  <span class="price-note">（底价（不含免租期）=表价*最大折扣）</span>
                </div>
              </a-col>
              <a-col :span="6">
                <div class="pricing-item">
                  <span class="label">计租单位：</span>
                  <span class="value">{{ pricingForm.rentUnit }}</span>
                  <a-link class="expand-link" @click="showPricingDetail = !showPricingDetail">
                    {{ showPricingDetail ? '收起' : '定价详情展开' }}
                  </a-link>
                </div>
              </a-col>
            </a-row>
          </div>

          <!-- 展开后的详细定价信息 -->
          <div v-if="showPricingDetail" style="margin-top: 16px;">
            <section-title title="免租期及底价" />
            <div class="pricing-info">
              <div class="pricing-row">
                <div class="pricing-item">
                  <span class="label">参考因素：</span>
                  <span class="value">租赁期限</span>
                </div>
              </div>
            </div>
            <a-table :columns="rentPeriodColumns" :data="rentPeriodData" :pagination="false" :bordered="{ cell: true }">
              <template #header>租赁期限对应免租期及底价</template>
            </a-table>

            <section-title title="其他信息" style="margin-top: 16px;" />
            <div class="pricing-info">
              <div class="pricing-row">
                <div class="pricing-item">
                  <span class="label">租金是否递增：</span>
                  <span class="value">{{ pricingDetailForm.hasIncrease === 'yes' ? '是' : '否' }}</span>
                </div>
                <template v-if="pricingDetailForm.hasIncrease === 'yes'">
                  <div class="pricing-item">
                    <span class="label">递增周期：</span>
                    <span class="value">{{ pricingDetailForm.increasePeriod }}年</span>
                  </div>
                  <div class="pricing-item">
                    <span class="label">单价递增率：</span>
                    <span class="value">{{ pricingDetailForm.increaseRate }}%</span>
                  </div>
                </template>
              </div>
              <div class="pricing-row">
                <div class="pricing-item">
                  <span class="label">保证金：</span>
                  <span class="value">{{ pricingDetailForm.depositAmount }}元</span>
                </div>
                <div class="pricing-item">
                  <span class="label">支付方式：</span>
                  <span class="value">{{ pricingDetailForm.paymentMethod === 'monthly' ? '月付' : '' }}</span>
                </div>
                <div class="pricing-item">
                  <span class="label">租赁期限：</span>
                  <span class="value">{{ pricingDetailForm.minLeaseTerm }}-{{ pricingDetailForm.maxLeaseTerm }}{{ pricingDetailForm.leaseUnit === 'month' ? '月' : '' }}</span>
                </div>
              </div>
            </div>
          </div>

          <section-title title="相关定价申请单" style="margin-top: 16px;" />
          <a-table
            :columns="pricingColumns"
            :data="pricingHistory"
            :pagination="false"
            :bordered="{ cell: true }"
            :scroll="{ x: 1 }"
          >
            <template #operations="{ record }">
              <a-link @click="handlePricingViewDetail(record)">详情</a-link>
            </template>
          </a-table>
        </div>

        <!-- 变更历史 -->
        <div v-show="currentTab === 'history'" v-if="isReadOnly">
          <section-title title="变更历史" class="first-child" />
          <a-table
            :columns="historyColumns"
            :data="changeHistory"
            :pagination="false"
            :bordered="{ cell: true }"
          />
        </div>
      </div>
    </div>
    </a-spin>
    <template #footer>
      <div class="footer-btns">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleSave" v-if="!isReadOnly">保存</a-button>
        </a-space>
      </div>
    </template>
    <room-type-edit-dialog
      v-model:visible="roomTypeDetailVisible"
      title="户型详情"
      :data="{ id: baseForm.houseTypeId }"
      :readonly="true"
    />

    <!-- 选择房源弹窗 -->
    <a-modal
      v-model:visible="showSelectRoomsModal"
      title="选择房源"
      :width="1000"
      @cancel="showSelectRoomsModal = false"
    >
      <div class="select-rooms-modal">
        <!-- 搜索区域 -->
        <div class="search-area">
          <a-input
            v-model="searchForm.roomName"
            placeholder="请输入房源名称"
            allow-clear
            style="width: 200px; margin:0 16px 16px 0;"
          />
          <a-button type="primary" @click="searchRooms">
            <template #icon><icon-search /></template>
            搜索
          </a-button>
        </div>
        <div class="select-rooms-modal-content" style="height: 55vh; overflow-y: auto;">
          <!-- 表格区域 -->
          <a-table
            :columns="roomColumns"
            :data="roomList"
            :pagination="pagination"
            :loading="roomLoading"
            :row-selection="rowSelection"
            @page-change="onPageChange"
            @selection-change="handleSelectionChange"
            :bordered="{ cell: true }"
            row-key="id"
          />
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <a-space>
            <a-button @click="showSelectRoomsModal = false">取消</a-button>
            <a-button type="primary" @click="handleConfirmSelectRooms">确定</a-button>
          </a-space>
        </div>
      </template>
    </a-modal>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import RoomTypeEditDialog from '@/views/property/projects/components/RoomTypeEditDialog.vue'
import { getRoomDetail, getRoomChangeHistory, getRoomList , editRoom , addRoom , type RoomChangeRecordVo, RoomStatus, RoomType, OperationSubject } from '@/api/room'
import { getParcelList, getBuildingSelectList, getFloorList, getHouseTypeList , type ProjectHouseTypeVo} from '@/api/project'
import { useDictSync } from '@/utils/dict'

const visible = ref(false)
const isReadOnly = ref(false)
const isEdit = ref(false)
const loading = ref(false)
const operationId = ref<string | null>(null)

const drawerTitle = computed(() => {
  if (isReadOnly.value) return '多经点位详情'
  if (isEdit.value) return '编辑多经点位'
  return '新增多经点位'
})

const currentTab = ref<'base' | 'pricing' | 'history'>('base')

const baseFormRef = ref()

// 基础表单数据
const baseForm = reactive({
  roomCode: '',               // 房源编码
  roomName: '',               // 房源名称
  propertyType: '',           // 物业类型
  projectId: '',              // 项目ID
  parcelId: '',               // 地块ID
  buildingId: '',             // 楼栋ID
  floorId: '',                // 楼层ID
  rentArea: '',               // 计租面积
  houseTypeId: '',            // 户型ID
  smartWaterMeter: '',        // 智能水电表
  smartLock: '',              // 智能锁
  remark: '',                // 描述/备注
  type: RoomType.MULTIPLE,    // 房源类型，固定为多经类型
  djBindRoomId: '',           // 多经绑定房源ID
  djBindName: ''              // 多经绑定房源名称
})

// 其他信息表单数据
const otherForm = reactive({
  operationStatus: '',
  recruitDate: null,
  leaseStartDate: null
})

// 定价历史表格列定义
const pricingColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '立项定价申请名称', dataIndex: 'name', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '定价楼栋', dataIndex: 'building', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '定价房源数', dataIndex: 'amount', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '审批通过时间', dataIndex: 'approvalTime', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '操作', slotName: 'operations', width: 100, ellipsis: false, tooltip: false, align: 'center', fixed: 'right' }
]

// 变更历史表格列定义
const historyColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '变更类型', dataIndex: 'type', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '变更时间', dataIndex: 'date', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '变更内容', dataIndex: 'remark', width: 200, ellipsis: true, tooltip: true, align: 'center' }
]

// 定价历史数据
const pricingHistory = ref([
  {
    index: 1,
    name: '定价申请单1',
    building: '楼栋1',
    amount: '2000元/月',
    approvalTime: '2024-04-01'
  }
])

// 变更历史数据
const changeHistory = ref<any[]>([])
const changeHistoryData = ref<RoomChangeRecordVo[]>([])

// 定价基础信息
const pricingForm = reactive({
  planType: '餐饮',
  baseRent: 900,
  additionalFee: 50,
  listPrice: 950,
  basePrice: 800,
  rentUnit: '元/月'
})

// 展开状态
const showPricingDetail = ref(false)

// 租赁期限表格列定义
const rentPeriodColumns = [
  { title: '租赁期限', dataIndex: 'period', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '免租期', dataIndex: 'freePeriod', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '底价(含免租期)', dataIndex: 'price', width: 200, ellipsis: true, tooltip: true, align: 'center' }
]

// 租赁期限数据
const rentPeriodData = ref([
  { period: '1-11月', freePeriod: '0月', price: '860元' },
  { period: '12-23月', freePeriod: '3月', price: '645元' },
  { period: '24月及以上', freePeriod: '7月', price: '619.72元' }
])

// 定价详细信息
const pricingDetailForm = reactive({
  factor: 'unlimited',
  hasIncrease: 'yes',
  increasePeriod: 2,
  increaseRate: 5,
  deposit: 'fixed',
  depositAmount: 100,
  paymentMethod: 'monthly',
  minLeaseTerm: 12,
  maxLeaseTerm: 24,
  leaseUnit: 'month'
})

// 户型信息相关
const roomTypeDetailVisible = ref(false)

const showSelectRoomsModal = ref(false)
const roomList = ref<RoomItem[]>([])
const roomLoading = ref(false)
const selectedRoomIds = ref<string[]>([])
const selectedRooms = ref<RoomItem[]>([])

// 分页
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
})

// 搜索表单
const searchForm = reactive({
  roomName: '',
  projectId: '',
  parcelId: '',
  buildingId: '',
  status: RoomStatus.ACTIVE, // 默认筛选生效中的房源
})

// 表格行选择配置
const rowSelection = computed(() => {
  return {
    type: 'radio' as const,
    showCheckedAll: true,
    onlyCurrent: false,
    selectedRowKeys: selectedRoomIds.value,
  }
})

// 处理选择变化
const handleSelectionChange = (rowKeys: string[], selectedRows: RoomItem[]) => {
  selectedRoomIds.value = rowKeys
  selectedRooms.value = selectedRows
}

// 搜索房源
const searchRooms = async () => {
  try {
    roomLoading.value = true
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      projectId: searchForm.projectId,
      roomName: searchForm.roomName,
      type: RoomType.NORMAL, // 只查询普通房源
      status: RoomStatus.ACTIVE // 只查询生效中的房源
    }
    
    const response = await getRoomList(params)
    if (response && response.rows) {
      roomList.value = response.rows
      pagination.total = response.total
    }
  } catch (error) {
    console.error('获取房源列表失败:', error)
  } finally {
    roomLoading.value = false
  }
}

// 页码变化
const onPageChange = (page: number) => {
  pagination.current = page
  searchRooms()
}

// 确认选择房源
const handleConfirmSelectRooms = async () => {
  if (selectedRoomIds.value.length === 0) {
    Message.warning('请至少选择一个房源')
    return
  }
  
  // 获取选中的房源完整数据
  const selectedRoomData = roomList.value.filter(room => selectedRoomIds.value.includes(room.id))
  
  if (selectedRoomData.length > 0) {
    const selectedRoom = selectedRoomData[0]
    
    // 绑定房源ID和名称
    baseForm.djBindRoomId = selectedRoom.id
    baseForm.djBindName = selectedRoom.roomName
    
    // 根据选中房源填充表单相关字段
    if (selectedRoom.projectId) baseForm.projectId = selectedRoom.projectId
    if (selectedRoom.parcelId) {
      baseForm.parcelId = selectedRoom.parcelId
      parcelName.value = selectedRoom.parcelName || ''
      // 加载楼栋列表
      await loadBuildingList(selectedRoom.parcelId)
    }
    
    if (selectedRoom.buildingId) {
      baseForm.buildingId = selectedRoom.buildingId
      buildingName.value = selectedRoom.buildingName || ''
      // 加载楼层列表
      await loadFloorList(selectedRoom.buildingId)
    }
    
    if (selectedRoom.floorId) {
      baseForm.floorId = selectedRoom.floorId
      floorName.value = selectedRoom.floorName || ''
    }
    
    // 填充面积信息
    if (selectedRoom.rentArea) baseForm.rentArea = selectedRoom.rentArea?.toString()
    
    // 如果有物业类型，也可以填充
    if (selectedRoom.propertyType) baseForm.propertyType = selectedRoom.propertyType
    
    //如果有智能水电表，则填充
    if (selectedRoom.smartWaterMeter) baseForm.smartWaterMeter = selectedRoom.smartWaterMeter
    //如果有智能锁，则填充
    if (selectedRoom.smartLock) baseForm.smartLock = selectedRoom.smartLock

    //如果有可招商日期，则填充
    if (selectedRoom.rentalStartDate) otherForm.recruitDate = selectedRoom.rentalStartDate
    //如果有可招商日期，则填充
    if (selectedRoom.externalRentStartDate) otherForm.leaseStartDate = selectedRoom.externalRentStartDate

    // 如果有户型ID，也可以填充
    if (selectedRoom.houseTypeId) {
      baseForm.houseTypeId = selectedRoom.houseTypeId
      // 如果是宿舍类型，加载户型列表
      if (checkIsDormitory(baseForm.propertyType) && baseForm.projectId) {
        await loadHouseTypeList(baseForm.projectId)
      }
    }
    
    // 关闭选择弹窗
    showSelectRoomsModal.value = false
  } else {
    Message.warning('未找到选中的房源数据')
  }
}

const handleBlur = (fieldName: string) => {
  const value = baseForm[fieldName as keyof typeof baseForm] as string
  // 判断是否是数字
  const isNumber = !isNaN(parseFloat(value)) && isFinite(parseFloat(value))
  if (!isNumber) {
    (baseForm as any)[fieldName] = ''
    return;
  }
  // 判断小数位数是否超过两位
  if (value.includes('.') && value.split('.')[1].length > 2) {
    // 保留两位小数
    (baseForm as any)[fieldName] = parseFloat(value).toFixed(2)
  }
}

// 监听项目变化，加载地块列表
watch(() => baseForm.projectId, (newVal) => {
  if (newVal) {
    loadParcelList(newVal)
    // 重置相关字段，但不触发验证
    baseForm.parcelId = ''
    baseForm.buildingId = ''
    baseForm.floorId = ''
    buildingOptions.value = []
    floorOptions.value = []
  }
}, { flush: 'post' })

// 监听地块变化，加载楼栋列表并存储地块名称
watch(() => baseForm.parcelId, (newVal) => {
  if (newVal) {
    // 清空楼栋和楼层，但不触发验证
    baseForm.buildingId = ''
    baseForm.floorId = ''
    buildingOptions.value = []
    floorOptions.value = []
    
    // 存储地块名称
    const selectedParcel = parcelOptions.value.find(item => item.id === newVal)
    if (selectedParcel) {
      parcelName.value = selectedParcel.parcelName
    }
    
    // 加载楼栋列表
    loadBuildingList(newVal)
  }
}, { flush: 'post' })

// 监听楼栋变化，加载楼层列表并存储楼栋名称
watch(() => baseForm.buildingId, (newVal) => {
  if (newVal) {
    // 清空楼层，但不触发验证
    baseForm.floorId = ''
    floorOptions.value = []
    
    // 存储楼栋名称
    const selectedBuilding = buildingOptions.value.find(item => item.id === newVal)
    if (selectedBuilding) {
      buildingName.value = selectedBuilding.buildingName
    }
    
    // 加载楼层列表
    loadFloorList(newVal)
  }
}, { flush: 'post' })

// 监听楼层变化，存储楼层名称
watch(() => baseForm.floorId, (newVal) => {
  if (newVal) {
    // 存储楼层名称
    const selectedFloor = floorOptions.value.find(item => String(item.id) === String(newVal))
    if (selectedFloor) {
      floorName.value = selectedFloor.floorName
    }
  }
})

// 选择物业类型时，检查是否为宿舍类型，并在控制台输出
watch(() => baseForm.propertyType, (newVal) => {
  console.log('物业类型变更为:', newVal, '类型:', typeof newVal)
  
  // 输出物业类型字典信息，用于调试
  console.log('物业类型字典:', diversificationPurposeOptions.value)
  
  // 检查是否为宿舍类型
  const isDormitory = checkIsDormitory(newVal)
  console.log('是否为宿舍类型:', isDormitory)
  
  // 如果是宿舍类型，加载户型列表
  if (isDormitory && baseForm.projectId) {
    loadHouseTypeList(baseForm.projectId)
  }
})

// 检查物业类型是否为宿舍
const checkIsDormitory = (propertyType: string): boolean => {
  if (!propertyType) return false
  
  // 检查物业类型值是否包含"宿舍"关键字
  if (propertyType.includes('宿舍')||propertyType.includes('日租房')) return true
  
  // 从字典中查找对应的标签
  const findPropertyTypeLabel = (options: any[], value: string): string => {
    for (const option of options) {
      if (option.dictValue === propertyType) {
        return option.dictLabel || ''
      }
      if (option.childList && option.childList.length > 0) {
        const childLabel = findPropertyTypeLabel(option.childList, value)
        if (childLabel) return childLabel
      }
    }
    return ''
  }
  
  const typeLabel = findPropertyTypeLabel(diversificationPurposeOptions.value, propertyType)
  console.log('物业类型标签:', typeLabel)
  
  // 检查标签是否包含"宿舍"关键字
  return typeLabel.includes('宿舍')||typeLabel.includes('日租房')
}


const handleMenuClick = (key: string) => {
  currentTab.value = key as 'base' | 'pricing' | 'history'
}

const emit = defineEmits(['confirm', 'cancel'])

const handleCancel = () => {
  isReadOnly.value = false
  isEdit.value = false
  currentTab.value = 'base'
  resetForm()
  setTimeout(() => {
    emit('cancel')
  }, 300);
  visible.value = false
}

const handleSave = async () => {
  try {
    // 按照开发规范进行表单校验
    const errors = await baseFormRef.value.validate()
    if (errors) return

    // 校验通过后设置加载状态
    loading.value = true
    
    // 准备提交数据 - 直接使用API原始字段名
    const formData = {
      id: operationId.value || undefined,
      type: RoomType.MULTIPLE, // 多经类型固定为2
      roomName: baseForm.roomName,
      propertyType: baseForm.propertyType,
      projectId: baseForm.projectId,
      parcelId: baseForm.parcelId,
      parcelName: parcelName.value,
      buildingId: baseForm.buildingId,
      buildingName: buildingName.value,
      floorId: baseForm.floorId,
      floorName: floorName.value,
      roomCode:`${projectName.value}-${buildingName.value}-${baseForm.roomName}`,
      operationSubject:OperationSubject.BUSINESS, // 默认商服
      rentArea: parseFloat(baseForm.rentArea) || undefined,
      remark: baseForm.remark,
      houseTypeId: baseForm.houseTypeId,
      smartWaterMeter: baseForm.smartWaterMeter,
      smartLock: baseForm.smartLock,
      rentalStartDate: otherForm.recruitDate || undefined,
      externalRentStartDate: otherForm.leaseStartDate || undefined,
      djBindRoomId: baseForm.djBindRoomId || undefined,
      djBindName: baseForm.djBindName || undefined
    }
    
    console.log('提交的表单数据:', formData)
    
    // 正常编辑或新增房源逻辑
    let response
    if (isEdit.value) {
      // 编辑房源
      response = await editRoom(formData)
    } else {
      // 新增房源
      response = await addRoom(formData)
    }
    
    if (response && response.code === 200) {
      Message.success(isEdit.value ? '编辑多经点位成功' : '新增多经点位成功')
      setTimeout(() => {
        // 触发confirm事件通知父组件刷新列表
        emit('confirm', { type: isEdit.value ? 'edit' : 'add', data: formData })
      }, 300);
      visible.value = false
    }
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    loading.value = false
  }
}

const handlePricingViewDetail = (record: any) => {
  Message.info('查看定价申请单详情')
}

const handleSelectHouse = () => {
  showSelectRoomsModal.value = true
  
  // 更新搜索条件
  searchForm.projectId = baseForm.projectId
  
  // 重置分页
  pagination.current = 1
  
  // 如果已有绑定的房源ID，则预选中
  if (baseForm.djBindRoomId) {
    selectedRoomIds.value = [baseForm.djBindRoomId]
  } else {
    // 否则重置选择
    selectedRoomIds.value = []
    selectedRooms.value = []
  }
  
  // 加载房源数据
  searchRooms()
}

const handleViewRoomType = () => {
  // 检查是否选择了户型
  if (!baseForm.houseTypeId) {
    Message.warning('请先选择户型')
    return
  }
  
  // 打开户型详情抽屉
  roomTypeDetailVisible.value = true
}

// 加载多经点位详情
const loadOperationDetail = (id: string): Promise<void> => {
  loading.value = true
  
  return getRoomDetail({ id })
    .then(async response => {
      if (response && response.data) {
        const detailData = response.data
        
        // 先记录原始的楼栋和楼层ID
        const originalParcelId = detailData.parcelId || ''
        const originalBuildingId = detailData.buildingId || ''
        const originalFloorId = detailData.floorId || ''
        
        console.log('开始加载详情数据，原始数据:', JSON.stringify({
          parcelId: originalParcelId,
          buildingId: originalBuildingId,
          floorId: originalFloorId
        }))
        
        // 先加载项目相关数据，确保下拉列表有数据可供匹配
        if (detailData.projectId) {
          console.log('加载项目数据:', detailData.projectId)
          
          // 设置项目ID
          baseForm.projectId = detailData.projectId
          
          // 使用await确保数据按顺序加载，避免级联验证问题
          // 加载地块列表
          await loadParcelList(detailData.projectId)
          
          // 加载户型列表，特别是宿舍类型
          if (checkIsDormitory(detailData.propertyType)) {
            await loadHouseTypeList(detailData.projectId)
          }
          
          // 如果有地块ID，设置地块并加载楼栋数据
          if (originalParcelId) {
            console.log('设置地块:', originalParcelId)
            baseForm.parcelId = originalParcelId
            await loadBuildingList(originalParcelId)
            // 如果有楼栋ID，设置楼栋并加载楼层数据
            if (originalBuildingId) {
              console.log('设置楼栋:', originalBuildingId)
              baseForm.buildingId = originalBuildingId
              await loadFloorList(originalBuildingId)

              // 设置楼层ID
              if (originalFloorId) {
                console.log('设置楼层:', originalFloorId)
                baseForm.floorId = originalFloorId
              }
            }
          }
        }
        
        // 一次性设置其他表单数据，减少重复验证
        Object.assign(baseForm, {
          roomName: detailData.roomName || '',
          propertyType: detailData.propertyType || '',
          rentArea: detailData.rentArea || '',
          roomCode: detailData.roomCode || '',
          remark: detailData.remark || '',
          houseTypeId: detailData.houseTypeId || '',
          smartWaterMeter: detailData.smartWaterMeter || '',
          smartLock: detailData.smartLock || '',
          djBindRoomId: detailData.djBindRoomId || '',
          djBindName: detailData.djBindName || ''
        })
        
        // 存储地块、楼栋、楼层名称
        parcelName.value = detailData.parcelName || ''
        buildingName.value = detailData.buildingName || ''
        floorName.value = detailData.floorName || ''
        
        // 其他信息
        otherForm.recruitDate = detailData.rentalStartDate || null
        otherForm.leaseStartDate = detailData.externalRentStartDate || null
      }
    })
    .catch(error => {
      console.error('获取多经点位详情失败:', error)
      Message.error('获取多经点位详情失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// 加载变更历史
const loadChangeHistory = (id: string): Promise<void> => {
  loading.value = true
  
  return getRoomChangeHistory({ roomId: id })
    .then(response => {
      if (response && response.data) {
        changeHistoryData.value = response.data
        
        // 转换为表格显示格式
        changeHistory.value = changeHistoryData.value.map((item, index) => {
          let remarkText = item.changeContent || ''
          
          // 尝试解析JSON字符串，提取changeContent值
          try {
            if (remarkText && typeof remarkText === 'string' && remarkText.startsWith('{')) {
              const parsedData = JSON.parse(remarkText)
              if (parsedData.changeContent) {
                remarkText = parsedData.changeContent
              }
            }
          } catch (error) {
            // 如果解析失败，保持原始值
            console.warn('解析变更内容JSON失败:', error)
          }
          
          return {
          index: index + 1,
          date: item.changeTime || '',
          type: item.changeType === 1 ? '生效' : 
                item.changeType === 2 ? '信息变更' : 
                item.changeType === 3 ? '拆分' : 
                item.changeType === 4 ? '合并' : 
                item.changeType === 5 ? '作废' : '其他',
            remark: remarkText
          }
        })
      } else {
        changeHistory.value = []
      }
    })
    .catch(error => {
      console.error('获取变更历史失败:', error)
      changeHistory.value = []
    })
    .finally(() => {
      loading.value = false
    })
}

// 表单重置
const resetForm = () => {
  // 重置基础表单
  Object.keys(baseForm).forEach(key => {
    if (typeof baseForm[key as keyof typeof baseForm] === 'string') {
      (baseForm as any)[key] = ''
    } else if (typeof baseForm[key as keyof typeof baseForm] === 'number') {
      (baseForm as any)[key] = undefined
    } else if (typeof baseForm[key as keyof typeof baseForm] === 'boolean') {
      (baseForm as any)[key] = false
    }
  })
  
  // 完全重置核心字段，确保清空
  baseForm.roomName = ''
  baseForm.propertyType = ''
  baseForm.parcelId = ''
  baseForm.buildingId = ''
  baseForm.floorId = ''
  baseForm.rentArea = ''
  baseForm.roomCode = ''
  baseForm.remark = ''
  baseForm.houseTypeId = ''
  baseForm.smartWaterMeter = ''
  baseForm.smartLock = ''
  baseForm.djBindRoomId = ''
  baseForm.djBindName = ''
  
  // 重置其他表单
  otherForm.operationStatus = ''
  otherForm.recruitDate = null
  otherForm.leaseStartDate = null
  
  // 重置名称字段
  parcelName.value = ''
  buildingName.value = ''
  floorName.value = ''
  
  // 重置选项数据
  buildingOptions.value = []
  floorOptions.value = []
  
  // 重置ID
  operationId.value = null
}
const projectName = ref('')
const parcelName = ref('')
const buildingName = ref('')
const floorName = ref('')

// Show method with mode support
const show = (mode: 'add' | 'edit' | 'view' = 'add', id?: string, data?: any) => {
  // 先设置只读状态，避免加载数据过程中触发验证
  isReadOnly.value = mode === 'view'
  
  // 确保表单完全重置
  resetForm()
  
  // 设置抽屉状态
  visible.value = true
  currentTab.value = 'base'
  isEdit.value = mode === 'edit'

  // 加载字典数据
  loadDictionaries()

  if (mode === 'edit' || mode === 'view') {
    if (id) {
      operationId.value = id
      
      if (data && data.project) {
        // 设置项目信息
        projectName.value = data.projectName || ''
      }
      
      // 加载详情数据，内部会处理级联关系
      loadOperationDetail(id).then(() => {
        // 如果是查看模式，还需要加载变更历史
        if (mode === 'view') {
          loadChangeHistory(id)
        }
      })
    } else {
      Message.warning('缺少多经点位ID，无法获取详情')
    }
  } else if (data) {
    // 新增模式下，使用传入的数据初始化表单
    console.log('新增模式，传入数据:', data)
    
    // 设置项目ID和名称
    if (data.project) {
      baseForm.projectId = data.project
      projectName.value = data.projectName || ''
      
      // 加载地块列表
      loadParcelList(data.project).then(() => {
        console.log('地块列表加载完成，数量:', parcelOptions.value.length)
      })
    }
  }
}

// 在组件挂载时初始化字典数据
onMounted(() => {
  loadDictionaries()
})

// 字典数据
const diversificationPurposeOptions = ref<DictData[]>([])

// 地块、楼栋、楼层数据
const parcelOptions = ref<any[]>([])
const buildingOptions = ref<any[]>([])
const floorOptions = ref<any[]>([])
const houseTypeOptions = ref<ProjectHouseTypeVo[]>([])
// 加载字典数据
const loadDictionaries = async () => {
  try {
    const dictData = await useDictSync('diversification_purpose')
    if (dictData.diversification_purpose) {
      // 处理树形结构数据
      const dictList = dictData.diversification_purpose as DictData[]
      // 组织成树形结构
      const treeData = dictList.filter(item => !item.parentCode)
      treeData.forEach(parent => {
        parent.childList = dictList.filter(child => child.parentCode === parent.dictCode)
      })
      diversificationPurposeOptions.value = treeData
    }
  } catch (error) {
    console.error('获取多经用途字典数据失败:', error)
  }
}

// 加载地块数据
const loadParcelList = async (projectId: string) => {
  if (!projectId) return
  try {
    const res = await getParcelList(projectId)
    if (res && res.data) {
      parcelOptions.value = res.data
      return res.data
    }
  } catch (error) {
    console.error('获取地块列表失败:', error)
  }
  return []
}

// 加载楼栋数据
const loadBuildingList = async (parcelId: string) => {
  if (!parcelId) return
  try {
    const res = await getBuildingSelectList(parcelId)
    if (res && res.data) {
      buildingOptions.value = res.data
      return res.data
    }
  } catch (error) {
    console.error('获取楼栋列表失败:', error)
  }
  return []
}

// 加载楼层数据
const loadFloorList = async (buildingId: string) => {
  if (!buildingId) return
  try {
    const res = await getFloorList(buildingId)
    if (res && res.data) {
      floorOptions.value = res.data
      return res.data
    }
  } catch (error) {
    console.error('获取楼层列表失败:', error)
  }
  return []
}

// 加载户型数据
const loadHouseTypeList = async (projectId: string) => {
  if (!projectId) return
  try {
    const res = await getHouseTypeList(projectId)
    if (res && res.data) {
      houseTypeOptions.value = res.data
    }
  } catch (error) {
    console.error('获取户型列表失败:', error)
  }
}

// 定义房源类型
interface RoomItem {
  id: string
  roomName: string
  propertyType?: string
  buildingArea?: number
  rentArea?: number
  [key: string]: any
}

// 字典数据
interface DictData {
  dictCode: string
  dictLabel: string
  dictValue: string
  dictSort: number
  parentCode?: string
  childList?: DictData[]
  [key: string]: any
}

// 房源列定义
const roomColumns = ref([
  { title: '房源名称', dataIndex: 'roomName', width: 140, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '物业类型', dataIndex: 'propertyTypeName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '楼栋', dataIndex: 'buildingName', width: 110, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '楼层', dataIndex: 'floorName', width: 110, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '建筑面积(㎡)', dataIndex: 'buildArea', width: 110, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '套内面积(㎡)', dataIndex: 'innerArea', width: 110, ellipsis: true, tooltip: true, align: 'center'  },
  { title: '计租面积(㎡)', dataIndex: 'rentArea', width: 110, ellipsis: true, tooltip: true, align: 'center'  }
])

// 计算属性：是否为宿舍类型
const isDormitoryType = computed(() => {
  return checkIsDormitory(baseForm.propertyType)
})

// 根据ID获取地块名称
const getParcelName = (parcelId: string): string => {
  if (!parcelId) return ''
  
  const parcel = parcelOptions.value.find(item => String(item.id) === String(parcelId))
  return parcel ? parcel.parcelName || '' : ''
}

// 根据ID获取楼栋名称
const getBuildingName = (buildingId: string): string => {
  if (!buildingId) return ''
  
  const building = buildingOptions.value.find(item => String(item.id) === String(buildingId))
  return building ? building.buildingName || '' : ''
}

// 根据ID获取楼层名称
const getFloorName = (floorId: string | undefined): string => {
  if (!floorId) return ''
  
  const floor = floorOptions.value.find(item => String(item.id) === String(floorId))
  return floor ? floor.floorName || '' : ''
}

// 根据ID获取户型名称
const getHouseTypeName = (houseTypeId: string): string => {
  if (!houseTypeId) return ''
  
  const houseType = houseTypeOptions.value.find(item => String(item.id) === String(houseTypeId))
  return houseType ? houseType.houseTypeName || '' : ''
}

defineExpose({ show })
</script>

<style scoped lang="less">
.add-operation-container {
  display: flex;
  height: 100%;

  .content-section {
    flex: 1;
    padding: 0 20px;
    overflow-y: auto;

    &.full-width {
      width: 100%;
    }
  }

  .scrollable-content {
    overflow-y: auto;
  }

  .section-title {
    margin: 16px 0;
    &.first-child {
      margin-top: 8px;
    }
  }

  .footer-btns {
    display: flex;
    justify-content: flex-end;
    padding: 16px 24px 16px 0;
    background: #fff;
    border-top: 1px solid #f0f0f0;
  }

  .input-link-row {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    .arco-input {
      flex: 1 1 auto;
      min-width: 0;
    }
    .bind-link {
      white-space: nowrap;
      flex-shrink: 0;
    }
  }

  .pricing-info {
    .pricing-row {
      display: flex;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }
    .pricing-item {
      flex: 0 0 33%;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .label {
        color: var(--color-text-3);
        margin-right: 8px;
        white-space: nowrap;
      }
      .value {
        color: var(--color-text-1);
        margin-right: 4px;
      }
      .price-note {
        color: var(--color-text-3);
        font-size: 12px;
      }
      .expand-link {
        margin-left: 16px;
      }
    }
  }

  .select-rooms-modal {
    .search-area {
      margin-bottom: 16px;
    }

    .modal-footer {
      display: flex;
      justify-content: flex-end;
      padding: 16px 24px 16px 0;
      background: #fff;
      border-top: 1px solid #f0f0f0;
    }
  }
}
</style> 