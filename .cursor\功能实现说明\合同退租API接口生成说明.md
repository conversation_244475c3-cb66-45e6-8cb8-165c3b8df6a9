# 合同退租功能实现说明

## 功能概述
根据 `合同退租-接口调用说明` 文档，已完成合同退租页面的前端实现，包括数据获取、表单交互和业务流程。

## 实现的功能模块

### 1. 承租方信息
- **数据来源**: `/terminate/detail` 接口，从 `contract.customer` 中获取
- **实现**: 显示承租方名称、个人/企业类型、合同用途
- **状态**: ✅ 已完成

### 2. 租赁房源
- **数据来源**: `/terminate/detail` 接口，从 `roomList` 中获取
- **功能**: 支持多选房源
- **联动**: 选中房源后自动调用 `/terminate/getBill` 查询预计退款信息
- **状态**: ✅ 已完成

### 3. 基础信息
- **数据来源**: `/terminate/detail` 接口，从 `data` 中获取
- **显示内容**: 合同起止日期、合同总金额、免租期、已收保证金、已收租金、已收款期限、逾期未收租金、逾期期限
- **状态**: ✅ 已完成

### 4. 退租信息
- **数据来源**: `/terminate/detail` 接口，从 `data` 中获取
- **表单字段**: 退租类型、退租日期、退租原因、其他原因说明
- **联动**: 选择退租类型和退租日期后，调用 `/terminate/getBill` 查询预计退款信息
- **状态**: ✅ 已完成

### 5. 免租期是否收费
- **数据来源**: `/terminate/detail` 接口，从 `contract.fees` 中获取
- **功能**: 每个免租期都可以选择是否收费
- **联动**: 
  - 选择"是"时，调用 `/terminate/freeRent` 查询免租期租金
  - 选择"否"时，重新调用账单接口
- **状态**: ✅ 已完成

### 6. 预计退款信息
- **数据来源**: 通过房源选择、退租类型、退租日期联动查询获得
- **功能**: 
  - 显示费项、期限、应收日期、账单金额等信息
  - 罚没金额可编辑输入
  - 自动计算合计金额
- **状态**: ✅ 已完成

### 7. 其他扣款
- **数据来源**: `/terminate/detail` 接口，从 `data` 中获取
- **功能**: 选择是否有其他扣款，可输入扣款金额和说明
- **状态**: ✅ 已完成

### 8. 附件
- **功能**: 使用全局上传组件，支持 word/ppt/excel/txt 格式
- **数据存储**: 附件信息保存在 `terminateAttachments` 字段
- **状态**: ✅ 已完成

### 9. 操作功能
- **暂存**: 调用 `/terminate/save` 接口，`isSubmit` 设为 false
- **提交申请**: 调用 `/terminate/save` 接口，`isSubmit` 设为 true
- **状态**: ✅ 已完成

## 技术实现细节

### API 集成
- ✅ 导入了 `contractTerminate.ts` 中的所有相关接口
- ✅ 实现了 `getContractTerminateDetail` - 获取退租详情
- ✅ 实现了 `getTerminateBill` - 获取账单信息
- ✅ 实现了 `getTerminateFreeRent` - 获取免租期租金
- ✅ 实现了 `saveContractTerminate` - 保存退租信息

### 数据流程
1. **初始化**: 通过 `contractId` 或 `terminateId` 调用详情接口获取数据
2. **房源选择**: 触发账单查询，更新预计退款信息
3. **免租期收费**: 根据选择动态查询免租期租金
4. **表单联动**: 退租类型和日期变化时重新查询账单
5. **数据保存**: 收集所有表单数据，调用保存接口

### 组件结构
- **主组件**: `contractTerminationMain.vue` - 负责步骤控制和参数传递
- **子组件**: `contractTermination.vue` - 退租申请表单的具体实现
- **Props**: 支持通过 `contractId` 和 `terminateId` 传递参数
- **Events**: 支持 `save` 和 `submit` 事件向上传递

### 数据绑定
- ✅ 承租方信息自动填充
- ✅ 房源列表支持多选
- ✅ 基础信息只读显示
- ✅ 退租信息表单双向绑定
- ✅ 免租期收费状态管理
- ✅ 预计退款信息动态计算
- ✅ 其他扣款条件显示
- ✅ 附件上传管理

### 样式优化
- ✅ 使用 `section-title` 组件分割各个功能区域
- ✅ 逾期相关字段使用红色高亮显示
- ✅ 合计信息使用突出的背景色显示
- ✅ 响应式布局适配不同屏幕

## 使用方式

### 新建退租申请
```vue
<contract-termination-main :contract-id="contractId" />
```

### 编辑已有退租申请
```vue
<contract-termination-main :terminate-id="terminateId" />
```

### 路由参数传递
- 支持通过 URL 查询参数传递：`?contractId=xxx` 或 `?terminateId=xxx`

## 注意事项

1. **必填参数**: 需要提供 `contractId` 或 `terminateId` 其中之一
2. **数据联动**: 房源选择、退租类型、退租日期会触发账单重新查询
3. **免租期处理**: 免租期收费状态变化会影响预计退款信息
4. **文件上传**: 目前使用模拟上传，实际使用时需要配置真实的上传接口
5. **表单验证**: 部分必填字段已标记，可根据需要添加更详细的验证规则

## 后续扩展

1. **表单验证**: 可以添加更严格的表单验证规则
2. **文件上传**: 集成真实的文件上传服务
3. **权限控制**: 根据用户角色控制操作权限
4. **打印功能**: 添加退租申请单的打印功能
5. **历史记录**: 显示退租申请的历史操作记录 