import http from './index'

// 字典数据DTO
export interface SysDictDataDTO {
  dictCode?: number
  parentCode?: number
  dictSort?: number
  dictLabel?: string
  dictValue?: string
  dictType?: string
  cssClass?: string
  listClass?: string
  isDefault?: string
  status?: string
  remark?: string
  createTime?: string
  childList?: SysDictDataDTO[]
}

// 固定资产新增/修改DTO
export interface FixedAssetsAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  category?: number
  name: string
  specification?: string
  attachments?: string | null
  usageScope: string
  usageScopeName?: string
  usageScopeList?: SysDictDataDTO[]
  remark?: string
  isDel?: boolean
}

// 固定资产查询DTO
export interface FixedAssetsQueryDTO {
  pageNum: number
  pageSize: number
  category?: number
  name?: string
}

// 固定资产详情VO
export interface FixedAssetsVo {
  id?: string
  category?: number
  name?: string
  specification?: string
  attachments?: string
  usageScope?: string
  usageScopeList?: string[]
  remark?: string
  createByName?: string
  updateByName?: string
  isDel?: boolean
}

// 固定资产导出参数
export interface FixedAssetsExportDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  id?: string
  category?: string
  name?: string
  specification?: string
  attachments?: string
  usageScope?: string
  remark?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  isDel?: number
}

/**
 * 查询固定资产列表
 * @param params 查询参数
 * @returns Promise<FixedAssetsVo[]>
 */
export function getFixedAssetsList(params: FixedAssetsQueryDTO) {
  return http.get<FixedAssetsVo[]>('/business-rent-admin/fixed/assets/list', params)
}

/**
 * 获取固定资产详细信息
 * @param id 固定资产ID
 * @returns Promise<FixedAssetsVo>
 */
export function getFixedAssetsDetail(id: string) {
  return http.get<FixedAssetsVo>('/business-rent-admin/fixed/assets/detail', { id: id })
}

/**
 * 新增固定资产
 * @param data 固定资产数据
 * @returns Promise<any>
 */
export function addFixedAssets(data: FixedAssetsAddDTO) {
  return http.post('/business-rent-admin/fixed/assets', data)
}

/**
 * 修改固定资产
 * @param data 固定资产数据
 * @returns Promise<any>
 */
export function updateFixedAssets(data: FixedAssetsAddDTO) {
  return http.put('/business-rent-admin/fixed/assets', data)
}

/**
 * 删除固定资产
 * @param ids 固定资产ID列表
 * @returns Promise<any>
 */
export function deleteFixedAssets(ids: string) {
  return http.delete('/business-rent-admin/fixed/assets/delete', { ids: ids })
}

/**
 * 导出固定资产列表
 * @param params 导出参数
 * @returns Promise<any>
 */
export function exportFixedAssets(params: FixedAssetsExportDTO) {
  return http.download('/business-rent-admin/fixed/assets/export', params)
} 