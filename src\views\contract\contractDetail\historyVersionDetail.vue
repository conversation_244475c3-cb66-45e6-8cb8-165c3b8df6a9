<template>
    <a-drawer class="common-drawer" v-model:visible="drawerVisible" title="历史版本合同详情" :body-style="{ padding: 0 }">
        <a-card class="flex-card" :bordered="false">
            <div class="wrap">
                <baseInfo v-if="drawerVisible" />
                <detailTabs v-if="drawerVisible" />
            </div>
        </a-card>
    </a-drawer>
</template>

<script setup lang="ts">
import baseInfo from '../contractHistoryDetail/baseInfo.vue'
import detailTabs from '../contractHistoryDetail/detailTabs.vue'
import { useContractStore } from '@/store/modules/contract/index';
import { getContractById, ContractAddDTO, ContractVo, getContractDetailBill } from '@/api/contract';
import customerApi from '@/api/customer'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)

const drawerVisible = ref(false)
const open = (record: any) => {
    contractStore.resetHistoryVersionContractDetail()
    drawerVisible.value = true
    fetchContractDetail(record.id)
}

// 获取合同详情
const fetchContractDetail = async (id?: string) => {
    try {
        const { data } = await getContractById(id || contractData.value.id as string);
        if (data) {
            // 更新合同数据
            const contractInfo = data as ContractVo;

            contractInfo.fees = contractInfo.fees || []
            contractInfo.costs = contractInfo.costs || []
            contractInfo.rooms = contractInfo.rooms || []
            contractInfo.rentReceivableDate = Number(contractInfo.rentReceivableDate)

            // 编辑时直接使用原数据16px
            contractStore.historyVersionContractDetail = { ...contractStore.historyVersionContractDetail, ...contractInfo as any };
            fetchBill()
            fetchLesseeInfo()
        }
    } catch (error) {
        console.error(error);
    }
};

// 获取账单
const fetchBill = async () => {
    const { data } = await getContractDetailBill(contractData.value.id as string)
    contractStore.historyVersionContractDetailCosts = data
}

// 获取承租人信息
const fetchLesseeInfo = async () => {
    const { data } = await customerApi.getCustomerDetail(contractData.value.customer.customerId as string)
    contractStore.historyVersionContractLesseeInfo = data
}

defineExpose({
    open
})
</script>

<style lang="less" scoped>
.arco-card {
    .wrap {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
}
</style>