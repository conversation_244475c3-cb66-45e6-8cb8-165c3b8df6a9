#!/usr/bin/env node

/**
 * 内存诊断脚本
 * 分析和解决 Node.js 内存分配失败问题
 */

const { execSync } = require('child_process');
const os = require('os');
const fs = require('fs');

console.log('🔍 Node.js 内存诊断工具');
console.log('='.repeat(60));

// 分析内存信息
function analyzeMemoryInfo() {
    const totalMemory = Math.round(os.totalmem() / 1024 / 1024 / 1024); // GB
    const freeMemory = Math.round(os.freemem() / 1024 / 1024 / 1024); // GB
    const usedMemory = totalMemory - freeMemory;
    const cpuCount = os.cpus().length;
    
    console.log('\n💻 系统内存分析:');
    console.log(`   总内存: ${totalMemory}GB`);
    console.log(`   已用内存: ${usedMemory}GB (${Math.round(usedMemory/totalMemory*100)}%)`);
    console.log(`   可用内存: ${freeMemory}GB`);
    console.log(`   CPU 核心: ${cpuCount}`);
    
    // 内存建议
    const recommendedMaxMemory = Math.min(Math.floor(totalMemory * 0.7), 16);
    const recommendedSemiSpace = Math.max(Math.floor(recommendedMaxMemory / 8), 1);
    
    console.log('\n📊 推荐配置:');
    console.log(`   最大内存: ${recommendedMaxMemory}GB`);
    console.log(`   半空间: ${recommendedSemiSpace}GB`);
    console.log(`   线程池: ${Math.min(cpuCount * 2, 128)}`);
    
    return { totalMemory, freeMemory, recommendedMaxMemory, recommendedSemiSpace };
}

// 检查Node.js版本和配置
function checkNodeJSConfig() {
    console.log('\n🟢 Node.js 配置检查:');
    
    const nodeVersion = process.version;
    console.log(`   Node.js 版本: ${nodeVersion}`);
    
    // 检查当前内存限制
    try {
        const v8Options = process.execArgv;
        console.log(`   当前启动参数: ${v8Options.join(' ') || '(无特殊参数)'}`);
        
        const currentMemory = process.memoryUsage();
        console.log(`   当前内存使用: ${Math.round(currentMemory.heapUsed / 1024 / 1024)}MB`);
        console.log(`   堆内存总量: ${Math.round(currentMemory.heapTotal / 1024 / 1024)}MB`);
        console.log(`   外部内存: ${Math.round(currentMemory.external / 1024 / 1024)}MB`);
    } catch (error) {
        console.log('   ⚠️  无法获取内存信息');
    }
    
    // 检查是否启用了垃圾回收
    if (global.gc) {
        console.log('   ✅ 垃圾回收已启用 (--expose-gc)');
    } else {
        console.log('   ❌ 垃圾回收未启用 (需要 --expose-gc)');
    }
}

// 分析典型的内存错误模式
function analyzeMemoryErrors() {
    console.log('\n🔍 常见内存问题分析:');
    
    const errorPatterns = [
        {
            pattern: 'allocation failure',
            description: '内存分配失败',
            solutions: [
                '增加 --max-old-space-size 参数',
                '启用 --optimize-for-size 优化内存使用',
                '使用 --memory-reducer 自动释放内存',
                '添加 --gc-interval 定期垃圾回收'
            ]
        },
        {
            pattern: 'Scavenge',
            description: '新生代垃圾回收频繁',
            solutions: [
                '增加 --max-semi-space-size 参数',
                '使用 --incremental-marking 增量标记',
                '启用 --concurrent-marking 并发标记'
            ]
        },
        {
            pattern: 'Mark-Compact',
            description: '老生代垃圾回收压力大',
            solutions: [
                '增加主内存大小',
                '优化代码减少内存泄漏',
                '使用更小的依赖包'
            ]
        }
    ];
    
    errorPatterns.forEach((error, index) => {
        console.log(`\n   ${index + 1}. ${error.description} (${error.pattern}):`);
        error.solutions.forEach(solution => {
            console.log(`      💡 ${solution}`);
        });
    });
}

// 生成优化的启动命令
function generateOptimizedCommands(recommendedMaxMemory, recommendedSemiSpace) {
    console.log('\n⚡ 优化的构建命令:');
    
    const maxMemoryMB = recommendedMaxMemory * 1024;
    const semiSpaceMB = recommendedSemiSpace * 1024;
    
    const baseOptions = [
        `--max-old-space-size=${maxMemoryMB}`,
        `--max-semi-space-size=${semiSpaceMB}`,
        '--expose-gc',
        '--optimize-for-size',
        '--memory-reducer',
        '--gc-interval=100',
        '--incremental-marking',
        '--concurrent-marking'
    ];
    
    const commands = [
        {
            name: '标准模式',
            memory: Math.floor(maxMemoryMB * 0.6),
            semiSpace: Math.floor(semiSpaceMB * 0.6),
            command: 'build:win'
        },
        {
            name: '快速模式',
            memory: Math.floor(maxMemoryMB * 0.8),
            semiSpace: Math.floor(semiSpaceMB * 0.8),
            command: 'build:win:fast'
        },
        {
            name: '超级模式',
            memory: maxMemoryMB,
            semiSpace: semiSpaceMB,
            command: 'build:win:turbo'
        }
    ];
    
    commands.forEach(cmd => {
        const options = [
            `--max-old-space-size=${cmd.memory}`,
            `--max-semi-space-size=${cmd.semiSpace}`,
            ...baseOptions.slice(2)
        ];
        
        console.log(`\n   ${cmd.name} (${Math.round(cmd.memory/1024)}GB):`);
        console.log(`     npm run ${cmd.command}`);
        console.log(`     node ${options.join(' ')} ...`);
    });
}

// 创建内存监控脚本
function createMemoryMonitor() {
    console.log('\n📊 创建内存监控脚本...');
    
    const monitorScript = `
// 内存监控脚本
setInterval(() => {
    const mem = process.memoryUsage();
    const heapUsed = Math.round(mem.heapUsed / 1024 / 1024);
    const heapTotal = Math.round(mem.heapTotal / 1024 / 1024);
    const external = Math.round(mem.external / 1024 / 1024);
    
    console.log(\`📊 内存: 堆 \${heapUsed}/\${heapTotal}MB, 外部 \${external}MB\`);
    
    // 内存使用超过阈值时触发垃圾回收
    if (heapUsed > 2048 && global.gc) {
        console.log('🗑️  触发垃圾回收...');
        global.gc();
    }
}, 10000);
`;
    
    fs.writeFileSync('memory-monitor.js', monitorScript);
    console.log('   ✅ 已创建 memory-monitor.js');
}

// 提供修复建议
function provideFixes() {
    console.log('\n🛠️  内存问题修复建议:');
    
    console.log('\n   1️⃣  立即修复:');
    console.log('      npm run build:win:fast  # 使用优化的内存参数');
    console.log('      或双击 build-windows-fast.bat');
    
    console.log('\n   2️⃣  临时解决方案:');
    console.log('      set NODE_OPTIONS=--max-old-space-size=8192 --expose-gc');
    console.log('      npm run build');
    
    console.log('\n   3️⃣  长期优化:');
    console.log('      - 升级系统内存到16GB+');
    console.log('      - 使用SSD硬盘');
    console.log('      - 定期清理node_modules');
    console.log('      - 关闭不必要的应用程序');
    
    console.log('\n   4️⃣  Windows特定:');
    console.log('      - 添加项目到Windows Defender排除列表');
    console.log('      - 设置虚拟内存(页面文件)');
    console.log('      - 关闭Windows内存压缩');
}

// 主函数
function main() {
    const { recommendedMaxMemory, recommendedSemiSpace } = analyzeMemoryInfo();
    checkNodeJSConfig();
    analyzeMemoryErrors();
    generateOptimizedCommands(recommendedMaxMemory, recommendedSemiSpace);
    createMemoryMonitor();
    provideFixes();
    
    console.log('\n✨ 诊断完成！使用建议的命令应该能解决内存问题。');
    console.log('='.repeat(60));
}

// 运行诊断
if (require.main === module) {
    main();
}

module.exports = { analyzeMemoryInfo, checkNodeJSConfig }; 