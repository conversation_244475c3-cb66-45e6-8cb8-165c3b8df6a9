<template>
    <div class="expand-bill">
        <section>
            <sectionTitle title="扩租前有效账单" :large="true" />
            <a-card :bordered="false" :body-style="{ padding: '0' }">
                <div class="table-container">
                    <sectionTitle class="margin-top-16" title="保证金信息列表" />
                    <div class="table-content">
                        <a-table :data="beforeDepositTableData" :columns="depositColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }">
                            <template #actualReceivable="{ record }">
                                {{ formatAmount(record.actualReceivable) }}
                            </template>
                            <template #receivedAmount="{ record }">
                                {{ formatAmount(record.receivedAmount) }}
                            </template>
                            <template #remainingReceivable="{ record }">
                                {{ formatAmount(record.remainingReceivable) }}
                            </template>
                        </a-table>
                    </div>
                </div>
                <div class="table-container">
                    <sectionTitle title="租金信息列表" />
                    <div class="table-content">
                        <a-table :data="beforeRentTableData" :columns="rentColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }">
                            <template #actualReceivable="{ record }">
                                {{ formatAmount(record.actualReceivable) }}
                            </template>
                            <template #freeRent="{ record }">
                                {{ formatAmount(record.freeRent) }}
                            </template>
                            <template #receivedAmount="{ record }">
                                {{ formatAmount(record.receivedAmount) }}
                            </template>
                        </a-table>
                    </div>
                </div>
            </a-card>
        </section>
        <section class="margin-top-16">
            <sectionTitle title="扩租后最新账单" :large="true" />
            <a-card :bordered="false" :body-style="{ padding: '0' }">
                <div class="table-container">
                    <sectionTitle title="保证金信息列表" />
                    <div class="table-content">
                        <a-table :data="afterDepositTableData" :columns="depositColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }">
                            <template #actualReceivable="{ record }">
                                {{ formatAmount(record.actualReceivable) }}
                            </template>
                            <template #receivedAmount="{ record }">
                                {{ formatAmount(record.receivedAmount) }}
                            </template>
                            <template #remainingReceivable="{ record }">
                                {{ formatAmount(record.remainingReceivable) }}
                            </template>
                        </a-table>
                    </div>
                </div>
                <div class="table-content">
                    <sectionTitle title="租金信息列表" />
                    <div class="table-content">
                        <a-table :data="afterRentTableData" :columns="rentColumns" :bordered="{ cell: true }"
                            :pagination="false" :scroll="{ x: 1200 }">
                            <template #actualReceivable="{ record }">
                                {{ formatAmount(record.actualReceivable) }}
                            </template>
                            <template #freeRent="{ record }">
                                {{ formatAmount(record.freeRent) }}
                            </template>
                            <template #receivedAmount="{ record }">
                                {{ formatAmount(record.receivedAmount) }}
                            </template>
                        </a-table>
                    </div>
                </div>
            </a-card>
        </section>
    </div>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue';
import { ref, computed, watch } from 'vue';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 定义props接收数据
interface Props {
    newCosts?: any[];
    oldCosts?: any[];
    changeCosts?: any[];
    isViewMode?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    newCosts: () => [],
    oldCosts: () => [],
    changeCosts: () => [],
    isViewMode: false
});

const beforeDepositTableData = ref<any[]>([]);
const beforeRentTableData = ref<any[]>([]);
const afterDepositTableData = ref<any[]>([]);
const afterRentTableData = ref<any[]>([]);

const depositColumns = [
    {
        title: '租期',
        dataIndex: 'startDate',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '租户',
        dataIndex: 'customerName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '款项',
        dataIndex: 'subjectName',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '应收日期',
        dataIndex: 'receivableDate',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '账单应收金额（元）',
        dataIndex: 'actualReceivable',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'actualReceivable'
    },
    {
        title: '账单已收金额（元）',
        dataIndex: 'receivedAmount',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'receivedAmount'
    },
    {
        title: '账单剩余应收金额（元）',
        dataIndex: 'remainingReceivable',
        width: 170,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'remainingReceivable'
    }
];

const rentColumns = [
    {
        title: '租期',
        dataIndex: 'startDate',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '租户',
        dataIndex: 'customerName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '款项',
        dataIndex: 'subjectName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '应收日期',
        dataIndex: 'receivableDate',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '账单总额（元）',
        dataIndex: 'totalAmount',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'totalAmount'
    },
    {
        title: '免租优惠（元）',
        dataIndex: 'freeRent',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'freeRent'
    },
    {
        title: '账单已收金额（元）',
        dataIndex: 'receivedAmount',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'receivedAmount'
    },
];

// 处理账单数据，根据period合并
const processBillData = () => {
    console.log('账单组件接收到的数据:', {
        oldCosts: props.oldCosts,
        newCosts: props.newCosts,
        changeCosts: props.changeCosts,
        isViewMode: props.isViewMode
    });
    
    // 扩租前有效账单使用oldCosts
    const oldCostsData = props.oldCosts || [];
    // 扩租后最新账单使用newCosts
    const newCostsData = props.newCosts || [];
    
    console.log('原始oldCosts数据:', oldCostsData);
    console.log('原始newCosts数据:', newCostsData);
    
    // 处理扩租前有效账单
    const beforeGroupedByPeriod = new Map();
    oldCostsData.forEach((cost: any) => {
        console.log('处理扩租前cost数据:', {
            period: cost.period,
            costType: cost.costType,
            actualReceivable: cost.actualReceivable,
            totalAmount: cost.totalAmount,
            discountAmount: cost.discountAmount,
            subjectName: cost.subjectName
        });
        
        const period = cost.period;
        if (!beforeGroupedByPeriod.has(period)) {
            beforeGroupedByPeriod.set(period, {
                period: period,
                startDate: cost.startDate,
                endDate: cost.endDate,
                customerName: cost.customerName,
                depositCosts: [],
                rentCosts: []
            });
        }
        
        const group = beforeGroupedByPeriod.get(period);
        
        // 根据costType分类
        if (cost.costType === 1) { // 保证金
            group.depositCosts.push(cost);
        } else if (cost.costType === 2) { // 租金
            group.rentCosts.push(cost);
        }
    });
    
    // 处理扩租后最新账单
    const afterGroupedByPeriod = new Map();
    newCostsData.forEach((cost: any) => {
        console.log('处理扩租后cost数据:', {
            period: cost.period,
            costType: cost.costType,
            actualReceivable: cost.actualReceivable,
            totalAmount: cost.totalAmount,
            discountAmount: cost.discountAmount,
            subjectName: cost.subjectName
        });
        
        const period = cost.period;
        if (!afterGroupedByPeriod.has(period)) {
            afterGroupedByPeriod.set(period, {
                period: period,
                startDate: cost.startDate,
                endDate: cost.endDate,
                customerName: cost.customerName,
                depositCosts: [],
                rentCosts: []
            });
        }
        
        const group = afterGroupedByPeriod.get(period);
        
        // 根据costType分类
        if (cost.costType === 1) { // 保证金
            group.depositCosts.push(cost);
        } else if (cost.costType === 2) { // 租金
            group.rentCosts.push(cost);
        }
    });
    
    // 转换为扩租前表格数据
    const beforeDepositData: any[] = [];
    const beforeRentData: any[] = [];
    
    beforeGroupedByPeriod.forEach((group: any, period) => {
        // 处理保证金数据
        if (group.depositCosts.length > 0) {
            const depositCost = group.depositCosts[0];
            
            // 直接使用API返回的值，不进行累加计算
            const totalAmount = depositCost.totalAmount || 0;
            const actualReceivable = depositCost.actualReceivable || 0;
            const receivedAmount = depositCost.receivedAmount || 0;
            const remainingReceivable = actualReceivable - receivedAmount;
            
            console.log('扩租前保证金数据处理:', {
                depositCost: depositCost,
                totalAmount,
                actualReceivable,
                receivedAmount,
                remainingReceivable,
                '使用字段': '直接使用API返回的值'
            });
            
            beforeDepositData.push({
                startDate: `${depositCost.startDate} 至 ${depositCost.endDate}`,
                customerName: depositCost.customerName,
                subjectName: depositCost.subjectName || '保证金',
                receivableDate: depositCost.receivableDate,
                totalAmount,
                actualReceivable,
                receivedAmount,
                remainingReceivable
            });
        }
        
        // 处理租金数据
        if (group.rentCosts.length > 0) {
            const rentCost = group.rentCosts[0];
            
            // 直接使用API返回的值，不进行累加计算
            const totalAmount = rentCost.totalAmount || 0;
            const actualReceivable = rentCost.actualReceivable || 0;
            const discountAmount = rentCost.discountAmount || 0; // 使用discountAmount字段作为免租优惠
            const receivedAmount = rentCost.receivedAmount || 0;
            
            console.log('扩租前租金数据处理:', {
                rentCost: rentCost,
                totalAmount,
                actualReceivable,
                discountAmount,
                receivedAmount,
                '使用字段': '直接使用API返回的值，discountAmount用于免租优惠'
            });
            
            beforeRentData.push({
                startDate: `${rentCost.startDate} 至 ${rentCost.endDate}`,
                customerName: rentCost.customerName,
                subjectName: rentCost.subjectName || '租金',
                receivableDate: rentCost.receivableDate,
                totalAmount,
                actualReceivable,
                freeRent: discountAmount, // 使用discountAmount作为免租优惠
                receivedAmount
            });
        }
    });
    
    // 转换为扩租后表格数据
    const afterDepositData: any[] = [];
    const afterRentData: any[] = [];
    
    afterGroupedByPeriod.forEach((group: any, period) => {
        // 处理保证金数据
        if (group.depositCosts.length > 0) {
            const depositCost = group.depositCosts[0];
            
            // 直接使用API返回的值，不进行累加计算
            const totalAmount = depositCost.totalAmount || 0;
            const actualReceivable = depositCost.actualReceivable || 0;
            const receivedAmount = depositCost.receivedAmount || 0;
            const remainingReceivable = actualReceivable - receivedAmount;
            
            console.log('扩租后保证金数据处理:', {
                depositCost: depositCost,
                totalAmount,
                actualReceivable,
                receivedAmount,
                remainingReceivable,
                '使用字段': '直接使用API返回的值'
            });
            
            afterDepositData.push({
                startDate: `${depositCost.startDate} 至 ${depositCost.endDate}`,
                customerName: depositCost.customerName,
                subjectName: depositCost.subjectName || '保证金',
                receivableDate: depositCost.receivableDate,
                totalAmount,
                actualReceivable,
                receivedAmount,
                remainingReceivable
            });
        }
        
        // 处理租金数据
        if (group.rentCosts.length > 0) {
            const rentCost = group.rentCosts[0];
            
            // 直接使用API返回的值，不进行累加计算
            const totalAmount = rentCost.totalAmount || 0;
            const actualReceivable = rentCost.actualReceivable || 0;
            const discountAmount = rentCost.discountAmount || 0; // 使用discountAmount字段作为免租优惠
            const receivedAmount = rentCost.receivedAmount || 0;
            
            console.log('扩租后租金数据处理:', {
                rentCost: rentCost,
                totalAmount,
                actualReceivable,
                discountAmount,
                receivedAmount,
                '使用字段': '直接使用API返回的值，discountAmount用于免租优惠'
            });
            
            afterRentData.push({
                startDate: `${rentCost.startDate} 至 ${rentCost.endDate}`,
                customerName: rentCost.customerName,
                subjectName: rentCost.subjectName || '租金',
                receivableDate: rentCost.receivableDate,
                totalAmount,
                actualReceivable,
                freeRent: discountAmount, // 使用discountAmount作为免租优惠
                receivedAmount
            });
        }
    });
    
    // 更新表格数据
    beforeDepositTableData.value = beforeDepositData;
    beforeRentTableData.value = beforeRentData;
    afterDepositTableData.value = afterDepositData;
    afterRentTableData.value = afterRentData;
};

// 监听props变化，重新处理数据
watch(() => [props.newCosts, props.oldCosts, props.changeCosts], () => {
    processBillData();
}, { deep: true, immediate: true });
</script>

<style lang="less" scoped>
section+section {
    margin-top: 16px;
}

.table-container {
    margin-top: 16px;
}

.table-content {
    margin-top: 16px;
}

sectionTitle+.table-content,
.table-content+.table-content {
    margin-top: 16px;
}
</style>