import http from './index'

const systemUrl = '/business-rent-admin'

// 合同规则数据类型
export interface ContractRulesAddDTO {
    id?: string // 主键ID
    type?: number // 类型（1集团，2项目）
    projectId?: string // 项目ID
    projectName?: string // 项目名称
    signDateRange?: number // 签订日期固化(1不固化 2当月固化 3自定义)
    days?: number // 签订日期天数，当前日期+-days=签订日期可选范围
    priceDigit?: number // 单价位数(0元 1角 2分)
    totalDigit?: number // 总价位数(0元 1角 2分)
    areaDigit?: number // 面积保留位(默认保留2位)
    dealType?: number // 成交总价进位方式(1四舍五入 2直接舍去 3直接进位)
    isDel?: boolean // 0-否,1-是
}

// 通用返回结果
export interface AjaxResult {
    error?: boolean
    success?: boolean
    warn?: boolean
    empty?: boolean
    [key: string]: any
}

// 合同规则查询参数
export interface ContractRulesQueryParams {
    type?: number // 类型（1集团，2项目）
    projectId?: string // 项目ID
    pageNum?: number // 页码
    pageSize?: number // 每页大小
}

/**
 * 新增合同规则
 * @param data 合同规则数据
 * @returns Promise<AjaxResult>
 */
export function addContractRules(data: ContractRulesAddDTO): Promise<AjaxResult> {
    return http.post<AjaxResult>(`${systemUrl}/contractRules`, data)
}

/**
 * 修改合同规则
 * @param data 合同规则数据
 * @returns Promise<AjaxResult>
 */
export function updateContractRules(data: ContractRulesAddDTO): Promise<AjaxResult> {
    return http.put<AjaxResult>(`${systemUrl}/contractRules`, data)
}

/**
 * 获取合同规则详细信息
 * @param params 查询参数
 * @returns Promise<AjaxResult>
 */
export function getContractRulesDetail(params: ContractRulesQueryParams): Promise<AjaxResult> {
    return http.get<AjaxResult>(`${systemUrl}/contractRules/detail`, params)
}
