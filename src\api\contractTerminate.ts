import http from './index'

const terminateUrl = '/business-rent-admin/terminate'

// 合同退租相关类型定义

// 出场费用结算列表
export interface ExitCostVo {
    id?: string
    exitId?: string
    costId?: string
    startDate?: string
    endDate?: string
    payType?: number
    subjectId?: string
    subjectName?: string
    receivableDate?: string
    amount?: number
    type?: number
    createByName?: string
    updateByName?: string
    isDel?: boolean
}

// 出场信息新增DTO
export interface ExitAddDTO {
    id?: string
    projectId?: string
    contractId?: string
    contractNo?: string
    contractUnionId?: string
    terminateId?: string
    refundId?: string
    customerId?: string
    customerName?: string
    processType?: number
    progressStatus?: number
    isDiscount?: boolean
    discountAmount?: number
    discountReason?: string
    finalAmount?: number
    refundProcessType?: number
    payeeName?: string
    payeeAccount?: string
    bankName?: string
    licenseStatus?: number
    taxCertStatus?: number
    refundApplyType?: number
    signType?: number
    signAttachments?: string
    signTime?: string
    copyTime?: string
    copyBy?: string
    copyByName?: string
    settleTime?: string
    settleBy?: string
    settleByName?: string
    exitCostList?: ExitCostVo[]
    isSubmit?: boolean
    isDel?: boolean
}

// 退租及出场信息DTO
export interface ContractTerminateWithExitDTO {
    terminateAddDTO?: ContractTerminateAddDTO
    exitAddDTO?: ExitAddDTO
}

// 退租房间新增DTO
export interface ContractTerminateRoomAddDTO {
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    id?: string
    contractId?: string
    terminateId?: string
    roomId?: string
    roomName?: string
    area?: number
    isDel?: boolean
}

// 退租费用新增DTO
export interface ContractTerminateCostAddDTO {
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    id?: string
    contractId?: string
    terminateId?: string
    costId?: string
    costType?: number
    startDate?: string
    endDate?: string
    customerId?: string
    customerName?: string
    roomId?: string
    roomName?: string
    subjectId?: string
    subjectName?: string
    receivableDate?: string
    totalAmount?: number
    discountAmount?: number
    actualReceivable?: number
    terminateReceivable?: number
    receivedAmount?: number
    penaltyAmount?: number
    refundAmount?: number
    freeId?: number
    isDel?: boolean
    contractCostId?: string
}

// 合同费用新增DTO
export interface ContractFeeAddDTO {
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    id?: string
    contractId?: string
    feeType?: number
    freeType?: number
    freeRentMonth?: number
    freeRentDay?: number
    startDate?: string
    endDate?: string
    isCharge?: boolean
    remark?: string
    isDel?: boolean
}

// 退租信息新增DTO
export interface ContractTerminateAddDTO {
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    id?: string
    contractId?: string
    unionId?: string
    approveStatus?: number
    processId?: string
    isExit?: boolean
    isPart?: boolean
    bondReceivedAmount?: number
    rentReceivedAmount?: number
    rentOverdueAmount?: number
    receivedPeriod?: string
    overduePeriod?: string
    terminateType?: number
    terminateDate?: string
    terminateReason?: string
    otherReasonDesc?: string
    hasOtherDeduction?: boolean
    otherDeductionDesc?: string
    terminateRemark?: string
    terminateAttachments?: string
    signAttachments?: string
    isDel?: boolean
    roomList?: ContractTerminateRoomAddDTO[]
    costList?: ContractTerminateCostAddDTO[]
    feeList?: ContractFeeAddDTO[]
    isSubmit?: boolean
}

// 退租初始化DTO
export interface ContractTerminateInitDto {
    freeRentIds?: string[]
    roomIds?: string[]
    contractId?: string
    terminateType?: number
    terminateDate?: string
}

// 退租费用VO
export interface ContractTerminateCostVo {
    id?: string
    contractId?: string
    terminateId?: string
    costId?: string
    costType?: number
    startDate?: string
    endDate?: string
    customerId?: string
    customerName?: string
    roomId?: string
    roomName?: string
    subjectId?: string
    subjectName?: string
    receivableDate?: string
    totalAmount?: number
    discountAmount?: number
    actualReceivable?: number
    terminateReceivable?: number
    receivedAmount?: number
    penaltyAmount?: number
    refundAmount?: number
    freeId?: number
    createByName?: string
    updateByName?: string
    isDel?: boolean
    contractCostId?: string
    period?: string
    billAmount?: number
    canEditPenalty?: boolean
    maxPenaltyAmount?: number
}

// 退租房间VO
export interface ContractTerminateRoomVo {
    id?: string
    contractId?: string
    terminateId?: string
    roomId?: string
    roomName?: string
    area?: number
    createByName?: string
    updateByName?: string
    isDel?: boolean
}

// 合同客户信息VO
export interface ContractCustomerVo {
    id?: string
    contractId?: string
    customerId?: string
    customerType?: number
    customerName?: string
    address?: string
    phone?: string
    idType?: string
    idNumber?: string
    isEmployee?: boolean
    creditCode?: string
    contactName?: string
    contactPhone?: string
    contactIdNumber?: string
    legalName?: string
    paymentAccount?: string
    guarantorName?: string
    guarantorPhone?: string
    guarantorIdType?: string
    guarantorIdNumber?: string
    guarantorAddress?: string
    guarantorIdFront?: string
    guarantorIdBack?: string
    invoiceTitle?: string
    invoiceTaxNumber?: string
    invoiceAddress?: string
    invoicePhone?: string
    invoiceBankName?: string
    invoiceAccountNumber?: string
    createByName?: string
    updateByName?: string
    isDel?: boolean
}

// 合同定单VO
export interface ContractBookingVo {
    id?: string
    contractId?: string
    bookingId?: string
    bookedRoom?: string
    bookerName?: string
    bookingReceivedAmount?: number
    bookingPaymentDate?: string
    transferBondAmount?: number
    transferRentAmount?: number
    createByName?: string
    updateByName?: string
    isDel?: boolean
}

// 合同费用VO
export interface ContractFeeVo {
    id?: string
    contractId?: string
    feeType?: number
    freeType?: number
    freeRentMonth?: number
    freeRentDay?: number
    startDate?: string
    endDate?: string
    isCharge?: boolean
    remark?: string
    createByName?: string
    updateByName?: string
    isDel?: boolean
}

// 合同应收VO
export interface ContractCostVo {
    id?: string
    contractId?: string
    costType?: number
    startDate?: string
    endDate?: string
    period?: number
    customerId?: string
    customerName?: string
    roomId?: string
    roomName?: string
    area?: number
    subjectId?: string
    subjectName?: string
    receivableDate?: string
    unitPrice?: number
    priceUnit?: number
    totalAmount?: number
    discountAmount?: number
    actualReceivable?: number
    receivedAmount?: number
    isRevenue?: boolean
    isDiscount?: boolean
    percentageType?: number
    fixedPercentage?: number
    stepPercentage?: string
    createByName?: string
    updateByName?: string
    isDel?: boolean
    contractStartDate?: string
    rentTicketPeriod?: number
}

// 合同房源VO
export interface ContractRoomVo {
    id?: string
    contractId?: string
    roomId?: string
    roomName?: string
    buildingName?: string
    floorName?: string
    parcelName?: string
    stageName?: string
    area?: number
    standardUnitPrice?: number
    bottomPrice?: number
    priceUnit?: number
    discount?: number
    signedUnitPrice?: number
    signedMonthlyPrice?: number
    startDate?: string
    endDate?: string
    bondPriceType?: number
    bondPrice?: number
    createByName?: string
    updateByName?: string
    isDel?: boolean
}

// 合同VO
export interface ContractVo {
    id?: string
    projectId?: string
    projectName?: string
    contractNo?: string
    unionId?: string
    version?: string
    isCurrent?: boolean
    isLatest?: boolean
    status?: number
    statusTwo?: number
    approveStatus?: number
    operateType?: number
    contractType?: number
    ourSigningParty?: string
    customerName?: string
    unenterNum?: number
    signWay?: number
    signType?: number
    originId?: string
    changeFromId?: string
    landUsage?: string
    signerId?: string
    signerName?: string
    ownerId?: string
    ownerName?: string
    contractMode?: number
    paperContractNo?: string
    signDate?: string
    handoverDate?: string
    contractPurpose?: number
    dealChannel?: number
    assistantId?: string
    assistantName?: string
    rentYear?: number
    rentMonth?: number
    rentDay?: number
    startDate?: string
    endDate?: string
    effectDate?: string
    bookingRelType?: number
    bondReceivableDate?: string
    bondReceivableType?: number
    bondPriceType?: number
    bondPrice?: number
    chargeWay?: number
    rentReceivableDate?: string
    rentReceivableType?: number
    rentTicketPeriod?: number
    rentPayPeriod?: number
    increaseGap?: number
    increaseRate?: number
    increaseRule?: string
    estimateRevenue?: number
    percentageType?: number
    fixedPercentage?: number
    stepPercentage?: string
    revenueType?: number
    isIncludePm?: boolean
    pmUnitPrice?: number
    pmMonthlyPrice?: number
    totalPrice?: number
    bizTypeId?: string
    bizTypeName?: string
    lesseeBrand?: string
    businessCategory?: string
    openDate?: string
    fireRiskCategory?: number
    sprinklerSystem?: number
    factoryEngaged?: string
    deliverDate?: string
    parkingSpaceType?: number
    hasParkingFee?: boolean
    parkingFeeAmount?: number
    venueDeliveryDate?: string
    venueLocation?: string
    dailyActivityStartTime?: string
    dailyActivityEndTime?: string
    venuePurpose?: string
    otherInfo?: string
    contractAttachments?: string
    signAttachments?: string
    attachmentsPlan?: string
    isUploadSignature?: boolean
    changeType?: string
    processId?: string
    changeDate?: string
    changeAttachments?: string
    isSignatureConfirm?: boolean
    isPaperConfirm?: boolean
    isFinish?: boolean
    createByName?: string
    createTime?: string
    updateByName?: string
    updateTime?: string
    isDel?: boolean
    roomName?: string
    terminateDate?: string
    customer?: ContractCustomerVo
    bookings?: ContractBookingVo[]
    fees?: ContractFeeVo[]
    costs?: ContractCostVo[]
    rooms?: ContractRoomVo[]
}

// 合同退租申请信息VO
export interface ContractTerminateVo {
    id?: string
    contractId?: string
    unionId?: string
    approveStatus?: number
    processId?: string
    isExit?: boolean
    isPart?: boolean
    bondReceivedAmount?: number
    rentReceivedAmount?: number
    rentOverdueAmount?: number
    receivedPeriod?: string
    overduePeriod?: string
    terminateType?: number
    terminateDate?: string
    terminateReason?: string
    otherReasonDesc?: string
    hasOtherDeduction?: boolean
    otherDeductionDesc?: string
    terminateRemark?: string
    terminateAttachments?: string
    signAttachments?: string
    createByName?: string
    updateByName?: string
    isDel?: boolean
    roomList?: ContractTerminateRoomVo[]
    costList?: ContractTerminateCostVo[]
    contract?: ContractVo
}

// OA通知DTO
export interface OaNotifyDto {
    processId?: string
    state?: string
    act?: string
    data?: string
    stateCode?: number
}

// API 接口

/**
 * 保存退租信息
 * @param data 退租信息数据
 * @returns Promise<string>
 */
export function saveContractTerminate(data: ContractTerminateAddDTO) {
    return http.post<string>(`${terminateUrl}/save`, data)
}

/**
 * 查询应收退账单列表
 * @param data 查询参数
 * @returns Promise<ContractTerminateCostVo[]>
 */
export function getTerminateBill(data: ContractTerminateInitDto) {
    return http.post<ContractTerminateCostVo[]>(`${terminateUrl}/getBill`, data)
}

/**
 * 查询免租期租金
 * @param data 查询参数
 * @returns Promise<ContractTerminateCostVo[]>
 */
export function getTerminateFreeRent(data: ContractTerminateInitDto) {
    return http.post<ContractTerminateCostVo[]>(`${terminateUrl}/freeRent`, data)
}

/**
 * 取消退租
 * @param id 退租ID
 * @returns Promise<boolean>
 */
export function cancelContractTerminate(id: string) {
    return http.post<boolean>(`${terminateUrl}/cancel/${id}`)
}

/**
 * 退租审批回调
 * @param data OA通知数据
 * @returns Promise<boolean>
 */
export function approveContractTerminate(data: OaNotifyDto) {
    return http.post<boolean>(`${terminateUrl}/approve`, data)
}

/**
 * 查询退租详情
 * @param params 查询参数
 * @returns Promise<ContractTerminateVo>
 */
export function getContractTerminateDetail(params: { id?: string; contractId?: string }) {
    return http.get<ContractTerminateVo>(`${terminateUrl}/detail`, params)
}

/**
 * 查询退租出场详情
 * @param id 退租ID
 * @param isEdit 是否编辑模式
 * @returns Promise<ContractTerminateVo>
 */
export function getContractTerminateExitDetail(id: string, isEdit: boolean = false) {
    return http.get<ContractTerminateVo>(`${terminateUrl}/exitDetail`, { id, isEdit })
}

/**
 * 保存退租及出场信息
 * @param data 退租及出场信息数据
 * @returns Promise<string>
 */
export function saveContractTerminateWithExit(data: ContractTerminateWithExitDTO) {
    return http.post<string>(`${terminateUrl}/saveWithExit`, data)
} 