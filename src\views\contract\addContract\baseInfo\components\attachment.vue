<template>
    <section>
        <sectionTitle title="附件" />
        <div class="content">
            <a-form ref="formRef" :model="contractData" :rules="isDetailMode ? {} : rules" auto-label-width>
                <a-grid :cols="1" :col-gap="16" :row-gap="0">
                    <a-grid-item>
                        <a-form-item label="上传附件">
                            <upload-file v-model="contractData.contractAttachments" :readonly="isAttachmentDisabled" />
                        </a-form-item>
                        <a-form-item
                            v-if="contractData.contractType === 0 && ['商铺', '中央食堂', '厂房', '综合体', '办公'].includes(contractPurposeLabel)"
                            label="平面图" field="attachmentsPlan">
                            <upload-image v-model="contractData.attachmentsPlan" :readonly="isPlanDisabled" />
                        </a-form-item>
                    </a-grid-item>
                </a-grid>
            </a-form>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import uploadFile from '@/components/upload/uploadFile.vue'
import uploadImage from '@/components/upload/uploadImage.vue'
import { useContractStore } from '@/store/modules/contract'
import { FormInstance } from '@arco-design/web-vue';
import { getDictLabel } from '@/dict'

const contractPurposeLabel = computed(() => {
    return getDictLabel('contract_purpose', Number(contractData.value.contractPurpose))
})


const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType)
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo')

// 上传附件禁用逻辑：详情模式 或者 变更模式且包含费用条款变更（changeType 包含 3）
const isAttachmentDisabled = computed(() => {
    if (isDetailMode.value) {
        return true
    }
    // 变更模式且包含费用条款变更时，上传附件不允许修改
    if (editType.value === 'change' && changeType.value.includes(3)) {
        return true
    }
    if (editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo') {
        return true
    }
    return false
})

// 平面图禁用逻辑：详情模式 或者 变更模式且包含费用条款变更（changeType 包含 3）或条款变更（changeType 包含 2）
const isPlanDisabled = computed(() => {
    return isDetailMode.value || (editType.value === 'change' && changeType.value.includes(3)) || (editType.value === 'change' && changeType.value.includes(2)) || editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo'
})

const rules = {
    // attachmentsPlan: [
    //     { required: true, message: '请上传附件' }
    // ]
}

const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            reject()
        } else {
            resolve(true)
        }
    })
}

defineExpose({
    validate
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.detail-text {
    padding: 4px 0;
    color: #333;
}
</style>