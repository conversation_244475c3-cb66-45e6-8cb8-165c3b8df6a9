<template>
  <a-select v-model="selectedProjectId" placeholder="请选择项目" allow-clear :loading="loading" :multiple="multiple"
    :mode="multiple ? 'multiple' : undefined" allow-search @change="handleProjectChange" v-bind="$attrs">
    <a-option v-for="item in projectList" :key="item.id" :value="item.id" :label="item.projectName">
      {{ item.projectName }}
    </a-option>
  </a-select>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import useProjectStore from '@/store/modules/project'
import { getProjectSelectList, type ProjectVo } from '@/api/asset/projectManage' // 使用项目管理的项目选择列表API

interface Props {
  modelValue?: string | string[]
  multiple?: boolean
}

interface ProjectOption extends ProjectVo {
  projectName: string
}

const emit = defineEmits(['update:modelValue', 'change'])

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  multiple: false
})

const projectStore = useProjectStore()

const loading = ref(false)
const projectList = ref<ProjectOption[]>([])
const getProjectListData = async () => {
  loading.value = true
  try {
    const res = await getProjectSelectList()
    if (res.data && res.data.length > 0) {
      projectList.value = res.data.map((item: ProjectVo) => {
        return {
          ...item,
          projectName: item.mdmName || item.name || ''
        }
      })
    } else {
      projectList.value = []
    }
  } catch (error) {
    projectList.value = []
  } finally {
    loading.value = false
    // 设置默认值
    if (projectList.value.length > 0) {
      if (props.multiple) {
        // 多选模式下的默认值处理
        if (!selectedProjectId.value || (Array.isArray(selectedProjectId.value) && selectedProjectId.value.length === 0)) {
          const defaultValue = projectStore.assetProjectIdList
          selectedProjectId.value = defaultValue
          // projectStore.setAssetProjectIdList(defaultValue)
          emit('update:modelValue', defaultValue)
          const projects = projectList.value.filter(item => defaultValue.includes(item.id))
          emit('change', defaultValue, projects)
        }
      } else {
        // 单选模式下的默认值处理
        selectedProjectId.value = projectStore.assetProjectId ?? projectList.value[0].id
        const project = projectList.value.find(item => item.id === selectedProjectId.value)
        projectStore.setAssetProjectId(selectedProjectId.value as string)
        emit('update:modelValue', selectedProjectId.value)
        emit('change', selectedProjectId.value, project)
      }
    } else {
      const emptyValue = props.multiple ? [] : ''
      selectedProjectId.value = emptyValue
      if (!props.multiple) {
        projectStore.setAssetProjectId('')
      } else {
        // projectStore.setAssetProjectIdList([])
      }
      emit('update:modelValue', emptyValue)
      emit('change', emptyValue, props.multiple ? [] : null)
    }
  }
}

const selectedProjectId = ref<string | string[]>(props.multiple ? [] : '')

// 处理项目选择变化
const handleProjectChange = (value: string | string[]) => {
  if (props.multiple) {
    // 多选模式
    const projectIds = value as string[]
    const projects = projectList.value.filter(item => projectIds.includes(item.id))
    // 多选模式下，将第一个选中的项目ID存储到store中
    // if (projectIds.length > 0) {
    //   projectStore.setAssetProjectIdList(projectIds)
    // } else {
    //   projectStore.setAssetProjectIdList([])
    // }
    emit('update:modelValue', projectIds)
    emit('change', projectIds, projects)
  } else {
    // 单选模式
    const projectId = value as string
    if (!!projectId) {
      projectStore.setAssetProjectId(projectId)
    }
    const project = projectList.value.find(item => item.id === projectId)
    emit('update:modelValue', projectId)
    emit('change', projectId, project)
  }
}

// 筛选选项函数
const filterOption = (inputValue: string, option: any) => {
  const projectName = option.label || ''
  return projectName.toLowerCase().includes(inputValue.toLowerCase())
}

// 组件挂载时加载项目列表
onMounted(() => {
  getProjectListData()
})

watch(() => props.modelValue, (newValue) => {
  if (newValue !== selectedProjectId.value) {
    selectedProjectId.value = newValue || (props.multiple ? [] : '')
  }
}, {
  immediate: true
})

watch(() => props.multiple, (newMultiple) => {
  // 当multiple属性变化时，重置选中值
  if (newMultiple) {
    selectedProjectId.value = Array.isArray(selectedProjectId.value) ? selectedProjectId.value :
      (selectedProjectId.value ? [selectedProjectId.value] : [])
  } else {
    selectedProjectId.value = Array.isArray(selectedProjectId.value) ?
      (selectedProjectId.value.length > 0 ? selectedProjectId.value[0] : '') : selectedProjectId.value
  }
}, {
  immediate: true
})
</script>

<style scoped lang="less">
// 组件样式可以根据需要添加</style>