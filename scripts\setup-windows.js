#!/usr/bin/env node

/**
 * Windows 环境构建优化设置脚本
 * 自动检测和配置 Windows 系统的最佳构建环境
 */

const { execSync } = require('child_process');
const os = require('os');
const fs = require('fs');
const path = require('path');

console.log('🪟 Windows 构建环境优化设置');
console.log('='.repeat(50));

// 检测系统信息
function detectSystemInfo() {
    const totalMemory = Math.round(os.totalmem() / 1024 / 1024 / 1024);
    const cpuCores = os.cpus().length;
    const nodeVersion = process.version;
    const platform = os.platform();
    const arch = os.arch();

    console.log('\n💻 系统信息:');
    console.log(`   操作系统: ${platform} ${arch}`);
    console.log(`   CPU 核心数: ${cpuCores}`);
    console.log(`   总内存: ${totalMemory}GB`);
    console.log(`   Node.js 版本: ${nodeVersion}`);

    return { totalMemory, cpuCores, nodeVersion };
}

// 优化Windows PowerShell执行策略
function optimizePowerShell() {
    try {
        console.log('\n⚡ 检查 PowerShell 执行策略...');
        const policy = execSync('powershell "Get-ExecutionPolicy"', { encoding: 'utf8' }).trim();
        console.log(`   当前策略: ${policy}`);
        
        if (policy === 'Restricted') {
            console.log('   ⚠️  执行策略受限，可能影响构建性能');
            console.log('   💡 建议在管理员模式下运行: Set-ExecutionPolicy RemoteSigned');
        } else {
            console.log('   ✅ PowerShell 执行策略正常');
        }
    } catch (error) {
        console.log('   ⚠️  无法检查 PowerShell 策略');
    }
}

// 检测和优化Node.js设置
function optimizeNodeJS(totalMemory) {
    console.log('\n🟢 Node.js 内存优化建议:');
    
    const recommendedMemory = Math.min(Math.floor(totalMemory * 0.7), 16);
    
    console.log(`   推荐最大内存: ${recommendedMemory}GB`);
    console.log(`   使用命令: npm run build:win:turbo`);
    
    // 检查npm配置
    try {
        console.log('\n📦 检查 npm 配置...');
        const cacheDir = execSync('npm config get cache', { encoding: 'utf8' }).trim();
        console.log(`   npm 缓存目录: ${cacheDir}`);
        
        // 检查缓存目录是否在SSD上
        const isSystemDrive = cacheDir.startsWith('C:');
        if (isSystemDrive) {
            console.log('   ✅ npm 缓存在系统盘(建议使用SSD)');
        } else {
            console.log('   ⚠️  npm 缓存不在系统盘');
        }
    } catch (error) {
        console.log('   ⚠️  无法检查 npm 配置');
    }
}

// 检测Windows Defender排除设置
function checkWindowsDefender() {
    console.log('\n🛡️  Windows Defender 优化建议:');
    console.log('   建议将以下目录添加到 Windows Defender 排除列表:');
    console.log(`   - ${process.cwd()}`);
    console.log(`   - ${path.join(process.cwd(), 'node_modules')}`);
    console.log(`   - ${path.join(process.cwd(), 'dist')}`);
    console.log(`   - ${path.join(os.homedir(), '.npm')}`);
    console.log('   这将显著提高构建速度');
}

// 创建Windows优化的环境变量文件
function createWindowsEnvFile() {
    console.log('\n📁 创建 Windows 优化环境变量...');
    
    const envContent = `# Windows 构建优化环境变量
# Node.js 性能优化
NODE_OPTIONS=--max-old-space-size=16384
UV_THREADPOOL_SIZE=128

# Windows 特定优化
FORCE_COLOR=1
CI=false
DISABLE_OPENCOLLECTIVE=1

# npm 优化
npm_config_progress=false
npm_config_loglevel=warn
npm_config_maxsockets=20

# Vite 优化
VITE_NODE_OPTIONS=--max-old-space-size=8192
`;

    fs.writeFileSync('.env.windows', envContent);
    console.log('   ✅ 已创建 .env.windows 文件');
}

// 创建Windows批处理文件
function createWindowsBatchFiles() {
    console.log('\n📝 创建 Windows 批处理文件...');
    
    const buildBat = `@echo off
title 万样GPT - Windows快速构建
echo 🚀 开始 Windows 优化构建...
echo.

:: 设置环境变量
set NODE_OPTIONS=--max-old-space-size=16384 --expose-gc
set UV_THREADPOOL_SIZE=128

:: 清理缓存
echo 🧹 清理构建缓存...
if exist dist rmdir /s /q dist
if exist node_modules\\.vite-win rmdir /s /q node_modules\\.vite-win

:: 开始构建
echo 📦 开始构建...
npm run build:win:turbo

:: 显示结果
echo.
echo ✅ 构建完成！
echo 📊 查看 dist 目录获取构建结果
pause
`;

    const fastBuildBat = `@echo off
title 万样GPT - Windows快速构建
echo ⚡ 开始 Windows 快速构建...
echo.

:: 设置环境变量
set NODE_OPTIONS=--max-old-space-size=12288
set UV_THREADPOOL_SIZE=64

:: 开始构建
echo 📦 开始快速构建...
npm run build:win:fast

echo.
echo ✅ 快速构建完成！
pause
`;

    fs.writeFileSync('build-windows.bat', buildBat);
    fs.writeFileSync('build-windows-fast.bat', fastBuildBat);
    
    console.log('   ✅ 已创建 build-windows.bat');
    console.log('   ✅ 已创建 build-windows-fast.bat');
}

// 检测构建瓶颈
function detectBottlenecks() {
    console.log('\n🔍 检测潜在构建瓶颈:');
    
    // 检查磁盘类型
    try {
        const drives = execSync('wmic logicaldisk get size,freespace,caption', { encoding: 'utf8' });
        console.log('   💾 磁盘信息已检测');
    } catch (error) {
        console.log('   ⚠️  无法检测磁盘信息');
    }
    
    // 检查node_modules大小
    const nodeModulesPath = path.join(process.cwd(), 'node_modules');
    if (fs.existsSync(nodeModulesPath)) {
        console.log('   📁 node_modules 存在 - 建议定期清理');
    }
    
    // 检查dist目录
    const distPath = path.join(process.cwd(), 'dist');
    if (fs.existsSync(distPath)) {
        console.log('   📦 发现旧的 dist 目录 - 将在构建时清理');
    }
}

// 主函数
function main() {
    const { totalMemory, cpuCores } = detectSystemInfo();
    
    optimizePowerShell();
    optimizeNodeJS(totalMemory);
    checkWindowsDefender();
    createWindowsEnvFile();
    createWindowsBatchFiles();
    detectBottlenecks();
    
    console.log('\n🎯 Windows 构建优化建议:');
    console.log('   1️⃣  推荐使用: npm run build:win:turbo');
    console.log('   2️⃣  快速构建: npm run build:win:fast');
    console.log('   3️⃣  批处理文件: 双击 build-windows.bat');
    console.log('   4️⃣  将项目目录添加到 Windows Defender 排除列表');
    console.log('   5️⃣  确保项目在 SSD 磁盘上');
    
    console.log('\n✨ Windows 优化设置完成！');
    console.log('='.repeat(50));
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { detectSystemInfo, optimizeNodeJS }; 