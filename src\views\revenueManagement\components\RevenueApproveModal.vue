<template>
	<a-modal v-model:visible="visible" title="营收审核" :width="580" @cancel="handleCancel" @ok="handleOk"
		:confirm-loading="loading">
		<div class="approve-content">
			<!-- 审核列表 -->
			<!-- <div class="approve-list">
				<h4>待审核营收记录 ({{ approveIds.length }} 条)</h4>
				<a-list :data="approveList" :bordered="false">
					<template #item="{ item }">
						<a-list-item class="approve-item">
							<div class="item-info">
								<div class="item-title">
									{{ item.projectName }} - {{ item.contractNo }}
								</div>
								<div class="item-detail">
									<span>{{ item.customerName }}</span>
									<a-divider direction="vertical" />
									<span>{{ item.revenueMonth }}</span>
									<a-divider direction="vertical" />
									<span class="amount">¥{{ formatAmount(item.revenueAmount) }}</span>
								</div>
							</div>
						</a-list-item>
					</template>
				</a-list>
			</div> -->

			<!-- 审核表单 -->
			<a-form ref="formRef" :model="formData" :rules="rules" :label-col-props="{ span: 4 }"
				:wrapper-col-props="{ span: 20 }" layout="horizontal">
				<a-form-item field="opinion" label="审核意见">
					<a-textarea v-model="formData.opinion" placeholder="请输入审核意见（选填）" :max-length="500" show-word-limit
						:auto-size="{ minRows: 3, maxRows: 6 }" />
				</a-form-item>
			</a-form>
		</div>
	</a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
	approveRevenue,
	getRevenueDetail,
	type ContractRevenueApproveDTO,
	type ContractRevenueVO
} from '@/api/revenue'

// 定义emit
const emit = defineEmits<{
	success: []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const approveIds = ref<string[]>([])
const approveList = ref<ContractRevenueVO[]>([])

// 表单引用和数据
const formRef = ref<any>()
const formData = reactive<ContractRevenueApproveDTO>({
	ids: [],
	opinion: ''
})

// 表单验证规则
const rules = {
	// 审核意见为可选项，不需要验证规则
}

// 格式化金额的工具函数
const formatAmount = (amount: number | undefined) => {
	if (!amount) return '0.00'
	return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 方法定义
const open = async (ids: string[]) => {
	visible.value = true
	approveIds.value = [...ids]
	formData.ids = [...ids]
	formData.opinion = ''

	// 加载待审核记录的详细信息
	await loadApproveList(ids)
}

const loadApproveList = async (ids: string[]) => {
	try {
		const promises = ids.map(id => getRevenueDetail(id))
		const responses = await Promise.all(promises)

		approveList.value = responses
			.filter(response => response.data)
			.map(response => response.data)
	} catch (error) {
		Message.error('获取审核记录失败')
		approveList.value = []
	}
}

const handleCancel = () => {
	visible.value = false
	resetForm()
}

const handleOk = async () => {
	try {
		const valid = await formRef.value?.validate()
		if (!valid) {
			loading.value = true

			await approveRevenue(formData)
			Message.success(`成功审核 ${approveIds.value.length} 条记录`)

			visible.value = false
			emit('success')
			resetForm()
		}
	} catch (error) {
		Message.error('审核失败')
	} finally {
		loading.value = false
	}
}

const resetForm = () => {
	approveIds.value = []
	approveList.value = []
	formData.ids = []
	formData.opinion = ''
	formRef.value?.resetFields()
}

// 暴露方法给父组件
defineExpose({
	open
})
</script>

<style scoped>
.approve-content {
	max-height: 500px;
	overflow-y: auto;
}

.approve-list {
	margin-bottom: 24px;
}

.approve-list h4 {
	margin-bottom: 16px;
	color: var(--color-text-1);
	font-weight: 500;
}

.approve-item {
	padding: 8px 0;
	border-bottom: 1px solid var(--color-border-2);
}

.approve-item:last-child {
	border-bottom: none;
}

.item-info {
	width: 100%;
}

.item-title {
	font-weight: 500;
	color: var(--color-text-1);
	margin-bottom: 4px;
}

.item-detail {
	font-size: 12px;
	color: var(--color-text-3);
}

.item-detail .amount {
	color: var(--color-primary-6);
	font-weight: 500;
}

:deep(.arco-list-item-content) {
	padding: 0;
}
</style>