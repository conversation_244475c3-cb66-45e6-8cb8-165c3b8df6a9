<template>
  <div class="icon-body">
    <a-input
      v-model="iconName"
      class="icon-search"
      allow-clear
      placeholder="请输入图标名称"
      @clear="filterIcons"
      @input="filterIcons"
    >
      <template #suffix>
        <icon-search />
      </template>
    </a-input>
    <div class="icon-list">
      <div class="list-container">
        <div 
          v-for="(item, index) in iconList" 
          class="icon-item-wrapper" 
          :key="index" 
          @click="selectedIcon(item)"
        >
          <div :class="['icon-item', { active: activeIcon === item }]">
            <svg-icon :icon-class="item" class-name="icon" style="height: 25px;width: 16px;"/>
            <span>{{ item }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { IconSearch } from '@arco-design/web-vue/es/icon';
import icons from './requireIcons';
import SvgIcon from '../SvgIcon/index.vue';

interface Props {
  activeIcon?: string;
}

const props = defineProps<Props>();

const iconName = ref<string>('');
const iconList = ref<string[]>(icons);
const emit = defineEmits<{
  (e: 'selected', name: string): void;
}>();

function filterIcons(): void {
  iconList.value = icons;
  if (iconName.value) {
    iconList.value = icons.filter(item => item.indexOf(iconName.value) !== -1);
  }
}

function selectedIcon(name: string): void {
  emit('selected', name);
  document.body.click();
}

function reset(): void {
  iconName.value = '';
  iconList.value = icons;
}

defineExpose({
  reset
});
</script>

<style lang="less" scoped>
.icon-body {
  width: 100%;
  padding: 10px;
  
  .icon-search {
    position: relative;
    margin-bottom: 5px;
  }
  
  .icon-list {
    height: 200px;
    overflow: auto;
    
    .list-container {
      display: flex;
      flex-wrap: wrap;
      
      .icon-item-wrapper {
        width: calc(100% / 3);
        height: 25px;
        line-height: 25px;
        cursor: pointer;
        display: flex;
        
        .icon-item {
          display: flex;
          max-width: 100%;
          height: 100%;
          padding: 0 5px;
          
          &:hover {
            background: var(--color-fill-2);
            border-radius: 4px;
          }
          
          .icon {
            flex-shrink: 0;
          }
          
          span {
            display: inline-block;
            vertical-align: -0.15em;
            fill: currentColor;
            padding-left: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        
        .icon-item.active {
          background: var(--color-fill-2);
          border-radius: 4px;
        }
      }
    }
  }
}
</style>