<template>
  <div class="project-org-tree-container">
    <a-tree
      :data="treeData"
      :expanded-keys="expandedKeys"
      :field-names="{ title: 'name', key: 'id' }"
      :selectable="isNodeSelectable"
      @select="handleSelect"
      @expand="handleExpand"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { getOrgTree } from '@/api/org'

// 组织树节点接口定义
interface OrgTreeNode {
  id?: string
  code?: string
  fullName?: string
  name?: string
  orgType?: number
  level?: number
  parentId?: string
  children?: OrgTreeNode[]
}

const expandedKeys = ref<string[]>([])
const originalTreeData = ref<OrgTreeNode[]>([])

const emit = defineEmits<{
  (e: 'change', node: any): void
}>()

// 判断节点是否可选择：只有片区（level=3）和未分配片区（id='-1'）可以选择
const isNodeSelectable = (node: any) => {
  // return node.level === 3 || node.id === '-1'
  return true
}

// 处理树形数据，只显示到第三级，在第二级添加"未分配片区"
const treeData = computed(() => {
  const processedData = JSON.parse(JSON.stringify(originalTreeData.value))
  
  const processNodes = (nodes: OrgTreeNode[], currentLevel: number = 1): OrgTreeNode[] => {
    return nodes.map(node => {
      const newNode = { ...node }
      
      // 只处理到第三级
      if (node.children && node.children.length > 0 && currentLevel < 3) {
        newNode.children = processNodes(node.children, currentLevel + 1)
        
        // 在第二级（区域事业部）添加"未分配片区"
        if (currentLevel === 1 && node.level === 1) {
          newNode.children = [
            ...newNode.children,
            {
              id: '-1',
              name: '未分配片区',
              code: 'unassigned',
              fullName: '未分配片区',
              orgType: 1,
              level: 3,
              parentId: node.id,
              children: []
            }
          ]
        }
      } else if (currentLevel >= 3) {
        // 第三级及以上不显示子节点
        newNode.children = []
      }
      
      return newNode
    })
  }
  
  return processNodes(processedData)
})

// 监听treeData变化，设置默认展开
watch(treeData, (newTreeData) => {
  if (newTreeData.length > 0 && expandedKeys.value.length === 0) {
    const keys: string[] = []
    const collectExpandedKeys = (nodes: OrgTreeNode[], level: number = 1) => {
      nodes.forEach(node => {
        if (level <= 2 && node.id) {
          keys.push(node.id)
        }
        if (node.children && node.children.length > 0 && level < 3) {
          collectExpandedKeys(node.children, level + 1)
        }
      })
    }
    collectExpandedKeys(newTreeData)
    expandedKeys.value = keys
    console.log('设置展开节点:', keys)
  }
}, { immediate: true })

// 处理展开/收起事件
const handleExpand = (keys: string[]) => {
  expandedKeys.value = keys
}

// 加载组织树数据
const loadOrgTree = async () => {
  try {
    const response = await getOrgTree()
    if (response && response.data) {
      originalTreeData.value = response.data
    }
  } catch (error) {
    console.error('加载组织树失败:', error)
    originalTreeData.value = []
  }
}

const handleSelect = (selectedKeys: string[], node: any) => {
  const selectedNode = node.selectedNodes[0]
  console.log('selectedNode', selectedNode)
  console.log('selectedKeys', selectedKeys)
  if (selectedNode) {
    emit('change', {
      id: selectedNode.id,
      name: selectedNode.name,
      code: selectedNode.code,
      fullName: selectedNode.fullName,
      orgType: selectedNode.orgType,
      level: selectedNode.level,
      parentId: selectedNode.parentId
    })
  }
}

onMounted(() => {
  loadOrgTree()
})
</script>

<style scoped lang="less">
.project-org-tree-container {
  height: 100%;
  padding: 16px 12px;
  
  :deep(.arco-tree) {
    height: 100%;
    overflow-y: auto;
  }
  :deep(.arco-tree-node-title-text){
    width: 110px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; 
  }
}
</style> 