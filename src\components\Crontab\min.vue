<template>
    <a-form :model="{}" :label-col-props="{ span: 1 }">
        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="1"> </a-radio>
                <span class="radio-span">分钟，允许的通配符[, - * /]</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="2"> </a-radio>
                <span class="radio-span">周期从</span>
                <a-input-number v-model:model-value="cycle01" :min="0" :max="58" />
                -
                <a-input-number v-model:model-value="cycle02" :min="cycle01 + 1" :max="59" />
                <span class="radio-span">分钟</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="3"> </a-radio>
                <span class="radio-span">从</span>
                <a-input-number v-model:model-value="average01" :min="0" :max="58" />
                <span class="radio-span">分钟开始，每</span>
                <a-input-number v-model:model-value="average02" :min="1" :max="59 - average01" />
                <span class="radio-span">分钟执行一次</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="4"> </a-radio>
                <span class="radio-span">指定</span>
                <a-select v-model:model-value="checkboxList" placeholder="可多选" multiple :max-tag-count="10" allow-clear>
                    <a-option v-for="item in minuteList" :key="item.value" :value="item.value" :label="item.label" />
                </a-select>
            </a-space>
        </a-form-item>
    </a-form>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';

interface CronProps {
    second: string;
    min: string;
    hour: string;
    day: string;
    month: string;
    week: string;
    year: string;
}

interface Props {
    cron: CronProps;
    check: (value: number, min: number, max: number) => number;
}

const emit = defineEmits(['update'])

const props = withDefaults(defineProps<Props>(), {
    cron: () => ({
        second: "*",
        min: "*",
        hour: "*",
        day: "*",
        month: "*",
        week: "?",
        year: "",
    }),
    check: () => (value: number) => value,
});

const radioValue = ref(1);
const cycle01 = ref(0);
const cycle02 = ref(1);
const average01 = ref(0);
const average02 = ref(1);
const checkboxList = ref<number[]>([]);
const checkCopy = ref<number[]>([0]);

const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, 0, 58);
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, 59);
    return `${cycle01.value}-${cycle02.value}`;
});

const averageTotal = computed(() => {
    average01.value = props.check(average01.value, 0, 58);
    average02.value = props.check(average02.value, 1, 59 - average01.value);
    return `${average01.value}/${average02.value}`;
});

const checkboxString = computed(() => {
    return checkboxList.value.join(',');
});

watch(() => props.cron.min, value => changeRadioValue(value));
watch([radioValue, cycleTotal, averageTotal, checkboxString], () => onRadioChange());

function changeRadioValue(value: string): void {
    if (value === '*') {
        radioValue.value = 1;
    } else if (value.indexOf('-') > -1) {
        const indexArr = value.split('-');
        cycle01.value = Number(indexArr[0]);
        cycle02.value = Number(indexArr[1]);
        radioValue.value = 2;
    } else if (value.indexOf('/') > -1) {
        const indexArr = value.split('/');
        average01.value = Number(indexArr[0]);
        average02.value = Number(indexArr[1]);
        radioValue.value = 3;
    } else {
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))];
        radioValue.value = 4;
    }
}

function onRadioChange(): void {
    switch (radioValue.value) {
        case 1:
            emit('update', 'min', '*', 'min');
            break;
        case 2:
            emit('update', 'min', cycleTotal.value, 'min');
            break;
        case 3:
            emit('update', 'min', averageTotal.value, 'min');
            break;
        case 4:
            if (checkboxList.value.length === 0) {
                checkboxList.value.push(checkCopy.value[0]);
            } else {
                checkCopy.value = checkboxList.value;
            }
            emit('update', 'min', checkboxString.value, 'min');
            break;
    }
}

interface MinuteOption {
    label: string;
    value: number;
}

const minuteList = computed(() => {
    const arr: MinuteOption[] = [];
    for (let i = 0; i < 60; i++) {
        arr.push({
            label: String(i),
            value: i,
        });
    }
    return arr;
});
</script>

<style lang="less" scoped>
.arco-input-number {
    margin: 0 0.2rem;
}

.arco-select {
    margin: 0 0.2rem;
    width: 18.8rem;
}

.radio-span {
    flex-shrink: 0;
}
</style>