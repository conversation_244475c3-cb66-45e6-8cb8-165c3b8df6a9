<template>
    <a-card class="flex-card" :bordered="false">
        <div class="wrap">
            <baseInfo />
            <detailTabs />
        </div>
    </a-card>
</template>

<script setup lang="ts">
import baseInfo from './baseInfo.vue'
import detailTabs from './detailTabs.vue'
</script>

<style scoped lang="less">
.arco-card {
    .wrap {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
}
</style>