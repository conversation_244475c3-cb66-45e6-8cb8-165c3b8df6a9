<template>
    <div class="exit-room-form">
        <a-form :model="formData">
            <!-- 出场日期 -->
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="出场日期" :rules="[{ required: true, message: '请选择出场日期' }]" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }">
                        <a-date-picker v-model="formData.exitDate" placeholder="请选择出场日期" :disabled="isFormReadonly" @change="handleExitDateChange" />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="租控管理" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }">
                        <a-select v-model="formData.rentControl" placeholder="请选择" :disabled="isFormReadonly">
                            <a-option :value="1">当前空置已做断电、锁门处理</a-option>
                            <a-option :value="2">二次出租无需断电处理,开门设置更新</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 房间配套情况 -->
            <section-title title="房间配套情况" style="margin-bottom: 16px;">
                <template #right>
                    <a-button v-if="!isFormReadonly" type="primary" size="small" @click="showAssetLibrary">
                    <template #icon>
                        <icon-plus />
                    </template>
                    添加配套
                </a-button>
            </template>
            </section-title>
            <!-- <div class="section-header">
                <span></span>
                <a-button type="primary" size="small" @click="showAssetLibrary">
                    <template #icon>
                        <icon-plus />
                    </template>
                    添加配套
                </a-button>
            </div> -->
            <a-table 
                :data="formData.exitRoomAssetsList" 
                :columns="assetColumns" 
                :pagination="false"
                :bordered="{ cell: true }" 
                row-key="id"
            >
                <template #category="{ record }">
                    {{ getAssetCategoryText(record.category) }}
                </template>
                <template #name="{ record }">
                    {{ record.name }}
                </template>
                <template #specification="{ record }">
                    {{ record.specification }}
                </template>
                <template #count="{ record }">
                    {{ record.count }}
                </template>
                <template #status="{ record }">
                    <a-select v-model="record.status" style="width: 100px" :disabled="isFormReadonly">
                        <a-option :value="1">完好</a-option>
                        <a-option :value="2">损坏</a-option>
                        <a-option :value="3">丢失</a-option>
                    </a-select>
                </template>
                <template #penalty="{ record }">
                    <a-input-number v-model="record.penalty" placeholder="0" :min="0" :disabled="isFormReadonly" @change="handlePenaltyChange(record)"/>
                </template>
                <template #remark="{ record }">
                    <a-input v-model="record.remark" placeholder="说明" :disabled="isFormReadonly" />
                </template>
                <template #operations="{ record }">
                    <a-button 
                        v-if="!isFormReadonly && record.isDel"
                        type="text" 
                        size="mini" 
                        status="danger"
                        @click="removeAsset(record)"
                    >
                        移除
                    </a-button>
                </template>
            </a-table>

            <!-- 房屋其他情况 -->
            <section-title title="房屋其他情况" style="margin-bottom: 16px;" />
            <a-form-item :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }">
                <div class="form-item">
                    <span>门、窗、墙体及其他</span>
                    <a-radio-group v-model="formData.doorWindowStatus" :disabled="isFormReadonly" @change="handleDoorWindowStatusChange">
                        <a-radio :value="1">完好</a-radio>
                        <a-radio :value="2">损坏</a-radio>
                    </a-radio-group>
                    <span                         v-if="formData.doorWindowStatus === 2"              style="margin-left: -30px;"      >，赔偿</span>
                    <a-input-number 
                        v-if="formData.doorWindowStatus === 2"
                        v-model="formData.doorWindowPenalty" 
                        placeholder="赔偿金额" 
                        :min="0" 
                        :disabled="isFormReadonly"
                        style="width: 220px;"
                        @change="handleDoorWindowPenaltyChange"
                    />
                    <span v-if="formData.doorWindowStatus === 2"    >元从押金中扣除</span>
                </div>
            </a-form-item>
            <a-form-item :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }">
                <div class="form-item">
                    <span>钥匙交接</span>
                    <a-radio-group v-model="formData.keyHandoverStatus" :disabled="isFormReadonly" @change="handleKeyHandoverStatusChange">
                        <a-radio :value="1">已交齐</a-radio>
                        <a-radio :value="2">未交齐</a-radio>
                    </a-radio-group>
                    <span v-if="formData.keyHandoverStatus === 2"    style="margin-left: -30px;">，赔偿</span>
                    <a-input-number 
                        v-if="formData.keyHandoverStatus === 2"
                        v-model="formData.keyPenalty" 
                        placeholder="赔偿金额" 
                        :min="0" 
                        :disabled="isFormReadonly"
                        style="width: 220px;"
                        @change="handleKeyPenaltyChange"
                    />
                    <span v-if="formData.keyHandoverStatus === 2"    >元从押金中扣除</span>
                </div>
            </a-form-item>
            <a-form-item :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }">
                <div class="form-item">
                    <span>清洁卫生</span>
                    <a-radio-group v-model="formData.cleaningStatus" :disabled="isFormReadonly" @change="handleCleaningStatusChange">
                        <a-radio :value="1">自行打扫完毕、洁净</a-radio>
                        <a-radio :value="2">保洁及垃圾清理收费</a-radio>
                    </a-radio-group>
                    <span v-if="formData.cleaningStatus === 2"   style="margin-left: -30px;" >，赔偿</span>
                    <a-input-number 
                        v-if="formData.cleaningStatus === 2"
                        v-model="formData.cleaningPenalty" 
                        placeholder="清理费用" 
                        :min="0" 
                        :disabled="isFormReadonly"
                        style="width: 220px;"
                        @change="handleCleaningPenaltyChange"
                    />
                    <span v-if="formData.cleaningStatus === 2"    >元从押金中扣除</span>
                </div>
            </a-form-item>

            <!-- 水电物业费情况 -->
            <section-title title="水电物业费情况" style="margin: 0  0 16px 0;" />
            <a-table :data="utilityData" :columns="utilityColumns" :pagination="false" :bordered="{ cell: true }" :span-method="utilitySpanMethod">
                <template #electric="{ record, rowIndex }">
                    <a-input-number 
                        v-if="rowIndex === 0"
                        v-model="utilityData[rowIndex].elecMeterReading" 
                        :placeholder="rowIndex === 0 ? '电表读数' : '电费金额'"
                        :precision="rowIndex === 0 ? 0 : 2"
                        :min="0"
                        :disabled="isFormReadonly"
                        style="width: 100%"
                    >
                    <template #suffix>
                        <span>{{ rowIndex === 0 ? '度' : '元' }}</span>
                    </template>
                </a-input-number>
                    <a-input-number 
                        v-else
                        v-model="utilityData[rowIndex].elecFee" 
                        :placeholder="rowIndex === 0 ? '电表读数' : '电费金额'"
                        :precision="rowIndex === 0 ? 0 : 2"
                        :min="0"
                        :disabled="isFormReadonly"
                        style="width: 100%"
                    >
                    <template #suffix>
                        <span>{{ rowIndex === 0 ? '度' : '元' }}</span>
                    </template>
                </a-input-number>
                </template>
                <template #coldWater="{ record, rowIndex }">
                    <a-input-number 
                        v-if="rowIndex === 0"
                        v-model="utilityData[rowIndex].coldWaterReading" 
                        :placeholder="rowIndex === 0 ? '冷水表读数' : '冷水费金额'"
                        :precision="rowIndex === 0 ? 0 : 2"
                        :min="0"
                        :disabled="isFormReadonly"
                        style="width: 100%"
                    >
                    <template #suffix>
                        <span>{{ rowIndex === 0 ? '吨' : '元' }}</span>
                    </template>
                </a-input-number>
                    <a-input-number 
                        v-else
                        v-model="utilityData[rowIndex].waterFee" 
                        placeholder="水费金额（冷热水）"
                        :precision="2"
                        :min="0"
                        :disabled="isFormReadonly"
                        style="width: 100%"
                    >
                    <template #suffix>
                        <span>元</span>
                    </template>
                </a-input-number>
                </template>
                <template #hotWater="{ record, rowIndex }">
                    <a-input-number 
                        v-if="rowIndex === 0"
                        v-model="utilityData[rowIndex].hotWaterReading" 
                        :placeholder="rowIndex === 0 ? '热水表读数' : '热水费金额'"
                        :precision="rowIndex === 0 ? 0 : 2"
                        :min="0"
                        :disabled="isFormReadonly"
                        style="width: 100%"
                    >
                    <template #suffix>
                        <span>{{ rowIndex === 0 ? '吨' : '元' }}</span>
                    </template>
                </a-input-number>
                    <!-- 热水表的欠费金额行不显示输入框，因为已经在冷水表列合并显示 -->
                </template>
                <template #propertyFee="{ record, rowIndex }">
                    <a-input-number 
                        v-if="rowIndex === 1"
                        v-model="utilityData[rowIndex].pmFee" 
                        placeholder="物业费金额"
                        :precision="2"
                        :min="0"
                        :disabled="isFormReadonly"
                        style="width: 100%"
                    >
                    <template #suffix>
                        <span>元</span>
                    </template>
                </a-input-number>
                    <span v-else>-</span>
                </template>
            </a-table>

            <!-- 房间照片 -->
            <section-title title="房间照片" style="margin: 16px 0;" />
            <uploadImage 
                v-model="formData.roomPhotos" 
                :limit="5"
                :max-size="10"
                accept="image/*"
                :disabled="isFormReadonly"
                @success="handlePhotoSuccess"
                @error="handlePhotoError"
            />

            <!-- 固定资产、设备设施评估情况 - 只有商铺或综合体才显示 -->
            <template v-if="shouldShowAssetsEvaluation">
                <section-title title="固定资产、设备设施评估情况" style="margin: 16px 0;" />
                <a-textarea 
                    v-model="formData.assetsSituation" 
                    placeholder="请输入评估情况"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    :disabled="isFormReadonly"
                />
            </template>

            <!-- 备注 -->
            <section-title title="备注" style="margin: 16px 0;" />
            <a-textarea 
                v-model="formData.remark" 
                placeholder="请输入备注信息"
                :auto-size="{ minRows: 3, maxRows: 6 }"
                :disabled="isFormReadonly"
            />

            <!-- 物业确认签字情况 -->
            <section-title title="物业确认签字情况" style="margin: 16px 0;" />
            <div class="signature-info">
                <div class="signature-item" v-if="formData.financeConfirmBy === '1'">
                    <span style="min-width: 120px;">
                        <a-tag color="blue" v-if="formData.financeConfirmBy === '1'">综合或财务已确认</a-tag>
                        <a-tag color="gray" v-else>未确认</a-tag>
                    </span>
                    <span style="margin-left: 30px;">{{ formData.financeConfirmByName }}</span>
                    <span style="min-width: 100px;margin-left: 30px;">{{ formData.financeConfirmTime || '未签字' }}</span>
                    <span style="margin-left: 30px;">签名：</span>
                    <img 
                        v-if="financeSignatureUrl" 
                        :src="financeSignatureUrl" 
                        class="signature-image" 
                        alt="财务确认签名"
                    />
                </div>
                <div class="signature-item" v-if="formData.engineeringConfirmBy === '1'">
                    <span style="min-width: 120px;">
                        <a-tag color="blue" v-if="formData.engineeringConfirmBy === '1'">工程或客服已确认</a-tag>
                        <a-tag color="gray" v-else>未确认</a-tag>
                    </span>
                    <span style="margin-left: 30px;">{{ formData.engineeringConfirmByName }}</span>
                    <span style="min-width: 100px;">{{ formData.engineeringConfirmTime || '未签字' }}</span>
                    <span style="margin-left: 30px;">签名：</span>
                    <img 
                        v-if="engineeringSignatureUrl" 
                        :src="engineeringSignatureUrl" 
                        class="signature-image" 
                        alt="工程确认签名"
                    />
                </div>

            </div>

            <!-- 确认状态 -->
            <!-- <div class="confirm-section">
                <a-row :gutter="16">
                    <a-col :span="8">
                        <a-checkbox v-model="formData.isBusinessConfirmed">商服确认</a-checkbox>
                    </a-col>
                    <a-col :span="8">
                        <a-checkbox v-model="formData.isEngineeringConfirmed">工程确认</a-checkbox>
                    </a-col>
                    <a-col :span="8">
                        <a-checkbox v-model="formData.isFinanceConfirmed">财务确认</a-checkbox>
                    </a-col>
                </a-row>
            </div> -->

            <!-- 按钮组 -->
            <div class="form-actions" v-if="!isFormReadonly">
                <a-space>
                    <a-button type="primary" status="success" @click="handleSave(false)">暂存</a-button>
                    <a-button type="primary" @click="handleSave(true)">确认</a-button>
                    
                    <!-- <a-button @click="handleCancel">取消</a-button> -->
                </a-space>
            </div>
        </a-form>

        <!-- 固定资产标准库选择模态框 -->
        <a-modal 
            v-model:visible="assetLibraryVisible" 
            title="选择固定资产" 
            width="800px"
            @ok="handleAssetLibraryConfirm"
            @cancel="handleAssetLibraryCancel"
        >
            <div class="asset-library-content">
                <!-- 搜索框 -->
                <a-row :gutter="16" style="margin-bottom: 16px;">
                    <a-col :span="8">
                        <a-input v-model="assetSearchForm.name" placeholder="请输入物品名称" />
                    </a-col>
                    <a-col :span="8">
                        <a-select v-model="assetSearchForm.category" placeholder="请选择种类" allow-clear>
                            <a-option :value="1">家具</a-option>
                            <a-option :value="2">家电</a-option>
                            <a-option :value="3">办公用品</a-option>
                            <a-option :value="4">其他</a-option>
                        </a-select>
                    </a-col>
                    <a-col :span="8">
                        <a-button type="primary" @click="searchAssets">搜索</a-button>
                    </a-col>
                </a-row>

                <!-- 固定资产列表 -->
                <a-table 
                    :data="assetLibraryData" 
                    :columns="assetLibraryColumns" 
                    :pagination="assetPagination"
                    :loading="assetLoading"
                    row-key="id"
                    @page-change="handleAssetPageChange"
                    @page-size-change="handleAssetPageSizeChange"
                >
                    <template #selection="{ record }">
                        <a-checkbox 
                            :model-value="selectedAssets.some(item => item.id === record.id)"
                            @change="(checked: boolean) => handleAssetSelect(record, checked)"
                        />
                    </template>
                    <template #category="{ record }">
                        {{ getAssetCategoryText(record.category) }}
                    </template>
                </a-table>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconPlus } from '@arco-design/web-vue/es/icon'
import type { ExitRoom, ExitRoomAssets, ExitRoomAddDTO } from '@/types/exit'
import sectionTitle from '@/components/sectionTitle/index.vue'
import uploadImage from '@/components/upload/uploadImage.vue'
import { getFixedAssetsList, type FixedAssetsVo, type FixedAssetsQueryDTO } from '@/api/fixedAssets'
import { useUserStore } from '@/store'

// Props定义
const props = defineProps<{
    room: ExitRoom
    contractPurpose?: number // 合同用途
    allRooms?: ExitRoom[] // 所有房间列表，用于判断是否全部确认
    readonly?: boolean // 是否只读模式
}>()

const emit = defineEmits(['save', 'cancel', 'rooms-confirmed', 'sync-data', 'syncRoomData', 'updatePenaltyAmount'])

// 获取用户信息
const userStore = useUserStore()

// 表单数据
let formData = reactive<ExitRoomAddDTO>({
    id: '',
    exitId: '',
    roomId: '',
    roomName: '',
    propertyType: 0,
    parcelName: '',
    buildingName: '',
    exitDate: '',
    rentControl: 1,
    doorWindowStatus: 1,
    doorWindowPenalty: 0,
    keyHandoverStatus: 1,
    keyPenalty: 0,
    cleaningStatus: 1,
    cleaningPenalty: 0,
    elecMeterReading: 0,
    coldWaterReading: 0,
    hotWaterReading: 0,
    elecFee: 0,
    waterFee: 0,
    pmFee: 0,
    roomPhotos: '',
    assetsSituation: '',
    remark: '',
    isBusinessConfirmed: false,
    businessConfirmBy: '',
    businessConfirmByName: '',
    businessConfirmTime: '',
    isFinanceConfirmed: false,
    financeConfirmBy: '',
    financeConfirmByName: '',
    financeConfirmTime: '',
    financeConfirmSignature: '',
    isEngineeringConfirmed: false,
    engineeringConfirmBy: '',
    engineeringConfirmByName: '',
    engineeringConfirmTime: '',
    engineeringConfirmSignature: '',
    exitRoomAssetsList: [],
    isSubmit: false,
    isDel: false
})

// 资产列表表格列定义
const assetColumns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 60,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ rowIndex }: any) => rowIndex + 1
    },
    {
        title: '种类',
        slotName: 'category',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '物品名称',
        slotName: 'name',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '规格',
        slotName: 'specification',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '数量',
        slotName: 'count',
        width: 80,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '现状',
        slotName: 'status',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '赔偿金(元)',
        slotName: 'penalty',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '说明',
        slotName: 'remark',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 80,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 水电物业费数据
const utilityData = reactive([
    {
        type: '抄表读数',
        elecMeterReading: 0,
        coldWaterReading: 0,
        hotWaterReading: 0,
        elecFee: 0,
        waterFee: 0,
        pmFee: 0
    },
    {
        type: '欠费金额',
        elecFee: 0,
        waterFee: 0,
        pmFee: 0,
    }
])

// 水电物业费表格列定义
const utilityColumns = [
    {
        title: '',
        dataIndex: 'type',
        width: 100
    },
    {
        title: '电表',
        slotName: 'electric',
        width: 150
    },
    {
        title: '冷水表',
        slotName: 'coldWater',
        width: 150
    },
    {
        title: '热水表',
        slotName: 'hotWater',
        width: 150
    },
    {
        title: '物业费',
        slotName: 'propertyFee',
        width: 150
    }
]

// 计算属性：判断是否应该显示固定资产评估情况
// 当房间类型是商铺或综合体时显示
const shouldShowAssetsEvaluation = computed(() => {
    // 根据合同用途判断是否为商铺或综合体
    // 商铺=31，综合体=32（来自字典数据）
    const contractPurpose = props.contractPurpose
    
    return contractPurpose === 31 || contractPurpose === 32
})

// 计算属性：判断表单是否应该为只读状态
// 当外部传入readonly为true或商服已确认时，表单为只读
const isFormReadonly = computed(() => {
    return props.readonly 
    // || formData.isBusinessConfirmed
})

// 计算签名图片URL
const financeSignatureUrl = computed(() => {
    if (!formData.financeConfirmSignature) return ''
    try {
        const signatureData = JSON.parse(formData.financeConfirmSignature)
        return signatureData.fileUrl || ''
    } catch (e) {
        console.error('解析财务签名数据失败:', e)
        return ''
    }
})

const engineeringSignatureUrl = computed(() => {
    if (!formData.engineeringConfirmSignature) return ''
    try {
        const signatureData = JSON.parse(formData.engineeringConfirmSignature)
        return signatureData.fileUrl || ''
    } catch (e) {
        console.error('解析工程签名数据失败:', e)
        return ''
    }
})

// 生命周期
onMounted(() => {
    initFormData()
})

// 监听房间数据变化 - 智能合并，避免覆盖用户输入的数据
const handleRoomChange = () => {
    console.log('props.room变化', props.room)
    if (props.room) {
        // 智能合并：只更新空值或系统字段，保留用户已输入的数据
        const currentFormData = { ...formData }
        
        // 基础房间信息（这些可以覆盖）
        if (props.room.id) currentFormData.id = props.room.id
        if (props.room.exitId) currentFormData.exitId = props.room.exitId
        if (props.room.roomId) currentFormData.roomId = props.room.roomId
        if (props.room.roomName) currentFormData.roomName = props.room.roomName
        if (props.room.propertyType !== undefined) currentFormData.propertyType = props.room.propertyType
        if (props.room.parcelName) currentFormData.parcelName = props.room.parcelName
        if (props.room.buildingName) currentFormData.buildingName = props.room.buildingName
        
        // 确认状态（这些可以覆盖）
        if (props.room.isBusinessConfirmed !== undefined) currentFormData.isBusinessConfirmed = props.room.isBusinessConfirmed
        if (props.room.businessConfirmBy) currentFormData.businessConfirmBy = props.room.businessConfirmBy
        if (props.room.businessConfirmByName) currentFormData.businessConfirmByName = props.room.businessConfirmByName
        if (props.room.businessConfirmTime) currentFormData.businessConfirmTime = props.room.businessConfirmTime
        
        // 其他确认状态字段也可以覆盖
        if (props.room.isFinanceConfirmed !== undefined) currentFormData.isFinanceConfirmed = props.room.isFinanceConfirmed
        if (props.room.financeConfirmBy) currentFormData.financeConfirmBy = props.room.financeConfirmBy
        if (props.room.financeConfirmByName) currentFormData.financeConfirmByName = props.room.financeConfirmByName
        if (props.room.financeConfirmTime) currentFormData.financeConfirmTime = props.room.financeConfirmTime
        
        if (props.room.isEngineeringConfirmed !== undefined) currentFormData.isEngineeringConfirmed = props.room.isEngineeringConfirmed
        if (props.room.engineeringConfirmBy) currentFormData.engineeringConfirmBy = props.room.engineeringConfirmBy
        if (props.room.engineeringConfirmByName) currentFormData.engineeringConfirmByName = props.room.engineeringConfirmByName
        if (props.room.engineeringConfirmTime) currentFormData.engineeringConfirmTime = props.room.engineeringConfirmTime
        
        // 配套资产列表（只有在当前为空时才初始化）
        if (props.room.exitRoomAssetsList && (!currentFormData.exitRoomAssetsList || currentFormData.exitRoomAssetsList.length === 0)) {
            currentFormData.exitRoomAssetsList = [...props.room.exitRoomAssetsList]
        }
        
        // 应用合并后的数据
        Object.assign(formData, currentFormData)
        
        // 如果出场日期为空，设置默认为当前日期
        if (!formData.exitDate) {
            const today = new Date().toISOString().split('T')[0]
            formData.exitDate = today
        }
        
        // 如果清洁卫生状态为空或未设置，默认设置为"自行打扫完毕、洁净"
        if (!formData.cleaningStatus || formData.cleaningStatus === 0) {
            formData.cleaningStatus = 1
        }
    }
}

// 门窗赔偿金变化处理
const handleDoorWindowPenaltyChange = () => {
    const roomId = formData.id || props.room?.id
    if (roomId) {
        console.log('门窗赔偿金发生变化:', formData.doorWindowPenalty, '房间ID:', roomId)
        emit('sync-data', {
            roomId: roomId,
            formData: { ...formData }
        })
        // 触发费用结算更新
        triggerPenaltyUpdate()
    } else {
        console.warn('房间ID为空，无法同步门窗赔偿金数据')
    }
}

// 钥匙赔偿金变化处理
const handleKeyPenaltyChange = () => {
    const roomId = formData.id || props.room?.id
    if (roomId) {
        console.log('钥匙赔偿金发生变化:', formData.keyPenalty, '房间ID:', roomId)
        emit('sync-data', {
            roomId: roomId,
            formData: { ...formData }
        })
        // 触发费用结算更新
        triggerPenaltyUpdate()
    } else {
        console.warn('房间ID为空，无法同步钥匙赔偿金数据')
    }
}

// 清洁费用变化处理
const handleCleaningPenaltyChange = () => {
    const roomId = formData.id || props.room?.id
    if (roomId) {
        console.log('清洁费用发生变化:', formData.cleaningPenalty, '房间ID:', roomId)
        emit('sync-data', {
            roomId: roomId,
            formData: { ...formData }
        })
        // 触发费用结算更新
        triggerPenaltyUpdate()
    } else {
        console.warn('房间ID为空，无法同步清洁费用数据')
    }
}

// 门窗状态变化处理
const handleDoorWindowStatusChange = () => {
    const roomId = formData.id || props.room?.id
    if (roomId) {
        emit('sync-data', {
            roomId: roomId,
            formData: { ...formData }
        })
    }
}

// 钥匙移交状态变化处理
const handleKeyHandoverStatusChange = () => {
    const roomId = formData.id || props.room?.id
    if (roomId) {
        emit('sync-data', {
            roomId: roomId,
            formData: { ...formData }
        })
    }
}

// 清洁状态变化处理
const handleCleaningStatusChange = () => {
    const roomId = formData.id || props.room?.id
    if (roomId) {
        emit('sync-data', {
            roomId: roomId,
            formData: { ...formData }
        })
    }
}

// 房间资产列表变化处理 - 供子组件调用
const handleRoomAssetsChange = () => {
    const roomId = formData.id || props.room?.id
    if (roomId) {
        emit('sync-data', {
            roomId: roomId,
            formData: { ...formData }
        })
        // 触发费用结算更新
        triggerPenaltyUpdate()
    }
}

// 固定资产库相关数据
const assetLibraryVisible = ref(false)
const assetLoading = ref(false)
const assetLibraryData = ref<FixedAssetsVo[]>([])
const selectedAssets = ref<FixedAssetsVo[]>([])

// 固定资产搜索表单
const assetSearchForm = reactive<FixedAssetsQueryDTO>({
    pageNum: 1,
    pageSize: 10,
    category: undefined,
    name: ''
})

// 固定资产分页
const assetPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showPageSize: true,
    showTotal: true,
    pageSizeOptions: ['10', '20', '50', '100']
})

// 固定资产库表格列定义
const assetLibraryColumns = [
    {
        title: '选择',
        slotName: 'selection',
        width: 60,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '种类',
        slotName: 'category',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '物品名称',
        dataIndex: 'name',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '规格',
        dataIndex: 'specification',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    // {
    //     title: '使用范围',
    //     dataIndex: 'usageScope',
    //     width: 120,
    //     align: 'center',
    //     ellipsis: true,
    //     tooltip: true,
    // },
    // {
    //     title: '备注',
    //     dataIndex: 'remark',
    //     width: 150,
    //     align: 'center',
    //     ellipsis: true,
    //     tooltip: true,
    // }
]

// 初始化表单数据
const initFormData = () => {
    // 
    console.log('initFormData', props.room)
    if (props.room) {
        //doorWindowStatus

        let _room = JSON.parse(JSON.stringify(props.room))

        // 状态
        // _room.doorWindowStatus = formData.doorWindowStatus || 0
        // _room.doorWindowPenalty = formData.doorWindowPenalty || 0
        // _room.keyHandoverStatus = formData.keyHandoverStatus || 0
        // _room.keyPenalty = formData.keyPenalty || 0
        // _room.cleaningStatus = formData.cleaningStatus || 0
        // _room.cleaningPenalty = formData.cleaningPenalty || 0

        // // 水电物业费
        // _room.elecMeterReading = formData.elecMeterReading || 0
        // _room.coldWaterReading = formData.coldWaterReading || 0
        // _room.hotWaterReading = formData.hotWaterReading || 0
        // _room.elecFee = formData.elecFee || 0
        // _room.waterFee = formData.waterFee || 0
        // _room.pmFee = formData.pmFee || 0



        

        // _room.exitRoomAssetsList = formData.exitRoomAssetsList || []

        // _room.exitRoomAssetsList = formData.exitRoomAssetsList || []
        Object.assign(formData, _room)
        // formData = _room
        console.log('formData', formData)
        
        // 如果出场日期为空，设置默认为当前日期
        if (!formData.exitDate) {
            const today = new Date().toISOString().split('T')[0] // 格式: YYYY-MM-DD
            formData.exitDate = today
        }
        
        // 如果清洁卫生状态为空或未设置，默认设置为"自行打扫完毕、洁净"
        if (!formData.cleaningStatus || formData.cleaningStatus === 0) {
            formData.cleaningStatus = 1 // 1表示"自行打扫完毕、洁净"
        }
        
        // 初始化资产列表
        if (!formData.exitRoomAssetsList) {
            formData.exitRoomAssetsList = []
        }
        
        // 初始化水电物业费数据
        utilityData[0].elecMeterReading = formData.elecMeterReading || 0
        utilityData[0].coldWaterReading = formData.coldWaterReading || 0
        utilityData[0].hotWaterReading = formData.hotWaterReading || 0
        utilityData[0].pmFee = 0 // 抄表读数行的物业费通常不填
        
        utilityData[1].elecFee = formData.elecFee || 0
        utilityData[1].waterFee = formData.waterFee || 0
        utilityData[1].pmFee = formData.pmFee || 0
    }
}

// 显示固定资产库
const showAssetLibrary = () => {
    assetLibraryVisible.value = true
    selectedAssets.value = []
    loadAssetLibrary()
}

// 加载固定资产库数据
const loadAssetLibrary = async () => {
    try {
        assetLoading.value = true
        const params = {
            pageNum: assetSearchForm.pageNum,
            pageSize: assetSearchForm.pageSize,
            category: assetSearchForm.category,
            name: assetSearchForm.name
        }
        const res = await getFixedAssetsList(params)
        assetLibraryData.value = res.rows || []
        assetPagination.total = res.total || 0
        assetPagination.current = assetSearchForm.pageNum
    } catch (error) {
        // Message.error('加载固定资产库失败')
        console.error('加载固定资产库失败:', error)
    } finally {
        assetLoading.value = false
    }
}

// 搜索固定资产
const searchAssets = () => {
    assetSearchForm.pageNum = 1
    assetPagination.current = 1
    loadAssetLibrary()
}

// 固定资产分页变化
const handleAssetPageChange = (page: number) => {
    assetSearchForm.pageNum = page
    assetPagination.current = page
    loadAssetLibrary()
}

// 固定资产分页大小变化
const handleAssetPageSizeChange = (pageSize: number) => {
    assetSearchForm.pageSize = pageSize
    assetSearchForm.pageNum = 1
    assetPagination.pageSize = pageSize
    assetPagination.current = 1
    loadAssetLibrary()
}

// 选择/取消选择固定资产
const handleAssetSelect = (asset: FixedAssetsVo, checked: boolean) => {
    if (checked) {
        selectedAssets.value.push(asset)
    } else {
        const index = selectedAssets.value.findIndex(item => item.id === asset.id)
        if (index !== -1) {
            selectedAssets.value.splice(index, 1)
        }
    }
}

// 获取资产种类文本
const getAssetCategoryText = (category?: number) => {
    const categoryMap: Record<number, string> = {
        1: '家具',
        2: '家电',
        3: '办公用品',
        4: '其他'
    }
    return categoryMap[category || 1] || '未知'
}

// 确认选择固定资产
const handleAssetLibraryConfirm = () => {
    if (selectedAssets.value.length === 0) {
        Message.warning('请选择要添加的固定资产')
        return
    }

    // 将选中的固定资产添加到房间资产列表
    selectedAssets.value.forEach(asset => {
        const newAsset: ExitRoomAssets = {
            id: `temp_${Date.now()}_${Math.random()}`,
            exitId: formData.exitId,
            exitRoomId: formData.id,
            category: asset.category || 1,
            name: asset.name || '',
            specification: asset.specification || '',
            count: 1,
            status: 1,
            penalty: 0,
            isAdd: true, // 可以移除
            remark: '',
            isDel: true
        }
        formData.exitRoomAssetsList?.push(newAsset)
    })

    // Message.success(`成功添加 ${selectedAssets.value.length} 个固定资产`)
    assetLibraryVisible.value = false
}

// 取消选择固定资产
const handleAssetLibraryCancel = () => {
    assetLibraryVisible.value = false
    selectedAssets.value = []
}

// 移除资产
const removeAsset = (asset: ExitRoomAssets) => {
    const index = formData.exitRoomAssetsList?.findIndex(item => item.id === asset.id)
    if (index !== undefined && index !== -1) {
        formData.exitRoomAssetsList?.splice(index, 1)
    }
}

// 处理照片上传成功
const handlePhotoSuccess = (file: any) => {
    console.log('照片上传成功:', file)
}

// 处理照片上传错误
const handlePhotoError = (error: any, file: any) => {
    console.error('照片上传失败:', error, file)
    Message.error('照片上传失败')
}

// 保存
const handleSave = (isSubmit: boolean) => {
    // 验证必填字段
    if (!formData.exitDate) {
        Message.warning('请选择出场日期')
        return
    }

    formData.isSubmit = isSubmit
    
    // 更新水电费数据 - 从编辑表格同步到表单数据
    formData.elecMeterReading = Number(utilityData[0].elecMeterReading) || 0
    formData.coldWaterReading = Number(utilityData[0].coldWaterReading) || 0
    formData.hotWaterReading = Number(utilityData[0].hotWaterReading) || 0
    formData.elecFee = Number(utilityData[1].elecFee) || 0
    formData.waterFee = Number(utilityData[1].waterFee) || 0
    formData.pmFee = Number(utilityData[1].pmFee) || 0

    // 如果是确认操作，设置商服确认相关字段
    if (isSubmit) {
        formData.isBusinessConfirmed = true
        formData.businessConfirmTime = new Date().toISOString()
        formData.businessConfirmBy = userStore.id || ''
        formData.businessConfirmByName = userStore.name || ''
    }

    // 检查是否所有房间都已确认（用于更新allRoomsConfirmed状态）
    const checkAllRoomsConfirmed = () => {
        if (!props.allRooms || props.allRooms.length === 0) return false
        
        // 当前房间确认后，检查其他房间的确认状态
        const otherRooms = props.allRooms.filter(room => room.id !== formData.id)
        const currentRoomConfirmed = isSubmit // 当前房间是否确认
        
        // 所有其他房间都已确认 且 当前房间也确认了
        return otherRooms.every(room => room.isBusinessConfirmed) && currentRoomConfirmed
    }

    const allRoomsConfirmed = checkAllRoomsConfirmed()

    // 触发保存事件
    console.log('formData', formData)
    emit('save', { ...formData })

    // 如果所有房间都确认了，触发rooms-confirmed事件
    if (allRoomsConfirmed) {
        emit('rooms-confirmed', {
            exitId: formData.exitId,
            allRoomsConfirmed: true
        })
    }
}

// 取消
const handleCancel = () => {
    emit('cancel', formData.id)
}

// 处理出场日期变化
const handleExitDateChange = (date: string | undefined) => {
    // 通知父组件数据变化
    emit('syncRoomData', {
        ...props.room,
        exitDate: date
    })
}

// 处理配套资产赔偿金变化
const handlePenaltyChange = (record: ExitRoomAssets) => {
    console.log('配套资产赔偿金发生变化:', record.name, record.penalty)
    
    // 立即同步数据到父组件
    const roomId = formData.id || props.room?.id
    if (roomId) {
        emit('sync-data', {
            roomId: roomId,
            formData: { ...formData }
        })
        
        // 触发费用结算更新（通过自定义事件）
        triggerPenaltyUpdate()
    }
}

// 触发费用结算中的罚没金更新
const triggerPenaltyUpdate = () => {
    console.log('触发费用结算罚没金更新')
    
    // 获取当前房间的所有赔偿金数据
    const currentRoomData = {
        id: formData.id || props.room?.id,
        roomName: formData.roomName,
        doorWindowPenalty: formData.doorWindowPenalty || 0,
        keyPenalty: formData.keyPenalty || 0,
        cleaningPenalty: formData.cleaningPenalty || 0,
        exitRoomAssetsList: formData.exitRoomAssetsList || []
    }   
    emit('updatePenaltyAmount', currentRoomData)
    
    // // 发送自定义事件到全局，让费用结算组件监听
    // const event = new CustomEvent('forceUpdatePenalty', {
    //     detail: {
    //         exitRoomList: [currentRoomData]
    //     }
    // })
    // window.dispatchEvent(event)
}

// 处理水电费表格的单元格合并
const utilitySpanMethod = ({ record, column, rowIndex, columnIndex }: any) => {
    // 对于欠费金额行（rowIndex === 1）的冷水表和热水表列进行合并
    if (rowIndex === 1) {
        // 冷水表列（columnIndex === 2）
        if (columnIndex === 2) {
            return {
                rowspan: 1,
                colspan: 2  // 合并冷水表和热水表两列
            }
        }
        // 热水表列（columnIndex === 3）被合并，不显示
        if (columnIndex === 3) {
            return {
                rowspan: 0,
                colspan: 0
            }
        }
    }
    // 其他情况正常显示
    return {
        rowspan: 1,
        colspan: 1
    }
}

// 暴露方法给父组件
defineExpose({
    handleRoomChange,
    handleSave,
    handleCancel,
    handleRoomAssetsChange,
    triggerPenaltyUpdate,
})
</script>

<style scoped>


 
.exit-room-form {
    padding: 16px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.form-item {
    display: flex;
    align-items: center;
    gap: 16px;
    /* margin-bottom: 12px;
     */
     box-sizing: border-box;
     padding-left: 16px;;
}

.form-item > span {
    /* min-width: 130px;
    color: #1d2129;
    text-align: right; */
}

.confirm-section {
    margin-top: 24px;
    padding: 16px;
    background: #f7f8fa;
    border-radius: 4px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e6eb;
}

.standard-tag {
    color: #86909c;
    font-size: 12px;
    padding: 2px 8px;
    background: #f2f3f5;
    border-radius: 4px;
}

.asset-library-content {
    max-height: 500px;
}

.signature-info {
    /* padding: 16px; */
    background: #f7f8fa;
    border-radius: 4px;
}

.signature-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.signature-item:last-child {
    margin-bottom: 0;
}

.signature-item > span:first-child {
    min-width: 100px;
    color: #1d2129;
}

.signature-item > span:last-child {
    color: #4e5969;
    margin-right: 16px;
}

.signature-image {
    height: 40px;
    max-width: 120px;
    object-fit: contain;
    margin-left: 8px;
}
</style> 