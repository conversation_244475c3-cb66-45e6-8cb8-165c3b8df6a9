---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 资产中心/项目

<a id="opIdgetRoomTree"></a>

## POST 查询房间树形结构

POST /project/room/tree

查询房间树形结构，按楼栋和楼层分组

> Body 请求参数

```json
{
  "buildingIds": [
    "string"
  ],
  "isCompanySelf": true,
  "productType": "string",
  "roomName": "string",
  "type": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysRoomSimpleDTO](#schemasysroomsimpledto)| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","buildingName":"string","parcelId":"string","parcelName":"string","floors":[{"id":"string","floorName":"string","rooms":[{"id":"string","mdmRoomId":"string","projectId":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomName":"string","productType":"string","areaType":0,"areaTypeName":"string","buildArea":0,"innerArea":0,"isSale":true,"isCompanySelf":true,"propertyType":0,"propertyTypeName":"string","selfHoldingTime":"2019-08-24T14:15:22Z","status":0,"receiveId":"string","changeId":"string","disposalId":"string","createByName":"string","updateByName":"string","isDel":true}]}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysBuildingSimpleVo](#schemasysbuildingsimplevo)]|false|none||none|
|» 楼栋列表|[SysBuildingSimpleVo](#schemasysbuildingsimplevo)|false|none|楼栋列表|none|
|»» id|string|false|none|楼栋ID|none|
|»» buildingName|string|false|none|楼栋名称|none|
|»» parcelId|string|false|none|地块ID|none|
|»» parcelName|string|false|none|地块名称|none|
|»» floors|[[SysFloorSimpleVo](#schemasysfloorsimplevo)]|false|none|楼层列表|none|
|»»» 楼层列表|[SysFloorSimpleVo](#schemasysfloorsimplevo)|false|none|楼层列表|none|
|»»»» id|string|false|none|楼层ID|none|
|»»»» floorName|string|false|none|楼层名称|none|
|»»»» rooms|[[SysRoomVo](#schemasysroomvo)]|false|none|房间列表|[关联房间信息列表]|
|»»»»» id|string|false|none|$column.columnComment|none|
|»»»»» mdmRoomId|string|false|none|主数据房间id|主数据房间id|
|»»»»» projectId|string|false|none|所属项目id|所属项目id|
|»»»»» parcelId|string|false|none|地块id|地块id|
|»»»»» parcelName|string|false|none|地块名称|地块名称|
|»»»»» buildingId|string|false|none|楼栋id|楼栋id|
|»»»»» buildingName|string|false|none|楼栋名称|楼栋名称|
|»»»»» floorId|string|false|none|楼层id|楼层id|
|»»»»» floorName|string|false|none|楼层名称|楼层名称|
|»»»»» roomName|string|false|none|房间名称|房间名称|
|»»»»» productType|string|false|none|产品类型，字典值|产品类型，字典值|
|»»»»» areaType|integer(int32)|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|»»»»» areaTypeName|string|false|none|面积类型|面积类型|
|»»»»» buildArea|number|false|none|建筑面积|建筑面积|
|»»»»» innerArea|number|false|none|套内面积|套内面积|
|»»»»» isSale|boolean|false|none|是否可售（0否 1是）|是否可售（0否 1是）|
|»»»»» isCompanySelf|boolean|false|none|是否公司自持（0否 1是）|是否公司自持（0否 1是）|
|»»»»» propertyType|integer(int32)|false|none|自持物业类型（1非自持 2自持）|自持物业类型（1非自持 2自持）|
|»»»»» propertyTypeName|string|false|none|自持物业类型名称|自持物业类型名称|
|»»»»» selfHoldingTime|string(date-time)|false|none|自持到期时间|自持到期时间|
|»»»»» status|integer(int32)|false|none|房间状态（0初始 1接收 2处置）|房间状态（0初始 1接收 2处置）|
|»»»»» receiveId|string|false|none|接收id|接收id|
|»»»»» changeId|string|false|none|最新变更id|最新变更id|
|»»»»» disposalId|string|false|none|处置id|处置id|
|»»»»» createByName|string|false|none|创建人姓名|创建人姓名|
|»»»»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»»»»» isDel|boolean|false|none|0 否 1是|0 否 1是|

# 数据模型

<h2 id="tocS_SysRoomVo">SysRoomVo</h2>

<a id="schemasysroomvo"></a>
<a id="schema_SysRoomVo"></a>
<a id="tocSsysroomvo"></a>
<a id="tocssysroomvo"></a>

```json
{
  "id": "string",
  "mdmRoomId": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomName": "string",
  "productType": "string",
  "areaType": 0,
  "areaTypeName": "string",
  "buildArea": 0,
  "innerArea": 0,
  "isSale": true,
  "isCompanySelf": true,
  "propertyType": 0,
  "propertyTypeName": "string",
  "selfHoldingTime": "2019-08-24T14:15:22Z",
  "status": 0,
  "receiveId": "string",
  "changeId": "string",
  "disposalId": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

关联房间信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|mdmRoomId|string|false|none|主数据房间id|主数据房间id|
|projectId|string|false|none|所属项目id|所属项目id|
|parcelId|string|false|none|地块id|地块id|
|parcelName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋id|楼栋id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层id|楼层id|
|floorName|string|false|none|楼层名称|楼层名称|
|roomName|string|false|none|房间名称|房间名称|
|productType|string|false|none|产品类型，字典值|产品类型，字典值|
|areaType|integer(int32)|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|areaTypeName|string|false|none|面积类型|面积类型|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|isSale|boolean|false|none|是否可售（0否 1是）|是否可售（0否 1是）|
|isCompanySelf|boolean|false|none|是否公司自持（0否 1是）|是否公司自持（0否 1是）|
|propertyType|integer(int32)|false|none|自持物业类型（1非自持 2自持）|自持物业类型（1非自持 2自持）|
|propertyTypeName|string|false|none|自持物业类型名称|自持物业类型名称|
|selfHoldingTime|string(date-time)|false|none|自持到期时间|自持到期时间|
|status|integer(int32)|false|none|房间状态（0初始 1接收 2处置）|房间状态（0初始 1接收 2处置）|
|receiveId|string|false|none|接收id|接收id|
|changeId|string|false|none|最新变更id|最新变更id|
|disposalId|string|false|none|处置id|处置id|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_SysRoomSimpleDTO">SysRoomSimpleDTO</h2>

<a id="schemasysroomsimpledto"></a>
<a id="schema_SysRoomSimpleDTO"></a>
<a id="tocSsysroomsimpledto"></a>
<a id="tocssysroomsimpledto"></a>

```json
{
  "buildingIds": [
    "string"
  ],
  "isCompanySelf": true,
  "productType": "string",
  "roomName": "string",
  "type": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|buildingIds|[string]|false|none|楼栋ID列表|按楼栋ID列表筛选|
|» 楼栋ID列表|string|false|none|楼栋ID列表|按楼栋ID列表筛选|
|isCompanySelf|boolean|false|none|是否自持|是否自持|
|productType|string|false|none|产品类型|产品类型|
|roomName|string|false|none|房源名称|按房源名称模糊搜索|
|type|integer(int32)|false|none|类型|1接收 2处置|

<h2 id="tocS_SysBuildingSimpleVo">SysBuildingSimpleVo</h2>

<a id="schemasysbuildingsimplevo"></a>
<a id="schema_SysBuildingSimpleVo"></a>
<a id="tocSsysbuildingsimplevo"></a>
<a id="tocssysbuildingsimplevo"></a>

```json
{
  "id": "string",
  "buildingName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "floors": [
    {
      "id": "string",
      "floorName": "string",
      "rooms": [
        {
          "id": "string",
          "mdmRoomId": "string",
          "projectId": "string",
          "parcelId": "string",
          "parcelName": "string",
          "buildingId": "string",
          "buildingName": "string",
          "floorId": "string",
          "floorName": "string",
          "roomName": "string",
          "productType": "string",
          "areaType": 0,
          "areaTypeName": "string",
          "buildArea": 0,
          "innerArea": 0,
          "isSale": true,
          "isCompanySelf": true,
          "propertyType": 0,
          "propertyTypeName": "string",
          "selfHoldingTime": "2019-08-24T14:15:22Z",
          "status": 0,
          "receiveId": "string",
          "changeId": "string",
          "disposalId": "string",
          "createByName": "string",
          "updateByName": "string",
          "isDel": true
        }
      ]
    }
  ]
}

```

楼栋列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|楼栋ID|none|
|buildingName|string|false|none|楼栋名称|none|
|parcelId|string|false|none|地块ID|none|
|parcelName|string|false|none|地块名称|none|
|floors|[[SysFloorSimpleVo](#schemasysfloorsimplevo)]|false|none|楼层列表|none|

<h2 id="tocS_SysFloorSimpleVo">SysFloorSimpleVo</h2>

<a id="schemasysfloorsimplevo"></a>
<a id="schema_SysFloorSimpleVo"></a>
<a id="tocSsysfloorsimplevo"></a>
<a id="tocssysfloorsimplevo"></a>

```json
{
  "id": "string",
  "floorName": "string",
  "rooms": [
    {
      "id": "string",
      "mdmRoomId": "string",
      "projectId": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "roomName": "string",
      "productType": "string",
      "areaType": 0,
      "areaTypeName": "string",
      "buildArea": 0,
      "innerArea": 0,
      "isSale": true,
      "isCompanySelf": true,
      "propertyType": 0,
      "propertyTypeName": "string",
      "selfHoldingTime": "2019-08-24T14:15:22Z",
      "status": 0,
      "receiveId": "string",
      "changeId": "string",
      "disposalId": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

楼层列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|楼层ID|none|
|floorName|string|false|none|楼层名称|none|
|rooms|[[SysRoomVo](#schemasysroomvo)]|false|none|房间列表|[关联房间信息列表]|

