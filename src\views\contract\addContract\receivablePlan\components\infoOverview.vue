<template>
    <section>
        <sectionTitle title="信息总览"></sectionTitle>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table :columns="columns" :data="tableData" :pagination="false" :scroll="{ x: 1200 }"
                :bordered="{ cell: true }">
                <template #depositAmount="{ record }">
                    {{ formatAmount(record.depositAmount) }}
                </template>
                <template #rentAmount="{ record }">
                    {{ formatAmount(record.rentAmount) }}
                </template>
                <template #otherAmount="{ record }">
                    {{ formatAmount(record.otherAmount) }}
                </template>
                <template #discountAmount="{ record }">
                    {{ formatAmount(record.discountAmount) }}
                </template>
                <template #actualReceivableAmount="{ record }">
                    {{ formatAmount(record.actualReceivableAmount) }}
                </template>
                <template #receivedAmount="{ record }">
                    {{ formatAmount(record.receivedAmount) }}
                </template>
                <template #remainingAmount="{ record }">
                    {{ formatAmount(record.remainingAmount) }}
                </template>
            </a-table>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { TableData } from '@arco-design/web-vue/es/table/interface';
import { useContractStore } from '@/store/modules/contract'

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractCosts)

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

interface TableDataItem {
    depositAmount: number;
    rentAmount: number;
    otherAmount: number;
    discountAmount: number;
    actualReceivableAmount: number;
    receivedAmount: number;
    remainingAmount: number;
}

// 存储初始化时的租金数据，不再监听变化
const initialRentData = ref<{
    rentAmount: number;
    rentDiscountAmount: number;
} | null>(null)

const tableData = ref<TableDataItem[]>([
    {
        depositAmount: 0,
        rentAmount: 0,
        otherAmount: 0,
        discountAmount: 0,
        actualReceivableAmount: 0,
        receivedAmount: 0,
        remainingAmount: 0,
    }
])

const columns: TableData = [
    {
        title: '保证金（元）',
        dataIndex: 'depositAmount',
        slotName: 'depositAmount',
        width: 120,
        align: 'right',
    },
    {
        title: '租金账单总额（元）',
        dataIndex: 'rentAmount',
        slotName: 'rentAmount',
        width: 120,
        align: 'right',
    },
    {
        title: '其他费用账单总额（元）',
        dataIndex: 'otherAmount',
        slotName: 'otherAmount',
        width: 120,
        align: 'right',
    },
    {
        title: '优惠金额（元）',
        dataIndex: 'discountAmount',
        slotName: 'discountAmount',
        width: 120,
        align: 'right',
    },
    {
        title: '总应收金额（元）',
        dataIndex: 'actualReceivableAmount',
        slotName: 'actualReceivableAmount',
        width: 120,
        align: 'right',
    },
    // {
    //     title: '已收金额（元）',
    //     dataIndex: 'receivedAmount',
    //     slotName: 'receivedAmount',
    //     width: 140,
    //     align: 'right',
    // },
    // {
    //     title: '剩余应收金额（元）',
    //     dataIndex: 'remainingAmount',
    //     slotName: 'remainingAmount',
    //     width: 160,
    //     align: 'right',
    // }
]

// 计算信息总览数据的函数
const calculateOverviewData = (costs: any[]) => {
    const depositAmount = costs.filter(item => item.costType === 1).reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
    const rentAmount = costs.filter(item => item.costType === 2).reduce((sum, item) => sum + (item.totalAmount || 0), 0)
    const otherAmount = costs.filter(item => item.costType === 3).reduce((sum, item) => sum + (item.totalAmount || 0), 0)
    const discountAmount = costs.reduce((sum, item) => sum + (item.discountAmount || 0), 0)
    const actualReceivableAmount = costs.reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
    const receivedAmount = costs.reduce((sum, item) => sum + (item.receivedAmount || 0), 0)
    const remainingAmount = actualReceivableAmount - receivedAmount
    
    return {
        depositAmount,
        rentAmount,
        otherAmount,
        discountAmount,
        actualReceivableAmount,
        receivedAmount,
        remainingAmount,
    }
}

/**
 * 混合监听逻辑：
 * 1. 租金账单总额和租金优惠金额：只在初始化时计算一次，之后不再变化
 * 2. 其他费用账单总额、总应收金额等：继续监听变化，实时更新
 */
watch(contractCosts, (newVal) => {
    if (newVal.costs && newVal.costs.length > 0) {
        // 1. 初始化租金数据（只计算一次）
        if (!initialRentData.value) {
            const rentCosts = newVal.costs.filter(item => item.costType === 2)
            const rentAmount = rentCosts.reduce((sum, item) => sum + (item.totalAmount || 0), 0)
            const rentDiscountAmount = rentCosts.reduce((sum, item) => sum + (item.discountAmount || 0), 0)
            
            console.log('信息总览：初始化租金数据', {
                rentCostsLength: rentCosts.length,
                rentAmount,
                rentDiscountAmount,
                rentCosts: rentCosts.map(item => ({
                    period: item.period,
                    totalAmount: item.totalAmount,
                    discountAmount: item.discountAmount
                }))
            })
            
            initialRentData.value = {
                rentAmount,
                rentDiscountAmount
            }
        } else {
            console.log('信息总览：租金数据已初始化，跳过', {
                existingData: initialRentData.value
            })
        }
        
        // 2. 实时计算其他数据
        const depositAmount = newVal.costs.filter(item => item.costType === 1).reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
        const otherAmount = newVal.costs.filter(item => item.costType === 3).reduce((sum, item) => sum + (item.totalAmount || 0), 0)
        const otherDiscountAmount = newVal.costs.filter(item => item.costType === 3).reduce((sum, item) => sum + (item.discountAmount || 0), 0)
        const depositDiscountAmount = newVal.costs.filter(item => item.costType === 1).reduce((sum, item) => sum + (item.discountAmount || 0), 0)
        const actualReceivableAmount = newVal.costs.reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
        const receivedAmount = newVal.costs.reduce((sum, item) => sum + (item.receivedAmount || 0), 0)
        const remainingAmount = actualReceivableAmount - receivedAmount
        
        // 3. 组合固定的租金数据和动态的其他数据
        const rentAmount = initialRentData.value.rentAmount
        const totalDiscountAmount = initialRentData.value.rentDiscountAmount + otherDiscountAmount + depositDiscountAmount
        
        tableData.value = [
            {
                depositAmount,
                rentAmount, // 使用固定的租金账单总额
                otherAmount,
                discountAmount: totalDiscountAmount, // 租金优惠金额固定 + 其他费用优惠金额动态
                actualReceivableAmount,
                receivedAmount,
                remainingAmount,
            }
        ]
    }
}, { deep: true, immediate: true })

// 暴露获取初始租金数据的方法，供校验使用
const getInitialRentData = () => {
    return initialRentData.value
}

// 暴露获取当前显示数据的方法，供校验使用
const getCurrentData = () => {
    return tableData.value[0] || null
}

defineExpose({
    getInitialRentData,
    getCurrentData,
    // 保持向后兼容
    getInitialData: () => {
        const current = getCurrentData()
        if (current && initialRentData.value) {
            return {
                ...current,
                rentAmount: initialRentData.value.rentAmount,
                discountAmount: initialRentData.value.rentDiscountAmount
            }
        }
        return null
    }
})
</script>

<style lang="less" scoped></style>