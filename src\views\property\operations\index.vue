<template>
  <div class="container">
    <a-card class="general-card">
      <!-- Tab切换 -->
      <a-tabs v-model:activeTab="activeTab" @change="handleTabChange">
        <a-tab-pane v-permission="['rent:room:list']" key="active" title="生效中" />
        <a-tab-pane v-permission="['rent:room:list']" key="draft" title="草稿" />
        <a-tab-pane v-permission="['rent:room:list']" key="all" title="全部" />
      </a-tabs>

      <!-- 统一筛选栏 -->
      <div class="filter-bar" v-show="showFilterBar">
        <a-row>
          <a-col :flex="1">
            <a-form label-align="right" :model="filterForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>
              <a-row :gutter="16">
                <!-- 项目 - 所有tab都有 -->
                <a-col :span="8">
                  <a-form-item field="projectId" label="项目">
                    <ProjectTreeSelect 
                      v-model="filterForm.projectId" 
                      :min-level="4"
                      @change="handleProjectChange"
                    />
                  </a-form-item>
                </a-col>
                <!-- 地块/楼栋 - 所有tab都有 -->
                <a-col :span="8">
                  <a-form-item field="parcelOrBuildingName" label="地块/楼栋">
                    <a-input v-model="filterForm.parcelOrBuildingName" placeholder="请输入地块/楼栋" allow-clear />
                  </a-form-item>
                </a-col>
                <!-- 多经点位 - 所有tab都有 -->
                <a-col :span="8">
                  <a-form-item field="roomName" label="多经点位">
                    <a-input v-model="filterForm.roomName" placeholder="请输入多经点位" allow-clear />
                  </a-form-item>
                </a-col>
                <!-- 多经用途 - 所有tab都有 -->
                <a-col :span="8">
                  <a-form-item field="propertyType" label="多经用途">
                    <a-tree-select
                      v-model="filterForm.propertyType"
                      :data="propertyTypeOptions"
                      placeholder="请选择用途"
                      allow-clear
                      :field-names="{
                        key: 'dictValue',
                        title: 'dictLabel',
                        children: 'childList'
                      }"
                    />
                  </a-form-item>
                </a-col>
                <!-- 是否定价 - 仅在生效中tab显示 -->
                <a-col :span="8" v-if="activeTab === 'active'">
                  <a-form-item field="priceFlag" label="是否定价">
                    <a-select v-model="filterForm.priceFlag" placeholder="请选择" allow-clear>
                      <a-option :value="1">是</a-option>
                      <a-option :value="0">否</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <!-- 点位状态 - 仅在全部tab显示 -->
                <a-col :span="8" v-if="activeTab === 'all'">
                  <a-form-item field="status" label="点位状态">
                    <a-select v-model="filterForm.status" placeholder="请选择状态" allow-clear>
                      <a-option :value="RoomStatus.DRAFT">草稿</a-option>
                      <a-option :value="RoomStatus.ACTIVE">生效中</a-option>
                      <a-option :value="RoomStatus.EXPIRED">已失效</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-col>
          <a-divider style="height: 84px" direction="vertical" />
          <a-col :flex="'86px'" style="text-align: right">
            <a-space direction="vertical" :size="18">
              <a-button type="primary" @click="handleSearch">
                <template #icon><icon-search /></template>
                查询
              </a-button>
              <a-button @click="handleReset">
                <template #icon><icon-refresh /></template>
                重置
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </div>
      <a-divider style="margin-top: 0" v-show="showFilterBar" />

      <!-- 根据activeTab显示不同的操作按钮 -->
      <div class="action-bar" v-show="showActionBar">
        <a-space>
          <!-- 生效中tab的按钮 -->
          <template v-if="activeTab === 'active'">
            <a-button v-permission="['rent:room:add']" type="primary" @click="handleAdd">
              <template #icon><icon-plus /></template>
              新增多经点位
            </a-button>
            <a-button v-permission="['rent:room:template:download']" @click="handleDownloadTemplate">
              <template #icon><icon-download /></template>
              下载模板
            </a-button>
            <a-button v-permission="['rent:room:template:import']" @click="handleImport">
              <template #icon><icon-upload /></template>
              导入多经点位
            </a-button>
            <a-button v-permission="['rent:room:project:add']" @click="handleInitiatePricing">
              <template #icon><icon-tag /></template>
              发起立项定价
            </a-button>
          </template>
          
          <!-- 草稿tab的按钮 -->
          <template v-if="activeTab === 'draft'">
            <a-button v-permission="['rent:room:add']" type="primary" @click="handleAdd">
              <template #icon><icon-plus /></template>
              新增多经点位
            </a-button>
            <a-button v-permission="['rent:room:template:download']" @click="handleDownloadTemplate">
              <template #icon><icon-download /></template>
              下载模板
            </a-button>
            <a-button v-permission="['rent:room:template:import']" @click="handleImport">
              <template #icon><icon-upload /></template>
              导入多经点位
            </a-button>
            <a-button v-permission="['rent:room:effect']" @click="handleActivateSelected">
              <template #icon><icon-check /></template>
              生效多经点位
            </a-button>
          </template>
          
          <!-- 全部tab的按钮 -->
          <template v-if="activeTab === 'all'">
            <a-button v-permission="['rent:room:export']" type="primary" @click="handleExport">
              <template #icon><icon-download /></template>
              导出多经点位
            </a-button>
          </template>
        </a-space>
      </div>

      <!-- 根据activeTab显示不同组件 -->
      <active-tab v-if="activeTab === 'active'"
        :filter-form="filterForm"
        v-model:selected-keys="selectedKeys"
        @update:loading="loading = $event"
        @view-detail="handleViewDetail"
        @edit-room="handleEditRoom"
        @update-room-data-cache="updateRoomDataCache"
      />
      <draft-tab v-if="activeTab === 'draft'"
        :filter-form="filterForm"
        v-model:selected-keys="selectedKeys"
        @update:loading="loading = $event"
        @view-detail="handleViewDetail"
        @edit-room="handleEditRoom"
        @update-room-data-cache="updateRoomDataCache"/>
      <all-tab v-if="activeTab === 'all'"
        :filter-form="filterForm"
        v-model:selected-keys="selectedKeys"
        @update:loading="loading = $event"
        @view-detail="handleViewDetail"
        @update-room-data-cache="updateRoomDataCache" />
    </a-card>
      <!-- 添加多经点位抽屉 -->
      <add-operation-drawer v-if="addOperationDrawerVisible" ref="addOperationDrawerRef" @confirm="handleOperationConfirm" @cancel="handleOperationCancel" />
      <!-- 立项定价抽屉 -->
      <project-pricing-drawer v-if="showProjectPricingDrawer" ref="projectPricingDrawerRef" @success="handleProjectPricingSuccess" @cancel="handleProjectPricingCancel" />
  </div>
  
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import { 
  IconSearch, 
  IconRefresh, 
  IconPlus, 
  IconDownload, 
  IconUpload, 
  IconTag,
  IconCheck
} from '@arco-design/web-vue/es/icon';
import { useDictSync } from '@/utils/dict';
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue';
import ActiveTab from './components/ActiveTab.vue';
import DraftTab from './components/DraftTab.vue';
import AllTab from './components/AllTab.vue';
import ProjectPricingDrawer from '@/views/projectPricing/components/ProjectPricingDrawer.vue';
import { 
  getRoomList, 
  exportRoomList, 
  downloadRoomTemplate,
  downloadMultipleRoomTemplateForLease, 
  importMultipleRooms,
  importRooms, 
  effectRooms, 
  submitProjectPricingApplication,
  RoomStatus, 
  RoomType 
} from '@/api/room';
import { exportExcel, importExcelFile } from '@/utils/exportUtil';
import AddOperationDrawer from './components/addOperationDrawer.vue';

// 当前选中的Tab
const activeTab = ref('active');

// 控制显示
const showFilterBar = computed(() => ['active', 'draft', 'all'].includes(activeTab.value));
const showActionBar = computed(() => ['active', 'draft', 'all'].includes(activeTab.value));

// 多经用途字典数据
interface DictData {
  dictCode: string;
  dictLabel: string;
  dictValue: string;
  dictSort: number;
  parentCode?: string;
  childList?: DictData[];
  [key: string]: any;
}

const propertyTypeOptions = ref<DictData[]>([]);

// 定义筛选表单类型
interface OperationsFilterForm {
  projectId?: string;
  parcelOrBuildingName: string;
  roomName: string;
  propertyType?: string;
  priceFlag?: number;
  status?: number;
  type: number;
  pageNum: number;
  pageSize: number;
}

// 筛选表单数据
const filterForm = reactive<OperationsFilterForm>({
  projectId: undefined,
  parcelOrBuildingName: '',
  roomName: '',
  propertyType: undefined,
  priceFlag: undefined,
  status: undefined,
  type: RoomType.MULTIPLE, // 固定为多经类型
  pageNum: 1,
  pageSize: 10
});

// 选中的行keys
const selectedKeys = ref<string[]>([]);

// 加载状态
const loading = ref(false);

// 控制弹框显示
const showProjectPricingDrawer = ref(false);

// 组件引用
const projectPricingDrawerRef = ref();

// 项目选择相关
const projectList = ref<any[]>([])
const currentProjectName = ref('')

// 加载多经用途字典数据
const loadPropertyTypeDicts = async () => {
  try {
    const dictData = await useDictSync('diversification_purpose');
    if (dictData.diversification_purpose) {
      // 处理树形结构数据
      const dictList = dictData.diversification_purpose as DictData[];
      // 组织成树形结构
      const treeData = dictList.filter(item => !item.parentCode);
      treeData.forEach(parent => {
        parent.childList = dictList.filter(child => child.parentCode === parent.dictCode);
      });
      propertyTypeOptions.value = treeData;
    }
  } catch (error) {
    console.error('获取多经用途字典数据失败:', error);
  }
};

// 项目变更处理
const handleProjectChange = (value: string, selectedOrg: any) => {
  filterForm.projectId = value;
  // 获取当前选择的项目名称
  currentProjectName.value = selectedOrg.name
  // 重置相关字段
  filterForm.parcelOrBuildingName = '';
  // 执行查询
  handleSearch();
};

// 组件挂载时初始化数据
onMounted(() => {
  loadPropertyTypeDicts();
});

// 监听tab切换
watch(() => activeTab.value, (newTab) => {
  // 根据不同tab重置表单
  if (newTab === 'active') {
    // 生效中tab默认字段
    filterForm.priceFlag = undefined;
    filterForm.status = RoomStatus.ACTIVE;
  } else if (newTab === 'draft') {
    // 草稿tab默认字段
    filterForm.priceFlag = undefined;
    filterForm.status = RoomStatus.DRAFT;
  } else if (newTab === 'all') {
    // 全部tab默认字段
    filterForm.status = undefined;
  }
  
  // 清空选中
  selectedKeys.value = [];
});

// 添加handleTabChange函数
// 处理Tab切换
const handleTabChange = (key: string) => {
  activeTab.value = key;
  // 重置loading状态
  loading.value = false;
  // 重置选中状态
  selectedKeys.value = [];
  
  // 重置筛选条件 - 保留项目ID
  const projectId = filterForm.projectId;
  Object.keys(filterForm).forEach(fieldKey => {
    if (fieldKey !== 'projectId' && fieldKey !== 'type' && fieldKey !== 'pageNum' && fieldKey !== 'pageSize') {
      if (typeof filterForm[fieldKey as keyof typeof filterForm] === 'string') {
        (filterForm as any)[fieldKey] = '';
      } else {
        (filterForm as any)[fieldKey] = undefined;
      }
    }
  });
  filterForm.projectId = projectId;
  
  // 根据当前tab设置特定字段
  if (key === 'active') {
    filterForm.status = RoomStatus.ACTIVE;
  } else if (key === 'draft') {
    filterForm.status = RoomStatus.DRAFT;
  } else if (key === 'all') {
    filterForm.status = undefined;
  }
  
  setTimeout(() => {
    // 执行查询
    handleSearch();
  }, 200);
};

// 查询
const handleSearch = () => {
  loading.value = false
  // 使用tab-specific的事件名称，避免所有组件同时响应
  const eventName = `refresh-operation-${activeTab.value}-data`
  console.log('发送事件:', eventName)
  const event = new CustomEvent(eventName)
  document.dispatchEvent(event)
};

// 重置
const handleReset = () => {
  // 保留项目ID和类型
  const projectId = filterForm.projectId;
  const type = filterForm.type;
  let status = undefined;
  if(activeTab.value !== 'all'){
    status = filterForm.status; // 保留状态，与当前tab对应
  }

  // 重置表单 - 根据当前tab保留特定字段
  Object.keys(filterForm).forEach(fieldKey => {
    if (fieldKey !== 'type' && fieldKey !== 'pageNum' && fieldKey !== 'pageSize') {
      if (typeof filterForm[fieldKey as keyof typeof filterForm] === 'string') {
        (filterForm as any)[fieldKey] = '';
      } else {
        (filterForm as any)[fieldKey] = undefined;
      }
    }
  });
  
  // 恢复保留的字段
  filterForm.projectId = projectId;
  filterForm.type = type;
  filterForm.status = status;
  
  // 执行查询
  handleSearch();
};

// 控制抽屉的显示
const addOperationDrawerVisible = ref(false);

// 新增多经点位
const handleAdd = () => {
  console.log('新增多经点位');

  // 获取当前选择的项目信息
  const projectData = {
    project: filterForm.projectId,
    projectName: currentProjectName.value
  };
  addOperationDrawerVisible.value = true;
  // 打开抽屉组件
  nextTick(() => {
    addOperationDrawerRef.value?.show('add', undefined, projectData);
  });
};
// 查看房源详情
const handleViewDetail = (id: string) => {
    console.log('查看房源详情:', id)
    if (!id) {
        Message.error('房源ID不存在')
        return
    }
    const projectData = {
      project: filterForm.projectId,
      projectName: currentProjectName.value
    };
    addOperationDrawerVisible.value = true;
    nextTick(() => {
      addOperationDrawerRef.value?.show('view', id, projectData)
    });
}

// 编辑房源
const handleEditRoom = (id: string) => {
    console.log('编辑房源:', id)
    if (!id) {
        Message.error('房源ID不存在')
        return
    }
    const projectData = {
      project: filterForm.projectId,
      projectName: currentProjectName.value
    };
    addOperationDrawerVisible.value = true;
    nextTick(() => {
      addOperationDrawerRef.value?.show('edit', id, projectData)
    });
}



// 引入抽屉组件
const addOperationDrawerRef = ref();

// 下载模板
const handleDownloadTemplate = async () => {
  if (!filterForm.projectId) {
		Message.warning('请先保存基本信息');
		return;
	}
	try {
		// 使用exportExcel方法下载模板
		exportExcel(downloadMultipleRoomTemplateForLease, filterForm.projectId, '多经导入模板')
			.then(() => {
				Message.success('模板下载成功');
			})
	} catch (error) {
		console.error('下载模板失败:', error);
	}
};

// 导入多经点位
const handleImport = () => {
  if (!filterForm.projectId) {
		Message.warning('请先保存基本信息');
		return;
	}

	// 创建文件选择器
	const input = document.createElement('input');
	input.type = 'file';
	input.accept = '.xlsx,.xls';
	input.style.display = 'none';

	// 监听文件选择事件
	input.onchange = (event) => {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			const file = target.files[0];
			
			// 检查文件类型
			if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
				Message.error('请上传Excel文件(.xlsx或.xls)');
				return;
			}

			// 显示加载状态
			const loadingInstance = Message.loading({
				content: '正在导入多经数据...',
				duration: 0
			});
			
			// 使用importExcelFile方法导入文件
			importExcelFile(file, importMultipleRooms)
				.then((res: any) => {
					if (res && res.code === 200) {
						Message.success('多经导入成功');
						// 刷新房源列表
            handleSearch()
					}
				})
				.catch((error: any) => {
					console.error('多经导入失败:', error);
				})
				.finally(() => {
					// 关闭加载状态
					loadingInstance.close();
					// 移除input元素
					document.body.removeChild(input);
				});
		}
	};
	
	// 添加到DOM并触发点击
	document.body.appendChild(input);
	input.click();
};

// 房间数据缓存
let roomDataCache: any[] = []

// 更新房间数据缓存
const updateRoomDataCache = (data: any[]) => {
  roomDataCache = [...data]
}

// 根据ID查找房源记录
const findRoomById = (id: string): any | undefined => {
  if (roomDataCache && roomDataCache.length > 0) {
    return roomDataCache.find(room => room.id === id)
  }
  return undefined
}

// 发起立项定价
const handleInitiatePricing = async () => {
  if (selectedKeys.value.length === 0) {
    Message.warning('请先选择多经点位');
    return;
  }

  // 从缓存中获取选中房间的完整数据
  const selectedRooms = selectedKeys.value.map(id => findRoomById(id)).filter(room => !!room)

  if (selectedRooms.length === 0) {
    Message.warning('未找到选中房源的详细信息')
    return
  }

  // 将RoomVo数据转换为ProjectPricingDrawer期望的格式
  const mappedRooms = selectedRooms.map((room: any) => ({
    ...room,
    id: room.id,
    name: room.roomName,
    roomName: room.roomName,
    buildingId: room.buildingId,
    buildingName: room.buildingName,
    floorId: room.floorId,
    floorName: room.floorName,
    plotId: room.parcelId || '',
    plotName: room.parcelName || '',
    propertyType: room.propertyType || '', // 物业类型字典值
    propertyTypeName: (room as any).propertyTypeName || room.propertyType || '', // 物业类型中文名称
    roomUsage: room.propertyType || '', // 用途字段，应该是字典值
    layoutName: (room as any).houseTypeName || room.houseType || '',
    roomType: (room as any).houseTypeName || room.houseType || '', // 户型字段
    rentArea: room.rentArea || 0
  }))

  // 构造传递给ProjectPricingDrawer的数据
  const projectPricingData = {
    selectedRooms: mappedRooms,
    selectedRoomIds: selectedKeys.value,
    projectId: filterForm.projectId,
    projectName: currentProjectName.value,
    buildingIds: [...new Set(selectedRooms.map((room: any) => room.buildingId).filter(id => !!id))]
  }

  // 打开立项定价抽屉
  showProjectPricingDrawer.value = true
  nextTick(() => {
    projectPricingDrawerRef.value?.show('add', projectPricingData)
  })
};

// 生效多经点位
const handleActivateSelected = async () => {
  if (selectedKeys.value.length === 0) {
    Message.warning('请先选择多经点位');
    return;
  }
  
  Modal.confirm({
    title: '确认生效',
    content: `确定要生效选中的 ${selectedKeys.value.length} 个多经点位吗？`,
    onOk: async () => {
      try {
        await effectRooms(selectedKeys.value);
        Message.success('生效成功');
        // 刷新数据
        handleSearch();
      } catch (error) {
        console.error('生效点位失败:', error);
      }
    }
  });
};

// 导出多经点位
const handleExport = async () => {
  try {
    // 构建导出查询参数，使用当前筛选条件
    const exportParams = {
      ...filterForm,
      pageNum: 1,
      pageSize: 10000, // 导出时使用大页码
    };
    
    await exportExcel(exportRoomList, exportParams, '多经点位列表');
    Message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
  }
};

// 添加抽屉组件的确认和取消处理函数
const handleOperationConfirm = (operation: string) => {
  console.log(`${operation} 操作确认`);
  // 处理确认逻辑
  handleSearch();
};

const handleOperationCancel = () => {
  console.log('抽屉组件取消');
  // 处理取消逻辑
  addOperationDrawerVisible.value = false;
};

// 立项定价相关处理函数
const handleProjectPricingSuccess = () => {
  showProjectPricingDrawer.value = false
  Message.success('立项定价申请提交成功')
  handleSearch()
}

const handleProjectPricingCancel = () => {
  showProjectPricingDrawer.value = false
}
</script>

<style lang="less" scoped>
.container {
  padding: 0 20px;
}

.filter-bar {
  margin-top: 16px;
}

.action-bar {
  margin-bottom: 16px;
}
</style>