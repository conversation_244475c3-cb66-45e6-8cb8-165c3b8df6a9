import http from './index'

const systemUrl = '/business-rent-admin'

// API响应类型定义
export interface AjaxResult<T = any> {
  error?: boolean
  success?: boolean
  warn?: boolean
  empty?: boolean
  data?: T
  [key: string]: any
}

export interface TableDataInfo<T = any> {
  total?: number
  rows?: T[]
  code?: number
  msg?: string
}

// 商业公司数据类型定义
export interface MerchantAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  orgCompanyName?: string
  merchantNo?: string
  bankName?: string
  bankAccount?: string
  unifiedSocialCreditCode?: string
  phoneNumber?: string
  registeredAddress?: string
  legalPersonName?: string
  legalPersonIdCard?: string
  agentName?: string
  agentPhoneNumber?: string
  agentIdCard?: string
  sealId?: string
  processId?: string
}

// 商业公司查询参数
export interface MerchantQueryParams {
  orgCompanyName?: string
  pageNum: number
  pageSize: number
}

// 商业公司关联项目查询参数
export interface MerchantProjectQueryParams {
  merchantId?: string
  projectName?: string
  pageNum: number
  pageSize: number
}

// 项目数据类型定义
export interface ProjectInfo {
  projectId: string
  projectName: string
  projectAddress?: string
  projectStatus?: string
  merchantId?: string
  createTime?: string
  updateTime?: string
}

// 商业公司项目关联DTO
export interface MerchantProjectDTO {
  merchantId: string
  projectIds: string[]
}

// 组织树查询参数
export interface OrgTreeQueryParams {
  name?: string // 组织简称，可选
  relProjectFlag?: number // 未关联项目标识，可选；1:未关联 2：已关联
  projectIds?: string[] // 项目ID数组，可选
}

/**
 * 编辑商业公司
 */
export function editMerchant(data: MerchantAddDTO): Promise<AjaxResult> {
  return http.put(`${systemUrl}/merchant`, data)
}

/**
 * 查询商业公司列表
 */
export function getMerchantList(data: MerchantQueryParams): Promise<TableDataInfo<MerchantAddDTO>> {
  return http.get(`${systemUrl}/merchant/list`, data)
}

/**
 * 获取商业公司详细信息
 */
export function getMerchantDetail(id: string): Promise<AjaxResult<MerchantAddDTO>> {
  return http.get(`${systemUrl}/merchant/detail`, { id: id })
}

/**
 * 获取商业公司关联项目列表
 */
export function getMerchantProjectList(data: MerchantProjectQueryParams): Promise<TableDataInfo<ProjectInfo>> {
  return http.get(`${systemUrl}/merchant/project/list`, data)
}

/**
 * 新增商业公司项目关联
 */
export function addMerchantProject(data: MerchantProjectDTO): Promise<AjaxResult> {
  return http.post(`${systemUrl}/merchant/project/add`, data)
}

/**
 * 取消商业公司项目关联
 */
export function cancelMerchantProject(data: MerchantProjectDTO): Promise<AjaxResult> {
  return http.post(`${systemUrl}/merchant/project/cancel`, data)
}

/**
 * 获取组织树（支持过滤）
 */
export function getOrgTreeWithFilter(data: OrgTreeQueryParams): Promise<AjaxResult> {
  return http.post(`${systemUrl}/org/tree`, data)
} 