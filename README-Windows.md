# Windows 系统构建优化指南

## 🪟 Windows 专用构建命令

本项目针对 Windows 系统进行了专门的构建优化，以下是推荐的使用方式：

### 快速开始

1. **首次设置** (推荐)
   ```bash
   npm run setup:win
   ```
   这会自动检测您的系统并创建优化配置

2. **Windows 优化构建**
   ```bash
   # 标准 Windows 构建
   npm run build:win
   
   # 快速 Windows 构建
   npm run build:win:fast
   
   # 超级 Windows 构建 (最大性能)
   npm run build:win:turbo
   ```

3. **使用批处理文件** (更简单)
   - 双击 `build-windows.bat` - 完整构建
   - 双击 `build-windows-fast.bat` - 快速构建

## 🚀 性能优化特性

### 内存优化
- **标准模式**: 8GB 内存分配
- **快速模式**: 12GB 内存分配  
- **超级模式**: 16GB 内存分配
- 自动垃圾回收优化 (`--expose-gc`)

### Windows 特定优化
- ✅ Windows 路径长度限制处理
- ✅ 文件系统监听优化 (轮询模式)
- ✅ Windows Defender 兼容
- ✅ PowerShell 执行策略适配
- ✅ 并行处理核心数优化

### 构建优化
- ✅ 智能代码分割
- ✅ 依赖预构建优化
- ✅ Windows 缓存策略
- ✅ 文件名长度优化
- ✅ 更激进的压缩策略

## 🛠️ 系统要求

### 最低配置
- Windows 10/11
- Node.js 14+
- 8GB RAM
- SSD 硬盘 (推荐)

### 推荐配置
- Windows 11
- Node.js 18+
- 16GB+ RAM
- NVMe SSD
- 8核+ CPU

## ⚡ 性能优化建议

### 1. Windows Defender 排除
将以下目录添加到 Windows Defender 排除列表：
```
C:\path\to\your\project
C:\path\to\your\project\node_modules
C:\path\to\your\project\dist
C:\Users\<USER>\.npm
```

### 2. PowerShell 执行策略
在管理员模式下运行：
```powershell
Set-ExecutionPolicy RemoteSigned
```

### 3. 环境变量优化
项目已自动创建 `.env.windows` 文件，包含：
```bash
NODE_OPTIONS=--max-old-space-size=16384
UV_THREADPOOL_SIZE=128
```

### 4. npm 配置优化
```bash
npm config set maxsockets 20
npm config set progress false
```

## 📊 性能对比

| 构建模式 | 内存使用 | 预计时间 | 适用场景 |
|---------|---------|---------|---------|
| 标准模式 | 8GB | 2-3分钟 | 日常开发 |
| 快速模式 | 12GB | 1-2分钟 | 测试部署 |
| 超级模式 | 16GB | 45-90秒 | 紧急发布 |

## 🔧 故障排除

### 构建慢的常见原因
1. **杀毒软件扫描** - 添加项目到排除列表
2. **机械硬盘** - 迁移到 SSD
3. **内存不足** - 升级到 16GB+
4. **node_modules 过大** - 定期清理
5. **Windows 更新** - 确保系统最新

### 常见错误解决

**内存不足错误**:
```bash
# 减少内存分配
npm run build:win  # 而不是 build:win:turbo
```

**文件路径过长**:
```bash
# 项目已自动处理，如果仍有问题：
npm config set prefer-dedupe true
```

**权限错误**:
```bash
# 以管理员身份运行命令提示符
npm run build:win
```

## 📈 监控构建性能

### 使用内置基准测试
```bash
npm run benchmark
```

### 查看详细构建信息
```bash
# 启用详细日志
set DEBUG=vite:* && npm run build:win
```

### Windows 任务管理器监控
- CPU 使用率应接近 100%
- 内存使用应稳定增长
- 磁盘活动应持续但不过载

## 🎯 最佳实践

1. **首次构建前运行**: `npm run setup:win`
2. **使用 SSD 硬盘**存储项目
3. **定期清理**: `npm run clean` (如果有)
4. **关闭不必要的程序**减少内存竞争
5. **使用批处理文件**简化操作

## 📞 支持

如果在 Windows 系统上遇到构建问题：

1. 运行 `npm run setup:win` 检查系统配置
2. 查看生成的 `.env.windows` 文件
3. 尝试不同的构建模式
4. 检查 Windows Defender 设置

---

*本指南针对 Windows 10/11 系统优化，其他系统请使用标准构建命令。* 