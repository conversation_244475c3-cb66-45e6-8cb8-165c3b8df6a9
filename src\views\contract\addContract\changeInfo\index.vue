<template>
    <div class="change-info">
        <section-title title="变更基本信息"></section-title>
        <a-card :bordered="false" :body-style="{ padding: '16px 0 0' }">
            <a-form ref="formRef" :model="contractData" :rules="contractStore.editType === 'changeDetail' ? {} : rules" auto-label-width :disabled="contractStore.editType === 'changeDetail'">
                <a-grid :cols="2">
                    <a-grid-item>
                        <a-form-item label="变更类型" field="changeType">
                            <a-checkbox-group 
                                disabled 
                                :model-value="changeType" 
                                :key="`changeType-${changeType.join(',')}-${contractStore.editType}`"
                            >
                                <a-checkbox :value="1">主体变更</a-checkbox>
                                <a-checkbox :value="2">条款变更</a-checkbox>
                                <a-checkbox :value="3">费用条款变更</a-checkbox>
                            </a-checkbox-group>
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="变更执行日期" field="changeDate">
                            <a-date-picker 
                                v-model="contractData.changeDate" 
                                format="YYYY-MM-DD" 
                                :disabled-date="disabledChangeDate"
                                placeholder="请选择变更执行日期"
                            />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item :span="2">
                        <a-form-item label="变更说明" field="changeExplanation">
                            <a-textarea v-model="contractData.changeExplanation" :rows="4" :maxlength="300" />
                        </a-form-item>
                    </a-grid-item>
                    <a-grid-item>
                        <a-form-item label="附件">
                            <upload-file :readonly="contractStore.editType === 'changeDetail'" v-model="contractData.changeAttachments"></upload-file>
                        </a-form-item>
                    </a-grid-item>
                </a-grid>
            </a-form>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import uploadFile from '@/components/upload/uploadFile.vue';
import { useContractStore } from '@/store/modules/contract/index';
import { FormInstance } from '@arco-design/web-vue';
import dayjs from 'dayjs'

const contractStore = useContractStore()
// 根据编辑类型决定变更类型的数据源
const changeType = computed(() => {
    // 如果contractData中有changeType值，优先使用它（编辑变更或查看变更）
    if (contractStore.contractData.changeType) {
        return contractStore.contractData.changeType.split(',').map(Number);
    }
    
    // 如果contractData中没有changeType值，使用弹窗选择的值（新增变更）
    if (contractStore.changeType.length > 0) {
        return [...contractStore.changeType];
    }
    
    // 其他情况返回空数组
    return []
})
const contractData = computed(() => contractStore.contractData)

// 禁用变更执行日期的函数
const disabledChangeDate = (date: Date) => {
    const currentDate = dayjs(date)
    const startDate = contractData.value.startDate
    const endDate = contractData.value.endDate
    
    // 如果合同开始日期和结束日期都存在，则限制在这个范围内
    // 变更执行日期必须晚于合同开始日期，早于合同结束日期（不包含开始和结束日期）
    if (startDate && endDate) {
        return !currentDate.isAfter(dayjs(startDate), 'day') || !currentDate.isBefore(dayjs(endDate), 'day')
    }
    
    // 如果只有开始日期，则不能早于或等于开始日期
    if (startDate) {
        return !currentDate.isAfter(dayjs(startDate), 'day')
    }
    
    // 如果只有结束日期，则不能晚于或等于结束日期
    if (endDate) {
        return !currentDate.isBefore(dayjs(endDate), 'day')
    }
    
    // 如果都没有，则不限制
    return false
}

const rules = {
    changeDate: [
        { required: true, message: '请选择变更执行日期' },
        {
            validator: (value: string, callback: (error?: string) => void) => {
                if (value) {
                    const changeDate = dayjs(value)
                    const startDate = contractData.value.startDate
                    const endDate = contractData.value.endDate
                    
                    if (startDate && endDate) {
                        if (!changeDate.isAfter(dayjs(startDate), 'day')) {
                            callback(`变更执行日期必须晚于合同开始日期（${startDate}）`)
                            return
                        }
                        if (!changeDate.isBefore(dayjs(endDate), 'day')) {
                            callback(`变更执行日期必须早于合同结束日期（${endDate}）`)
                            return
                        }
                    } else if (startDate) {
                        if (!changeDate.isAfter(dayjs(startDate), 'day')) {
                            callback(`变更执行日期必须晚于合同开始日期（${startDate}）`)
                            return
                        }
                    } else if (endDate) {
                        if (!changeDate.isBefore(dayjs(endDate), 'day')) {
                            callback(`变更执行日期必须早于合同结束日期（${endDate}）`)
                            return
                        }
                    }
                }
                callback()
            }
        }
    ],
    changeExplanation: [{ required: true, message: '请输入变更说明' }],
}
const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            reject()
        } else {
            contractData.value.changeType = changeType.value.join(',')
            resolve(true)
        }
    })
}

defineExpose({
    validate
})
</script>

<style lang="less" scoped></style>