import auth from '@/plugins/auth';
import router, { constantRoutes, dynamicRoutes } from '@/router';
import { getRouters } from '@/api/auth/router';
import Layout from '@/layout/default-layout.vue';
import { defineStore } from 'pinia';
import { RouteRecordRaw } from 'vue-router';
import useCenterStore from '@/store/modules/center';

// 定义路由相关接口
interface RouteOption {
    hidden?: boolean;
    permissions?: string[];
    roles?: string[];
    component?: any;
    children?: RouteOption[];
    redirect?: string;
    path: string;
    name?: string;
    level?: number;
    meta: {
        [key: string]: any;
    };
}

interface PermissionState {
    sidebarRouters: RouteRecordRaw[];
}

// 匹配views里面所有的.vue文件
const modules = import.meta.glob('@/views/**/*.vue');

function filterChildren(
    childrenMap: RouteOption[],
    lastRouter: RouteOption | false = false
): RouteOption[] {
    let children: RouteOption[] = [];
    childrenMap.forEach((el) => {
        if (lastRouter) {
            el.path = `${lastRouter.path}/${el.path}`;
        }
        if (
            el.children &&
            el.children.length &&
            el.component === 'ParentView'
        ) {
            children = children.concat(filterChildren(el.children, el));
        } else {
            children.push(el);
        }
    });
    return children;
}

function filterAsyncRouter(
    asyncRouterMap: RouteOption[],
    lastRouter = false,
    type = false
): RouteOption[] {
    return asyncRouterMap.filter((route) => {
        if(!route.path.includes('/')) {
            route.path = '/' + route.path
        }
        if (type && route.children) {
            route.children = filterChildren(route.children);
        }
        if (route.component) {
            if (route.component === 'Layout') {
                route.component = Layout;
            } else {
                route.component = modules[`/src/views/${route.component}.vue`];
            }
        }
        if (route.children != null && route.children && route.children.length) {
            route.children = filterAsyncRouter(route.children, !!route, type);
        } else {
            delete route.children;
            delete route.redirect;
        }
        return true;
    });
}

// 将routeOption转换为RouteRecordRaw
function convertRouteOptionToRouteRecordRaw(
    routeOptions: RouteOption[]
): RouteRecordRaw[] {
    return routeOptions.map((routeOption: RouteOption): RouteRecordRaw => {
        const routeRecordRaw: RouteRecordRaw = {
            path: routeOption.path,
            name: routeOption.name,
            component: routeOption.component,
            meta: {
                hidden: routeOption.hidden,
                ...routeOption.meta,
            },
            redirect: routeOption.redirect,
            children: routeOption.children
                ? convertRouteOptionToRouteRecordRaw(routeOption.children)
                : [],
        };
        return routeRecordRaw;
    });
}

export function filterDynamicRoutes(routes: RouteOption[]): RouteOption[] {
    const res: RouteOption[] = [];
    routes.forEach((route) => {
        if (route.permissions) {
            if (auth.hasPermiOr(route.permissions)) {
                res.push(route);
            }
        } else if (route.roles) {
            if (auth.hasRoleOr(route.roles)) {
                res.push(route);
            }
        } else {
            res.push(route);
        }
    });
    return res;
}

const usePermissionStore = defineStore('permission', {
    state: (): PermissionState => ({
        sidebarRouters: [],
    }),
    actions: {
        setSidebarRouters(routes: RouteRecordRaw[]) {
            routes.forEach((route: RouteRecordRaw) => {
                if (route.meta) {
                    route.meta.level = 1;
                } else {
                    route.meta = { level: 1 };
                }
            });
            this.sidebarRouters = routes;
        },
        generateRoutes() {
            return new Promise<RouteRecordRaw[]>((resolve) => {
                getRouters(useCenterStore().currentCenter).then((res) => {
                    const sidebarData = JSON.parse(JSON.stringify(res.data));
                    const rewriteData = JSON.parse(JSON.stringify(res.data));
                    const sidebarRoutes: RouteRecordRaw[] =
                        convertRouteOptionToRouteRecordRaw(
                            filterAsyncRouter(sidebarData)
                        );
                    const rewriteRoutes: RouteRecordRaw[] =
                        convertRouteOptionToRouteRecordRaw(
                            filterAsyncRouter(rewriteData, false, true)
                        );
                    // 注册本地动态路由（不在菜单栏展示的页面，如详情页，编辑页等等）
                    const asyncRoutes = dynamicRoutes;
                    asyncRoutes.forEach((route: any) => {
                        router.addRoute(route);
                    });
                    this.setSidebarRouters(
                        constantRoutes.concat(sidebarRoutes)
                    );
                    resolve(rewriteRoutes);
                });
            });
        },
    },
});

export default usePermissionStore;
