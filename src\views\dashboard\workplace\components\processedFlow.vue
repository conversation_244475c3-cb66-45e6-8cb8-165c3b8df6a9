<template>
  <div class="processed-flow">
    <div class="flow-title">
      <div class="title-decorator"></div>
      待处理流水
    </div>
    <div class="flow-content">
      <div class="flow-items">
        <div class="flow-item">
          <div class="item-label">公共</div>
          <div class="item-count">4</div>
        </div>
        <div class="flow-item">
          <div class="item-label">未记账</div>
          <div class="item-count danger">23</div>
        </div>
        <div class="flow-item">
          <div class="item-label">部分记账</div>
          <div class="item-count">6</div>
        </div>
      </div>
      <div class="flow-items col-2">
        <div class="flow-item">
          <div class="item-label">3个月未明</div>
          <div class="item-count danger">12</div>
        </div>
        <div class="flow-item">
          <div class="item-label">待财务确认</div>
          <div class="item-count">12</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style lang="less" scoped>
.processed-flow {
  background: #fff;
  border-radius: 10px 0 0 0;
  padding: 20px 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  
  .flow-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #000;
    position: relative;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    z-index: 2;
    
    .title-decorator {
      width: 5px;
      height: 19px;
      background-color: #0071ff;
      border-radius: 4px 0px 4px 0px;
      margin-right: 14px;
    }
  }
  
  .flow-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 8px;
    position: relative;
    border-radius: 10px 0 0 0;
    background: linear-gradient(90deg, rgba(54, 161, 246, 0.14) 0%, rgba(255, 255, 255, 0) 100%);
    
    .flow-items {
      position: relative;
      z-index: 2;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
      height: 100%;
      padding: 6px 0;
      
      &.col-2 {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    .flow-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      
      .item-label {
        font-size: 14px;
        color: #3A4252;
        line-height: 1.4;
        margin-bottom: 4px;
      }
      
      .item-count {
        font-size: 20px;
        color: #3A4252;
        line-height: 1.4;
        font-weight: 500;
        
        &.danger {
          color: rgb(var(--danger-6));
        }
      }
    }
  }
}
</style>