<template>
	<div class="template-print-container">
		<a-card class="general-card">
			<!-- 搜索区域 -->
			<a-row>
				<a-col :flex="1">
					<a-form :model="queryParams" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" label-align="right">
						<a-row :gutter="16">
							<a-col :span="8">
								<a-form-item field="templateName" label="模板名称">
									<a-input v-model="queryParams.templateName" placeholder="请输入模板名称" allow-clear />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="applyLevel" label="适用层级">
									<a-select v-model="queryParams.applyLevel" placeholder="请选择适用层级" allow-clear>
										<a-option value="1">集团级</a-option>
										<a-option value="2">项目级</a-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="printType" label="套打类型">
									<a-select v-model="queryParams.printType" placeholder="请选择套打类型" allow-clear>
										<a-option 
											v-for="item in printTypeOptions" 
											:key="item.value" 
											:value="item.value.toString()"
										>
											{{ item.label }}
										</a-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="contractPurpose" label="用途">
									<a-select v-model="queryParams.contractPurpose" placeholder="请选择用途" allow-clear>
										<a-option 
											v-for="item in currentPurposeOptions" 
											:key="item.value" 
											:value="item.value.toString()"
										>
											{{ item.label }}
										</a-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="status" label="状态">
									<a-select v-model="queryParams.status" placeholder="请选择状态" allow-clear>
										<a-option value="0">新增</a-option>
										<a-option value="1">生效</a-option>
										<a-option value="2">失效</a-option>
									</a-select>
								</a-form-item>
							</a-col>
						</a-row>
					</a-form>
				</a-col>
				<a-divider style="height: 84px" direction="vertical" />
				<a-col :flex="'86px'" style="text-align: right">
					<a-space direction="vertical" :size="18">
						<a-button type="primary" @click="handleQuery">
							<template #icon>
								<icon-search />
							</template>
							查询
						</a-button>
						<a-button @click="resetQuery">
							<template #icon>
								<icon-refresh />
							</template>
							重置
						</a-button>
					</a-space>
				</a-col>
			</a-row>
			<a-divider style="margin-top: 0" />

			<!-- 数据表格 -->
			<a-row style="margin-bottom: 16px">
				<!-- 新增模板 靠右 -->
				<a-col :span="24" style="text-align: right">
					<a-space>
						<a-button v-permission="['print:template:add']" type="primary" @click="handleAdd">
							<template #icon>
								<icon-plus />
							</template>
							新增模板
						</a-button>
					</a-space>
				</a-col>
			</a-row>
			<a-table :columns="columns" :data="tableData" :loading="loading" :pagination="pagination" :bordered="{ cell: true }" :scroll="{ x: 1 }" @page-change="onPageChange" @page-size-change="onPageSizeChange">
				<template #index="{ rowIndex }">
                    {{
                        rowIndex +
                        1 +
                        (pagination.current - 1) * pagination.pageSize
                    }}
                </template>
				<!-- 适用层级 -->
				<template #applyLevel="{ record }">
					<span>{{ record.applyLevel === 1 ? '集团' : '项目' }}</span>
				</template>
				<!-- 套打类型 -->
				<template #printType="{ record }">
					<!-- 【更新】使用数据字典映射显示套打类型 -->
					<span>{{ printTypeMap[record.printType] || '未知' }}</span>
				</template>
				
				<!-- 用途 -->
				<template #contractPurpose="{ record }">
					<span>{{ getPurposeText(record.printType, record.contractPurpose) }}</span>
				</template>
				
				<!-- 状态列 -->
				<template #status="{ record }">
					<a-tag :color="statusMap[record.status]?.color || 'gray'">
						{{ statusMap[record.status]?.text || '未知' }}
					</a-tag>
				</template>

				<!-- 操作列 -->
				<template #operations="{ record }">
					<div>
						<a-button v-permission="['print:template:edit']" type="text" @click="handleEdit(record)" size="mini">编辑</a-button>
						<a-button v-permission="['print:template:editStatus']" type="text" @click="handleStatusChange(record)" style="margin-left: 8px" size="mini">
							{{ record.status === 1 ? '禁用' : '启用' }}
						</a-button>
						<a-popconfirm content="确定要删除该模板吗？" @ok="handleDelete(record)" style="margin-left: 8px" >
							<a-button v-permission="['print:template:remove']" type="text" status="danger" size="mini">删除</a-button>
						</a-popconfirm>
					</div>
				</template>
			</a-table>
		</a-card>

		<!-- 新增/编辑模板抽屉 -->
		<add-form ref="formRef" @success="onSuccess" @cancel="onCancel" />
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconSearch, IconRefresh, IconPlus } from '@arco-design/web-vue/es/icon'
import {
	getTemplateList,
	deleteTemplate,
	updateTemplateStatus,
	getTemplateDetail
} from '@/api/templatePrint';

import { TemplateData, TemplateQueryParams } from './types';
import addForm from './components/addForm.vue'
import { DictType, dictData } from '@/dict/data'

// 查询参数
const queryParams = reactive<Partial<TemplateQueryParams>>({
	templateName: '',
	applyLevel: undefined,
	printType: undefined,
	contractPurpose: undefined,
	status: undefined
});

// 【新增】从数据字典获取选项数据
const printTypeOptions = computed(() => dictData[DictType.TEMPLATE_PRINT_TYPE] || [])

// 【修正】根据选择的套打类型动态获取用途选项，未选择套打类型时返回空数组
const currentPurposeOptions = computed(() => {
	const printType = queryParams.printType
	if (printType === '1') {
		return dictData[DictType.TEMPLATE_PURPOSE_CONTRACT] || []
	} else if (printType === '2') {
		return dictData[DictType.TEMPLATE_PURPOSE_AGREEMENT] || []
	} else if (printType === '3') {
		return dictData[DictType.TEMPLATE_PURPOSE_NOTICE] || []
	}
	// 【修正】如果没有选择套打类型，返回空数组，不显示任何用途选项
	return []
})

// 【新增】监听套打类型变化，清空用途选择
watch(() => queryParams.printType, (newValue, oldValue) => {
	if (newValue !== oldValue) {
		// 套打类型变化时清空用途选择
		queryParams.contractPurpose = undefined
	}
})

// 用途映射：根据套打类型获取对应的用途文本
const getPurposeText = (printType: number, contractPurpose: number) => {
	let purposeOptions: Array<{value: number, label: string}> = []
	
	if (printType === 1) {
		purposeOptions = dictData[DictType.TEMPLATE_PURPOSE_CONTRACT] || []
	} else if (printType === 2) {
		purposeOptions = dictData[DictType.TEMPLATE_PURPOSE_AGREEMENT] || []
	} else if (printType === 3) {
		purposeOptions = dictData[DictType.TEMPLATE_PURPOSE_NOTICE] || []
	}
	
	const purpose = purposeOptions.find(item => item.value === contractPurpose)
	return purpose ? purpose.label : ''
}

// 【新增】套打类型映射：从数据字典生成
const printTypeMap = computed(() => {
	const map: Record<number, string> = {}
	dictData[DictType.TEMPLATE_PRINT_TYPE]?.forEach(item => {
		map[item.value] = item.label
	})
	return map
})

// 状态映射
const statusMap: Record<number, {text: string, color: string}> = {
	0: { text: '新增', color: 'blue' },
	1: { text: '生效', color: 'green' },
	2: { text: '失效', color: 'red' }
}

// 表格列定义
const columns = [
	{ title: '序号', dataIndex: 'index', slotName: 'index', width: 70, align: 'center', ellipsis: true, tooltip: true },
	{ title: '模板名称', dataIndex: 'templateName', key: 'templateName', width: 180, ellipsis: true, tooltip: true, align: 'center'},
	{ 
		title: '适用层级', 
		dataIndex: 'applyLevel', 
		key: 'applyLevel', 
		width: 100,
		ellipsis: true,
		tooltip: true,
		align: 'center',
		slotName: 'applyLevel'
	},
	{ 
		title: '适用范围', 
		dataIndex: 'applyScope', 
		key: 'applyScope', 
		width: 200, 
		ellipsis: true, 
		tooltip: true, 
		align: 'center',
	},
	{ 
		title: '套打类型', 
		dataIndex: 'printType', 
		key: 'printType', 
		width: 100,
		ellipsis: true,
		tooltip: true,
		align: 'center',
		slotName: 'printType'
	},
	{ 
		title: '用途', 
		dataIndex: 'contractPurpose', 
		key: 'contractPurpose', 
		width: 100,
		ellipsis: true,
		tooltip: true,
		align: 'center',
		slotName: 'contractPurpose'
	},
	{ 
		title: '生效日期', 
		dataIndex: 'effectiveDate', 
		key: 'effectiveDate', 
		width: 140,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{ 
		title: '失效日期', 
		dataIndex: 'expirationDate', 
		key: 'expirationDate', 
		width: 140,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{ 
		title: '状态', 
		dataIndex: 'status', 
		key: 'status', 
		width: 100,
		ellipsis: true,
		tooltip: true,
		align: 'center',
		slotName: 'status'
	},
	{ title: '创建人', dataIndex: 'createByName', key: 'createByName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
	{ title: '创建日期', dataIndex: 'createTime', key: 'createTime', width: 140, ellipsis: true, tooltip: true, align: 'center' },
	{ 
		title: '操作',
		key: 'operations', 
		width: 220,
		ellipsis: true,
		tooltip: true,
		fixed: 'right',
		align: 'center',
		slotName: 'operations'
	}
];

// 表格数据
const tableData = ref<TemplateData[]>([]);
const loading = ref(false);
const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showTotal: true,
	showJumper: true,
	showPageSize: true
});

// 表单引用
const formRef = ref();

// 获取表格数据
const getList = async () => {
	loading.value = true
	try {
		const res = await getTemplateList({
			...queryParams,
			pageNum: pagination.current,
			pageSize: pagination.pageSize
		} as TemplateQueryParams)
		
		if (res && res.rows) {
			tableData.value = res.rows || []
			pagination.total = res.total || 0
		}
	} catch (error) {
		console.error('获取模板列表失败:', error)
		// Message.error('获取模板列表失败')
	} finally {
		loading.value = false
	}
}

// 查询
const handleQuery = () => {
	pagination.current = 1
	getList()
}

// 重置查询
const resetQuery = () => {
	// 重置所有查询条件
	Object.keys(queryParams).forEach(key => {
		queryParams[key as keyof typeof queryParams] = undefined
	})
	// 重新查询
	handleQuery()
}

// 分页方法
const onPageChange = (current: number) => {
	pagination.current = current
	getList()
}

const onPageSizeChange = (pageSize: number) => {
	pagination.pageSize = pageSize
	getList()
}

// 新增模板
const handleAdd = () => {
	formRef.value?.open()
}

// 编辑模板
const handleEdit = async (record: any) => {
	const res = await getTemplateDetail(record.id)
	console.log(res)
	formRef.value?.edit(res.data)
}

// 删除模板
const handleDelete = async (record: any) => {
	try {
		await deleteTemplate(record.id)
		Message.success('删除成功')
		getList()
	} catch (error) {
		console.error('删除失败:', error)
		// Message.error('删除失败')
	}
}

// 状态变更
const handleStatusChange = async (record: any) => {
	try {
		// 【变更】状态值从string改为number
		const newStatus: number = record.status === 1 ? 2 : 1 // 1-生效，2-失效
		await updateTemplateStatus({
			templateId: record.id,
			status: newStatus
		})
		Message.success('状态更新成功')
		getList()
	} catch (error) {
		console.error('状态更新失败:', error)
		// Message.error('状态更新失败')
	}
}

// 操作成功回调
const onSuccess = () => {
	getList()
}

// 取消回调
const onCancel = () => {
	console.log('取消操作')
}

// 页面加载时获取数据
onMounted(() => {
	getList()
})
</script>

<style scoped>
.template-print-container {
	padding: 16px 0;
}

.general-card {
	box-sizing: border-box;
}
</style>