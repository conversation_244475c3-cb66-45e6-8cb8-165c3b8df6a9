import http from './index';

// 账单相关接口类型定义
export interface CostAddDTO {
  createBy?: string;
  createByName?: string;
  createTime?: string;
  updateBy?: string;
  updateByName?: string;
  updateTime?: string;
  id?: string;
  projectId?: string;
  chargeType?: number; // 收费类别: 0-定单, 1-合同
  bizId?: string;
  bizNo?: string;
  customerId?: string;
  customerName?: string;
  costType?: number; // 账单类型: 1-保证金,2-租金,3-其他费用
  period?: number;
  subjectId?: string;
  subjectName?: string;
  startDate?: string;
  endDate?: string;
  receivableDate?: string;
  status?: number; // 账单状态: 0-待收、1-待付、2-已收、3-已付
  canPay?: boolean;
  totalAmount?: number;
  discountAmount?: number;
  actualReceivable?: number;
  receivedAmount?: number;
  carryoverAmount?: number;
  confirmStatus?: number; // 确认状态: 0-待确认, 1-已确认
  isDel?: boolean;
}

export interface CostQueryDTO {
  params?: Record<string, any>;
  pageNum: number;
  pageSize: number;
  id?: string;
  projectId?: string;
  chargeType?: number;
  bizId?: string;
  bizNo?: string;
  customerId?: string;
  customerName?: string;
  costType?: number;
  period?: number;
  subjectId?: string;
  subjectName?: string;
  startDate?: string;
  endDate?: string;
  receivableDate?: string;
  status?: number;
  canPay?: boolean;
  totalAmount?: number;
  discountAmount?: number;
  actualReceivable?: number;
  receivedAmount?: number;
  carryoverAmount?: number;
  confirmStatus?: number;
  createBy?: string;
  createByName?: string;
  createTime?: string;
  updateBy?: string;
  updateByName?: string;
  updateTime?: string;
  isDel?: boolean;
  contractType?: number; // 合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房
  subStatus?: number; // 子状态(0-逾期,1-今日,2-近7天,3-待确认,4-已确认)
  roomName?: string;
  statusList?: string; // 账单状态（多选拼接）
  receivableDateStart?: string;
  receivableDateEnd?: string;
}

export interface CostFlowRelAddDTO {
  createBy?: string;
  createByName?: string;
  createTime?: string;
  updateBy?: string;
  updateByName?: string;
  updateTime?: string;
  id?: string;
  costId?: string;
  refundId?: string;
  flowId?: string;
  flowNo?: string;
  type?: number; // 账单类型：1-收款 2-转入 3-转出 4-退款
  confirmStatus?: number; // 确认状态: 0-未确认、1-自动确认、2-手动确认
  confirmTime?: string;
  confirmUserId?: string;
  confirmUserName?: string;
  payAmount?: number;
  acctAmount?: number;
  isDel?: boolean;
}

export interface CostSaveRecordDTO {
  costId: string;
  flowRelList: CostFlowRelAddDTO[];
}

export interface CostConfirmRecordDTO {
  costId: string;
  isOneKeyConfirm: boolean;
  flowRelId?: string; // 记账记录id（非一键确认时必填）
}

export interface CostSummaryVo {
  overdueCount?: number; // 逾期数
  todayCount?: number; // 今日数
  sevenDaysCount?: number; // 近七天数
  pendingConfirmCount?: number; // 待确认数
  confirmedCount?: number; // 已确认数
}

export interface CostVo {
  id?: string;
  projectId?: string;
  projectName?: string;
  chargeType?: number;
  bizId?: string;
  bizNo?: string;
  customerId?: string;
  customerName?: string;
  costType?: number;
  period?: number;
  subjectId?: string;
  subjectName?: string;
  startDate?: string;
  endDate?: string;
  receivableDate?: string;
  status?: number;
  canPay?: boolean;
  isRevenueBill?: boolean;
  isRevenueGenerated?: boolean;
  totalAmount?: number;
  discountAmount?: number;
  actualReceivable?: number;
  receivedAmount?: number;
  carryoverAmount?: number;
  confirmStatus?: number;
  createByName?: string;
  updateByName?: string;
  isDel?: boolean;
  contractType?: number;
  roomName?: string;
  contractStatus?: number;
  contractStatusTwo?: number;
}

export interface CostFlowRelVo {
  id?: string;
  costId?: string;
  refundId?: string;
  flowId?: string;
  flowNo?: string;
  type?: number;
  confirmStatus?: number;
  confirmTime?: string;
  confirmUserId?: string;
  confirmUserName?: string;
  payAmount?: number;
  acctAmount?: number;
  createByName?: string;
  createTime?: string;
  updateByName?: string;
  isDel?: boolean;
  projectId?: string;
  projectName?: string;
  entryTime?: string;
  payType?: number;
  payMethod?: number;
  orderNo?: string;
  usedAmount?: number;
  payerName?: string;
  target?: string;
  merchant?: string;
}

export interface CostFlowLogVo {
  id?: string;
  costId?: string;
  flowId?: string;
  flowNo?: string;
  type?: number;
  carryoverId?: string;
  carryoverNo?: string;
  amount?: number;
  createByName?: string;
  updateByName?: string;
  isDel?: boolean;
}

export interface CostDetailVo {
  cost?: CostVo;
  flowRelList?: CostFlowRelVo[];
  flowLogList?: CostFlowLogVo[];
}

export interface TableDataInfo<T = any> {
  total?: number;
  rows?: T[];
  code?: number;
  msg?: string;
}

// 枚举定义
export enum CostStatus {
  PENDING_RECEIVE = 0, // 待收
  PENDING_PAY = 1,     // 待付
  RECEIVED = 2,        // 已收
  PAID = 3            // 已付
}

export enum CostType {
  DEPOSIT = 1,  // 保证金
  RENT = 2,     // 租金
  OTHER = 3     // 其他费用
}

export enum ConfirmStatus {
  PENDING = 0,    // 待确认
  CONFIRMED = 1   // 已确认
}

export enum ContractType {
  NON_DORMITORY = 0, // 非宿舍
  DORMITORY = 1,     // 宿舍
  MULTIPLE = 2,      // 多经
  DAILY_RENT = 3     // 日租房
}

// 修改账单
export const editCost = (data: CostAddDTO) => {
  return http.put('/business-rent-admin/cost', data);
};

// 新增账单
export const addCost = (data: CostAddDTO) => {
  return http.post('/business-rent-admin/cost', data);
};

// 账单统计
export const getCostSummary = (data: CostQueryDTO) => {
  return http.post<CostSummaryVo>('/business-rent-admin/cost/summary', data);
};

// 保存记账
export const saveCostRecord = (data: CostSaveRecordDTO) => {
  return http.post<boolean>('/business-rent-admin/cost/saveRecord', data);
};

// 查询账单列表 (POST)
export const getCostListPost = (data: CostQueryDTO) => {
  return http.post<TableDataInfo>('/business-rent-admin/cost/list', data);
};

// 查询账单列表 (GET) - 主要使用这个
export const getCostList = (params: CostQueryDTO) => {
  return http.get<TableDataInfo>('/business-rent-admin/cost/list', params);
};

// 导出账单列表
export const exportCostList = (params: CostQueryDTO) => {
  return http.download('/business-rent-admin/cost/export', params);
};

// 确认记账
export const confirmCostRecord = (data: CostConfirmRecordDTO) => {
  return http.post<boolean>('/business-rent-admin/cost/confirmRecord', data);
};

// 取消记账
export const cancelCostRecord = (flowRelId: string) => {
  return http.post<boolean>('/business-rent-admin/cost/cancelRecord', {}, {
    params: { 'flowRelId': flowRelId }
  });
};

// 查看账单码
export const getCostQrcode = (costId: string) => {
  return http.get<string>('/business-rent-admin/cost/qrcode', { 'id': costId });
};

// 查看账单流水详情
export const getCostDetail = (costId: string) => {
  return http.get<CostDetailVo>('/business-rent-admin/cost/detail', { 'id': costId });
};

// 获取账单详细信息
export const getCostInfo = (costId: string) => {
  return http.get('/business-rent-admin/cost/getInfo', { 'id': costId });
};

// 删除账单
export const deleteCost = (costIds: string[]) => {
  return http.delete('/business-rent-admin/cost/delete', { 'ids': costIds });
};

// 默认导出（保持兼容性）
export default {
  editCost,
  addCost,
  getCostSummary,
  saveCostRecord,
  getCostListPost,
  getCostList,
  exportCostList,
  confirmCostRecord,
  cancelCostRecord,
  getCostQrcode,
  getCostDetail,
  getCostInfo,
  deleteCost
};