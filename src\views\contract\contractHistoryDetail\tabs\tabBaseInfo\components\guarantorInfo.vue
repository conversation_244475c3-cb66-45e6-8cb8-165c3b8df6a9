<template>
    <section>
        <sectionTitle title="担保人信息" />
        <div class="content">
            <a-grid :cols="1" :col-gap="16" :row-gap="0">
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">姓名</label>
                        <div class="detail-text">{{ contractData.customer.guarantorName || '无' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">手机号</label>
                        <div class="detail-text">{{ contractData.customer.guarantorPhone || '无' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">证件类型</label>
                        <div class="detail-text">{{idTypeOptions.find(item => item.value ===
                            contractData.customer.guarantorIdType)?.label || '无' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">证件号码</label>
                        <div class="detail-text">{{ contractData.customer.guarantorIdNumber || '无' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">通讯地址</label>
                        <div class="detail-text">{{ contractData.customer.guarantorAddress || '无' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">身份证正反面</label>
                        <a-grid :cols="2" :col-gap="16" style="margin-top: 8px;">
                            <div class="card-item">
                                <a-image v-if="contractData.customer.guarantorIdFront" width="100%" class="card-bg" :src="contractData.customer.guarantorIdFront" />
                            </div>
                            <div class="card-item">
                                <a-image v-if="contractData.customer.guarantorIdBack" width="100%" class="card-bg" :src="contractData.customer.guarantorIdBack" />
                            </div>
                        </a-grid>
                    </div>
                </a-grid-item>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import idCardFront from '@/assets/images/contract/idCard-front.png';
import idCardBack from '@/assets/images/contract/idCard-back.png';
import { useContractStore } from '@/store/modules/contract'
import { ContractAddDTO } from '@/api/contract'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)

// 证件类型选项
const idTypeOptions = [
    { label: '身份证', value: '1' },
    { label: '护照', value: '2' },
    { label: '军官证', value: '3' }
]
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;

        .card-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;

            .card-bg {
                width: 100%;
                height: 140px;
                display: block;
                object-fit: cover;
            }

            .card-label {
                position: absolute;
                left: 50%;
                bottom: 0;
                transform: translateX(-50%);
                width: 100%;
                height: 38px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                background: rgba(0, 0, 0, 0.5);
                font-size: 14px;
            }
        }
    }
}

.form-item {
    margin-bottom: 16px;
    
    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>