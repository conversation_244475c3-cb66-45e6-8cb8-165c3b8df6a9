<template>
    <section>
        <sectionTitle title="信息总览"></sectionTitle>
        <a-card :bordered="false" :body-style="{padding: '16px 0'}">
            <a-table :columns="columns" :data="tableData" :pagination="false" :scroll="{ x: 1000 }" :bordered="{ cell: true }">
                <template #depositAmount="{ record }">
                    {{ formatAmount(record.depositAmount) }}
                </template>
                <template #rentAmount="{ record }">
                    {{ formatAmount(record.rentAmount) }}
                </template>
                <template #otherAmount="{ record }">
                    {{ formatAmount(record.otherAmount) }}
                </template>
                <template #discountAmount="{ record }">
                    {{ formatAmount(record.discountAmount) }}
                </template>
                <template #actualReceivableAmount="{ record }">
                    {{ formatAmount(record.actualReceivableAmount) }}
                </template>
                <!-- <template #receivedAmount="{ record }">
                    {{ formatAmount(record.receivedAmount) }}
                </template>
                <template #remainingAmount="{ record }">
                    {{ formatAmount(record.remainingAmount) }}
                </template> -->
            </a-table>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { TableData } from '@arco-design/web-vue/es/table/interface';
import { useContractStore } from '@/store/modules/contract'
import { ContractVo } from '@/api/contract';

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.historyVersionContractDetailCosts as ContractVo)

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

interface TableDataItem {
    depositAmount: number;
    rentAmount: number;
    otherAmount: number;
    discountAmount: number;
    actualReceivableAmount: number;
    receivedAmount: number;
    remainingAmount: number;
}
const tableData = ref<TableDataItem[]>([
    {
        depositAmount: 0,
        rentAmount: 0,
        otherAmount: 0,
        discountAmount: 0,
        actualReceivableAmount: 0,
        receivedAmount: 0,
        remainingAmount: 0,
    }
])

const columns: TableData = [
    {
        title: '保证金（元）',
        dataIndex: 'depositAmount',
        slotName: 'depositAmount',
        width: 130,
        align: 'right',
    },
    {
        title: '租金账单总额（元）',
        dataIndex: 'rentAmount',
        slotName: 'rentAmount',
        width: 160,
        align: 'right',
    },
    {
        title: '其他费用账单总额（元）',
        dataIndex: 'otherAmount',
        slotName: 'otherAmount',
        width: 180,
        align: 'right',
    },
    {
        title: '优惠金额（元）',
        dataIndex: 'discountAmount',
        slotName: 'discountAmount',
        width: 140,
        align: 'right',
    },
    {
        title: '总应收金额（元）',
        dataIndex: 'actualReceivableAmount',
        slotName: 'actualReceivableAmount',
        width: 140,
        align: 'right',
    },
    // {
    //     title: '已收金额（元）',
    //     dataIndex: 'receivedAmount',
    //     slotName: 'receivedAmount',
    //     width: 140,
    //     align: 'right',
    // },
    // {
    //     title: '剩余应收金额（元）',
    //     dataIndex: 'remainingAmount',
    //     slotName: 'remainingAmount',
    //     width: 160,
    //     align: 'right',
    // }
]

/**
 * 监听应收计划计算结果
 * 1. 保证金为costs中所有costType为保证金的数据之和（actualReceivable）
 * 2. 租金账单总额为costs中所有costType为租金的数据之和（totalAmount）
 * 3. 其他费用账单总额为costs中所有costType为其他费用的数据之和（totalAmount）
 * 4. 优惠金额为costs中所有discountAmount之和
 * 5. 总应收金额为costs中所有actualReceivable之和
 * 6. 已收金额为costs中所有receivedAmount之和
 * 7. 剩余应收金额为总应收金额 - 已收金额
 */
watch(contractCosts, (newVal) => {
    if (newVal.costs && newVal.costs.length > 0) {
        const depositAmount = newVal.costs.filter(item => item.costType === 1).reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
        const rentAmount = newVal.costs.filter(item => item.costType === 2).reduce((sum, item) => sum + (item.totalAmount || 0), 0)
        const otherAmount = newVal.costs.filter(item => item.costType === 3).reduce((sum, item) => sum + (item.totalAmount || 0), 0)
        const discountAmount = newVal.costs.reduce((sum, item) => sum + (item.discountAmount || 0), 0)
        const actualReceivableAmount = newVal.costs.reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
        const receivedAmount = newVal.costs.reduce((sum, item) => sum + (item.receivedAmount || 0), 0)
        const remainingAmount = actualReceivableAmount - receivedAmount
        tableData.value = [
            {
                depositAmount,
                rentAmount,
                otherAmount,
                discountAmount,
                actualReceivableAmount,
                receivedAmount,
                remainingAmount,
            }
        ]
    }
}, { deep: true, immediate: true })
</script>

<style lang="less" scoped></style>