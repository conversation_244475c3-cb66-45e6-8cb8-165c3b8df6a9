<template>
  <div class="area-change-alert">
    <!-- 搜索区域 -->
    <a-row>
      <a-col :flex="1">
        <a-form :model="formModel" auto-label-width label-align="right">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="projectId" label="项目">
                <ProjectSelector v-model="formModel.projectId" @change="handleProjectSelectorChange" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="location" label="地块/楼栋">
                <a-input v-model="formModel.location" placeholder="请输入地块/楼栋" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="roomName" label="房源名称">
                <a-input v-model="formModel.roomName" placeholder="请输入名称" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <a-divider style="height: 32px" direction="vertical" />
      <a-col :flex="'86px'" style="text-align: right;">
        <a-space :size="18">
          <a-button v-permission="['asset:change:list']" type="primary" @click="search">
            <template #icon>
              <icon-search />
            </template>
            查询
          </a-button>
          <a-button @click="reset">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-divider style="margin-top: 16px" />
    <!-- 表格区域 -->
    <a-row style="margin-bottom: 12px">
      <a-col :span="24" style="text-align: right">
        <a-space>
          <a-button v-permission="['asset:change:add']" type="primary" @click="handleBatchProcess">
            批量申请变更
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-table row-key="id" :loading="loading" :pagination="pagination" :columns="columns" :data="tableData"
      :bordered="{ cell: true }" :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        selectedRowKeys: selectedKeys
      }" @select="onSelect" @select-all="onSelectAll" @page-change="onPageChange" @page-size-change="onPageSizeChange"
      :scroll="{ x: 2000 }">
      <template #index="{ rowIndex }">
        {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
      </template>
      <template #roomName="{ record }">
        <PermissionLink v-permission="['asset:project:room:detail']" @click="handleRoomName(record)">{{ record.roomName }}</PermissionLink>
      </template>
      <template #buildingAreaDiff="{ record }">
        <span style="color: #FF0000;">{{ record.buildingAreaDiff }}</span>
      </template>
      <template #operations="{ record }">
        <a-button v-permission="['asset:change:add']" type="text" size="mini" @click="handleChange(record)">
          申请变更
        </a-button>
      </template>
    </a-table>
    <chooseRoom ref="chooseRoomRef" @next="nextClick" />
    <changeForm ref="changeFormRef" v-model:visible="changeFormVisible" :project-id="selectProjectData.projectId"
      :project-name="selectProjectData.projectName" :selected-rooms="selectedRooms" :selected-room-data="selectedRoomData"
      mode="add" @submit="handleFormSubmit" @cancel="handleFormCancel"
      @add-asset="handleAddMoreAsset" />
    <assetDetail ref="assetDetailRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useRouter } from 'vue-router'
import { getLedgerAreaChange, type LedgerAreaChangeDto, type AssetChangeRoomVo } from '@/api/asset/projectLedger'
import { getProjectList } from '@/api/asset/projectReceive'
import ProjectSelector from '@/components/projectSelector/index.vue'
import changeForm from '@/views/asset/change/components/changeForm.vue'
import chooseRoom from '@/views/asset/change/components/chooseRoom.vue'
import assetDetail from './assetDetail.vue'
import { useDict } from '@/utils/dict'

// 字典支持
const { product_type } = useDict('product_type')

// 产品类型转换
const getProductTypeText = (value: string | number) => {
    if (!value) return '-'
    const item = product_type.value.find((item: any) => item.value === String(value))
    return item ? item.label : value
}

const router = useRouter()

interface ProjectOption {
  id: string
  name: string
}

interface TableItem extends AssetChangeRoomVo {
  id: string
  projectId: string
  projectName: string
}

// 表格数据
const loading = ref(false)
const selectedKeys = ref<string[]>([])
const tableData = ref<TableItem[]>([])
const projectOptions = ref<ProjectOption[]>([])
const assetDetailRef = ref<InstanceType<typeof assetDetail>>()

// 表格列定义
const columns = [
  {
    title: '序号',
    slotName: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '房源名称',
    slotName: 'roomName',
    width: 120,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '所属项目',
    dataIndex: 'projectName',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '所属地块',
    dataIndex: 'parcelName',
    width: 120,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '楼栋',
    dataIndex: 'buildingName',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '产品类型',
    dataIndex: 'productType',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true,
    render: ({ record }: { record: any }) => {
      return getProductTypeText(record.productType)
    }
  },
  {
    title: '面积类型',
    dataIndex: 'areaTypeName',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '建筑面积',
    dataIndex: 'buildArea',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '套内面积',
    dataIndex: 'innerArea',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '主数据面积类型',
    dataIndex: 'mdmAreaTypeName',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '主数据建筑面积',
    dataIndex: 'mdmBuildingArea',
    width: 120,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '主数据套内面积',
    dataIndex: 'mdmInsideArea',
    width: 120,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '建筑面积差',
    slotName: 'buildingAreaDiff',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 100,
    fixed: 'right',
    align: 'center'
  }
]

// 分页
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showPageSize: true
})

// 表单
const formModel = reactive({
  projectId: '',
  location: '',
  roomName: '',
  dateRange: [] as any[]
})

// 搜索
const search = async () => {
  loading.value = true
  try {
    const params: LedgerAreaChangeDto = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      projectId: formModel.projectId,
      buildingName: formModel.location,
      roomName: formModel.roomName
    }

    const response = await getLedgerAreaChange(params)
    if (response.rows) {
      tableData.value = response.rows.map((item: AssetChangeRoomVo, index: number) => ({
        ...item,
        id: item.id || `${index}`
      }))
      pagination.total = response.total || 0
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    Message.error('搜索失败')
  }
}

// 重置
const reset = () => {
  formModel.projectId = ''
  formModel.location = ''
  formModel.roomName = ''
  formModel.dateRange = []
  search()
}

// 分页变化
const onPageChange = (current: number) => {
  pagination.current = current
  search()
}

// 每页条数变化
const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  search()
}

// 选择事件处理
const selectedRoomsData = ref<any>([])
const onSelect = (rowKeys: string[], selectedKey: string, record: any) => {
  selectedKeys.value = rowKeys
  if(rowKeys.includes(selectedKey)) {
    selectedRoomsData.value.push(record)
  } else {
    selectedRoomsData.value = selectedRoomsData.value.filter((item: any) => item.id !== record.id)
  }
}

// 全选事件处理
const onSelectAll = (checked: boolean) => {
  if (checked) {
    selectedKeys.value = tableData.value.map(item => item.id)
    selectedRoomsData.value = tableData.value
  } else {
    selectedKeys.value = []
    selectedRoomsData.value = []
  }
}

const selectProjectData = ref({
  projectId: '',
  projectName: ''
})
// 批量申请变更
const handleBatchProcess = () => {
  if (selectedKeys.value.length === 0) {
    Message.warning('请选择一条需要变更的资产')
    return
  }
  const projectIdList = selectedRoomsData.value.map((item: any) => item.projectId)
  if (new Set(projectIdList).size > 1) {
    Message.warning('请选择同一项目下的资产')
    return
  }
  const projectId = selectedRoomsData.value[0].projectId
  const projectName = selectedRoomsData.value[0].projectName
  selectProjectData.value = {
    projectId,
    projectName
  }
  const params = {
    projectInfo: {id: projectId},
    roomData: selectedRoomsData.value,
    roomIds: selectedKeys.value
  }
  nextClick(params)
}

// 房源名称点击
const handleRoomName = (record: TableItem) => {
  assetDetailRef.value?.open(record)
}

// 申请变更
const handleChange = (record: TableItem) => {
  selectProjectData.value = {
    projectId: record.projectId,
    projectName: record.projectName
  }
  const params = {
    projectInfo: {id: record.projectId},
    roomData: [record],
    roomIds: [record.id]
  }
  nextClick(params)
}

// 处理项目选择变化（用于ProjectSelector组件）
const handleProjectSelectorChange = (projectId: string, project: any) => {
  // 项目变化时重新查询数据
  search()
}

// 组件挂载时加载数据
// onMounted(() => {
//   search()
// })

// 申请变更
const changeFormVisible = ref(false)
const changeFormRef = ref<InstanceType<typeof changeForm>>()
const chooseRoomRef = ref<InstanceType<typeof chooseRoom>>()
const selectedRooms = ref<any>([])
const selectedRoomData = ref<any[]>([])
const handleFormSubmit = () => {
  changeFormVisible.value = false
  search()
}
const handleFormCancel = () => {
  changeFormVisible.value = false
}
const handleAddMoreAsset = () => {
  chooseRoomRef.value?.show(formModel.projectId)
}
const nextClick = (data: any) => {
  console.log('选择的房源数据:', data)

    if (data.roomIds) {
        // 如果changeForm已经打开，说明是在添加更多资产
        if (changeFormVisible.value) {
            // 合并新选择的房源到现有列表中，避免重复
            const existingRoomIds = selectedRooms.value
            const newRoomIds = data.roomIds.filter((id: string) => !existingRoomIds.includes(id))
            selectedRooms.value = [...existingRoomIds, ...newRoomIds]

            // 合并房源数据
            if (data.roomData) {
                const existingRoomData = selectedRoomData.value
                const newRoomData = data.roomData.filter((room: any) =>
                    !existingRoomData.some((existing: any) => existing.id === room.id)
                )
                selectedRoomData.value = [...existingRoomData, ...newRoomData]
            }
        } else {
            // 首次选择房源
            selectedRooms.value = data.roomIds
            if (data.roomData) {
                selectedRoomData.value = data.roomData
            }
        }
    }

    // 确保changeForm打开
    changeFormVisible.value = true
}
</script>

<style scoped>
.area-change-alert {
  min-height: 500px;
  /* 根据实际情况调整 */
}
</style>