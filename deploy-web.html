<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目部署控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1em;
        }
        
        .deployment-status-banner {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            display: none;
            text-align: left;
        }
        
        .deployment-status-banner.active {
            display: block;
            background: #d1ecf1;
            border-color: #bee5eb;
        }
        
        .deployment-status-banner.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .deployment-status-banner h4 {
            color: #856404;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .deployment-status-banner.active h4 {
            color: #0c5460;
        }
        
        .deployment-info {
            font-size: 0.9em;
            color: #666;
        }
        
        .deployment-info strong {
            color: #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin-top: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            animation: pulse 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulse {
            0% { opacity: 0.6; }
            100% { opacity: 1; }
        }
        
        .branch-selector {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .branch-selector h3 {
            color: #495057;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .branch-row {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }
        
        .branch-select {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1em;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .branch-select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .branch-select:disabled {
            background: #f8f9fa;
            cursor: not-allowed;
        }
        
        .refresh-btn {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .refresh-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(23, 162, 184, 0.3);
        }
        
        .refresh-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .current-branch {
            display: inline-block;
            background: #d4edda;
            color: #155724;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
            margin-left: 10px;
        }
        
        .deploy-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 20px 40px;
            font-size: 1.1em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            margin: 10px;
            min-width: 220px;
            font-weight: 600;
        }
        
        .deploy-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }
        
        .deploy-btn:active {
            transform: translateY(-1px);
        }
        
        .deploy-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            z-index: 1000;
        }
        
        .connection-status.connected {
            background: #d4edda;
            color: #155724;
        }
        
        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            text-align: left;
            max-height: 400px;
            overflow-y: auto;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .status.loading {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .git-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
        }
        
        .git-info h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .commit-item {
            background: white;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        
        .commit-hash {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>


    <div class="container">
        <h1 class="title">🚀 项目部署控制台</h1>
        <p class="subtitle">选择分支，一键构建并部署到服务器</p>
        
        <!-- 部署状态横幅 -->
        <div id="deploymentBanner" class="deployment-status-banner">
            <h4><span class="loading-spinner"></span>系统部署状态</h4>
            <div id="deploymentInfo" class="deployment-info"></div>
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
        </div>
        
        <!-- 分支选择器 -->
        <div class="branch-selector">
            <h3>🌿 分支选择</h3>
            <div class="branch-row">
                <select id="branchSelect" class="branch-select">
                    <option value="">正在加载分支...</option>
                </select>
                <button id="refreshBtn" onclick="loadBranches()" class="refresh-btn">🔄 刷新</button>
            </div>
            <div id="currentBranchInfo" style="margin-top: 10px; font-size: 0.9em; color: #666;"></div>
        </div>
        
        <button id="deployTestBtn" class="deploy-btn" onclick="startDeploy('test')">
            🧪 部署到测试环境
        </button>
        
        <button id="deployUatBtn" class="deploy-btn" onclick="startDeploy('uat')" style="background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);">
            🚀 部署到UAT环境
        </button>
        
        <!-- <button id="gitInfoBtn" class="deploy-btn" onclick="getGitInfo()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            📝 查看今日提交
        </button> -->
        
        <div id="envInfo" class="git-info" style="margin-top: 30px;">
            <h3>🌍 环境信息</h3>
            <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 200px; background: #e8f5e8; padding: 15px; border-radius: 8px;">
                    <h4 style="color: #2e7d32; margin-bottom: 10px;">🧪 测试环境</h4>
                    <p><strong>服务器:</strong> 172.30.1.254</p>
                    <p><strong>路径:</strong> /app/vanyang/h5</p>
                </div>
                <div style="flex: 1; min-width: 200px; background: #fff3e0; padding: 15px; border-radius: 8px;">
                    <h4 style="color: #f57c00; margin-bottom: 10px;">🚀 UAT环境</h4>
                    <p><strong>服务器:</strong> **************</p>
                    <p><strong>路径:</strong> /root/h5/admin</p>
                </div>
            </div>
        </div>
        
        <div id="status" class="status"></div>
        <div id="gitInfo" class="git-info" style="display: none;"></div>
    </div>

    <script>
        // 防止第三方脚本错误影响我们的应用
        window.addEventListener('error', function(e) {
            // 只处理我们自己的错误，忽略第三方脚本错误
            if (e.filename && e.filename.includes('ads.')) {
                e.preventDefault();
                return false;
            }
        });
        
        let isDeploying = false;
        let branches = [];
        let currentBranch = '';
        let deploymentState = {
            isDeploying: false,
            environment: null,
            branch: null,
            startTime: null,
            deployId: null,
            progress: ''
        };
        
        // 获取部署状态
        async function checkDeploymentStatus() {
            try {
                const response = await fetch('/deployment-status');
                if (response.ok) {
                    const newState = await response.json();
                    deploymentState = newState;
                    updateDeploymentBanner();
                    updateButtonStates();
                    
                    // 如果正在部署，继续检查状态
                    if (deploymentState.isDeploying) {
                        setTimeout(checkDeploymentStatus, 2000);
                    }
                }
            } catch (error) {
                console.warn('获取部署状态失败:', error);
            }
        }
        
        // 更新部署状态横幅
        function updateDeploymentBanner() {
            const banner = document.getElementById('deploymentBanner');
            const info = document.getElementById('deploymentInfo');
            const progressFill = document.getElementById('progressFill');
            
            if (deploymentState.isDeploying) {
                banner.className = 'deployment-status-banner active';
                banner.style.display = 'block';
                
                const duration = deploymentState.startTime ? 
                    Math.floor((Date.now() - new Date(deploymentState.startTime)) / 1000) : 0;
                
                const envText = deploymentState.environment === 'uat' ? 'UAT' : '测试';
                const branchText = deploymentState.branch ? ` (${deploymentState.branch})` : '';
                
                info.innerHTML = `
                    <div><strong>环境:</strong> ${envText}环境${branchText}</div>
                    <div><strong>进度:</strong> ${deploymentState.progress || '准备中...'}</div>
                    <div><strong>用时:</strong> ${duration}秒</div>
                    <div><strong>部署ID:</strong> ${deploymentState.deployId}</div>
                `;
                
                // 模拟进度条
                const progress = Math.min(90, duration * 2); // 最多90%，避免100%
                progressFill.style.width = `${progress}%`;
            } else {
                banner.style.display = 'none';
                progressFill.style.width = '0%';
            }
        }
        
        // 更新按钮状态
        function updateButtonStates() {
            const testBtn = document.getElementById('deployTestBtn');
            const uatBtn = document.getElementById('deployUatBtn');
            const branchSelect = document.getElementById('branchSelect');
            const refreshBtn = document.getElementById('refreshBtn');
            const gitInfoBtn = document.getElementById('gitInfoBtn');
            
            const disabled = deploymentState.isDeploying;
            
            if (testBtn) testBtn.disabled = disabled;
            if (uatBtn) uatBtn.disabled = disabled;
            if (branchSelect) branchSelect.disabled = disabled;
            if (refreshBtn) refreshBtn.disabled = disabled;
            if (gitInfoBtn) gitInfoBtn.disabled = disabled;
            
            if (!disabled) {
                if (testBtn) testBtn.innerHTML = '🧪 部署到测试环境';
                if (uatBtn) uatBtn.innerHTML = '🚀 部署到UAT环境';
            }
        }
        
        // 加载分支列表
        async function loadBranches() {
            const branchSelect = document.getElementById('branchSelect');
            const currentBranchInfo = document.getElementById('currentBranchInfo');
            const refreshBtn = document.getElementById('refreshBtn');
            
            try {
                if (refreshBtn) refreshBtn.disabled = true;
                if (branchSelect) branchSelect.innerHTML = '<option value="">正在加载分支...</option>';
                
                const response = await fetch('/branches');
                const data = await response.json();
                
                branches = data.branches;
                currentBranch = data.currentBranch;
                
                // 更新下拉框
                if (branchSelect) {
                    branchSelect.innerHTML = '';
                    
                    // 添加"当前分支"选项（不切换分支）
                    const currentOption = document.createElement('option');
                    currentOption.value = '';
                    currentOption.textContent = `🎯 当前分支: ${currentBranch}`;
                    branchSelect.appendChild(currentOption);
                    
                    // 添加分隔线
                    const separator = document.createElement('option');
                    separator.disabled = true;
                    separator.textContent = '─────────────────';
                    branchSelect.appendChild(separator);
                    
                    // 添加其他分支
                    branches.forEach(branch => {
                        const option = document.createElement('option');
                        option.value = branch.name;
                        option.textContent = branch.isCurrent ? 
                            `${branch.name} (当前)` : 
                            branch.name;
                        branchSelect.appendChild(option);
                    });
                }
                
                // 更新当前分支信息
                if (currentBranchInfo) {
                    currentBranchInfo.innerHTML = `当前分支: <span class="current-branch">${currentBranch}</span>`;
                }
                
            } catch (error) {
                if (branchSelect) branchSelect.innerHTML = '<option value="">加载分支失败</option>';
                if (currentBranchInfo) currentBranchInfo.innerHTML = `<span style="color: #dc3545;">❌ 加载分支失败: ${error.message}</span>`;
            } finally {
                if (refreshBtn) refreshBtn.disabled = false;
            }
        }
        
        async function startDeploy(environment = 'test') {
            // 检查本地状态
            if (isDeploying) return;
            
            // 检查全局部署状态
            if (deploymentState.isDeploying) {
                const envText = deploymentState.environment === 'uat' ? 'UAT' : '测试';
                const branchText = deploymentState.branch ? ` (分支: ${deploymentState.branch})` : '';
                const duration = Math.floor((Date.now() - new Date(deploymentState.startTime)) / 1000);
                
                alert(`⚠️ 系统正在部署中，请稍后再试！\n\n当前正在部署到: ${envText}环境${branchText}\n已用时: ${duration}秒\n进度: ${deploymentState.progress}`);
                return;
            }
            
            isDeploying = true;
            const testBtn = document.getElementById('deployTestBtn');
            const uatBtn = document.getElementById('deployUatBtn');
            const branchSelect = document.getElementById('branchSelect');
            const status = document.getElementById('status');
            
            // 禁用所有按钮
            updateButtonStates();
            
            const envText = environment === 'uat' ? 'UAT' : '测试';
            const currentBtn = environment === 'uat' ? uatBtn : testBtn;
            const selectedBranch = branchSelect.value;
            
            // 更新按钮文本
            if (selectedBranch) {
                currentBtn.innerHTML = `<span class="loading-spinner"></span>切换到 ${selectedBranch} 并部署到${envText}环境...`;
            } else {
                currentBtn.innerHTML = `<span class="loading-spinner"></span>${envText}部署中...`;
            }
            
            status.className = 'status loading';
            status.style.display = 'block';
            
            if (selectedBranch) {
                status.innerHTML = `🔄 正在切换到分支 ${selectedBranch} 并部署到${envText}环境...\n`;
            } else {
                status.innerHTML = `🔄 正在拉取最新代码并部署到${envText}环境...\n`;
            }
            
            try {
                const response = await fetch('/deploy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        environment,
                        branch: selectedBranch || undefined
                    })
                });
                
                if (response.status === 409) {
                    // 服务器端检测到并发部署
                    const errorData = await response.json();
                    const info = errorData.deploymentInfo;
                    alert(`⚠️ ${errorData.error}\n\n正在部署: ${info.environment === 'uat' ? 'UAT' : '测试'}环境\n分支: ${info.branch || '当前分支'}\n已用时: ${info.duration}\n部署ID: ${info.deployId}`);
                    return;
                }
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    status.innerHTML += chunk;
                    status.scrollTop = status.scrollHeight;
                }
                
                status.className = 'status success';
                status.innerHTML += `\n✅ ${envText}环境部署完成！`;
                
                // 部署成功后重新加载分支信息
                setTimeout(() => {
                    loadBranches();
                }, 1000);
                
            } catch (error) {
                status.className = 'status error';
                status.innerHTML += `\n❌ ${envText}环境部署失败: ${error.message}`;
            } finally {
                isDeploying = false;
                updateButtonStates();
                
                // 部署结束后再次检查状态
                setTimeout(checkDeploymentStatus, 1000);
            }
        }
        
        async function getGitInfo() {
            const gitInfo = document.getElementById('gitInfo');
            
            try {
                const response = await fetch('/git-info');
                const data = await response.json();
                
                if (data.totalCount === 0) {
                    gitInfo.innerHTML = `
                        <h3>📝 今日提交记录</h3>
                        <p>今天还没有提交记录</p>
                        <p><strong>当前分支:</strong> ${data.branch}</p>
                    `;
                } else {
                    let commitsHtml = `
                        <h3>📝 今日提交记录 (${data.totalCount} 条)</h3>
                        <p><strong>当前分支:</strong> ${data.branch}</p>
                    `;
                    
                    data.commits.forEach((commit, index) => {
                        commitsHtml += `
                            <div class="commit-item">
                                <strong>${index + 1}. [${commit.time}] ${commit.message}</strong><br>
                                <small>👤 ${commit.author} | 🔗 <span class="commit-hash">${commit.hash}</span></small>
                            </div>
                        `;
                    });
                    
                    gitInfo.innerHTML = commitsHtml;
                }
                
                gitInfo.style.display = 'block';
                
            } catch (error) {
                gitInfo.innerHTML = `
                    <h3>❌ 获取Git信息失败</h3>
                    <p>${error.message}</p>
                `;
                gitInfo.style.display = 'block';
            }
        }
        
        // 页面加载时自动获取Git信息和分支信息
        window.onload = function() {
            console.log('🚀 部署控制台已加载');
            
            // 加载页面数据
            loadBranches();
            getGitInfo();
            
            // 获取初始部署状态
            checkDeploymentStatus();
        };
        
        // 添加调试信息
        console.log('📱 部署控制台脚本已初始化');
        
        // 测试服务器连接
        async function testConnection() {
            try {
                const response = await fetch('/git-info');
                if (response.ok) {
                    console.log('✅ 服务器连接正常');
                } else {
                    console.warn('⚠️ 服务器响应异常:', response.status);
                }
            } catch (error) {
                console.error('❌ 无法连接到服务器:', error.message);
                // 显示连接错误提示
                const container = document.querySelector('.container');
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    background: #f8d7da;
                    border: 1px solid #f5c6cb;
                    color: #721c24;
                    padding: 15px;
                    border-radius: 10px;
                    margin-top: 20px;
                    text-align: center;
                `;
                errorDiv.innerHTML = `
                    <strong>⚠️ 服务器连接失败</strong><br>
                    请确保运行了 <code>node deploy-server.js</code><br>
                    错误信息: ${error.message}
                `;
                container.appendChild(errorDiv);
            }
        }
        
        // 页面加载后测试连接
        setTimeout(testConnection, 1000);
    </script>
</body>
</html> 