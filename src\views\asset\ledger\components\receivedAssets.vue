<template>
  <div class="received-assets">
    <!-- 搜索区域 -->
    <a-row>
      <a-col :flex="1">
        <a-form :model="formModel" auto-label-width label-align="right">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="projectId" label="项目">
                <ProjectSelector v-model="formModel.projectIdList" multiple @change="handleProjectSelectorChange" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="location" label="地块/楼栋">
                <a-input v-model="formModel.location" placeholder="请输入地块/楼栋" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="roomName" label="房源名称">
                <a-input v-model="formModel.roomName" placeholder="请输入房源名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="dateRange" label="接收日期">
                <a-range-picker v-model="formModel.dateRange" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <a-divider style="height: 84px" direction="vertical" />
      <a-col :flex="'86px'" style="text-align: right">
        <a-space direction="vertical" :size="18">
          <a-button v-permission="['asset:ledger:list']" type="primary" @click="search">
            <template #icon>
              <icon-search />
            </template>
            查询
          </a-button>
          <a-button @click="reset">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-divider style="margin-top: 16px" />
    <!-- 表格区域 -->
    <a-row style="margin-bottom: 12px">
      <a-col :span="24" style="text-align: right">
        <a-space>
          <a-button v-permission="['asset:disposal:add']" type="primary" @click="handleBatchProcess">
            批量申请处置
          </a-button>
          <a-button v-permission="['asset:ledger:export']" type="primary" @click="handleExport">
            导出资产清单
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-table row-key="id" :loading="loading" :pagination="pagination" :columns="columns" :data="tableData"
      :bordered="{ cell: true }"
      :row-selection="{ type: 'checkbox', showCheckedAll: true, selectedRowKeys: selectedKeys }" :scroll="{ x: 2000 }"
      @select="onSelect" @select-all="onSelectAll" @page-change="onPageChange" @page-size-change="onPageSizeChange">
      <template #index="{ rowIndex }">
        {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
      </template>
      <template #roomName="{ record }">
        <PermissionLink v-permission="['asset:project:room:detail']" @click="handleRoomName(record)">{{ record.roomName }}</PermissionLink>
      </template>
      <template #operations="{ record }">
        <a-button v-permission="['asset:disposal:add']" type="text" size="mini" @click="handleProcess(record)">
          申请处理
        </a-button>
      </template>
    </a-table>

    <add-form ref="addFormRef" v-model:visible="addFormVisible" :projectId="selectedProject.projectId"
      :projectName="selectedProject.projectName" :selected-rooms="selectedRooms" :room-data="roomData"
      :key="addFormVisible.toString() + Date.now()" @submit="handleAddFormSubmit" @cancel="handleAddFormCancel" />

    <asset-detail ref="assetDetailRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useRouter } from 'vue-router'
import ProjectSelector from '@/components/projectSelector/index.vue'
import { getLedgerList, exportLedgerList, type LedgerListDto, type LedgerVo } from '@/api/asset/projectLedger'
import { getProjectList } from '@/api/asset/projectReceive' // 使用资产接收的项目列表API
import { exportExcel } from '@/utils/exportUtil';
import AddForm from '@/views/asset/disposal/components/addForm.vue'
import { getRoomTree } from '@/api/asset/projectDisposal'
import AssetDetail from './assetDetail.vue'
import { useDict, getDictLabel } from '@/utils/dict'

const { asset_property_type, product_type } = useDict('asset_property_type', 'product_type')

// 产品类型转换
const getProductTypeText = (value: string | number) => {
    if (!value) return '-'
    const item = product_type.value.find((item: any) => item.value === String(value))
    return item ? item.label : value
}

const router = useRouter()

interface ProjectOption {
  id: string
  name: string
}

interface TableItem extends LedgerVo {
  id: string
}

// 表格数据
const loading = ref(false)
const selectedKeys = ref<string[]>([])
const tableData = ref<TableItem[]>([])
const projectOptions = ref<ProjectOption[]>([])
const assetDetailRef = ref<InstanceType<typeof AssetDetail>>()

// 表格列定义
const columns = [
  {
    title: '序号',
    slotName: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '房源名称',
    slotName: 'roomName',
    width: 120,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '所属项目',
    dataIndex: 'projectName',
    width: 120,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '所属地块',
    dataIndex: 'parcelName',
    width: 100,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '楼栋',
    dataIndex: 'buildingName',
    width: 100,
    align: 'center'
  },
  {
    title: '产品类型',
    dataIndex: 'productType',
    width: 100,
    align: 'center',
    render: ({ record }: { record: any }) => {
      return getProductTypeText(record.productType)
    }
  },
  {
    title: '面积类型',
    dataIndex: 'areaTypeName',
    width: 100,
    align: 'center'
  },
  {
    title: '建筑面积',
    dataIndex: 'buildArea',
    width: 100,
    align: 'center'
  },
  {
    title: '套内面积',
    dataIndex: 'innerArea',
    width: 100,
    align: 'center'
  },
  {
    title: '自持物业类型',
    dataIndex: 'propertyType',
    width: 120,
    align: 'center',
    render: ({ record }: { record: any }) => {
      return getDictLabel(asset_property_type.value, record.propertyType.toString())
    }
  },
  {
    title: '接收日期',
    dataIndex: 'receiveDate',
    width: 120,
    align: 'center'
  },
  {
    title: '最近变更日期',
    dataIndex: 'lastChangeDate',
    width: 120,
    align: 'center'
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 100,
    fixed: 'right',
    align: 'center'
  }
]

// 分页
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showPageSize: true
})

// 表单
const formModel = reactive({
  projectIdList: [],
  location: '',
  roomName: '',
  dateRange: [] as any[]
})

const projectData = ref<any[]>([])
// 处理项目选择变化（用于ProjectSelector组件）
const handleProjectSelectorChange = (projectIds: string[], projects: any[]) => {
  projectData.value = projects
  // 项目变化时重新查询数据
  search()
}

// 搜索
const search = async () => {
  loading.value = true
  try {
    const params: LedgerListDto = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      projectIdList: formModel.projectIdList,
      buildingName: formModel.location,
      roomName: formModel.roomName,
      receiveStartDate: formModel.dateRange ? formModel.dateRange[0] : '',
      receiveEndDate: formModel.dateRange ? formModel.dateRange[1] : ''
    }

    const response = await getLedgerList(params)
    if (response.rows) {
      tableData.value = response.rows.map((item: LedgerVo, index: number) => ({
        ...item,
        id: item.roomId || `${index}`
      }))
      pagination.total = response.total || 0
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    Message.error('搜索失败')
  }
}

// 重置
const reset = () => {
  formModel.projectIdList = []
  formModel.location = ''
  formModel.roomName = ''
  formModel.dateRange = []
  search()
}

// 分页变化
const onPageChange = (current: number) => {
  pagination.current = current
  search()
}

// 每页条数变化
const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  search()
}

// 选择事件
const selectedRows = ref<any[]>([])
const onSelect = (selectedRowKeys: string[], selectedKey: string, record: any) => {
  selectedKeys.value = selectedRowKeys
  selectedRoomIds.value = selectedRowKeys
  if (selectedRowKeys.includes(selectedKey)) {
    buildingIds.value.push(record.buildingId)
    selectedRows.value.push(record)
  } else {
    buildingIds.value = buildingIds.value.filter(item => item !== record.buildingId)
    selectedRows.value = selectedRows.value.filter(item => item.id !== record.id)
  }
}

// 全选事件
const onSelectAll = (checked: boolean) => {
  selectedKeys.value = checked ? tableData.value.map(item => item.id) : []
  buildingIds.value = checked ? tableData.value.map(item => item.buildingId || '') : []
  selectedRoomIds.value = checked ? tableData.value.map(item => item.id) : []
  selectedRows.value = checked ? tableData.value : []
}

// 批量处理
const selectedProject = ref<any>({
  projectId: '',
  projectName: ''
})
const handleBatchProcess = async () => {
  if (selectedKeys.value.length === 0) {
    Message.warning('请选择需要处置的资产')
    return
  }
  const projectIdList = selectedRows.value.map(item => item.projectId)
  if (new Set(projectIdList).size > 1) {
    Message.warning('请选择同一项目下的资产')
    return
  }
  const projectId = selectedRows.value[0].projectId
  const projectName = selectedRows.value[0].projectName
  selectedProject.value = {
    projectId: projectId,
    projectName: projectName
  }
  selectedRooms.value = {}
  await loadRoomTree()
  addFormVisible.value = true
}

// 导出
const handleExport = async () => {
  try {
    const params: LedgerListDto = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      projectIdList: formModel.projectIdList,
      buildingName: formModel.location,
      roomName: formModel.roomName,
      receiveStartDate: formModel.dateRange ? formModel.dateRange[0] : '',
      receiveEndDate: formModel.dateRange ? formModel.dateRange[1] : ''
    }
    exportExcel(exportLedgerList, params, '已接收资产')
    Message.success('导出成功')
  } catch (error) {
    Message.error('导出失败')
  }
}

// 处理单条记录
const handleProcess = async (record: TableItem) => {
  selectedRoomIds.value = [record.id]
  buildingIds.value = [record.buildingId || '']
  await loadRoomTree()
  selectedProject.value = {
    projectId: record.projectId,
    projectName: record.projectName
  }
  addFormVisible.value = true
}

// 点击跳转房源
const handleRoomName = (record: TableItem) => {
  assetDetailRef.value?.open(record)
}

// 申请处置
const addFormVisible = ref(false)
const buildingIds = ref<string[]>([])
const selectedRooms = ref<any>({})
const selectedRoomIds = ref<string[]>([])
const roomData = ref<any[]>([])
const addFormRef = ref<InstanceType<typeof AddForm>>()

const handleAddFormSubmit = () => {
  selectedRows.value = []
  selectedKeys.value = []
  buildingIds.value = []
  selectedRoomIds.value = []
  addFormVisible.value = false
  search()
}

// 获取房间树形数据
const loadRoomTree = async () => {

  console.log('aaaa', selectedRoomIds.value);
  const params = {
    buildingIds: buildingIds.value,
    roomName: '',
    productType: '',
    isSelf: '',
    type: 2
  }
  const res = await getRoomTree(params)
  roomData.value = res.data
  // 构建selectedRooms，key为buildingId_floorId_roomId，value为true, roomData格式为[{buildingName: '', id: '', floors: [{floorName: '', id: '', rooms: [{roomName: '', id: ''}]}]}]
  roomData.value.forEach((item: any) => {
    item.floors.forEach((floor: any) => {
      floor.rooms.forEach((room: any) => {
        if (selectedRoomIds.value.includes(room.id)) {
          selectedRooms.value[`${item.id}_${floor.id}_${room.id}`] = true
        }
      })
    })
  })
}

const handleAddFormCancel = () => {
  addFormVisible.value = false
  selectedRows.value = []
  selectedKeys.value = []
  buildingIds.value = []
  selectedRoomIds.value = []
}
</script>