import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken } from '@/utils/auth';
import { isHttp, isPathMatch } from '@/utils/validate';
import useUserStore from '@/store/modules/user';
import usePermissionStore from '@/store/modules/permission/index';
import { setRouteEmitter } from '@/utils/route-listener';
import router from './index';
import { whiteList as approvalWhiteList } from './approvalRoutes';

NProgress.configure({ showSpinner: false });

const whiteList = ['/login'];

const isWhiteList = (path: string) => {
    return [...whiteList].some((pattern) => isPathMatch(pattern, path));
};

const isApprovalWhiteList = (path: string) => {
    return approvalWhiteList.some((pattern) => isPathMatch(pattern, path));
};

router.beforeEach((to, from, next) => {
    setRouteEmitter(to);
    NProgress.start();
    if (isApprovalWhiteList(to.path)) {
        // 审批流程详情白名单
        next();
    } else if (getToken()) {
        /* has token*/
        if (to.path === '/login') {
            next({ path: '/' });
            NProgress.done();
        } else if (isWhiteList(to.path)) {
            next();
        } else {
            const userStore = useUserStore();
            if (userStore.roles.length === 0) {
                // 判断当前用户是否已拉取完user_info信息
                userStore
                    .getInfo()
                    .then(() => {
                        usePermissionStore()
                            .generateRoutes()
                            .then((accessRoutes) => {
                                // 根据roles权限生成可访问的路由表
                                accessRoutes.forEach((route: any) => {
                                    if (!isHttp(route.path)) {
                                        router.addRoute(route); // 动态添加可访问路由表
                                    }
                                });
                                next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
                            });
                    })
                    .catch((err) => {
                        userStore.logout().then(() => {
                            next({ path: '/' });
                        });
                    });
            } else {
                next();
            }
        }
    } else if (isWhiteList(to.path)) {
        // 没有token
        // 在免登录白名单，直接进入
        next();
    } else {
        next('/login'); // 否则全部重定向到登录页
        NProgress.done();
    }
});

router.afterEach(() => {
    NProgress.done();
});
