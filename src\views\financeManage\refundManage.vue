<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form :model="formModel" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }"
                        label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="projectId" label="项目">
                                    <ProjectTreeSelect 
                                        v-model="formModel.projectId" 
                                        placeholder="请选择项目" 
                                        allow-clear 
                                        @change="handleProjectChange"
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="refundType" label="退款类型">
                                    <a-select v-model="formModel.refundType" placeholder="请选择退款类型" mode="multiple" allow-clear>
                                        <a-option value="0">退租退款</a-option>
                                        <a-option value="1">退定退款</a-option>
                                        <a-option value="2">未明流水退款</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="refundFeeType" label="退款费用类型">
                                    <a-select v-model="formModel.refundFeeType" placeholder="请选择退款费用类型" mode="multiple" allow-clear>
                                        <a-option value="保证金">保证金</a-option>
                                        <a-option value="租金">租金</a-option>
                                        <a-option value="定金">定金</a-option>
                                        <a-option value="未明流水">未明流水</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="payeeName" label="收款方信息">
                                    <a-input v-model="formModel.payeeName" placeholder="请输入收款方信息" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="refundTarget" label="退款对象">
                                    <a-input v-model="formModel.refundTarget" placeholder="请输入退款对象" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="refundStatus" label="退款状态">
                                    <a-select v-model="formModel.refundStatus" placeholder="请选择退款状态" allow-clear>
                                        <a-option value="0">草稿</a-option>
                                        <a-option value="1">待退款</a-option>
                                        <a-option value="2">已退款</a-option>
                                        <a-option value="3">作废</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <template v-if="advancedSearchVisible">
                                <a-col :span="8">
                                    <a-form-item field="refundMethod" label="退款方式">
                                        <a-select v-model="formModel.refundMethod" placeholder="请选择退款方式" allow-clear>
                                            <a-option value="0">原路退回</a-option>
                                            <a-option value="1">银行转账</a-option>
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="approvalStatus" label="审批状态">
                                        <a-select v-model="formModel.approvalStatus" placeholder="请选择审批状态" allow-clear>
                                            <a-option value="0">草稿</a-option>
                                            <a-option value="1">审批中</a-option>
                                            <a-option value="2">审批通过</a-option>
                                            <a-option value="3">审批驳回</a-option>
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="applyDateRange" label="申请时间">
                                        <a-range-picker v-model="formModel.applyDateRange" style="width: 100%"
                                            value-format="YYYY-MM-DD" />
                                    </a-form-item>
                                </a-col>
                            </template>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-row>
                <AdvancedSearch @toggle="handleToggle" />
            </a-row>
            <!-- 状态-表头操作区 -->
            <div class="table-header">
                <a-tabs v-model:activeKey="activeStatus" type="rounded" hide-content @change="handleChangeStatus">
                    <a-tab-pane v-for="item in statusOptions" :key="item.value" :title="item.label" />
                    <template #extra>
                        <a-space>
                            <a-button v-permission="['rent:refund:export']" type="primary" @click="handleExport">导出</a-button>
                        </a-space>
                    </template>
                </a-tabs>
            </div>

            <!-- 表格区域 -->
            <a-table row-key="id" :loading="loading" :pagination="pagination" :columns="columns" :data="tableData" :scroll="{ x: 1 }"
                :bordered="{ cell: true }" @page-change="onPageChange" @page-size-change="onPageSizeChange">
                <template #index="{ rowIndex }">
                    {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
                </template>
                <template #refundStatus="{ record }">
                    <a-tag :color="getRefundStatusColor(record.refundStatus)">
                        {{
                            record?.refundStatus === 0 ? '草稿' :
                            record?.refundStatus === 1 ? '待退款' :
                            record?.refundStatus === 2 ? '已退款' :
                            record?.refundStatus === 3 ? '作废' : ''
                        }}
                    </a-tag>
                </template>
                
                <template #refundType="{ record }">
                    <a-tag :color="getRefundTypeColor(record.refundType)">
                        {{
                            record?.refundType === 0 ? '退租退款' :
                            record?.refundType === 1 ? '退定退款' :
                            record?.refundType === 2 ? '未明流水退款' : ''
                        }}
                    </a-tag>
                </template>
                
                <template #refundFeeType="{ record }">
                    <a-tag color="blue">{{ record?.feeType || '' }}</a-tag>
                </template>
                
                <template #refundMethod="{ record }">
                    <a-tag :color="getRefundMethodColor(record.refundWay)">
                        {{
                            record?.refundWay === 0 ? '原路退回' :
                            record?.refundWay === 1 ? '银行转账' : ''
                        }}
                    </a-tag>
                </template>
                
                <template #approvalStatus="{ record }">
                    <a-tag :color="getApprovalStatusColor(record.approveStatus)">
                        {{
                            record?.approveStatus === 0 ? '草稿' :
                            record?.approveStatus === 1 ? '审批中' :
                            record?.approveStatus === 2 ? '审批通过' :
                            record?.approveStatus === 3 ? '审批驳回' : ''
                        }}
                    </a-tag>
                </template>
                
                <template #refundAmount="{ record }">
                    <span>{{ record?.refundAmount !== undefined ? formatAmount(record.refundAmount) : '' }}</span>
                </template>
                
                <template #operations="{ record }">
                    <a-space>
                        <!-- 查看退款详情 -->
                        <a-button v-permission="['rent:refund:detail']" v-if="record" 
                            type="text" size="mini" @click="handleViewDetail(record)">
                            查看
                        </a-button>
                        
                        <!-- 记账 - 对于待退款状态的退款 -->
                        <a-button v-permission="['rent:refund:record']" v-if="record && record.refundStatus === 1" 
                            type="text" size="mini" @click="handleRefundAccount(record)">
                            记账
                        </a-button>
                        
                        <!-- 查看记账记录 - 对于已退款状态的退款 -->
                        <a-button v-permission="['rent:refund:detail']" 
                            type="text" size="mini" @click="handleViewRefundAccount(record)">
                            查看记账记录
                        </a-button>
                        
                        <!-- 编辑退款 - 对于草稿状态的退款申请 -->
                        <a-button v-permission="['rent:refund:save']" v-if="record && record.refundStatus === 0" 
                            type="text" size="mini" @click="handleEdit(record)">
                            编辑
                        </a-button>
                        <!-- 删除 - 对于审批中或待退款状态的退款 -->
                        <a-button v-permission="['rent:refund:remove']" v-if="record && record.refundStatus === 0" 
                            type="text" size="mini" @click="handleDelete(record)">
                            删除
                        </a-button>
                        
                        <!-- 取消退款 - 对于审批中或待退款状态的退款 -->
                        <!-- <a-button v-if="record && (record.refundStatus === 1 || record.approvalStatus === 1)" 
                            type="text" size="mini" @click="handleCancel(record)">
                            取消
                        </a-button>
                         -->
                        <!-- 作废 - 对于草稿状态的退款申请 -->
                        <a-button v-permission="['rent:refund:cancel']" v-if="record && record.refundStatus === 1" 
                            type="text" size="mini" @click="handleVoid(record)">
                            作废
                        </a-button>

                        <!-- 查看审批流 -->
                        <!-- <a-button v-if="record  && (record.refundStatus === 1 || record.refundStatus === 2)" 
                            type="text" size="mini" @click="handleViewApproval(record)">
                            查看审批流
                        </a-button> -->
                        
                        <!-- 提交审批 - 对于草稿状态的退款申请 -->
                        <!-- <a-button v-if="record && record.refundStatus === 0" 
                            type="text" size="mini" @click="handleSubmitApproval(record)">
                            提交审批
                        </a-button> -->
                        
                        <!-- 确认退款 - 对于审批通过待退款状态 -->
                        <!-- <a-button v-if="record && record.refundStatus === 1 && record.approvalStatus === 2" 
                            type="text" size="mini" @click="handleConfirmRefund(record)">
                            确认退款
                        </a-button> -->

                        <!-- 退款申请单 -->
                        <!-- <a-button v-if="record" 
                            type="text" size="mini" @click="handleRefundApply(record)">
                            退款申请单
                        </a-button> -->
                    </a-space>
                </template>
            </a-table>
        </a-card>

        <!-- 流水详情抽屉 -->
        <a-drawer v-model:visible="flowDrawerVisible" title="查看流水" width="1200px" @cancel="handleFlowCancel">
            <flow-detail v-if="flowDrawerVisible" :data="currentFlowData" />
        </a-drawer>

        <!-- 取消记账确认弹窗 -->
        <a-modal v-model:visible="cancelAccountVisible" title="提示" width="380px" @ok="handleCancelAccountConfirm"
            @cancel="handleCancelAccountCancel">
            <p><icon-exclamation-circle-fill style="color: #F56C6C;font-size: 16px;" /> 是否确认执行当前取消记账操作？</p>
        </a-modal>

        <!-- 收款码弹窗 -->
        <a-modal v-model:visible="collectCodeVisible" title="定单收款码" :footer="false" @cancel="handleCollectCancel">
            <collect-code-modal :data="{
                intentRoom: currentRecord.intentRoom,
                customerName: currentRecord.customerName,
                amount: currentRecord.depositAmount,
                qrcodeUrl: currentRecord.qrcodeUrl || 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=GIF?w=500&h=500'
            }" />
        </a-modal>

        <!-- 记账抽屉 -->
        <a-drawer v-model:visible="accountDrawerVisible" title="记账" width="1200px" ok-text="保存 " cancel-text="取消"
            @cancel="handleAccountCancel">
            <account-modal v-if="accountDrawerVisible" :data="currentAccountData" @cancel="handleAccountCancel"
                @save="handleAccountSave" />
        </a-drawer>

        <!-- 记账记录抽屉 -->
        <a-drawer v-model:visible="recordDrawerVisible" title="记账记录" width="1200px" @cancel="handleRecordCancel">
            <account-record />
        </a-drawer>

        <!-- 退款申请单 -->
        <refund-apply-form ref="refundApplyFormRef" />
        <!-- 退款详情 -->
        <refund-detail ref="refundDetailRef" @refresh="fetchData" />
        <!-- 作废 -->
         <a-modal :visible="voidModalVisible" title="作废" @cancel="handleVoidCancel" @ok="handleVoidConfirm">
            <p><icon-exclamation-circle-fill style="color: #FF9900;font-size: 16px;" /> 是否将此退款单作废?</p>
            <a-form :model="voidForm" ref="voidFormRef">
                <a-form-item field="reason" label="作废原因" :rules="[{ required: true, message: '请输入作废原因' }]">
                    <a-textarea v-model="voidForm.reason" placeholder="请输入作废原因" :auto-size="{ minRows: 3, maxRows: 5 }" />
                </a-form-item>
            </a-form>
         </a-modal>

        <!-- 删除确认弹窗 -->
        <a-modal :visible="deleteModalVisible" title="删除确认" @cancel="handleDeleteCancel" @ok="handleDeleteConfirm">
            <p><icon-exclamation-circle-fill style="color: #F56C6C;font-size: 16px;" /> 确定要删除这条退款记录吗？</p>
            <p style="color: #86909c; margin-top: 8px;">删除后将无法恢复，请谨慎操作。</p>
        </a-modal>

        <!-- 退款记账抽屉 -->
        <RefundAccountModal 
            ref="refundAccountModalRef" 
            :data="currentRefundAccountData" 
            @cancel="handleRefundAccountCancel" 
            @save="handleRefundAccountSave" 
        />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import dayjs from 'dayjs'
import { Message } from '@arco-design/web-vue'
import FlowDetail from './components/flowDetail.vue'
import CollectCodeModal from '@/components/collectCodeModal/index.vue'
import AccountModal from './components/accountModal.vue'
import AdvancedSearch from '@/components/advancedSearch/index.vue'
import AccountRecord from './components/accountRecord.vue'
import RefundApplyForm from './components/refundApplyForm.vue'
import RefundDetail from './components/refundDetail.vue'
import RefundAccountModal from './components/refundAccountModal.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import useProjectStore from '@/store/modules/project'
// 导入退款管理API
import {
  getFinancialRefundList,
  getFinancialRefundDetail,
  deleteFinancialRefund,
  cancelFinancialRefund,
  type FinancialRefundQueryDTO,
  type FinancialRefundVo,
  type FinancialFlowCancelDTO
} from '@/api/refundManage'

// 作废
const voidModalVisible = ref(false)
const voidForm = reactive({
    reason: '',
})
const currentVoidRecord = ref<FinancialRefundVo | null>(null)

// 删除确认弹窗
const deleteModalVisible = ref(false)
const currentDeleteRecord = ref<FinancialRefundVo | null>(null)

// 视图类型 全部 非宿舍 宿舍类 多经 日租房
const activeType = ref('')
const typeOptions = [
    { label: '全部', value: '' },
    { label: '非宿舍', value: '1' },
    { label: '宿舍类', value: '2' },
    { label: '多经', value: '3' },
    { label: '日租房', value: '4' }
]

const activeStatus = ref<string>('')
const statusOptions = [
    { label: '全部', value: '' },
    { label: '草稿', value: '0' },
    { label: '待退款', value: '1' },
    { label: '已退款', value: '2' },
    { label: '作废', value: '3' },
]

// 支付方式选项
const paymentMethodOptions = ref([
    { label: '微信', value: 'wechat' },
    { label: '支付宝', value: 'alipay' },
    { label: '银行卡', value: 'bank' },
    { label: '现金', value: 'cash' },
])

// 收款商户选项
const merchantOptions = ref([
    { label: '商户A', value: 'merchant_a' },
    { label: '商户B', value: 'merchant_b' },
    { label: '商户C', value: 'merchant_c' },
])

// 退款申请单引用
const refundApplyFormRef = ref<any>(null)
// 退款详情引用
const refundDetailRef = ref<any>(null)
// 退款记账组件引用
const refundAccountModalRef = ref<any>(null)

// 当前退款记账数据
const currentRefundAccountData = reactive({
    id: '',
    viewMode: false
})

// 审批流抽屉
const approvalDrawerVisible = ref(false)

// 查看审批流
const handleViewApproval = (record: FinancialRefundVo) => {
    console.log('查看审批流:', record)
    approvalDrawerVisible.value = true
}

const handleProjectChange = (value: string) => {
    formModel.projectId = value
    pagination.current = 1
    // formModel.pageNum = 1
    fetchData()
}

// 退款申请单
const handleRefundApply = (record: any) => {
    console.log('退款申请单:', record)
    refundApplyFormRef.value.open(record)
}

// 作废
const handleVoid = (record: FinancialRefundVo) => {
    console.log('作废:', record)
    currentVoidRecord.value = record
    voidModalVisible.value = true
}

// 作废取消
const handleVoidCancel = () => {
    voidModalVisible.value = false
    voidForm.reason = ''
}

// 作废确认 
const handleVoidConfirm = async () => {
    try {
        if (!currentVoidRecord.value?.id) {
            Message.error('退款单ID不存在')
            return
        }

        if (!voidForm.reason.trim()) {
            Message.error('请输入作废原因')
            return
        }

        const cancelData: FinancialFlowCancelDTO = {
            refundId: currentVoidRecord.value.id,
            cancelReason: voidForm.reason
        }

        const response = await cancelFinancialRefund(cancelData)
        
        if (response.code === 200) {
            Message.success('作废成功')
            voidModalVisible.value = false
            voidForm.reason = ''
            currentVoidRecord.value = null
            fetchData() // 刷新数据
        } else {
            // Message.error(response.msg || '作废失败')
        }
    } catch (error) {
        console.error('作废失败:', error)
        // Message.error('作废失败')
    }
}

// 状态切换
const handleChangeStatus = (key: string) => {
    console.log('状态切换:', key)
    activeStatus.value = key
    // 同步更新表单中的退款状态筛选
    formModel.refundStatus = key
    // 重置分页
    pagination.current = 1
    // 根据页签重新获取数据
    columns.value = [...baseColumns.value, ...handleColumns.value]
    if (key === '0') {
        // 草稿状态 - 基础列
        columns.value = [...baseColumns.value, ...handleColumns.value]
    } else if (key === '1') {
        // 待退款状态 - 基础列
        columns.value = [...baseColumns.value, ...handleColumns.value]
    } else if (key === '2') {
        // 已退款状态 - 添加退款时间列
        columns.value = [...baseColumns.value,
            {
                title: '退款时间',
                dataIndex: 'refundTime',
                width: 120,
                align: 'center',
            },
        , ...handleColumns.value]
    } else if (key === '3') {
        // 作废状态 - 添加作废相关列
        columns.value = [...baseColumns.value,
            {
                title: '作废时间',
                dataIndex: 'cancelTime',
                width: 120,
                align: 'center',
            },
            {
                title: '作废人',
                dataIndex: 'cancelByName',
                width: 100,
                align: 'center',
            },
            {
                title: '作废原因',
                dataIndex: 'cancelReason',
                width: 150,
                ellipsis: true,
                tooltip: true,
                align: 'center',
            }
        ,...handleColumns.value]
    } else {
        // 全部状态 - 基础列
        columns.value = [...baseColumns.value, ...handleColumns.value]
        // 全部状态时清空表单中的退款状态筛选
        formModel.refundStatus = ''
    }
    fetchData()
}

// 类型切换
const handleChangeType = (key: string) => {
    console.log('类型切换:', key)
}



// 账单码弹窗控制
const collectCodeVisible = ref(false)
const currentRecord = reactive({
    intentRoom: '',
    customerName: '',
    depositAmount: 0,
    qrcodeUrl: '',
    billStatus: '1'
})

// 账单类型
const billTypeOptions = ref([
    { label: '定金', value: '1' },
    { label: '保证金', value: '2' },
])

// 默认日期范围（近3个月）
const defaultDateRange = [
    dayjs().subtract(3, 'month').startOf('day'),
    dayjs().endOf('day')
]

const projectStore = useProjectStore()

const formModel = reactive({
    projectId: projectStore.assetProjectId || '',
    unitName: '',
    roomName: '',
    contractNo: '',
    billStatus: [],
    confirmStatus: '',
    billType: '',
    dateRange: defaultDateRange,
    // 新增筛选条件
    flowStatus: '',
    paymentType: [],
    paymentMethod: [],
    objectType: [],
    objectName: '',
    payerName: '',
    payerPhone: '',
    orderId: '',
    payerAccount: '',
    merchantId: [],
    accountTimeRange: [],
    refundType: '',
    refundFeeType: '',
    payeeName: '',
    refundTarget: '',
    refundStatus: '',
    refundMethod: '',
    approvalStatus: '',
    applyDateRange: [],
})

const loading = ref(false)
const tableData = ref<any[]>([])

const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
})
// 项目
// 收款方信息
// 退款金额
// 退款类型
// 退款费用类型
// 退款方式
// 退款对象
// 退款单状态
// 审批状态
// 申请时间
// 申请人
// 退款时间
// 作废时间
// 作废人
// 作废原因

let baseColumns = ref<any[]>([
    {
        title: '序号',
        dataIndex: 'index',
        slotName: 'index',
        width: 80,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '项目',
        dataIndex: 'projectName',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center',
    },
    {
        title: '收款方信息',
        dataIndex: 'receiverName',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center',
    },
    {
        title: '退款金额',
        dataIndex: 'refundAmount',
        width: 120,
        slotName: 'refundAmount',
        align: 'center',
    },
    {
        title: '退款类型',
        dataIndex: 'refundType',
        width: 120,
        slotName: 'refundType',
        align: 'center',
    },
    {
        title: '退款费用类型',
        dataIndex: 'feeType',
        width: 150,
        slotName: 'refundFeeType',
        ellipsis: true,
        tooltip: true,
        align: 'center',
    },
    {
        title: '退款方式',
        dataIndex: 'refundWay',
        width: 100,
        slotName: 'refundMethod',
        align: 'center',
    },
    {
        title: '退款对象',
        dataIndex: 'refundTarget',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center',
    },
    {
        title: '退款单状态',
        dataIndex: 'refundStatus',
        width: 140,
        slotName: 'refundStatus',
        align: 'center',
    },
    {
        title: '审批状态',
        dataIndex: 'approveStatus',
        width: 100,
        slotName: 'approvalStatus',
        align: 'center',
        ellipsis: true, tooltip: true
    },
    {
        title: '申请时间',
        dataIndex: 'applyTime',
        width: 120,
        align: 'center',
        ellipsis: true, tooltip: true
    },
    {
        title: '申请人',
        dataIndex: 'createByName',
        width: 120,
        align: 'center',
        ellipsis: true, tooltip: true
    },
])
let columns = ref<any[]>([])
let handleColumns = ref<any[]>([
    {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 280,
    fixed: 'right',
    align: 'center'
}
])
columns.value = [...baseColumns.value, ...handleColumns.value]
// 高级搜索控制
const advancedSearchVisible = ref(false)

// 高级搜索
const handleToggle = (visible: boolean) => {
    advancedSearchVisible.value = visible
}

// 记账记录
const handleRecord = (record: any) => {
    console.log('记账记录:', record)
    recordDrawerVisible.value = true
}

// 导出
const handleExport = async () => {
    try {
        // 如果没有数据，先获取数据
        if (tableData.value.length === 0) {
            Message.warning('暂无数据可导出')
            return
        }

        // 获取所有标题
        const headers = [
            "项目", "收款方信息", "退款金额", "退款类型", "退款费用类型", 
            "退款方式", "退款对象", "退款单状态", "审批状态", "申请时间", "申请人"
        ];

        // 根据当前状态页签添加特定字段
        if (activeStatus.value === '2') {
            // 已退款状态 - 添加退款时间
            headers.push("退款时间");
        } else if (activeStatus.value === '3') {
            // 作废状态 - 添加作废相关字段
            headers.push("作废时间", "作废人", "作废原因");
        }

        // 创建HTML表格
        let htmlContent = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
        htmlContent += '<head><meta charset="UTF-8"></head><body>';
        htmlContent += '<table border="1">';

        // 添加表头（带背景色）
        htmlContent += '<tr style="background-color: #4C8BF5; color: white; font-weight: bold;">';
        headers.forEach(header => {
            htmlContent += `<th>${header}</th>`;
        });
        htmlContent += '</tr>';

        // 添加数据行
        tableData.value.forEach((row: any) => {
            // 处理退款状态显示
            const refundStatusText = 
                row.refundStatus === 0 ? '草稿' :
                row.refundStatus === 1 ? '待退款' :
                row.refundStatus === 2 ? '已退款' :
                row.refundStatus === 3 ? '作废' : '';

            // 处理退款类型显示
            const refundTypeText = 
                row.refundType === 0 ? '退租退款' :
                row.refundType === 1 ? '退定退款' :
                row.refundType === 2 ? '未明流水退款' : '';

            // 处理退款方式显示
            const refundMethodText = 
                row.refundWay === 0 ? '原路退回' :
                row.refundWay === 1 ? '银行转账' : '';

            // 处理审批状态显示
            const approvalStatusText = 
                row.approveStatus === 0 ? '草稿' :
                row.approveStatus === 1 ? '审批中' :
                row.approveStatus === 2 ? '审批通过' :
                row.approveStatus === 3 ? '审批驳回' : '';

            // 构建数据行
            htmlContent += '<tr>';

            // 添加基础字段
            htmlContent += `<td>${row.projectName || ''}</td>`;
            htmlContent += `<td>${row.receiverName || ''}</td>`;
            htmlContent += `<td>${row.refundAmount ? formatAmount(row.refundAmount) : ''}</td>`;
            htmlContent += `<td>${refundTypeText}</td>`;
            htmlContent += `<td>${row.feeType || ''}</td>`;
            htmlContent += `<td>${refundMethodText}</td>`;
            htmlContent += `<td>${row.refundTarget || ''}</td>`;
            htmlContent += `<td>${refundStatusText}</td>`;
            htmlContent += `<td>${approvalStatusText}</td>`;
            htmlContent += `<td>${row.applyTime || ''}</td>`;
            htmlContent += `<td>${row.createByName || ''}</td>`;

            // 根据状态页签添加特定字段
            if (activeStatus.value === '2') {
                // 已退款状态 - 添加退款时间
                htmlContent += `<td>${row.refundTime || ''}</td>`;
            } else if (activeStatus.value === '3') {
                // 作废状态 - 添加作废相关字段
                htmlContent += `<td>${row.cancelTime || ''}</td>`;
                htmlContent += `<td>${row.cancelByName || ''}</td>`;
                htmlContent += `<td>${row.cancelReason || ''}</td>`;
            }

            htmlContent += '</tr>';
        });

        htmlContent += '</table></body></html>';

        // 创建Blob对象
        const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel' });
        const url = window.URL.createObjectURL(blob);

        // 创建下载链接
        const link = document.createElement('a');
        link.href = url;
        
        // 根据状态生成文件名
        const statusText = activeStatus.value ? 
            statusOptions.find(item => item.value === activeStatus.value)?.label || '全部' : '全部';
        link.download = `退款管理_${statusText}_${new Date().getTime()}.xls`;
        
        document.body.appendChild(link);
        link.click();

        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
    } catch (error) {
        console.error('导出失败:', error)
        Message.error('导出失败')
    }
}

// 金额格式化 - 参考出场管理模块
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return '0.00'
    // 保留2位小数并转为数字，避免浮点数精度问题
    const roundedAmount = Math.round((amount || 0) * 100) / 100
    return roundedAmount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 获取退款状态颜色
const getRefundStatusColor = (status: number | string) => {
    const colorMap: Record<string, string> = {
        '0': 'gray',      // 草稿
        '1': 'orange',    // 待退款
        '2': 'green',     // 已退款
        '3': 'red'        // 作废
    }
    return colorMap[status?.toString()] || 'default'
}

// 获取退款类型颜色
const getRefundTypeColor = (type: number | string) => {
    const colorMap: Record<string, string> = {
        '0': 'purple',    // 退租退款
        '1': 'blue',      // 退定退款
        '2': 'cyan'       // 未明流水退款
    }
    return colorMap[type?.toString()] || 'default'
}

// 获取退款方式颜色
const getRefundMethodColor = (method: number | string) => {
    const colorMap: Record<string, string> = {
        '0': 'green',     // 原路退回
        '1': 'orange'     // 银行转账
    }
    return colorMap[method?.toString()] || 'default'
}

// 获取审批状态颜色
const getApprovalStatusColor = (status: number | string) => {
    const colorMap: Record<string, string> = {
        '0': 'gray',      // 草稿
        '1': 'orange',    // 审批中
        '2': 'green',     // 审批通过
        '3': 'red'        // 审批驳回
    }
    return colorMap[status?.toString()] || 'default'
}

// 跳转合同详情
const handleContractDetail = (contractId: string | number) => {
    // TODO: 实现跳转逻辑
    console.log('跳转合同详情:', contractId)
}

// 查询
const search = () => {
    fetchData()
}

// 重置
const reset = () => {
    formModel.projectId = ''
    // 移除不需要的字段
    // formModel.unitName = ''
    // formModel.roomName = ''
    // formModel.contractNo = ''
    // formModel.billStatus = []
    // formModel.confirmStatus = ''
    // formModel.billType = ''
    formModel.dateRange = defaultDateRange
    
    // 重置退款管理页面特有的筛选条件
    formModel.refundType = ''
    formModel.refundFeeType = ''
    formModel.payeeName = ''
    formModel.refundTarget = ''
    formModel.refundStatus = ''
    formModel.refundMethod = ''
    formModel.approvalStatus = ''
    formModel.applyDateRange = []
    
    // 清除流水管理相关字段
    formModel.flowStatus = ''
    formModel.paymentType = []
    formModel.paymentMethod = []
    formModel.objectType = []
    formModel.objectName = ''
    formModel.payerName = ''
    formModel.payerPhone = ''
    formModel.orderId = ''
    formModel.payerAccount = ''
    formModel.merchantId = []
    formModel.accountTimeRange = []
    
    fetchData()
}

// 新增
const handleAdd = () => {
    // TODO: 实现新增逻辑
    console.log('新增')
}

// 分页
const onPageChange = (current: number) => {
    pagination.current = current
    fetchData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    fetchData()
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        console.log('formModel', formModel)
        // 构建查询参数
        const queryParams: FinancialRefundQueryDTO = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            projectId: formModel.projectId || undefined,
            refundType: formModel.refundType ? Number(formModel.refundType) : undefined,
            feeType: formModel.refundFeeType || undefined,
            receiverName: formModel.payeeName || undefined,
            refundTarget: formModel.refundTarget || undefined,
            refundWay: formModel.refundMethod ? Number(formModel.refundMethod) : undefined,
            approveStatus: formModel.approvalStatus ? Number(formModel.approvalStatus) : undefined,
            applyTimeStart: formModel.applyDateRange?.[0] || undefined,
            applyTimeEnd: formModel.applyDateRange?.[1] || undefined
        }

        // 优先使用状态页签的值，如果页签是"全部"则使用表单筛选的值
        if (activeStatus.value && activeStatus.value !== '') {
            queryParams.refundStatus = Number(activeStatus.value)
        } else if (formModel.refundStatus && formModel.refundStatus !== '') {
            queryParams.refundStatus = Number(formModel.refundStatus)
        }

        const response = await getFinancialRefundList(queryParams)
        
        if (response.code === 200) {
            tableData.value = response.rows || []
            pagination.total = response.total || 0
        } else {
            Message.error(response.msg || '获取数据失败')
            tableData.value = []
            pagination.total = 0
        }
    } catch (error) {
        console.error('获取退款列表失败:', error)
        Message.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 流水抽屉控制
const flowDrawerVisible = ref(false)
const currentFlowData = reactive({
    projectName: '',
    contractNo: '',
    tenantName: '',
    billType: '',
    billPeriod: '',
    dueDate: '',
    totalAmount: 0,
    discountAmount: 0,
    actualAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0,
    flowList: [],
    flowLogList: [] // 添加缺失的属性
})

// 查看使用记录
const handleViewUsage = (record: any) => {
    console.log('查看使用记录:', record)
    
    // 根据新的数据结构映射到FlowDetail组件期望的数据结构
    Object.assign(currentFlowData, {
        projectName: record.projectName || '',
        contractNo: record.orderId || '', // 使用订单号作为合同号
        tenantName: record.payerName || '', // 使用支付人姓名作为承租方
        billType: record.paymentMethod || '', // 使用支付方式作为账单类型
        billPeriod: '', // 无可用数据
        dueDate: record.accountTime || '', // 使用入账时间作为应收日期
        totalAmount: record.payAmount || 0,
        discountAmount: 0, // 默认无优惠
        actualAmount: record.payAmount || 0, // 支付金额作为实际应收
        paidAmount: record.usedAmount || 0, // 已使用金额作为已收
        unpaidAmount: record.remainAmount || 0, // 剩余金额作为未收
        flowList: [] // TODO: 调用接口获取流水列表
    })
    
    flowDrawerVisible.value = true
}

// 关闭流水抽屉
const handleFlowCancel = () => {
    flowDrawerVisible.value = false
}

const handleCollectCode = (record: any) => {
    // TODO: 实现账单码逻辑
    console.log('账单码:', record)
    collectCodeVisible.value = true
}

// 关闭账单码弹窗
const handleCollectCancel = () => {
    collectCodeVisible.value = false
}

// 记账抽屉控制
const accountDrawerVisible = ref(false)
const currentAccountData = reactive({
    projectName: '',
    orderId: '',
    paymentType: '',
    paymentMethod: '',
    objectType: '',
    objectName: '',
    accountTime: '',
    payAmount: 0,
    usedAmount: 0,
    remainAmount: 0,
    payerName: '',
    payerPhone: ''
})

// 点击记账按钮
const handleAccount = (record: any) => {
    Object.assign(currentAccountData, {
        projectName: record.projectName,
        orderId: record.orderId,
        paymentType: record.paymentType === 1 ? '线上' : '线下',
        paymentMethod: record.paymentMethod,
        objectType: record.objectType,
        objectName: record.objectName,
        accountTime: record.accountTime,
        payAmount: record.payAmount,
        usedAmount: record.usedAmount,
        remainAmount: record.remainAmount,
        payerName: record.payerName,
        payerPhone: record.payerPhone
    })
    accountDrawerVisible.value = true
}

// 关闭记账抽屉
const handleAccountCancel = () => {
    accountDrawerVisible.value = false
}

// 保存记账
const handleAccountSave = async () => {
    try {
        // TODO: 调用记账接口
        Message.success('记账成功')
        accountDrawerVisible.value = false
        // 刷新数据
        fetchData()
    } catch (error) {
        console.error('记账失败:', error)
    }
}

// 状态切换
const handleStatus = (item: any) => {
    activeStatus.value = item.field
}

// 取消记账弹窗控制
const cancelAccountVisible = ref(false)
const currentCancelRecord = reactive<any>({})

// 点击取消记账按钮
const handleCancelAccount = (record: any) => {
    currentCancelRecord.value = record
    cancelAccountVisible.value = true
}

// 确认取消记账
const handleCancelAccountConfirm = async () => {
    try {
        // TODO: 调用取消记账接口
        Message.success('取消记账成功')
        cancelAccountVisible.value = false
        // 刷新数据
        fetchData()
    } catch (error) {
        console.error('取消记账失败:', error)
    }
}

// 取消操作
const handleCancelAccountCancel = () => {
    cancelAccountVisible.value = false
}

const handleUrge = (record: any) => {
    // TODO: 实现催缴逻辑
    console.log('催缴:', record)
}

const handleDiscount = (record: any) => {
    // TODO: 实现减免缓逻辑
    console.log('减免缓:', record)
}

// 记账记录抽屉控制
const recordDrawerVisible = ref(false)

// 处理记账记录抽屉取消
const handleRecordCancel = () => {
    recordDrawerVisible.value = false
}

// 标记为其他收入
const handleMarkAsOtherIncome = (record: any) => {
    console.log('标记为其他收入:', record)
    // TODO: 实现标记为其他收入功能
    Message.success('已标记为其他收入')
    fetchData() // 刷新数据
}

// 取消其他收入标记
const handleCancelOtherIncome = (record: any) => {
    console.log('取消其他收入标记:', record)
    // TODO: 实现取消其他收入标记功能
    Message.success('已取消其他收入标记')
    fetchData() // 刷新数据
}

// 处理退款
const handleRefund = (record: any) => {
    console.log('处理退款:', record)
    // 打开退款申请表单
    refundApplyFormRef.value.open(record)
}

// 查看退款详情
const handleViewDetail = async (record: FinancialRefundVo) => {
    refundDetailRef.value?.open(record, 'view')
}

// 编辑退款申请
const handleEdit = (record: FinancialRefundVo) => {
    console.log('编辑退款申请:', record)
    // 直接通过 refundId 调用详情接口
    refundDetailRef.value?.openByRefundId(record.id!, 'edit')
}

// 取消退款申请
const handleCancel = (record: FinancialRefundVo) => {
    console.log('取消退款申请:', record)
    // TODO: 实现取消退款申请功能
    Message.success('已取消退款申请')
    fetchData() // 刷新数据
}

// 提交审批
const handleSubmitApproval = (record: FinancialRefundVo) => {
    console.log('提交审批:', record)
    // TODO: 实现提交审批功能
    Message.success('已提交审批')
    fetchData() // 刷新数据
}

// 确认退款
const handleConfirmRefund = (record: FinancialRefundVo) => {
    console.log('确认退款:', record)
    // TODO: 实现确认退款功能
    Message.success('已确认退款')
    fetchData() // 刷新数据
}

// 删除退款单
const handleDelete = async (record: FinancialRefundVo) => {
    // 显示删除确认弹窗
    currentDeleteRecord.value = record
    deleteModalVisible.value = true
}

// 取消删除
const handleDeleteCancel = () => {
    deleteModalVisible.value = false
    currentDeleteRecord.value = null
}

// 确认删除
const handleDeleteConfirm = async () => {
    try {
        if (!currentDeleteRecord.value?.id) {
            Message.error('退款单ID不存在')
            return
        }

        const response = await deleteFinancialRefund(currentDeleteRecord.value.id)
        
        if (response.code === 200) {
            Message.success('删除成功')
            deleteModalVisible.value = false
            currentDeleteRecord.value = null
            fetchData() // 刷新数据
        } else {
            Message.error(response.msg || '删除失败')
        }
    } catch (error) {
        console.error('删除失败:', error)
        Message.error('删除失败')
    }
}

// 退款记账
const handleRefundAccount = (record: FinancialRefundVo) => {
    console.log('退款记账:', record)
    Object.assign(currentRefundAccountData, {
        id: record.id,
        viewMode: false
    })
    refundAccountModalRef.value?.open()
}

// 查看退款记账记录
const handleViewRefundAccount = (record: FinancialRefundVo) => {
    console.log('查看退款记账记录:', record)
    Object.assign(currentRefundAccountData, {
        id: record.id,
        viewMode: true
    })
    refundAccountModalRef.value?.open()
}

// 退款记账取消
const handleRefundAccountCancel = () => {
    console.log('退款记账取消')
}

// 退款记账保存
const handleRefundAccountSave = (data: any) => {
    console.log('退款记账保存:', data)
    Message.success('退款记账保存成功')
    fetchData() // 刷新数据
}

// 监听表单中退款状态的变化，同步更新状态页签
watch(() => formModel.refundStatus, (newStatus) => {
    // 只有当新状态与当前页签状态不同时才更新
    if (newStatus !== activeStatus.value) {
        activeStatus.value = newStatus || ''
        // 重置分页
        pagination.current = 1
        // 不需要调用fetchData，因为会在状态页签变化时自动调用
    }
})

// onMounted(async () => {
//     // 如果store中没有当前项目ID，获取第一个项目的ID
//     if (!projectStore.assetProjectId && projectStore.assetProjectIdList.length === 0) {
//         // 这里我们依赖 ProjectTreeSelect 组件来处理默认项目的选择
//         formModel.projectId = ''
//     } else {
//         formModel.projectId = projectStore.assetProjectId || ''
//     }
//     fetchData()
// })
</script>

<style scoped lang="less">
.container {
    padding: 0 16px 0 16px;

    .table-header {
        margin-bottom: 16px;

        .operation-group {
            display: flex;
            gap: 8px;
        }
    }

    .general-card {
        box-sizing: border-box;
        padding-top: 16px;
        // padding: 16px 0 0 0;
    }

    .active {
        background-color: rgb(var(--arcoblue-6));
        color: #fff;
    }
}
</style>