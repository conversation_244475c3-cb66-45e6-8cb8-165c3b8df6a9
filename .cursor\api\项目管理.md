---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁后台/项目

<a id="opIdsave"></a>

## POST save

POST /sync/project/save

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": 0,
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "isDel": true,
  "stageList": [
    {}
  ],
  "parcelList": [
    {
      "id": "string",
      "mdmParcelId": "string",
      "projectId": "string",
      "stageId": "string",
      "parcelName": "string",
      "mdmNatureName": "string",
      "landUsage": 0,
      "mdmAddress": "string",
      "address": "string",
      "totalSelfArea": "string",
      "isDel": true
    }
  ],
  "buildingList": [
    {
      "id": "string",
      "source": 0,
      "mdmBuildingId": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingName": "string",
      "certificateBuildingName": "string",
      "upFloorNums": 0,
      "underFloorNums": 0,
      "totalSelfArea": "string",
      "isDel": true
    }
  ],
  "floorList": [
    {
      "id": "string",
      "mdmFloorId": "string",
      "projectId": "string",
      "buildingId": "string",
      "floorName": "string",
      "floorTypeName": "string",
      "designFloorHeight": "string",
      "designLoad": "string",
      "isDel": true
    }
  ],
  "roomList": [
    {
      "id": "string",
      "mdmRoomId": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingId": "string",
      "floorId": "string",
      "roomName": "string",
      "productType": "string",
      "areaType": 0,
      "areaTypeName": "string",
      "buildArea": 0,
      "innerArea": 0,
      "isSale": true,
      "isCompanySelf": true,
      "propertyType": 0,
      "propertyTypeName": "string",
      "selfHoldingTime": "2019-08-24T14:15:22Z",
      "status": 0,
      "receiveId": "string",
      "changeId": "string",
      "disposalId": "string",
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SynProjectDTO](#schemasynprojectdto)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

<a id="opIdsave_1"></a>

## POST save_1

POST /sync/org/save

> Body 请求参数

```json
[
  {
    "id": "string",
    "code": "string",
    "fullCode": "string",
    "fullName": "string",
    "name": "string",
    "orgType": 0,
    "level": 0,
    "parentId": "string",
    "thirdId": "string",
    "thirdName": "string",
    "sort": 0,
    "status": 0,
    "remark": "string"
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SyncOrgDTO](#schemasyncorgdto)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

<a id="opIdsaveHouseType"></a>

## POST 新增编辑宿舍户型

POST /project/houseType/save

新增或编辑宿舍户型信息，包含户型基本信息、户型特色和标准配套

> Body 请求参数

```json
{
  "id": "uuid",
  "projectId": "uuid",
  "houseTypeName": "标准单人间",
  "roomNum": 1,
  "hallNum": 1,
  "toiletNum": 1,
  "decorationType": 1,
  "livableNum": 2,
  "houseTypeDesc": "标准单人间，配套齐全",
  "specialFeatures": "string",
  "specialFeaturesList": [
    "独卫",
    "带阳台",
    "可做饭"
  ],
  "houseTypeImg": "url",
  "assetList": [
    {
      "id": "string",
      "houseTypeId": "string",
      "fixedAssetId": "string",
      "fixedAssetName": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ProjectHouseSaveDTO](#schemaprojecthousesavedto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|操作结果|boolean|

<a id="opIdedit_11"></a>

## POST 编辑项目

POST /project/edit

编辑项目信息，包括关联商业公司和所属片区

> Body 请求参数

```json
{
  "id": "string",
  "merchantIds": [
    "string"
  ],
  "parentId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysProjectEditDTO](#schemasysprojecteditdto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_11"></a>

## GET 查询项目列表

GET /project/list

根据条件查询项目列表，支持分页

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|parentId|query|string| 否 | 集团/区域/片区ID|集团/区域/片区ID|
|projectName|query|string| 否 | 项目名称|项目名称|
|projectIdList|query|array[string]| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","parentId":"string","parentName":"string","type":0,"mdmProjectId":"string","mdmName":"string","mdmSaleName":"string","code":"string","name":"string","provinceCode":"string","provinceName":"string","cityCode":"string","cityName":"string","countryCode":"string","countryName":"string","mdmTypeName":"string","assetType":0,"propertyUnit":"string","projectAddress":"string","longitude":"string","latitude":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":true,"parcelList":[{"createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","id":"string","mdmParcelId":"string","projectId":"string","stageId":"string","parcelName":"string","mdmNatureName":"string","landUsage":0,"mdmAddress":"string","address":"string","totalSelfArea":"string","isDel":true}],"receivedArea":0,"totalRoomArea":0,"roomCount":0,"pendingDeliveryCount":0,"merchantList":[{"id":"string","orgCompanyName":"string"}],"landUsageList":[0]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|项目列表数据|[SysProjectVo](#schemasysprojectvo)|

<a id="opIdupdateBuildingName"></a>

## POST 修改楼栋名称

POST /project/edit/buildingName

根据楼栋ID修改楼栋名称

> Body 请求参数

```json
{
  "id": "string",
  "buildingName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysBuildingUpdateNameDTO](#schemasysbuildingupdatenamedto)| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|操作结果|boolean|

<a id="opIdbuildingList"></a>

## POST 楼栋列表

POST /project/building/list

查询项目下的楼栋列表，包含资产面积、房源总面积、房源个数等统计信息

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "uuid",
  "buildingName": "1号楼"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysBuildingListQueryDTO](#schemasysbuildinglistquerydto)| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","source":0,"mdmBuildingId":"string","projectId":"string","parcelId":"string","parcelName":"string","buildingName":"string","mdmBuildingName":"string","certificateBuildingName":"string","upFloorNums":0,"underFloorNums":0,"totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":true,"assetArea":0,"roomArea":0,"roomCount":0}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|操作结果|[SysBuildingVo](#schemasysbuildingvo)|

<a id="opIdbindArea"></a>

## POST 批量关联片区

POST /project/bind/area

批量关联项目与片区的关系

> Body 请求参数

```json
{
  "projectIds": [
    "string"
  ],
  "areaId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[SysProjectBindAreaDTO](#schemasysprojectbindareadto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdstatistics"></a>

## GET 数据统计

GET /project/statistics/{id}

根据上级ID查询统计数据

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||项目id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"projectCount":0,"receivedArea":0,"totalRoomArea":0,"roomCount":0,"pendingDeliveryCount":0}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|项目详情数据|[SysProjectStatisticsVo](#schemasysprojectstatisticsvo)|

<a id="opIdhouseTypeList"></a>

## GET 宿舍户型列表

GET /project/houseType/list

查询项目下的所有宿舍户型列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||项目id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","houseTypeName":"string","roomNum":0,"hallNum":0,"toiletNum":0,"decorationType":0,"livableNum":0,"houseTypeDesc":"string","specialFeatures":"string","houseTypeImg":"string","createByName":"string","updateByName":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|宿舍户型列表|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ProjectHouseTypeVo](#schemaprojecthousetypevo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» projectId|string|false|none|项目id|项目id|
|» houseTypeName|string|false|none|户型名称|户型名称|
|» roomNum|integer(int32)|false|none|房间数|房间数|
|» hallNum|integer(int32)|false|none|厅数|厅数|
|» toiletNum|integer(int32)|false|none|卫生间数|卫生间数|
|» decorationType|integer(int32)|false|none|装修类型|装修类型|
|» livableNum|integer(int32)|false|none|可住人数|可住人数|
|» houseTypeDesc|string|false|none|户型描述|户型描述|
|» specialFeatures|string|false|none|户型特色|户型特色|
|» houseTypeImg|string|false|none|户型图片|户型图片|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIdhouseTypeDetail"></a>

## GET 查询宿舍户型详情

GET /project/houseType/detail

根据户型ID查询宿舍户型详情，包含基本信息、户型特色和标准配套

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||户型id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","houseTypeName":"string","roomNum":0,"hallNum":0,"toiletNum":0,"decorationType":0,"livableNum":0,"houseTypeDesc":"string","specialFeatures":"string","specialFeaturesList":["string"],"houseTypeImg":"string","assetList":[{"id":"string","houseTypeId":"string","fixedAssetId":"string","fixedAssetName":"string"}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|宿舍户型详情|[ProjectHouseTypeDetailVO](#schemaprojecthousetypedetailvo)|

<a id="opIddetail"></a>

## GET 查询项目详情

GET /project/detail/{id}

根据项目ID查询项目详情，包括项目、地块、楼栋的信息及统计数据

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||项目id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","parentId":"string","parentName":"string","type":0,"mdmProjectId":"string","mdmName":"string","mdmSaleName":"string","code":"string","name":"string","provinceCode":"string","provinceName":"string","cityCode":"string","cityName":"string","countryCode":"string","countryName":"string","mdmTypeName":"string","assetType":0,"propertyUnit":"string","projectAddress":"string","longitude":"string","latitude":"string","totalSelfArea":"string","createByName":"string","updateByName":"string","isDel":true,"parcelList":[{"createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","id":"string","mdmParcelId":"string","projectId":"string","stageId":"string","parcelName":"string","mdmNatureName":"string","landUsage":0,"mdmAddress":"string","address":"string","totalSelfArea":"string","isDel":true}],"receivedArea":0,"totalRoomArea":0,"roomCount":0,"pendingDeliveryCount":0,"merchantList":[{"id":"string","orgCompanyName":"string"}],"landUsageList":[0]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|项目详情数据|[SysProjectVo](#schemasysprojectvo)|

<a id="opIddeleteHouseType"></a>

## DELETE 删除宿舍户型

DELETE /project/houseType/delete/{id}

根据户型ID删除宿舍户型，若被房源引用则不能删除

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||户型id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|操作结果|boolean|

<a id="opIdroomTree"></a>

## POST 选择房源接口

POST /project/room/tree

根据传入的查询条件查询出符合条件的房间信息，按楼栋、楼层分组构建树形结构

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "string",
  "buildingIds": [
    "string"
  ],
  "productType": "string",
  "roomName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ProjectRoomTreeDto](#schemaprojectroomtreedto)| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","name":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","children":[{"id":"string","name":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","children":[{"id":"string","name":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","children":[null]}]}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|房源树形结构|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ProjectRoomTreeVo](#schemaprojectroomtreevo)]|false|none||[选择房源接口出参VO]|
|» id|string|false|none|节点ID|节点ID|
|» name|string|false|none|节点名称|节点名称|
|» buildingId|string|false|none|楼栋ID|楼栋ID|
|» buildingName|string|false|none|楼栋名称|楼栋名称|
|» floorId|string|false|none|楼层ID|楼层ID|
|» floorName|string|false|none|楼层名称|楼层名称|
|» children|[[ProjectRoomTreeVo](#schemaprojectroomtreevo)]|false|none|子节点列表|子节点列表|
|»» id|string|false|none|节点ID|节点ID|
|»» name|string|false|none|节点名称|节点名称|
|»» buildingId|string|false|none|楼栋ID|楼栋ID|
|»» buildingName|string|false|none|楼栋名称|楼栋名称|
|»» floorId|string|false|none|楼层ID|楼层ID|
|»» floorName|string|false|none|楼层名称|楼层名称|
|»» children|[[ProjectRoomTreeVo](#schemaprojectroomtreevo)]|false|none|子节点列表|子节点列表|

<a id="opIdtree_1"></a>

## GET 地块楼栋组织树接口

GET /project/tree

根据传入的查询条件查询出所有的楼栋数据，关联地块表，按照地块id分组，构建树形结构

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 否 ||项目id|
|parcelId|query|string| 否 ||地块id|
|buildingId|query|string| 否 ||楼栋id|
|buildingName|query|string| 否 ||楼栋名称|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","name":"string","children":[{"id":"string","name":"string","children":[{"id":"string","name":"string","children":[{}]}]}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|地块楼栋组织树|[ProjectTreeNodeVo](#schemaprojecttreenodevo)|

<a id="opIdparcelList"></a>

## GET 地块下拉列表接口

GET /project/parcel/list

查询项目下所有地块信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 是 ||项目ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","id":"string","mdmParcelId":"string","projectId":"string","stageId":"string","parcelName":"string","mdmNatureName":"string","landUsage":0,"mdmAddress":"string","address":"string","totalSelfArea":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|地块列表|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysParcel](#schemasysparcel)]|false|none||[地块信息]|
|» 地块信息|[SysParcel](#schemasysparcel)|false|none|地块信息|地块信息|
|»» createBy|string|false|none||none|
|»» createByName|string|false|none||none|
|»» createTime|string(date-time)|false|none||none|
|»» updateBy|string|false|none||none|
|»» updateByName|string|false|none||none|
|»» updateTime|string(date-time)|false|none||none|
|»» id|string|false|none||none|
|»» mdmParcelId|string|false|none||none|
|»» projectId|string|false|none||none|
|»» stageId|string|false|none||none|
|»» parcelName|string|false|none||none|
|»» mdmNatureName|string|false|none||none|
|»» landUsage|integer(int32)|false|none||none|
|»» mdmAddress|string|false|none||none|
|»» address|string|false|none||none|
|»» totalSelfArea|string|false|none||none|
|»» isDel|boolean|false|none||none|

<a id="opIdfloorList"></a>

## GET 楼层下拉列表接口

GET /project/floor/list

查询楼栋下所有楼层信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|buildingId|query|string| 是 ||楼栋ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","id":"string","mdmFloorId":"string","projectId":"string","buildingId":"string","floorName":"string","floorTypeName":"string","designFloorHeight":"string","designLoad":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|楼层列表|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysFloor](#schemasysfloor)]|false|none||none|
|» createBy|string|false|none||none|
|» createByName|string|false|none||none|
|» createTime|string(date-time)|false|none||none|
|» updateBy|string|false|none||none|
|» updateByName|string|false|none||none|
|» updateTime|string(date-time)|false|none||none|
|» id|string|false|none||none|
|» mdmFloorId|string|false|none||none|
|» projectId|string|false|none||none|
|» buildingId|string|false|none||none|
|» floorName|string|false|none||none|
|» floorTypeName|string|false|none||none|
|» designFloorHeight|string|false|none||none|
|» designLoad|string|false|none||none|
|» isDel|boolean|false|none||none|

<a id="opIdbuildingTree"></a>

## GET 地块楼栋组织树接口

GET /project/building/tree

根据传入的查询条件查询出所有的楼栋数据，关联地块表，按照地块id分组，构建树形结构

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 否 ||项目id|
|parcelId|query|string| 否 ||地块id|
|buildingId|query|string| 否 ||楼栋id|
|buildingName|query|string| 否 ||楼栋名称|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","name":"string","children":[{"id":"string","name":"string","children":[{"id":"string","name":"string","children":[{}]}]}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|地块楼栋组织树|[ProjectTreeNodeVo](#schemaprojecttreenodevo)|

<a id="opIdbuildingSelectList"></a>

## GET 楼栋下拉列表接口

GET /project/building/selectList

查询地块下所有楼栋信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|parcelId|query|string| 是 ||地块ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","id":"string","source":0,"mdmBuildingId":"string","projectId":"string","parcelId":"string","buildingName":"string","mdmBuildingName":"string","certificateBuildingName":"string","upFloorNums":0,"underFloorNums":0,"totalSelfArea":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|楼栋列表|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysBuilding](#schemasysbuilding)]|false|none||none|
|» createBy|string|false|none||none|
|» createByName|string|false|none||none|
|» createTime|string(date-time)|false|none||none|
|» updateBy|string|false|none||none|
|» updateByName|string|false|none||none|
|» updateTime|string(date-time)|false|none||none|
|» id|string|false|none||none|
|» source|integer(int32)|false|none||none|
|» mdmBuildingId|string|false|none||none|
|» projectId|string|false|none||none|
|» parcelId|string|false|none||none|
|» buildingName|string|false|none||none|
|» mdmBuildingName|string|false|none||none|
|» certificateBuildingName|string|false|none||none|
|» upFloorNums|integer(int32)|false|none||none|
|» underFloorNums|integer(int32)|false|none||none|
|» totalSelfArea|string|false|none||none|
|» isDel|boolean|false|none||none|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|boolean|false|none||none|

<h2 id="tocS_SynBuildingDTO">SynBuildingDTO</h2>

<a id="schemasynbuildingdto"></a>
<a id="schema_SynBuildingDTO"></a>
<a id="tocSsynbuildingdto"></a>
<a id="tocssynbuildingdto"></a>

```json
{
  "id": "string",
  "source": 0,
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "certificateBuildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|source|integer(int32)|false|none||none|
|mdmBuildingId|string|false|none||none|
|projectId|string|false|none||none|
|parcelId|string|false|none||none|
|buildingName|string|false|none||none|
|certificateBuildingName|string|false|none||none|
|upFloorNums|integer(int32)|false|none||none|
|underFloorNums|integer(int32)|false|none||none|
|totalSelfArea|string|false|none||none|
|isDel|boolean|false|none||none|

<h2 id="tocS_SynFloorDTO">SynFloorDTO</h2>

<a id="schemasynfloordto"></a>
<a id="schema_SynFloorDTO"></a>
<a id="tocSsynfloordto"></a>
<a id="tocssynfloordto"></a>

```json
{
  "id": "string",
  "mdmFloorId": "string",
  "projectId": "string",
  "buildingId": "string",
  "floorName": "string",
  "floorTypeName": "string",
  "designFloorHeight": "string",
  "designLoad": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|mdmFloorId|string|false|none||none|
|projectId|string|false|none||none|
|buildingId|string|false|none||none|
|floorName|string|false|none||none|
|floorTypeName|string|false|none||none|
|designFloorHeight|string|false|none||none|
|designLoad|string|false|none||none|
|isDel|boolean|false|none||none|

<h2 id="tocS_SynParcelDTO">SynParcelDTO</h2>

<a id="schemasynparceldto"></a>
<a id="schema_SynParcelDTO"></a>
<a id="tocSsynparceldto"></a>
<a id="tocssynparceldto"></a>

```json
{
  "id": "string",
  "mdmParcelId": "string",
  "projectId": "string",
  "stageId": "string",
  "parcelName": "string",
  "mdmNatureName": "string",
  "landUsage": 0,
  "mdmAddress": "string",
  "address": "string",
  "totalSelfArea": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|mdmParcelId|string|false|none||none|
|projectId|string|false|none||none|
|stageId|string|false|none||none|
|parcelName|string|false|none||none|
|mdmNatureName|string|false|none||none|
|landUsage|integer(int32)|false|none||none|
|mdmAddress|string|false|none||none|
|address|string|false|none||none|
|totalSelfArea|string|false|none||none|
|isDel|boolean|false|none||none|

<h2 id="tocS_SynProjectDTO">SynProjectDTO</h2>

<a id="schemasynprojectdto"></a>
<a id="schema_SynProjectDTO"></a>
<a id="tocSsynprojectdto"></a>
<a id="tocssynprojectdto"></a>

```json
{
  "id": "string",
  "type": 0,
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": 0,
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "isDel": true,
  "stageList": [
    {}
  ],
  "parcelList": [
    {
      "id": "string",
      "mdmParcelId": "string",
      "projectId": "string",
      "stageId": "string",
      "parcelName": "string",
      "mdmNatureName": "string",
      "landUsage": 0,
      "mdmAddress": "string",
      "address": "string",
      "totalSelfArea": "string",
      "isDel": true
    }
  ],
  "buildingList": [
    {
      "id": "string",
      "source": 0,
      "mdmBuildingId": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingName": "string",
      "certificateBuildingName": "string",
      "upFloorNums": 0,
      "underFloorNums": 0,
      "totalSelfArea": "string",
      "isDel": true
    }
  ],
  "floorList": [
    {
      "id": "string",
      "mdmFloorId": "string",
      "projectId": "string",
      "buildingId": "string",
      "floorName": "string",
      "floorTypeName": "string",
      "designFloorHeight": "string",
      "designLoad": "string",
      "isDel": true
    }
  ],
  "roomList": [
    {
      "id": "string",
      "mdmRoomId": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingId": "string",
      "floorId": "string",
      "roomName": "string",
      "productType": "string",
      "areaType": 0,
      "areaTypeName": "string",
      "buildArea": 0,
      "innerArea": 0,
      "isSale": true,
      "isCompanySelf": true,
      "propertyType": 0,
      "propertyTypeName": "string",
      "selfHoldingTime": "2019-08-24T14:15:22Z",
      "status": 0,
      "receiveId": "string",
      "changeId": "string",
      "disposalId": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|type|integer(int32)|false|none||none|
|mdmProjectId|string|false|none||none|
|mdmName|string|false|none||none|
|mdmSaleName|string|false|none||none|
|code|string|false|none||none|
|name|string|false|none||none|
|provinceCode|string|false|none||none|
|provinceName|string|false|none||none|
|cityCode|string|false|none||none|
|cityName|string|false|none||none|
|countryCode|string|false|none||none|
|countryName|string|false|none||none|
|mdmTypeName|string|false|none||none|
|assetType|integer(int32)|false|none||none|
|propertyUnit|string|false|none||none|
|projectAddress|string|false|none||none|
|longitude|string|false|none||none|
|latitude|string|false|none||none|
|totalSelfArea|string|false|none||none|
|isDel|boolean|false|none||none|
|stageList|[object]|false|none||none|
|parcelList|[[SynParcelDTO](#schemasynparceldto)]|false|none||none|
|buildingList|[[SynBuildingDTO](#schemasynbuildingdto)]|false|none||none|
|floorList|[[SynFloorDTO](#schemasynfloordto)]|false|none||none|
|roomList|[[SynRoomDTO](#schemasynroomdto)]|false|none||none|

<h2 id="tocS_SynRoomDTO">SynRoomDTO</h2>

<a id="schemasynroomdto"></a>
<a id="schema_SynRoomDTO"></a>
<a id="tocSsynroomdto"></a>
<a id="tocssynroomdto"></a>

```json
{
  "id": "string",
  "mdmRoomId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomName": "string",
  "productType": "string",
  "areaType": 0,
  "areaTypeName": "string",
  "buildArea": 0,
  "innerArea": 0,
  "isSale": true,
  "isCompanySelf": true,
  "propertyType": 0,
  "propertyTypeName": "string",
  "selfHoldingTime": "2019-08-24T14:15:22Z",
  "status": 0,
  "receiveId": "string",
  "changeId": "string",
  "disposalId": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|mdmRoomId|string|false|none||none|
|projectId|string|false|none||none|
|parcelId|string|false|none||none|
|buildingId|string|false|none||none|
|floorId|string|false|none||none|
|roomName|string|false|none||none|
|productType|string|false|none||none|
|areaType|integer(int32)|false|none||none|
|areaTypeName|string|false|none||none|
|buildArea|number|false|none||none|
|innerArea|number|false|none||none|
|isSale|boolean|false|none||none|
|isCompanySelf|boolean|false|none||none|
|propertyType|integer(int32)|false|none||none|
|propertyTypeName|string|false|none||none|
|selfHoldingTime|string(date-time)|false|none||none|
|status|integer(int32)|false|none||none|
|receiveId|string|false|none||none|
|changeId|string|false|none||none|
|disposalId|string|false|none||none|
|isDel|boolean|false|none||none|

<h2 id="tocS_SyncOrgDTO">SyncOrgDTO</h2>

<a id="schemasyncorgdto"></a>
<a id="schema_SyncOrgDTO"></a>
<a id="tocSsyncorgdto"></a>
<a id="tocssyncorgdto"></a>

```json
{
  "id": "string",
  "code": "string",
  "fullCode": "string",
  "fullName": "string",
  "name": "string",
  "orgType": 0,
  "level": 0,
  "parentId": "string",
  "thirdId": "string",
  "thirdName": "string",
  "sort": 0,
  "status": 0,
  "remark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|code|string|false|none||none|
|fullCode|string|false|none||none|
|fullName|string|false|none||none|
|name|string|false|none||none|
|orgType|integer(int32)|false|none||none|
|level|integer(int32)|false|none||none|
|parentId|string|false|none||none|
|thirdId|string|false|none||none|
|thirdName|string|false|none||none|
|sort|integer(int32)|false|none||none|
|status|integer(int32)|false|none||none|
|remark|string|false|none||none|

<h2 id="tocS_SynStageDTO">SynStageDTO</h2>

<a id="schemasynstagedto"></a>
<a id="schema_SynStageDTO"></a>
<a id="tocSsynstagedto"></a>
<a id="tocssynstagedto"></a>

```json
{
  "id": "string",
  "mdmStageId": "string",
  "projectId": "string",
  "stageName": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|mdmStageId|string|false|none||none|
|projectId|string|false|none||none|
|stageName|string|false|none||none|
|isDel|boolean|false|none||none|

<h2 id="tocS_ProjectHouseSaveDTO">ProjectHouseSaveDTO</h2>

<a id="schemaprojecthousesavedto"></a>
<a id="schema_ProjectHouseSaveDTO"></a>
<a id="tocSprojecthousesavedto"></a>
<a id="tocsprojecthousesavedto"></a>

```json
{
  "id": "uuid",
  "projectId": "uuid",
  "houseTypeName": "标准单人间",
  "roomNum": 1,
  "hallNum": 1,
  "toiletNum": 1,
  "decorationType": 1,
  "livableNum": 2,
  "houseTypeDesc": "标准单人间，配套齐全",
  "specialFeatures": "string",
  "specialFeaturesList": [
    "独卫",
    "带阳台",
    "可做饭"
  ],
  "houseTypeImg": "url",
  "assetList": [
    {
      "id": "string",
      "houseTypeId": "string",
      "fixedAssetId": "string",
      "fixedAssetName": "string"
    }
  ]
}

```

新增编辑宿舍户型DTO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||户型ID|
|projectId|string|true|none||项目ID|
|houseTypeName|string|true|none||户型名称|
|roomNum|integer(int32)|true|none||房间数|
|hallNum|integer(int32)|true|none||厅数|
|toiletNum|integer(int32)|true|none||卫生间数|
|decorationType|integer(int32)|true|none||装修类型|
|livableNum|integer(int32)|true|none||可住人数|
|houseTypeDesc|string|false|none||户型描述|
|specialFeatures|string|false|none|户型特色|户型特色|
|specialFeaturesList|[string]|false|none||户型特色列表|
|houseTypeImg|string|false|none||户型图片|
|assetList|[[HouseTypeAssetRelAddDTO](#schemahousetypeassetreladddto)]|false|none||标准配套列表|

<h2 id="tocS_HouseTypeAssetRelAddDTO">HouseTypeAssetRelAddDTO</h2>

<a id="schemahousetypeassetreladddto"></a>
<a id="schema_HouseTypeAssetRelAddDTO"></a>
<a id="tocShousetypeassetreladddto"></a>
<a id="tocshousetypeassetreladddto"></a>

```json
{
  "id": "string",
  "houseTypeId": "string",
  "fixedAssetId": "string",
  "fixedAssetName": "string"
}

```

标准配套列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|houseTypeId|string|false|none|户型id|户型id|
|fixedAssetId|string|false|none|固定资产id|固定资产id|
|fixedAssetName|string|false|none|固定资产名称|固定资产名称|

<h2 id="tocS_SysProjectVo">SysProjectVo</h2>

<a id="schemasysprojectvo"></a>
<a id="schema_SysProjectVo"></a>
<a id="tocSsysprojectvo"></a>
<a id="tocssysprojectvo"></a>

```json
{
  "id": "string",
  "parentId": "string",
  "parentName": "string",
  "type": 0,
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": 0,
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "parcelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmParcelId": "string",
      "projectId": "string",
      "stageId": "string",
      "parcelName": "string",
      "mdmNatureName": "string",
      "landUsage": 0,
      "mdmAddress": "string",
      "address": "string",
      "totalSelfArea": "string",
      "isDel": true
    }
  ],
  "receivedArea": 0,
  "totalRoomArea": 0,
  "roomCount": 0,
  "pendingDeliveryCount": 0,
  "merchantList": [
    {
      "id": "string",
      "orgCompanyName": "string"
    }
  ],
  "landUsageList": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|parentId|string|false|none|片区ID，sys_org表的id|片区ID，sys_org表的id|
|parentName|string|false|none|所属片区|所属片区|
|type|integer(int32)|false|none|项目类型（1内部 2外部）|项目类型（1内部 2外部）|
|mdmProjectId|string|false|none|主数据项目ID|主数据项目ID|
|mdmName|string|false|none|主数据项目名称|主数据项目名称|
|mdmSaleName|string|false|none|主数据销售推广名|主数据销售推广名|
|code|string|false|none|编码|编码|
|name|string|false|none|简称|简称|
|provinceCode|string|false|none|省编码|省编码|
|provinceName|string|false|none|省名称|省名称|
|cityCode|string|false|none|市编码|市编码|
|cityName|string|false|none|市名称|市名称|
|countryCode|string|false|none|区编码|区编码|
|countryName|string|false|none|区名称|区名称|
|mdmTypeName|string|false|none|主数据项目类型|主数据项目类型|
|assetType|integer(int32)|false|none|项目资产分类（1众创城 2科技城 3产城 4外部项目）|项目资产分类（1众创城 2科技城 3产城 4外部项目）|
|propertyUnit|string|false|none|产权单位|产权单位|
|projectAddress|string|false|none|项目地址|项目地址|
|longitude|string|false|none|地址经度|地址经度|
|latitude|string|false|none|地址维度|地址维度|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0 否 1是|0 否 1是|
|parcelList|[[SysParcel](#schemasysparcel)]|false|none|地块信息|地块信息|
|receivedArea|number|false|none||已接收资产面积(万平方米)|
|totalRoomArea|number|false|none||房源总面积(万平方米)|
|roomCount|integer(int32)|false|none||房源个数|
|pendingDeliveryCount|integer(int32)|false|none||待交付房源数量|
|merchantList|[[MerchantSimpleVo](#schemamerchantsimplevo)]|false|none||签约商业公司|
|landUsageList|[integer]|false|none||none|

<h2 id="tocS_MerchantSimpleVo">MerchantSimpleVo</h2>

<a id="schemamerchantsimplevo"></a>
<a id="schema_MerchantSimpleVo"></a>
<a id="tocSmerchantsimplevo"></a>
<a id="tocsmerchantsimplevo"></a>

```json
{
  "id": "string",
  "orgCompanyName": "string"
}

```

签约商业公司

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|orgCompanyName|string|false|none|商业公司名称，从一房一码同步|商业公司名称，从一房一码同步|

<h2 id="tocS_SysParcel">SysParcel</h2>

<a id="schemasysparcel"></a>
<a id="schema_SysParcel"></a>
<a id="tocSsysparcel"></a>
<a id="tocssysparcel"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmParcelId": "string",
  "projectId": "string",
  "stageId": "string",
  "parcelName": "string",
  "mdmNatureName": "string",
  "landUsage": 0,
  "mdmAddress": "string",
  "address": "string",
  "totalSelfArea": "string",
  "isDel": true
}

```

地块信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none||none|
|mdmParcelId|string|false|none||none|
|projectId|string|false|none||none|
|stageId|string|false|none||none|
|parcelName|string|false|none||none|
|mdmNatureName|string|false|none||none|
|landUsage|integer(int32)|false|none||none|
|mdmAddress|string|false|none||none|
|address|string|false|none||none|
|totalSelfArea|string|false|none||none|
|isDel|boolean|false|none||none|

<h2 id="tocS_SysBuildingVo">SysBuildingVo</h2>

<a id="schemasysbuildingvo"></a>
<a id="schema_SysBuildingVo"></a>
<a id="tocSsysbuildingvo"></a>
<a id="tocssysbuildingvo"></a>

```json
{
  "id": "string",
  "source": 0,
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingName": "string",
  "mdmBuildingName": "string",
  "certificateBuildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "assetArea": 0,
  "roomArea": 0,
  "roomCount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|source|integer(int32)|false|none|来源（1主数据 2手动添加）|来源（1主数据 2手动添加）|
|mdmBuildingId|string|false|none|主数据楼栋id|主数据楼栋id|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|所属地块id|所属地块id|
|parcelName|string|false|none|所属地块名称|所属地块名称|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|mdmBuildingName|string|false|none|主数据楼栋名称|主数据楼栋名称|
|certificateBuildingName|string|false|none|产证楼栋名称|产证楼栋名称|
|upFloorNums|integer(int32)|false|none|地上楼层数|地上楼层数|
|underFloorNums|integer(int32)|false|none|地下楼层数|地下楼层数|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0 否 1是|0 否 1是|
|assetArea|number|false|none||资产面积（万平方米）|
|roomArea|number|false|none||房源总面积（万平方米）|
|roomCount|integer(int32)|false|none||房间数|

<h2 id="tocS_SysProjectEditDTO">SysProjectEditDTO</h2>

<a id="schemasysprojecteditdto"></a>
<a id="schema_SysProjectEditDTO"></a>
<a id="tocSsysprojecteditdto"></a>
<a id="tocssysprojecteditdto"></a>

```json
{
  "id": "string",
  "merchantIds": [
    "string"
  ],
  "parentId": "string"
}

```

项目编辑对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||项目ID|
|merchantIds|[string]|false|none||签约商业公司ID数组|
|parentId|string|false|none||所属片区ID|

<h2 id="tocS_SysProjectBindAreaDTO">SysProjectBindAreaDTO</h2>

<a id="schemasysprojectbindareadto"></a>
<a id="schema_SysProjectBindAreaDTO"></a>
<a id="tocSsysprojectbindareadto"></a>
<a id="tocssysprojectbindareadto"></a>

```json
{
  "projectIds": [
    "string"
  ],
  "areaId": "string"
}

```

项目批量关联片区对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectIds|[string]|true|none||项目ID数组|
|areaId|string|true|none||片区ID|

<h2 id="tocS_SysBuildingUpdateNameDTO">SysBuildingUpdateNameDTO</h2>

<a id="schemasysbuildingupdatenamedto"></a>
<a id="schema_SysBuildingUpdateNameDTO"></a>
<a id="tocSsysbuildingupdatenamedto"></a>
<a id="tocssysbuildingupdatenamedto"></a>

```json
{
  "id": "string",
  "buildingName": "string"
}

```

楼栋名称更新DTO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none||楼栋ID|
|buildingName|string|true|none||楼栋名称|

<h2 id="tocS_SysBuildingListQueryDTO">SysBuildingListQueryDTO</h2>

<a id="schemasysbuildinglistquerydto"></a>
<a id="schema_SysBuildingListQueryDTO"></a>
<a id="tocSsysbuildinglistquerydto"></a>
<a id="tocssysbuildinglistquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "uuid",
  "buildingName": "1号楼"
}

```

楼栋列表查询DTO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|projectId|string|true|none||项目ID|
|buildingName|string|false|none||楼栋名称|

<h2 id="tocS_ProjectRoomTreeDto">ProjectRoomTreeDto</h2>

<a id="schemaprojectroomtreedto"></a>
<a id="schema_ProjectRoomTreeDto"></a>
<a id="tocSprojectroomtreedto"></a>
<a id="tocsprojectroomtreedto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "string",
  "buildingIds": [
    "string"
  ],
  "productType": "string",
  "roomName": "string"
}

```

选择房源接口入参DTO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|projectId|string|false|none|项目id|项目id|
|buildingIds|[string]|false|none|楼栋id列表|楼栋id列表|
|» 楼栋id列表|string|false|none|楼栋id列表|楼栋id列表|
|productType|string|false|none|产品类型|产品类型|
|roomName|string|false|none|房源名称|房源名称|

<h2 id="tocS_ProjectRoomTreeVo">ProjectRoomTreeVo</h2>

<a id="schemaprojectroomtreevo"></a>
<a id="schema_ProjectRoomTreeVo"></a>
<a id="tocSprojectroomtreevo"></a>
<a id="tocsprojectroomtreevo"></a>

```json
{
  "id": "string",
  "name": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "children": [
    {
      "id": "string",
      "name": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "children": [
        {
          "id": "string",
          "name": "string",
          "buildingId": "string",
          "buildingName": "string",
          "floorId": "string",
          "floorName": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

选择房源接口出参VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|节点ID|节点ID|
|name|string|false|none|节点名称|节点名称|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层ID|楼层ID|
|floorName|string|false|none|楼层名称|楼层名称|
|children|[[ProjectRoomTreeVo](#schemaprojectroomtreevo)]|false|none|子节点列表|子节点列表|

<h2 id="tocS_ProjectHouseTypeVo">ProjectHouseTypeVo</h2>

<a id="schemaprojecthousetypevo"></a>
<a id="schema_ProjectHouseTypeVo"></a>
<a id="tocSprojecthousetypevo"></a>
<a id="tocsprojecthousetypevo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "houseTypeName": "string",
  "roomNum": 0,
  "hallNum": 0,
  "toiletNum": 0,
  "decorationType": 0,
  "livableNum": 0,
  "houseTypeDesc": "string",
  "specialFeatures": "string",
  "houseTypeImg": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|houseTypeName|string|false|none|户型名称|户型名称|
|roomNum|integer(int32)|false|none|房间数|房间数|
|hallNum|integer(int32)|false|none|厅数|厅数|
|toiletNum|integer(int32)|false|none|卫生间数|卫生间数|
|decorationType|integer(int32)|false|none|装修类型|装修类型|
|livableNum|integer(int32)|false|none|可住人数|可住人数|
|houseTypeDesc|string|false|none|户型描述|户型描述|
|specialFeatures|string|false|none|户型特色|户型特色|
|houseTypeImg|string|false|none|户型图片|户型图片|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_HouseTypeAssetRelVo">HouseTypeAssetRelVo</h2>

<a id="schemahousetypeassetrelvo"></a>
<a id="schema_HouseTypeAssetRelVo"></a>
<a id="tocShousetypeassetrelvo"></a>
<a id="tocshousetypeassetrelvo"></a>

```json
{
  "id": "string",
  "houseTypeId": "string",
  "fixedAssetId": "string",
  "fixedAssetName": "string"
}

```

标准配套列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|houseTypeId|string|false|none|户型id|户型id|
|fixedAssetId|string|false|none|固定资产id|固定资产id|
|fixedAssetName|string|false|none|固定资产姓名|固定资产姓名|

<h2 id="tocS_ProjectHouseTypeDetailVO">ProjectHouseTypeDetailVO</h2>

<a id="schemaprojecthousetypedetailvo"></a>
<a id="schema_ProjectHouseTypeDetailVO"></a>
<a id="tocSprojecthousetypedetailvo"></a>
<a id="tocsprojecthousetypedetailvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "houseTypeName": "string",
  "roomNum": 0,
  "hallNum": 0,
  "toiletNum": 0,
  "decorationType": 0,
  "livableNum": 0,
  "houseTypeDesc": "string",
  "specialFeatures": "string",
  "specialFeaturesList": [
    "string"
  ],
  "houseTypeImg": "string",
  "assetList": [
    {
      "id": "string",
      "houseTypeId": "string",
      "fixedAssetId": "string",
      "fixedAssetName": "string"
    }
  ]
}

```

宿舍户型详情VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||户型ID|
|projectId|string|false|none||项目ID|
|houseTypeName|string|false|none||户型名称|
|roomNum|integer(int32)|false|none||房间数|
|hallNum|integer(int32)|false|none||厅数|
|toiletNum|integer(int32)|false|none||卫生间数|
|decorationType|integer(int32)|false|none||装修类型|
|livableNum|integer(int32)|false|none||可住人数|
|houseTypeDesc|string|false|none||户型描述|
|specialFeatures|string|false|none||户型特色|
|specialFeaturesList|[string]|false|none||户型特色列表|
|houseTypeImg|string|false|none||户型图片|
|assetList|[[HouseTypeAssetRelVo](#schemahousetypeassetrelvo)]|false|none||标准配套列表|

<h2 id="tocS_SysProjectStatisticsVo">SysProjectStatisticsVo</h2>

<a id="schemasysprojectstatisticsvo"></a>
<a id="schema_SysProjectStatisticsVo"></a>
<a id="tocSsysprojectstatisticsvo"></a>
<a id="tocssysprojectstatisticsvo"></a>

```json
{
  "projectCount": 0,
  "receivedArea": 0,
  "totalRoomArea": 0,
  "roomCount": 0,
  "pendingDeliveryCount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectCount|integer(int32)|false|none||项目数|
|receivedArea|number|false|none||资产面积(万平方米)|
|totalRoomArea|number|false|none||房源总面积(万平方米)|
|roomCount|integer(int32)|false|none||房源数|
|pendingDeliveryCount|integer(int32)|false|none||待交付房源数量|

<h2 id="tocS_ProjectTreeNodeVo">ProjectTreeNodeVo</h2>

<a id="schemaprojecttreenodevo"></a>
<a id="schema_ProjectTreeNodeVo"></a>
<a id="tocSprojecttreenodevo"></a>
<a id="tocsprojecttreenodevo"></a>

```json
{
  "id": "string",
  "name": "string",
  "children": [
    {
      "id": "string",
      "name": "string",
      "children": [
        {
          "id": "string",
          "name": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

地块楼栋组织树节点VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||节点ID|
|name|string|false|none||节点名称|
|children|[[ProjectTreeNodeVo](#schemaprojecttreenodevo)]|false|none||子节点列表|

<h2 id="tocS_SysFloor">SysFloor</h2>

<a id="schemasysfloor"></a>
<a id="schema_SysFloor"></a>
<a id="tocSsysfloor"></a>
<a id="tocssysfloor"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmFloorId": "string",
  "projectId": "string",
  "buildingId": "string",
  "floorName": "string",
  "floorTypeName": "string",
  "designFloorHeight": "string",
  "designLoad": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none||none|
|mdmFloorId|string|false|none||none|
|projectId|string|false|none||none|
|buildingId|string|false|none||none|
|floorName|string|false|none||none|
|floorTypeName|string|false|none||none|
|designFloorHeight|string|false|none||none|
|designLoad|string|false|none||none|
|isDel|boolean|false|none||none|

<h2 id="tocS_SysBuilding">SysBuilding</h2>

<a id="schemasysbuilding"></a>
<a id="schema_SysBuilding"></a>
<a id="tocSsysbuilding"></a>
<a id="tocssysbuilding"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "source": 0,
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "mdmBuildingName": "string",
  "certificateBuildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none||none|
|source|integer(int32)|false|none||none|
|mdmBuildingId|string|false|none||none|
|projectId|string|false|none||none|
|parcelId|string|false|none||none|
|buildingName|string|false|none||none|
|mdmBuildingName|string|false|none||none|
|certificateBuildingName|string|false|none||none|
|upFloorNums|integer(int32)|false|none||none|
|underFloorNums|integer(int32)|false|none||none|
|totalSelfArea|string|false|none||none|
|isDel|boolean|false|none||none|

