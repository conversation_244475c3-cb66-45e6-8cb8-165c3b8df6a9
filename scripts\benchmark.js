#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 构建性能基准测试脚本
 */

const buildConfigs = [
    {
        name: '标准构建',
        command: 'npm run build',
        description: '使用标准配置的生产构建'
    },
    {
        name: '快速构建',
        command: 'npm run build:fast',
        description: '使用性能优化配置的快速构建'
    },
    {
        name: '超级构建',
        command: 'npm run build:turbo',
        description: '使用最大内存和CPU优化的超快构建'
    }
];

function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return minutes > 0 ? `${minutes}分${remainingSeconds}秒` : `${remainingSeconds}秒`;
}

function formatSize(bytes) {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(2)} MB`;
}

function getDistSize() {
    const distPath = path.join(process.cwd(), 'dist');
    if (!fs.existsSync(distPath)) {
        return 0;
    }
    
    let totalSize = 0;
    
    function calculateSize(dirPath) {
        const files = fs.readdirSync(dirPath);
        files.forEach(file => {
            const filePath = path.join(dirPath, file);
            const stats = fs.statSync(filePath);
            if (stats.isDirectory()) {
                calculateSize(filePath);
            } else {
                totalSize += stats.size;
            }
        });
    }
    
    calculateSize(distPath);
    return totalSize;
}

function cleanDist() {
    const distPath = path.join(process.cwd(), 'dist');
    if (fs.existsSync(distPath)) {
        execSync('rm -rf dist', { stdio: 'pipe' });
    }
}

async function runBenchmark() {
    console.log('\n🚀 开始构建性能基准测试...\n');
    console.log('='.repeat(80));
    
    const results = [];
    
    for (const config of buildConfigs) {
        console.log(`\n📦 正在执行: ${config.name}`);
        console.log(`📋 描述: ${config.description}`);
        console.log(`⚡ 命令: ${config.command}\n`);
        
        // 清理之前的构建产物
        cleanDist();
        
        const startTime = Date.now();
        
        try {
            execSync(config.command, { 
                stdio: 'pipe',
                env: { ...process.env, NODE_ENV: 'production' }
            });
            
            const endTime = Date.now();
            const buildTime = (endTime - startTime) / 1000;
            const distSize = getDistSize();
            
            const result = {
                name: config.name,
                buildTime,
                distSize,
                success: true
            };
            
            results.push(result);
            
            console.log(`✅ ${config.name} 完成:`);
            console.log(`   ⏱️  构建时间: ${formatTime(buildTime)}`);
            console.log(`   📦 输出大小: ${formatSize(distSize)}`);
            
        } catch (error) {
            console.log(`❌ ${config.name} 失败:`, error.message);
            results.push({
                name: config.name,
                buildTime: 0,
                distSize: 0,
                success: false,
                error: error.message
            });
        }
        
        console.log('-'.repeat(50));
    }
    
    // 输出对比结果
    console.log('\n📊 构建性能对比结果:');
    console.log('='.repeat(80));
    
    const successfulResults = results.filter(r => r.success);
    
    if (successfulResults.length > 0) {
        // 找出最快的构建
        const fastest = successfulResults.reduce((prev, current) => 
            current.buildTime < prev.buildTime ? current : prev
        );
        
        console.log('\n🏆 构建时间排名:');
        successfulResults
            .sort((a, b) => a.buildTime - b.buildTime)
            .forEach((result, index) => {
                const speedup = fastest.buildTime === result.buildTime ? 
                    '' : ` (慢 ${((result.buildTime / fastest.buildTime - 1) * 100).toFixed(1)}%)`;
                console.log(`   ${index + 1}. ${result.name}: ${formatTime(result.buildTime)}${speedup}`);
            });
        
        console.log('\n📦 输出大小对比:');
        successfulResults.forEach(result => {
            console.log(`   ${result.name}: ${formatSize(result.distSize)}`);
        });
        
        console.log('\n💡 优化建议:');
        const fastestConfig = fastest.name;
        console.log(`   🎯 推荐使用 "${fastestConfig}" 配置以获得最佳构建速度`);
        
        if (fastest.buildTime < 60) {
            console.log(`   ⚡ 当前最快构建时间已经很理想 (${formatTime(fastest.buildTime)})`);
        } else {
            console.log(`   🔧 可以考虑进一步优化以减少构建时间`);
        }
    }
    
    console.log('\n✨ 基准测试完成!');
    console.log('='.repeat(80));
}

// 检查是否在CI环境中
if (process.env.CI) {
    console.log('检测到CI环境，跳过基准测试');
    process.exit(0);
}

runBenchmark().catch(console.error); 