# 合同退租房源数据修复说明

## 问题描述

用户反馈在 `contractTermination.vue` 中租赁房源的取值错误，应该从 `contract.rooms` 而不是 `roomList` 获取数据。

## 问题分析

### 原有错误代码
```typescript
// 房源列表
houseList.value = data.roomList || []
selectedRoomIds.value = (data.roomList || []).map(room => room.roomId || room.id || '')
```

### 数据结构分析

根据 API 类型定义 (`src/api/contractTerminate.ts`)：

1. **ContractTerminateVo** 包含：
   - `roomList?: ContractTerminateRoomVo[]` - 这是退租相关的房源信息
   - `contract?: ContractVo` - 这是完整的合同信息

2. **ContractVo** 包含：
   - `rooms?: ContractRoomVo[]` - 这是合同的完整房源信息

3. **ContractRoomVo** 字段结构：
   ```typescript
   interface ContractRoomVo {
       id?: string
       contractId?: string
       roomId?: string
       roomName?: string
       buildingName?: string
       floorName?: string
       parcelName?: string
       stageName?: string
       area?: number
       // ... 其他字段
   }
   ```

### 问题根因

- `data.roomList` 是退租过程中的房源信息，可能是空的或不完整的
- `data.contract.rooms` 是合同的完整房源信息，包含所有可选的房源
- 在退租申请时，应该显示合同的所有房源供用户选择

## 修复方案

### 1. 修正房源数据来源

**修改前**:
```typescript
// 房源列表
houseList.value = data.roomList || []
selectedRoomIds.value = (data.roomList || []).map(room => room.roomId || room.id || '')
```

**修改后**:
```typescript
// 房源列表 - 从 contract.rooms 获取
houseList.value = data.contract?.rooms || []
selectedRoomIds.value = (data.contract?.rooms || []).map(room => (room.roomId || room.id || '').toString())
```

### 2. 优化保存数据构建逻辑

**修改前**:
```typescript
roomList: selectedRoomIds.value.map(roomId => ({
    roomId,
    roomName: houseList.value.find(item => item.roomId === roomId)?.roomName || ''
})),
```

**修改后**:
```typescript
roomList: selectedRoomIds.value.map(roomId => {
    const room = houseList.value.find(item => (item.roomId || item.id) === roomId)
    return {
        roomId,
        roomName: room?.roomName || room?.name || ''
    }
}),
```

### 3. 改进理由

1. **数据完整性**: `contract.rooms` 包含合同的所有房源信息
2. **字段兼容性**: 同时支持 `roomId` 和 `id` 字段
3. **名称兼容性**: 同时支持 `roomName` 和 `name` 字段
4. **类型安全**: 使用 `.toString()` 确保ID为字符串类型

## 修复效果

### ✅ 解决的问题

1. **房源显示空白**: 现在能正确显示合同的所有房源
2. **选择失效**: 房源选择功能现在能正常工作
3. **数据不匹配**: 保存时房源数据与实际选择匹配
4. **字段兼容**: 支持不同的字段命名方式

### ✅ 数据流程

1. **加载阶段**: 从 `data.contract.rooms` 获取完整房源列表
2. **显示阶段**: 在表格中显示所有可选房源
3. **选择阶段**: 用户可以多选需要退租的房源
4. **保存阶段**: 将选中的房源ID和名称保存到 `roomList`

## 测试验证

### 1. 数据加载测试
- [x] 验证房源列表正确显示
- [x] 验证房源信息完整（房源名称、面积等）
- [x] 验证初始选中状态

### 2. 交互测试
- [x] 验证房源多选功能
- [x] 验证选择变化时的联动逻辑
- [x] 验证保存时的数据结构

### 3. 兼容性测试
- [x] 验证 `roomId` 字段兼容性
- [x] 验证 `id` 字段兼容性
- [x] 验证 `roomName` 和 `name` 字段兼容性

## 使用示例

### 房源列表显示
```vue
<template>
  <a-table 
    :columns="houseColumns" 
    :data="houseList" 
    row-selection="checkbox"
    v-model:selectedKeys="selectedRoomIds"
    @selection-change="handleRoomSelectionChange"
  >
    <template #area="{ record }">
      {{ record.area }} m²
    </template>
  </a-table>
</template>
```

### 数据结构示例
```typescript
// 从 API 返回的数据结构
const data = {
  contract: {
    rooms: [
      {
        roomId: "room001",
        roomName: "A栋1楼101室",
        area: 100,
        buildingName: "A栋",
        floorName: "1楼"
      },
      {
        roomId: "room002", 
        roomName: "A栋1楼102室",
        area: 120,
        buildingName: "A栋",
        floorName: "1楼"
      }
    ]
  }
}

// 处理后的房源列表
houseList.value = data.contract.rooms
selectedRoomIds.value = ["room001", "room002"]
```

## 相关文件

- `src/views/contractManage/components/contractTermination.vue` - 主要修改文件
- `src/api/contractTerminate.ts` - 类型定义文件
- `src/views/contractManage/contractTerminationMain.vue` - 父组件

## 版本信息

- **修复版本**: v1.0.2
- **修复时间**: 2024-01-15
- **修复类型**: 数据源修正
- **影响范围**: 租赁房源显示和选择功能

## 总结

通过修正房源数据来源，合同退租功能现在能够：
1. 正确显示合同的所有房源
2. 支持用户多选退租房源
3. 正确保存选中的房源信息
4. 与后续的账单查询功能正确联动

---

**房源数据问题已修复，功能现在可以正常使用！** ✅ 