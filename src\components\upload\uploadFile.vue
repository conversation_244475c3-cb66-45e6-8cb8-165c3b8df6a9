<template>
    <div class="upload-file-container">
        <div v-if="!readonly" class="upload-area">
            <a-upload :file-list="fileList" :limit="limit" :accept="accept" :max-size="maxSize * 1024 * 1024"
                :disabled="disabled" :show-file-list="false" :custom-request="customRequest" @success="handleSuccess"
                @change="handleChange" @exceed-limit="handleExceedLimit" @exceed-size="handleExceedSize">
            </a-upload>
        </div>

        <div class="file-list">
            <div v-for="(file, index) in fileList" :key="index" class="file-item">
                <div class="file-info">
                    <div class="file-icon">
                        <icon-file />
                    </div>
                    <a-typography-paragraph class="file-name" :ellipsis="{
                        rows: 1,
                        showTooltip: true,
                        css: true
                    }">
                        {{ file.name || '' }}
                    </a-typography-paragraph>
                </div>
                <a-space>
                    <a-link @click="handlePreview(file)">查看</a-link>
                    <a-link v-if="!readonly" @click="handleRemove(file)">删除</a-link>
                </a-space>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';
import type { FileItem } from '@arco-design/web-vue/es/upload/interfaces';
import { uploadFile } from '@/api/common';

interface Props {
    // 文件列表，用于v-model双向绑定
    modelValue?: string;
    // 上传接口地址
    uploadApi?: (params: any) => Promise<any>;
    // 接受的文件类型，例如'.jpg,.png'
    accept?: string;
    // 单个文件大小限制，单位MB
    maxSize?: number;
    // 最大上传文件数量，0表示不限制
    limit?: number;
    // 是否为只读模式
    readonly?: boolean;
    // 是否禁用上传功能
    disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    accept: '',
    maxSize: 5,
    limit: 0,
    readonly: false,
    disabled: false,
});

const emit = defineEmits(['update:modelValue', 'success', 'error', 'exceed-limit', 'exceed-size']);

// 文件列表
const fileList = ref<FileItem[]>([]);

// 防止循环更新的标志
let isUpdatingFileList = false;
let isEmittingUpdate = false;

// 监听传入的值变化
watch(() => props.modelValue, (val) => {
    try {
        if (isEmittingUpdate) return; // 避免递归触发

        isUpdatingFileList = true;

        if (val) {
            // 字符串转数组
            const list = JSON.parse(val);
            fileList.value = list.map((item: any, index: number) => {
                // 为每个文件生成唯一的uid，用于后续的文件项匹配
                const uid = `file-${Date.now()}-${index}-${Math.random().toString(36).substr(2, 9)}`;
                return {
                    uid,
                    status: 'done',
                    name: item.fileName || '',
                    url: item.fileUrl || ''
                } as FileItem;
            });
        } else {
            fileList.value = [];
        }

        nextTick(() => {
            isUpdatingFileList = false;
        });
    } catch (error) {
    }
}, { deep: true, immediate: true });

// 自定义上传请求
const customRequest = async (options: any) => {
    const { onProgress, onSuccess, onError, fileItem } = options;
    const formData = new FormData();
    formData.append('file', fileItem.file);
    try {
        // 如果提供了上传API，则使用它；否则使用通用上传接口
        let res;
        if (typeof props.uploadApi === 'function') {
            res = await props.uploadApi(formData);
        } else {
            // 使用通用上传接口
            res = await uploadFile(formData);
        }

        // // 获取文件信息
        // const fileName = res.data.fileName;
        // const fileUrl = res.data.fileUrl;
        // // 同时更新传入的fileItem（为了兼容a-upload组件）
        // fileItem.name = fileName;
        // fileItem.url = fileUrl;

        // fileList.value.push(fileItem)

        // const list = fileList.value.map((item: any) => {
        //     return {
        //         fileName: item.name,
        //         fileUrl: item.url
        //     }
        // })
        // emit('update:modelValue', JSON.stringify(list));

        onProgress({ percent: 100 });
        onSuccess(res);

        // 处理响应数据，只包含fileName和fileUrl
        // const fileData = {
        //     fileName: fileName,
        //     fileUrl: fileUrl
        // };

        // emit('success', { data: fileData }, fileItem);
        Message.success('上传成功');
    } catch (error) {
        onError(error);
        emit('error', error, fileItem);
    }
};

// 处理成功
const handleSuccess = (file: FileItem) => {
    // 自定义处理
    file.name = file.response?.data.fileName
    file.url = file.response?.data.fileUrl
};

// 处理文件变化
const handleChange = (files: FileItem[], file: FileItem) => {
    // 简化处理：让 customRequest 负责更新文件信息
    if (file.status === 'done') {
        const list = files.map((item: any) => {
            return {
                fileName: item.name,
                fileUrl: item.url
            }
        })
        emit('update:modelValue', JSON.stringify(list));
    }
};

// 处理超出数量限制
const handleExceedLimit = (files: FileItem[]) => {
    Message.warning(`最多只能上传${props.limit}个文件`);
    emit('exceed-limit', files);
};

// 处理超出大小限制
const handleExceedSize = (file: FileItem) => {
    Message.warning(`文件大小不能超过${props.maxSize}MB`);
    emit('exceed-size', file);
};

// 处理预览
const handlePreview = (file: FileItem) => {
    if (file.url) {
        window.open(file.url);
    }
};

// 处理删除
const handleRemove = (file: FileItem) => {
    const index = fileList.value.findIndex((item) => item.uid === file.uid);
    if (index !== -1) {
        fileList.value.splice(index, 1);
        const list = fileList.value.map((item: any) => {
            return {
                fileName: item.name,
                fileUrl: item.url
            }
        })
        emit('update:modelValue', list.length > 0 ? JSON.stringify(list) : '');
    }
};
</script>

<style lang="less" scoped>
.upload-file-container {
    width: 100%;

    .upload-area {
        margin-bottom: 16px;
    }

    .upload-trigger {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 148px;
        border: 1px dashed #c9cdd4;
        border-radius: 2px;
        cursor: pointer;

        &:hover {
            border-color: #1677ff;
        }

        .upload-text {
            margin-top: 8px;
            color: #4e5969;
        }
    }

    .file-list {
        width: 100%;
        max-width: 500px;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 1px solid #E5E6EA;
            gap: 16px;

            .file-info {
                flex: 1;
                display: flex;
                align-items: center;

                .file-icon {
                    margin-right: 8px;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                :deep(.file-name) {
                    flex: 1;
                    margin-bottom: 0;
                }
            }
        }
    }
}
</style>