<template>
  <div class="entry-and-exit">
    <div class="entry-title">
      <div class="title-decorator"></div>
      进出场
    </div>
    <div class="entry-content">
      <div class="entry-item" v-for="item in entryItems" :key="item.label">
        <div class="item-background"></div>
        <div class="item-icon">
          <img :src="item.icon" :alt="item.label" />
        </div>
        <div class="item-label">{{ item.label }}</div>
        <div class="item-count">{{ item.count }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import entry1 from '@/assets/images/dashboard/entry-1.png'
import entry2 from '@/assets/images/dashboard/entry-2.png'
import entry3 from '@/assets/images/dashboard/entry-3.png'

const entryItems = ref([
  { label: '待进场', count: 9, icon: entry1 },
  { label: '待出场', count: 8, icon: entry2 },
  { label: '出场办理中', count: 9, icon: entry3 },
])
</script>

<style lang="less" scoped>
.entry-and-exit {
  background: #fff;
  border-radius: 10px 0 0 0;
  padding: 20px 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  
  .entry-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #000;
    position: relative;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    
    .title-decorator {
      width: 5px;
      height: 19px;
      background-color: #0071ff;
      border-radius: 4px 0px 4px 0px;
      margin-right: 14px;
    }
  }
  
  .entry-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .entry-item {
    position: relative;
    height: 56px;
    display: flex;
    align-items: center;
    padding: 0 17px;
    opacity: 0.9;
    
    .item-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, #FCFEFF 0%, #E3F0FF 100%);
      border-radius: 35px;
      z-index: 1;
    }
    
    .item-icon {
      position: relative;
      z-index: 2;
      width: 29px;
      height: 29px;
      background: linear-gradient(180deg, #AFC7E2 0%, #84A3CA 100%);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 5px;
      
      img {
        width: 29px;
        object-fit: contain;
      }
    }
    
    .item-label {
      position: relative;
      z-index: 2;
      font-size: 15px;
      color: #000;
      line-height: 2.47;
      margin-right: auto;
    }
    
    .item-count {
      position: relative;
      z-index: 2;
      font-size: 20px;
      font-weight: 500;
      color: #000;
      line-height: 1.85;
    }
  }
}
</style>