<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form label-align="right" :model="filterForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
                        auto-label-width>
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="projectId" label="项目">
                                    <ProjectTreeSelect
                                        v-model="filterForm.projectId"
                                        :min-level="4"
                                        @change="handleProjectChange"
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="contractNo" label="合同号">
                                    <a-input v-model="filterForm.contractNo" placeholder="请输入合同号" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="roomName" label="租赁单元">
                                    <a-input v-model="filterForm.roomName" placeholder="请输入租赁单元" allow-clear />
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="customerName" label="承租人">
                                    <a-input v-model="filterForm.customerName" placeholder="请输入承租人" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="subjectIds" label="费用类型">
                                    <a-select v-model="filterForm.subjectIds" placeholder="请选择费用类型" allow-clear multiple>
                                        <a-option v-for="item in costTypeOptions" :key="item.value" :value="item.value">
                                            {{ item.label }}
                                        </a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="approveStatus" label="审批状态">
                                    <a-select v-model="filterForm.approveStatus" placeholder="请选择审批状态" allow-clear multiple>
                                        <a-option :value="0">草稿</a-option>
                                        <a-option :value="1">审批中</a-option>
                                        <a-option :value="2">已通过</a-option>
                                        <a-option :value="3">已驳回</a-option>
                                        <a-option :value="4">作废</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin-top: 0" />

            <!-- 操作按钮 -->
            <div class="action-bar">
                <a-space>
                    <a-button type="primary" @click="handleAdd">
                        <template #icon><icon-plus /></template>
                        新增
                    </a-button>
                    <a-button @click="handleExport">
                        <template #icon><icon-download /></template>
                        导出
                    </a-button>
                </a-space>
            </div>

            <!-- 表格区域 -->
            <a-table
                :columns="columns"
                :data="tableData"
                :pagination="pagination"
                :bordered="{ cell: true }"
                :scroll="{ x: 1 }"
                :stripe="true"
                :loading="loading"
                row-key="id"
                @page-change="onPageChange"
                @page-size-change="onPageSizeChange"
            >
                <template #index="{ rowIndex }">
                    {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
                </template>
                <template #costPeriod="{ record }">
                    {{ record.startDate && record.endDate ? `${record.startDate} ~ ${record.endDate}` : '' }}
                </template>
                <template #contractPeriod="{ record }">
                    {{ record.contractStartDate && record.contractEndDate ? `${record.contractStartDate} ~ ${record.contractEndDate}` : '' }}
                </template>
                <template #approveStatus="{ record }">
                    {{ getApproveStatusText(record.approveStatus) }}
                </template>
                <template #totalAmount="{ record }">
                    {{ formatAmount(record.totalAmount) }}
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <a-button type="text" size="mini" @click="handleView(record)">
                            查看
                        </a-button>
                        <a-button type="text" size="mini" @click="handleEdit(record)" v-if="record.approveStatus === 0">
                            编辑
                        </a-button>
                        <a-button type="text" size="mini" @click="handleSubmit(record)" v-if="record.approveStatus === 0">
                            发起审批
                        </a-button>
                        <a-button type="text" size="mini" @click="handleApprove(record)" v-if="record.approveStatus === 1">
                            查看审批流
                        </a-button>
                        <a-button type="text" size="mini" @click="handleDelete(record)" v-if="record.approveStatus === 0">
                            删除
                        </a-button>
                    </a-space>
                </template>
            </a-table>
        </a-card>

        <!-- 新增/编辑临时收费抽屉 -->
        <temporary-cost-drawer
            v-if="showDrawer"
            ref="temporaryCostDrawerRef"
            @success="handleDrawerSuccess"
            @cancel="handleDrawerCancel"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'
import { IconSearch, IconRefresh, IconPlus, IconDownload } from '@arco-design/web-vue/es/icon'
import { Message, Modal } from '@arco-design/web-vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import TemporaryCostDrawer from './components/TemporaryCostDrawer.vue'
import {
    getTemporaryCostList,
    deleteTemporaryCost,
    submitTemporaryCost,
    approveTemporaryCost,
    exportTemporaryCostList,
    type TemporaryCostQueryDTO,
    type TemporaryCostVo,
    type TemporaryCostExportDTO,
    TemporaryApproveStatus
} from '@/api/temporaryCost'
import { DictType, getDictLabel } from '@/dict/index'
import { dictData } from '@/dict/data'
import { exportExcel } from '@/utils/exportUtil'

// 筛选表单数据
const filterForm = reactive<TemporaryCostQueryDTO>({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    contractNo: '',
    roomName: '',
    customerName: '',
    subjectIds: [],
    approveStatus: []
})

// 表格数据
const tableData = ref<TemporaryCostVo[]>([])
const loading = ref(false)

const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
})

// 表格列配置
const columns = [
    {
        title: '序号',
        slotName: 'index',
        width: 80,
        align: 'center'
    },
    {
        title: '项目',
        dataIndex: 'projectName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 180
    },
    {
        title: '费用类型',
        dataIndex: 'subjectName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 100
    },
    {
        title: '账单周期',
        dataIndex: 'costPeriod',
        slotName: 'costPeriod',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 220
    },
    {
        title: '应收金额',
        dataIndex: 'actualReceivable',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 120
    },
    {
        title: '应收日期',
        dataIndex: 'receivableDate',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 120
    },
    {
        title: '合同号',
        dataIndex: 'contractNo',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 180
    },
    {
        title: '租赁单元',
        dataIndex: 'roomName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 150
    },
    {
        title: '承租方',
        dataIndex: 'customerName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 120
    },
    {
        title: '合同周期',
        dataIndex: 'contractPeriod',
        slotName: 'contractPeriod',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 220
    },
    {
        title: '审批状态',
        dataIndex: 'approveStatus',
        slotName: 'approveStatus',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 100
    },
    {
        title: '审批日期',
        dataIndex: 'approveTime',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 150
    },
    {
        title: '创建人',
        dataIndex: 'createByName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 120
    },
    {
        title: '创建日期',
        dataIndex: 'createTime',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 150
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 260,
        ellipsis: false,
        tooltip: false,
        fixed: 'right',
        align: 'center'
    }
]

// 抽屉相关
const temporaryCostDrawerRef = ref()
const showDrawer = ref(false)

// 项目变更控制
const isInit = ref(false)

// 费用类型选项（排除定金、保证金、租金）
const costTypeOptions = computed(() => {
    const allCostTypes = dictData[DictType.COST_TYPE_AND_TAX_RATE] || []
    // 排除定金(10)、保证金(20)、租金(30)
    return allCostTypes.filter(item => ![10, 20, 30].includes(item.value))
})

// 方法
const search = async () => {
    if (!filterForm.projectId) {
        Message.warning('请先选择项目')
        return
    }

    try {
        loading.value = true
        const params: TemporaryCostQueryDTO = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            projectId: filterForm.projectId,
            contractNo: filterForm.contractNo || undefined,
            roomName: filterForm.roomName || undefined,
            customerName: filterForm.customerName || undefined,
            subjectIds: filterForm.subjectIds && filterForm.subjectIds.length > 0 ? filterForm.subjectIds : undefined,
            approveStatus: filterForm.approveStatus && filterForm.approveStatus.length > 0 ? filterForm.approveStatus : undefined
        }

        const response = await getTemporaryCostList(params)
        if (response) {
            tableData.value = response.rows || []
            pagination.total = response.total || 0
        }
    } catch (error) {
        console.error('获取临时收费列表失败:', error)
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 分页变化
const onPageChange = (current: number) => {
    pagination.current = current
    search()
}

// 每页条数变化
const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    search()
}

// 重置
const reset = () => {
    Object.assign(filterForm, {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        contractNo: '',
        roomName: '',
        customerName: '',
        subjectIds: [],
        approveStatus: []
    })
    pagination.current = 1
    tableData.value = []
    pagination.total = 0
}

// 项目变更
const handleProjectChange = (value: string | number, selectedOrg: any) => {
    console.log('项目变化，检查是否有项目ID再调用列表', value, selectedOrg)

    // 存储项目信息
    currentProject.value = {
        projectId: value,
        projectName: selectedOrg?.name || ''
    }

    // 只有在有项目ID时才自动触发搜索
    if (value && !isInit.value) {
        isInit.value = true
        pagination.current = 1
        search()
    }
}

// 新增
const handleAdd = async () => {
    // 检查是否已选择项目
    if (!filterForm.projectId) {
        Message.warning('请先选择项目')
        return
    }

    showDrawer.value = true
    await nextTick()
    // 传递项目ID和项目名称给表单组件
    temporaryCostDrawerRef.value?.show('add', {
        projectId: filterForm.projectId,
        projectName: currentProject.value.projectName
    })
}

// 查看
const handleView = async (record: TemporaryCostVo) => {
    showDrawer.value = true
    await nextTick()
    temporaryCostDrawerRef.value?.show('view', record)
}

// 编辑
const handleEdit = async (record: TemporaryCostVo) => {
    showDrawer.value = true
    await nextTick()
    temporaryCostDrawerRef.value?.show('edit', record)
}

// 删除
const handleDelete = (record: TemporaryCostVo) => {
    Modal.confirm({
        title: '确认删除',
        content: '确定要删除这条临时收费记录吗？',
        onOk: async () => {
            try {
                await deleteTemporaryCost(record.id!)
                Message.success('删除成功')
                search()
            } catch (error) {
                console.error('删除失败:', error)
            }
        }
    })
}

// 提交审批
const handleSubmit = (record: TemporaryCostVo) => {
    Modal.confirm({
        title: '确认提交',
        content: '确定要提交这条临时收费记录进行审批吗？',
        onOk: async () => {
            try {
                await submitTemporaryCost(record.id!)
                Message.success('提交成功')
                search()
            } catch (error) {
                console.error('提交失败:', error)
            }
        }
    })
}

// 审批流
const handleApprove = (record: TemporaryCostVo) => {
    Message.info('审批功能待实现')
}

// 导出
const handleExport = async () => {
    if (!filterForm.projectId) {
        Message.warning('请先选择项目')
        return
    }

    try {
        // 构建导出查询参数，使用当前筛选条件
        const exportParams: TemporaryCostExportDTO = {
            projectId: filterForm.projectId,
            contractNo: filterForm.contractNo || undefined,
            roomName: filterForm.roomName || undefined,
            customerName: filterForm.customerName || undefined,
            subjectIds: filterForm.subjectIds && filterForm.subjectIds.length > 0 ? filterForm.subjectIds : undefined,
            approveStatus: filterForm.approveStatus && filterForm.approveStatus.length > 0 ? filterForm.approveStatus : undefined
        }

        await exportExcel(exportTemporaryCostList, exportParams, '临时收费列表')
        Message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
    }
}

// 抽屉成功回调
const handleDrawerSuccess = () => {
    showDrawer.value = false
    search()
}

// 抽屉取消回调
const handleDrawerCancel = () => {
    showDrawer.value = false
}

// 审批状态文本
const getApproveStatusText = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '草稿',
        1: '审批中',
        2: '已通过',
        3: '已驳回',
        4: '作废'
    }
    return statusMap[status || 0] || '-'
}

// 审批状态颜色
const getApproveStatusColor = (status: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'gray',
        1: 'blue',
        2: 'green',
        3: 'red',
        4: 'red'
    }
    return colorMap[status || 0] || 'gray'
}

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 获取费用类型文本
const getCostTypeText = (costType: number | undefined) => {
    if (costType === undefined || costType === null) return '-'
    return getDictLabel(DictType.COST_TYPE_AND_TAX_RATE, costType) || '-'
}

// 存储当前选中的项目信息
const currentProject = ref<any>({
    projectId: '',
    projectName: ''
})
</script>

<style scoped lang="less">
.container {
    padding: 0 16px 16px 16px;
}

.general-card {
    border-radius: 4px;

    :deep(.arco-card-body) {
        padding: 16px;
    }
}

.action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
}
</style>
