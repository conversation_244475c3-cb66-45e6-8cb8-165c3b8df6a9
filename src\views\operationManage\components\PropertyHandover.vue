<template>
    <div class="property-handover">
        <!-- 标题和操作按钮 -->
        <div class="section-header">
            <section-title :title="title" />
            <div class="operation-row" v-if="!readonly">
                <a-space>
                    <a-date-picker placeholder="出场日期" :model-value="batchExitDate"
                        @change="$emit('update:batchExitDate', $event)" style="width: 200px" />
                    <a-button type="primary" size="small" @click="$emit('batchSetExitDate')">
                        批量设置出场日期
                    </a-button>
                    <a-button type="primary" size="small" @click="$emit('batchConfirm')">
                        商服批量确认
                    </a-button>
                    <a-button type="primary" size="small" @click="$emit('copyPropertyUrl')">
                        复制物业确认单地址
                    </a-button>
                </a-space>
            </div>
        </div>

        <!-- 批量操作 -->
        <div class="batch-operations" v-if="!readonly">
            <a-space>
                <a-checkbox :model-value="selectAll" @click.stop @change="(checked) => {
                    console.log('PropertyHandover: 全选状态变更', checked);
                    $emit('selectAll', checked);
                }">
                    全选
                </a-checkbox>
                <a-input placeholder="搜索房源" v-model="searchKeywordLocal" @input="handleSearch" style="width: 200px" />

            </a-space>
        </div>

        <!-- 有没有哪个字段可以区分 个人的 和 公司的 -->

        <!-- 房源列表 -->
        <a-collapse :default-active-key="activePropertyKeys" :expand-icon-position="'right'" style="margin-top: 16px;">
            <a-collapse-item v-for="(room, index) in filteredRoomList" :key="index">
                <template #header>
                    <div class="property-header">
                        <div class="left">
                            <a-checkbox :model-value="room.selected" :disabled="readonly" @click.stop @change="(checked) => {
                                console.log('PropertyHandover: 单选房源状态变更', room.id, checked, filteredRoomList, room);
                                $emit('selectSingle', room, checked);
                            }" />
                            <span>{{ room.buildingName }} {{ room.roomName }}</span>
                        </div>
                        <div class="right">
                            <!-- 根据模式显示不同的标签样式 -->
                            <template v-if="tagMode === 'simple'">
                                <a-tag color="green" v-if="room.isBusinessConfirmed" class="common-tag">商服已确认</a-tag>
                                <a-tag color="red" v-else class="common-tag">商服未确认</a-tag>

                                <a-tag color="green" v-if="room.isEngineeringConfirmed && room.isFinanceConfirmed"
                                    class="common-tag">物业已确认</a-tag>
                                <a-tag color="red" v-else class="common-tag">物业未确认</a-tag>
                            </template>
                            <template v-else>
                                <a-tag color="green" v-if="room.isBusinessConfirmed" class="common-tag">商服已确认</a-tag>
                                <a-tag color="red" v-else class="common-tag">商服未确认</a-tag>

                                <a-tag color="green" v-if="room.isEngineeringConfirmed && room.isFinanceConfirmed"
                                    class="common-tag">物业已确认</a-tag>
                                <a-tag color="red" v-else class="common-tag">物业未确认</a-tag>
                            </template>
                            <span class="date">出场日期：{{ room.exitDate || '未设置' }}</span>
                            <span class="delay-days" v-if="getDelayDays(room) > 0">
                                延迟天数：<span class="delay-days-number">{{ getDelayDays(room) }}</span>
                            </span>
                        </div>
                    </div>
                </template>

                <exit-room-form :room="room" :contract-purpose="contractPurpose" :readonly="readonly"
                    @save="handleSaveRoom" @cancel="$emit('cancelRoom', $event)" @sync-room-data="handleSyncRoomData" @updatePenaltyAmount="handleUpdatePenaltyAmount" />
            </a-collapse-item>
        </a-collapse>
    </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed, watch } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import ExitRoomForm from './exitRoomForm.vue'
import dayjs from 'dayjs'

// 定义props
const props = defineProps({
    title: {
        type: String,
        default: '物业交割'
    },
    showHeader: {
        type: Boolean,
        default: true
    },
    tagMode: {
        type: String,
        default: 'simple', // 'simple' 或 'detailed'
        validator: (value) => ['simple', 'detailed'].includes(value)
    },
    selectAll: {
        type: Boolean,
        default: false
    },
    searchKeyword: {
        type: String,
        default: ''
    },
    batchExitDate: {
        type: String,
        default: ''
    },
    exitRoomList: {
        type: Array,
        default: () => []
    },
    activePropertyKeys: {
        type: Array,
        default: () => []
    },
    contractPurpose: {
        type: Number,
        default: undefined
    },
    exitInfo: {
        type: Object,
        default: () => ({})
    },
    contractTerminateInfo: {
        type: Object,
        default: () => ({})
    },
    readonly: {
        type: Boolean,
        default: false
    }
})

// 定义emits
const emits = defineEmits([
    'batchConfirm',
    'copyPropertyUrl',
    'selectAll',
    'update:searchKeyword',
    'update:batchExitDate',
    'batchSetExitDate',
    'selectSingle',
    'saveRoom',
    'cancelRoom',
    'syncRoomData',
    'updatePenaltyAmount'
])

// 处理房间赔偿金额变化
const handleUpdatePenaltyAmount = (currentRoomData) => {
    console.log('handleUpdatePenaltyAmount', currentRoomData, props.exitRoomList)
    // let exitRoomList = JSON.parse(JSON.stringify(props.exitRoomList))
    props.exitRoomList.forEach(room => {
        if (room.id === currentRoomData.id) {
            room.exitRoomAssetsList = currentRoomData.exitRoomAssetsList
            room.doorWindowPenalty = currentRoomData.doorWindowPenalty
            room.keyPenalty = currentRoomData.keyPenalty
            room.cleaningPenalty = currentRoomData.cleaningPenalty
        }
    })
    emits('updatePenaltyAmount', currentRoomData, props.exitRoomList)
}

// 本地搜索关键词
const searchKeywordLocal = ref('')


// 处理搜索
const handleSearch = (value) => {
    searchKeywordLocal.value = value
    // 清空选中状态并通知父组件
    emits('selectAll', false)
}

// 过滤后的房源列表
const filteredRoomList = computed(() => {
    if (!searchKeywordLocal.value) {
        return props.exitRoomList
    }

    const keyword = searchKeywordLocal.value.toLowerCase()
    return props.exitRoomList.filter(room => {
        const buildingName = (room.buildingName || '').toLowerCase()
        const roomName = (room.roomName || '').toLowerCase()
        return buildingName.includes(keyword) || roomName.includes(keyword)
    })
})

// 计算每个房间的延迟天数
const roomDelayDays = computed(() => {
    const result = {}
    props.exitRoomList.forEach(room => {
        if (!room.exitDate || !props.contractTerminateInfo?.terminateDate) {
            result[room.id] = 0
            return
        }

        const exitDay = dayjs(room.exitDate)
        const terminateDay = dayjs(props.contractTerminateInfo.terminateDate)
        const diffDays = exitDay.diff(terminateDay, 'day')
        result[room.id] = diffDays > 0 ? diffDays : 0
    })
    return result
})

// 获取延迟天数的方法改为使用计算属性
const getDelayDays = (room) => {
    return roomDelayDays.value[room.id] || 0
}

// 监听房源列表变化，重新计算延迟天数
watch(() => props.exitRoomList, (newList) => {
    // 当房源列表变化时，会自动触发重新渲染，延迟天数也会重新计算
    console.log('房源列表变化，重新计算延迟天数')
}, { deep: true })

// 监听退租日期变化
watch(() => props.contractTerminateInfo?.terminateDate, (newDate) => {
    if (newDate) {
        console.log('退租日期变化，重新计算延迟天数')
    }
})

// 处理房间数据同步
const handleSyncRoomData = (updatedRoom) => {
    console.log('房间数据同步：', updatedRoom)
    // 向父组件发送同步事件
    emits('syncRoomData', updatedRoom)
}

// 处理房间数据保存
const handleSaveRoom = (roomData) => {
    console.log('房间数据保存：', roomData)
    // 向父组件发送保存事件
    emits('saveRoom', roomData)
}
</script>

<style scoped>
.property-handover {
    width: 100%;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.operation-row {
    flex-shrink: 0;
}

.batch-operations {
    margin-bottom: 16px;
}

.property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date {
    color: #666;
    font-size: 12px;
}

.common-tag {
    margin-right: 4px;
}

.delay-days {
    color: #ff4d4f;
    font-size: 12px;
    /* font-weight: bold; */
    background-color: #fff1f0;
    padding: 1px 8px;
    border-radius: 4px;
    border: 1px solid #ffccc7;
    margin-left: 10px;
    line-height: 20px;
}

.delay-days-number {
    font-size: 14px;
}
</style>