<template>
    <section>
        <sectionTitle :title="contractData.contractType === 2 ? '多经点位信息' : '房源信息'" />
        <div class="content">
            <a-table :data="contractData.rooms" :bordered="{ cell: true }" summary summary-text="合计" :pagination="{showJumper: true}" :scroll="{ x: 1200 }">
                <template #columns>
                    <a-table-column title="房源" data-index="roomName" :width="100" align="center" ellipsis tooltip>
                        <template #cell="{ record }">
                            <span>{{ record.parcelName+'-'+record.buildingName+'-'+record.roomName }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="面积（㎡）" data-index="area" :width="60" align="center"></a-table-column>
                    <a-table-column title="标准租金（单价）" data-index="standardUnitPrice" :width="80" align="center">
                        <template #cell="{ record }">
                            <span>{{ formatAmount(record.standardUnitPrice) }} {{ unitMap.get(record.priceUnit) }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="折扣" data-index="discount" :width="80" align="center">
                        <template #cell="{ record }">
                            <span>{{ record.discount }}%</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="签约单价" data-index="signedUnitPrice" :width="80" align="center">
                        <template #cell="{ record }">
                            <span>{{ formatAmount8(record.signedUnitPrice) }} {{ unitMap.get(record.priceUnit) }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="签约月总价（元/月）" data-index="signedMonthlyPrice" :width="80" align="center">
                        <template #cell="{ record }">
                            {{ formatAmount(record.signedMonthlyPrice) }}
                        </template>
                    </a-table-column>
                </template>
                <template #summary-cell="{ column }">
                    <template v-if="column.dataIndex === 'roomName'">合计</template>
                    <template v-if="column.dataIndex === 'area'">{{ calculateAreaTotal }}</template>
                    <template v-if="column.dataIndex === 'signedUnitPrice'">{{ formatAmount8(Number(calculateUnitAvg)) }}</template>
                    <template v-if="column.dataIndex === 'signedMonthlyPrice'">{{ formatAmount(Number(calculateTotalSum)) }}</template>
                </template>
            </a-table>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { useContractStore } from '@/store/modules/contract'
import { ContractAddDTO } from '@/api/contract';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

const unitMap = new Map([
    [1, '元/平方米/月'],
    [2, '元/月']
])

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const formatAmount8 = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 8,
        maximumFractionDigits: 8
    })
}

// 计算面积合计
const calculateAreaTotal = computed(() => {
    return contractData.value.rooms?.reduce((sum, room) => {
        return sum + (isNaN(room.area || 0) ? 0 : room.area)
    }, 0).toFixed(2) || '0.00'
})

// 计算签约单价合计
const calculateUnitAvg = computed(() => {
    if (!contractData.value.rooms?.length) return '0.00'
    const priceUnit = contractData.value.rooms[0].priceUnit
    if (priceUnit === 1) {
        // 单位为 元/平方米/月， 签约单价合计 = 签约单价 * 面积 之和 / 面积之和
        const totalArea = contractData.value.rooms.reduce((sum, room) => {
            return sum + (isNaN(room.area || 0) ? 0 : room.area)
        }, 0)
        const totalPrice = contractData.value.rooms.reduce((sum, room) => {
            return sum + (isNaN(room.signedUnitPrice || 0) ? 0 : room.signedUnitPrice * (isNaN(room.area || 0) ? 0 : room.area))
        }, 0)
        return totalArea === 0 ? '0.00' : (totalPrice / totalArea).toFixed(8)
    } else if (priceUnit === 2) {
        // 单位为 元/月， 签约单价合计 = 签约单价 之和 / 房源数量
        const totalPrice = contractData.value.rooms.reduce((sum, room) => {
            return sum + (isNaN(room.signedUnitPrice || 0) ? 0 : room.signedUnitPrice)
        }, 0)
        return (totalPrice / contractData.value.rooms.length).toFixed(8)
    } else {
        return '0.00'
    }
})

// 计算签约总价合计
const calculateTotalSum = computed(() => {
    return contractData.value.rooms?.reduce((sum, room) => {
        return sum + (isNaN(room.signedMonthlyPrice) ? 0 : room.signedMonthlyPrice)
    }, 0).toFixed(2) || '0.00'
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.detail-text {
    color: #333;
}
</style>
