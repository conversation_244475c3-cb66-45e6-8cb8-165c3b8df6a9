<template>
  <div class="test-refund-status">
    <h1>退款管理状态显示优化</h1>
    
    <div class="test-section">
      <h2>功能说明</h2>
      <p>根据订单管理页面的状态显示方式，将退款管理页面的状态也改为使用 <code>a-tag</code> 显示，提升视觉效果和用户体验。</p>
    </div>

    <div class="test-section">
      <h2>状态标签颜色配置</h2>
      
      <h3>退款状态</h3>
      <div class="status-demo">
        <a-tag color="gray">草稿</a-tag>
        <a-tag color="orange">待退款</a-tag>
        <a-tag color="green">已退款</a-tag>
        <a-tag color="red">作废</a-tag>
      </div>
      
      <h3>退款类型</h3>
      <div class="status-demo">
        <a-tag color="purple">退租退款</a-tag>
        <a-tag color="blue">退定退款</a-tag>
        <a-tag color="cyan">未明流水退款</a-tag>
      </div>
      
      <h3>退款方式</h3>
      <div class="status-demo">
        <a-tag color="green">原路退回</a-tag>
        <a-tag color="orange">银行转账</a-tag>
      </div>
      
      <h3>审批状态</h3>
      <div class="status-demo">
        <a-tag color="gray">草稿</a-tag>
        <a-tag color="orange">审批中</a-tag>
        <a-tag color="green">审批通过</a-tag>
        <a-tag color="red">审批驳回</a-tag>
      </div>
      
      <h3>退款费用类型</h3>
      <div class="status-demo">
        <a-tag color="blue">定金</a-tag>
        <a-tag color="blue">保证金</a-tag>
        <a-tag color="blue">租金</a-tag>
        <a-tag color="blue">未明流水</a-tag>
      </div>
    </div>

    <div class="test-section">
      <h2>颜色配置说明</h2>
      <table class="color-table">
        <thead>
          <tr>
            <th>状态类型</th>
            <th>状态值</th>
            <th>显示文本</th>
            <th>颜色</th>
            <th>说明</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td rowspan="4">退款状态</td>
            <td>0</td>
            <td>草稿</td>
            <td><a-tag color="gray">gray</a-tag></td>
            <td>暂存状态，可继续编辑</td>
          </tr>
          <tr>
            <td>1</td>
            <td>待退款</td>
            <td><a-tag color="orange">orange</a-tag></td>
            <td>已提交审批，等待处理</td>
          </tr>
          <tr>
            <td>2</td>
            <td>已退款</td>
            <td><a-tag color="green">green</a-tag></td>
            <td>退款已完成</td>
          </tr>
          <tr>
            <td>3</td>
            <td>作废</td>
            <td><a-tag color="red">red</a-tag></td>
            <td>退款申请已作废</td>
          </tr>
          <tr>
            <td rowspan="3">退款类型</td>
            <td>0</td>
            <td>退租退款</td>
            <td><a-tag color="purple">purple</a-tag></td>
            <td>租赁合同退租时的退款</td>
          </tr>
          <tr>
            <td>1</td>
            <td>退定退款</td>
            <td><a-tag color="blue">blue</a-tag></td>
            <td>定金退款</td>
          </tr>
          <tr>
            <td>2</td>
            <td>未明流水退款</td>
            <td><a-tag color="cyan">cyan</a-tag></td>
            <td>未明确来源的流水退款</td>
          </tr>
          <tr>
            <td rowspan="2">退款方式</td>
            <td>0</td>
            <td>原路退回</td>
            <td><a-tag color="green">green</a-tag></td>
            <td>按原支付方式退回</td>
          </tr>
          <tr>
            <td>1</td>
            <td>银行转账</td>
            <td><a-tag color="orange">orange</a-tag></td>
            <td>通过银行转账退款</td>
          </tr>
          <tr>
            <td rowspan="4">审批状态</td>
            <td>0</td>
            <td>草稿</td>
            <td><a-tag color="gray">gray</a-tag></td>
            <td>暂存状态</td>
          </tr>
          <tr>
            <td>1</td>
            <td>审批中</td>
            <td><a-tag color="orange">orange</a-tag></td>
            <td>已提交审批，等待审批</td>
          </tr>
          <tr>
            <td>2</td>
            <td>审批通过</td>
            <td><a-tag color="green">green</a-tag></td>
            <td>审批已通过</td>
          </tr>
          <tr>
            <td>3</td>
            <td>审批驳回</td>
            <td><a-tag color="red">red</a-tag></td>
            <td>审批被驳回</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="test-section">
      <h2>实现细节</h2>
      <h3>模板更新</h3>
      <pre><code>&lt;template #refundStatus="&#123; record &#125;"&gt;
    &lt;a-tag :color="getRefundStatusColor(record.refundStatus)"&gt;
        &#123;&#123;
            record?.refundStatus === 0 ? '草稿' :
            record?.refundStatus === 1 ? '待退款' :
            record?.refundStatus === 2 ? '已退款' :
            record?.refundStatus === 3 ? '作废' : ''
        &#125;&#125;
    &lt;/a-tag&gt;
&lt;/template&gt;</code></pre>

      <h3>颜色配置函数</h3>
      <pre><code>// 获取退款状态颜色
const getRefundStatusColor = (status: number | string) =&gt; &#123;
    const colorMap: Record&lt;string, string&gt; = &#123;
        '0': 'gray',      // 草稿
        '1': 'orange',    // 待退款
        '2': 'green',     // 已退款
        '3': 'red'        // 作废
    &#125;
    return colorMap[status?.toString()] || 'default'
&#125;</code></pre>
    </div>

    <div class="test-section">
      <h2>优化效果</h2>
      <ul>
        <li>✅ 状态显示更加醒目，提升视觉效果</li>
        <li>✅ 颜色语义化，便于用户快速识别状态</li>
        <li>✅ 与订单管理页面保持一致的设计风格</li>
        <li>✅ 支持不同状态类型的差异化显示</li>
        <li>✅ 提升用户体验和操作效率</li>
      </ul>
    </div>

    <div class="test-actions">
      <a-button type="primary" @click="goToRefundManage">
        查看退款管理页面
      </a-button>
      <a-button @click="goToOrderList" style="margin-left: 16px;">
        对比订单管理页面
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToRefundManage = () => {
  router.push('/financeManage/refundManage')
}

const goToOrderList = () => {
  router.push('/orderManagement/list')
}
</script>

<style scoped lang="less">
.test-refund-status {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    color: #1d2129;
    margin-bottom: 24px;
    text-align: center;
  }

  .test-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      color: #1d2129;
      margin-bottom: 16px;
      border-bottom: 2px solid #165dff;
      padding-bottom: 8px;
    }

    h3 {
      color: #4e5969;
      margin: 16px 0 8px 0;
    }

    p {
      line-height: 1.6;
      margin-bottom: 16px;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }

    code {
      background: #f7f8fa;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Monaco', 'Consolas', monospace;
      color: #e74c3c;
    }

    pre {
      background: #f7f8fa;
      padding: 16px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 16px 0;

      code {
        background: none;
        padding: 0;
        color: #2c3e50;
      }
    }
  }

  .status-demo {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 16px;
  }

  .color-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;

    th, td {
      border: 1px solid #e5e6eb;
      padding: 12px;
      text-align: left;
    }

    th {
      background: #f7f8fa;
      font-weight: 600;
      color: #1d2129;
    }

    td {
      color: #4e5969;
    }

    tr:nth-child(even) {
      background: #fafbfc;
    }
  }

  .test-actions {
    text-align: center;
    margin-top: 32px;
  }
}
</style> 