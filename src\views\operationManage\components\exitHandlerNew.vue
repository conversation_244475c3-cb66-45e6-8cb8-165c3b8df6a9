<template>
    <div class="exit-handler">
        <a-tabs v-model:active-key="activeTab" type="card">
            <!-- 退租申请 Tab -->
            <a-tab-pane key="1" title="退租申请">
                <div class="tab-content">
                    <!-- 使用专门的退租申请展示组件 -->
                    <ContractTerminationDisplay 
                        :terminate-id="exitDetail?.exitInfo?.terminateId"
                        v-if="exitDetail?.exitInfo"
                    />
                    
                    <!-- 如果没有退租信息，显示简单的基本信息 -->
                    <div v-else>
                        <div class="info-section">
                            <section-title title="基本信息" style="margin-bottom: 16px;"/>
                            <a-row :gutter="16">
                                <a-col :span="8">
                                    <div class="info-item">
                                        <span class="label">合同编号:</span>
                                        <span class="value">{{ exitDetail?.exitInfo?.contractNo }}</span>
                                    </div>
                                </a-col>
                                <a-col :span="8">
                                    <div class="info-item">
                                        <span class="label">承租方:</span>
                                        <span class="value">{{ exitDetail?.exitInfo?.customerName }}</span>
                                    </div>
                                </a-col>
                                <a-col :span="8">
                                    <div class="info-item">
                                        <span class="label">退租类型:</span>
                                        <span class="value">{{
                                            getTerminateTypeText(exitDetail?.contractTerminateInfo?.terminateType) }}</span>
                                    </div>
                                </a-col>
                                <a-col :span="8">
                                    <div class="info-item">
                                        <span class="label">退租日期:</span>
                                        <span class="value">{{ exitDetail?.contractTerminateInfo?.terminateDate }}</span>
                                    </div>
                                </a-col>
                            </a-row>
                        </div>

                        <section-title title="退租房源" style="margin-bottom: 16px;"/>
                        <a-table :data="exitDetail?.exitRoomList || []" :columns="roomColumns" :pagination="false"
                            :bordered="{ cell: true }" />
                    </div>
                </div>
            </a-tab-pane>

            <!-- 物业交割 Tab -->
            <a-tab-pane key="2" title="物业交割" v-if="showSettlementTab">
                <div class="tab-content" :class="{'from-terminate': props.fromTerminate}">
                    <PropertyHandover
                        title="物业交割单"
                        :show-header="true"
                        tag-mode="simple"
                        :select-all="selectAll"
                        v-model:search-keyword="searchKeyword"
                        v-model:batch-exit-date="batchExitDate"
                        :exit-room-list="exitRoomList"
                        :active-property-keys="activePropertyKeys"
                        :contract-purpose="exitDetail?.exitInfo?.contractPurpose"
                        :exit-info="exitDetail?.exitInfo"
                        :contract-terminate-info="exitDetail?.contractTerminateInfo"
                        :readonly="props.mode === 'view'"
                        @batch-confirm="handleBatchConfirm"
                        @copy-property-url="handleCopyPropertyUrl"
                        @select-all="handleSelectAll"
                        @batch-set-exit-date="handleBatchSetExitDate"
                        @select-single="handleSelectSingle"
                        @save-room="handleSaveRoom"
                        @cancel-room="handleCancelRoom"
                        @sync-room-data="handleSyncRoomData"
                        @update-penalty-amount="handleUpdatePenaltyAmount"
                    />
                </div>
            </a-tab-pane>

            <!-- 费用结算 Tab -->
            <a-tab-pane key="3" title="费用结算" v-if="showSettlementTab" :disabled="!allRoomsConfirmed">
                <div class="tab-content" :class="{'from-terminate': props.fromTerminate}">
                                    <SettlementSection
                    ref="settlementFormRef"
                    :show-title="false"
                    :exit-info="exitDetail?.exitInfo"
                    :cost-list="exitDetail?.exitCostList || []"
                    :exit-room-list="exitDetail?.exitRoomList || []"
                    :contract-terminate-info="exitDetail?.contractTerminateInfo"
                    :readonly="props.mode === 'view'"
                    @save="handleSaveSettlement"
                />
                </div>
            </a-tab-pane>

            <!-- 物业交割和费用结算 Tab -->
            <a-tab-pane key="4" title="物业交割和费用结算" v-if="showCombinedTab">
                <div class="tab-content" :class="{'from-terminate': props.fromTerminate}">
                    <!-- 物业交割部分 -->
                    <PropertyHandover
                        title="物业交割"
                        ref="propertyHandoverRef"
                        :show-header="false"
                        tag-mode="detailed"
                        :select-all="selectAll"
                        v-model:search-keyword="searchKeyword"
                        v-model:batch-exit-date="batchExitDate"
                        :exit-room-list="exitRoomList"
                        :active-property-keys="activePropertyKeys"
                        :contract-purpose="exitDetail?.exitInfo?.contractPurpose"
                        :contract-terminate-info="exitDetail?.contractTerminateInfo"
                        :readonly="props.mode === 'view'"
                        @batch-confirm="handleBatchConfirm"
                        @copy-property-url="handleCopyPropertyUrl"
                        @select-all="handleSelectAll"
                        @batch-set-exit-date="handleBatchSetExitDate"
                        @select-single="handleSelectSingle"
                        @save-room="handleSaveRoom"
                        @cancel-room="handleCancelRoom"
                        @sync-room-data="handleSyncRoomData"
                        @update-penalty-amount="handleUpdatePenaltyAmount"
                    />

                    <!-- 费用结算部分 -->
                    <SettlementSection
                        title="费用结算"
                        ref="settlementFormRefAndProperty"
                        title-style="margin-top: 32px;"
                        :exit-info="exitDetail?.exitInfo"
                        :cost-list="exitDetail?.exitCostList || []"
                        :exit-room-list="exitDetail?.exitRoomList || []"
                        :contract-terminate-info="exitDetail?.contractTerminateInfo"
                        :readonly="props.mode === 'view'"
                        @save="handleSaveSettlement"
                    />
                </div>
            </a-tab-pane>
        </a-tabs>

        <!-- 底部按钮 -->
        <div class="footer-actions" v-if="!props.fromTerminate">
            <a-space>
                <!-- view 模式下的按钮 -->
                <template v-if="props.mode === 'view'">
                    <a-button @click="handleCancel">取消</a-button>
                    <a-button @click="handlePreviousStep" :disabled="activeTab === '1'">上一步</a-button>
                    <a-button type="primary" @click="handleNextStepView" :disabled="activeTab === '3' || (showCombinedTab && activeTab === '4')">下一步</a-button>
                </template>

                <!-- 正常模式下的按钮 -->
                <template v-else>
                    <a-button @click="handleCancel">取消</a-button>
                    <a-button type="primary" size="large" :disabled="!allRoomsConfirmed" @click="handleNextStep"
                        v-if="props.mode === 'property-only' && activeTab === '2'">
                        进入下一步费用结算
                    </a-button>
                    <a-button type="primary" status="warning" size="large" @click="handleImmediateSettlement"
                        v-if="props.mode === 'property-only' && activeTab === '2'">
                        立即结算
                    </a-button>
                    <a-button type="primary" status="success" @click="handleSaveOnly" v-if="(props.mode === 'property-only' && activeTab === '3')">暂存</a-button>
                    <a-button type="primary" @click="handleSubmitSettlement" v-if="(props.mode === 'property-only' && activeTab === '3')">提交结算单</a-button>

                    <a-button type="primary" status="success" @click="handleSaveOnlyAndSettlement" v-if="props.mode === 'property-and-settlement' && activeTab === '4'">暂存</a-button>
                    <a-button type="primary" @click="handleSubmitSettlementAndProperty" v-if="props.mode === 'property-and-settlement' && activeTab === '4'">提交结算单</a-button>
                </template>
            </a-space>
        </div>

        <!-- 物业确认单二维码弹窗 -->
        <a-modal v-model:visible="qrcodeVisible" title="物业确认单" :footer="false" @cancel="handleQrcodeCancel" :width="500">
            <div class="qrcode-container">
                <div class="qrcode-header">
                    <!-- <div class="title">扫码进入物业确认</div> -->
                    <div class="exit-info">
                        <div class="info-item">
                            <span class="label">合同号：</span>
                            <span class="value">{{ exitDetail?.exitInfo?.contractNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">承租方：</span>
                            <span class="value">{{ exitDetail?.exitInfo?.customerName }}</span>
                        </div>
                        <!-- <div class="info-item">
                            <span class="label">房间：</span>
                            <span class="value">{{ currentQrcodeRoom?.buildingName }} {{ currentQrcodeRoom?.roomName }}</span>
                        </div> -->
                        <!-- <div class="info-item">
                            <span class="label">保存时间：</span>
                            <span class="value">{{ new Date().toLocaleString() }}</span>
                        </div> -->
                    </div>
                </div>

                <div class="qrcode-display">
                    <QRCode
                        :value="qrcodeUrl"
                        :size="200"
                        :show-placeholder="true"
                        :show-download="!!qrcodeUrl"
                        placeholder-text="生成确认单链接"
                        @generated="handleQRCodeGenerated"
                        @error="handleQRCodeError"
                    />
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
    getExitDetail,
    saveExitRoom,
    saveExitSettlement,
    batchUpdateExitRoom,
    copyExitPropertyUrl
} from '@/api/operationManage'
import { getContractTerminateExitDetail } from '@/api/contractTerminate'
import type {
    ExitDetailVo,
    ExitRoom,
    ExitRoomAddDTO,
    ExitAddDTO,
    ExitRoomBatchUpdateDTO
} from '@/types/exit'
import sectionTitle from '@/components/sectionTitle/index.vue'

import QRCode from '@/components/QRCode/index.vue'
import PropertyHandover from './PropertyHandover.vue'
import SettlementSection from './SettlementSection.vue'
import ContractTerminationDisplay from './ContractTerminationDisplay.vue'
import { exportLedgerList } from '@/api/asset/projectLedger'
import { useDictSync } from '@/utils/dict'

// Props定义
const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    },
    mode: {
        type: String,
        default: 'property-only' // 'property-only' 或 'property-and-settlement' 或 'view'
    },
    fromTerminate: {
        type: Boolean,
        default: false // 是否来自合同退租
    }
})

const emit = defineEmits(['cancel', 'save', 'refresh', 'modeChange'])

// 状态管理
const activeTab = ref('1')
const showSettlementTab = computed(() => {
    if (props.mode === 'view') {
        // view模式下根据数据的processType来决定
        return exitDetail.value?.exitInfo?.processType === 1
    }
    return props.mode === 'property-only'
})
const showCombinedTab = computed(() => {
    if (props.mode === 'view') {
        // view模式下根据数据的processType来决定
        return exitDetail.value?.exitInfo?.processType === 2
    }
    return props.mode === 'property-and-settlement'
})
const loading = ref(false)

// 组件引用
const settlementFormRef = ref()
const propertyHandoverRef = ref()
const settlementFormRefAndProperty = ref()

// 出场详情数据
const exitDetail = ref<ExitDetailVo | null>(null)
const exitRoomList = ref<(ExitRoom & { selected?: boolean })[]>([])

// 物业交割相关
const activePropertyKeys = ref<number[]>([])
const selectAll = ref(false)
const searchKeyword = ref('')
const batchExitDate = ref<string>('')

// 二维码相关
const qrcodeVisible = ref(false)
const qrcodeUrl = ref('')
const currentQrcodeRoom = ref<ExitRoom | null>(null)

// 字典数据
interface DictData {
    dictCode: string;
    dictLabel: string;
    dictValue: string;
    dictSort: number;
    parentCode?: string;
    childList?: DictData[];
    [key: string]: any;
}

const propertyTypeOptions = ref<DictData[]>([])

// 初始化数据
const initData = async () => {
    let mode = props.mode
    if (mode === 'property-only') {
        activeTab.value = '2'
    } else if (mode === 'property-and-settlement') {
        activeTab.value = '4'
    }else {

    }
    // // 优先检查传入的数据是否已经包含完整的出场详情
    // console.log('使用传入的完整出场详情数据:--', props.data)

    // if (props.data?.exitInfo && props.data?.exitRoomList && props.data?.exitRoomList?.length > 0) {
    //     // 如果传入的数据已经包含完整的出场详情，直接使用
    //     console.log('使用传入的完整出场详情数据:', props.data)
    //     exitDetail.value = props.data as ExitDetailVo
    //     initExitRoomList()
    //     return
    // }

    // // 如果是来自合同退租且有基础的exitInfo但缺少房间数据，需要获取完整的退租数据
    // if (props.fromTerminate && props.data?.exitInfo && props.data?.terminateId) {
    //     try {
    //         console.log('从合同退租获取完整退租数据:', props.data.terminateId)
    //         loading.value = true
    //         const res = await getContractTerminateExitDetail(props.data.terminateId, true)
    //         if (res && res.code === 200) {
    //             const terminateData = res.data
    //             // 构建完整的出场详情数据
    //             exitDetail.value = {
    //                 exitInfo: {
    //                     ...props.data.exitInfo,
    //                     // 从合同数据中补充缺失的字段
    //                     projectId: terminateData.contract?.projectId || '',
    //                     contractNo: terminateData.contract?.contractNo || '',
    //                     customerId: terminateData.contract?.customer?.customerId || '',
    //                     customerName: terminateData.contract?.customer?.customerName || '',
    //                     contractPurpose: terminateData.contract?.contractPurpose
    //                 },
    //                 contractTerminateInfo: terminateData,
    //                 exitRoomList: [], // 这个需要通过其他方式获取
    //                 exitCostList: [] // 这个需要通过其他方式获取
    //             }
    //             console.log('构建的完整出场详情:', exitDetail.value)
    //             return
    //         }
    //     } catch (error) {
    //         console.error('获取退租详情失败:', error)
    //     } finally {
    //         loading.value = false
    //     }
    // }

    // if (!props.data?.id) {
    //     // 如果没有ID，说明是新建，使用传入的数据
    //     // 根据当前模式设置 processType
    //     const currentProcessType = props.mode === 'property-and-settlement' ? 2 : 1

    //     exitDetail.value = {
    //         exitInfo: {
    //             ...props.data,
    //             processType: currentProcessType
    //         } as any, // 临时使用 any 类型
    //         exitRoomList: [],
    //         exitCostList: []
    //     }
    //     return
    // }

    try {
        loading.value = true
        let res

        // 根据是否来自合同退租选择不同的接口
        if (props.fromTerminate) {
            // 来自合同退租，调用退租出场详情接口
            res = props.data
            // res = await getContractTerminateExitDetail(props.data.id, true)
        } else {
            // 普通出场，调用出场详情接口
            res = await getExitDetail(props.data.id)
        }

        if (res && res.code === 200) {
            exitDetail.value = res.data
            initExitRoomList()
            if (mode === 'property-only') {
                // activeTab.value = '2'
                settlementFormRef.value.open(exitDetail.value?.exitCostList, exitDetail.value)
            } else if (mode === 'property-and-settlement') {
                // activeTab.value = '4'
                settlementFormRefAndProperty.value.open(exitDetail.value?.exitCostList, exitDetail.value)
            }else {

            }
        }
    } catch (error) {
        console.error('获取出场详情失败', error)
        // Message.error('获取出场详情失败')
    } finally {
        loading.value = false
    }
}

// 获取物业类型字典
const getPropertyTypeDicts = async () => {
    try {
        console.log('开始获取物业类型字典数据...')
        const dictData = await useDictSync('property_type')
        console.log('获取到的字典数据:', dictData)
        if (dictData.property_type) {
            // 处理树形结构数据
            const dictList = dictData.property_type as DictData[]
            console.log('原始字典列表:', dictList)
            // 组织成树形结构
            const treeData = dictList.filter(item => !item.parentCode)
            treeData.forEach(parent => {
                parent.childList = dictList.filter(child => child.parentCode === parent.dictCode)
            })
            propertyTypeOptions.value = treeData
            console.log('处理后的物业类型字典:', propertyTypeOptions.value)
        }
    } catch (error) {
        console.error('获取物业类型字典数据失败:', error)
    }
}

// 根据字典值获取物业类型标签
const getPropertyTypeLabel = (propertyType: string): string => {
    console.log('propertyType:---', propertyType, typeof propertyType)
    console.log('propertyTypeOptions.value:---', propertyTypeOptions.value)
    if (!propertyType || !propertyTypeOptions.value) return propertyType || ''

    const findLabel = (options: DictData[], value: string): string => {
        for (const option of options) {
            // 确保类型匹配 - 都转换为字符串进行比较
            const dictValueStr = String(option.dictValue)
            const valueStr = String(value)
            console.log('对比字典项:', dictValueStr, '===', valueStr, option.dictLabel)
            if (dictValueStr === valueStr) {
                console.log('找到匹配项:', option.dictLabel)
                return option.dictLabel || ''
            }
            if (option.childList && option.childList.length > 0) {
                const childLabel = findLabel(option.childList, value)
                if (childLabel) return childLabel
            }
        }
        return ''
    }

    const result = findLabel(propertyTypeOptions.value, propertyType) || propertyType
    console.log('最终返回结果:', result)
    return result
}

// 房源列表表格列定义
const roomColumns = [
    {
        title: '房源名称',
        dataIndex: 'roomName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '楼栋',
        dataIndex: 'buildingName',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '地块',
        dataIndex: 'parcelName',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '物业类型',
        dataIndex: 'propertyType',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: { record: any }) => {
            return getPropertyTypeLabel(record.propertyType)
        }
    }
]

// 生命周期
onMounted(() => {
    getPropertyTypeDicts()
    initData()
})

// 检查所有房间是否都确认通过
const allRoomsConfirmed = computed(() => {
    if (!exitRoomList.value || exitRoomList.value.length === 0) {
        return false
    }

    // 物业交割并结算模式下，不需要物业确认(isFinanceConfirmed)
    if (props.mode === 'property-and-settlement') {
        return exitRoomList.value.every(room =>
            room.isBusinessConfirmed &&
            room.isEngineeringConfirmed
        )
    }

    // 先物业交割，后续结算模式下，需要三方确认
    return exitRoomList.value.every(room =>
        room.isBusinessConfirmed &&
        room.isEngineeringConfirmed &&
        room.isFinanceConfirmed
    )
})

// 监听模式变化
// watch(() => props.mode, (newMode) => {
//     if (newMode === 'property-only') {
//         activeTab.value = '2'
//     } else if (newMode === 'property-and-settlement') {
//         activeTab.value = '4'
//     }
// }, { immediate: true })

// 监视 allRoomsConfirmed 的变化
// watch(() => allRoomsConfirmed.value, (newVal) => {
//     console.log('allRoomsConfirmed changed:', newVal)
//     console.log('exitData:', exitDetail.value?.exitRoomList)
// }, { immediate: true })



// 初始化房源列表
const initExitRoomList = () => {
    if (exitDetail.value?.exitRoomList) {
        console.log('原始房源列表数据:', exitDetail.value.exitRoomList)
        exitRoomList.value = exitDetail.value.exitRoomList.map(room => ({
            ...room,
            selected: false,
            // 确保 exitRoomAssetsList 存在
            exitRoomAssetsList: room.exitRoomAssetsList || []
        }))
        console.log('处理后的房源列表:', exitRoomList.value)

        // 同步更新全选状态
        selectAll.value = exitRoomList.value.length > 0 && exitRoomList.value.every(r => r.selected)
        console.log('全选状态同步更新为:', selectAll.value)

        // 默认展开所有房源
        activePropertyKeys.value = exitRoomList.value.map((_, index) => index)
    }
}

// 刷新数据 - 智能选择刷新策略
const refreshData = async () => {
    try {
        loading.value = true
        let res

        // 如果是来自合同退租，优先调用合同退租的详情接口
        if (props.fromTerminate && (props.data?.terminateId || exitDetail.value?.exitInfo?.terminateId)) {
            const terminateId = props.data?.terminateId || exitDetail.value?.exitInfo?.terminateId
            console.log('来自合同退租，刷新退租出场详情:', terminateId)
            res = await getContractTerminateExitDetail(terminateId, true)

            if (res && res.code === 200) {
                const terminateData = res.data
                // 保持原有的exitInfo结构，更新数据
                if (exitDetail.value) {
                    // 更新合同退租信息
                    exitDetail.value.contractTerminateInfo = terminateData

                    // 由于类型不兼容，这里使用类型断言来临时处理
                    // 在实际的生产环境中，应该创建适当的数据转换函数
                    exitDetail.value.exitRoomList = (terminateData.roomList || []) as any
                    exitDetail.value.exitCostList = (terminateData.costList || []) as any
                } else {
                    // 如果exitDetail为空，构建完整数据
                    exitDetail.value = {
                        exitInfo: {
                            ...props.data?.exitInfo,
                            projectId: terminateData.contract?.projectId || '',
                            contractNo: terminateData.contract?.contractNo || '',
                            customerId: terminateData.contract?.customer?.customerId || '',
                            customerName: terminateData.contract?.customer?.customerName || '',
                            contractPurpose: terminateData.contract?.contractPurpose
                        },
                        contractTerminateInfo: terminateData,
                        exitRoomList: (terminateData.roomList || []) as any,
                        exitCostList: (terminateData.costList || []) as any
                    }
                }
                initExitRoomList()
                console.log('合同退租数据刷新成功')
                return
            }
        }

        // 如果不是来自合同退租，或者合同退租接口调用失败，使用普通出场详情接口
        if (props.data?.id || exitDetail.value?.exitInfo?.id) {
            const exitId = props.data?.id || exitDetail.value?.exitInfo?.id
            console.log('刷新普通出场详情:', exitId)
            res = await getExitDetail(exitId)

            if (res && res.code === 200) {
                exitDetail.value = res.data
                initExitRoomList()
                console.log('普通出场数据刷新成功')
                return
            }
        }

        console.warn('无法确定刷新数据的方式，使用initData作为后备方案')
        await initData()

    } catch (error) {
        console.error('刷新数据失败:', error)
        // 如果刷新失败，尝试使用原来的initData方法
        await initData()
    } finally {
        loading.value = false
    }
}

// 获取退租类型文本
const getTerminateTypeText = (type?: number) => {
// 退租类型:0-到期退租,1-提前退租
    switch (type) {
        case 0:
            return '到期退租'
        case 1:
            return '提前退租'
        default:
            return '未知'
    }
}

// 全选/取消全选
const handleSelectAll = (checked: boolean) => {
    console.log('全选状态变更:', checked)
    selectAll.value = checked
    exitRoomList.value.forEach(room => {
        room.selected = checked
    })
    console.log('全选操作完成，当前选中房源数量:', exitRoomList.value.filter(r => r.selected).length)
}

// 单选房源
const handleSelectSingle = (room: ExitRoom & { selected?: boolean }, checked: boolean) => {
    console.log('单选房源状态变更:', room.id, checked, exitRoomList.value, room)
    room.selected = checked
    // 更新全选状态
    const allSelected = exitRoomList.value.every(r => r.selected)
    const someSelected = exitRoomList.value.some(r => r.selected)
    selectAll.value = allSelected
    console.log('单选操作完成，全选状态:', selectAll.value, '总数:', exitRoomList.value.length, '选中数:', exitRoomList.value.filter(r => r.selected).length)
}

// 商服-批量确认
const handleBatchConfirm = async () => {
    const selectedRooms = exitRoomList.value.filter(room => room.selected)
    if (!batchExitDate.value) {
        Message.warning('请选择出场日期')
        return
    }
    if (selectedRooms.length === 0) {
        Message.warning('请先选择要确认的房源')
        return
    }

    try {
        let isBusinessConfirmed = false
        selectedRooms.forEach(room => {
            if (room.isBusinessConfirmed) {
                isBusinessConfirmed = true
            }
        })
        if (isBusinessConfirmed) {
            Message.warning('存在商服已确认的房源，无法批量确认')
            return
        }
        // const exitDate = exitDetail.value?.exitInfo?.exitDate
        const exitRoomIds = selectedRooms.map(room => room.id).filter(id => id) as string[]
        const params: ExitRoomBatchUpdateDTO = {
            exitDate: batchExitDate.value,
            exitRoomIds,
            type: 1 // 批量确认
        }

        const res = await batchUpdateExitRoom(params)
        if (res && res.code === 200) {
            Message.success('批量确认成功')
            // 更新状态
            selectedRooms.forEach(room => {
                room.isBusinessConfirmed = true
            })
        }
    } catch (error) {
        console.error('批量确认失败', error)
        // Message.error('批量确认失败')
    }
}

// 批量设置出场日期
const handleBatchSetExitDate = async () => {
    if (!batchExitDate.value) {
        Message.warning('请选择出场日期')
        return
    }

    const selectedRooms = exitRoomList.value.filter(room => room.selected)
    if (selectedRooms.length === 0) {
        Message.warning('请先选择要设置的房源')
        return
    }

    try {
        let isBusinessConfirmed = false
        selectedRooms.forEach(room => {
            if (room.isBusinessConfirmed) {
                isBusinessConfirmed = true
            }
        })
        if (isBusinessConfirmed) {
            Message.warning('存在商服已确认的房源，无法批量设置出场日期')
            return
        }
        const exitRoomIds = selectedRooms.map(room => room.id).filter(id => id) as string[]
        const params: ExitRoomBatchUpdateDTO = {
            exitRoomIds,
            type: 2, // 批量设置出场日期
            exitDate: batchExitDate.value
        }

        const res = await batchUpdateExitRoom(params)
        if (res && res.code === 200) {
            Message.success('批量设置出场日期成功')
            // 更新状态
            selectedRooms.forEach(room => {
                room.exitDate = batchExitDate.value
            })
        }
    } catch (error) {
        console.error('批量设置出场日期失败', error)
        // Message.error('批量设置出场日期失败')
    }
}

// 复制物业确认单地址
const handleCopyPropertyUrl = async () => {
    if (!exitDetail.value?.exitInfo?.id) {
        Message.warning('出场单ID不存在')
        return
    }

    try {
        const res = await copyExitPropertyUrl(exitDetail.value.exitInfo.id)
        if (res && res.code === 200) {
            // Message.success('复制物业确认单地址成功')
            showQrcode()
        }
    } catch (error) {
        console.error('复制物业确认单地址失败', error)
        // Message.error('复制物业确认单地址失败')
    }
}

// 保存房间信息
const handleSaveRoom = async (roomData: ExitRoomAddDTO) => {
    console.log('roomData', roomData)
    try {
        // 在重新获取数据前，保存当前所有房间的临时状态
        // const roomTempStates = new Map()
        // exitRoomList.value.forEach(room => {
        //     roomTempStates.set(room.id, {
        //         doorWindowStatus: room.doorWindowStatus,
        //         doorWindowPenalty: room.doorWindowPenalty,
        //         keyHandoverStatus: room.keyHandoverStatus,
        //         keyPenalty: room.keyPenalty,
        //         cleaningStatus: room.cleaningStatus,
        //         cleaningPenalty: room.cleaningPenalty,
        //         exitRoomAssetsList: room.exitRoomAssetsList ? [...room.exitRoomAssetsList] : []
        //     })
        // })

        const res = await saveExitRoom(roomData)
        if (res && res.code === 200) {
            Message.success('保存房间信息成功')
            // 收起当前展开的房间信息
            const roomIndex = exitRoomList.value.findIndex(room => room.id === roomData.id)
            if (roomIndex !== -1) {
                const keyIndex = activePropertyKeys.value.indexOf(roomIndex)
                if (keyIndex !== -1) {
                    activePropertyKeys.value.splice(keyIndex, 1)
                }
            }
            if (roomData.isSubmit) {
                exitRoomList.value[roomIndex].isBusinessConfirmed = true
                exitRoomList.value[roomIndex].exitDate = roomData.exitDate
                // await initData()
            }

            // 重新调用详情接口更新状态
            // await initData()

            // 恢复其他房间的临时状态（除了刚保存的房间）
            // const updatedRooms = [...exitRoomList.value]
            // let hasStateChanged = false

            // updatedRooms.forEach(room => {
            //     if (room.id !== roomData.id && roomTempStates.has(room.id)) {
            //         const tempState = roomTempStates.get(room.id)

            //         // 检查是否有状态需要恢复
            //         if (room.doorWindowStatus !== tempState.doorWindowStatus ||
            //             room.doorWindowPenalty !== tempState.doorWindowPenalty ||
            //             room.keyHandoverStatus !== tempState.keyHandoverStatus ||
            //             room.keyPenalty !== tempState.keyPenalty ||
            //             room.cleaningStatus !== tempState.cleaningStatus ||
            //             room.cleaningPenalty !== tempState.cleaningPenalty) {

            //             room.doorWindowStatus = tempState.doorWindowStatus
            //             room.doorWindowPenalty = tempState.doorWindowPenalty
            //             room.keyHandoverStatus = tempState.keyHandoverStatus
            //             room.keyPenalty = tempState.keyPenalty
            //             room.cleaningStatus = tempState.cleaningStatus
            //             room.cleaningPenalty = tempState.cleaningPenalty
            //             hasStateChanged = true
            //         }

            //         // 恢复配套资产的临时状态
            //         if (tempState.exitRoomAssetsList && tempState.exitRoomAssetsList.length > 0) {
            //             const savedAssets = room.exitRoomAssetsList || []
            //             const tempAssets = tempState.exitRoomAssetsList || []

            //             // 如果临时状态有更多的配套数据，则保留
            //             if (tempAssets.length > savedAssets.length) {
            //                 room.exitRoomAssetsList = [...tempAssets]
            //                 hasStateChanged = true
            //             }
            //         }
            //     }
            // })

            // // 如果有状态变化，触发响应式更新
            // if (hasStateChanged) {
            //     exitRoomList.value = updatedRooms
            // }

            // 通知父组件数据已更新
            emit('refresh')
        }
    } catch (error) {
        console.error('保存房间信息失败', error)
        // Message.error('保存房间信息失败')
    }
}

// 取消房间编辑
const handleCancelRoom = (roomId: string) => {
    // 重置房间数据
    initData()
}

// 同步房间数据（实时更新房间表单数据到父组件）
const handleSyncRoomData = (updatedRoom: ExitRoom) => {
    // 更新房间列表中对应房间的数据
    const index = exitRoomList.value.findIndex(room => room.id === updatedRoom.id)
    if (index !== -1) {
        exitRoomList.value[index] = {
            ...exitRoomList.value[index],
            ...updatedRoom
        }
    }
}

// 处理房间赔偿金额变化
const handleUpdatePenaltyAmount = (currentRoomData: any, list: any) => {
    // console.log('handleUpdatePenaltyAmount', currentRoomData)
    if (props.mode === 'property-only') {
        settlementFormRef.value.updatePenaltyAmount(currentRoomData, list)
    }else if (props.mode === 'property-and-settlement') {
        settlementFormRefAndProperty.value.updatePenaltyAmount(currentRoomData, list)
    }
}
// 处理费用结算数据
const handleUpdateSettlementData = (currentProcessType: any, settlementData: any) => {
        return {
            id: exitDetail.value?.exitInfo?.id || '',
            projectId: exitDetail.value?.exitInfo?.projectId || '',
            contractId: exitDetail.value?.exitInfo?.contractId || '',
            contractNo: exitDetail.value?.exitInfo?.contractNo || '',
            contractUnionId: exitDetail.value?.exitInfo?.contractUnionId || '',
            terminateId: exitDetail.value?.exitInfo?.terminateId || '',
            refundId: exitDetail.value?.exitInfo?.refundId || '',
            customerId: exitDetail.value?.exitInfo?.customerId || '',
            customerName: exitDetail.value?.exitInfo?.customerName || '',
            processType: currentProcessType,
            progressStatus: exitDetail.value?.exitInfo?.progressStatus || 1,
            isDiscount: settlementData.isDiscount || false,
            discountAmount: settlementData.discountAmount || 0,
            discountReason: settlementData.discountReason || '',
            finalAmount: settlementData.finalAmount || 0,
            refundProcessType: settlementData.refundProcessType || 1,
            payeeName: settlementData.payeeName || '',
            payeeAccount: settlementData.payeeAccount || '',
            bankName: settlementData.bankName || '',
            licenseStatus: settlementData.licenseStatus || 1,
            taxCertStatus: settlementData.taxCertStatus || 1,
            refundApplyType: settlementData.refundApplyType || 1,
            signType: settlementData.signType || 1,
            signAttachments: settlementData.signAttachments || null,
            exitCostList: settlementData.exitCostList || [],
            isSubmit: settlementData.isSubmit || false,
            isDel: false
        }
}

// 保存费用结算
const handleSaveSettlement = async (settlementData: ExitAddDTO) => {
    try {
        // 根据接口文档构造保存结算单的参数
        // 根据当前模式设置 processType

        const currentProcessType = props.mode === 'property-and-settlement' ? 2 : 1
        const saveData = handleUpdateSettlementData(currentProcessType, settlementData)



        const res = await saveExitSettlement(saveData)
        if (res && res.code === 200) {
            Message.success(settlementData.isSubmit ? '提交结算单成功' : '保存结算单成功')
            // 无论是提交还是暂存，都需要通知父组件刷新列表并关闭弹窗
            emit('save', saveData)
        }
    } catch (error) {
        console.error('保存费用结算失败', error)
        // Message.error('保存费用结算失败')
    }
}

// 取消
const handleCancel = () => {
    emit('cancel')
}

// 保存
const handleSave = () => {
    // 根据当前模式设置 processType
    const currentProcessType = props.mode === 'property-and-settlement' ? 2 : 1

    const formData = {
        exitInfo: {
            ...exitDetail.value?.exitInfo,
            processType: currentProcessType
        },
        exitRoomList: exitRoomList.value,
        mode: props.mode,
        isSubmit: false
    }
    emit('save', formData)
}

// 进入下一步费用结算
const handleNextStep = () => {
    // 检查所有房间是否都确认通过
    const allConfirmed = exitRoomList.value.every(room =>
        room.isBusinessConfirmed &&
        room.isEngineeringConfirmed &&
        room.isFinanceConfirmed
    )

    if (!allConfirmed) {
        Message.warning('请确保所有交割单都已确认通过')
        return
    }

    // 切换到费用结算标签页
    activeTab.value = '3'
}

// 提交
const handleSubmit = () => {
    // 验证数据
    if (activeTab.value === '2' || activeTab.value === '4') {
        // 物业交割验证
        let unconfirmedRooms

        // 物业交割并结算模式下，不需要物业确认(isFinanceConfirmed)
        if (props.mode === 'property-and-settlement') {
            unconfirmedRooms = exitRoomList.value.filter(room =>
                !room.isBusinessConfirmed || !room.isEngineeringConfirmed
            )
        } else {
            // 先物业交割，后续结算模式下，需要三方确认
            unconfirmedRooms = exitRoomList.value.filter(room =>
                !room.isBusinessConfirmed || !room.isEngineeringConfirmed || !room.isFinanceConfirmed
            )
        }

        if (unconfirmedRooms.length > 0) {
            const confirmText = props.mode === 'property-and-settlement'
                ? '有未确认的物业交割单（需商服和工程确认），请确认后再提交'
                : '有未确认的物业交割单，请确认后再提交'
            Message.warning(confirmText)
            return
        }
    }

    // 根据当前模式设置 processType
    const currentProcessType = props.mode === 'property-and-settlement' ? 2 : 1

    const formData = {
        exitInfo: {
            ...exitDetail.value?.exitInfo,
            processType: currentProcessType
        },
        exitRoomList: exitRoomList.value,
        mode: props.mode,
        isSubmit: true
    }
    emit('save', formData)
}

// 暂存结算单
const handleSaveOnly = async () => {
    // 调用结算表单的暂存方法
    if (settlementFormRef.value && settlementFormRef.value.handleSave) {
        let res = await settlementFormRef.value.handleSave(false) // false表示暂存
        console.log('res', res)
        if (res) {
            emit('cancel') // 暂存后直接关闭抽屉
        }
    } else {
        Message.warning('结算表单未准备就绪')
    }
}

// 提交结算单
const handleSubmitSettlement = () => {
    // 调用结算表单的提交方法
    if (settlementFormRef.value && settlementFormRef.value.handleSave) {
        settlementFormRef.value.handleSave(true) // true表示提交
    } else {
        Message.warning('结算表单未准备就绪')
    }
}

// 暂存结算单
const handleSaveOnlyAndSettlement = async () => {
    // 调用结算表单的暂存方法
    if (settlementFormRefAndProperty.value && settlementFormRefAndProperty.value.handleSave) {
        let isSuccess = await settlementFormRefAndProperty.value.handleSave(false) // false表示暂存
        if(isSuccess) {
            emit('cancel') // 暂存后直接关闭抽屉
        }
    } else {
        Message.warning('结算表单未准备就绪')
    }
}

// 提交结算单
const handleSubmitSettlementAndProperty = () => {
    // 调用结算表单的提交方法
    if (settlementFormRefAndProperty.value && settlementFormRefAndProperty.value.handleSave) {
        settlementFormRefAndProperty.value.handleSave(true) // true表示提交
    } else {
        Message.warning('结算表单未准备就绪')
    }
}

// 显示二维码弹窗
const showQrcode = () => {
    // currentQrcodeRoom.value = room
    // 生成物业确认单链接
    const baseUrl = import.meta.env.VITE_APP_BASE_URL
    const exitId = exitDetail.value?.exitInfo?.id
    console.log('exitId', exitId)
    // const roomId = room.id
    // const contractNo = exitDetail.value?.exitInfo?.contractNo

    qrcodeUrl.value = `${baseUrl}/property-handover-detail?exitId=${exitId}`
    qrcodeVisible.value = true
}

// 关闭二维码弹窗
const handleQrcodeCancel = () => {
    qrcodeVisible.value = false
    currentQrcodeRoom.value = null
    qrcodeUrl.value = ''
}

// 处理二维码生成成功
const handleQRCodeGenerated = (dataUrl: string) => {
    console.log('二维码生成成功:', dataUrl)
}

// 处理二维码生成失败
const handleQRCodeError = (error: Error) => {
    // Message.error('二维码生成失败: ' + error.message)
    console.error('二维码生成失败:', error)
}

// 设置活跃tab
const setActiveTab = (tab: string) => {
    activeTab.value = tab
}



// 立即结算
const handleImmediateSettlement = () => {
    // // 检查所有房间是否都确认通过
    // const allConfirmed = exitRoomList.value.every(room =>
    //     room.isBusinessConfirmed &&
    //     room.isEngineeringConfirmed &&
    //     room.isFinanceConfirmed
    // )

    // if (!allConfirmed) {
    //     Message.warning('请确保所有交割单都已确认通过')
    //     return
    // }

    // 通知父组件切换模式
    emit('modeChange', 'property-and-settlement')
    // 切换到物业交割和费用结算标签页
    nextTick(() => {
        activeTab.value = '4'
    })
}

// view模式下的上一步
const handlePreviousStep = () => {
    const currentTabNum = parseInt(activeTab.value)
    if (currentTabNum > 1) {
        if (props.mode === 'view') {
            // 在view模式下，根据数据决定可见的tab
            if (currentTabNum === 3 && showSettlementTab.value) {
                activeTab.value = '2' // 从费用结算回到物业交割
            } else if (currentTabNum === 4 && showCombinedTab.value) {
                activeTab.value = '1' // 从物业交割和费用结算回到退租申请
            } else if (currentTabNum === 2) {
                activeTab.value = '1' // 从物业交割回到退租申请
            }
        }
    }
}

// view模式下的下一步
const handleNextStepView = () => {
    const currentTabNum = parseInt(activeTab.value)
    if (props.mode === 'view') {
        if (currentTabNum === 1) {
            // 从退租申请到物业交割或物业交割和费用结算
            if (showCombinedTab.value) {
                activeTab.value = '4'
            } else if (showSettlementTab.value) {
                activeTab.value = '2'
            }
        } else if (currentTabNum === 2 && showSettlementTab.value) {
            // 从物业交割到费用结算
            activeTab.value = '3'
        }
    }
}

/**
 * 房间 类型 是 商铺或综合体
 * 显示：固定资产、设备设施评估情况
*/

// 暴露方法给父组件
defineExpose({
    setActiveTab,
    settlementFormRef,
    settlementFormRefAndProperty,
    propertyHandoverRef,
    refreshData,
    initData,
    handleUpdateSettlementData
})
</script>

<style scoped>
.exit-handler {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tab-content {
    height: calc(100vh - 180px);
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.info-section {
    margin-bottom: 24px;
}

.info-item {
    margin-bottom: 16px;
    display: flex;
}

.label {
    color: #86909c;
    margin-right: 8px;
    min-width: 80px;
    text-align: right;
}

.value {
    color: #1d2129;
    flex: 1;
    text-align: left;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.batch-operations {
    margin-bottom: 16px;
    padding: 16px;
    background: #f7f8fa;
    border-radius: 4px;
}

.property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.property-header .left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.property-header .right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date {
    color: #86909c;
    font-size: 14px;
    margin-left: 16px;
}

.footer-actions {
    display: flex;
    justify-content: flex-end;
    padding: 16px;
    border-top: 1px solid #e5e6eb;
    background: #fff;
}

.next-step-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 32px;
    padding: 24px;
    background: #f7f8fa;
    border-radius: 8px;
    border: 1px solid #e5e6eb;
}

.tip-text {
    margin-top: 12px;
    margin-bottom: 0;
    color: #86909c;
    font-size: 14px;
    text-align: center;
}

.qrcode-container {
    padding: 24px;
    text-align: center;

    .qrcode-header {
        margin-bottom: 24px;

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #1d2129;
        }

        .exit-info {
            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                padding: 0 16px;

                .label {
                    color: #86909c;
                    font-size: 14px;
                }

                .value {
                    color: #1d2129;
                    font-size: 14px;
                    font-weight: 500;
                }
            }
        }
    }

    .qrcode-display {
        margin-bottom: 24px;
        display: flex;
        justify-content: center;
    }
}

.from-terminate {
    height: calc(100vh - 200px);
}
</style>
