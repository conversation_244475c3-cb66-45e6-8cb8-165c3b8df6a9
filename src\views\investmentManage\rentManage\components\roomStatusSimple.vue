<template>
    <div class="room-status-simple" ref="roomStatusContainer">
        <!-- 状态切换按钮区 -->
        <div class="status-filters">
            <div class="status-buttons-group">
                <div class="status-buttons-main">
                    <div class="status-button all" :class="{ active: queryForm.roomStatus === undefined }"
                        @click="setStatusFilter(undefined)">全部 {{ roomStatusCounts.all }}
                    </div>
                    <div class="status-button vacant" :class="{ active: queryForm.roomStatus === 1 }"
                        @click="setStatusFilter(1)">空置 {{ roomStatusCounts.vacant }}</div>
                    <div class="status-button in-progress" :class="{ active: queryForm.roomStatus === 2 }"
                        @click="setStatusFilter(2)">在租 {{ roomStatusCounts.inProgress }}</div>
                    <div class="status-button reserved" :class="{ active: queryForm.roomStatus === 3 }"
                        @click="setStatusFilter(3)">待生效/签约中/已预订 {{ roomStatusCounts.reserved }}</div>
                    <div class="status-button unavailable" :class="{ active: queryForm.roomStatus === 4 }"
                        @click="setStatusFilter(4)">不可招商 {{ roomStatusCounts.unavailable }}</div>
                </div>
                <div class="status-buttons-divider"></div>
                <div class="status-buttons-secondary">
                    <div class="status-button no-bg">
                        <span class="triangle-indicator blue"></span>
                        自用
                    </div>
                    <div class="status-button no-bg">
                        <span class="triangle-indicator purple"></span>
                        未出场
                    </div>
                </div>
                <div class="filter-item date-range">
                    <span class="label">远期房态日期:</span>
                    <a-date-picker 
                        v-model="queryForm.diagramDate" 
                        style="width: 130px" 
                        class="gray-bg-select"
                        format="YYYY-MM-DD"
                        :disabledDate="(current: any) => dayjs(current).isBefore(dayjs(), 'day')"
                    />
                </div>
            </div>
        </div>

        <!-- 筛选区 -->
        <div class="filter-bar-wrapper">
            <div class="filter-bar">
                <div class="filter-group">
                    <div class="filter-item">
                        <project-tree-select
                            v-model="queryForm.projectId"
                            style="width: 220px"
                            placeholder="请选择项目"
                            class="gray-bg-select"
                            @change="handleProjectChange"
                        />
                    </div>
                    <div class="filter-item">
                        <a-select v-model="queryForm.parcelId" placeholder="请选择地块" style="width: 120px"
                            class="gray-bg-select" @change="handleParcelChange" :loading="parcelLoading">
                            <a-option v-for="parcel in parcelList" :key="parcel.id" :value="parcel.id">
                                {{ parcel.parcelName }}
                            </a-option>
                        </a-select>
                    </div>
                    <div class="filter-item">
                        <a-select v-model="queryForm.buildingId" placeholder="请选择楼栋" style="width: 120px"
                            class="gray-bg-select" @change="handleBuildingChange" :loading="buildingLoading">
                            <a-option v-for="building in buildingList" :key="building.id" :value="building.id">
                                {{ building.buildingName }}
                            </a-option>
                        </a-select>
                    </div>
                    <div class="filter-item">
                        <a-select v-model="queryForm.floorId" placeholder="全部楼层" style="width: 120px"
                            class="gray-bg-select" @change="handleFloorChange" :loading="floorLoading">
                            <a-option value="">全部楼层</a-option>
                            <a-option v-for="floor in floorList" :key="floor.id" :value="floor.id">
                                {{ floor.floorName }}
                            </a-option>
                        </a-select>
                    </div>
                </div>
                <div class="filter-group">
                    <div class="filter-item use-type">
                        <span class="label">用途:</span>
                        <a-checkbox-group v-model="queryForm.propertyTypes" class="use-type-checkbox">
                            <a-checkbox
                                v-for="type in filteredPropertyTypeOptions"
                                :key="type.dictValue"
                                :value="type.dictValue"
                            >
                                {{ type.dictLabel }}
                            </a-checkbox>
                        </a-checkbox-group>
                    </div>

                </div>
            </div>
        </div>

        <!-- 房态图区域 - 使用真实数据 -->
        <div class="zoom-container" @wheel="handleWheel">
            <div class="floor-blocks">
                <!-- 动态渲染楼层数据 -->
                <div v-for="floor in floorDiagramData" :key="floor.floorId" class="floor-block">
                    <div class="floor-content">
                        <div class="floor-title">{{ floor.floorName }}</div>
                        <div class="rooms-wrapper">
                            <div v-for="room in getFilteredRoomsByFloor(floor)" :key="room.roomId"
                                :class="['room-cell', `status-${mapRoomStatus(room.roomStatus)}`]" :style="roomCellStyle"
                                @click="handleRoomClick(room)">
                                {{ room.roomName }}
                                <div v-if="room.isSelfUse" class="status-tag-corner tag-self-use"></div>
                                <div v-if="room.needCheckOut" class="status-tag-corner tag-checkout"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 房间详情弹窗 -->
        <a-modal v-model:visible="roomDetailVisible" width="400px" :footer="false" class="room-detail-modal"
            :header="false" :closable="false" :body-style="{ padding: 0 }">
            <div v-if="selectedRoom" class="room-detail-content">
                <div class="room-detail-header">
                    <div class="room-no-icon">{{ selectedBuilding }}{{ selectedRoom.number }}</div>
                    <div class="room-status-tag-custom" :class="`status-${selectedRoom.status}`">
                        {{ selectedRoom.statusName }}
                    </div>
                </div>

                <!-- 合同信息显示（在租、待生效、签约中状态时显示） -->
                <div class="room-detail-period" v-if=" selectedRoom.contractVo&&(selectedRoom.status === 'in-progress' || (selectedRoom.status === 'reserved'&&selectedRoom.statusName!='已预定'))">
                    <span class="label">合同租期:</span>
                    <span>{{ selectedRoom.contractStartDate }} 至 {{ selectedRoom.contractEndDate }}</span>
                </div>

                <!-- 待生效状态 - 显示租客确定和租客签约按钮 -->
                <div v-if=" (selectedRoom.status === 'reserved'&&selectedRoom.statusName!='已预定')||selectedRoom.status === 'vacant'" class="room-detail-actions">
                    <a-button type="primary" @click="handleRoomButtonClick('租客预定')">租客预定</a-button>
                    <a-button type="primary" @click="handleRoomButtonClick('租客签约')">租客签约</a-button>
                </div>

                <!-- 已预定状态 - 只有预订单没有合同时显示 -->
                <div v-if="selectedRoom.statusName == '已预定'">
                    <div class="customer-info">
                        <span class="label">预定客户:</span>
                        <span>{{ selectedRoom.reserveCustomName}}</span>
                    </div>
                </div>

                <!-- 合同和预订单都存在时 - 上面显示合同信息，下面显示预订单信息 -->
                <div v-if="selectedRoom.reserveName=='已预定'&&selectedRoom.statusName!='已预定'">
                    <!-- 预订单信息在下方显示 -->
                    <div class="booking-section">
                        <div class="section-divider"></div>
                        <div class="customer-info">
                            <span class="label">预定客户:</span>
                            <span>{{ selectedRoom.reserveCustomName}}</span>
                        </div>
                        <div class="reservation-tag-box">
                            <div class="reservation-tag">已预定</div>
                        </div>
                    </div>
                </div>
                <div class="room-detail-actions" v-if="selectedRoom.reserveName=='已预定'||selectedRoom.statusName=='已预定'">
                    <a-button type="primary" @click="handleRoomButtonClick('转签约')">转签约</a-button>
                </div>
            </div>
        </a-modal>
        
        <!-- 直接引入相关功能组件 -->
        <!-- 新增合同抽屉 -->
        <addContractDrawer ref="addContractRef" @submit="handleContractSubmit" />
        
        <!-- 新增订单抽屉 -->
        <addOrderForm ref="addOrderFormRef" @success="handleOrderSuccess" />
        
        <!-- 合同类型选择弹窗 -->
        <a-modal 
            v-model:visible="contractTypeModalVisible" 
            title="选择合同类型" 
            width="400px"
            @cancel="handleContractTypeCancel"
            :footer="false">
            <div class="contract-type-selection">
                <p class="selection-tip">该预定未确认具体房源，请选择要创建的合同类型：</p>
                <div class="contract-type-options">
                    <div 
                        v-for="option in contractTypeOptions" 
                        :key="option.value"
                        class="contract-type-option"
                        @click="handleContractTypeConfirm(option.value)">
                        <div class="option-content">
                            <div class="option-icon">
                                <icon-file />
                            </div>
                            <div class="option-text">
                                <span class="option-label">{{ option.label }}</span>
                            </div>
                            <div class="option-arrow">
                                <icon-right />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import dayjs from 'dayjs'
import { Message } from '@arco-design/web-vue'
import { IconFile, IconRight } from '@arco-design/web-vue/es/icon'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
// 引入合同相关组件
import addContractDrawer from '@/views/contract/addContractDrawer.vue'
// 引入订单管理组件
import addOrderForm from '@/views/orderManagement/components/addForm.vue'
import { getParcelList, getBuildingSelectList, getFloorList } from '@/api/project'
import { getRoomSimpleDiagram, type RoomDiagramQueryDTO, type FloorDiagramVo } from '@/api/room'
import { useDictSync } from '@/utils/dict'

// 定义房间状态和标签类型
type RoomStatus = 'vacant' | 'in-progress' | 'reserved' | 'unavailable' | 'not-launched' | 'self-use'
type TagType = 'reserved' | 'pending' | 'none'
type Position = 'left' | 'right'

// 定义房间接口
interface Room {
    id: string
    number: string
    floor: number
    position: Position
    status: RoomStatus
    hasTag?: boolean
    tagType?: TagType
    leaseStartDate?: string
    leaseEndDate?: string,
    bookingVo?: {
        customerName?: string
        [key: string]: any
    },
    contractVo?: {
        status?: string
        startDate?: string
        endDate?: string
        [key: string]: any
    },
    statusName?: string,
    reserveName?: string,
    reserveCustomName?: string,
    contractStartDate?: string,
    contractEndDate?: string
}

// 字典数据类型
interface DictData {
    dictCode: string
    dictValue: string
    dictLabel: string
    parentCode?: string
    childList?: DictData[]
}

// 查询表单
const queryForm = reactive<RoomDiagramQueryDTO & { propertyTypes: string[] }>({
    projectId: '',
    parcelId: '',
    buildingId: '',
    floorId: '',
    roomStatus: undefined,
    propertyType: '',
    diagramDate: dayjs().format('YYYY-MM-DD'),
    propertyTypes: []
})

// 数据列表
const parcelList = ref<any[]>([])
const buildingList = ref<any[]>([])
const floorList = ref<any[]>([])
const propertyTypeOptions = ref<DictData[]>([])
const propertyList = ref<any[]>([])

// 合同和订单相关数据
const addContractRef = ref()
const addOrderFormRef = ref()
const contractTypeModalVisible = ref(false)
const currentContractOperation = ref('')
const currentContractRecord = ref<any>(null)

// 合同类型选项
const contractTypeOptions = ref([
  { value: 0, label: '非宿舍合同' },
  { value: 1, label: '宿舍合同' },
  { value: 2, label: '多经合同' }
])

// 根据房源类型判断合同类型
const getContractTypeByRoom = (room: any): number => {
  if (!room) return 0
  
  // 如果房源类型为2（多经），直接返回多经合同类型
  if (room.type === 2) {
    return 2 // 多经合同
  }
  
  // 如果房源类型为1（普通），则根据物业类型判断
  if (room.type === 1) {
    // 如果物业类型为"10"（宿舍），返回宿舍合同
    if (room.propertyType === '10') {
      return 1 // 宿舍合同
    } else {
      return 0 // 非宿舍合同
    }
  }
  
  // 默认返回非宿舍合同
  return 0
}

const selectionInfo = ref<any>({
  projectId: '',
  projectName: '',
  blockId: '',
  blockName: '',
  buildingId: '',
  buildingName: ''
})

// 加载状态
const parcelLoading = ref(false)
const buildingLoading = ref(false)
const floorLoading = ref(false)

// 计算属性：只显示叶子节点的物业类型选项
const propertyTypeLeafOptions = computed(() => {
    const getLeafNodes = (nodes: DictData[]): DictData[] => {
        let leafNodes: DictData[] = []
        for (const node of nodes) {
            if (!node.childList || node.childList.length === 0) {
                leafNodes.push(node)
            } else {
                leafNodes = leafNodes.concat(getLeafNodes(node.childList))
            }
        }
        return leafNodes
    }
    return getLeafNodes(propertyTypeOptions.value)
})

// 计算属性：根据propertyList过滤出实际存在的物业类型选项
const filteredPropertyTypeOptions = computed(() => {
    if (!propertyList.value || propertyList.value.length === 0) {
        return []
    }
    
    const leafOptions = propertyTypeLeafOptions.value
    return leafOptions.filter(option => 
        propertyList.value.includes(option.dictValue)
    )
})

// 筛选参数（保留原有的一些字段用于兼容）
const filters = reactive({
    area: '1',
    block: 'F05',
    building: '31',
    floor: 'all',
    useType: 'all',
    showRented: false,
    date: undefined
})

// 选中的房间和楼栋
const selectedRoom = ref<Room | null>(null)
const selectedBuilding = ref('')
const roomDetailVisible = ref(false)

// 缩放和平移相关变量
const roomStatusContainer = ref<HTMLElement | null>(null)
const zoomLevel = ref(1)

// 房源状态统计
const roomStatusCounts = ref({
    all: 0,
    vacant: 0,
    inProgress: 0,
    reserved: 0,
    unavailable: 0
})

// 设置状态过滤参数
const setStatusFilter = (status: number | undefined) => {
    queryForm.roomStatus = status
    loadSimpleDiagramData()
}

// 根据楼层获取已过滤的房间数据（模拟数据备用）
const filteredRoomsByFloor = (floor: number) => {
    let rooms = roomsData.filter(room => room.floor === floor)
    return rooms
}

// 计算房间样式
const roomCellStyle = computed(() => {
    return {
        width: `${60 * zoomLevel.value}px`,
        height: `${36 * zoomLevel.value}px`,
        fontSize: `${12 * zoomLevel.value}px`,
        margin: `${5 * zoomLevel.value}px`,
        '--room-scale': zoomLevel.value
    }
})

// 处理鼠标滚轮事件 - 缩放功能
const handleWheel = (event: WheelEvent) => {
    event.preventDefault()

    // 计算缩放量 - 向下滚动缩小，向上滚动放大
    const delta = -event.deltaY * 0.001

    // 计算新的缩放级别，限制在 0.5 到 2 之间
    const newZoomLevel = Math.min(Math.max(zoomLevel.value + delta, 0.5), 2)

    // 应用新的缩放级别
    zoomLevel.value = newZoomLevel
}

// 模拟房间数据
const roomsData = reactive<Room[]>([
    // 3层房间 - 左侧
    { id: '301', number: '301', floor: 3, position: 'left', status: 'in-progress' },
    { id: '302', number: '302', floor: 3, position: 'left', status: 'in-progress' },
    { id: '303', number: '303', floor: 3, position: 'left', status: 'in-progress' },
    { id: '304', number: '304', floor: 3, position: 'left', status: 'in-progress' },
    { id: '305', number: '305', floor: 3, position: 'left', status: 'unavailable' },
    { id: '306', number: '306', floor: 3, position: 'left', status: 'in-progress' },
    { id: '307', number: '307', floor: 3, position: 'left', status: 'in-progress' },
    { id: '308', number: '308', floor: 3, position: 'left', status: 'in-progress' },
    { id: '316', number: '316', floor: 3, position: 'left', status: 'in-progress' },
    { id: '317', number: '317', floor: 3, position: 'left', status: 'reserved' },
    { id: '318', number: '318', floor: 3, position: 'left', status: 'in-progress' },
    { id: '319', number: '319', floor: 3, position: 'left', status: 'in-progress' },

    // 3层房间 - 右侧
    { id: '309', number: '309', floor: 3, position: 'right', status: 'in-progress' },
    { id: '310', number: '310', floor: 3, position: 'right', status: 'in-progress' },
    { id: '311', number: '311', floor: 3, position: 'right', status: 'in-progress' },
    { id: '312', number: '312', floor: 3, position: 'right', status: 'in-progress' },
    { id: '313', number: '313', floor: 3, position: 'right', status: 'in-progress' },
    { id: '314', number: '314', floor: 3, position: 'right', status: 'in-progress' },
    { id: '315', number: '315', floor: 3, position: 'right', status: 'in-progress' },

    // 2层房间 - 左侧
    { id: '201', number: '201', floor: 2, position: 'left', status: 'in-progress' },
    { id: '202', number: '202', floor: 2, position: 'left', status: 'in-progress' },
    { id: '203', number: '203', floor: 2, position: 'left', status: 'vacant' },
    { id: '204', number: '204', floor: 2, position: 'left', status: 'in-progress' },
    { id: '205', number: '205', floor: 2, position: 'left', status: 'unavailable' },
    { id: '206', number: '206', floor: 2, position: 'left', status: 'in-progress' },
    { id: '207', number: '207', floor: 2, position: 'left', status: 'reserved' },
    { id: '216', number: '216', floor: 2, position: 'left', status: 'in-progress' },
    { id: '217', number: '217', floor: 2, position: 'left', status: 'reserved' },
    { id: '218', number: '218', floor: 2, position: 'left', status: 'in-progress' },
    { id: '219', number: '219', floor: 2, position: 'left', status: 'in-progress' },

    // 2层房间 - 右侧
    { id: '208', number: '208', floor: 2, position: 'right', status: 'in-progress' },
    { id: '209', number: '209', floor: 2, position: 'right', status: 'in-progress' },
    { id: '210', number: '210', floor: 2, position: 'right', status: 'reserved' },
    { id: '211', number: '211', floor: 2, position: 'right', status: 'in-progress' },
    { id: '212', number: '212', floor: 2, position: 'right', status: 'in-progress' },
    { id: '213', number: '213', floor: 2, position: 'right', status: 'in-progress' },
    { id: '214', number: '214', floor: 2, position: 'right', status: 'in-progress' },
    { id: '215', number: '215', floor: 2, position: 'right', status: 'in-progress' },

    // 1层房间 - 左侧
    { id: '101', number: '101', floor: 1, position: 'left', status: 'in-progress' },
    { id: '102', number: '102', floor: 1, position: 'left', status: 'unavailable' },
    { id: '103', number: '103', floor: 1, position: 'left', status: 'vacant' },
    { id: '104', number: '104', floor: 1, position: 'left', status: 'in-progress' },
    { id: '105', number: '105', floor: 1, position: 'left', status: 'unavailable' },
    { id: '106', number: '106', floor: 1, position: 'left', status: 'in-progress' },
    { id: '107', number: '107', floor: 1, position: 'left', status: 'in-progress' },
    { id: '116', number: '116', floor: 1, position: 'left', status: 'in-progress' },
    { id: '117', number: '117', floor: 1, position: 'left', status: 'in-progress' },
    { id: '118', number: '118', floor: 1, position: 'left', status: 'reserved' },
    { id: '119', number: '119', floor: 1, position: 'left', status: 'reserved' },

    // 1层房间 - 右侧
    { id: '108', number: '108', floor: 1, position: 'right', status: 'in-progress' },
    { id: '109', number: '109', floor: 1, position: 'right', status: 'in-progress' },
    { id: '110', number: '110', floor: 1, position: 'right', status: 'in-progress' },
    { id: '111', number: '111', floor: 1, position: 'right', status: 'reserved' },
    { id: '112', number: '112', floor: 1, position: 'right', status: 'in-progress' },
    { id: '113', number: '113', floor: 1, position: 'right', status: 'in-progress' },
    { id: '114', number: '114', floor: 1, position: 'right', status: 'in-progress' },
    { id: '115', number: '115', floor: 1, position: 'right', status: 'vacant' }
])

// 根据楼层获取房间数据
const getRoomsByFloor = (floor: number) => {
    return roomsData.filter(room => room.floor === floor)
}

// 根据楼层和位置筛选房间
const getRoomsByFloorAndPosition = (floor: number, position: Position) => {
    return roomsData.filter(room => room.floor === floor && room.position === position)
}

// 处理房间点击
const handleRoomClick = (room: any) => {
    let roomData = {
        id: room.roomId,
        number: room.roomName,
        position: 'left' as Position,
        status: mapRoomStatus(room.roomStatus),
        statusName:'',
        reserveName:'',
        reserveCustomName:'',
        contractStartDate:'',
        contractEndDate:''
    }
    if(roomData.status == 'reserved'){
        if (room.bookingVo && room.contractVo) {
            roomData.statusName = (room.contractVo.status == '30'||(room.contractVo.status == '20'&&room.contractVo.approveStatus == '1')) ? '签约中' : '待生效';
            roomData.reserveName = '已预定';
            roomData.contractStartDate = room.contractVo.startDate;
            roomData.contractEndDate = room.contractVo.endDate;
            roomData.reserveCustomName = room.bookingVo.customerName;
        }else{
            if (room.contractVo) {
                roomData.contractStartDate = room.contractVo.startDate;
                roomData.contractEndDate = room.contractVo.endDate;
                roomData.statusName = (room.contractVo.status == '30'||(room.contractVo.status == '20'&&room.contractVo.approveStatus == '1')) ? '签约中' : '待生效';
            } else if (room.bookingVo) {
                roomData.statusName = '已预定';
                roomData.reserveCustomName = room.bookingVo.customerName;
            } else {
                roomData.statusName = '待生效';
            }
        }
    }else if(roomData.status == 'in-progress'){
        if(room.contractVo){
            roomData.contractStartDate = room.contractVo.startDate;
            roomData.contractEndDate = room.contractVo.endDate;
        }
        roomData.statusName = '在租';
    }else{
        roomData.statusName = getStatusText(roomData.status);
    }
    selectedRoom.value = Object.assign(roomData,room);
    roomDetailVisible.value = true
}

// 处理房间按钮点击 - 直接调用功能组件
const handleRoomButtonClick = (buttonType: string) => {
    if (!selectedRoom.value) return
    
    // 关闭当前弹窗
    roomDetailVisible.value = false
    
    // 根据按钮类型执行相应操作
    switch (buttonType) {
        case '租客预定':
            handleBooking()
            break
        case '租客签约':
            handleDirectContract()
            break
        case '转签约':
            handleBookingToContract()
            break
        default:
            Message.info(`${buttonType}功能开发中...`)
    }
}

// 租客预定 - 参照 orderManagementList.vue 的新增逻辑
const handleBooking = () => {
    const projectId = selectionInfo.value?.projectId
    if (!projectId) {
        Message.warning('请先选择项目')
        return
    }
    
    // 构建房源信息
    const roomInfo = {
        roomId: (selectedRoom.value as any)?.roomId || '',
        roomName: (selectedRoom.value as any)?.roomName || '',
        propertyType: (selectedRoom.value as any)?.propertyType || '',
        propertyTypeName: (selectedRoom.value as any)?.propertyTypeName || '',
        projectId: projectId,
        projectName: selectionInfo.value?.projectName || ''
    }
    
    console.log('租客预定传递房源信息:', roomInfo)
    
    // 打开订单新增抽屉，传入项目ID和房源信息
    addOrderFormRef.value?.open(projectId, roomInfo)
}

// 租客签约 - 参照 contract/list.vue 的新增合同逻辑
const handleDirectContract = () => {
    const projectId = selectionInfo.value?.projectId
    if (!projectId) {
        Message.warning('请选择项目')
        return
    }

    // 根据房源类型自动判断合同类型
    const contractType = getContractTypeByRoom(selectedRoom.value)
    
    // 构建项目信息
    const project = {
        id: projectId,
        name: selectionInfo.value?.projectName || ''
    }

    // 构建房间信息作为参数
    const roomInfo = {
        roomId: (selectedRoom.value as any)?.roomId || '',
        roomName: (selectedRoom.value as any)?.roomName || '',
        projectId: project.id,
        projectName: project.name,
        isDirectContract: true, // 添加直接签约标识
        // 可以添加更多房间相关信息
        ...selectedRoom.value
    }
    
    console.log('直接签约，合同类型:', contractType, '房间信息:', roomInfo)
    
    // 直接打开对应类型的合同新增页面
    addContractRef.value?.open('create', contractType, roomInfo, project)
}

// 预定转签约处理
const handleBookingToContract = () => {
    try {
        const room = selectedRoom.value
        const booking = (room as any)?.bookingVo
        
        if (!booking) {
            Message.warning('当前房间没有预定信息')
            return
        }
        
        // 保存当前预定记录，构建类似订单的数据结构
        const bookingRecord = {
            id: booking.bookingId || '',
            projectId: selectionInfo.value?.projectId || '',
            projectName: selectionInfo.value?.projectName || '',
            customerName: booking.customerName || '',
            roomName: (room as any).roomName || '',
            roomId: (room as any).roomId || '',
            propertyType: (room as any).propertyType || '',
            type: (room as any).type || 1, // 添加房源类型
            bookingAmount: booking.bookingAmount || 0,
            isRenewalContract: true,
            ...booking
        }
        
        console.log('预定转签约记录:', bookingRecord)
        
        // 判断是否为暂未确认房源
        const isUnknownSource = !bookingRecord.roomId
        
        if (isUnknownSource) {
            // 暂未确认房源的预定仍需要选择合同类型
            currentContractRecord.value = bookingRecord
            currentContractOperation.value = 'booking-to-contract'
            contractTypeModalVisible.value = true
        } else {
            // 已确认房源的预定根据房源类型自动判断合同类型
            const contractType = getContractTypeByRoom(room)
            
            // 构建项目信息
            const project = {
                id: bookingRecord.projectId,
                name: bookingRecord.projectName
            }
            
            console.log('预定转签约，合同类型:', contractType)
            
            // 直接打开对应类型的合同新增页面
            addContractRef.value?.open('create', contractType, bookingRecord, project)
        }
    } catch (error) {
        console.error('预定转签约失败:', error)
    }
}

// 确认选择合同类型（仅用于暂未确认房源的预定转签约）
const handleContractTypeConfirm = (contractType: number) => {
    try {
        if (currentContractOperation.value === 'booking-to-contract' && currentContractRecord.value) {
            // 预定转签约：传入预定信息
            const project = {
                id: currentContractRecord.value.projectId,
                name: currentContractRecord.value.projectName
            }
            
            console.log('暂未确认房源预定转签约，选择合同类型:', contractType)
            
            // 打开合同新增页面，传入预定信息和选择的合同类型
            addContractRef.value?.open('create', contractType, currentContractRecord.value, project)
        }
        
        // 关闭选择弹窗
        contractTypeModalVisible.value = false
        currentContractRecord.value = null
    } catch (error) {
        console.error('合同类型选择失败:', error)
    }
}

// 取消选择合同类型
const handleContractTypeCancel = () => {
    contractTypeModalVisible.value = false
    currentContractRecord.value = null
}

// 合同提交成功回调
const handleContractSubmit = () => {
    Message.success('合同创建成功')
    // 刷新房间状态数据
    loadSimpleDiagramData()
}

// 订单提交成功回调
const handleOrderSuccess = () => {
    Message.success('订单创建成功')
    // 刷新房间状态数据
    loadSimpleDiagramData()
}

// 获取状态文本
const getStatusText = (status: RoomStatus) => {
    const statusMap: Record<RoomStatus, string> = {
        'vacant': '空置',
        'in-progress': '在租',
        'reserved': '待生效',
        'unavailable': '不可招商',
        'self-use': '自用',
        'not-launched': '未出场'
    }
    return statusMap[status] || '未知'
}



// 获取物业类型字典数据
const getPropertyTypeDicts = async () => {
    try {
        const dictData = await useDictSync('diversification_purpose')
        console.log('物业类型字典数据:', dictData)
        if (dictData.diversification_purpose) {
            const dictList = dictData.diversification_purpose as DictData[]
            // 组织成树形结构
            const treeData = dictList.filter(item => !item.parentCode)
            treeData.forEach(parent => {
                parent.childList = dictList.filter(child => child.parentCode === parent.dictCode)
            })
            propertyTypeOptions.value = treeData
        }
    } catch (error) {
        console.error('获取物业类型字典数据失败:', error)
    }
}

// 处理项目选择变化
const handleProjectChange = (projectId: string, selection: any) => {
    if (!projectId) return
    
    queryForm.projectId = projectId
    queryForm.parcelId = ''
    queryForm.buildingId = ''
    queryForm.floorId = ''
    
    // 更新选择信息
    selectionInfo.value.projectId = projectId
    selectionInfo.value.projectName = selection?.name || ''
    selectionInfo.value.blockId = ''
    selectionInfo.value.blockName = ''
    selectionInfo.value.buildingId = ''
    selectionInfo.value.buildingName = ''
    
    // 重置地块、楼栋、楼层列表
    parcelList.value = []
    buildingList.value = []
    floorList.value = []
    
    // 加载地块列表
    loadParcelList(projectId)
}

// 加载地块列表
const loadParcelList = async (projectId: string) => {
    parcelLoading.value = true
    try {
        const { data } = await getParcelList(projectId)
        parcelList.value = data || []
        
        // 默认选择第一个地块
        if (parcelList.value.length > 0 && !queryForm.parcelId) {
            queryForm.parcelId = parcelList.value[0].id
            selectionInfo.value.blockId = parcelList.value[0].id
            selectionInfo.value.blockName = parcelList.value[0].parcelName
            if (queryForm.parcelId) {
                loadBuildingList(queryForm.parcelId)
            }
        }
    } catch (error) {
        console.error('获取地块列表失败:', error)
        parcelList.value = []
    } finally {
        parcelLoading.value = false
    }
}

// 处理地块选择变化
const handleParcelChange = (parcelId: string) => {
    if (!parcelId) return
    
    queryForm.parcelId = parcelId
    queryForm.buildingId = ''
    queryForm.floorId = ''
    
    // 更新选择信息
    selectionInfo.value.blockId = parcelId
    selectionInfo.value.blockName = parcelList.value.find(item => item.id === parcelId)?.parcelName || ''
    selectionInfo.value.buildingId = ''
    selectionInfo.value.buildingName = ''
    
    // 重置楼栋、楼层列表
    buildingList.value = []
    floorList.value = []
    
    // 加载楼栋列表
    loadBuildingList(parcelId)
}

// 加载楼栋列表
const loadBuildingList = async (parcelId: string) => {
    buildingLoading.value = true
    try {
        const { data } = await getBuildingSelectList(parcelId)
        buildingList.value = data || []
        
        // 默认选择第一个楼栋
        if (buildingList.value.length > 0 && !queryForm.buildingId) {
            queryForm.buildingId = buildingList.value[0].id
            // 更新选中的楼栋名称
            selectedBuilding.value = buildingList.value[0].buildingName + '-'
            selectionInfo.value.buildingId = buildingList.value[0].id
            selectionInfo.value.buildingName = buildingList.value[0].buildingName
            if (queryForm.buildingId) {
                loadFloorList(queryForm.buildingId)
            }
        }
    } catch (error) {
        console.error('获取楼栋列表失败:', error)
        buildingList.value = []
    } finally {
        buildingLoading.value = false
    }
}

// 处理楼栋选择变化
const handleBuildingChange = (buildingId: string) => {
    if (!buildingId) return
    
    queryForm.buildingId = buildingId
    queryForm.floorId = ''
    
    // 更新选中的楼栋名称
    const selectedBuildingItem = buildingList.value.find(building => building.id === buildingId)
    selectedBuilding.value = selectedBuildingItem ? selectedBuildingItem.buildingName + '-' : ''
    
    // 更新选择信息
    selectionInfo.value.buildingId = buildingId
    selectionInfo.value.buildingName = selectedBuildingItem ? selectedBuildingItem.buildingName : ''
    
    // 重置楼层列表
    floorList.value = []
    
    // 加载楼层列表
    loadFloorList(buildingId)
}

// 加载楼层列表
const loadFloorList = async (buildingId: string) => {
    floorLoading.value = true
    try {
        const { data } = await getFloorList(buildingId)
        floorList.value = data || []
        
        // 默认选择全部楼层
        if (!queryForm.floorId) {
            queryForm.floorId = ''
        }
        
        // 加载房态数据
        loadSimpleDiagramData()
    } catch (error) {
        console.error('获取楼层列表失败:', error)
        floorList.value = []
    } finally {
        floorLoading.value = false
    }
}

// 处理楼层选择变化
const handleFloorChange = (floorId: string) => {
    queryForm.floorId = floorId
    // 可以在这里触发数据重新加载
    loadSimpleDiagramData()
}

// 加载房态简图数据
const loadSimpleDiagramData = async () => {
    if (!queryForm.projectId || !queryForm.parcelId || !queryForm.buildingId) {
        return
    }
    
    try {
        // 组装查询参数
        const params: RoomDiagramQueryDTO = {
            projectId: queryForm.projectId,
            parcelId: queryForm.parcelId,
            buildingId: queryForm.buildingId,
            floorId: queryForm.floorId || undefined,
            roomStatus: queryForm.roomStatus,
            propertyType: queryForm.propertyTypes.join(',') || undefined,
            diagramDate: queryForm.diagramDate
        }
        
        const { data } = await getRoomSimpleDiagram(params)
        console.log('房态简图数据:', data)
        updateRoomStatusCounts(data)
        updateRoomDisplayData(data)
    } catch (error) {
        console.error('获取房态简图数据失败:', error)
    }
}

// 更新房源状态统计
const updateRoomStatusCounts = (floorData: any) => {
    const counts = {
        all: floorData.totalCount,
        vacant: floorData.emptyCount,
        inProgress: floorData.rentCount,
        reserved: floorData.toEffectCount,
        unavailable: floorData.invalidCount
    }
    roomStatusCounts.value = counts
}

// 房态数据
const floorDiagramData = ref<FloorDiagramVo[]>([])

// 更新房间显示数据
const updateRoomDisplayData = (apiData: any) => {
    // 将接口返回的数据保存到响应式变量中
    floorDiagramData.value = apiData.floorDiagramList || []
    propertyList.value = apiData.propertyList || []
    console.log('更新房间显示数据:', apiData)
}

// 房间状态映射函数
const mapRoomStatus = (roomStatus: number | undefined): RoomStatus => {
    if (roomStatus === undefined) return 'vacant'
    
    switch (roomStatus) {
        case 1:
            return 'vacant'  // 空置
        case 2:
            return 'in-progress'  // 在租
        case 3:
            return 'reserved'  // 待生效/签约中/已预订
        case 4:
            return 'unavailable'  // 不可招商
        default:
            return 'vacant'
    }
}

// 根据房态数据获取所有房间（已过滤）
const getAllFilteredRooms = computed(() => {
    let allRooms: any[] = []
    
    floorDiagramData.value.forEach(floor => {
        if (floor.rooms && Array.isArray(floor.rooms)) {
            floor.rooms.forEach(room => {
                allRooms.push({
                    ...room,
                    floorName: floor.floorName,
                    status: mapRoomStatus(room.roomStatus)
                })
            })
        }
    })
    
    // 根据当前状态过滤进行过滤
    if (queryForm.roomStatus !== undefined) {
        allRooms = allRooms.filter(room => room.roomStatus === queryForm.roomStatus)
    }
    
    return allRooms
})

// 根据楼层获取过滤后的房间数据
const getFilteredRoomsByFloor = (floor: FloorDiagramVo) => {
    if (!floor.rooms || !Array.isArray(floor.rooms)) {
        return []
    }
    
    let rooms = floor.rooms
    
    // 根据当前状态过滤
    if (queryForm.roomStatus !== undefined) {
        rooms = rooms.filter(room => room.roomStatus === queryForm.roomStatus)
    }
    
    return rooms
}

// 监听远期房态日期变化
watch(() => queryForm.diagramDate, (newDate) => {
    if (newDate && queryForm.projectId && queryForm.parcelId && queryForm.buildingId) {
        loadSimpleDiagramData()
    }
})

// 监听物业类型变化
watch(() => queryForm.propertyTypes, () => {
    if (queryForm.projectId && queryForm.parcelId && queryForm.buildingId) {
        loadSimpleDiagramData()
    }
}, { deep: true })

// 设置事件监听
onMounted(() => {
    // 防止页面滚动
    const container = roomStatusContainer.value
    if (container) {
        container.addEventListener('wheel', (e) => e.preventDefault(), { passive: false })
    }
    
    // 初始化字典数据
    getPropertyTypeDicts()
})

// 清理事件监听
onUnmounted(() => {
    const container = roomStatusContainer.value
    if (container) {
        container.removeEventListener('wheel', (e) => e.preventDefault())
    }
})

// 获取查询参数
const getQueryParams = () => {
    return {
        projectId: queryForm.projectId,
        parcelId: queryForm.parcelId,
        buildingId: queryForm.buildingId,
        floorId: queryForm.floorId,
        roomStatus: queryForm.roomStatus,
        propertyTypes: [...queryForm.propertyTypes],
        diagramDate: queryForm.diagramDate,
        selectionInfo: { ...selectionInfo.value }
    }
}

// 设置查询参数
const setQueryParams = (params: any) => {
    if (!params) return
    
    // 设置基础查询参数
    if (params.projectId) queryForm.projectId = params.projectId
    if (params.parcelId) queryForm.parcelId = params.parcelId
    if (params.buildingId) queryForm.buildingId = params.buildingId
    if (params.floorId !== undefined) queryForm.floorId = params.floorId
    if (params.roomStatus !== undefined) queryForm.roomStatus = params.roomStatus
    if (params.propertyTypes) queryForm.propertyTypes = [...params.propertyTypes]
    if (params.diagramDate) queryForm.diagramDate = params.diagramDate
    
    // 设置选择信息
    if (params.selectionInfo) {
        selectionInfo.value = { ...params.selectionInfo }
    }
    
    // 重新加载相关数据（异步执行，不阻塞）
    if (params.projectId && params.projectId !== '') {
        loadParcelList(params.projectId)
    }
    if (params.parcelId && params.parcelId !== '') {
        loadBuildingList(params.parcelId)
    }
    if (params.buildingId && params.buildingId !== '') {
        loadFloorList(params.buildingId)
    }
}

// 暴露方法给父组件调用
defineExpose({
  loadSimpleDiagramData,
  getQueryParams,
  setQueryParams
})
</script>

<style scoped lang="less">
.room-status-simple {
    width: 100%;
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    border-radius: 8px;
    padding: 0 16px;
}

.status-filters {
    margin-bottom: 16px;
}

.status-buttons-group {
    display: flex;
    align-items: center;
    gap: 0 16px;
}

.status-buttons-main {
    display: flex;
    gap: 8px;
}

.status-buttons-divider {
    width: 1px;
    height: 24px;
    background: #e5e6eb;
    margin: 0 64px;
}
@media screen and (max-width: 1366px) {
    .status-buttons-divider {
        margin: 0 0 0 16px;
    }
}

.status-buttons-secondary {
    display: flex;
    gap: 8px;
}

.status-button {
    padding: 0 16px;
    height: 36px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    border: none;
    background: #f7f8fa;
    color: #165dff;
    transition: background 0.2s, color 0.2s;
}

.status-button.all.active {
    background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
    color: #fff;
}

.status-button.vacant {
    background: #EFF5FF;
    color: #3fc06d;
    &.active {
        background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
        color: #fff;
    }
}

.status-button.in-progress {
    background: #EFF5FF;
    color: #f53f3f;
    &.active {
        background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
        color: #fff;
    }
}

.status-button.reserved {
    background: #EFF5FF;
    color: #ff9a2e;
    &.active {
        background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
        color: #fff;
    }
}

.status-button.unavailable {
    background: #EFF5FF;
    color: #86909c;
    &.active {
        background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
        color: #fff;
    }
}

.status-button.no-bg {
    background: transparent;
    color: #1d2129;
    font-weight: 400;
    padding: 0 16px;
}

.status-button:not(.all):not(.no-bg):hover {
    opacity: 0.85;
}

.triangle-indicator {
    width: 0;
    height: 0;
    margin-right: 8px;
    border-style: solid;
    border-width: 0 16px 16px 0;
}

.triangle-indicator.blue {
    border-color: transparent #3583FF transparent transparent;
}

.triangle-indicator.purple {
    border-color: transparent #D73EFF transparent transparent;
}

/* 房间状态标识角标样式 */
.status-tag-corner {
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 12px 12px 0;
}

.tag-self-use {
    border-color: transparent #3583FF transparent transparent;
}

.tag-checkout {
    border-color: transparent #D73EFF transparent transparent;
}

.filter-bar-wrapper {
    background: #f7f8fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.filter-bar {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
}

.filter-group {
    display: flex;
    gap: 16px;
    align-items: center;
}
.filter-group:first-child{
  margin-right: 50px;
}

.filter-group-right {
    margin-left: auto;
    display: flex;
    gap: 16px;
    align-items: center;
}

.filter-item .label {
    margin-right: 8px;
    font-size: 14px;
    color: #616974;
}

.use-type {
    display: flex;
    align-items: center;
}

.use-type-checkbox {
    display: inline-flex;
    align-items: center;
}

.date-range {
    margin-left: 0;
}

:deep(.gray-bg-select .arco-select-view) {
    background-color: #f2f3f5;
    border-radius: 4px;
}

:deep(.checkbox-style .arco-checkbox-icon) {
    background-color: #f2f3f5;
}

:deep(.arco-select-view-single) {
    background-color: transparent;
}

:deep(.arco-modal-header) {
    display: none;
}
:deep(.arco-modal) {
  .arco-modal-body {
    padding: 0 !important;
  }
}
.zoom-container {
    margin-top: 16px;
    overflow: auto;
    position: relative;
    border: 1px solid #eee;
    border-radius: 8px;
    background: #fff;
}

.floor-blocks {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 16px;
    transform-origin: top left;
    transition: transform 0.1s ease;
}

.floor-block {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #eee;
    margin-bottom: 16px;
    width: 100%;
    overflow-x: auto;
}

.floor-content {
    display: flex;
    background: #EFF5FF;
}

.floor-title {
    min-width: 60px;
    padding-left: 16px;
    white-space: nowrap;
    display: flex;
    color: #333;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: sticky;
    left: 0;
    z-index: 1;
    font-size: 14px;
}

.rooms-wrapper {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    gap: 0;
    align-content: flex-start;
    flex: 1;
}

.room-cell {
    margin: 5px;
    width: 60px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    overflow: hidden;
    padding: 5px;
    background: #165dff;
    word-break: break-all;
}

.room-cell.status-vacant {
    background-color: #67C23A;
}

.room-cell.status-in-progress {
    background-color: #F56C6C;
}

.room-cell.status-reserved {
    background-color: #E6A23C;
}

.room-cell.status-unavailable {
    background-color: #8B929C;
}

.room-cell.status-self-use {
    background-color: #fff;
    color: #165dff;
    border: 1px solid #165dff;
}

.room-cell.status-not-launched {
    background-color: #fff;
    color: #8F87FF;
    border: 1px solid #8F87FF;
}

.room-cell:hover {
    transform: translateY(-2px) scale(1.04);
    box-shadow: 0 2px 8px rgba(22, 93, 255, 0.12);
    z-index: 2;
}

.triangle-corner {
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 12px 12px 0;
    z-index: 1;
    transform-origin: top right;
    transform: scale(var(--room-scale, 1));
}

.triangle-corner.blue {
    border-color: transparent #165dff transparent transparent;
}

.triangle-corner.purple {
    border-color: transparent #8F87FF transparent transparent;
}

.status-tag-corner {
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    z-index: 2;
    transform-origin: top right;
    transform: scale(var(--room-scale, 1));
}

.status-tag-corner.tag-reserved {
    background-color: #E6A23C;
}

.status-tag-corner.tag-pending {
    background-color: #FAAD14;
}

.room-detail-modal :deep(.arco-modal-content) {
    border-radius: 10px;
    padding: 0;
    background: #fff;
}


.room-detail-content {
    padding: 0px 0px 24px 16px;
    position: relative;
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    overflow: hidden;
}

.reservation-tag-box {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
    .reservation-tag{
        padding: 4px 16px;
        font-size: 14px;
        font-weight: 500;
        color: #fff;
        border-radius: 0 0 0 16px;
        box-shadow: 0 2px 8px rgba(230, 162, 60, 0.2);
        z-index: 2;
        text-align: center;
        background-color: #E6A23C;
    }
}

.room-detail-header {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 24px;
    padding-left: 14px;
}

.room-no-icon {
    white-space: nowrap;
    text-align: left;
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
    padding: 8px 8px 8px 35px;
    height: 30px;
    width: 128px;
    display: flex;
    align-items: center;
    margin-top: 30px;
    background-image: url('@/assets/images/rent/title-bg.png');
    background-size: 100% 100%;
}

.room-no-icon::before {
    content: "";
    position: absolute;
    left: -18px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    // background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23909399"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>');
    background-repeat: no-repeat;
    background-size: contain;
    opacity: 0.7;
}

.room-status-tag-custom {
    position: absolute;
    right: 0;
    top: 0;
    padding: 4px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    border-radius: 0 4px 0 16px;
    box-shadow: 0 2px 8px rgba(230, 162, 60, 0.2);
    z-index: 2;
    text-align: center;
}

.room-status-tag-custom.status-vacant {
    background: #67C23A;
}

.room-status-tag-custom.status-in-progress {
    background: #F56C6C;
}

.room-status-tag-custom.status-reserved {
    background: #E6A23C;
}

.room-status-tag-custom.status-unavailable {
    background: #909399;
}

.room-status-tag-custom.status-self-use {
    background: #409EFF;
}

.room-status-tag-custom.status-not-launched {
    background: #8F87FF;
}

.room-detail-period {
    font-size: 14px;
    color: #4e5969;
    margin-bottom: 24px;
    text-align: center;
    background-color: #f7f8fa;
    padding: 10px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
}

.room-detail-period .label {
    font-weight: 500;
    color: #666;
    margin-right: 8px;
}

.room-detail-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 10px;
}

.room-detail-actions .arco-btn {
    min-width: 120px;
    height: 38px;
    font-size: 15px;
    font-weight: 500;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(22, 93, 255, 0.1);
    background: #165dff;
    border-color: #165dff;
}

.customer-info {
    font-size: 14px;
    color: #4e5969;
    margin-bottom: 16px;
    text-align: center;
    background-color: #f7f8fa;
    padding: 10px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
}

.customer-info .label {
    font-weight: 500;
    color: #666;
    margin-right: 8px;
}

.booking-section {
    margin-top: 16px;
}

.section-divider {
    height: 1px;
    background-color: #e5e6eb;
    margin: 16px 0;
}
:deep(.w-full) {
    @media (max-width: 1919px) {
        flex-direction: column;
        .filter-group:first-child{
            width: 100%;
            display: flex;
            justify-content: flex-start;
        }
        .filter-group:last-child{
            width: 100%;
            display: flex;
            justify-content: flex-end;
        }
    }
}

// 合同类型选择弹窗样式
.contract-type-selection {
    .selection-tip {
        margin-bottom: 16px;
        color: #666;
        font-size: 14px;
    }
    
    .contract-type-options {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .contract-type-option {
        border: 1px solid #e5e6eb;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
            border-color: #165dff;
            background-color: #f2f6ff;
        }
        
        .option-content {
            display: flex;
            align-items: center;
            padding: 16px;
            gap: 12px;
            
            .option-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                background-color: #f2f6ff;
                border-radius: 6px;
                color: #165dff;
            }
            
            .option-text {
                flex: 1;
                
                .option-label {
                    font-size: 14px;
                    font-weight: 500;
                    color: #1d2129;
                }
            }
            
            .option-arrow {
                color: #86909c;
            }
        }
    }
}
</style>