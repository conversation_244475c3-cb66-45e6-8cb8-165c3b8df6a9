<template>
    <a-drawer v-model:visible="dialogVisible" title="批量资产处置" class="common-drawer" @cancel="handleCancel">
        <div class="reception-form">
            <!-- 项目信息 -->
            <div class="project-info">
                <a-form :model="projectForm" layout="inline">
                    <a-form-item field="project" label="项目" required>
                        <a-select v-model="projectForm.project" placeholder="请选择项目" :disabled="true"
                            style="width: 240px;">
                            <a-option :value="projectForm.project">{{ projectForm.projectName }}</a-option>
                        </a-select>
                    </a-form-item>
                </a-form>
            </div>

            <!-- 处置资产清单 -->
            <div class="reception-list">
                <div class="list-header">
                    <section-title title="处置资产清单" />
                    <div class="list-actions">
                        <div class="search-box">
                            <a-input-search v-model="searchKeyword" placeholder="请输入楼栋/房源名称搜索" allow-clear
                                style="width: 360px;" @search="handleSearch" @clear="handleClearSearch" />
                        </div>
                        <a-space v-if="!readOnlyMode">
                            <a-button type="primary" @click="handleAddAsset">
                                <template #icon><icon-plus /></template>
                                添加资产
                            </a-button>
                            <a-button @click="handleBatchRemove" :disabled="!hasSelected">
                                <template #icon><icon-delete /></template>
                                批量移除
                            </a-button>
                        </a-space>
                    </div>
                </div>

                <a-table :bordered="{ cell: true }" :columns="columns" :data="tableData" size="small" row-key="index"
                    :row-selection="readOnlyMode ? undefined : rowSelection" @selection-change="handleSelectionChange" :scroll="{ x: 1 }">
                    <template #productType="{ record }">
                        {{ getProductTypeText(record.productType) }}
                    </template>
                    <template #operation="{ record }">
                        <a-button v-if="!readOnlyMode" type="text" size="mini" @click="handleRemove(record)">移除</a-button>
                    </template>
                </a-table>
            </div>

            <!-- 处置信息 -->
            <div class="reception-info">
                <section-title title="处置信息" />
                <a-form ref="formRef" :model="formData" :rules="formRules" :label-col-props="{ span: 6 }"
                    :wrapper-col-props="{ span: 18 }" auto-label-width>
                    <a-row :gutter="16">
                        <a-col :span="6">
                            <a-form-item field="disposalMethod" label="处置方式" required>
                                <a-select v-model="formData.disposalMethod" placeholder="请选择处置类型"
                                    :disabled="readOnlyMode">
                                    <a-option v-for="item in disposal_type" :value="item.value" :key="item.value">
                                        {{ item.label }}
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="receptionDate" label="处置日期" required>
                                <a-date-picker v-model="formData.receptionDate" style="width: 100%"
                                    :disabled="readOnlyMode" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="14">
                            <a-form-item field="disposalDescription" label="处置说明">
                                <a-textarea v-model="formData.disposalDescription" :max-length="100" show-word-limit placeholder="请输入处置说明"
                                    :disabled="readOnlyMode" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="14">
                            <a-form-item field="remark" label="备注">
                                <a-textarea v-model="formData.remark" placeholder="请输入备注" :max-length="500" show-word-limit :disabled="readOnlyMode" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="14">
                            <a-form-item field="attachment" label="附件">
                                <upload-file v-model="formData.attachment" :readonly="readOnlyMode" />
                            </a-form-item>
                        </a-col>
                    </a-row>

                </a-form>
            </div>
        </div>

        <template #footer>
            <a-space>
                <a-button @click="handleCancel">{{ readOnlyMode ? '关闭' : '取消' }}</a-button>
                <template v-if="!readOnlyMode">
                    <a-button type="primary" @click="handleSubmit">提交</a-button>
                    <a-button @click="handleSave">暂存</a-button>
                </template>
            </a-space>
        </template>

        <!-- 选择楼栋弹窗 -->
        <a-modal v-model:visible="buildingDialogVisible" :width="'700px'" title="资产处置-选择楼栋" :footer="false"
            @cancel="handleBuildingCancel" @open="handleBuildingDialogOpen">
            <a-form :model="buildingForm" :label-col-props="{ span: 4 }" :wrapper-col-props="{ span: 20 }"
                auto-label-width>
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item field="projectId" label="所属项目">
                            <a-select :disabled="true" v-model="buildingForm.projectId" placeholder="请选择项目">
                                <a-option :value="projectForm.project">{{ projectForm.projectName }}</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item field="buildingName" label="楼栋名称">
                            <a-input v-model="buildingForm.buildingName" placeholder="请输入楼栋名称" allow-clear
                                @change="handleBuildingNameChange" />
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>

            <a-card style="position: relative; min-height: 300px;">
                <a-spin :loading="buildingLoading">
                    <div class="empty-container" v-if="treeData.length === 0">
                        <a-empty class="centered-empty" description="暂无楼栋数据">
                            <template #image>
                                <icon-folder style="font-size: 48px; color: var(--color-text-3)" />
                            </template>
                        </a-empty>
                    </div>
                    <a-tree v-else ref="treeRef" blockNode checkable v-model:checked-keys="checkedKeys"
                        checked-strategy="child" :data="treeData" :virtualListProps="{ height: 300 }" :show-line="true"
                        :auto-expand-parent="true" :field-names="{
                            key: 'key',
                            title: 'title',
                            children: 'children'
                        }">
                    </a-tree>
                </a-spin>
            </a-card>

            <div class="modal-footer">
                <a-space>
                    <a-button @click="handleBuildingCancel">取消</a-button>
                    <a-button type="primary" @click="handleBuildingNext">下一步</a-button>
                </a-space>
            </div>
        </a-modal>

        <!-- 选择房源弹窗 -->
        <a-modal v-model:visible="roomDialogVisible" title="资产处置-选择房源" :width="1000" :body-style="{ maxHeight: '80vh' }"
            @cancel="handleRoomCancel">
            <a-form :model="currentRoomForm" auto-label-width>
                <a-row :gutter="16">
                    <a-col :span="8">
                        <a-form-item label="是否公司自持" field="company">
                            <a-select v-model="currentRoomForm.company" placeholder="全部">
                                <a-option value="1">是</a-option>
                                <a-option value="0">否</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="产品类型" field="productType">
                            <a-select v-model="currentRoomForm.productType" placeholder="全部">
                                <a-option value="all">全部</a-option>
                                <a-option v-for="item in product_type" :key="item.value" :value="item.value">
                                    {{ item.label }}
                                </a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="房源名称" field="roomName">
                            <a-input v-model="currentRoomForm.roomName" placeholder="请输入房源名称" allow-clear />
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>

            <div class="room-selection">
                <div class="building-container" v-for="building in currentRoomForm.roomData" :key="building.id">

                    <div class="select-all-container">
                        <a-checkbox v-model="currentRoomForm.selectAll">全选</a-checkbox>
                    </div>

                    <div class="building-header">
                        <a-checkbox v-model="currentRoomForm.buildingSelect[building.id]"
                            @change="(checked: boolean) => handleBuildingChange(building.id, checked)">
                            {{ building.buildingName }}
                        </a-checkbox>
                    </div>

                    <div class="room-table">
                        <!-- 表头 -->
                        <div class="table-header">
                            <div class="floor-header">楼层</div>
                            <div class="room-header">房间</div>
                        </div>

                        <!-- 表格内容 -->
                        <div class="table-content">
                            <div v-for="floor in building.floors" :key="floor.id" class="table-row">
                                <div class="floor-cell">
                                    <a-checkbox v-model="currentRoomForm.rooms[`${building.id}_${floor.id}`]"
                                        @change="(checked: boolean) => handleFloorChange(building.id, floor.id, checked)">
                                        {{ floor.floorName }}
                                    </a-checkbox>
                                </div>
                                <div class="room-cell">
                                    <a-checkbox v-for="room in floor.rooms" :key="room.id"
                                        v-model="currentRoomForm.rooms[`${building.id}_${floor.id}_${room.id}`]"
                                        @change="() => handleRoomChange(building.id, floor.id)" class="room-checkbox">
                                        {{ room.roomName }}
                                    </a-checkbox>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="currentRoomForm.roomData.length === 0" class="empty-data"
                    style="position: relative; min-height: 300px;">
                    <div class="empty-container">
                        <a-empty class="centered-empty" description="暂无房间数据" />
                    </div>
                </div>

            </div>

            <template #footer>
                <a-space>
                    <a-button @click="handleRoomPrev">上一步</a-button>
                    <a-button type="primary" @click="handleRoomNext">下一步</a-button>
                    <a-button @click="handleRoomCancel">取消</a-button>
                </a-space>
            </template>
        </a-modal>
    </a-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconPlus, IconDelete, IconFolder } from '@arco-design/web-vue/es/icon'
import { addDisposal, updateDisposal, getBuildingTree, getRoomTree, getDisposalDetail } from '@/api/asset/projectDisposal'
import uploadFile from '@/components/upload/uploadFile.vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useDict, getDictLabel } from '@/utils/dict'

const { disposal_type, product_type } = useDict('disposal_type', 'product_type');

// 产品类型转换
const getProductTypeText = (value: string | number) => {
    if (!value) return '-'
    const item = product_type.value.find((item: any) => item.value === String(value))
    return item ? item.label : value
}

interface Props {
    visible: boolean
    projectId: string
    projectName?: string
    selectedRooms?: Record<string, boolean>
    roomData?: any[]
    id?: string // 资产处置记录ID，用于编辑模式
    isEdit?: boolean // 是否为编辑模式
    readOnly?: boolean // 是否为只读模式
}

interface Emits {
    (e: 'update:visible', visible: boolean): void
    (e: 'submit', formData: any): void
    (e: 'cancel'): void
}

const isEdit = ref(false); // 用响应式变量处置 props 的值
const formId = ref(''); // 用响应式变量处置 props 的 id 值

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    projectName: '',
    selectedRooms: () => ({}),
    roomData: () => [],
    id: '',
    readOnly: false
})

const emit = defineEmits<Emits>()

const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})

// 监听项目ID和名称变化
watch(() => props.projectId, (newVal) => {
    if (newVal) {
        projectForm.project = newVal;
    }
})

watch(() => props.projectName, (newVal) => {
    if (newVal) {
        projectForm.projectName = newVal;
    }
})

// 定义房间数据结构
const roomForm = reactive({
    roomData: props.roomData || [] as any[]
})

// 监听房间数据变化
watch(() => props.roomData, (newData) => {
    if (newData && newData.length > 0) {
        roomForm.roomData = newData;
    }
}, { immediate: true, deep: true })

// 监听弹窗显示状态，确保每次打开都重置选中状态
watch(() => dialogVisible.value, (newVisible) => {
    if (newVisible) {
        // 弹窗打开时，确保选中状态被重置
        selectedKeys.value = []
        searchKeyword.value = ''
        console.log('弹窗打开，重置选中状态和搜索关键词')
    }
})

// 监听选中的房源数据变化
watch(() => props.selectedRooms, (newRooms) => {
    nextTick(() => {
        if (newRooms && Object.keys(newRooms).length > 0) {
            loadSelectedRooms(newRooms);
        }
    })
}, { immediate: true, deep: true })

// 项目表单数据
const projectForm = reactive({
    project: props.projectId || '',
    projectName: props.projectName || '未知项目'
})

// 表单校验规则
const formRef = ref()
const formRules = {
    disposalMethod: [
        { required: true, message: '请选择处置方式', trigger: ['change', 'blur'] }
    ],
    receptionDate: [
        { required: true, message: '请选择处置日期', trigger: ['change', 'blur'] }
    ]
}

// 表格列定义
const columns = [
    { title: '序号', dataIndex: 'index', width: 100, align: 'center' },
    { title: '房源名称', dataIndex: 'roomName', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '所属地块', dataIndex: 'parcelName', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '所属楼栋', dataIndex: 'building', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '楼层', dataIndex: 'floor', width: 100, align: 'center' },
    { title: '产品类型', dataIndex: 'productType', width: 100, align: 'center', ellipsis: true, tooltip: true, slotName: 'productType' },
    { title: '面积类型', dataIndex: 'areaTypeName', width: 100, align: 'center' },
    { title: '建筑面积', dataIndex: 'buildArea', width: 100, align: 'center' },
    { title: '室内面积', dataIndex: 'innerArea', width: 100, align: 'center' },
    { title: '是否可售', dataIndex: 'isSaleable', width: 100, align: 'center' },
    { title: '是否公司自持', dataIndex: 'isCompanyHold', width: 120, align: 'center' },
    { title: '自持物业类型', dataIndex: 'propertyTypeName', width: 120, align: 'center' },
    { title: '自持到期时间', dataIndex: 'selfHoldingTime', width: 120, align: 'center' },
    { title: '接收日期', dataIndex: 'receiveDate', width: 120, align: 'center' },
    { title: '操作', slotName: 'operation', width: 100, fixed: 'right', align: 'center' }
]

// 选择行配置
const selectedKeys = ref<string[]>([])
const rowSelection = {
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
}
// 多选框选中数据
const handleSelectionChange = (rowKeys: string[]) => {
    selectedKeys.value = rowKeys
};;

// 是否有选中行
const hasSelected = computed(() => {
    const result = selectedKeys.value && selectedKeys.value.length > 0
    console.log('hasSelected计算：', {
        selectedKeys: selectedKeys.value,
        result
    })  // 添加调试日志
    return result
})

// 搜索功能
const handleSearch = (value: string) => {
    searchKeyword.value = value
    if (!value.trim()) {
        // 如果搜索关键词为空，显示所有数据
        tableData.value = [...originalTableData.value]
    } else {
        // 根据楼栋名称和房源名称进行筛选
        const keyword = value.trim().toLowerCase()
        tableData.value = originalTableData.value.filter(item => {
            const buildingMatch = item.building && item.building.toLowerCase().includes(keyword)
            const roomMatch = item.roomName && item.roomName.toLowerCase().includes(keyword)
            return buildingMatch || roomMatch
        })
    }

    // 搜索后清空选中状态
    selectedKeys.value = []
    console.log('搜索完成，当前显示数据数量:', tableData.value.length)
}

// 清空搜索
const handleClearSearch = () => {
    searchKeyword.value = ''
    tableData.value = [...originalTableData.value]
    selectedKeys.value = []
    console.log('清空搜索，显示所有数据，数量:', tableData.value.length)
}

// 添加资产
const buildingDialogVisible = ref(false)
const buildingForm = reactive({
    projectId: '',
    buildingName: '',
    selectedBuildings: [] as string[]
})
const checkedKeys = ref<string[]>([])
const treeData = ref<any[]>([])
const buildingLoading = ref(false)

// 房间选择弹窗相关
const roomDialogVisible = ref(false)
const currentRoomForm = reactive({
    company: '',
    productType: '',
    roomName: '',
    selectAll: false,
    rooms: {} as Record<string, boolean>,
    buildingSelect: {} as Record<string, boolean>,
    roomData: [] as any[]
})

// 初始化时设置项目ID
onMounted(() => {
    buildingForm.projectId = projectForm.project
})

const handleAddAsset = () => {
    // 打开选择楼栋弹窗，并设置当前项目
    buildingForm.projectId = projectForm.project
    buildingForm.buildingName = ''
    buildingForm.selectedBuildings = []
    checkedKeys.value = []
    buildingDialogVisible.value = true

    // 清空房间选择数据
    currentRoomForm.company = ''
    currentRoomForm.productType = ''
    currentRoomForm.roomName = ''
    currentRoomForm.selectAll = false
    currentRoomForm.rooms = {}
    currentRoomForm.buildingSelect = {}
    currentRoomForm.roomData = []

    // 在弹窗打开后加载楼栋数据
    loadBuildingTree()
}

// 移除单个资产
const handleRemove = (record: any) => {
    const index = tableData.value.findIndex(item => item.index === record.index)
    if (index !== -1) {
        tableData.value.splice(index, 1)
        // 同时从原始数据中移除
        const originalIndex = originalTableData.value.findIndex(item => item.index === record.index)
        if (originalIndex !== -1) {
            originalTableData.value.splice(originalIndex, 1)
        }

        // 更新序号
        tableData.value.forEach((item, i) => {
            item.index = i + 1
        })
        originalTableData.value.forEach((item, i) => {
            item.index = i + 1
        })
    }
}

// 批量移除
const handleBatchRemove = () => {
    if (selectedKeys.value.length === 0) return
    console.log('selectedKeys.value', selectedKeys.value)

    // 从显示数据中移除
    tableData.value = tableData.value.filter(item => !selectedKeys.value.includes(item.index))
    // 从原始数据中移除
    originalTableData.value = originalTableData.value.filter(item => !selectedKeys.value.includes(item.index))

    // 更新序号
    tableData.value.forEach((item, i) => {
        item.index = i + 1
    })
    originalTableData.value.forEach((item, i) => {
        item.index = i + 1
    })

    selectedKeys.value = []
}

// 表格数据
const tableData = ref<any[]>([])
// 原始表格数据（用于搜索筛选）
const originalTableData = ref<any[]>([])
// 搜索关键词
const searchKeyword = ref('')

// 只读模式标志
const readOnlyMode = ref(false)

const formData = reactive({
    disposalMethod: '',
    receptionDate: '',
    remark: '',
    disposalDescription: '',
    attachment: '' // 添加附件字段
})

const handleCancel = () => {
    emit('cancel')
    resetAll()
}

// 重置所有数据
const resetAll = () => {
    // 重置表单数据
    formData.disposalMethod = ''
    formData.receptionDate = ''
    formData.remark = ''
    formData.disposalDescription = ''
    formData.attachment = '' // 重置附件字段

    // 重置表格数据和选中状态
    tableData.value = []
    originalTableData.value = []
    selectedKeys.value = []
    searchKeyword.value = ''

    // 重置状态
    isEdit.value = false
    formId.value = ''
    readOnlyMode.value = false

    // 重置楼栋和房间选择状态
    buildingForm.buildingName = ''
    buildingForm.selectedBuildings = []
    checkedKeys.value = []

    // 重置房间选择数据
    currentRoomForm.company = ''
    currentRoomForm.productType = ''
    currentRoomForm.roomName = ''
    currentRoomForm.selectAll = false
    currentRoomForm.rooms = {}
    currentRoomForm.buildingSelect = {}
    currentRoomForm.roomData = []

    // 关闭所有子弹窗
    buildingDialogVisible.value = false
    roomDialogVisible.value = false
}

const handleSubmit = async () => {
    // 表单校验
    try {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('表单校验失败:', errors)
            Message.warning('表单校验失败，请完善必填信息')
            return
        }

        // 检查是否有选中的房间
        if (tableData.value.length === 0) {
            Message.warning('请至少选择一个房间');
            return;
        }

        // 构建提交数据
        const submitData: any = {
            projectId: projectForm.project,
            disposalMethod: formData.disposalMethod,
            disposalDate: formData.receptionDate,
            disposalDescription: formData.disposalDescription,
            remark: formData.remark,
            attachment: formData.attachment, // 添加附件数据
            disposalBuilding: tableData.value.map(item => item.building).join(','),
            disposalRoomCount: tableData.value.length,
            roomIds: tableData.value.map(item => item.roomId ? item.roomId.toString() : item.index.toString()),
            operationType: 2 // 提交操作
        }
        console.log('提交数据 - isEdit:', isEdit.value, 'formId:', formId.value)
        // 如果是编辑模式，添加ID
        if (isEdit.value && formId.value) {
            submitData.id = formId.value
        }

        console.log('提交资产处置数据:', submitData)
        if (submitData.attachment === '') { 
            delete submitData.attachment
        }        // 调用API
        const response = isEdit.value
            ? await updateDisposal(submitData)
            : await addDisposal(submitData)

        if (response && response.code === 200) {
            Message.success('提交成功')
            emit('submit', submitData)
            resetAll()
            dialogVisible.value = false // 关闭弹窗
        } else {
            // Message.error('提交失败: ' + (response?.msg || '未知错误'))
        }
    } catch (error) {
        console.error('提交资产处置失败:', error)
    }
}

const handleSave = async () => {
    try {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('表单校验失败:', errors)
            Message.warning('表单校验失败，请完善必填信息')
            return
        }

        // 构建暂存数据
        const saveData: any = {
            projectId: projectForm.project,
            disposalMethod: formData.disposalMethod || '',
            disposalDate: formData.receptionDate || '',
            disposalDescription: formData.disposalDescription || '',
            remark: formData.remark || '',
            attachment: formData.attachment || '', // 添加附件数据
            disposalBuilding: tableData.value.map(item => item.building).join(','),
            disposalRoomCount: tableData.value.length,
            roomIds: tableData.value.map(item => item.roomId ? item.roomId.toString() : item.index.toString()),
            operationType: 1 // 暂存操作
        }

        // 如果是编辑模式，添加ID
        if (isEdit.value && formId.value) {
            saveData.id = formId.value
        }
        if (saveData.attachment === '') { 
            delete saveData.attachment
        }  
        console.log('暂存数据 - isEdit:', isEdit.value, 'formId:', formId.value)
        console.log('暂存资产处置数据:', saveData)

        // 调用API
        const response = isEdit.value
            ? await updateDisposal(saveData)
            : await addDisposal(saveData)

        if (response && response.code === 200) {
            Message.success('暂存成功')
            emit('submit', saveData) // 触发父组件刷新列表
            dialogVisible.value = false // 关闭弹窗
        } else {
            // Message.error('暂存失败: ' + (response?.msg || '未知错误'))
        }
    } catch (error) {
        console.error('暂存资产处置失败:', error)
    }
}

// 加载选中的房间数据
const loadSelectedRooms = (selectedRooms: Record<string, boolean>) => {
    // 清空原有数据
    tableData.value = [];
    originalTableData.value = [];

    // 转换选中的房间数据为表格数据
    const selectedEntries = Object.entries(selectedRooms).filter(([key, selected]) => {
        // 只处理选中的且是房间级别的数据（key格式：buildingId_floorId_roomId）
        return selected && key.split('_').length === 3;
    });

    console.log('处理选中的房间数据，数量:', selectedEntries.length);

    if (selectedEntries.length === 0) {
        console.log('没有选中的房间数据');
        return;
    }

    // 从房源数据中获取选中的房间详细信息
    selectedEntries.forEach(([key], index) => {
        const [buildingId, floorId, roomId] = key.split('_');

        // 尝试从房源数据中查找详细信息
        try {
            if (roomForm.roomData && roomForm.roomData.length > 0) {
                // 查找对应的楼栋
                const building = roomForm.roomData.find((b: any) => b.id === buildingId);
                if (building && building.floors) {
                    // 查找对应的楼层
                    const floor = building.floors.find((f: any) => f.id === floorId);
                    if (floor && floor.rooms) {
                        // 查找对应的房间
                        const room = floor.rooms.find((r: any) => r.id === roomId);
                        if (room) {
                            // 添加详细房间信息到表格
                            const roomData = {
                                index: index + 1,
                                roomName: room.roomName || ``,
                                building: building.buildingName || '',
                                floor: floor.floorName || ``,
                                productType: room.productType || '',
                                areaTypeName: room.areaTypeName || '',
                                buildArea: room.buildArea || '0',
                                innerArea: room.innerArea || '0',
                                isSaleable: room.isSale ? '是' : '否',
                                isCompanyHold: room.isCompanySelf ? '是' : '否',
                                selfHoldingTime: room.selfHoldingTime || '',
                                roomId: roomId,
                                parcelName: room.parcelName || '',
                                propertyTypeName: room.propertyTypeName || (room.propertyType === 1 ? '非自持' : '自持')
                            };
                            originalTableData.value.push(roomData);
                            return; // 找到了房间，处理下一个
                        }
                    }
                }
            }
        } catch (error) {
            console.error('处理房间数据时出错:', error);
            
        }
    });

    // 复制到当前显示数据
    tableData.value = [...originalTableData.value];

    // 重置选中状态和搜索关键词
    selectedKeys.value = [];
    searchKeyword.value = '';

    console.log('表格数据加载完成，房间数:', tableData.value.length);
}

// 添加buildingTree相关API实现
const loadBuildingTree = async () => {
    console.log('加载楼栋树，项目ID:', buildingForm.projectId)

    // 如果没有项目ID，显示提示并返回
    if (!buildingForm.projectId) {
        console.warn('没有项目ID，请先选择项目')
        treeData.value = []
        return
    }

    // 设置加载状态
    buildingLoading.value = true

    try {
        console.log('调用getBuildingTree API，参数:', buildingForm.projectId, buildingForm.buildingName || '')

        const response = await getBuildingTree(buildingForm.projectId, buildingForm.buildingName || '')
        console.log('楼栋树API响应:', response)

        // 处理API响应数据
        let dataToProcess = []

        if (response) {
            // 处理不同的响应结构
            if (Array.isArray(response)) {
                // 直接是数组
                dataToProcess = response
            } else if (response.code === 200 && response.data) {
                // 标准API响应格式
                if (Array.isArray(response.data)) {
                    dataToProcess = response.data
                } else if (typeof response.data === 'object') {
                    // 可能嵌套在data中
                    const nestedData = response.data.list || response.data.rows || response.data
                    if (Array.isArray(nestedData)) {
                        dataToProcess = nestedData
                    } else {
                        dataToProcess = [response.data]
                    }
                }
            } else if (response.rows && Array.isArray(response.rows)) {
                dataToProcess = response.rows
            } else if (response.list && Array.isArray(response.list)) {
                dataToProcess = response.list
            }
        }

        console.log('提取的数据长度:', dataToProcess.length)

        if (dataToProcess.length === 0) {
            treeData.value = []
            Message.info('当前项目没有楼栋数据')
        } else {
            // 格式化树数据
            const formattedTreeData = formatTreeData(dataToProcess)
            treeData.value = formattedTreeData
        }
    } catch (error) {
        console.error('获取楼栋数据失败:', error)
        Message.error('获取楼栋数据失败')
        treeData.value = []
    } finally {
        buildingLoading.value = false
    }
}

// 格式化树形数据
const formatTreeData = (data: any[]) => {
    if (!data || data.length === 0) {
        return []
    }

    try {
        return data
            .filter(parcel => parcel !== null && parcel !== undefined)
            .map(parcel => {
                // 创建唯一标识符
                const parcelId = parcel.id || parcel.parcelId || `parcel_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
                const parcelName = parcel.parcelName || parcel.name || '未命名地块'

                // 创建地块节点
                const parcelNode = {
                    key: `parcel_${parcelId}`,
                    title: parcelName,
                    // selectable: false,
                    children: []
                }

                // 处理楼栋数据
                if (parcel.buildings && Array.isArray(parcel.buildings)) {
                    // 过滤掉null或undefined的楼栋
                    const validBuildings = parcel.buildings.filter((b: any) => b !== null && b !== undefined)

                    if (validBuildings.length > 0) {
                        parcelNode.children = validBuildings.map((building: any) => {
                            const buildingId = building.id || building.buildingId || `building_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
                            const buildingName = building.buildingName || building.name || '未命名楼栋'

                            // 创建楼栋节点
                            const buildingNode: any = {
                                key: String(buildingId),
                                title: buildingName,
                                selectable: true,
                                children: []
                            }

                            // 处理楼层数据
                            if (building.floors && Array.isArray(building.floors)) {
                                const validFloors = building.floors.filter((f: any) => f !== null && f !== undefined)

                                if (validFloors.length > 0) {
                                    buildingNode.children = validFloors.map((floor: any) => {
                                        const floorId = floor.id || floor.floorId || `floor_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
                                        const floorName = floor.floorName || floor.name || '未命名楼层'

                                        return {
                                            key: `floor_${floorId}`,
                                            title: floorName,
                                            // selectable: false,
                                            isLeaf: true
                                        }
                                    })
                                }
                            }

                            return buildingNode
                        })
                    }
                }

                return parcelNode
            })
    } catch (error) {
        console.error('格式化树数据出错:', error)
        return []
    }
}

// 加载房间树
const loadRoomTree = async () => {
    if (!buildingForm.selectedBuildings || buildingForm.selectedBuildings.length === 0) {
        currentRoomForm.roomData = []
        return
    }

    try {
        const params = {
            buildingIds: buildingForm.selectedBuildings,
            roomName: currentRoomForm.roomName || undefined,
            productType: currentRoomForm.productType !== 'all' ? currentRoomForm.productType : undefined,
            isSelf: currentRoomForm.company !== 'all' ? currentRoomForm.company : undefined
        }

        console.log('查询房间数据参数:', params)
        const res = await getRoomTree(params)
        console.log('房间数据API响应:', res)

        // 处理API响应
        if (res && Array.isArray(res)) {
            currentRoomForm.roomData = res
            initRoomSelection(res)
        } else if (res && res.code === 200) {
            if (Array.isArray(res.data)) {
                currentRoomForm.roomData = res.data
                initRoomSelection(res.data)
            } else if (res.data && typeof res.data === 'object') {
                const dataSource = res.data.list || res.data.rows || res.data

                if (Array.isArray(dataSource)) {
                    currentRoomForm.roomData = dataSource
                    initRoomSelection(dataSource)
                } else {
                    currentRoomForm.roomData = []
                }
            } else {
                currentRoomForm.roomData = []
            }
        } else {
            currentRoomForm.roomData = []
        }
    } catch (error) {
        console.error('获取房间数据失败:', error)
        Message.error('获取房间数据失败')
        currentRoomForm.roomData = []
    }
}

// 初始化房间选择状态
const initRoomSelection = (buildings: any[]) => {
    console.log('初始化房间选择状态，楼栋数:', buildings.length)

    // 清空之前的选择
    currentRoomForm.rooms = {}
    currentRoomForm.buildingSelect = {}
    currentRoomForm.selectAll = false

    // 为每个楼栋、楼层和房间设置初始选择状态
    buildings.forEach(building => {
        // 初始化楼栋选择状态
        currentRoomForm.buildingSelect[building.id] = false

        if (building.floors && Array.isArray(building.floors)) {
            building.floors.forEach((floor: any) => {
                const floorKey = `${building.id}_${floor.id}`
                currentRoomForm.rooms[floorKey] = false

                if (floor.rooms && Array.isArray(floor.rooms)) {
                    floor.rooms.forEach((room: any) => {
                        const roomKey = `${building.id}_${floor.id}_${room.id}`
                        currentRoomForm.rooms[roomKey] = false
                    })
                }
            })
        }
    })
}

// 选择楼栋弹窗相关方法
const handleBuildingDialogOpen = () => {
    console.log('楼栋弹窗打开')
    loadBuildingTree()
}

const handleBuildingCancel = () => {
    buildingDialogVisible.value = false
    buildingForm.buildingName = ''
    checkedKeys.value = []
}

const handleBuildingNext = () => {
    if (checkedKeys.value.length === 0) {
        Message.warning('请至少选择一个楼栋')
        return
    }

    // 存储选中的楼栋ID
    buildingForm.selectedBuildings = checkedKeys.value.filter(key =>
        !key.toString().startsWith('parcel_') && !key.toString().startsWith('floor_')
    )

    console.log('选中的楼栋:', buildingForm.selectedBuildings)

    // 关闭楼栋弹窗，打开房间弹窗
    buildingDialogVisible.value = false
    roomDialogVisible.value = true

    // 加载房间数据
    loadRoomTree()
}

const handleBuildingNameChange = () => {
    console.log('楼栋名称变更为:', buildingForm.buildingName)
    loadBuildingTree()
}

// 房间选择相关方法
const handleBuildingChange = (buildingId: string, checked: boolean) => {
    console.log(`楼栋 ${buildingId} 选中状态变更为: ${checked}`)

    // 设置该楼栋下所有楼层和房间的选中状态
    Object.keys(currentRoomForm.rooms).forEach(key => {
        if (key.startsWith(`${buildingId}_`)) {
            currentRoomForm.rooms[key] = checked
        }
    })

    // 更新楼栋选中状态
    currentRoomForm.buildingSelect[buildingId] = checked

    // 更新全选状态
    updateSelectAllStatus()
}

const handleFloorChange = (buildingId: string, floorId: string, checked: boolean) => {
    console.log(`楼层 ${buildingId}_${floorId} 选中状态变更为: ${checked}`)

    // 设置该层所有房间的选中状态
    Object.keys(currentRoomForm.rooms).forEach(key => {
        if (key.startsWith(`${buildingId}_${floorId}_`)) {
            currentRoomForm.rooms[key] = checked
        }
    })

    // 更新该楼的选中状态
    currentRoomForm.rooms[`${buildingId}_${floorId}`] = checked

    // 检查该楼栋所有楼层是否都被选中，并更新楼栋状态
    updateBuildingSelectStatus(buildingId)

    // 检查是否所有楼栋都被选中，并更新全选状态
    updateSelectAllStatus()
}

const handleRoomChange = (buildingId: string, floorId: string) => {
    const floorKey = `${buildingId}_${floorId}`

    // 查找属于该楼层的所有房间
    const floorRooms = Object.keys(currentRoomForm.rooms).filter(key =>
        key.startsWith(`${buildingId}_${floorId}_`)
    )

    if (floorRooms.length === 0) return

    // 检查是否所有房间都被选中
    const allRoomsSelected = floorRooms.every(roomKey => currentRoomForm.rooms[roomKey])

    // 更新该层的选中状态
    currentRoomForm.rooms[floorKey] = allRoomsSelected

    // 更新楼栋的选中状态
    updateBuildingSelectStatus(buildingId)

    // 检查是否所有楼栋都被选中，并更新全选状态
    updateSelectAllStatus()
}

// 更新楼栋选中状态
const updateBuildingSelectStatus = (buildingId: string) => {
    // 获取该楼栋下所有楼层的key
    const floorKeys = Object.keys(currentRoomForm.rooms).filter(key =>
        key.startsWith(`${buildingId}_`) && key.split('_').length === 2
    )

    if (floorKeys.length === 0) {
        currentRoomForm.buildingSelect[buildingId] = false
        return
    }

    // 检查该楼栋所有楼层是否都被选中
    const allFloorsSelected = floorKeys.every(key => currentRoomForm.rooms[key])
    currentRoomForm.buildingSelect[buildingId] = allFloorsSelected
}

// 更新全选状态
const updateSelectAllStatus = () => {
    if (currentRoomForm.roomData.length === 0) {
        currentRoomForm.selectAll = false
        return
    }

    // 检查是否所有楼栋都被选中
    currentRoomForm.selectAll = currentRoomForm.roomData.every(building => {
        return currentRoomForm.buildingSelect[building.id] === true
    })
}

const handleRoomPrev = () => {
    roomDialogVisible.value = false
    buildingDialogVisible.value = true
}

const handleRoomNext = () => {
    // 检查是否有选中的房间
    const hasSelectedRooms = Object.entries(currentRoomForm.rooms).some(([key, value]) =>
        value && key.split('_').length === 3 // 只计算房间级别的选择
    )

    if (!hasSelectedRooms) {
        Message.warning('请至少选择一个房间')
        return
    }

    // 添加选中的房间到tableData
    processSelectedRooms()

    // 关闭房间选择弹窗
    roomDialogVisible.value = false
}

const handleRoomCancel = () => {
    roomDialogVisible.value = false
    currentRoomForm.company = ''
    currentRoomForm.productType = ''
    currentRoomForm.roomName = ''
    currentRoomForm.selectAll = false
    currentRoomForm.rooms = {}
    currentRoomForm.roomData = []
}

// 打开详情弹窗
const openDetail = async (detailProps: any) => {
    console.log('打开详情弹窗，参数:', detailProps)

    // 先重置所有数据，避免残留上次的数据
    resetAll()

    // 设置编辑状态
    isEdit.value = detailProps.isEdit || false

    // 保存id值到响应式变量
    formId.value = detailProps.id || ''
    console.log('设置formId:', formId.value)

    // 设置项目信息
    if (detailProps.projectId) {
        projectForm.project = detailProps.projectId
    }

    if (detailProps.projectName) {
        projectForm.projectName = detailProps.projectName
    }

    // 设置只读状态
    if (detailProps.readOnly !== undefined) {
        // 注意：props是只读的，这里我们直接使用detailProps中的值
        readOnlyMode.value = detailProps.readOnly
    }

    // 显示弹窗
    dialogVisible.value = true

    // 获取详情数据
    if (formId.value) {
        try {
            const response = await getDisposalDetail(formId.value)

            if (response && response.code === 200 && response.data) {
                const detailData = response.data
                // 填充表单数据
                formData.disposalMethod = detailData.disposalInfo.disposalMethod || ''
                formData.receptionDate = detailData.disposalInfo.disposalDate || ''
                formData.disposalDescription = detailData.disposalInfo.disposalDescription || ''
                formData.remark = detailData.disposalInfo.remark || ''
                formData.attachment = detailData.disposalInfo.attachment || ''

                // 加载房间数据
                if (detailData.roomList && Array.isArray(detailData.roomList)) {
                    const roomsData = detailData.roomList.map((room: any, index: number) => ({
                        index: index + 1,
                        roomName: room.roomName || ``,
                        building: room.buildingName || ``,
                        floor: room.floorName || ``,
                        productType: room.productType || '',
                        areaTypeName: room.areaTypeName || '',
                        buildArea: room.buildArea || '',
                        innerArea: room.innerArea || '',
                        isSaleable: room.isSale ? '是' : '否',
                        isCompanyHold: room.isCompanySelf? '是' : '否',
                        holdEndTime: room.selfHoldingTime || '',
                        roomId: room.id,
                        parcelName: room.parcelName || '',
                        propertyTypeName: room.propertyTypeName || ''
                    }))

                    // 同时更新原始数据和显示数据
                    originalTableData.value = roomsData
                    tableData.value = [...roomsData]

                    // 重置选中状态和搜索关键词
                    selectedKeys.value = []
                    searchKeyword.value = ''
                }

                console.log('加载资产处置详情成功')
                console.log('selectedKeys.value', selectedKeys.value)
            } else {
                Message.error('加载资产处置详情失败: ' + (response?.msg || '未知错误'))
            }
        } catch (error) {
            console.error('加载资产处置详情失败:', error)
            Message.error('加载资产处置详情失败，请稍后重试')
        }
    }
}



// 暴露方法给父组件
defineExpose({
    openDetail
})

// 处理选中的房间，添加到表格数据中
const processSelectedRooms = () => {
    // 获取选中的房间
    const selectedEntries = Object.entries(currentRoomForm.rooms).filter(([key, selected]) =>
        selected && key.split('_').length === 3
    )

    console.log('处理选中的房间，数量:', selectedEntries.length)

    if (selectedEntries.length === 0) return

    // 逐个处理选中的房间
    selectedEntries.forEach(([key], index) => {
        const [buildingId, floorId, roomId] = key.split('_')

        // 查找房间详细信息
        try {
            if (currentRoomForm.roomData && currentRoomForm.roomData.length > 0) {
                const building = currentRoomForm.roomData.find((b: any) => b.id === buildingId)
                if (building && building.floors) {
                    const floor = building.floors.find((f: any) => f.id === floorId)
                    if (floor && floor.rooms) {
                        const room = floor.rooms.find((r: any) => r.id === roomId)
                        if (room) {
                            // 添加房间到表格数据
                            const newItem = {
                                index: tableData.value.length + 1,
                                roomName: room.roomName || `房间${roomId}`,
                                building: building.buildingName || `楼栋${buildingId}`,
                                floor: floor.floorName || `楼层${floorId}`,
                                productType: room.productType || '联合办公',
                                areaType: room.areaType || '实际',
                                buildingArea: room.buildingArea || '100',
                                indoorArea: room.indoorArea || '90',
                                isSaleable: room.isSaleable === 1 ? '是' : '否',
                                isCompanyHold: room.isCompanyHold === 1 ? '是' : '否',
                                holdEndTime: room.holdEndTime || '2044-05-11',
                                roomId: roomId,
                                propertyTypeName: room.propertyTypeName || ''
                            }

                            // 检查是否已存在相同房间
                            const exists = tableData.value.some(item =>
                                item.roomId === roomId &&
                                item.building === newItem.building &&
                                item.floor === newItem.floor
                            )

                            if (!exists) {
                                tableData.value.push(newItem)
                                originalTableData.value.push(newItem)
                            }

                            return
                        }
                    }
                }
            }

            // 如果找不到详细信息，使用默认值
            const newItem = {
                index: tableData.value.length + 1,
                roomName: `房间${roomId}`,
                building: `楼栋${buildingId}`,
                floor: `楼层${floorId}`,
                productType: '联合办公',
                areaType: '实际',
                buildingArea: '100',
                indoorArea: '90',
                isSaleable: '否',
                isCompanyHold: '是',
                holdEndTime: '2044-05-11',
                roomId: roomId
            }

            // 检查是否已存在相同房间
            const exists = tableData.value.some(item =>
                item.roomId === roomId &&
                item.building === newItem.building &&
                item.floor === newItem.floor
            )

            if (!exists) {
                tableData.value.push(newItem)
                originalTableData.value.push(newItem)
            }
        } catch (error) {
            console.error('处理房间数据时出错:', error)
        }
    })

    // 更新序号
    tableData.value.forEach((item, i) => {
        item.index = i + 1
    })
    originalTableData.value.forEach((item, i) => {
        item.index = i + 1
    })

    console.log('添加房间完成，表格数据数量:', tableData.value.length)
}


</script>

<style scoped>
.section-title {
    margin-bottom: 16px;
}


.reception-list,
.reception-info {
    margin-bottom: 16px;
}

.list-title,
.info-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
    color: #1d2129;
}

.search-box {
    width: 360px;
}

.modal-footer {
    margin-top: 16px;
    text-align: right;
    border-top: 1px solid #e5e6eb;
    padding-top: 16px;
}

.upload-trigger {
    color: #165dff;
    cursor: pointer;
}

.project-info {
    margin-bottom: 24px;
}

.list-header {
    margin-bottom: 16px;
}

.list-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

:deep(.arco-table-size-small .arco-table-th) {
    background-color: #f2f3f5;
}

:deep(.arco-form-item-label-col) {
    text-align: right;
}

/* 选择楼栋和房间弹窗样式 */
.empty-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    width: 100%;
}

:deep(.arco-empty) {
    padding: 0;
    textAlign: center;
}

:deep(.centered-empty) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.centered-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.room-selection {
    .building-container {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .building-header {
        font-weight: bold;
        font-size: 16px;
        color: rgb(var(--primary-6));
        padding: 8px;
        background-color: #f5f7fa;
        border-radius: 4px 4px 0 0;
        border: 1px solid var(--color-neutral-3);
    }

    .select-all-container {
        padding: 8px 0;
    }

    .room-table {
        border: 1px solid var(--color-neutral-3);
        border-top: none;
        border-radius: 0 0 4px 4px;
        overflow: hidden;

        .table-header {
            display: flex;
            background-color: #e8f3ff;
            border-bottom: 1px solid var(--color-neutral-3);

            .floor-header,
            .room-header {
                padding: 8px 16px;
                font-weight: bold;
            }

            .floor-header {
                width: 100px;
                border-right: 1px solid var(--color-neutral-3);
            }

            .room-header {
                flex: 1;
            }
        }

        .table-content {
            max-height: 300px;
            overflow-y: auto;

            .table-row {
                display: flex;
                border-bottom: 1px solid var(--color-neutral-3);

                &:last-child {
                    border-bottom: none;
                }

                .floor-cell {
                    width: 100px;
                    border-right: 1px solid var(--color-neutral-3);
                    padding: 8px 16px;
                }

                .room-cell {
                    flex: 1;
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                    gap: 8px;
                    padding: 8px 16px;
                }
            }
        }
    }

    .room-checkbox {
        margin-right: 8px;
    }
}

.empty-data {
    padding: 0;
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    margin-top: 16px;
    min-height: 200px;
}

:deep(.arco-tree) {
    padding: 8px;
}
</style>