<template>
    <section>
        <sectionTitle title="合同补充信息" />
        <div class="content">
            <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                <!-- 商铺 -->
                <template v-if="contractPurposeLabel === '商铺'">
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">经营业态</label>
                            <div class="detail-text">{{ getDictLabelUtil(business_format, contractData.bizTypeId || ''
                            ) || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">承租方品牌</label>
                            <div class="detail-text">{{ contractData.lesseeBrand || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">经营品类</label>
                            <div class="detail-text">{{ contractData.businessCategory || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">开业日期</label>
                            <div class="detail-text">{{ contractData.openDate || '' }}</div>
                        </div>
                    </a-grid-item>
                </template>

                <!-- 中央食堂 -->
                <template v-if="contractPurposeLabel === '中央食堂'">
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">开业日期</label>
                            <div class="detail-text">{{ contractData.openDate || '' }}</div>
                        </div>
                    </a-grid-item>
                </template>

                <!-- 厂房 -->
                <template v-if="contractPurposeLabel === '厂房'">
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">生产火灾危险性类别</label>
                            <div class="detail-text">{{ getDictLabelUtil(fire_level,
                                contractData.fireRiskCategory?.toString() ||
                                '') }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">喷淋系统</label>
                            <div class="detail-text">{{ getDictLabelUtil(spray_system,
                                contractData.fireRiskCategory?.toString() ||
                                '') }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">厂房从事</label>
                            <div class="detail-text">{{ contractData.factoryEngaged || '' }}</div>
                        </div>
                    </a-grid-item>
                </template>

                <!-- 综合体 -->
                <template v-if="contractPurposeLabel === '综合体'">
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">经营业态</label>
                            <div class="detail-text">{{ getDictLabelUtil(business_format, contractData.bizTypeId || ''
                            ) || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">承租方品牌</label>
                            <div class="detail-text">{{ contractData.lesseeBrand || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">经营品类</label>
                            <div class="detail-text">{{ contractData.businessCategory || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">交接日期</label>
                            <div class="detail-text">{{ contractData.handoverDate || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">开业日期</label>
                            <div class="detail-text">{{ contractData.openDate || '' }}</div>
                        </div>
                    </a-grid-item>
                </template>

                <!-- 车位 -->
                <template v-if="contractPurposeLabel === '车位'">
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">车位类型</label>
                            <div class="detail-text">{{ getDictLabelUtil(parking_type,
                                contractData.parkingSpaceType?.toString() ||
                                '') }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">是否包含车位管理费</label>
                            <div class="detail-text">{{ contractData.hasParkingFee ? '是' : '否' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item v-if="contractData.hasParkingFee">
                        <div class="form-item">
                            <label class="form-label">车位管理费</label>
                            <div class="detail-text">{{ contractData.parkingFeeAmount == null ? '' :
                                contractData.parkingFeeAmount + '元/月' }}</div>
                        </div>
                    </a-grid-item>
                </template>

                <!-- 广告位、设备类、其他 -->
                <template v-if="['广告位', '设备类', '其他'].includes(contractPurposeLabel)">
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">场地交付日期</label>
                            <div class="detail-text">{{ contractData.venueDeliveryDate || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">场地交付位置</label>
                            <div class="detail-text">{{ contractData.venueLocation || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">每日活动开展时间</label>
                            <div class="detail-text">{{ contractData.dailyActivityStartTime || '' }} - {{
                                contractData.dailyActivityEndTime || '' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">场地用途</label>
                            <div class="detail-text">{{ contractData.venuePurpose || '' }}</div>
                        </div>
                    </a-grid-item>
                </template>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import { getDictLabel } from '@/dict'
import { ContractAddDTO } from '@/api/contract'
import { useDict, getDictLabel as getDictLabelUtil, useDictTree } from '@/utils/dict'

const contractPurposeLabel = computed(() => {
    return getDictLabel('diversification_purpose', Number(contractData.value.contractPurpose))
})

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

const { parking_type, fire_level, spray_system, } = useDict('parking_type', 'fire_level', 'spray_system')
const { business_format } = useDictTree('business_format')
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.form-item {
    margin-bottom: 16px;

    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>