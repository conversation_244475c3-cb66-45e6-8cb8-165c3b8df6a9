<template>
  <a-table
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :bordered="{ cell: true }"
    :scroll="{ x: 1 }"
    row-key="id"
  >
    <template #bankNameTitle>
      <span style="color: red;">*</span>银行
    </template>
    
    <template #accountNumberTitle>
      <span style="color: red;">*</span>账号
    </template>
    
    <template #bankName="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model.trim="record.bankName" 
        placeholder="请输入银行"
        size="small"
      />
      <span v-else>{{ record.bankName }}</span>
    </template>
    
    <template #accountNumber="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.accountNumber" 
        placeholder="请输入账号"
        size="small"
      />
      <span v-else>{{ record.accountNumber }}</span>
    </template>
    
    <template #accountRemark="{ record, rowIndex }">
      <a-textarea 
        v-if="record.isEditing" 
        v-model="record.accountRemark" 
        placeholder="请输入备注"
        :rows="1"
        size="small"
      />
      <span v-else>{{ record.accountRemark }}</span>
    </template>
    
    <template #operations="{ record, rowIndex }">
      <a-space v-if="!readonly">
        <a-button 
          v-if="record.isEditing" 
          type="text" 
          size="small" 
          @click="saveRow(record, rowIndex)"
        >
          保存
        </a-button>
        <a-button 
          v-else
          type="text" 
          size="small" 
          @click="editRow(record)"
        >
          修改
        </a-button>
        
        <a-button 
          type="text" 
          size="small" 
          status="danger" 
          @click="deleteRow(rowIndex)"
        >
          删除
        </a-button>
      </a-space>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Message } from '@arco-design/web-vue'

interface BankAccount {
  id: number
  bankName: string
  accountNumber: string
  accountRemark: string
  isEditing?: boolean
}

interface Props {
  modelValue: BankAccount[]
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  readonly: false
})

const emit = defineEmits(['update:modelValue'])

const tableData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const columns = computed(() => {
  const baseColumns: any[] = [
    {
      title: '银行',
      dataIndex: 'bankName',
      slotName: 'bankName',
      width: 200,
      titleSlotName: 'bankNameTitle',
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '账号',
      dataIndex: 'accountNumber',
      slotName: 'accountNumber',
      width: 220,
      titleSlotName: 'accountNumberTitle',
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '备注',
      dataIndex: 'accountRemark',
      slotName: 'accountRemark',
      width: 180,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    }
  ]
  
  // 只有非只读模式才显示操作列
  if (!props.readonly) {
    baseColumns.push({
      title: '操作',
      slotName: 'operations',
      width: 120,
      fixed: 'right',
      align: 'center'
    })
  }
  
  return baseColumns
})

const editRow = (record: BankAccount) => {
  record.isEditing = true
}

const saveRow = (record: BankAccount, index: number) => {
  if (!record.bankName) {
    Message.warning('请输入银行')
    return
  }
  if (!record.accountNumber) {
    Message.warning('请输入账号')
    return
  }
  
  record.isEditing = false
  Message.success('保存成功')
}

const deleteRow = (index: number) => {
  const newData = [...tableData.value]
  newData.splice(index, 1)
  tableData.value = newData
  Message.success('删除成功')
}
</script>

<style scoped lang="less">
:deep(.arco-table-td) {
  padding: 8px !important;
}

:deep(.arco-input),
:deep(.arco-select),
:deep(.arco-textarea) {
  border: none;
  box-shadow: none;
}
</style> 