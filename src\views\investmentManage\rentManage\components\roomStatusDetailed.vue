<template>
  <div class="room-status-detailed">
    <!-- 状态切换按钮区 -->
    <div class="status-filters">
      <div class="status-buttons-group">
        <div class="status-buttons-main">
          <div class="status-button all" :class="{ active: queryForm.roomStatus === undefined }"
            @click="setStatusFilter(undefined)">全部 {{ roomStatusCounts.all }}
          </div>
          <div class="status-button vacant" :class="{ active: queryForm.roomStatus === 1 }"
            @click="setStatusFilter(1)">空置 {{ roomStatusCounts.vacant }}</div>
          <div class="status-button in-progress" :class="{ active: queryForm.roomStatus === 2 }"
            @click="setStatusFilter(2)">在租 {{ roomStatusCounts.inProgress }}</div>
          <div class="status-button reserved" :class="{ active: queryForm.roomStatus === 3 }"
            @click="setStatusFilter(3)">待生效/签约中/已预订 {{ roomStatusCounts.reserved }}</div>
          <div class="status-button unavailable" :class="{ active: queryForm.roomStatus === 4 }"
            @click="setStatusFilter(4)">不可招商 {{ roomStatusCounts.unavailable }}</div>
        </div>
        <div class="status-buttons-divider"></div>
        <div class="status-buttons-secondary">
          <div class="status-button custom-btn">
            <img src="@/assets/images/rent/expiring.png" class="expiring-icon" />
            即将到期
          </div>
          <div class="status-button custom-btn">
            <span class="triangle-indicator blue"></span>
            自用
          </div>
        </div>
      </div>
      <div class="control-buttons-wrapper">
        <div class="control-label">租控:</div>
        <div class="control-buttons">
          <a-button class="control-button custom-btn" :class="{ checked: queryForm.isLock === true }" @click="handleRentControl('locked')">
            锁房
            <img v-if="queryForm.isLock === true" :src="checkedIcon" class="checked-icon" />
          </a-button>
          <a-button class="control-button custom-btn" :class="{ checked: queryForm.isDirty === true }" @click="handleRentControl('dirty')">
            脏房
            <img v-if="queryForm.isDirty === true" :src="checkedIcon" class="checked-icon" />
          </a-button>
          <a-button class="control-button custom-btn" :class="{ checked: queryForm.isMaintain === true }" @click="handleRentControl('maintenance')">
            维修
            <img v-if="queryForm.isMaintain === true" :src="checkedIcon" class="checked-icon" />
          </a-button>
        </div>
        <div class="control-buttons-divider" style="display: none;"></div>
        <div class="control-label" style="display: none;">预警:</div>
        <div class="control-buttons" style="display: none;">
          <a-button class="control-button custom-btn" :class="{ checked: queryForm.electricityAbnormal === true }" @click="handleWarning('electricity')">
            水电异常
            <img v-if="queryForm.electricityAbnormal === true" :src="checkedIcon" class="checked-icon" />
          </a-button>
        </div>
      </div>
    </div>

    <!-- 筛选区 -->
    <div class="filter-bar-wrapper">
      <div class="filter-bar">
        <div class="filter-group">
          <div class="filter-item">
            <project-tree-select
              v-model="queryForm.projectId"
              style="width: 220px"
              placeholder="请选择项目"
              class="gray-bg-select"
              @change="handleProjectChange"
            />
          </div>
          <div class="filter-item">
            <a-select v-model="queryForm.parcelId" placeholder="请选择地块" style="width: 120px"
              class="gray-bg-select" @change="handleParcelChange" :loading="parcelLoading">
              <a-option v-for="parcel in parcelList" :key="parcel.id" :value="parcel.id">
                {{ parcel.parcelName }}
              </a-option>
            </a-select>
          </div>
          <div class="filter-item">
            <a-select v-model="queryForm.buildingId" placeholder="请选择楼栋" style="width: 120px"
              class="gray-bg-select" @change="handleBuildingChange" :loading="buildingLoading">
              <a-option v-for="building in buildingList" :key="building.id" :value="building.id">
                {{ building.buildingName }}
              </a-option>
            </a-select>
          </div>
          <div class="filter-item">
            <a-select v-model="queryForm.floorId" placeholder="全部楼层" style="width: 120px"
              class="gray-bg-select" @change="handleFloorChange" :loading="floorLoading">
              <a-option value="">全部楼层</a-option>
              <a-option v-for="floor in floorList" :key="floor.id" :value="floor.id">
                {{ floor.floorName }}
              </a-option>
            </a-select>
          </div>
        </div>
        <div class="filter-group">
          <div class="filter-item use-type">
            <span class="label">用途:</span>
            <a-checkbox-group v-model="queryForm.propertyTypes" class="use-type-checkbox">
              <a-checkbox
                v-for="type in filteredPropertyTypeOptions"
                :key="type.dictValue"
                :value="type.dictValue"
              >
                {{ type.dictLabel }}
              </a-checkbox>
            </a-checkbox-group>
          </div>
        </div>
      </div>
    </div>

    <!-- 房态内容 -->
    <div class="content-wrapper">
      <div class="chart-content" ref="chartContentRef">
        <div v-for="(floor, fIdx) in floorDiagramData" :key="floor.floorId" class="building-section">
          <div class="building-title clickable" @click="toggleBuilding(floor.floorId)">
            <img :src="headerLocation" alt="location" class="header-location-icon" />
            <span>{{ floor.floorName }}</span>
            <a-button type="text" class="view-all-btn" @click.stop="toggleBuilding(floor.floorId)">
              <icon-down v-if="!expandedBuildings.includes(floor.floorId)" />
              <icon-up v-else />
            </a-button>
          </div>
          <div class="floors-container" v-show="expandedBuildings.includes(floor.floorId)">
            <div class="floor-section" :ref="el => setFloorRef(floor.floorId, el)">
              <div class="rooms-container">
                <div v-for="room in getFilteredRoomsByFloor(floor)" :key="room.roomId" 
                     class="room-card" 
                     :class="[`status-${mapRoomStatus(room.roomStatus)}`]"
                     @click="handleRoomClick(room)">
                  
                  <!-- 自用右上角标识 -->
                  <div v-if="room.isSelfUse" class="status-tag-corner tag-self-use"></div>
                  
                  <div class="room-header">
                    <div class="room-number">{{ room.roomName }}</div>
                    <div class="room-status-tag-wrapper">
                      <!-- 租控状态标识 -->
                      <div v-if="room.isLock" class="room-status-tag-round">
                        锁
                      </div>
                      <div v-if="room.isDirty" class="room-status-tag-round">
                        脏
                      </div>
                      <div v-if="room.isMaintain" class="room-status-tag-round">
                        修
                      </div>
                      <!-- 进出场状态标识 -->
                      <div v-if="shouldShowTag(room, '未出场')" class="room-status-tag-ellipse">
                        未出场
                      </div>
                      <div v-if="shouldShowTag(room, '未进场')" class="room-status-tag-ellipse">
                        未进场
                      </div>
                    </div>
                  </div>
                  
                  <!-- 所有状态都显示的字段：名称、用途、计租面积、朝向 -->
                  <div class="room-tags">
                    <div v-if="room.propertyTypeName" class="room-type">{{ room.propertyTypeName }}</div>
                    <div v-if="room.rentArea" class="room-area">{{ room.rentArea }}m²{{ !room.orientationName ? '' : `-${room.orientationName}` }}</div>
                    <!-- 不可招商（自用）状态：显示自用主体 -->
                    <div v-if="room.roomStatus === 4 && room.isSelfUse && room.selfUseSubject" class="room-self-use-subject">
                      {{ getSelfUseSubjectName(room.selfUseSubject) }}
                    </div>
                    
                    <!-- 不可招商（非自用）、待生效/签约中/已预订、空置状态：显示户型 -->
                    <div v-if="(room.roomStatus === 4 && !room.isSelfUse) || room.roomStatus === 3 || room.roomStatus === 1" class="room-house-type">
                      <div v-if="room.houseTypeId && getHouseTypeName(room.houseTypeId)" class="room-occupancy">
                        {{ getHouseTypeName(room.houseTypeId) }}
                      </div>
                    </div>
                  </div>
                  
                  <!-- 表价和空置天数/可招商日期同一行显示 -->
                  <div class="room-price-vacancy-row">
                    <div class="room-rent">
                      <div v-if="room.baseRent" class="rent-value">
                        {{ room.baseRent }} <span class="rent-unit">{{ priceUnitMap[room.priceUnit] || '元/月' }}</span>
                      </div>
                      <span v-else class="rent-no-price">未定价</span>
                    </div>
                    
                    <!-- 空置天数（待生效/签约中/已预订、空置状态显示） -->
                    <div v-if="(room.roomStatus === 3 || room.roomStatus === 1) && room.emptyDays !== null && room.emptyDays !== undefined" class="room-vacancy">
                      空置 <span class="vacancy-days">{{ room.emptyDays }}</span> 天
                    </div>
                  </div>

                  <div class="horizontal-divider"></div>
                  
                  <!-- 在租状态显示承租方和租期 -->
                  <div v-if="room.roomStatus === 2 && room.contractVo" class="room-tenant">
                    {{ room.contractVo.tenantName }}
                    <div class="room-contract">
                      {{ formatDate(room.contractVo.startDate) }} 至 {{ formatDate(room.contractVo.endDate) }}
                    </div>
                  </div>
                  
                  <!-- 不可招商（非自用）状态显示可招商日期 -->
                  <div v-if="room.roomStatus === 4 && !room.isSelfUse" class="room-attract-date">
                    可招商日期：<span v-if="getAttractDate(room)" class="attract-date-value">{{ getAttractDate(room) }}</span><span v-else class="attract-date-undefined">未定</span>
                  </div>
                  <div class="room-tags-bottom">
                    <!-- 已预定标识 -->
                    <div v-if="shouldShowTag(room, '已预定')" class="room-reserved">
                      <div class="reserved-icon">
                        <img src="@/assets/images/rent/booked.png" class="icon-status">
                        已预定
                      </div>
                    </div>

                    <!-- 签约中标识 -->
                    <div v-if="shouldShowTag(room, '签约中')" class="room-contract-info">
                      <div class="contract-icon">
                        <img src="@/assets/images/rent/tobeEffective.png" class="icon-status">
                        签约中
                      </div>
                    </div>
                    
                    <!-- 待生效标识 -->
                    <div v-if="shouldShowTag(room, '待生效')" class="room-contract-info">
                      <div class="contract-icon">
                        <img src="@/assets/images/rent/tobeEffective.png" class="icon-status">
                        待生效
                      </div>
                    </div>
                    
                    <!-- 即将到期标识 -->
                    <div v-if="shouldShowTag(room, '即将到期')" class="room-expiring">
                      <img src="@/assets/images/rent/expiring.png" class="expiring-icon" />
                      即将到期
                    </div>
                  </div>
                  
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧楼层选择器 - 只有多于一层时才显示 -->
      <div v-if="allFloors.length > 1" class="floor-selector-wrapper">
        <div v-for="floor in allFloors" :key="floor.id" class="floor-selector-btn"
          :class="{ active: activeFloorId === floor.id }" @click="scrollToFloor(floor.id)">
          {{ floor.name }}
        </div>
      </div>
      <a-empty v-if="floorDiagramData.length === 0" description="暂无符合条件的房间" />
    </div>

    <RoomDetailDrawer v-model:visible="roomDetailVisible" :selectionInfo="selectionInfo" :room="roomDetail" :room-tags="roomTags" @refresh="handleRefresh" />

  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, nextTick, onMounted } from 'vue'
import headerLocation from '@/assets/images/rent/header-location.png'
import checkedIcon from '@/assets/images/rent/checked.png'
import { IconClose } from '@arco-design/web-vue/es/icon'
import RoomDetailDrawer from './roomDetailDrawer.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import { getParcelList, getBuildingSelectList, getFloorList, getHouseTypeList } from '@/api/project'
import { getRoomDetailDiagram, type RoomDiagramQueryDTO } from '@/api/room'
import { useDictSync } from '@/utils/dict'

// 价格单位映射
const priceUnitMap: Record<number, string> = {
  1: '元/平方米/月',
  2: '元/月',
  3: '元/日'
}

interface RoomDetail {
  id: string
  number: string
  status: 'rented' | 'vacant' | 'decorating' | 'in-progress' | 'reserved' | 'unavailable'
  area?: string
  rent?: string
  tenant?: string
  expireDate?: string
  details?: any
  type?: string
  occupancy?: string
  leaseStartDate?: string
  leaseEndDate?: string
  hasContract?: boolean
}

interface Floor {
  id: string
  name: string
  rooms: RoomDetail[]
}

interface Building {
  id: string
  name: string
  floors: Floor[]
}

const props = defineProps({
  data: {
    type: Array as () => Building[],
    default: () => []
  }
})

const emit = defineEmits(['room-click'])

// 更新房源状态统计
const updateRoomStatusCounts = (apiData: any) => {
    roomStatusCounts.value = {
        all: apiData.totalCount || 0,
        vacant: apiData.emptyCount || 0,
        inProgress: apiData.rentCount || 0,
        reserved: apiData.toEffectCount || 0,
        unavailable: apiData.invalidCount || 0
    }
}

// 不再使用模拟数据，完全基于接口数据

// 字典数据类型
interface DictData {
    dictCode: string
    dictValue: string
    dictLabel: string
    parentCode?: string
    childList?: DictData[]
}

// 查询表单
const queryForm = reactive<RoomDiagramQueryDTO & { 
    propertyTypes: string[]
    isLock?: boolean
    isDirty?: boolean  
    isMaintain?: boolean
    dueSoon?: boolean
    isSelfUse?: boolean
    electricityAbnormal?: boolean
}>({
    projectId: '',
    parcelId: '',
    buildingId: '',
    floorId: '',
    roomStatus: undefined,
    propertyType: '',
    propertyTypes: [],
    isLock: undefined,
    isDirty: undefined,
    isMaintain: undefined,
    dueSoon: undefined,
    isSelfUse: undefined,
    electricityAbnormal: undefined
})

// 数据列表
const parcelList = ref<any[]>([])
const buildingList = ref<any[]>([])
const floorList = ref<any[]>([])
const propertyTypeOptions = ref<DictData[]>([])
const houseTypeList = ref<any[]>([])
const houseTypeMap = ref<Record<string, string>>({})
const propertyList = ref<any[]>([])
const selectionInfo = ref<any>({
  projectId: '',
  projectName: '',
  blockId: '',
  blockName: '',
  buildingId: '',
  buildingName: ''
})
// 加载状态
const parcelLoading = ref(false)
const buildingLoading = ref(false)
const floorLoading = ref(false)

// 计算属性：只显示叶子节点的物业类型选项
const propertyTypeLeafOptions = computed(() => {
    const getLeafNodes = (nodes: DictData[]): DictData[] => {
        let leafNodes: DictData[] = []
        for (const node of nodes) {
            if (!node.childList || node.childList.length === 0) {
                leafNodes.push(node)
            } else {
                leafNodes = leafNodes.concat(getLeafNodes(node.childList))
            }
        }
        return leafNodes
    }
    return getLeafNodes(propertyTypeOptions.value)
})

// 计算属性：根据propertyList过滤出实际存在的物业类型选项
const filteredPropertyTypeOptions = computed(() => {
    if (!propertyList.value || propertyList.value.length === 0) {
        return []
    }
    
    const leafOptions = propertyTypeLeafOptions.value
    return leafOptions.filter(option => 
        propertyList.value.includes(option.dictValue)
    )
})

// Filter form (保留原有的一些字段用于兼容)
const filterForm = reactive({
  area: '1',
  block: 'F05',
  buildingId: '',
  floorId: '',
  status: '',
  useType: [],
  date: undefined,
  rentControl: 'locked'
})

// 所有过滤相关逻辑现在基于真实接口数据，不再需要这些旧的过滤逻辑

const roomDetailVisible = ref(false)
const roomDetail = ref<RoomDetail | null>(null)
const roomTags = ref<{ type: string, class: string }[]>([])

const handleRoomClick = (room: any) => {
  roomDetail.value = room
  roomDetailVisible.value = true
  roomTags.value = [
    { type: room.propertyTypeName || '', class: 'room-type' },
    { type: `${room.rentArea}m²${room.orientationName ? '-' + room.orientationName : ''}`, class: 'room-area' },
    { type: getHouseTypeName(room.houseTypeId) || '', class: 'room-occupancy' }
  ]
}

const handleSearch = () => {
  // The filteredBuildings computed property will update automatically
}

const resetFilter = () => {
  filterForm.buildingId = ''
  filterForm.floorId = ''
  filterForm.status = ''
  filterForm.useType = []
  queryForm.roomStatus = undefined
}

// Get room type name based on the room data
const getRoomTypeName = (room: RoomDetail) => {
  const roomTypeMap: Record<string, string> = {
    'dorm': '宿舍',
    'commercial': '商铺',
    'central': '中央空调'
  }
  return roomTypeMap[room.type || 'commercial'] || '宿舍'
}

// Get room occupancy information
const getRoomOccupancy = (room: RoomDetail) => {
  const occupancyMap: Record<string, string> = {
    'single': '单人间',
    'double': '双人间',
    'multiple': '多人间'
  }
  return occupancyMap[room.occupancy || 'single'] || '单人间'
}

// 房源状态统计
const roomStatusCounts = ref({
    all: 0,
    vacant: 0,
    inProgress: 0,
    reserved: 0,
    unavailable: 0
})

// 设置状态过滤参数
const setStatusFilter = (status: number | undefined) => {
    queryForm.roomStatus = status
    loadDetailDiagramData()
}

// 处理租控按钮点击（单选模式）
const handleRentControl = (type: string) => {
    // 单选模式：点击已选中的按钮则取消选中，点击其他按钮则选中
    if (type === 'locked') {
        queryForm.isLock = queryForm.isLock === true ? undefined : true
        queryForm.isDirty = undefined
        queryForm.isMaintain = undefined
    } else if (type === 'dirty') {
        queryForm.isDirty = queryForm.isDirty === true ? undefined : true
        queryForm.isLock = undefined
        queryForm.isMaintain = undefined
    } else if (type === 'maintenance') {
        queryForm.isMaintain = queryForm.isMaintain === true ? undefined : true
        queryForm.isLock = undefined
        queryForm.isDirty = undefined
    }
    loadDetailDiagramData()
}

const handleWarning = (type: string) => {
     // 预警选项处理
     if (type === 'electricity') {
         queryForm.electricityAbnormal = queryForm.electricityAbnormal === true ? undefined : true
     }
     loadDetailDiagramData()
}

// 新增：记录展开的楼栋id集合
const expandedBuildings = ref<string[]>([])

// 切换楼栋展开/收起
const toggleBuilding = (buildingId: string) => {
  if (expandedBuildings.value.includes(buildingId)) {
    expandedBuildings.value = expandedBuildings.value.filter(id => id !== buildingId)
  } else {
    expandedBuildings.value.push(buildingId)
  }
}

const setCustomBtn = (type: string) => {
    if (type === 'expiring') {
        queryForm.dueSoon = queryForm.dueSoon === true ? undefined : true
    } else if (type === 'self') {
        queryForm.isSelfUse = queryForm.isSelfUse === true ? undefined : true
    }
    loadDetailDiagramData()
}

const chartContentRef = ref<HTMLElement | null>(null)
const floorRefs = ref<Record<string, HTMLElement | null>>({})
const activeFloorId = ref('')

const setFloorRef = (id: string, el: any) => {
  if (el && el.tagName) floorRefs.value[id] = el as HTMLElement
}

// 根据楼层获取过滤后的房间数据
const getFilteredRoomsByFloor = (floor: any) => {
    if (!floor.rooms || !Array.isArray(floor.rooms)) {
        return []
    }
    
    let rooms = floor.rooms
    
    // 根据当前状态过滤
    if (queryForm.roomStatus !== undefined) {
        rooms = rooms.filter((room: any) => room.roomStatus === queryForm.roomStatus)
    }
    
    return rooms
}

// 右侧楼层选择器数据：根据接口返回的楼层名称显示
const allFloors = computed(() => {
  return floorDiagramData.value.map((floor) => ({ 
    id: floor.floorId, 
    name: floor.floorName || '未知楼层'
  }))
})

const scrollToFloor = (floorId: string) => {
  // 先确保目标楼层已展开
  if (!expandedBuildings.value.includes(floorId)) {
    expandedBuildings.value.push(floorId)
  }
  
  // 等待DOM更新后再滚动
  nextTick(() => {
    // 再次等待一个tick确保展开动画完成
    setTimeout(() => {
      const el = floorRefs.value[floorId]
      if (el && chartContentRef.value) {
        const top = el.getBoundingClientRect().top - chartContentRef.value.getBoundingClientRect().top + chartContentRef.value.scrollTop
        chartContentRef.value.scrollTo({ top, behavior: 'smooth' })
        activeFloorId.value = floorId
      }
    }, 100) // 给展开动画一点时间
  })
}

const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'in-progress': '在租',
    'vacant': '空置',
    'reserved': '待生效',
    'unavailable': '不可招商',
    'rented': '已租',
    'decorating': '装修中'
  }
  return map[status] || status
}

// 获取物业类型字典数据
const getPropertyTypeDicts = async () => {
    try {
        const dictData = await useDictSync('diversification_purpose')
        if (dictData.diversification_purpose) {
            const dictList = dictData.diversification_purpose as DictData[]
            // 组织成树形结构
            const treeData = dictList.filter(item => !item.parentCode)
            treeData.forEach(parent => {
                parent.childList = dictList.filter(child => child.parentCode === parent.dictCode)
            })
            propertyTypeOptions.value = treeData
        }
    } catch (error) {
        console.error('获取物业类型字典数据失败:', error)
    }
}

// 处理项目选择变化
const handleProjectChange = (projectId: string, selection: any) => {
    if (!projectId) return
    
    queryForm.projectId = projectId
    queryForm.parcelId = ''
    queryForm.buildingId = ''
    queryForm.floorId = ''
    selectionInfo.value.projectId = projectId
    selectionInfo.value.projectName = selection.name
    selectionInfo.value.blockId = ''
    selectionInfo.value.blockName = ''
    selectionInfo.value.buildingId = ''
    selectionInfo.value.buildingName = ''
    // 重置地块、楼栋、楼层列表
    parcelList.value = []
    buildingList.value = []
    floorList.value = []
    
    // 加载地块列表和户型列表
    loadParcelList(projectId)
    loadHouseTypeList(projectId)
}

// 加载地块列表
const loadParcelList = async (projectId: string) => {
    parcelLoading.value = true
    try {
        const { data } = await getParcelList(projectId)
        parcelList.value = data || []
        
        // 默认选择第一个地块
        if (parcelList.value.length > 0 && !queryForm.parcelId) {
            queryForm.parcelId = parcelList.value[0].id
            selectionInfo.value.blockId = parcelList.value[0].id
            selectionInfo.value.blockName = parcelList.value[0].parcelName
            if (queryForm.parcelId) {
                loadBuildingList(queryForm.parcelId)
            }
        }
    } catch (error) {
        console.error('获取地块列表失败:', error)
        parcelList.value = []
    } finally {
        parcelLoading.value = false
    }
}

// 处理地块选择变化
const handleParcelChange = (parcelId: string) => {
    if (!parcelId) return
    
    queryForm.parcelId = parcelId
    queryForm.buildingId = ''
    queryForm.floorId = ''
    selectionInfo.value.blockId = parcelId
    selectionInfo.value.blockName = parcelList.value.find(item => item.id === parcelId)?.parcelName || ''
    selectionInfo.value.buildingId = ''
    selectionInfo.value.buildingName = ''
    // 重置楼栋、楼层列表
    buildingList.value = []
    floorList.value = []
    
    // 加载楼栋列表
    loadBuildingList(parcelId)
}

// 加载楼栋列表
const loadBuildingList = async (parcelId: string) => {
    buildingLoading.value = true
    try {
        const { data } = await getBuildingSelectList(parcelId)
        buildingList.value = data || []
        
        // 默认选择第一个楼栋
        if (buildingList.value.length > 0 && !queryForm.buildingId) {
            queryForm.buildingId = buildingList.value[0].id
            selectionInfo.value.buildingId = buildingList.value[0].id
            selectionInfo.value.buildingName = buildingList.value[0].buildingName
            if (queryForm.buildingId) {
                loadFloorList(queryForm.buildingId)
            }
        }
    } catch (error) {
        console.error('获取楼栋列表失败:', error)
        buildingList.value = []
    } finally {
        buildingLoading.value = false
    }
}

// 处理楼栋选择变化
const handleBuildingChange = (buildingId: string) => {
    if (!buildingId) return
    
    queryForm.buildingId = buildingId
    queryForm.floorId = ''
    selectionInfo.value.buildingId = buildingId
    selectionInfo.value.buildingName = buildingList.value.find(item => item.id === buildingId)?.buildingName || ''

    // 重置楼层列表
    floorList.value = []
    
    // 加载楼层列表
    loadFloorList(buildingId)
}

// 加载楼层列表
const loadFloorList = async (buildingId: string) => {
    floorLoading.value = true
    try {
        const { data } = await getFloorList(buildingId)
        floorList.value = data || []
        
        // 默认选择全部楼层
        if (!queryForm.floorId) {
            queryForm.floorId = ''
        }
        
        // 加载房态数据
        loadDetailDiagramData()
    } catch (error) {
        console.error('获取楼层列表失败:', error)
        floorList.value = []
    } finally {
        floorLoading.value = false
    }
}

// 处理楼层选择变化
const handleFloorChange = (floorId: string) => {
    queryForm.floorId = floorId
    // 触发数据重新加载
    loadDetailDiagramData()
}

// 加载户型列表
const loadHouseTypeList = async (projectId: string) => {
    if (!projectId) {
        houseTypeList.value = []
        houseTypeMap.value = {}
        return
    }
    
    try {
        const response = await getHouseTypeList(projectId)
        if (response.code === 200) {
            houseTypeList.value = response.data || []
            // 创建户型ID到名称的映射
            const map: Record<string, string> = {}
            houseTypeList.value.forEach((item: any) => {
                if (item.id && item.houseTypeName) {
                    map[item.id] = item.houseTypeName
                }
            })
            houseTypeMap.value = map
        }
    } catch (error) {
        console.error('加载户型列表失败:', error)
    }
}

// 根据户型ID获取户型名称
const getHouseTypeName = (houseTypeId: string) => {
    return houseTypeMap.value[houseTypeId] || ''
}

// 获取自用主体名称
const getSelfUseSubjectName = (selfUseSubject: number) => {
    // 根据selfUseSubject值返回对应的名称
    const subjectMap: Record<number, string> = {
        1: '运营',
        2: '商服',
        3: '众创',
        4: '其他'
    }
    return subjectMap[selfUseSubject] || '未知'
}

// 格式化日期
const formatDate = (dateStr: string | null | undefined) => {
    if (!dateStr) return ''
    // 如果是ISO格式的日期字符串，只取日期部分
    if (dateStr.includes('T')) {
        return dateStr.split('T')[0]
    }
    return dateStr
}

// 判断是否显示特定标识
const shouldShowTag = (room: any, tag: string) => {
    // 首先检查tags数组
    if (room.tags && Array.isArray(room.tags) && room.tags.includes(tag)) {
        return true
    }
    
    // 如果tags数组中没有，检查单独字段和业务逻辑
    switch (tag) {
        case '未进场':
            return room.needCheckIn === true
        case '未出场':
            return room.needCheckOut === true
        case '即将到期':
            return room.dueSoon === true
        case '自用':
            return room.isSelfUse === true
        case '已预定':
            // 存在生效中的订单，判断bookingVo是否存在
            return room.bookingVo !== null && room.bookingVo !== undefined
        case '签约中':
            // 存在审批中的待生效合同
            return room.contractVo && room.contractVo.approveStatus === 1
        case '待生效':
            // 存在审批通过的待生效合同
            return room.contractVo && room.contractVo.approveStatus === 2 && room.contractVo.status === 20
        default:
            return false
    }
}

// 获取可招商日期（用于不可招商非自用状态）
const getAttractDate = (room: any) => {
    // 使用rentalStartDate字段作为可招商日期
    return room.rentalStartDate ? formatDate(room.rentalStartDate) : null
}

// 房态数据
const floorDiagramData = ref<any[]>([])

// 更新房间显示数据
const updateRoomDisplayData = (apiData: any) => {
    // 将接口返回的数据保存到响应式变量中
    floorDiagramData.value = apiData.floorDiagramList || []
    propertyList.value = apiData.propertyList || []
    console.log('更新房间显示数据:', apiData)
    
    // 数据更新后，自动展开第一个楼层
    nextTick(() => {
        if (floorDiagramData.value.length > 0) {
            expandedBuildings.value = [floorDiagramData.value[0].floorId]
        } else {
            expandedBuildings.value = []
        }
    })
}

// 房间状态映射函数
const mapRoomStatus = (roomStatus: number | undefined): string => {
    if (roomStatus === undefined) return 'vacant'
    
    switch (roomStatus) {
        case 1:
            return 'vacant'  // 空置
        case 2:
            return 'in-progress'  // 在租
        case 3:
            return 'reserved'  // 待生效/签约中/已预订
        case 4:
            return 'unavailable'  // 不可招商
        default:
            return 'vacant'
    }
}

// 加载房态详图数据
const loadDetailDiagramData = async () => {
    if (!queryForm.projectId || !queryForm.parcelId || !queryForm.buildingId) {
        return
    }
    
    try {
        // 组装查询参数
        const params: any = {
            projectId: queryForm.projectId,
            parcelId: queryForm.parcelId,
            buildingId: queryForm.buildingId,
            floorId: queryForm.floorId || undefined,
            roomStatus: queryForm.roomStatus,
            propertyType: queryForm.propertyTypes.join(',') || undefined,
            isLock: queryForm.isLock,
            isDirty: queryForm.isDirty,
            isMaintain: queryForm.isMaintain,
            dueSoon: queryForm.dueSoon,
            isSelfUse: queryForm.isSelfUse,
            electricityAbnormal: queryForm.electricityAbnormal
        }
        
        const { data } = await getRoomDetailDiagram(params)
        console.log('房态详图数据:', data)
        
        // 更新房间状态统计和显示数据
        updateRoomStatusCounts(data)
        updateRoomDisplayData(data)
    } catch (error) {
        console.error('获取房态详图数据失败:', error)
    }
}

// 处理刷新事件 - 由 RoomDetailDrawer 触发
const handleRefresh = () => {
    // 重新加载房态详图数据
    loadDetailDiagramData()
}

// 初始化数据
const initData = () => {
    getPropertyTypeDicts()
    // 初始化时状态统计为0
    roomStatusCounts.value = {
        all: 0,
        vacant: 0,
        inProgress: 0,
        reserved: 0,
        unavailable: 0
    }
}

// 组件挂载时初始化
nextTick(() => {
    initData()
})

// 监听物业类型变化
watch(() => queryForm.propertyTypes, () => {
    if (queryForm.projectId && queryForm.parcelId && queryForm.buildingId) {
        loadDetailDiagramData()
    }
}, { deep: true })

// 组件初始化
onMounted(() => {
    // 初始化字典数据
    getPropertyTypeDicts()
    
})

// 获取查询参数
const getQueryParams = () => {
    return {
        projectId: queryForm.projectId,
        parcelId: queryForm.parcelId,
        buildingId: queryForm.buildingId,
        floorId: queryForm.floorId,
        roomStatus: queryForm.roomStatus,
        propertyTypes: [...queryForm.propertyTypes],
        selectionInfo: { ...selectionInfo.value }
    }
}

// 设置查询参数
const setQueryParams = (params: any) => {
    if (!params) return
    
    // 设置基础查询参数
    if (params.projectId) queryForm.projectId = params.projectId
    if (params.parcelId) queryForm.parcelId = params.parcelId
    if (params.buildingId) queryForm.buildingId = params.buildingId
    if (params.floorId !== undefined) queryForm.floorId = params.floorId
    if (params.roomStatus !== undefined) queryForm.roomStatus = params.roomStatus
    if (params.propertyTypes) queryForm.propertyTypes = [...params.propertyTypes]
    
    // 设置选择信息
    if (params.selectionInfo) {
        selectionInfo.value = { ...params.selectionInfo }
    }
    
    // 重新加载相关数据（异步执行，不阻塞）
    if (params.projectId && params.projectId !== '') {
        loadParcelList(params.projectId)
        loadHouseTypeList(params.projectId)
    }
    if (params.parcelId && params.parcelId !== '') {
        loadBuildingList(params.parcelId)
    }
    if (params.buildingId && params.buildingId !== '') {
        loadFloorList(params.buildingId)
    }
}

// 暴露方法给父组件调用
defineExpose({
  loadDetailDiagramData,
  getQueryParams,
  setQueryParams
})

// 注释掉旧的监听器，现在在updateRoomDisplayData函数中处理楼层展开
// watch(floorDiagramData, (val) => {
//   if (val.length && expandedBuildings.value.length === 0) {
//     expandedBuildings.value = [val[0].floorId]
//   }
// }, { immediate: true })

</script>

<style scoped lang="less">
.room-status-detailed {
  width: 100%;
  padding: 0 16px;
  background: #fff;
  border-radius: 4px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.content-wrapper {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  position: relative;
}

.status-filters {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.status-buttons-group {
  display: flex;
  align-items: center;
  gap: 0 16px;
}

.status-buttons-main {
  display: flex;
  gap: 8px;
}

.status-buttons-divider {
  width: 1px;
  height: 24px;
  background: #e5e6eb;
  margin: 0 16px;
}

.status-buttons-secondary {
  display: flex;
  gap: 8px;
  position: relative;
}

.status-button {
  padding: 0 16px;
  height: 36px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  border: none;
  background: #f7f8fa;
  color: #165dff;
  transition: background 0.2s, color 0.2s;
}

.status-button.rent-control-btn,
.status-button.warning-btn {
  cursor: pointer;
  padding: 0 8px;
  height: 32px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  background-color: #f2f3f5;
  color: #1d2129;
  font-weight: 500;
}

.status-button.rent-control-btn:hover .rent-control-dropdown,
.status-button.warning-btn:hover .warning-dropdown {
  display: block;
}

.rent-control-dropdown,
.warning-dropdown {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
  min-width: 200px;
}

.dropdown-header {
  padding: 8px 12px;
  font-weight: 500;
  border-bottom: 1px solid #f2f3f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-header:after {
  content: '更多';
  font-size: 12px;
  color: #3583FF;
  cursor: pointer;
}

.dropdown-content {
  padding: 8px 0;
}

.rent-control-item {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rent-control-item .item-title {
  color: #4e5969;
}

.rent-control-item .item-value {
  font-weight: 500;
  color: #1d2129;
}

.warning-item {
  padding: 8px 12px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.warning-item .warning-icon {
  color: #F56C6C;
  margin-top: 2px;
}

.warning-item .warning-info {
  flex: 1;
}

.warning-item .warning-info .warning-title {
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 2px;
}

.warning-item .warning-info .warning-desc {
  font-size: 12px;
  color: #86909c;
}

.status-button.all.active {
  background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
  color: #fff;
}

.status-button.vacant {
  background: #EFF5FF;
  color: #3fc06d;
  &.active {
    background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
    color: #fff;
  }
}

.status-button.in-progress {
  background: #EFF5FF;
  color: #f53f3f;
  &.active {
    background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
    color: #fff;
  }
}

.status-button.reserved {
  background: #EFF5FF;
  color: #ff9a2e;
  &.active {
    background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
    color: #fff;
  }
}

.status-button.unavailable {
  background: #EFF5FF;
  color: #86909c;
  &.active {
    background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
    color: #fff;
  }
}

.status-button.no-bg {
  background: transparent;
  color: #1d2129;
  font-weight: 400;
  padding: 0 16px;
}

.status-button:not(.all):not(.no-bg):hover {
  opacity: 0.85;
}

.triangle-indicator {
  width: 0;
  height: 0;
  margin-right: 8px;
  border-style: solid;
  border-width: 0 16px 16px 0;
}

.expiring-icon {
  width: 12px;
  height: 16px;
  margin-right: 4px;
}

.triangle-indicator.blue {
  border-color: transparent #3583FF transparent transparent;
}

.triangle-indicator.purple {
  border-color: transparent #D73EFF transparent transparent;
}

.filter-bar-wrapper {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.filter-bar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;
}

.filter-group {
  display: flex;
  gap: 16px;
  align-items: center;
}
.filter-group:first-child{
  margin-right: 50px;
}

.filter-group-right {
  margin-left: auto;
  display: flex;
  gap: 16px;
  align-items: center;
}

.filter-item .label {
  margin-right: 8px;
  font-size: 14px;
  color: #616974;
}

.use-type {
  display: flex;
  align-items: center;
}

.use-type-checkbox {
  display: inline-flex;
  align-items: center;
}

.date-range {
  margin-left: 0;
}

:deep(.gray-bg-select .arco-select-view) {
  background-color: #f2f3f5;
  border-radius: 4px;
}

:deep(.checkbox-style .arco-checkbox-icon) {
  background-color: #f2f3f5;
}

:deep(.arco-select-view-single) {
  background-color: transparent;
}

.chart-content {
  flex: 1;
  max-width: 100%;
  overflow-y: auto;
  height: 70vh;
  position: relative;

  .building-section {
    margin-bottom: 24px;
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;

    .building-title {
      font-weight: 500;
      padding: 12px 16px;
      font-size: 14px;
      background: linear-gradient(270deg, #F3F8FD 1%, #EDF5FC 100%);
      border-bottom: 1px solid #eee;
      display: flex;
      align-items: center;

      .title-indicator {
        width: 3px;
        height: 16px;
        background: #165dff;
        margin-right: 8px;
        border-radius: 2px;
      }

      .view-all-btn {
        margin-left: auto;
        color: #165dff;

        :deep(.arco-icon) {
          margin-right: 4px;
        }
      }
    }

    .floors-container {
      .floor-section {
        margin-bottom: 0;
        padding: 16px;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        .rooms-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 16px;

          .room-card {
            padding: 16px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            cursor: pointer;
            background: #fff;
            border: 1px solid #eee;
            position: relative;
            overflow: hidden;

            &:hover {
              transform: translateY(-3px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            &.status-in-progress {
              border-left: 8px solid #F56C6C;
            }

            &.status-vacant {
              border-left: 8px solid #67C23A;
            }

            &.status-reserved {
              border-left: 8px solid #E6A23C;
            }

            &.status-unavailable {
              border-left: 8px solid #8B929C;
            }

            .room-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
            }

            .room-number {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 0;
              color: #1d2129;
            }

            .room-status {
              font-size: 12px;
              font-weight: 500;
              color: #fff;
              padding: 2px 8px;
              border-radius: 4px;

              &.in-progress {
                background: #3583FF;
              }

              &.vacant {
                background: #D73EFF;
              }

              &.unavailable {
                background: #3583FF;
              }
            }
            .room-status-tag-wrapper{
              display: flex;
              gap: 8px;
            }
            .room-status-tag-round{
              width: 24px;
              height: 24px;
              border-radius: 100%;
              background: #E5E9F0;
              color: #3583FF;
              font-size: 12px;
              display: flex;
              justify-content: center;
              align-items: center;
              border: 1px solid #3583FF;
            }
            .room-status-tag-ellipse{
              width: 60px;
              height: 24px;
              border-radius:12px 12px 12px 0;
              background: #E5E9F0;
              color: #3583FF;
              font-size: 12px;
              display: flex;
              justify-content: center;
              align-items: center;
              border: 1px solid #3583FF;
            }
            .room-tags {
              display: flex;
              margin-bottom: 10px;
              gap: 8px;
              flex-wrap: wrap;

              .room-type,
              .room-area,
              .room-occupancy,
              .room-self-use-subject {
                font-size: 12px;
                color: #657D9E;
                padding: 2px 8px;
                background: #E5E9F0;
                border-radius: 4px;
              }
              .room-type {
                background-color: #E2F7FF;
                color: #449CBE;
              }
            }

            .room-price-vacancy-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;
              gap: 8px;
            }

            .room-rent {
              font-size: 14px;
              color: #4e5969;
              flex-shrink: 0;

              .rent-value {
                font-size: 18px;
                font-weight: 600;
                color: #516588;
              }
              .rent-unit{
                font-size: 12px;
                color: #516588;
              }
            }

            .room-tenant {
              font-size: 14px;
              color: #1d2129;
              font-weight: 500;

              .room-contract {
                font-size: 12px;
                color: #86909c;
                font-weight: normal;
                margin-top: 4px;
              }
            }

            .room-vacancy {
              font-size: 14px;
              color: #4e5969;
              flex-shrink: 0;
              text-align: right;

              .vacancy-days {
                color: #F56C6C;
                font-weight: 600;
              }
            }

            .horizontal-divider {
              width: 100%;
              height: 1px;
              margin: 16px 0;
              background-color: #E5E6EB;
            }

            .room-reserved,
            .room-contract-info,
            .room-unavailable {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 6px;
              width: 100px;
              border-radius: 16px;
              .reserved-icon {
                color: #52B93C;
              }

              .contract-icon {
                color: #E6A23C;
              }

              .unavailable-icon {
                color: #F56C6C;
              }
            }

            .icon-status {
              width: 14px;
              height: 14px;
            }

            .room-unavailable {
              background-color: #FFE7E7;
            }

            .room-reserved {
              background-color: #E4F5EB;
            }

            .room-contract-info {
              background-color: #FEF7E6;
              color: #E6A23C;
              width: 100px;
            }

            .room-expiring {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 6px;
              width: 100px;
              border-radius: 16px;
              background-color: #fce9e9;
              color: #DC5252 ;  
              font-size: 12px;
              gap: 4px;

              .expiring-icon {
                width: 12px;
                height: 12px;
              }
            }

            /* 自用右上角标识 */
            .status-tag-corner {
              position: absolute;
              top: 0;
              right: 0;
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 0 12px 12px 0;
              z-index: 2;
            }

            .tag-self-use {
              border-color: transparent #3583FF transparent transparent;
            }
          }
        }
      }
    }
  }
}

/* 右侧租控和预警模块样式 */
.right-panel {
  width: 280px;
  flex-shrink: 0;
}

.panel-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  overflow: hidden;
  border: 1px solid #eee;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f2f3f5;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
}

.more-btn {
  color: #165dff;
  font-size: 14px;
  padding: 0;
}

.panel-content {
  padding: 16px;
}

/* 租控和预警按钮组样式 */
.control-buttons-wrapper {
  display: flex;
  align-items: center;
  margin-top: 0;
  margin-left: 16px;
  padding: 0;
  background: none;
  border: none;
  gap: 0 8px;
}

.control-label {
  font-size: 14px;
  color: #1d2129;
  font-weight: normal;
  margin: 0 4px 0 0;
}

.control-buttons {
  display: flex;
  gap: 0 4px;
  flex-wrap: wrap;
}

.control-buttons-divider {
  width: 1px;
  height: 24px;
  background: #e5e6eb;
  margin: 0 8px;
}

.control-button {
  height: 32px;
  padding: 0 14px;
  border-radius: 4px;
  font-size: 14px;
  margin-right: 0;
  background-color: #EFF5FF;

  &.checked {
    color: #1677FF;
  }
}

.building-title.clickable {
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-location-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;
}

.status-button.custom-btn,
.control-button.custom-btn {
  background: #EFF5FF;
  color: #1d2129;
  position: relative;
}

.status-button.custom-btn.checked,
.control-button.custom-btn.checked {
  color: #1677FF;
}

.checked-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 14px;
  height: 10px;
  z-index: 2;
}

.floor-selector-wrapper {
  position: absolute;
  right: -16px;
  top: 40px;
  width: 36px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 20;
  height: fit-content;
}

.floor-selector-btn {
  width: 36px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background: #f7f8fa;
  color: #000000;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.floor-selector-btn.active {
  background: #165dff;
  color: #fff;
}

.room-detail-modal :deep(.arco-modal-content) {
  border-radius: 14px;
  box-shadow: 0 8px 32px 0 rgba(22,93,255,0.10);
  padding: 0;
  background: #fff;
}
.room-detail-content {
  padding: 24px 24px 16px 24px;
  position: relative;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  min-height: 320px;
}
.room-detail-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}
.room-detail-number {
  color: #1d2129;
}
.room-detail-status {
  font-size: 14px;
  font-weight: 500;
  padding: 2px 12px;
  border-radius: 8px 8px 8px 0;
  margin-left: 8px;
  background: #f7f8fa;
  color: #165dff;
  &.in-progress { background: #e8f3ff; color: #165dff; }
  &.vacant { background: #e8f9f0; color: #3fc06d; }
  &.reserved { background: #fff7e6; color: #ff9a2e; }
  &.unavailable { background: #f7f8fa; color: #86909c; }
}
.room-detail-divider {
  height: 1px;
  background: #e5e6eb;
  margin: 16px 0;
}
.room-detail-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 15px;
}
.room-detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4e5969;
}
.room-detail-row .label {
  color: #86909c;
  min-width: 56px;
}
.room-detail-row .value {
  color: #1d2129;
  font-weight: 500;
}
.room-detail-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}
.room-detail-actions .arco-btn {
  min-width: 110px;
  height: 36px;
  font-size: 15px;
  font-weight: 500;
  border-radius: 6px;
}
.room-detail-actions .arco-btn-primary {
  background: linear-gradient(90deg, #165dff 0%, #408cff 100%);
  border: none;
}
.room-detail-close {
  position: absolute;
  top: 18px;
  right: 18px;
  font-size: 18px;
  color: #86909c;
  cursor: pointer;
  transition: color 0.2s;
}
.room-detail-close:hover {
  color: #165dff;
}
.room-tags-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 10px;
}

/* 新增样式 */
.room-status-specific {
  margin: 4px 0;
  min-height: 20px;
}

.room-self-use-subject {
  font-size: 12px;
  color: #4e5969;
  margin: 2px 0;
}

.room-house-type .room-occupancy {
  font-size: 12px;
  color: #1d2129;
  background: #f2f3f5;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.room-orientation {
  font-size: 12px;
  color: #4e5969;
  background: #f7f8fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.room-attract-date {
  font-size: 12px;
  color: #4e5969;
  white-space: nowrap;
}

.rent-no-price {
  color: #f53f3f;
}

.attract-date-undefined {
  color: #f53f3f;
}
:deep(.w-full) {
    @media (max-width: 1600px) {
        flex-direction: column;
        .filter-group:first-child{
            width: 100%;
            display: flex;
            justify-content: flex-start;
        }
        .filter-group:last-child{
            width: 100%;
            display: flex;
            justify-content: flex-end;
        }
    }
}
:deep(.status-filters-full){
    @media (max-width: 1599px) {
        flex-direction: column;
        .status-buttons-group{
            width: 100%;
            display: flex;
            justify-content: flex-start;
        }
        .control-buttons-wrapper{
            width: 100%;
            display: flex;
            justify-content: flex-end;
            margin-top: 16px;
        }
    }
}
</style>