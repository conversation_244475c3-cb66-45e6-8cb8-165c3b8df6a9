<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="isEdit ? '编辑固定资产' : '新增固定资产'"
    width="800px"
    @ok="handleOk"
    @cancel="handleCancel"
    :ok-loading="loading"
    :mask-closable="true"
    :esc-to-close="false"
    :ok-button-props="{ 
      disabled: !formData.category || !formData.name || formData.usageScope.length === 0 
    }"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
      label-align="right"
      validate-trigger="blur"
    >
      <a-form-item field="category" label="种类" required>
        <a-select v-model="formData.category" placeholder="请选择种类">
          <a-option :value="1">家电</a-option>
          <a-option :value="2">家具</a-option>
          <a-option :value="3">装饰</a-option>
          <a-option :value="4">其他</a-option>
        </a-select>
      </a-form-item>
      
      <a-form-item field="name" label="物品名称" required>
        <a-input 
          v-model="formData.name" 
          placeholder="请输入物品名称" 
          :max-length="100"
          :show-word-limit="true"
        />
      </a-form-item>
      
      <a-form-item field="specification" label="规格">
        <a-input 
          v-model="formData.specification" 
          placeholder="请输入规格" 
          :max-length="100"
          :show-word-limit="true"
        />
      </a-form-item>
      
      <a-form-item field="usageScope" label="使用范围" required>
        <a-tree-select 
          v-model="formData.usageScope" 
          :data="usageScopeTreeData"
          :field-names="{
            key: 'dictValue',
            title: 'dictLabel',
            children: 'childList'
          }"
          placeholder="请选择使用范围" 
          multiple
          allow-clear
          tree-checkable
          :check-strictly="true"
          :selectable="isLeafNode"
          :tree-props="{
            blockNode: true,
            checkStrictly: true
          }"
        />
      </a-form-item>
      
      <a-form-item field="remark" label="物品描述">
        <a-textarea 
          v-model="formData.remark" 
          placeholder="请输入物品描述"
          :max-length="500"
          show-word-limit
          :auto-size="{ minRows: 3, maxRows: 6 }"
        />
      </a-form-item>
      
      <a-form-item field="attachments" label="物品图片">
        <upload-image 
          v-model="formData.attachments" 
          :limit="5"
          accept=".jpg,.jpeg,.png"
          :max-size="5"
        />
        <div class="upload-tip">支持jpg、png格式，最多上传5张图片，单张图片不超过5MB</div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import type { FormInstance } from '@arco-design/web-vue'
import { 
  addFixedAssets, 
  updateFixedAssets, 
  type FixedAssetsAddDTO, 
  type FixedAssetsVo 
} from '@/api/fixedAssets'
import UploadImage from '@/components/upload/uploadImage.vue'

interface Props {
  visible: boolean
  editData?: FixedAssetsVo | null
  usageScopeOptions?: any[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.editData)

// 将选项数据转换为树形结构
const usageScopeTreeData = computed(() => {
  return props.usageScopeOptions || []
})

// 判断是否为叶子节点
const isLeafNode = (node: any) => {
  return !node.childList || node.childList.length === 0
}

// 表单数据
const formData = reactive({
  id: '',
  category: undefined as number | undefined,
  name: '',
  specification: '',
  usageScope: [] as string[],
  remark: '',
  attachments: ''
})

// 表单验证规则
const rules = {
  category: [
    { required: true, message: '请选择种类', type: 'number' }
  ],
  name: [
    { required: true, message: '请输入物品名称' },
    { max: 100, message: '物品名称不能超过100个字符' }
  ],
  usageScope: [
    { required: true, message: '请选择使用范围', type: 'array' }
  ],
  specification: [
    { max: 100, message: '规格不能超过100个字符' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    category: undefined,
    name: '',
    specification: '',
    usageScope: [],
    remark: '',
    attachments: ''
  })
  formRef.value?.resetFields()
}

// 监听弹窗显示状态变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 弹窗打开时，根据是否有编辑数据来决定是填充还是重置
    if (props.editData) {
      // 编辑模式，填充数据
      const usageScopeArray = props.editData.usageScope ? props.editData.usageScope.split(',') : []
      
      nextTick(() => {
        Object.assign(formData, {
          id: props.editData!.id,
          category: props.editData!.category,
          name: props.editData!.name,
          specification: props.editData!.specification,
          usageScope: usageScopeArray,
          remark: props.editData!.remark,
          attachments: props.editData!.attachments || ''
        })
      })
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  }
})

// 监听编辑数据变化（保留原有逻辑作为备用）
watch(() => props.editData, (newData) => {
  // 只有在弹窗已经显示的情况下才处理数据变化
  if (!props.visible) return
  
  if (newData) {
    // 编辑模式，填充数据
    const usageScopeArray = newData.usageScope ? newData.usageScope.split(',') : []
    
    nextTick(() => {
      Object.assign(formData, {
        id: newData.id,
        category: newData.category,
        name: newData.name,
        specification: newData.specification,
        usageScope: usageScopeArray,
        remark: newData.remark,
        attachments: newData.attachments || ''
      })
    })
  } else {
    // 新增模式，重置表单
    resetForm()
  }
})

// 根据选中的使用范围生成usageScopeList
const generateUsageScopeList = () => {
  const usageScopeList: any[] = []
  
  if (formData.usageScope && formData.usageScope.length > 0) {
    // 遍历所有选中的值，找到对应的字典项并添加到列表中
    formData.usageScope.forEach(selectedValue => {
      // 在字典数据中查找对应的项
      const findSelectedItem = (options: any[], value: string): any => {
        for (const option of options) {
          if (option.dictValue === value) {
            return {
              dictCode: option.dictCode,
              dictLabel: option.dictLabel,
              dictType: option.dictType,
              dictValue: option.dictValue,
              parentCode: option.parentCode
            }
          }
          if (option.childList && option.childList.length > 0) {
            const childResult = findSelectedItem(option.childList, value)
            if (childResult) return childResult
          }
        }
        return null
      }
      
      const selectedItem = findSelectedItem(usageScopeTreeData.value, selectedValue)
      if (selectedItem) {
        usageScopeList.push(selectedItem)
      }
    })
  }
  
  return usageScopeList
}

// 获取选中的使用范围名称
const getSelectedItemNames = () => {
  const selectedNames: string[] = []
  
  const findSelectedNames = (options: any[], selectedValues: string[]) => {
    options.forEach(option => {
      if (selectedValues.includes(option.dictValue)) {
        selectedNames.push(option.dictLabel)
      }
      if (option.childList && option.childList.length > 0) {
        findSelectedNames(option.childList, selectedValues)
      }
    })
  }
  
  findSelectedNames(usageScopeTreeData.value, formData.usageScope)
  return selectedNames
}

// 确认提交
const handleOk = async () => {
  try {
    // 先执行表单验证
    try {
      await formRef.value?.validate()
    } catch (validationError) {
      // 表单验证失败，不做任何操作，弹窗保持打开状态
      console.error('表单验证失败:', validationError)
      Message.error('请填写必填项')
      return
    }
    
    // 设置加载状态
    loading.value = true
    
    // 生成使用范围相关数据
    const usageScopeList = generateUsageScopeList()
    
    const usageScopeName = getSelectedItemNames().join('、')
    
    // 处理使用范围（多选转字符串）
    const submitData: FixedAssetsAddDTO = {
      ...formData,
      category: formData.category!,
      usageScope: Array.isArray(formData.usageScope) 
        ? formData.usageScope.join(',') 
        : formData.usageScope,
      attachments: formData.attachments || null,
      usageScopeName,
      usageScopeList
    }
    
    // 调用API
    if (isEdit.value) {
      await updateFixedAssets(submitData)
      Message.success('修改成功')
      // 成功后重置表单并关闭弹窗
      resetForm()
      modalVisible.value = false
      emit('success')
    } else {
      await addFixedAssets(submitData)
      Message.success('新增成功')
      // 成功后重置表单并关闭弹窗
      resetForm()
      modalVisible.value = false
      emit('success')
    }
  } catch (error: any) {
    console.error('提交失败:', error)
    // API错误由响应拦截器处理，不关闭弹窗
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  modalVisible.value = false
  // 关闭弹窗时重置表单，确保下次打开时是干净的状态
  resetForm()
}
</script>

<style scoped lang="less">
.upload-tip {
  margin-top: 8px;
  color: var(--color-text-3);
  font-size: 12px;
}
</style> 