<template>
  <a-drawer
    v-model:visible="visible"
    class="common-drawer"
    title="批量更新"
    @cancel="handleCancel"
  >
    <div class="modal-scroll-content">
      <a-form label-align="right" :model="form" layout="horizontal" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width ref="formRef">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="物业类型" field="propertyType">
              <a-tree-select
                v-model="form.propertyType"
                :data="propertyTypeOptions"
                placeholder="请选择物业类型"
                allow-clear
                :field-names="{
                  key: 'dictValue',
                  title: 'dictLabel',
                  children: 'childList'
                }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="户型" field="houseTypeId">
              <a-select v-model="form.houseTypeId" placeholder="请选择户型" allow-clear>
                <a-option v-for="item in houseTypeOptions" :key="item.id" :value="item.id">
                  {{ item.houseTypeName }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="朝向" field="orientation">
              <a-select v-model="form.orientation" placeholder="请选择朝向" allow-clear>
                <a-option v-for="item in roomOrientationOptions" :key="item.dictValue" :value="item.dictValue">
                  {{ item.dictLabel }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="面积类型" field="areaType">
              <a-select v-model="form.areaType" placeholder="请选择面积类型" allow-clear>
                <a-option :value="1">实测</a-option>
                <a-option :value="2">预测</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="建筑面积" field="buildArea">
              <a-input v-model="form.buildArea" type="number" placeholder="请输入建筑面积" allow-clear>
                <template #append>㎡</template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="套内面积" field="innerArea">
              <a-input v-model="form.innerArea" type="number" placeholder="请输入套内面积" allow-clear>
                <template #append>㎡</template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="资产运营模式" field="assetOperationMode">
              <a-select v-model="form.assetOperationMode" placeholder="请选择资产运营模式" allow-clear>
                <a-option v-for="item in assetOperationModeOptions" :key="item.dictValue" :value="item.dictValue">
                  {{ item.dictLabel }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="资产运营分类" field="assetOperationType">
              <a-select v-model="form.assetOperationType" placeholder="请选择资产运营分类" allow-clear>
                <a-option v-for="item in assetOperationTypeOptions" :key="item.dictValue" :value="item.dictValue">
                  {{ item.dictLabel }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="产权情况" field="propertyStatus">
              <a-select v-model="form.propertyStatus" placeholder="请选择产权情况" allow-clear>
                <a-option value="无证">无证</a-option>
                <a-option value="有证">有证</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="交付状态" field="paymentStatus">
              <a-select v-model="form.paymentStatus" placeholder="请选择交付状态" allow-clear>
                <a-option :value="1">未交付</a-option>
                <a-option :value="2">已交付</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="16">
            <a-form-item label="特殊标签" field="specialTag">
              <a-checkbox v-model="form.isGuarantee">保障房</a-checkbox>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="商管承租成本单价" field="firstRentPrice">
              <a-input v-model="form.firstRentPrice" type="number" placeholder="请输入商管承租成本单价" allow-clear>
                <template #append>元/平米/月</template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="16">
            <a-form-item label="商管承租日期" field="rentDate">
              <a-range-picker v-model="form.rentDate" style="width: 100%;" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="运营主体" field="operationSubject">
              <a-select v-model="form.operationSubject" placeholder="请选择运营主体" allow-clear>
                <a-option :value="1">商服</a-option>
                <a-option :value="2">众创城</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="可招商日期" field="rentalStartDate">
              <a-date-picker v-model="form.rentalStartDate" style="width: 100%;" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="对外出租起始日期" field="externalRentStartDate">
              <a-date-picker v-model="form.externalRentStartDate" style="width: 100%;" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="是否自用" field="isSelfUse">
              <a-select v-model="form.isSelfUse" placeholder="请选择是否自用" allow-clear>
                <a-option :value="true">是</a-option>
                <a-option :value="false">否</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8" v-if="form.isSelfUse === true">
            <a-form-item label="自用主体" field="selfUseSubject" :rules="[{ required: true, message: '请选择自用主体' }]">
              <a-select v-model="form.selfUseSubject" placeholder="请选择自用主体" allow-clear>
                <a-option v-for="item in selfUseSubjectOptions" :key="item.dictValue" :value="Number(item.dictValue)">
                  {{ item.dictLabel }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" v-if="form.isSelfUse === true">
            <a-form-item label="自用用途" field="selfUsePurpose" :rules="[{ required: true, message: '请输入自用用途' }]">
              <a-textarea v-model="form.selfUsePurpose" placeholder="请输入自用用途"  :max-length="200" allow-clear show-word-limit />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <template #footer>
      <div style="text-align: center;">
        <a-button type="primary" @click="handleOk" :loading="loading" style="width: 120px;">确定</a-button>
        <a-button @click="handleCancel" style="margin-left: 24px; width: 120px;">取消</a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineExpose, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useDictSync } from '@/utils/dict'
import { batchUpdateRooms, type BatchUpdateRoomDTO } from '@/api/room'
import { getHouseTypeList, type ProjectHouseTypeVo } from '@/api/project'

// 表单状态
const visible = ref(false)
const loading = ref(false)
const roomIds = ref<string[]>([])
const projectId = ref<string>('')

const emit = defineEmits(['confirm','cancel'])

// 表单数据
const form = reactive({
  propertyType: '',
  houseTypeId: '',
  orientation: '',
  areaType: undefined as number | undefined,
  buildArea: undefined as number | undefined,
  innerArea: undefined as number | undefined,
  assetOperationMode: '',
  assetOperationType: '',
  propertyStatus: '',
  paymentStatus: undefined as number | undefined,
  isGuarantee: false,
  firstRentPrice: undefined as number | undefined,
  rentDate: [] as any[],
  operationSubject: undefined as number | undefined,
  rentalStartDate: '',
  externalRentStartDate: '',
  isSelfUse: undefined as boolean | undefined,
  selfUseSubject: undefined as number | undefined,
  selfUsePurpose: ''
})

const formRef = ref()

// 字典数据
const propertyTypeOptions = ref<any[]>([])
const roomOrientationOptions = ref<any[]>([])
const assetOperationModeOptions = ref<any[]>([])
const assetOperationTypeOptions = ref<any[]>([])
const selfUseSubjectOptions = ref<any[]>([])
const houseTypeOptions = ref<ProjectHouseTypeVo[]>([])

// 加载字典数据
const loadDictionaries = async () => {
  try {
    // 获取物业类型字典
    const dictData = await useDictSync('property_type','room_orientation','asset_operation_mode','asset_operation_type','self_use_subject')
    if (dictData.property_type) {
      const dictList = dictData.property_type
      // 组织成树形结构
      const treeData = dictList.filter((item: any) => !item.parentCode)
      treeData.forEach((parent: any) => {
        parent.childList = dictList.filter((child: any) => child.parentCode === parent.dictCode)
      })
      propertyTypeOptions.value = treeData
    }

    // 获取朝向字典
    if (dictData.room_orientation) {
      roomOrientationOptions.value = dictData.room_orientation
    }

    // 获取资产运营模式字典
    if (dictData.asset_operation_mode) {
      assetOperationModeOptions.value = dictData.asset_operation_mode
    }

    // 获取资产运营分类字典
    if (dictData.asset_operation_type) {
      assetOperationTypeOptions.value = dictData.asset_operation_type
    }

    // 获取自用主体字典
    if (dictData.self_use_subject) {
      selfUseSubjectOptions.value = dictData.self_use_subject
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 加载户型数据
const loadHouseTypeList = async (projectId: string) => {
  if (!projectId) return
  try {
    const res = await getHouseTypeList(projectId)
    if (res && res.data) {
      houseTypeOptions.value = res.data
    }
  } catch (error) {
    console.error('获取户型列表失败:', error)
  }
}

function show(ids?: string[], projectIdValue?: string) {
  visible.value = true
  loading.value = false
  
  // 保存房源ID列表和项目ID
  if (Array.isArray(ids) && ids.length > 0) {
    roomIds.value = ids
  } else {
    roomIds.value = []
  }
  
  if (projectIdValue) {
    projectId.value = projectIdValue
    // 加载户型列表
    loadHouseTypeList(projectIdValue)
  }
  
  // 重置表单
  resetForm()
}

function resetForm() {
  // 重置表单字段
  form.propertyType = '';
  form.houseTypeId = '';
  form.orientation = '';
  form.areaType = undefined;
  form.buildArea = undefined;
  form.innerArea = undefined;
  form.assetOperationMode = '';
  form.assetOperationType = '';
  form.propertyStatus = '';
  form.paymentStatus = undefined;
  form.isGuarantee = false;
  form.firstRentPrice = undefined;
  form.rentDate = [];
  form.operationSubject = undefined;
  form.rentalStartDate = '';
  form.externalRentStartDate = '';
  form.isSelfUse = undefined;
  form.selfUseSubject = undefined;
  form.selfUsePurpose = '';
}

async function handleOk() {
  if (roomIds.value.length === 0) {
    Message.warning('请先选择要更新的房源')
    return
  }
  
  // 检查是否有字段被设置
  const hasChanges = Object.entries(form).some(([key, value]) => {
    if (key === 'isGuarantee') {
      return form.isGuarantee !== false // 只有当isGuarantee为true时才认为有变更
    }
    if (key === 'rentDate') {
      return Array.isArray(form.rentDate) && form.rentDate.length > 0
    }
    return value !== undefined && value !== ''
  })
  
  if (!hasChanges) {
    Message.warning('请至少选择一个要更新的字段')
    return
  }
  // 校验表单
  const errors = await formRef.value?.validate()
  if (errors) {
    Message.warning('请先完善房源信息的必填字段')
    return
  }
  try {
    loading.value = true
    
    // 构造API请求参数
    const apiData: BatchUpdateRoomDTO = {
      roomIds: roomIds.value,
      roomInfo: {}
    }
    // 转换特殊标签
    const specialTag = form.isGuarantee ? '1' : undefined
    
    // 设置更新字段
    if (form.propertyType) apiData.roomInfo!.propertyType = form.propertyType
    if (form.houseTypeId) apiData.roomInfo!.houseTypeId = form.houseTypeId
    if (form.paymentStatus !== undefined) apiData.roomInfo!.paymentStatus = form.paymentStatus
    if (form.operationSubject !== undefined) apiData.roomInfo!.operationSubject = form.operationSubject
    if (form.orientation) apiData.roomInfo!.orientation = form.orientation
    if (form.areaType !== undefined) apiData.roomInfo!.areaType = form.areaType
    if (form.buildArea !== undefined) apiData.roomInfo!.buildArea = form.buildArea
    if (form.innerArea !== undefined) apiData.roomInfo!.innerArea = form.innerArea
    if (form.assetOperationMode) apiData.roomInfo!.assetOperationMode = form.assetOperationMode
    if (form.assetOperationType) apiData.roomInfo!.assetOperationType = form.assetOperationType
    if (form.propertyStatus) apiData.roomInfo!.propertyStatus = form.propertyStatus
    if (specialTag) apiData.roomInfo!.specialTag = specialTag
    if (form.firstRentPrice !== undefined) apiData.roomInfo!.firstRentPrice = form.firstRentPrice
    if (form.rentDate && form.rentDate.length === 2) {
      apiData.roomInfo!.firstRentStartDate = form.rentDate[0].toString();
      apiData.roomInfo!.firstRentEndDate = form.rentDate[1].toString();
    }
    if (form.rentalStartDate) apiData.roomInfo!.rentalStartDate = form.rentalStartDate
    if (form.externalRentStartDate) apiData.roomInfo!.externalRentStartDate = form.externalRentStartDate
    if (form.isSelfUse !== undefined) apiData.roomInfo!.isSelfUse = form.isSelfUse
    if (form.selfUseSubject !== undefined) apiData.roomInfo!.selfUseSubject = form.selfUseSubject
    if (form.selfUsePurpose) apiData.roomInfo!.selfUsePurpose = form.selfUsePurpose
    
    // 调用批量更新API
    await batchUpdateRooms(apiData)
    
    Message.success('批量更新成功')
    setTimeout(() => {
      emit('confirm')
    }, 300);
    visible.value = false
  } catch (error) {
    console.error('批量更新失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  setTimeout(() => {
    emit('cancel')
  }, 300);
  visible.value = false
}

// 组件挂载时加载字典数据
onMounted(() => {
  loadDictionaries()
})

defineExpose({ show })
</script>

<style scoped>
.modal-scroll-content {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
}
</style> 