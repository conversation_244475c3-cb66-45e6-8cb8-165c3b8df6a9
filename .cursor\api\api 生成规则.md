# 接口生成规则
    - 
        import http from './index'
        const systemUrl = '/system'
    - post
        export function xxx(data: any) {
            return http.post(`${systemUrl}/xx/xx`, data)
        }
    - get
        export function xxx(data: any) {
            return http.get(`${systemUrl}/xx/xx`, data)
        }
    - put
        export function xxx(data: any) {
            return http.put(`${systemUrl}/xx/xx`, data)
        }
    - delete
        export function xxx(data: any) {
            return http.delete(`${systemUrl}/xx/xx`, data)
        }