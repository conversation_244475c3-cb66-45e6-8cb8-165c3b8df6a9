/**
 * Vite 性能优化配置
 * 提升构建速度和开发体验
 */
import type { Plugin, UserConfig } from 'vite';
import os from 'os';

export interface PerformanceOptions {
    /** 是否启用缓存 */
    enableCache?: boolean;
    /** 是否启用多线程构建 */
    enableMultiThread?: boolean;
    /** 最大内存使用量(MB) */
    maxMemory?: number;
    /** 是否启用文件系统缓存 */
    enableFsCache?: boolean;
}

export default function configPerformancePlugin(options: PerformanceOptions = {}): Partial<UserConfig> {
    const {
        enableCache = true,
        enableMultiThread = true,
        maxMemory = 8192,
        enableFsCache = true,
    } = options;

    // 获取 CPU 核心数
    const cpuCount = os.cpus().length;
    const workerCount = Math.max(1, cpuCount - 1); // 保留一个核心给系统

    return {
        // 优化构建配置
        build: {
            // 启用 Rollup 多线程
            rollupOptions: {
                // 设置最大并行数
                maxParallelFileOps: workerCount,
                output: {
                    // 分块策略优化
                    manualChunks: undefined, // 让 Vite 自动处理
                }
            },
        },

        // 优化依赖预构建
        optimizeDeps: {
            // 开发时强制预构建
            force: false,
            // 包含需要预构建的依赖
            include: [
                'vue',
                'vue-router',
                'pinia',
                '@arco-design/web-vue',
                'echarts',
                'vue-echarts',
                'dayjs',
                'lodash',
                'axios',
            ],
            // 排除不需要预构建的依赖
            exclude: [
                // 'vue-demi'
            ],
        },

        // 启用文件系统缓存
        cacheDir: enableFsCache ? 'node_modules/.vite' : undefined,

        // 服务器配置优化
        server: {
            // 启用文件系统缓存
            fs: {
                // 允许访问工作区根目录之外的文件
                strict: false,
            },
        },

        // 启用 esbuild 优化
        esbuild: {
            // 使用更现代的目标环境以支持 import.meta
            target: 'es2020',
            // 保持类名用于调试
            keepNames: true,
        }
    };
} 