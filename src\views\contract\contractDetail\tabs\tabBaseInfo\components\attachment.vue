<template>
    <section>
        <sectionTitle title="附件" />
        <div class="content">
            <a-grid :cols="1" :col-gap="16" :row-gap="0">
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">上传附件</label>
                        <upload-file v-model="contractData.contractAttachments" :readonly="true" />
                    </div>
                    <div v-if="contractData.contractType === 0 && ['商铺', '中央食堂', '厂房', '综合体', '办公'].includes(contractPurposeLabel)" class="form-item">
                        <label class="form-label">平面图</label>
                        <upload-image v-model="contractData.attachmentsPlan" :readonly="true" />
                    </div>
                </a-grid-item>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import uploadFile from '@/components/upload/uploadFile.vue'
import uploadImage from '@/components/upload/uploadImage.vue'
import { useContractStore } from '@/store/modules/contract'
import { ContractAddDTO } from '@/api/contract';
import { getDictLabel } from '@/dict'

const contractPurposeLabel = computed(() => {
    return getDictLabel('contract_purpose', Number(contractData.value.contractPurpose))
})

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.form-item {
    margin-bottom: 16px;
    
    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>