<template>
    <a-drawer title="选择新责任人" class="common-drawer-small" v-model:visible="visible" unmount-on-close>
        <a-space direction="vertical" :size="16" fill>
            <a-space>
                <a-input v-model="searchParam" placeholder="请输入姓名/手机号/帐号"></a-input>
                <a-button type="primary">查询</a-button>
            </a-space>
            <a-table :data="tableData" :columns="columns" :bordered="{ cell: true }" :pagination="pagination"
                column-resizable :row-selection="rowSelection" rowKey="userId" @select="handleSelect"
                @page-change="onPageChange" @page-size-change="onPageSizeChange">
                <template #status="{ record }">
                    <a-tag :color="record.status === '0' ? 'green' : 'red'">
                        {{ record.status === '0' ? '启用' : '停用' }}
                    </a-tag>
                </template>
            </a-table>
        </a-space>
        <template #footer>
            <a-space>
                <a-button @click="visible = false">取消</a-button>
                <a-button type="primary" @click="handleSubmit">确定</a-button>
            </a-space>
        </template>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getUserList } from '@/api/user'
import { updateContractOwner } from '@/api/contract'
import { Message } from '@arco-design/web-vue';

const visible = ref(false)

const open = (ids: string[]) => {
    console.log(ids);
    tableData.value = []
    pagination.value = {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: true,
        showJumper: true,
        showPageSize: true,
    }
    selectedRecord.value = undefined
    contractIds.value = ids
    visible.value = true
    getTableData()
}

const rowSelection = {
    type: 'radio'
};
const contractIds = ref<string[]>([])
const searchParam = ref('')
const columns = [
    {
        title: '姓名',
        dataIndex: 'nickName',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '帐号',
        dataIndex: 'userName',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '片区',
        dataIndex: 'postName',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '手机号',
        dataIndex: 'phonenumber',
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '状态',
        dataIndex: 'status',
        slotName: 'status',
        align: 'center',
        ellipsis: true,
        tooltip: true
    }
]
const tableData = ref<any[]>([])
// 分页数据
const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
})
// 分页方法
const onPageChange = (current: number) => {
    pagination.value.current = current
    getTableData()
}
const onPageSizeChange = (pageSize: number) => {
    pagination.value.current = 1
    pagination.value.pageSize = pageSize
    getTableData()
}
const getTableData = async () => {
    const params = {
        pageNum: pagination.value.current,
        pageSize: pagination.value.pageSize,
        searchParam: searchParam.value
    }
    const { rows, total } = await getUserList(params)
    tableData.value = rows
    pagination.value.total = total
}
const selectedRecord = ref()
const handleSelect = (rowKeys: string[], rowKey: string, record: any) => {
    selectedRecord.value = record
}

const emit = defineEmits(['submit'])
const handleSubmit = async () => {
    if(!selectedRecord.value) {
        Message.warning('请选择责任人')
        return
    }
    const params = {
        contractIds: contractIds.value,
        ownerId: selectedRecord.value.userId,
        ownerName: selectedRecord.value.nickName
    }
    await updateContractOwner(params)
    Message.success('更新成功')
    visible.value = false
    emit('submit')
 }

defineExpose({
    open
})
</script>

<style lang="less" scoped></style>