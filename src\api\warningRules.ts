import http from './index'

const systemUrl = '/business-rent-admin'

// 预警规则数据类型
export interface WarningRulesAddDTO {
    id?: string // 主键ID
    expireStatus?: boolean // 即将到期配置状态 0-否,1-是
    expireDays?: number // 合同即将到期前多少天
    waterElectricityStatus?: boolean // 水电异常配置状态 0-否,1-是
    electricityNum?: number // 电度数
    coldWaterNum?: number // 冷水吨数
    hotWaterNum?: number // 热水吨数
    billStatus?: boolean // 催缴状态 0-否,1-是
    isDel?: boolean // 0-否,1-是
}

// 通用返回结果
export interface AjaxResult {
    error?: boolean
    success?: boolean
    warn?: boolean
    empty?: boolean
    [key: string]: any
}

/**
 * 新增预警规则
 * @param data 预警规则数据
 * @returns Promise<AjaxResult>
 */
export function addWarningRules(data: WarningRulesAddDTO): Promise<AjaxResult> {
    return http.post<AjaxResult>(`${systemUrl}/warningRules`, data)
}

/**
 * 获取预警规则详细信息
 * @returns Promise<AjaxResult>
 */
export function getWarningRulesDetail(): Promise<AjaxResult> {
    return http.get<AjaxResult>(`${systemUrl}/warningRules/detail`)
}

/**
 * 删除预警规则
 * @param ids 预警规则ID列表
 * @returns Promise<AjaxResult>
 */
export function deleteWarningRules(ids: string[]): Promise<AjaxResult> {
    return http.delete<AjaxResult>(`${systemUrl}/warningRules/delete`, { ids })
}
