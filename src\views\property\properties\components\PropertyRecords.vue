<template>
  <div class="property-records">
    <!-- 数据表格 -->
    <a-table :columns="columns" :scroll="{x: 1}" :data="tableData" :pagination="pagination" :bordered="{ cell: true }" :stripe="true"
      :loading="loading" @page-change="onPageChange" @page-size-change="onPageSizeChange">
      <template #applicationNo="{ record }">
        <PermissionLink v-permission="['rent:room:merge:detail']" @click="handleViewDetail(record)">{{ record.applicationNo }}</PermissionLink>
      </template>
      <template #operation="{ record }">
        <a-space>
          <!-- 草稿状态：提交、编辑、删除 -->
          <template v-if="record.approvalStatus === ApprovalStatus.PENDING_SUBMIT">
            <a-button v-permission="['rent:room:merge:submit']" type="text" size="mini" @click="handleSubmit(record)">提交</a-button>
            <a-button v-permission="['rent:room:merge:add']" type="text" size="mini" @click="handleEdit(record)">编辑</a-button>
            <a-button v-permission="['rent:room:mergeSplit:remove']" type="text" size="mini" status="danger" @click="handleDelete(record)">删除</a-button>
          </template>
          <template v-else-if="record.approvalStatus === ApprovalStatus.APPROVING">
            <a-button v-permission="['rent:room:merge:approval']" type="text" size="mini" @click="handleApprove(record)">审批</a-button>
          </template>
        </a-space>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { 
  getRoomMergeSplitList,
  submitMergeSplitApplication,
  deleteMergeSplitRecord,
  approveMergeSplitApplication,
  submitSplitRoom,
  submitMergeRoom,
  type RoomMergeSplitQueryDTO, 
  type RoomMergeSplitVo,
  AdjustmentType,
  ApprovalStatus,
  approveSplitRoom,
  approveMergeRoom
} from '@/api/room'

// 定义props
const props = defineProps<{
  selection: {
    projectId: string
    projectName: string
    blockId: string
    blockName: string
    buildingId: string
    buildingName: string
  }
  filterForm: {
    projectId: string
    parcelId?: string
    buildingId?: string
    roomName: string
    adjustmentType?: number
    approvalStatus?: number
    createTime: string[]
    createTimeStart: string
    createTimeEnd: string
    pageNum: number
    pageSize: number
  }
  loading: boolean
}>()

// 定义emits
const emit = defineEmits<{
  'update:loading': [value: boolean]
  'open-split-drawer': [data: { splitId: string, readonly: boolean }]
  'open-merge-drawer': [data: { mergeId: string, readonly: boolean }]
}>()

// 表格数据
const tableData = ref<RoomMergeSplitVo[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
})

// 表格列定义
const columns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { 
    title: '审批状态', 
    dataIndex: 'approvalStatus', 
    width: 120, 
    ellipsis: true, 
    tooltip: true, 
    align: 'center',
    render: (data: any) => {
      const statusMap: Record<number, string> = {
        0: '待提交',
        1: '审批中',
        2: '已通过',
        3: '已驳回'
      }
      return statusMap[data.record.approvalStatus] || '未知'
    }
  },
  { 
    title: '调整类型', 
    dataIndex: 'operationType', 
    width: 120, 
    ellipsis: true, 
    tooltip: true, 
    align: 'center',
    render: (data: any) => {
      return data.record.operationType === AdjustmentType.SPLIT ? '拆分' : '合并'
    }
  },
  { title: '申请编号', dataIndex: 'applicationNo', slotName: 'applicationNo', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '地块', dataIndex: 'areaName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼栋', dataIndex: 'buildingName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '原房源', dataIndex: 'sourceRoomInfo', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '调整后房源', dataIndex: 'targetRoomInfo', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '创建人', dataIndex: 'createByName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '创建日期', dataIndex: 'createTime', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '操作', slotName: 'operation', width: 210, ellipsis: false, tooltip: false, align: 'center', fixed: 'right' }
]

// 查询调整记录列表
const fetchAdjustmentRecords = async () => {
  // 如果没有项目ID，不调用接口
  if (!props.filterForm.projectId) {
    console.log('没有项目ID，跳过接口调用')
    emit('update:loading', false)
    return
  }

  try {
    emit('update:loading', true)
    const queryParams: RoomMergeSplitQueryDTO = {
      projectId: props.filterForm.projectId,
      parcelId: props.filterForm.parcelId,
      buildingId: props.filterForm.buildingId,
      roomName: props.filterForm.roomName,
      operationType: props.filterForm.adjustmentType,
      approvalStatus: props.filterForm.approvalStatus,
      createTimeStart: props.filterForm.createTimeStart,
      createTimeEnd: props.filterForm.createTimeEnd,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    
    console.log('PropertyRecords 调用接口，参数:', queryParams)
    const response = await getRoomMergeSplitList(queryParams)
    if (response.rows) {
      tableData.value = response.rows || []
      pagination.total = response.total || 0
      
      // 添加序号
      tableData.value.forEach((item: any, index) => {
        item.index = (pagination.current - 1) * pagination.pageSize + index + 1
      })
    }
  } catch (error) {
    console.error('查询调整记录失败:', error)
    Message.error('查询调整记录失败')
  } finally {
    emit('update:loading', false)
  }
}

// 分页变化
const onPageChange = (page: number) => {
  pagination.current = page
  fetchAdjustmentRecords()
}

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchAdjustmentRecords()
}

// 查看详情
const handleViewDetail = (record: RoomMergeSplitVo) => {
  if (!record.id) {
    Message.error('记录ID不存在')
    return
  }
  
  // 根据操作类型决定是拆分还是合并
  if (record.operationType === AdjustmentType.SPLIT) {
    // 通过emit通知父组件打开拆分抽屉，以只读模式查看
    emit('open-split-drawer', { splitId: record.id, readonly: true })
  } else if (record.operationType === AdjustmentType.MERGE) {
    // 通过emit通知父组件打开合并抽屉，以只读模式查看
    emit('open-merge-drawer', { mergeId: record.id, readonly: true })
  } else {
    Message.warning('未知的调整类型')
  }
}

// 提交申请
const handleSubmit = async (record: RoomMergeSplitVo) => {
  if (!record.id) {
    Message.error('记录ID不存在')
    return
  }

  const operationType = record.operationType === AdjustmentType.SPLIT ? '拆分' : '合并'
  
  Modal.confirm({
    title: '确认提交',
    content: `确定要提交当前${operationType}申请吗？提交后将进入审批流程。`,
    onOk: async () => {
      try {
        emit('update:loading', true)

        if (record.operationType === AdjustmentType.SPLIT) {
          // 拆分提交
          await submitSplitRoom({}, record.id)
        } else if (record.operationType === AdjustmentType.MERGE) {
          // 合并提交
          await submitMergeRoom({}, record.id)
        }
        
        Message.success(`${operationType}申请提交成功`)
        fetchAdjustmentRecords()
      } catch (error) {
        console.error(`${operationType}申请提交失败:`, error)
      } finally {
        emit('update:loading', false)
      }
    }
  })
}

// 编辑申请
const handleEdit = (record: RoomMergeSplitVo) => {
  if (!record.id) {
    Message.error('记录ID不存在')
    return
  }
  
  // 根据操作类型决定是拆分还是合并
  if (record.operationType === AdjustmentType.SPLIT) {
    // 通过emit通知父组件打开拆分抽屉，以编辑模式
    emit('open-split-drawer', { splitId: record.id, readonly: false })
  } else if (record.operationType === AdjustmentType.MERGE) {
    // 通过emit通知父组件打开合并抽屉，以编辑模式
    emit('open-merge-drawer', { mergeId: record.id, readonly: false })
  } else {
    Message.warning('未知的调整类型')
  }
}

// 删除申请
const handleDelete = async (record: RoomMergeSplitVo) => {
  try {
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除当前拆分合并数据吗？删除后无法恢复。`,
        onOk: async () => {
            try {
              await deleteMergeSplitRecord({ id: record.id! })
              Message.success('删除成功')
              fetchAdjustmentRecords()
            } catch (error) {
                console.error('删除失败:', error)
            }
        }
    })

  } catch (error) {
    console.error('删除申请失败:', error)
  }
}

// 审批申请
const handleApprove = async (record: RoomMergeSplitVo) => {
  if (!record.id) {
    Message.error('记录ID不存在')
    return
  }

  const operationType = record.operationType === AdjustmentType.SPLIT ? '拆分' : '合并'
  
  Modal.confirm({
    title: '确认审批',
    content: `确定要审批当前${operationType}申请吗？`,
    okText: '审批通过',
    cancelText: '拒绝',
    onOk: async () => {
      try {
        emit('update:loading', true)
        if (record.operationType === AdjustmentType.SPLIT) {
          await approveSplitRoom({ id: record.id!, approveResult: '2' })
        } else if (record.operationType === AdjustmentType.MERGE) {
          await approveMergeRoom({ id: record.id!, approveResult: '2' })
        }
        Message.success('审批成功')
        fetchAdjustmentRecords()
      } catch (error) {
        console.error('审批申请失败:', error)
      } finally {
        emit('update:loading', false)
      }
    },
    onCancel: async () => {
      try {
        emit('update:loading', true)
        if (record.operationType === AdjustmentType.SPLIT) {
          await approveSplitRoom({ id: record.id!, approveResult: '1' })
        } else if (record.operationType === AdjustmentType.MERGE) {
          await approveMergeRoom({ id: record.id!, approveResult: '1' })
        }
        Message.success('审批成功')
        fetchAdjustmentRecords()
      } catch (error) {
        console.error('审批申请失败:', error)
      } finally {
        emit('update:loading', false)
      }
    }
  })
}

// 监听全局刷新事件
onMounted(() => {
  document.addEventListener('refresh-property-records-data', fetchAdjustmentRecords)
  // 移除自动调用，改为只通过事件触发
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('refresh-property-records-data', fetchAdjustmentRecords)
})
</script>

<style scoped lang="less">
.property-records {
  .filter-bar {
    padding: 16px;
    background-color: var(--color-bg-2);
    border-radius: 4px;
  }
}
</style>