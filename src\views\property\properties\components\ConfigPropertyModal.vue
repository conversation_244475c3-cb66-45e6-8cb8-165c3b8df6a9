<template>
  <a-modal
    v-model:visible="visible"
    :title="'创建房源--配置房源属性'"
    :width="900"
    @cancel="handleCancel"
  >
    <div class="modal-scroll-content">
      <a-form label-align="right" :model="form" layout="horizontal" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="物业类型" field="productType" :rules="[{ required: true, message: '请选择物业类型' }]">
              <a-tree-select
                v-model="form.productType"
                :data="propertyTypeOptions"
                placeholder="请选择物业类型"
                allow-clear
                :field-names="{
                  key: 'dictValue',
                  title: 'dictLabel',
                  children: 'childList'
                }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="运营主体" field="mainBody" :rules="[{ required: true, message: '请选择运营主体' }]">
              <a-select v-model="form.mainBody" placeholder="请选择运营主体">
                <a-option :value="1">商服</a-option>
                <a-option :value="2">众创城</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="产权情况" field="propertyStatus" :rules="[{ required: true, message: '请选择产权情况' }]">
              <a-select v-model="form.propertyStatus" placeholder="请选择产权情况">
                <a-option value="无证">无证</a-option>
                <a-option value="有证">有证</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="交付状态" field="deliveryStatus" :rules="[{ required: true, message: '请选择交付状态' }]">
              <a-select v-model="form.deliveryStatus" placeholder="请选择交付状态">
                <a-option value="未交付">未交付</a-option>
                <a-option value="已交付">已交付</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="可招商日期" field="recruitDate">
              <a-date-picker v-model="form.recruitDate" style="width:100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="对外出租起始日期" field="leaseStartDate">
              <a-date-picker v-model="form.leaseStartDate" style="width:100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <section-title title="承租信息" />
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="商管承租成本单价" field="rentCost">
              <a-input v-model="form.rentCost" type="number" placeholder="请输入">
                <template #append>元/平米/月</template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="商管承租日期" field="rentDate">
              <a-range-picker v-model="form.rentDate" style="width:100%" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <template #footer>
      <div style="text-align: center;">
        <a-button type="primary" :loading="loading" @click="handleSave">保存</a-button>
        <a-button style="margin-left: 16px;" @click="handleCancel">取消</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, defineExpose, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useDictSync } from '@/utils/dict'
import { introduceReceivedRooms, type RoomAddDTO } from '@/api/room'

const visible = ref(false)
const emit = defineEmits(['confirm', 'cancel'])
const loading = ref(false)

// 字典数据
const propertyTypeOptions = ref<any[]>([])

// 加载字典数据
const loadDictionaries = async () => {
  try {
    // 获取物业类型字典
    const dictData = await useDictSync('property_type')
    if (dictData.property_type) {
      const dictList = dictData.property_type
      // 组织成树形结构
      const treeData = dictList.filter((item: any) => !item.parentCode)
      treeData.forEach((parent: any) => {
        parent.childList = dictList.filter((child: any) => child.parentCode === parent.dictCode)
      })
      propertyTypeOptions.value = treeData
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 接收额外数据的属性
interface InitialData {
  roomIds?: string[]
  projectId?: string
  [key: string]: any
}

const form = reactive({
  productType: '',
  mainBody: '',
  propertyStatus: '无证',
  deliveryStatus: '未交付',
  recruitDate: '',
  leaseStartDate: '',
  rentCost: '',
  rentDate: []
})

// 存储额外数据
const extraData = reactive<InitialData>({
  roomIds: [],
  projectId: ''
})

const show = (init?: any) => {
  // 重置表单为默认值
  form.productType = ''
  form.mainBody = ''
  form.propertyStatus = '无证'
  form.deliveryStatus = '未交付'
  form.recruitDate = ''
  form.leaseStartDate = ''
  form.rentCost = ''
  form.rentDate = []
  
  // 重置额外数据
  extraData.roomIds = []
  extraData.projectId = ''
  
  // 如果有传入的初始化数据，则使用该数据覆盖默认值
  if (init) {
    // 表单数据
    if (init.form) {
      Object.assign(form, init.form)
    }
    
    // 额外数据
    if (init.roomIds) {
      extraData.roomIds = init.roomIds
    }
    // 处理从SelectRoomsModal传递的selectedRooms字段
    else if (init.selectedRooms) {
      extraData.roomIds = init.selectedRooms
    }
    
    if (init.projectId) {
      extraData.projectId = init.projectId
    }
  }
  
  visible.value = true
}

const handleSave = async () => {
  // 验证必填数据
  if (!extraData.roomIds || extraData.roomIds.length === 0) {
    Message.error('房源ID不能为空')
    return
  }
  
  if (!extraData.projectId) {
    Message.error('项目ID不能为空')
    return
  }
  
  if (!form.productType) {
    Message.error('请选择物业类型')
    return
  }
  
  if (!form.mainBody) {
    Message.error('请选择运营主体')
    return
  }
  
  try {
    loading.value = true
    
    // 构建API请求数据
    const apiData: RoomAddDTO = {
      // 必要的房源信息
      propertyType: form.productType,
      operationSubject: Number(form.mainBody),
      propertyStatus: form.propertyStatus,
      paymentStatus: form.deliveryStatus === '已交付' ? 2 : 1, // 1-未交付 2-已交付
      
      // 其他属性
      rentalStartDate: form.recruitDate || undefined,
      externalRentStartDate: form.leaseStartDate || undefined,
      
      // 承租信息
      firstRentPrice: form.rentCost ? parseFloat(form.rentCost) : undefined,
      firstRentStartDate: form.rentDate?.[0] || undefined,
      firstRentEndDate: form.rentDate?.[1] || undefined,
      
      // 额外数据
      projectId: extraData.projectId,
      // 添加系统房间ID列表
      sysRoomIds: extraData.roomIds
    }
    
    // 调用接口
    const response = await introduceReceivedRooms(apiData)
    
    if (response && response.code === 200) {
      setTimeout(() => {
        emit('confirm', { ...form, success: true })
      }, 300);
      visible.value = false
    } else {
      Message.error(response?.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存房源属性失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  setTimeout(() => {
    emit('cancel')
  }, 300);
  visible.value = false
}

// 组件挂载时初始化数据
onMounted(() => {
  loadDictionaries()
})

defineExpose({ show })
</script> 

<style scoped lang="less">
  .section-title {
    margin: 16px 0;
    &.first-child{
      margin-top: 8px;
    }
  }
  .modal-scroll-content {
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 8px;
  }
</style>
