# FlowUsageRecord 流水使用记录组件

一个用于显示流水使用记录和退款记录的表格组件，包含两个标签页。

## 功能特性

- **使用记录标签页**: 显示收入流水的使用记录
- **退款记录标签页**: 显示支出类流水或流水退款记录
- **操作按钮**: 支持取消记账和确认操作
- **响应式表格**: 支持横向滚动，适配不同屏幕尺寸
- **状态标签**: 不同确认状态使用不同颜色的标签显示

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| flowId | string | - | 流水ID |
| usageData | UsageRecord[] | [] | 使用记录数据 |
| refundData | RefundRecord[] | [] | 退款记录数据 |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| takeRecord | 取消记账时触发 | (record: UsageRecord \| RefundRecord) |
| confirm | 确认时触发 | (record: UsageRecord) |

## 数据类型

### UsageRecord (使用记录)

```typescript
interface UsageRecord {
    id: string
    projectName: string      // 项目名称
    accountType: string      // 单据类型
    orderNo: string         // 单据编号
    roomUnit: string        // 租赁单元
    customerName: string    // 客户名称
    orderType: string       // 账单类型
    orderPeriod: string     // 账单起始周期
    type: string           // 类型
    recordAmount: number   // 记账金额
    recordTime: string     // 记账时间
    recorder: string       // 记账人
    confirmStatus: string  // 确认状态
    confirmer: string      // 确认人
    confirmTime: string    // 确认时间
}
```

### RefundRecord (退款记录)

```typescript
interface RefundRecord {
    id: string
    refundType: string      // 退款类型
    refundNo: string       // 退款单号
    refundTarget: string   // 退款对象
    refundApplyTime: string // 退款申请时间
    refundApplicant: string // 退款申请人
    auditPassTime: string  // 审批通过时间
    type: string          // 类型
    recordAmount: number  // 记账金额
    recordTime: string    // 记账时间
    recorder: string      // 记账人
    confirmStatus: string // 确认状态
    confirmer: string     // 确认人
    confirmTime: string   // 确认时间
}
```

## 基础用法

```vue
<template>
    <FlowUsageRecord
        :flow-id="flowId"
        :usage-data="usageRecords"
        :refund-data="refundRecords"
        @take-record="handleTakeRecord"
        @confirm="handleConfirm"
    />
</template>

<script setup lang="ts">
import type { UsageRecord, RefundRecord } from '@/components/FlowUsageRecord'

const flowId = ref('flow_123')
const usageRecords = ref<UsageRecord[]>([
    {
        id: '1',
        projectName: '万洋（博罗）万洋众创城',
        accountType: 'deposit',
        orderNo: 'DD-XXXXXXXX',
        roomUnit: 'F230、F393',
        customerName: 'XX',
        orderType: 'deposit',
        orderPeriod: '/',
        type: '手动记账（收款）',
        recordAmount: 10000.00,
        recordTime: '2024-03-12 09:00:00',
        recorder: '张三',
        confirmStatus: 'manual_confirm',
        confirmer: '张三',
        confirmTime: '2024-03-12 09:00:00'
    }
])

const refundRecords = ref<RefundRecord[]>([])

const handleTakeRecord = (record: UsageRecord | RefundRecord) => {
    console.log('取消记账:', record)
}

const handleConfirm = (record: UsageRecord) => {
    console.log('确认:', record)
}
</script>
```

## 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| setUsageData | 设置使用记录数据 | (data: UsageRecord[]) |
| setRefundData | 设置退款记录数据 | (data: RefundRecord[]) |

```vue
<script setup lang="ts">
const flowUsageRecordRef = ref()

// 动态更新数据
const updateData = () => {
    flowUsageRecordRef.value?.setUsageData(newUsageData)
    flowUsageRecordRef.value?.setRefundData(newRefundData)
}
</script>

<template>
    <FlowUsageRecord ref="flowUsageRecordRef" />
</template>
```

## 样式自定义

组件支持通过CSS变量自定义样式：

```less
.flow-usage-record {
    // 自定义标签提示背景色
    .tab-tip {
        background: #ffeaa7;
        color: #2d3436;
    }
    
    // 自定义负金额颜色
    .negative-amount {
        color: #f53f3f;
    }
}
``` 