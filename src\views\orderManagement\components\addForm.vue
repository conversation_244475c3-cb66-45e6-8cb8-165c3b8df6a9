<template>
    <!-- Word文档预览组件 -->
    <WordViewer
        v-model="previewVisible"
        :file-url="previewFileUrl"
        :title="previewTitle"
    />

    <a-drawer :visible="drawerVisible" :title="drawerTitle" :mask-closable="true" @cancel="handleCancel" class="common-drawer-small">
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" status="success" @click="handleSave" v-if="isEdit">暂存</a-button>
                <a-button type="primary" status="danger" @click="handleSubmit" v-if="isEdit">确认提交</a-button>
                <a-button type="primary" v-if="!isEdit" @click="handlePrint">预览&打印</a-button>
            </a-space>
        </template>
        <a-form
            ref="formRef"
            :model="formData"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
        >
        <!-- 3.定单确认提交后，留在当前页面，展示定单收款账单码区域及生成按钮，点击生成，展示账单码信息
4.当选择了意向房源后，定单金额是否可退默认"否"；选择暂不确认房源，定单金额是否可退默认"是"；定单金额是否可退字段不允许编辑 -->
            <!-- <a-divider>基础信息</a-divider> -->
            <section-title title="基础信息" style="margin-bottom: 16px;" />
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="projectId"
                        label="所属项目"
                        required
                        :rules="[{ required: true, message: '请选择所属项目' }]"
                    >
                        <ProjectTreeSelect
                            v-model="formData.projectId"
                            :disabled="true"
                            v-if="isEdit"
                        />
                        <a-space v-else>
                            {{ formData.projectName || formData.projectId }}
                        </a-space>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="customerName"
                        label="客户名称"
                        required
                        :rules="[{ required: true, message: '请输入客户名称' }]"
                        :disabled="!isEdit"
                    >
                        <a-input
                            v-model="formData.customerName"
                            placeholder="请输入客户名称"
                            allow-clear
                            v-if="isEdit"
                        >
                        </a-input>
                        <a-space v-else>
                            {{ formData.customerName }}
                        </a-space>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="businessTypeArray"
                        label="意向物业类型"
                        :required="!formData.unknownSource"
                        :rules="businessTypeRules"
                    >
                    <!-- {{ formData.businessTypeArray }} -->
                        <a-cascader
                            v-model="formData.businessTypeArray"
                            :options="propertyTypeOptions"
                            placeholder="请选择意向物业类型"
                            allow-clear
                            expand-trigger="hover"
                            :field-names="{ value: 'code', label: 'name', children: 'children' }"
                            @change="handlePropertyTypeChange"
                            :key="`cascader-${formData.id}-${formData.businessTypeArray}`"
                            :disabled="formData.unknownSource"
                            v-if="isEdit"
                        />
                        <a-space v-else>
                            {{ formData.businessTypeName }}
                        </a-space>
                        <!-- <a-tag v-if="formData.unknownSource" color="orange" size="small" style="margin-left: 8px;">
                            暂不确认房源时无需选择物业类型
                        </a-tag> -->
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="intentRoomObject"
                        label="意向房源"
                        :required="!formData.unknownSource"
                        :rules="intentRoomRules"
                        :disabled="!isEdit"
                    >
                        <a-space v-if="isEdit">
                            <RoomTreeSelect
                                v-model="formData.intentRoomObject"
                                value-type="object"
                                placeholder="请选择"
                                :project-id="formData.projectId"
                                :building-type="formData.businessType"
                                :disabled="formData.unknownSource"
                                allow-clear
                                style="width: 300px"
                                @change="handleRoomSelectChange"
                            />
                            <a-checkbox
                                v-model="formData.unknownSource"
                                @change="handleUnknownSourceChange"
                            >
                                暂不确认房源
                            </a-checkbox>
                        </a-space>
                        <a-space v-else>
                            {{ formData.roomName }}
                        </a-space>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="depositAmount"
                        label="定单金额"
                        required
                        :rules="depositAmountRules"
                        :disabled="!isEdit"
                    >
                        <a-input-number
                            v-model="formData.depositAmount"
                            placeholder="请输入金额"
                            :min="0"
                            :precision="2"
                            style="width: 100%"
                            v-if="isEdit"
                        >
                            <template #append>元</template>
                        </a-input-number>
                        <a-space v-else>
                            {{ formData.depositAmount }}
                        </a-space>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="dueDate"
                        label="应收日期"
                        required
                        :rules="[{ required: true, message: '请选择应收日期' }]"
                        :disabled="!isEdit"
                    >
                        <a-date-picker
                            v-model="formData.dueDate"
                            style="width: 100%"
                            v-if="isEdit"
                        />
                        <a-space v-else>
                            {{ formData.dueDate }}
                        </a-space>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="expectedSignDate"
                        label="预计签约日期"
                        :disabled="!isEdit"
                    >
                        <a-date-picker
                            v-model="formData.expectedSignDate"
                            style="width: 100%"
                            v-if="isEdit"
                        />
                        <a-space v-else>
                            {{ formData.expectedSignDate }}
                        </a-space>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="refundable"
                        label="定单金额是否可退"
                        required
                        :rules="[{ required: true, message: '请选择是否可退' }]"
                        :disabled="!isEdit"
                    >
                        <a-radio-group
                            v-model="formData.refundable"
                            :disabled="!isRefundableEditable"
                            v-if="isEdit"
                        >
                            <a-radio value="是">是</a-radio>
                            <a-radio value="否">否</a-radio>
                        </a-radio-group>
                        <a-space v-else>
                            {{ formData.refundable }}
                        </a-space>
                        <a-tag v-if="!isRefundableEditable && formData.intentRoomObject?.id" color="blue" size="small" style="margin-left: 8px;">
                            选择意向房源后默认不可退
                        </a-tag>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 收款码区域 - 只在未转签约状态下显示 -->
<!-- {{ formData.status }} -->
            <template v-if="formData.status === 1">
                <section-title title="定单收款账单码" style="margin-bottom: 16px;" />
                <div class="qrcode-container">
                    <div class="qrcode-actions">
                        <a-button type="primary" @click="generateQRCode">
                            {{ paymentUrl ? '重新生成' : '生成收款码' }}
                        </a-button>
                    </div>

                    <div class="collect-code-content">
                        <div class="order-info" v-if="paymentUrl">
                            <div class="info-item">
                                <span class="label">项目名称：</span>
                                <span class="value">{{ formData.projectName }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">意向房源：</span>
                                <span class="value">{{ formData.intentRoomObject?.name || '暂不确认房源' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">客户名称：</span>
                                <span class="value">{{ formData.customerName }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">支付金额：</span>
                                <span class="value amount">¥{{ formatAmount(formData.depositAmount) }}</span>
                            </div>
                        </div>

                        <!-- 使用新的QRCode组件 -->
                        <div class="qrcode-display">
                            <QRCode
                                :value="paymentUrl"
                                :size="200"
                                :show-placeholder="true"
                                :show-download="!!paymentUrl"
                                placeholder-text="点击上方按钮生成收款码"
                                @generated="handleQRCodeGenerated"
                                @error="handleQRCodeError"
                            />
                        </div>

                        <!-- 支付链接信息 -->
                        <!-- <div v-if="paymentUrl" class="payment-link">
                            <a-input
                                :model-value="paymentUrl"
                                readonly
                                placeholder="支付链接"
                                size="small"
                            >
                                <template #append>
                                    <a-button @click="copyPaymentUrl" size="small">
                                        复制链接
                                    </a-button>
                                </template>
                            </a-input>
                        </div> -->
                    </div>
                </div>
            </template>
        </a-form>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import QRCode from '@/components/QRCode/index.vue'
import { saveOrder, printBooking, type BookingPrintDto, type EnclosureInfo, getOrderDetail } from '@/api/orderManagement'
import RoomTreeSelect from '@/components/roomTreeSelect/index.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import { mapGamepadToXbox360Controller } from '@vueuse/core'

const emit = defineEmits(['success', 'cancel'])
const drawerVisible = ref(false)
const isEdit = ref(true)
const formRef = ref()

// 定单模式：add-新增, edit-编辑, view-查看
const orderMode = ref<'add' | 'edit' | 'view'>('add')

// 动态标题
const drawerTitle = computed(() => {
  switch (orderMode.value) {
    case 'add':
      return '新增定单'
    case 'edit':
      return '编辑定单'
    case 'view':
      return '查看定单'
    default:
      return '新增定单'
  }
})
const formData = reactive({
    id: '', // 主键ID
    projectId: '', // 项目id
    customerName: '', // 客户名称
    propertyType: '', // 意向物业类型
    roomId: '', // 房源id
    roomName: '', // 房源名称
    bookingAmount: 0, // 定单金额
    receivableDate: '', // 应收日期
    expectSignDate: '', // 预计签约日期
    isRefundable: 0, // 定单金额是否可退:0-否,1-是
    cancelTime: '', // 作废时间
    cancelBy: '', // 作废人
    cancelByName: '', // 作废人姓名
    cancelReason: 0, // 作废原因
    isRefund: 0, // 是否退款:0-否,1-是
    cancelEnclosure: '', // 退定附件
    cancelRemark: '', // 退定说明
    status: 0, // 状态:0-草稿,1-待收费,2-已生效,3-已转签,4-已作废
    contractId: '', // 转签约合同id
    refundId: '', // 退款单id
    isDel: 0, // 0-否,1-是
    isSubmit: 0, // 0-暂存,1-提交

    // 界面中使用的临时字段
    businessType: '', // 业务类型（存储编码值）
    businessTypeName: '', // 业务类型名称（显示用）
    businessTypeArray: '', // 业务类型级联选择值（string类型）
    intentRoom: '', // 意向房源（显示用）
    intentRoomObject: {
        id: '',
        name: '',
    }, // 意向房源（对象用）
    depositAmount: 0, // 定金金额
    dueDate: '', // 应收日期
    expectedSignDate: '', // 预计签约日期
    refundable: '否', // 是否可退
    unknownSource: false, // 是否暂不确认房源
    projectName: '', // 项目名称
})

// 房源树形数据
const roomTreeData = [
    {
        title: '项目',
        key: 'project',
        children: [
            {
                title: '地块',
                key: 'block',
                children: [
                    {
                        title: '楼栋-77幢',
                        key: 'building-77',
                        children: [
                            { title: '101', key: '101' }
                        ]
                    },
                    {
                        title: '楼栋-78幢',
                        key: 'building-78',
                        children: [
                            { title: '101', key: '101' },
                            { title: '102', key: '102' }
                        ]
                    }
                ]
            }
        ]
    }
]

const showRoomTree = ref(false)
const showRefundTip = computed(() => formData.unknownSource)
const qrcodeUrl = ref('')
const paymentUrl = ref('') // 支付链接

// 物业类型选项配置
const propertyTypeOptions = [
    {
        code: '10',
        name: '宿舍',
        sort: 1
    },
    {
        code: '20',
        name: '厂房',
        sort: 5
    },
    {
        code: '30',
        name: '商业',
        sort: 10,
        children: [
            {
                code: '31',
                name: '商铺',
                sort: 10
            },
            {
                code: '32',
                name: '综合体',
                sort: 15
            },
            {
                code: '33',
                name: '中央食堂',
                sort: 20
            }
        ]
    },
    {
        code: '40',
        name: '车位',
        sort: 25
    },
    {
        code: '50',
        name: '办公',
        sort: 30
    }
]

// 计算是否可退字段是否可编辑
// 选择了具体房源时不可编辑，暂不确认房源时可编辑
const isRefundableEditable = computed(() => {
    return formData.unknownSource || !formData.intentRoomObject?.id
})

// 级联选择器显示值
const cascaderDisplayValue = computed(() => {
    if (!formData.businessTypeArray) {
        return ''
    }

    // 直接根据编码获取名称
    return getBusinessTypeName(formData.businessTypeArray)
})

// 意向房源验证规则
const intentRoomRules = computed(() => {
    return [
        {
            required: !formData.unknownSource,
            message: '请选择意向房源',
            validator: (value: any, callback: (error?: string) => void) => {
                if (formData.unknownSource) {
                    // 如果勾选了"暂不确认房源"，则不需要验证
                    callback()
                } else {
                    // 如果没有勾选"暂不确认房源"，则必须选择房源
                    if (!value || !value.id) {
                        callback('请选择意向房源')
                    } else {
                        callback()
                    }
                }
            }
        }
    ]
})

// 定单金额验证规则
const depositAmountRules = computed(() => {
    return [
        {
            required: true,
            message: '请输入定单金额'
        },
        {
            validator: (value: any, callback: (error?: string) => void) => {
                if (value === null || value === undefined || value === '') {
                    callback('请输入定单金额')
                } else if (Number(value) <= 0) {
                    callback('定单金额必须大于0')
                } else {
                    callback()
                }
            }
        }
    ]
})

// 意向物业类型验证规则
const businessTypeRules = computed(() => {
    return [
        {
            required: !formData.unknownSource,
            message: '请选择意向物业类型',
            validator: (value: string, callback: (error?: string) => void) => {
                // 如果勾选了"暂不确认房源"，则跳过验证
                if (formData.unknownSource) {
                    callback()
                    return
                }

                // 未勾选"暂不确认房源"时，检查值是否为空
                if (!value || value.trim() === '') {
                    callback('请选择意向物业类型')
                } else {
                    callback()
                }
            }
        }
    ]
})

// 初始化表单数据
const initFormData = (record: any, projectId?: string, roomInfo?: any) => {
    console.log('initFormData调用参数:', { record, projectId, roomInfo })
    if (record && record.id) {
        console.log('进入编辑模式分支')
        // 根据接口返回的数据格式初始化表单
        // "roomName": "暂不确认房源",
        formData.id = record.id || ''
        formData.projectId = record.projectId || projectId || ''
        formData.customerName = record.customerName || ''
        formData.propertyType = record.propertyType || record.businessType || ''
        formData.businessType = record.propertyType || record.businessType || '' // 存储编码
        formData.businessTypeName = getBusinessTypeName(formData.businessType) // 获取对应的名称
        // 根据businessType编码反向设置businessTypeArray
        formData.businessTypeArray = getBusinessTypeArray(formData.businessType)

        console.log('编辑模式初始化物业类型:', {
            原始businessType: record.propertyType || record.businessType,
            设置后businessType编码: formData.businessType,
            设置后businessTypeName名称: formData.businessTypeName,
            businessTypeArray: formData.businessTypeArray,
            级联选择器配置: '使用code作为value，name作为label'
        })

        // 确保级联选择器能正确显示
        nextTick(() => {
            console.log('nextTick后的businessTypeArray:', formData.businessTypeArray)
            // 强制清除表单验证，确保级联选择器正确渲染
            formRef.value?.clearValidate('businessTypeArray')
        })
        formData.roomId = record.roomId || ''
        formData.roomName = record.roomName,
        formData.intentRoom = record.roomName || record.intentRoom || ''
        formData.intentRoomObject = {
            id: record.roomId || '',
            name: record.roomName || '',
        }
        formData.bookingAmount = record.bookingAmount || record.depositAmount || 0
        formData.depositAmount = record.bookingAmount || record.depositAmount || 0
        formData.receivableDate = record.receivableDate || record.dueDate || ''
        formData.dueDate = record.receivableDate || record.dueDate || ''
        formData.expectSignDate = record.expectSignDate || record.expectedSignDate || ''
        formData.expectedSignDate = record.expectSignDate || record.expectedSignDate || ''
        formData.isRefundable = record.isRefundable || (record.refundable === '是' ? 1 : 0)
        formData.refundable = record.isRefundable === 1 || record.refundable === '是' ? '是' : '否'
        formData.status = record.status || 0
        formData.unknownSource = record.roomName === '暂不确认房源' ? true : false
        formData.intentRoom = record.roomName === '暂不确认房源' ? '' : record.roomName
        formData.projectName = record.projectName || ''
    } else {
        console.log('进入新增模式分支')
        // 新增时默认值
        formData.id = ''
        formData.projectId = projectId || ''
        formData.customerName = ''

        // 如果传入了房源信息，则预填充房源相关数据
        if (roomInfo) {
            console.log('开始预填充房源信息:', roomInfo)
            formData.propertyType = roomInfo.propertyType || ''
            formData.businessType = roomInfo.propertyType || ''
            formData.businessTypeName = getBusinessTypeName(roomInfo.propertyType || '')
            formData.businessTypeArray = getBusinessTypeArray(roomInfo.propertyType || '')
            formData.roomId = roomInfo.roomId || ''
            formData.roomName = roomInfo.roomName || ''
            formData.intentRoom = roomInfo.roomName || ''
            formData.intentRoomObject = {
                id: roomInfo.roomId || '',
                name: roomInfo.roomName || '',
            }
            formData.refundable = '否' // 选择了具体房源时，默认不可退
            formData.unknownSource = false
            console.log('新增模式预填充房源信息:', {
                roomId: formData.roomId,
                roomName: formData.roomName,
                propertyType: formData.propertyType,
                businessType: formData.businessType,
                businessTypeName: formData.businessTypeName,
                businessTypeArray: formData.businessTypeArray
            })
        } else {
            formData.propertyType = ''
            formData.businessType = ''
            formData.businessTypeName = ''
            formData.businessTypeArray = ''
            formData.roomId = ''
            formData.roomName = ''
            formData.intentRoom = ''
            formData.intentRoomObject = {
                id: '',
                name: '',
            }
            formData.refundable = '否'
            formData.unknownSource = false
            console.log('新增模式初始化物业类型:', {
                businessType: formData.businessType,
                businessTypeName: formData.businessTypeName,
                businessTypeArray: formData.businessTypeArray
            })
        }

        formData.bookingAmount = 0
        formData.depositAmount = 0
        formData.receivableDate = ''
        formData.dueDate = ''
        formData.expectSignDate = ''
        formData.expectedSignDate = ''
        formData.isRefundable = 0
        formData.status = 0
        formData.isSubmit = 0
        formData.projectName = ''
    }
    formRef.value.clearValidate()

}

// 根据物业类型编码获取对应的级联选择器值（string）
const getBusinessTypeArray = (businessTypeCode: string): string => {
    console.log('getBusinessTypeArray 输入编码:', businessTypeCode)
    if (!businessTypeCode) return ''

    // 直接返回编码本身，级联选择器会自动处理层级关系
    console.log('返回编码:', businessTypeCode)
    return businessTypeCode
}

// 根据编码获取物业类型名称
const getBusinessTypeName = (businessTypeCode: string): string => {
    if (!businessTypeCode) return ''

    // 先查找一级分类
    for (const option of propertyTypeOptions) {
        if (option.code === businessTypeCode) {
            return option.name
        }
        // 再查找二级分类
        if (option.children) {
            for (const child of option.children) {
                if (child.code === businessTypeCode) {
                    return child.name
                }
            }
        }
    }
    return ''
}

// 处理物业类型选择变化
const handlePropertyTypeChange = (value: string) => {
    console.log('物业类型选择变化:', value, '类型:', typeof value)

    if (value) {
        // 直接使用选择的编码值
        formData.businessType = value // 存储编码
        formData.businessTypeName = getBusinessTypeName(value) // 获取对应的名称

        console.log('物业类型处理:', {
            selectedCode: value,
            businessType: formData.businessType,
            businessTypeName: formData.businessTypeName
        })
    } else {
        formData.businessType = ''
        formData.businessTypeName = ''
    }

    // 物业类型变化时，清空意向房源选择（因为筛选条件改变了）
    if (!formData.unknownSource) {
        formData.intentRoomObject = {
            id: '',
            name: '',
        }
        formData.roomId = ''
        formData.roomName = ''
        formData.intentRoom = ''

        // 清除意向房源的验证错误
        nextTick(() => {
            formRef.value?.clearValidate('intentRoomObject')
        })

        console.log('物业类型变化，已清空意向房源信息')
    }

    console.log('设置后的businessType(编码):', formData.businessType)
    console.log('设置后的businessTypeName(名称):', formData.businessTypeName)
}

const handleUnknownSourceChange = () => {
    console.log('formData.unknownSource', formData.unknownSource)
    if (formData.unknownSource) {
        // 勾选"暂不确认房源"时
        formData.refundable = '是'

        // 清空意向房源选择
        formData.intentRoomObject = {
            id: '',
            name: '',
        }

        // 清空意向物业类型选择
        formData.businessTypeArray = ''
        formData.businessType = ''
        formData.businessTypeName = ''

        nextTick(() => {
            // 清除意向房源和意向物业类型的验证错误（因为勾选了暂不确认房源）
            formRef.value?.clearValidate('intentRoomObject')
            formRef.value?.clearValidate('businessTypeArray')
        })
    } else {
        // 取消勾选"暂不确认房源"时
        // 重新启用房源选择器，用户可以重新选择
        // 如果当前没有选择房源，设置为"否"
        if (!formData.intentRoomObject?.id) {
            formData.refundable = '否'
        } else {
            // 如果已有房源选择，设置为"否"
            formData.refundable = '否'
        }

        nextTick(() => {
            // 重新验证意向房源和意向物业类型字段（因为取消了暂不确认房源，现在需要必填）
            formRef.value?.validateField('intentRoomObject')
            formRef.value?.validateField('businessTypeArray')
        })
    }
}

// 取消
const handleCancel = () => {
    drawerVisible.value = false
}

// 打印预览状态
const previewVisible = ref(false)
const previewFileUrl = ref('')
const previewTitle = ref('')

// 打印 预览&打印
const handlePrint = async () => {
    try {
        // 先检查表单验证
        const errors = await formRef.value.validate()
        if (errors) {
            Message.warning('请先完善表单信息')
            return
        }

        let printId = formData.id

        // 如果是新增模式且没有保存，先自动暂存
        if (orderMode.value === 'add' && !formData.id) {
            try {
                const submitData = {
                    ...prepareSubmitData(),
                    isSubmit: 0 // 标记为暂存
                }
                const res = await saveOrder(submitData)
                if (res.code === 200) {
                    // 保存成功后更新表单ID，用于打印
                    formData.id = res.data?.id || res.data
                    printId = formData.id
                    Message.success('定单已自动暂存')
                } else {
                    return
                }
            } catch (saveError) {
                console.error('自动保存失败:', saveError)
                return
            }
        }

        // 准备打印数据
        const submitData = prepareSubmitData()
        const printData: BookingPrintDto = {
            id: printId,
            type: 10, // 定单类型
            // bookingData: {
            //     ...submitData,
            //     isDel: false // 修复类型错误：转换为boolean
            // }
        }

        const response = await printBooking(printData)

        if (response.data?.fileUrl) {
            // 设置预览数据
            previewFileUrl.value = response.data.fileUrl
            previewTitle.value = `定单预览 - ${formData.customerName || '未命名'}`
            previewVisible.value = true
            // Message.success('文件生成成功')
        } else {
            // Message.error('文件生成失败')
        }
    } catch (error) {
        console.error('打印失败:', error)
    }
}


// 房源选择处理
const handleRoomSelect = (selectedKeys: string[]) => {
    formData.intentRoom = selectedKeys[0]
    showRoomTree.value = false
}

// 生成收款码
const generateQRCode = () => {
    // 构建支付链接，这里可以根据实际业务需求构建
    const baseUrl = import.meta.env.VITE_APP_BASE_URL
    const orderId = formData.id || `temp_${Date.now()}`
    const amount = formData.depositAmount
    const customerName = encodeURIComponent(formData.customerName || '')

    // 构建支付链接 - 这里可以根据实际的支付系统来构建
    const paymentLink = `${baseUrl}/order-payment?id=${orderId}&amount=${amount}&customer=${customerName}&type=deposit`

    paymentUrl.value = paymentLink
    // Message.success('收款码生成成功')
}

// 处理二维码生成成功
const handleQRCodeGenerated = (dataUrl: string) => {
    qrcodeUrl.value = dataUrl
    console.log('二维码生成成功:', dataUrl)
}

// 处理二维码生成失败
const handleQRCodeError = (error: Error) => {
    // Message.error('二维码生成失败: ' + error.message)
    console.error('二维码生成失败:', error)
}

// 复制支付链接
const copyPaymentUrl = async () => {
    if (!paymentUrl.value) {
        Message.warning('暂无支付链接')
        return
    }

    try {
        await navigator.clipboard.writeText(paymentUrl.value)
        Message.success('支付链接已复制到剪贴板')
    } catch (error) {
        // 降级方案：使用传统方法复制
        const textArea = document.createElement('textarea')
        textArea.value = paymentUrl.value
        document.body.appendChild(textArea)
        textArea.select()
        try {
            document.execCommand('copy')
            Message.success('支付链接已复制到剪贴板')
        } catch (err) {
            Message.error('复制失败，请手动复制')
        }
        document.body.removeChild(textArea)
    }
}

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) {
        return '0.00'
    }
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 准备保存的数据
const prepareSubmitData = () => {
    // 日期格式化处理
    const formatDate = (dateStr: any) => {
        if (!dateStr) return undefined
        if (typeof dateStr === 'string') return dateStr
        if (dateStr instanceof Date) {
            return dateStr.toISOString()
        }
        return undefined
    }

    // 确保businessType有值
    if (!formData.businessType && formData.businessTypeArray) {
        console.log('businessType为空，重新处理businessTypeArray:', formData.businessTypeArray)
        // 如果businessType为空但有选择，重新设置businessType
        handlePropertyTypeChange(formData.businessTypeArray)
    }

    console.log('提交数据前检查:', {
        businessType编码: formData.businessType,
        businessTypeName名称: formData.businessTypeName,
        businessTypeArray: formData.businessTypeArray,
        最终提交的propertyType: formData.businessType
    })

    // 构建符合BookingAddDTO结构的数据
    return {
        id: formData.id || undefined,
        projectId: formData.projectId,
        customerName: formData.customerName,
        propertyType: formData.businessType, // 使用业务类型作为物业类型
        roomId: formData.intentRoomObject?.id,
        roomName: formData.intentRoomObject?.name,
        bookingAmount: formData.depositAmount,
        receivableDate: formatDate(formData.dueDate),
        expectSignDate: formatDate(formData.expectedSignDate),
        isRefundable: formData.refundable === '是' ? 1 : 0,
        cancelTime: formatDate(formData.cancelTime),
        cancelBy: formData.cancelBy || undefined,
        cancelByName: formData.cancelByName || undefined,
        cancelReason: formData.cancelReason || undefined,
        isRefund: formData.isRefund || 0,
        cancelEnclosure: formData.cancelEnclosure || undefined,
        cancelRemark: formData.cancelRemark || undefined,
        status: formData.status || 0,
        contractId: formData.contractId || undefined,
        refundId: formData.refundId || undefined,
        isDel: 0
    }
}

// 暂存
const handleSave = async () => {
    try {
        console.log('formData.intentRoomObject', formData)
        const errors = await formRef.value.validate()
        // console.log('errors---handleSave', errors)
        if (errors) return
        const submitData = {
            ...prepareSubmitData(),
            isSubmit: 0 // 标记为暂存
        }
        const res = await saveOrder(submitData)
        // console.log('res---handleSave', res)
        if (res.code === 200) {
            Message.success('暂存成功')
            emit('success')
            drawerVisible.value = false
        } else {
            // Message.error(res.msg || '暂存失败')
        }
    } catch (error) {
        // console.error('暂存失败:', error)
        // Message.error('暂存失败')
    }
}

// 提交
const handleSubmit = async () => {
    try {
        const errors = await formRef.value.validate()
        if (errors) return
        const submitData = {
            ...prepareSubmitData(),
            isSubmit: 1 // 标记为提交
        }

        const res = await saveOrder(submitData)
        // {
        //     "msg": "5d7b67b58e5703cd128d8a119b1e43c8",
        //     "code": 200
        // }
        // console.log('res---handleSubmit', res)
        if (res.code === 200) {
            Message.success('提交成功')
            emit('success')
            // 直接确认提交就自动生成收款码
            const res2 = await getOrderDetail(res.msg)
            orderMode.value = 'view'
            isEdit.value = false
            // drawerVisible.value = true
            initFormData(res2.data, undefined, undefined)
            // formData.status = res2.data.status
            // formData.id = res.msg
            // formData.depositAmount = res2.data.depositAmount
            // formData.dueDate = res2.data.dueDate
            // formData.expectedSignDate = res2.data.expectedSignDate
            // formData.customerName = res2.data.customerName
            // formData.customerPhone = res2.data.customerPhone
            // formData.customerIdCard = res2.data.customerIdCard
            // formData.customerIdCardType = res2.data.customerIdCardType
            // isEdit.value = false
            generateQRCode()
            // drawerVisible.value = false
        } else {
            // Message.error(res.msg || '提交失败')
        }
    } catch (error) {
        // console.error('提交失败:', error)
        // Message.error('提交失败')
        // return Promise.reject(error)
    }
}

// 监听表单数据变化
// 注意：这个监听器已经被 handleUnknownSourceChange 函数替代
// 避免重复处理逻辑

// 处理房源选择变化
const handleRoomSelectChange = (value: any, roomData: any) => {
    console.log('房源选择变化:', { value, roomData })

    if (value && roomData) {
        // 按照"地块-楼栋-房源"格式拼接显示名称
        const displayName = formatRoomDisplayName(roomData)

        // 更新formData中的房源信息
        formData.intentRoomObject = {
            id: value.id,
            name: displayName // 使用格式化后的名称
        }
        formData.roomId = value.id
        formData.roomName = displayName // 提交接口时使用的roomName
        formData.intentRoom = displayName

        console.log('房源信息更新:', {
            roomId: formData.roomId,
            roomName: formData.roomName,
            displayName
        })

    } else {
        // 清空房源选择
        formData.intentRoomObject = { id: '', name: '' }
        formData.roomId = ''
        formData.roomName = ''
        formData.intentRoom = ''
    }

    // 如果选择了具体房源（不是暂不确认房源），则设置为"否"且不可修改
    if (value?.id && !formData.unknownSource) {
        formData.refundable = '否'
    }
}

// 格式化房源显示名称：地块-楼栋-房源
const formatRoomDisplayName = (roomData: any): string => {
    if (!roomData) return ''

    const parts: string[] = []

    // 添加地块名称
    if (roomData.parcelName) {
        parts.push(roomData.parcelName)
    }

    // 添加楼栋名称
    if (roomData.buildingName) {
        parts.push(roomData.buildingName)
    }

    // 添加房源名称
    if (roomData.roomName) {
        parts.push(roomData.roomName)
    }

    // 用"-"连接各部分
    return parts.join('-')
}

// 监听意向房源选择变化
watch(() => formData.intentRoomObject, (newRoom) => {
    // 如果选择了具体房源（不是暂不确认房源），则设置为"否"且不可修改
    if (newRoom?.id && !formData.unknownSource) {
        formData.refundable = '否'
    }
}, { deep: true })

// 监听项目ID变化，清空意向房源
watch(() => formData.projectId, (newProjectId, oldProjectId) => {
    // 只有在项目ID真正发生变化时才清空（避免初始化时清空）
    if (oldProjectId && newProjectId !== oldProjectId) {
        // 清空意向房源相关字段
        formData.intentRoomObject = {
            id: '',
            name: '',
        }
        formData.roomId = ''
        formData.roomName = ''
        formData.intentRoom = ''

        // 清除意向房源的验证错误
        nextTick(() => {
            formRef.value?.clearValidate('intentRoomObject')
        })

        console.log('项目切换，已清空意向房源信息')
    }
})

// 暴露方法给父组件
defineExpose({
    open(projectId?: string, roomInfo?: any) {
        orderMode.value = 'add'
        isEdit.value = true
        drawerVisible.value = true
        initFormData(null, projectId, roomInfo)
    },
    view(record: any) {
        orderMode.value = 'view'
        isEdit.value = false
        drawerVisible.value = true
        initFormData(record, undefined, undefined)
        generateQRCode()
    },
    edit(record: any) {
        orderMode.value = 'edit'
        isEdit.value = true
        drawerVisible.value = true
        initFormData(record, undefined, undefined)
    },
    async save() {
        return handleSave()
    },
    async submit() {
        return handleSubmit()
    },
    print() {
        return handlePrint()
    },
    initFormData(record: any, projectId?: string, roomInfo?: any) {
        initFormData(record, projectId, roomInfo)
    }
})
</script>
<script lang="ts"> export default {
    name: 'addForm'
}
</script>
<style scoped lang="less">
.qrcode-container {
    text-align: center;
    margin-top: 16px;

    .qrcode-actions {
        margin-bottom: 24px;
    }

    .collect-code-content {
        .order-info {
            margin-bottom: 24px;

            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                padding: 0 16px;

                .label {
                    color: #86909c;
                    font-size: 14px;
                }

                .value {
                    color: #1d2129;
                    font-size: 14px;
                    font-weight: 500;

                    &.amount {
                        color: #f53f3f;
                        font-weight: 600;
                        font-size: 16px;
                    }
                }
            }
        }

        .qrcode-display {
            margin-bottom: 24px;
            display: flex;
            justify-content: center;
        }

        .payment-link {
            margin-top: 16px;
        }
    }

    // 保留原有的占位符样式作为备用
    .qrcode-placeholder {
        width: 200px;
        height: 200px;
        margin: 16px auto;
        border: 1px dashed #ccc;
        display: flex;
        align-items: center;
        justify-content: center;

        .placeholder-cross {
            width: 100%;
            height: 100%;
            position: relative;
            &::before,
            &::after {
                content: '';
                position: absolute;
                background-color: #ccc;
            }
            &::before {
                width: 1px;
                height: 100%;
                left: 50%;
                transform: translateX(-50%);
            }
            &::after {
                width: 100%;
                height: 1px;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }

    .qrcode-image {
        width: 200px;
        height: 200px;
        margin: 16px auto;
    }
}
</style>
