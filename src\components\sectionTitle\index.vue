<template>
    <div class="section-title" :class="{ 'large-title': large }">
        {{ title }} 
        <!-- solt -->
        <slot name="right"></slot>
    </div>
</template>

<script lang="ts" setup>
interface SectionTitleProps {
    title?: string;
    large?: boolean;
}
const props = withDefaults(defineProps<SectionTitleProps>(), {
    title: '',
    large: false,
});
</script>

<style lang="less" scoped>
.section-title {
    width: 100%;
    height: 40px;
    background: linear-gradient(270deg, #ffffff, #e9edf4);
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding-left: 18px;
    position: relative;
    justify-content: space-between;
    font-weight: bold;

    &::before {
        content: '';
        width: 4px;
        height: 16px;
        background: rgb(var(--primary-6));
        border-radius: 0px 3px 3px 0px;
        position: absolute;
        left: 5px;
        top: 50%;
        transform: translateY(-50%);
    }

    &.large-title {
        font-size: 15px;
        font-weight: 1000;
        height: 48px;
    }
}
</style>