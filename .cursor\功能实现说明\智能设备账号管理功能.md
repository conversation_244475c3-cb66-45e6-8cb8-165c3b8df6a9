# 智能设备账号管理功能实现说明

## 功能概述

本次实现了智能设备账号管理的功能优化：
1. 在 BatchBindDrawer.vue 中实现品牌账号的添加和删除功能
2. 去掉了品牌选择相关的代码，简化为纯管理功能
3. 解释了 AddAccountModal.vue 中监听品牌变化的作用

## 1. BatchBindDrawer.vue 品牌账号管理功能

### 功能描述
- 品牌列表通过 `getTTLockAccountList` 接口动态获取，显示已添加的账号
- 每个品牌卡片右上角显示删除图标，支持删除不需要的账号
- 点击删除图标会弹出二次确认对话框
- 确认删除后调用 `deleteTTLockAccount` 接口删除账号
- 去掉了品牌选择功能，专注于账号的添加和删除管理

### 主要改动

#### 1. 导入必要的组件和API
```javascript
import { Message, Modal } from '@arco-design/web-vue'
import { getTTLockAccountList, deleteTTLockAccount } from '@/api/smartLock'
```

#### 2. 修改品牌列表数据结构
```javascript
// 从静态数据改为动态数据结构，去掉选择相关的 value 字段
const brandList = ref<Array<{id: string, label: string, username: string}>>([])
```

#### 3. 更新模板结构
```html
<!-- 去掉选择功能，专注于显示和删除 -->
<div
    v-for="brand in brandList"
    :key="brand.id"
    class="brand-card"
>
    <span class="brand-label">{{ brand.label }}({{ brand.username }})</span>
    <icon-delete
        class="delete-icon"
        @click="handleDeleteAccount(brand.id, brand.label)"
    />
</div>
```

#### 4. 新增方法

##### fetchBrandList() - 获取品牌列表
- 调用 `getTTLockAccountList` 接口获取账号列表
- 将账号数据转换为品牌卡片显示格式
- 去掉了品牌选择逻辑，专注于数据展示

##### handleDeleteAccount() - 删除账号
- 显示二次确认对话框
- 确认后调用 `deleteTTLockAccount` 接口
- 删除成功后重新加载品牌列表

#### 5. 样式更新
- 品牌卡片使用 flex 布局
- 删除图标默认透明，hover 时显示
- 去掉了选中状态相关的样式
- 添加过渡动画效果

### 使用流程
1. 组件加载时自动获取项目下的所有账号
2. 将账号信息显示为品牌卡片（显示品牌名和用户名）
3. 用户可以点击"+绑定账号"添加新账号
4. 用户可以点击删除图标删除不需要的账号
5. 删除后自动刷新列表

## 2. AddAccountModal.vue 简化说明

### 功能变化
由于去掉了品牌选择功能，AddAccountModal.vue 也进行了相应的简化：

1. **删除了 selectedBrand 属性**：不再接收父组件传入的品牌选择信息
2. **删除了品牌映射**：去掉了 `brandMap` 对象和相关的监听逻辑
3. **简化了组件接口**：Props 接口只保留必要的 `visible` 和 `projectId` 属性

### 当前实现
```javascript
// Props 接口简化
interface Props {
    visible: boolean
    projectId?: string
}

// 表单数据中 brandType 默认为通通锁
const formData = reactive({
    brandType: 1, // 默认通通锁
    username: '',
    password: '',
    projectId: ''
})
```

### 设计说明
- 目前只支持通通锁品牌，brandType 固定为 1
- 如果将来需要支持多品牌，可以在表单中添加品牌选择器
- 组件接口保持简洁，专注于账号信息的录入

## 技术要点

### 1. API 调用规范
- 使用项目统一的 API 调用方式
- 错误处理遵循项目规范（不在 catch 中显示错误提示）
- 分页参数使用标准格式

### 2. 用户交互设计
- 删除操作使用 Modal.confirm 进行二次确认
- 删除图标使用 hover 效果，避免误操作
- 使用 @click.stop 阻止事件冒泡

### 3. 数据流管理
- 品牌列表数据与项目 ID 关联
- 添加账号成功后自动刷新品牌列表
- 删除账号后自动刷新品牌列表

### 4. 样式设计
- 遵循项目统一的设计规范
- 使用 CSS 过渡动画提升用户体验
- 响应式布局适配不同屏幕尺寸

## 完整改动总结

### BatchBindDrawer.vue 改动
✅ **删除的代码：**
- `selectedBrand` 变量及相关逻辑
- `handleBrandSelect` 方法
- 品牌变化监听器
- 模板中的选择状态绑定
- CSS 中的选中状态样式

✅ **保留的功能：**
- 动态获取账号列表
- 账号删除功能
- 添加账号功能

### AddAccountModal.vue 改动
✅ **删除的代码：**
- `selectedBrand` 属性
- `brandMap` 品牌映射对象
- 品牌变化监听器

✅ **简化的接口：**
```javascript
// 之前
interface Props {
    visible: boolean
    selectedBrand?: string
    projectId?: string
}

// 现在
interface Props {
    visible: boolean
    projectId?: string
}
```

## 后续扩展建议

1. **多品牌支持**：如需支持多品牌，可在 AddAccountModal 中添加品牌选择器
2. **批量操作**：可以添加批量删除账号的功能
3. **账号验证**：可以添加账号连接状态检测功能
4. **权限控制**：可以根据用户权限控制删除操作的可见性

## 3. 组件初始化方式优化

### 改动说明
将 `BatchBindDrawer.vue` 的初始化方式从监听项目变化改为通过 `show` 方法调用，参考了 `src/views/reduction/index.vue` 的实现模式。

### BatchBindDrawer.vue 改动

#### 删除的代码：
```javascript
// 删除 Props 接口
interface Props {
    projectId: string
    projectName: string
}

// 删除项目变化监听
watch(() => props.projectId, (newVal) => {
    if (newVal) {
        fetchBrandList()
        fetchRoomList()
        fetchDeviceList()
    }
}, { immediate: true })
```

#### 新增的代码：
```javascript
// 项目信息 - 通过 show 方法设置
const projectInfo = ref({
    projectId: '',
    projectName: ''
})

// show 方法 - 用于初始化组件
const show = (params: { projectId: string, projectName: string }) => {
    // 设置项目信息
    projectInfo.value = {
        projectId: params.projectId,
        projectName: params.projectName
    }

    // 初始化数据
    fetchBrandList()
    fetchRoomList()
    fetchDeviceList()
}

// 暴露方法给父组件
defineExpose({
    show
})
```

### index.vue 改动

#### 模板简化：
```html
<!-- 之前：需要传递项目信息 -->
<BatchBindDrawer
  v-if="batchBindVisible"
  ref="batchBindDrawerRef"
  :project-id="filterForm.projectId"
  :project-name="currentProject.projectName"
  @success="handleBatchBindSuccess"
  @cancel="handleBatchBindCancel"
/>

<!-- 现在：不需要传递项目信息 -->
<BatchBindDrawer
  v-if="batchBindVisible"
  ref="batchBindDrawerRef"
  @success="handleBatchBindSuccess"
  @cancel="handleBatchBindCancel"
/>
```

#### 方法更新：
```javascript
// 批量绑定设备
const handleBindDevice = async () => {
  if (!filterForm.projectId) {
    Message.warning('请先选择项目')
    return
  }

  if (activeTab.value !== 'door-lock') {
    Message.warning('批量绑定功能仅支持智能门锁')
    return
  }

  batchBindVisible.value = true
  await nextTick()
  // 调用子组件的 show 方法传递项目信息
  batchBindDrawerRef.value?.show({
    projectId: filterForm.projectId,
    projectName: currentProject.value.projectName
  })
}
```

## 最终效果

现在的组件初始化流程更加清晰：
1. 📋 用户点击"批量绑定设备"按钮
2. 🔍 检查项目选择和功能支持
3. 📂 显示 BatchBindDrawer 组件
4. 🚀 调用 show 方法传递项目信息并初始化数据
5. ➕ 支持添加新账号
6. 🗑️ 支持删除现有账号（带二次确认）
7. 🔄 操作后自动刷新列表

这种方式的优势：
- ✅ 组件职责更清晰，不依赖 Props 监听
- ✅ 初始化时机更可控，避免不必要的数据加载
- ✅ 参考了项目中成熟的实现模式
- ✅ 代码结构更简洁，易于维护
