# Node.js 内存分配失败解决方案

## 🚨 问题描述

您遇到的错误信息显示了典型的 Node.js 内存分配失败问题：

```
allocation failure;
Scavenge 29.4 (38.7) -> 24.6 (40.4) MB
Mark-Compact 53.5 (116.7) -> 39.3 (116.9) MB
```

这表明：
- **Scavenge**: 新生代垃圾回收频繁触发
- **Mark-Compact**: 老生代内存压力过大
- **allocation failure**: 内存分配失败

## 🎯 立即解决方案

### 1. **使用优化的构建命令** (推荐)

```bash
# Windows 系统
npm run build:win:fast

# 或者双击批处理文件
build-windows-fast.bat
```

### 2. **临时修复**

如果上面命令不可用，使用以下临时方案：

```bash
# Windows CMD
set NODE_OPTIONS=--max-old-space-size=8192 --expose-gc --optimize-for-size --memory-reducer
npm run build

# Windows PowerShell  
$env:NODE_OPTIONS="--max-old-space-size=8192 --expose-gc --optimize-for-size --memory-reducer"
npm run build

# Linux/macOS
export NODE_OPTIONS="--max-old-space-size=8192 --expose-gc --optimize-for-size --memory-reducer"
npm run build
```

## 🔧 完整解决方案

### 第一步：运行诊断工具

```bash
npm run diagnose:memory
```

这会分析您的系统并提供个性化建议。

### 第二步：选择合适的构建模式

根据您的系统配置选择：

| 模式 | 内存要求 | 适用场景 | 命令 |
|------|---------|---------|------|
| 标准 | 8GB+ | 日常开发 | `npm run build:win` |
| 快速 | 12GB+ | 测试部署 | `npm run build:win:fast` |
| 超级 | 16GB+ | 紧急发布 | `npm run build:win:turbo` |

### 第三步：Windows 系统特定优化

1. **设置环境优化**
   ```bash
   npm run setup:win
   ```

2. **添加 Windows Defender 排除**
   - 打开 Windows 安全中心
   - 转到病毒和威胁防护 > 管理设置
   - 添加排除项：
     ```
     C:\path\to\your\project
     C:\path\to\your\project\node_modules
     %USERPROFILE%\.npm
     ```

3. **使用批处理文件**
   - 双击 `build-windows-fast.bat` 进行快速构建
   - 双击 `build-windows.bat` 进行完整构建

## ⚙️ Node.js 参数详解

### 内存相关参数

| 参数 | 作用 | 推荐值 |
|------|------|--------|
| `--max-old-space-size` | 老生代最大内存 | 8192-16384MB |
| `--max-semi-space-size` | 新生代半空间大小 | 1024-2048MB |
| `--expose-gc` | 启用手动垃圾回收 | 必需 |

### 优化参数

| 参数 | 作用 | 效果 |
|------|------|------|
| `--optimize-for-size` | 优化内存使用 | 减少内存占用 |
| `--memory-reducer` | 内存缩减器 | 自动释放内存 |
| `--gc-interval` | 垃圾回收间隔 | 定期清理 |
| `--incremental-marking` | 增量标记 | 减少GC停顿 |
| `--concurrent-marking` | 并发标记 | 提高GC效率 |

## 📊 内存监控

### 实时监控

项目已自动生成 `memory-monitor.js`，可以监控构建过程中的内存使用：

```javascript
// 内存监控脚本会显示：
📊 内存: 堆 1024/2048MB, 外部 256MB
🗑️  触发垃圾回收...
```

### 手动检查

```bash
# 检查当前内存使用
node -e "console.log(process.memoryUsage())"

# 检查系统内存
node -e "console.log(require('os').totalmem()/1024/1024/1024 + 'GB')"
```

## 🛠️ 故障排除

### 常见错误及解决方案

#### 1. "JavaScript heap out of memory"

**解决方案**：
```bash
# 增加内存限制
set NODE_OPTIONS=--max-old-space-size=16384
npm run build
```

#### 2. "allocation failure" 频繁出现

**解决方案**：
```bash
# 启用内存优化
set NODE_OPTIONS=--max-old-space-size=8192 --optimize-for-size --memory-reducer
npm run build
```

#### 3. 构建过程中系统卡死

**解决方案**：
```bash
# 降低并行度，减少内存使用
set NODE_OPTIONS=--max-old-space-size=6144 --max-semi-space-size=512
npm run build
```

### Windows 特定问题

#### 1. PowerShell 执行策略限制

```powershell
# 以管理员身份运行 PowerShell
Set-ExecutionPolicy RemoteSigned
```

#### 2. 虚拟内存设置

1. 右键"此电脑" → 属性 → 高级系统设置
2. 性能 → 设置 → 高级 → 虚拟内存
3. 设置为系统内存的 1.5-2 倍

#### 3. Windows 内存压缩

```cmd
# 禁用内存压缩（可选）
Disable-MMAgent -MemoryCompression
```

## 🚀 性能优化建议

### 系统级优化

1. **硬件升级**
   - 内存：升级到 16GB+ DDR4
   - 存储：使用 NVMe SSD
   - CPU：现代多核处理器

2. **软件优化**
   - 关闭不必要的应用
   - 清理系统垃圾文件
   - 更新 Node.js 到最新 LTS 版本

### 项目级优化

1. **依赖管理**
   ```bash
   # 清理依赖
   rm -rf node_modules package-lock.json
   npm install

   # 分析依赖大小
   npm install -g webpack-bundle-analyzer
   ```

2. **构建优化**
   ```bash
   # 使用生产模式
   NODE_ENV=production npm run build

   # 启用缓存
   npm run build:win:fast
   ```

## 📈 效果预期

使用优化方案后，您应该看到：

- ✅ **内存分配失败消失**
- ✅ **垃圾回收频率降低**
- ✅ **构建速度提升 30-50%**
- ✅ **内存使用更加稳定**

## 🆘 紧急方案

如果所有方案都失败，使用最小内存配置：

```bash
# 最小配置
set NODE_OPTIONS=--max-old-space-size=4096 --expose-gc
npm run build:dev

# 或者分步构建
npm run type:check  # 先检查类型
npm run build:notsc  # 跳过类型检查构建
```

## 📞 获取帮助

1. **运行诊断**: `npm run diagnose:memory`
2. **查看日志**: 保存构建输出到文件分析
3. **检查系统**: 使用任务管理器监控资源使用

---

**记住**：这些优化专门针对您遇到的内存分配失败问题设计，应该能有效解决构建过程中的内存问题！ 