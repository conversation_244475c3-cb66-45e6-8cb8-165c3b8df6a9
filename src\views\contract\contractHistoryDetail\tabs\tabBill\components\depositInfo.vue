<template>
    <section>
        <sectionTitle title="保证金信息"></sectionTitle>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table :data="tableData" :pagination="false" :scroll="{ x: 1000 }" :bordered="{ cell: true }">
                <template #columns>
                    <a-table-column title="租期" data-index="startDate" :width="100" align="center">
                        <template #cell="{ record }">
                            {{ dayjs(record.startDate).format('YYYY-MM-DD') }} 至 {{
                                dayjs(record.endDate).format('YYYY-MM-DD') }}
                        </template>
                    </a-table-column>
                    <a-table-column title="商户" data-index="customerName" :width="80" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="费项" data-index="subjectName" :width="80" align="center" :ellipsis="true"
                        :tooltip="true">
                        <template #cell="{ record }">
                            履约保证金
                        </template>
                    </a-table-column>
                    <a-table-column title="应收日期" data-index="receivableDate" :width="80" align="center">
                        <template #cell="{ record }">
                            {{ record.receivableDate }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单应收金额（元）" data-index="actualReceivable" :width="80" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单已收金额（元）" data-index="receivedAmount" :width="100" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.receivedAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单剩余应收金额（元）" data-index="remainingAmount" :width="100" align="right">
                        <template #cell="{ record }">
                            <span v-if="contractData.contractMode === 0">{{ formatAmount(record.remainingAmount) }}</span>
                            <span v-else>{{ formatAmount(record.actualReceivable - record.receivedAmount) }}</span>
                        </template>
                    </a-table-column>
                </template>
            </a-table>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import { ContractCostDTO, ContractAddDTO, ContractVo } from '@/api/contract';
import dayjs from 'dayjs';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.historyVersionContractDetailCosts as ContractVo)
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)

const tableData = ref<any[]>([])

/**
 * 监听应收计划计算结果
 * 1. 当合同为标准合同时，需要合并costs中所有costType为保证金且period相同的数据
 *    - rentDate取保证金同一期中最早的日期(startDate)和最晚的日期(endDate)，格式为YYYY-MM-DD 至 YYYY-MM-DD
 *    - customerName取保证金同一期第一条数据的customerName，因为都相同
 *    - subjectName取保证金同一期第一条数据的subjectName，因为都相同
 *    - receivableDate取保证金同一期第一条数据的receivableDate，因为都相同
 *    - actualReceivable取保证金同一期所有数据的actualReceivable之和
 *    - receivedAmount取保证金同一期所有数据的receivedAmount之和
 *    - remainingAmount为actualReceivable - receivedAmount
 * 2. 当合同为非标准合同时，直接使用costs中的数据
 *    - rentDate 格式为 startDate 至 endDate
 *    - remainingAmount为actualReceivable - receivedAmount
 */
watch(contractCosts, (newVal) => {
    if (newVal.costs && newVal.costs.length > 0) {
        if (contractData.value.contractMode === 0) {
            // 标准合同
            const depositCosts = newVal.costs.filter(item => item.costType === 1)
            if (depositCosts.length > 0) {
                const depositCostsGroup = depositCosts.reduce((acc, item) => {
                    if (!acc[item.period]) {
                        acc[item.period] = []
                    }
                    acc[item.period].push(item)
                    return acc
                }, {} as Record<number, ContractCostDTO[]>)
                tableData.value = Object.values(depositCostsGroup).map(item => {
                    const startDate = item[0].startDate
                    const endDate = item[item.length - 1].endDate
                    const customerName = item[0].customerName ?? ''
                    const subjectName = item[0].subjectName ?? ''
                    const receivableDate = item[0].receivableDate ? dayjs(item[0].receivableDate).format('YYYY-MM-DD') : ''
                    const actualReceivable = item.reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
                    const receivedAmount = item.reduce((sum, item) => sum + (item.receivedAmount || 0), 0)
                    const remainingAmount = actualReceivable - receivedAmount
                    const period = item[0].period
                    return {
                        startDate,
                        endDate,
                        customerName,
                        subjectName,
                        receivableDate,
                        actualReceivable,
                        receivedAmount,
                        remainingAmount,
                        period
                    }
                })
            }
        } else {
            // 非标准合同
            const depositCosts = contractCosts.value.costs?.filter(item => item.costType === 1)
            if (depositCosts && depositCosts.length > 0) {
                tableData.value = depositCosts
            }
        }
    } else {
        tableData.value = []
    }
}, { deep: true, immediate: true })
</script>

<style lang="less" scoped>
.detail-text {
    color: #333;
}
</style>