<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form label-align="right" :model="filterForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>
                        <a-row :gutter="16">
                            <a-col :span="6">
                                <a-form-item field="projectId" label="项目">
                                    <ProjectTreeSelect
                                        v-model="filterForm.projectId"
                                        :min-level="4"
                                        @change="handleProjectChange"
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="typeList" label="类型">
                                    <a-select :max-tag-count="2" v-model="filterForm.typeList" placeholder="请选择类型" allow-clear multiple>
                                        <a-option :value="1">减免</a-option>
                                        <a-option :value="2">缓缴</a-option>
                                        <a-option :value="3">分期</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="statusList" label="单据状态">
                                    <a-select :max-tag-count="2" v-model="filterForm.statusList" placeholder="请选择单据状态" allow-clear multiple>
                                        <a-option :value="0">草稿</a-option>
                                        <a-option :value="1">待生效</a-option>
                                        <a-option :value="2">生效</a-option>
                                        <a-option :value="4">作废</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="approveStatusList" label="审批状态">
                                    <a-select :max-tag-count="2" v-model="filterForm.approveStatusList" placeholder="请选择审批状态" allow-clear multiple>
                                        <a-option :value="0">草稿</a-option>
                                        <a-option :value="1">审批中</a-option>
                                        <a-option :value="2">已通过</a-option>
                                        <a-option :value="3">已驳回</a-option>
                                        <a-option :value="4">作废</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="16">
                            <a-col :span="6">
                                <a-form-item field="contractNo" label="合同号">
                                    <a-input v-model="filterForm.contractNo" placeholder="请输入合同号" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="roomName" label="租赁单元">
                                    <a-input v-model="filterForm.roomName" placeholder="请输入租赁单元" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item field="customerName" label="承租方">
                                    <a-input v-model="filterForm.customerName" placeholder="请输入承租方" allow-clear />
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="handleSearch">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="handleReset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin-top: 0" />

            <!-- 操作按钮 -->
            <div class="action-bar">
                <a-space>
                    <a-button type="primary" @click="handleAdd">
                        <template #icon><icon-plus /></template>
                        新增
                    </a-button>
                    <a-button @click="handleExport">
                        <template #icon><icon-download /></template>
                        导出
                    </a-button>
                </a-space>
            </div>

            <!-- 表格区域 -->
            <a-table
                :columns="columns"
                :data="tableData"
                :pagination="pagination"
                :bordered="{ cell: true }"
                :scroll="{ x: 1 }"
                :stripe="true"
                :loading="loading"
                row-key="id"
                @page-change="onPageChange"
                @page-size-change="onPageSizeChange"
            >
                <template #type="{ record }">
                    {{ getTypeText(record.type) }}
                </template>
                <template #status="{ record }">
                     {{ getStatusText(record.status) }}
                </template>
                <template #approveStatus="{ record }">
                     {{ getApproveStatusText(record.approveStatus) }}
                </template>
                <template #contractPeriod="{ record }">
                    <span>{{ record.startDate && record.endDate ? `${record.startDate} 至 ${record.endDate}` : record.contractPeriod }}</span>
                </template>
                <template #action="{ record }">
                    <a-space :key="`action-${record.id}-${record.status}`">
                        <template v-if="record.status === 0">
                            <a-button type="text" size="small" @click="handleEdit(record)">编辑</a-button>
                            <a-button type="text" size="small" status="danger" @click="handleDelete(record)">删除</a-button>
                            <a-button type="text" size="small" @click="handleApprove(record)">审批</a-button>
                        </template>
                        <template v-else>
                            <a-button type="text" size="small" @click="handleView(record)">查看</a-button>
                            <a-button type="text" size="small" @click="handleViewWorkflow(record)">查看工作流</a-button>
                        </template>
                    </a-space>
                </template>
            </a-table>
        </a-card>

        <!-- 减免缓表单组件 -->
        <ReductionForm v-if="showReductionForm" ref="reductionFormRef" @success="handleFormSuccess" @cancel="handleFormCancel" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconSearch, IconRefresh, IconPlus, IconDownload } from '@arco-design/web-vue/es/icon'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import ReductionForm from './components/ReductionForm.vue'
import {
    getReductionList,
    deleteReduction,
    exportReductionList,
    type ReductionQueryDTO
} from '@/api/reduction'
import { exportExcel } from '@/utils/exportUtil'

// 筛选表单
const filterForm = reactive<ReductionQueryDTO>({
    projectId: '',
    typeList: undefined,
    statusList: undefined,
    approveStatusList: undefined,
    contractNo: '',
    roomName: '',
    customerName: '',
    pageNum: 1,
    pageSize: 10
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页配置
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100]
})

// 组件引用和显示控制
const reductionFormRef = ref()
const showReductionForm = ref(false)

// 项目变更控制
const isInit = ref(false)

// 表格列配置
const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 70,
        align: 'center'
    },
    {
        title: '项目',
        dataIndex: 'projectName',
        width: 200,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '类型',
        dataIndex: 'type',
        slotName: 'type',
        width: 80,
        align: 'center'
    },
    {
        title: '合同号',
        dataIndex: 'contractNo',
        width: 240,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '租赁单元',
        dataIndex: 'roomName',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '承租方',
        dataIndex: 'customerName',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '合同租期',
        dataIndex: 'contractPeriod',
        slotName: 'contractPeriod',
        width: 220,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '单据状态',
        dataIndex: 'status',
        slotName: 'status',
        width: 100,
        align: 'center'
    },
    {
        title: '审批状态',
        dataIndex: 'approveStatus',
        slotName: 'approveStatus',
        width: 100,
        align: 'center'
    },
    {
        title: '审批通过时间',
        dataIndex: 'approveTime',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '创建人',
        dataIndex: 'createByName',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '创建日期',
        dataIndex: 'createTime',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '操作',
        slotName: 'action',
        width: 260,
        align: 'center',
        fixed: 'right'
    }
]

// 类型相关方法
const getTypeText = (type: number) => {
    const typeMap: Record<number, string> = {
        1: '减免',
        2: '缓缴',
        3: '分期'
    }
    return typeMap[type] || '-'
}

const getTypeColor = (type: number) => {
    const colorMap: Record<number, string> = {
        1: 'blue',
        2: 'orange',
        3: 'green'
    }
    return colorMap[type] || 'gray'
}

// 状态相关方法
const getStatusText = (status: number) => {
    const statusMap: Record<number, string> = {
        0: '草稿',
        1: '待生效',
        2: '生效',
        4: '作废'
    }
    return statusMap[status] || '-'
}

const getStatusColor = (status: number) => {
    const colorMap: Record<number, string> = {
        0: 'gray',
        1: 'orange',
        2: 'green',
        4: 'red'
    }
    return colorMap[status] || 'gray'
}

// 审批状态相关方法
const getApproveStatusText = (approveStatus: number) => {
    const statusMap: Record<number, string> = {
        0: '草稿',
        1: '审批中',
        2: '已通过',
        3: '已驳回',
        4: '作废'
    }
    return statusMap[approveStatus] || '-'
}

const getApproveStatusColor = (approveStatus: number) => {
    const colorMap: Record<number, string> = {
        0: 'gray',
        1: 'blue',
        2: 'green',
        3: 'red',
        4: 'red'
    }
    return colorMap[approveStatus] || 'gray'
}

// 查询数据
const loadData = async () => {
    try {
        loading.value = true
        // 直接使用正确的字段名，无需转换
        const queryParams = {
            ...filterForm,
            pageNum: pagination.current,
            pageSize: pagination.pageSize
        }

        const response = await getReductionList(queryParams)
        if (response.rows) {
            tableData.value = response.rows.map((item: any, index: number) => ({
                ...item,
                index: (pagination.current - 1) * pagination.pageSize + index + 1
            }))
            pagination.total = response.total
        }
    } catch (error) {
        console.error('查询减免缓列表失败:', error)
    } finally {
        loading.value = false
    }
}

// 搜索
const handleSearch = () => {
    pagination.current = 1
    loadData()
}

// 重置
const handleReset = () => {
    Object.assign(filterForm, {
        projectId: '',
        typeList: undefined,
        statusList: undefined,
        approveStatusList: undefined,
        contractNo: '',
        roomName: '',
        customerName: '',
        pageNum: 1,
        pageSize: 10
    })
    pagination.current = 1
    loadData()
}

// 存储当前选中的项目信息
const currentProject = ref<any>({
    projectId: '',
    projectName: ''
})

// 项目变更
const handleProjectChange = (value: string | number, selectedOrg: any) => {
    console.log('项目变化，检查是否有项目ID再调用列表', value, selectedOrg)

    // 存储项目信息
    currentProject.value = {
        projectId: value,
        projectName: selectedOrg?.name || ''
    }

    // 只有在有项目ID时才自动触发搜索
    if (value && !isInit.value) {
        isInit.value = true
        pagination.current = 1
        loadData()
    }
}

// 分页变更
const onPageChange = (page: number) => {
    pagination.current = page
    loadData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    loadData()
}

// 新增
const handleAdd = async () => {
    // 检查是否已选择项目
    if (!filterForm.projectId) {
        Message.warning('请先选择项目')
        return
    }

    showReductionForm.value = true
    await nextTick()
    // 传递项目ID和项目名称给表单组件
    reductionFormRef.value?.show({
        projectId: filterForm.projectId,
        projectName: currentProject.value.projectName
    }, 'add')
}

// 编辑
const handleEdit = async (record: any) => {
    showReductionForm.value = true
    await nextTick()
    // 传递记录信息，包含类型，用于调用对应的详情接口
    reductionFormRef.value?.show(record, 'edit')
}

// 查看
const handleView = async (record: any) => {
    showReductionForm.value = true
    await nextTick()
    // 传递记录信息，包含类型，用于调用对应的详情接口
    reductionFormRef.value?.show(record, 'view')
}

// 删除
const handleDelete = async (record: any) => {
    Modal.confirm({
        title: '确认删除',
        content: '确定要删除这条记录吗？删除后不可恢复。',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
            try {
                await deleteReduction(record.id)
                Message.success('删除成功')
                loadData()
            } catch (error) {
                console.error('删除失败:', error)
            }
        }
    })
}

// 审批
const handleApprove = (record: any) => {
    // TODO: 实现审批功能
    Message.info('审批功能待实现')
}

// 查看工作流
const handleViewWorkflow = (record: any) => {
    // TODO: 实现查看工作流功能
    Message.info('查看工作流功能待实现')
}

// 导出
const handleExport = async () => {
    if (!filterForm.projectId) {
        Message.warning('请先选择项目')
        return
    }

    try {
        // 构建导出查询参数，使用当前筛选条件
        const exportParams = {
            ...filterForm,
            pageNum: 1,
            pageSize: 10000 // 导出时使用大页码
        }

        await exportExcel(exportReductionList, exportParams, '减免缓列表')
        Message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
        Message.error('导出失败')
    }
}

// 表单成功回调
const handleFormSuccess = () => {
    showReductionForm.value = false
    loadData()
}

// 表单取消回调
const handleFormCancel = () => {
    showReductionForm.value = false
}


</script>

<style scoped lang="less">
.container {
    padding: 0 16px 16px 16px;
}

.general-card {
    border-radius: 4px;

    :deep(.arco-card-body) {
        padding: 16px;
    }
}

.action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
}
</style>
