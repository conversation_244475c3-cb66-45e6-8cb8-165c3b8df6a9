<template>
    <section>
        <sectionTitle title="补充条款" />
        <div class="content">
            <a-grid :cols="1" :col-gap="16" :row-gap="0">
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">补充条款内容</label>
                        <div class="detail-text">{{ contractData.otherInfo || '' }}</div>
                    </div>
                </a-grid-item>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { useContractStore } from '@/store/modules/contract'
import { ContractAddDTO } from '@/api/contract';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.form-item {
    margin-bottom: 16px;
    
    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>