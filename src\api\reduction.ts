import http from './index';

/**
 * 减免缓类型枚举
 */
export enum ReductionType {
    /** 减免 */
    REDUCTION = 1,
    /** 缓交 */
    POSTPONED = 2,
    /** 分期 */
    SPLIT = 3,
}

/**
 * 状态枚举
 */
export enum ReductionStatus {
    /** 草稿 */
    DRAFT = 0,
    /** 待生效 */
    PENDING = 1,
    /** 生效 */
    EFFECTIVE = 2,
    /** 作废 */
    INVALID = 4,
}

/**
 * 审批状态枚举
 */
export enum ApproveStatus {
    /** 草稿 */
    DRAFT = 0,
    /** 审批中 */
    APPROVING = 1,
    /** 已通过 */
    APPROVED = 2,
    /** 已驳回 */
    REJECTED = 3,
    /** 作废 */
    INVALID = 4,
}

/**
 * 账单类型枚举
 */
export enum CostType {
    /** 保证金 */
    DEPOSIT = 1,
    /** 租金 */
    RENT = 2,
    /** 其他费用 */
    OTHER = 3,
}

/**
 * 减免缓调整明细DTO
 */
export interface ReductionPostponedAdjustAddDTO {
    /** 主键ID */
    id?: string;
    /** 减免缓id */
    reductionId?: string;
    /** 合同ID */
    contractId?: string;
    /** 账单ID */
    costId?: string;
    /** 账单类型: 1-保证金,2-租金,3-其他费用 */
    costType?: number;
    /** 账单期数 */
    period?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 应收日期 */
    receivableDate?: string;
    /** 账单总额 */
    totalAmount?: number;
    /** 优惠金额 */
    discountAmount?: number;
    /** 实际应收金额 */
    actualReceivable?: number;
    /** 已收金额 */
    receivedAmount?: number;
    /** 实际未收金额 */
    pendingAmount?: number;
    /** 减免金额 */
    reductionAmount?: number;
    /** 缓交日期 */
    delayTime?: string;
    /** 调整后实际应收 */
    currActualReceivable?: number;
    /** 是否删除: 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 减免缓新增/编辑DTO
 */
export interface ReductionPostponedAddDTO {
    /** 主键ID */
    id?: string;
    /** 类型（1减免 2缓交 3分期） */
    type?: number;
    /** 立项定价申请编号 */
    applicationCode?: string;
    /** 立项定价申请名称 */
    applicationName?: string;
    /** 项目ID */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 客户ID */
    customerId?: string;
    /** 客户名称 */
    customerName?: string;
    /** 合同ID */
    contractId?: string;
    /** 合同编号 */
    contractNo?: string;
    /** 合同统一ID */
    unionId?: string;
    /** 房间名称，多个按照逗号分开 */
    roomName?: string;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 合同用途 */
    contractPurpose?: number;
    /** 申请原因 */
    reason?: string;
    /** 申请说明 */
    remark?: string;
    /** 状态(0草稿 1待生效 2生效 4作废) */
    status?: number;
    /** 审批状态(0草稿 1审批中 2已通过 3已驳回 4作废) */
    approveStatus?: number;
    /** 审批通过时间 */
    approveTime?: string;
    /** 调整列表 */
    adjustList?: ReductionPostponedAdjustAddDTO[];
    /** 附件 */
    attachments?: string | null;
    /** 是否删除: 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 减免缓查询参数
 */
export interface ReductionQueryDTO {
    /** 项目ID */
    projectId?: string;
    /** 类型（1减免 2缓交 3分期） */
    typeList?: number[];
    /** 状态(0草稿 1待生效 2生效 4作废) */
    statusList?: number[];
    /** 审批状态(0草稿 1审批中 2已通过 3已驳回 4作废) */
    approveStatusList?: number[];
    /** 合同编号 */
    contractNo?: string;
    /** 租赁单元 */
    roomName?: string;
    /** 客户名称 */
    customerName?: string;
    /** 页码 */
    pageNum: number;
    /** 页大小 */
    pageSize: number;
    /** 合同统一ID */
    unionId?: string;
}

/**
 * 减免缓导出参数
 */
export interface ReductionExportDTO {
    /** 项目ID */
    projectId?: string;
    /** 类型（1减免 2缓交 3分期） */
    typeList?: number[];
    /** 状态(0草稿 1待生效 2生效 4作废) */
    statusList?: number[];
    /** 审批状态(0草稿 1审批中 2已通过 3已驳回 4作废) */
    approveStatusList?: number[];
    /** 合同编号 */
    contractNo?: string;
    /** 租赁单元 */
    roomName?: string;
    /** 客户名称 */
    customerName?: string;
    /** 页码 */
    pageNum: number;
    /** 页大小 */
    pageSize: number;
}

/**
 * 合同费用减免查询DTO
 */
export interface ContractCostReductionDTO {
    /** 项目id */
    projectId?: string;
    /** 合同号 */
    contractNo?: string;
    /** 承租方名称 */
    customerName?: string;
    /** 房间id */
    roomId?: string;
    /** 类型（1减免 2缓交 3分期） */
    type?: number;
    /** 合同id */
    contractId?: string;
    /** 账单类型,1-保证金,2-租金,3-其他费用 */
    costType?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
}

/**
 * 获取承租方房间查询DTO
 */
export interface CustomerRoomQueryDTO {
    /** 承租方ID */
    customerId?: string;
    /** 承租人姓名 */
    customerName?: string;
    /** 项目ID */
    projectId?: string;
}

/**
 * 承租方房间信息VO
 */
export interface CustomerRoomVo {
    /** 房间ID */
    id?: string;
    /** 房间名称 */
    roomName?: string;
    /** 项目ID */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 地块ID */
    parcelId?: string;
    /** 地块名称 */
    parcelName?: string;
    /** 楼栋ID */
    buildingId?: string;
    /** 楼栋名称 */
    buildingName?: string;
    /** 楼层ID */
    floorId?: string;
    /** 楼层名称 */
    floorName?: string;
    /** 物业类型 */
    propertyType?: string;
    /** 计租面积 */
    rentArea?: number;
    /** 计租面积类型（1建筑面积 2套内面积） */
    rentAreaType?: number;
}

/**
 * 账单信息VO
 */
export interface CostVo {
    /** 主键ID */
    id?: string;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 收费类别: 0-定单, 1-合同 */
    chargeType?: number;
    /** 业务id, 定单类别对应定单id, 合同类别对应合同id */
    bizId?: string;
    /** 退款单id */
    refundId?: string;
    /** 退款关联的收费单Cost表id */
    refundCostId?: string;
    /** 业务单号 */
    bizNo?: string;
    /** 承租人id */
    customerId?: string;
    /** 客户类型:1-个人1,2-企业 */
    customerType?: number;
    /** 承租人名称 */
    customerName?: string;
    /** 客户手机号 */
    customerPhone?: string;
    /** 账单类型: 1-保证金,2-租金,3-其他费用 */
    costType?: number;
    /** 账单类型: 1-保证金,2-租金,3-其他费用 */
    costTypeName?: string;
    /** 账单期数 */
    period?: number;
    /** 收款用途id */
    subjectId?: string;
    /** 收款用途名称 */
    subjectName?: string;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 账单周期 */
    rentPeriod?: string;
    /** 应收日期 */
    receivableDate?: string;
    /** 账单状态: 0-待收、1-待付、2-已收、3-已付 */
    status?: number;
    /** 是否可收/付 0-否,1-是 */
    canPay?: boolean;
    /** 是否是营收抽点账单 0-否,1-是 */
    isRevenueBill?: boolean;
    /** 营收抽点金额是否已生成 0-否,1-是 */
    isRevenueGenerated?: boolean;
    /** 是否来自合同生成 0-否,1-是 */
    isFromContract?: boolean;
    /** 账单总额 */
    totalAmount?: number;
    /** 优惠金额 */
    discountAmount?: number;
    /** 实际应收金额 */
    actualReceivable?: number;
    /** 已收金额 */
    receivedAmount?: number;
    /** 待收/付金额 */
    pendingAmount?: number;
    /** 结转金额 */
    carryoverAmount?: number;
    /** 确认状态: 0-待确认, 1-已确认 */
    confirmStatus?: number;
    /** 创建人姓名 */
    createByName?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房 */
    contractType?: number;
    /** 租赁资源 */
    roomName?: string;
    /** 合同一级状态 */
    contractStatus?: number;
    /** 合同二级状态 */
    contractStatusTwo?: number;
}

const systemUrl = '/business-rent-admin';
const reductionUrl = `${systemUrl}/reduction`;

// ==================== 减免缓通用接口 ====================

/**
 * 查询减免缓列表
 */
export function getReductionList(data: ReductionQueryDTO) {
    return http.post(`${reductionUrl}/list`, data);
}

/**
 * 删除减免
 */
export function deleteReduction(id: string) {
    return http.delete(`${reductionUrl}/delete`, { id });
}

/**
 * 减免详情
 */
export function getReductionDetail(id: string) {
    return http.get(`${reductionUrl}/detail`, { id });
}

/**
 * 导出减免缓列表
 */
export function exportReductionList(params: ReductionExportDTO) {
    return http.post(`${reductionUrl}/export`, params, {
        responseType: 'blob'
    });
}

// ==================== 减免相关接口 ====================

/**
 * 新增减免
 */
export function addReductionOnly(data: ReductionPostponedAddDTO) {
    return http.post(`${reductionUrl}/add`, data);
}

/**
 * 编辑减免
 */
export function editReduction(data: ReductionPostponedAddDTO) {
    return http.post(`${reductionUrl}/edit`, data);
}

// ==================== 缓交相关接口 ====================

/**
 * 新增缓交
 */
export function addPostponed(data: ReductionPostponedAddDTO) {
    return http.post(`${reductionUrl}/postponed/add`, data);
}

/**
 * 编辑缓交
 */
export function editPostponed(data: ReductionPostponedAddDTO) {
    return http.post(`${reductionUrl}/postponed/edit`, data);
}

// ==================== 分期相关接口 ====================

/**
 * 新增分期
 */
export function addSplit(data: ReductionPostponedAddDTO) {
    return http.post(`${reductionUrl}/split/add`, data);
}

/**
 * 编辑分期
 */
export function editSplit(data: ReductionPostponedAddDTO) {
    return http.post(`${reductionUrl}/split/edit`, data);
}

/**
 * 分期详情
 */
export function getSplitDetail(id: string) {
    return http.get(`${reductionUrl}/split/detail`, { id });
}

// ==================== 合同相关接口 ====================

/**
 * 获取符合条件的合同
 */
export function getContractList(data: ContractCostReductionDTO) {
    return http.post(`${reductionUrl}/contract/list`, data);
}

/**
 * 获取合同未收齐账单
 * 根据合同ID查询未收齐账单信息
 */
export function getUnpaidCost(data: ContractCostReductionDTO) {
    return http.post<CostVo[]>(`${reductionUrl}/contract/unpaidCost`, data);
}

/**
 * 获取承租方的房间
 * 根据承租方ID查询其租赁的房间信息
 */
export function getCustomerRooms(data: CustomerRoomQueryDTO) {
    return http.post<CustomerRoomVo[]>(`${reductionUrl}/contract/room`, data);
}
