import http from './index';
import type { RouteRecordNormalized } from 'vue-router';
import { UserState } from '@/store/modules/user/types';
const systemUrl = '/business-rent-admin'


export function getOrgTree(params: any = {}) {
    return http.post(`${systemUrl}/org/tree`, params);
}
export function getOrgList() {
    return http.get(`${systemUrl}/org/list`);
}

export function getOrgUserList(data: any) {
    return http.post(`${systemUrl}/org/user/list`, data);
}

export function getOrgDetail(data: any) {
    return http.get(`${systemUrl}/org/${data.orgId}`, data);
}
