<template>
    <section>
        <sectionTitle title="开票信息" />
        <div class="content">
            <a-grid :cols="1" :col-gap="16" :row-gap="0">
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">名称</label>
                        <div class="detail-text">{{ contractData.customer.invoiceTitle || '' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">税号</label>
                        <div class="detail-text">{{ contractData.customer.invoiceTaxNumber || '' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">单位地址</label>
                        <div class="detail-text">{{ contractData.customer.invoiceAddress || '' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">电话号码</label>
                        <div class="detail-text">{{ contractData.customer.invoicePhone || '' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">开户银行</label>
                        <div class="detail-text">{{ contractData.customer.invoiceBankName || '' }}</div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">银行账户</label>
                        <div class="detail-text">{{ contractData.customer.invoiceAccountNumber || '' }}</div>
                    </div>
                </a-grid-item>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { useContractStore } from '@/store/modules/contract'
import { ContractAddDTO } from '@/api/contract';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0 0;
    }
}

.form-item {
    margin-bottom: 16px;
    
    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>