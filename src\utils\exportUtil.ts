/**
 * 导出excel
 * @param {*} requestApi 接口方法
 * @param {*} params 接口参数
 * @param {*} fileName 导出文件名
 */
export const exportExcel = (requestApi: any, params: any, fileName: string) => {
    return new Promise((resolve, reject) => {
        requestApi(params)
            .then((res: Blob) => {
                console.log('res', res)
                if (res === null) {
                    reject();
                    return;
                }
                let url = window.URL.createObjectURL(
                    new Blob([res], { type: 'application/ms-excel' })
                );
                let link = document.createElement('a');
                link.style.display = 'none';
                link.href = url;
                link.setAttribute('download', fileName + '.xlsx');
                document.body.appendChild(link);
                link.click();
                // 释放 URL 对象
                window.URL.revokeObjectURL(url);
                resolve(res);
            })
            .catch((err: any) => {
                reject();
            });
    });
};

/**
 * 导出pdf
 * @param {*} requestApi 接口方法
 * @param {*} params 接口参数
 * @param {*} fileName 导出文件名
 */
export const exportPdf = (requestApi: any , params: any, fileName: string) => {
    return new Promise((resolve, reject) => {
        requestApi(params)
            .then((res: Blob) => {
                if (res === null) {
                    reject();
                    return;
                }
                let url = window.URL.createObjectURL(
                    new Blob([res], { type: 'application/pdf;chartset=UTF-8' })
                );
                window.open(url);
                // 释放 URL 对象
                window.URL.revokeObjectURL(url);
                resolve(res);
            })
            .catch((err: any) => {
                reject();
            });
    });
};

/**
 * 文件下载
 * @param {*} url 文件地址
 * @param {*} fileName 文件名
 */
export const downloadFile = (url: string, fileName: string) => {
    let link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
};

/**
 * 导入Excel文件
 * @param {*} file 文件对象
 * @param {*} uploadApi 上传API
 * @returns Promise
 */
export const importExcelFile = (file: File, uploadApi: Function): Promise<any> => {
    return new Promise((resolve, reject) => {
        const formData = new FormData();
        formData.append('file', file);
        
        uploadApi(file)
            .then((res: any) => {
                resolve(res);
            })
            .catch((err: any) => {
                reject(err);
            });
    });
};
