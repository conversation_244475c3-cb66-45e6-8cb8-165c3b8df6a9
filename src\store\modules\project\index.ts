import { defineStore } from 'pinia';

interface ProjectState {
    assetProjectId: string;
    assetProjectIdList: string[];
    rentProjectId: string;
}

const useProjectStore = defineStore('project', {
    state: (): ProjectState => ({
        assetProjectId: '',
        assetProjectIdList: [],
        rentProjectId: '',
    }),

    actions: {
        setAssetProjectId(projectId: string) {
            this.assetProjectId = projectId;
        },
        setAssetProjectIdList(projectIdList: string[]) {
            this.assetProjectIdList = projectIdList;
        },
        setRentProjectId(projectId: string) {
            this.rentProjectId = projectId;
        },
    },

    persist: {
        key: 'project-store',
        storage: localStorage,
        paths: ['assetProjectId', 'rentProjectId'],
    },
});

export default useProjectStore;