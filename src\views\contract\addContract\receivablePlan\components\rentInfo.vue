<template>
    <section>
        <sectionTitle title="租金信息"></sectionTitle>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table :scroll="{ x: 1500 }" :data="tableData" :pagination="false" :bordered="{ cell: true }" summary
                summary-text="合计">
                <template #columns>
                    <a-table-column title="租期" data-index="rentDate" :width="185" align="center">
                        <template #cell="{ record }">
                            <span>{{ dayjs(record.startDate).format('YYYY-MM-DD') }} 至 {{
                                dayjs(record.endDate).format('YYYY-MM-DD') }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="商户" data-index="customerName" :width="100" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="费项" data-index="subjectName" :width="100" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column :title="`签约单价（${priceUnitStr}）`" data-index="roomUnitPrice" :width="165"
                        align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(getPrice(record)) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="应收日期" data-index="receivableDate" :width="100" align="center">
                        <template #cell="{ record }">
                            <span>{{ record.receivableDate }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="账单总额（元）" data-index="totalAmount" :width="120" align="right">
                        <template #cell="{ record }">
                            <span>{{ formatAmount(record.totalAmount) }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="优惠金额（元）" data-index="discountAmount" :width="120" align="right">
                        <template #cell="{ record }">
                            <span>{{ formatAmount(record.discountAmount) }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="账单应收金额（元）" data-index="actualReceivable" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单已收金额（元）" data-index="receivedAmount" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.receivedAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单剩余应收金额（元）" data-index="remainingAmount" :width="160" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable - record.receivedAmount) }}
                        </template>
                    </a-table-column>

                </template>
                <template #summary-cell="{ column }">
                    <template v-if="column.dataIndex === 'rentDate'">合计</template>
                    <template v-if="column.dataIndex === 'totalAmount'">{{ formatAmount(totalAmountSum) }}</template>
                    <template v-if="column.dataIndex === 'discountAmount'">{{ formatAmount(discountAmountSum)
                        }}</template>
                    <template v-if="column.dataIndex === 'actualReceivable'">{{ formatAmount(actualReceivableSum)
                        }}</template>
                    <template v-if="column.dataIndex === 'receivedAmount'">{{ formatAmount(receivedAmountSum)
                        }}</template>
                    <template v-if="column.dataIndex === 'remainingAmount'">{{ formatAmount(remainingAmountSum)
                        }}</template>
                </template>
            </a-table>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import dayjs from 'dayjs';
import { ContractCostDTO } from '@/api/contract';
import { Message } from '@arco-design/web-vue';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const unitMap = new Map([
    [1, '元/平方米/月'],
    [2, '元/月']
])

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractCosts)
const contractData = computed(() => contractStore.contractData)

const tableData = ref<any[]>([])
const priceUnitStr = ref('')

// 计算单价
const getPrice = (record: any) => {
    // 1. 如果单位是1，则单价为 账单总额除以面积之和
    // 2. 如果单位是2，则单价为 账单总额除以数量之和
    if (record.priceUnit === 1) {
        const areaTotal = contractData.value.rooms.reduce((acc, item) => acc + (item.area ?? 0), 0)
        return areaTotal === 0 ? 0 : record.totalAmount / areaTotal
    } else {
        const numTotal = contractData.value.rooms.length
        return numTotal === 0 ? 0 : record.totalAmount / numTotal
    }
}

/**
 * 监听应收计划计算结果 - 标准合同处理
 * 合并costs中所有costType为租金且period相同的数据
 */
watch(contractCosts, (newVal) => {
    if (newVal.costs && newVal.costs.length > 0) {
        const rentCosts = newVal.costs.filter((item: any) => item.costType === 2)
        if (rentCosts.length > 0) {
            const rentCostsGroup = rentCosts.reduce((acc: any, item: any) => {
                if (!acc[item.period]) {
                    acc[item.period] = []
                }
                acc[item.period].push(item)
                return acc
            }, {} as Record<number, ContractCostDTO[]>)

            priceUnitStr.value = unitMap.get(rentCosts[0].priceUnit) ?? ''

            tableData.value = Object.values(rentCostsGroup).map((item: any) => {
                const startDate = dayjs(item[0].startDate).format('YYYY-MM-DD')
                const endDate = dayjs(item[item.length - 1].endDate).format('YYYY-MM-DD')
                const customerName = item[0].customerName ?? ''
                const subjectName = item[0].subjectName ?? ''
                const roomUnitPrice = item[0].unitPrice ?? 0
                const receivableDate = item[0].receivableDate ? dayjs(item[0].receivableDate).format('YYYY-MM-DD') : ''
                const totalAmount = item.reduce((acc: any, item: any) => acc + (item.totalAmount ?? 0), 0)
                const discountAmount = item.reduce((acc: any, item: any) => acc + (item.discountAmount ?? 0), 0)
                const actualReceivable = item.reduce((acc: any, item: any) => acc + (item.actualReceivable ?? 0), 0)
                const receivedAmount = item.reduce((acc: any, item: any) => acc + (item.receivedAmount ?? 0), 0)
                const area = item.reduce((acc: any, item: any) => acc + (item.area ?? 0), 0)
                const priceUnit = item[0].priceUnit
                return {
                    startDate,
                    endDate,
                    customerName,
                    subjectName,
                    roomUnitPrice,
                    receivableDate,
                    totalAmount,
                    discountAmount,
                    actualReceivable,
                    receivedAmount,
                    area,
                    priceUnit
                }
            })
        } else {
            tableData.value = []
        }
    }
}, { deep: true, immediate: true })

// 计算合计
const totalAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.totalAmount || 0), 0))
const discountAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.discountAmount || 0), 0))
const actualReceivableSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.actualReceivable || 0), 0))
const receivedAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.receivedAmount || 0), 0))
const remainingAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + ((item.actualReceivable || 0) - (item.receivedAmount || 0)), 0))

// 校验方法
const validate = () => {
    try {
        // 检查是否有租金数据
        if (!tableData.value || tableData.value.length === 0) {
            Message.error('租金信息不能为空')
            return false
        }
        
        // 检查每条数据的账单总额不能为0
        for (let i = 0; i < tableData.value.length; i++) {
            const item = tableData.value[i]
            
            if (!item || typeof item !== 'object') {
                Message.error(`第${i + 1}期租金账单数据格式错误`)
                return false
            }
            
            // 校验账单总额不能为0
            if (Number(item.totalAmount) === 0) {
                Message.error(`第${i + 1}期租金账单的总额不能为0`)
                return false
            }
        }
        
        return true
    } catch (error: any) {
        Message.error(`租金信息校验异常: ${error.message}`)
        return false
    }
}

// 暴露校验方法给父组件
defineExpose({
    validate
})
</script>

<style lang="less" scoped></style>