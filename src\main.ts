import { createApp } from 'vue';
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import globalComponents from '@/components';
import router from './router';
import store from './store';
import i18n from './locale';
import directive from './directive';
import BaiduMap from 'vue-baidu-map-3x';
// import './mock';
import App from './App.vue';
// Styles are imported via arco-plugin. See config/plugin/arcoStyleImport.ts in the directory for details
// 样式通过 arco-plugin 插件导入。详见目录文件 config/plugin/arcoStyleImport.ts
// https://arco.design/docs/designlab/use-theme-package
import '@/assets/style/global.less';

import 'virtual:svg-icons-register';
import './router/permission';
const appBaseUrl = import.meta.env.VITE_APP_BASE_URL

const app = createApp(App);

app.use(ArcoVue, {});
app.use(ArcoVueIcon);

app.use(router);
app.use(store);
app.use(i18n);
app.use(globalComponents);
app.use(directive);
app.use(BaiduMap, {
    // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
    // ak: 'O506gVPHjmvMXkA64AmzXArXzpM94E6R'
    ak: '0IEwIktUHtquTtSil9eDfcgbnPVO3KgK'
})
app.config.globalProperties.$appBaseUrl = appBaseUrl

app.mount('#app');
