# 组件问题
-<a-modal :visible="voidModalVisible"
 - 组件的表单验证，如果表单验证不通过，则无法关闭弹窗，需要手动关闭弹窗

# 公共组件
https://ulbnx7wsgg.feishu.cn/wiki/SG3QwQBsmiLM4qkN0uAclk7knHa

# figma MCP配置

    "Figma AI Bridge": {
      "command": "npx",
      "args": [
        "-y",
        "figma-developer-mcp",
        "--stdio"
      ],
      "env": {
        "FIGMA_API_KEY": "*********************************************"
      }
    }

# apifox MCP配置
- {
  "mcpServers": {
    "万洋资管平台接口MCP": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--project=6144247"
      ],
      "env": {
        "APIFOX_ACCESS_TOKEN": "APS-8FMnbMmIKFDdSJMSWEVvT1f4FUvybUTK"
      }
    }
  }
}

安装配置好 MCP 后，Apifox MCP Server 会自动读取接口文档的数据并缓存在本地电脑，AI 可以通过 MCP 读取接口文档数据。
你只要告诉 AI 你想要通过 API 文档做什么即可，示例：
1.
“通过 MCP 获取 API 文档，然后生成 Product 及其相关模型的定义代码”
2.
“根据 API 文档，在 Product DTO 里添加 API 文档新增的几个字段”
3.
“根据 API 文档给 Product 类的每个字段都加上注释”
4.
“根据 API 文档，生成接口 /users 相关的所有 MVC 代码”
注意：接口文档数据默认是会缓存在本地的，如果接口文档内的数据有更新，请告诉 AI 刷新接口文档数据，否则 AI 读到的数据可能不是最新的。