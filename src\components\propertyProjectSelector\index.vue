<template>
  <div class="property-project-selector">
    <!-- 项目选择 -->
    <div class="project-select">
      <ProjectTreeSelect 
        v-model="selectedProject" 
        :min-level="4"
        @change="handleProjectChange"
      />
    </div>

    <!-- 地块和楼栋树形结构 -->
    <div class="tree-container" v-if="selectedProject">
      <a-spin :loading="loading" style="width: 100%;overflow: hidden;">
        <a-tree
          :key="selectedProject"
          :data="projectTreeData"
          :field-names="{
            key: 'id',
            title: 'name',
            children: 'children'
          }"
          :selected-keys="selectedTreeKey"
          :expanded-keys="expandedKeys"
          @select="handleTreeSelect"
          @expand="handleTreeExpand"
        />
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import { getProjectTree, type ProjectTreeNodeVo } from '@/api/project'

const emit = defineEmits<{
  (e: 'update:selection', value: { 
    projectId: string; 
    projectName: string;
    blockId: string; 
    blockName: string;
    buildingId: string;
    buildingName: string;
  }): void
}>()

const selectedProject = ref<string>('')
const projectTreeData = ref<ProjectTreeNodeVo[]>([])
const selectedTreeKey = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const loading = ref(false)

// 监听项目选择变化
watch(selectedProject, async (newValue, oldValue) => {
  console.log('propertyProjectSelector watch 触发:', { newValue, oldValue, type: typeof newValue })
  if (newValue && newValue.toString().trim() !== '' && newValue !== oldValue) {
    loading.value = true
    try {
      // 重置树形状态
      selectedTreeKey.value = []
      expandedKeys.value = []
      
      // 调用真实API获取地块和楼栋数据
      console.log('调用getProjectTree API，项目ID:', newValue)
      const response = await getProjectTree({ projectId: newValue })
      if (response && response.data) {
        // 如果返回的是单个节点，需要转换为数组
        projectTreeData.value = Array.isArray(response.data) ? response.data : [response.data]
        console.log('获取到项目树数据:', projectTreeData.value)
        
        // 等待DOM更新完成后设置默认展开和选中
        await nextTick()
        // 再次等待确保树形组件完全渲染
        setTimeout(() => {
          setDefaultSelection()
        }, 100)
      } else {
        console.log('API返回数据为空')
        projectTreeData.value = []
        emitSelection()
      }
    } catch (error) {
      console.error('获取项目树数据失败:', error)
      projectTreeData.value = []
      emitSelection()
    } finally {
      loading.value = false
    }
  } else if (!newValue) {
    console.log('项目选择被清空')
    projectTreeData.value = []
    selectedTreeKey.value = []
    expandedKeys.value = []
    emitSelection()
  }
})

// 设置默认选择和展开
const setDefaultSelection = () => {
  console.log('开始设置默认选择，当前树数据:', projectTreeData.value)
  
  if (projectTreeData.value.length === 0) {
    console.log('树数据为空，直接触发选择事件')
    emitSelection()
    return
  }

  // 获取根节点（"全部"节点）
  const rootNode = projectTreeData.value[0]
  if (!rootNode || !rootNode.children || rootNode.children.length === 0) {
    console.log('根节点或地块数据无效，直接触发选择事件')
    emitSelection()
    return
  }

  // 获取第一个真正的地块（根节点的第一个子节点）
  const firstBlock = rootNode.children[0]
  if (!firstBlock || !firstBlock.id) {
    console.log('第一个地块无效，直接触发选择事件')
    emitSelection()
    return
  }

  console.log('第一个地块:', firstBlock)

  // 如果第一个地块有楼栋，展开根节点和地块，并选中第一个楼栋
  if (firstBlock.children && firstBlock.children.length > 0) {
    const firstBuilding = firstBlock.children[0]
    if (firstBuilding && firstBuilding.id) {
      console.log('找到第一个楼栋:', firstBuilding)
      
      // 先清空状态
      expandedKeys.value = []
      selectedTreeKey.value = []
      
      // 使用 nextTick 确保状态更新
      nextTick(() => {
        // 展开根节点和第一个地块
        expandedKeys.value = [rootNode.id!, firstBlock.id!]
        console.log('设置展开节点:', { rootId: rootNode.id, blockId: firstBlock.id })
        
        // 再次使用 nextTick 确保展开状态生效后再选中楼栋
        nextTick(() => {
          selectedTreeKey.value = [firstBuilding.id!]
          console.log('设置选中楼栋:', firstBuilding.id)
          
          // 最后触发选择事件
          setTimeout(() => {
            emitSelection()
          }, 50)
        })
      })
    } else {
      console.log('第一个楼栋无效，选中地块')
      expandedKeys.value = [rootNode.id!]
      selectedTreeKey.value = [firstBlock.id!]
      emitSelection()
    }
  } else {
    // 如果没有楼栋，展开根节点并选中地块本身
    console.log('没有楼栋，展开根节点并选中地块:', firstBlock.id)
    expandedKeys.value = [rootNode.id!]
    selectedTreeKey.value = [firstBlock.id!]
    emitSelection()
  }
}

// ProjectTreeSelect 组件已经处理了项目的获取和默认选择
// 这里只需要处理项目变化后的地块楼栋数据获取

// 存储项目名称
const selectedProjectName = ref<string>('')

// 处理项目选择
const handleProjectChange = (value: string | number, selectedOrg: any) => {
  console.log('ProjectTreeSelect 项目选择变化:', value, selectedOrg, '当前selectedProject:', selectedProject.value)
  selectedProject.value = String(value)
  selectedProjectName.value = selectedOrg?.name || ''
  console.log('更新后的selectedProject:', selectedProject.value, '项目名称:', selectedProjectName.value)
  // 项目变化时不立即触发emitSelection，等待树形数据加载完成后再触发
}

// 处理树形选择
const handleTreeSelect = (selectedKeys: string[]) => {
  selectedTreeKey.value = selectedKeys
  emitSelection()
}

// 处理树形展开
const handleTreeExpand = (keys: string[]) => {
  expandedKeys.value = keys
}

// 发送选择结果到父组件
const emitSelection = () => {
  const selection = {
    projectId: selectedProject.value,
    projectName: selectedProjectName.value,
    blockId: '',
    blockName: '',
    buildingId: '',
    buildingName: ''
  }
  
  // 解析选中的节点ID
  if (selectedTreeKey.value.length > 0) {
    const nodeId = selectedTreeKey.value[selectedTreeKey.value.length - 1]
    if (nodeId) {
      console.log('解析选中节点ID:', nodeId)
      
      // 根据三层结构查找节点：根节点("全部") → 地块 → 楼栋
      const findNodeInTree = (nodes: ProjectTreeNodeVo[], targetId: string, level: number = 0): { node: ProjectTreeNodeVo; parent?: ProjectTreeNodeVo; level: number } | null => {
        for (const node of nodes) {
          if (node.id === targetId) {
            return { node, level }
          }
          if (node.children) {
            for (const child of node.children) {
              if (child.id === targetId) {
                return { node: child, parent: node, level: level + 1 }
              }
              if (child.children) {
                for (const grandChild of child.children) {
                  if (grandChild.id === targetId) {
                    return { node: grandChild, parent: child, level: level + 2 }
                  }
                }
              }
            }
          }
        }
        return null
      }
      
      const result = findNodeInTree(projectTreeData.value, nodeId)
      console.log('查找节点结果:', result)
      
      if (result) {
        if (result.level === 0) {
          // 选中的是根节点("全部")，不设置地块和楼栋
          console.log('选中根节点')
        } else if (result.level === 1) {
          // 选中的是地块
          selection.blockId = nodeId
          selection.blockName = result.node.name || ''
          console.log('选中地块:', { blockId: nodeId, blockName: result.node.name })
        } else if (result.level === 2) {
          // 选中的是楼栋
          selection.buildingId = nodeId
          selection.buildingName = result.node.name || ''
          selection.blockId = result.parent?.id || ''
          selection.blockName = result.parent?.name || ''
          console.log('选中楼栋:', { 
            buildingId: nodeId, 
            buildingName: result.node.name,
            blockId: result.parent?.id,
            blockName: result.parent?.name
          })
        }
      }
    }
  }
  
  console.log('发送选择结果到父组件:', selection)
  emit('update:selection', selection)
}
</script>

<style scoped lang="less">
.property-project-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
  .tree-container {
    margin-top: 16px;
    flex: 1;
    overflow-y: auto;
  }
  :deep(.arco-tree-node-title-text){
    width: 130px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; 
  }
}
</style> 