<template>
    <a-drawer class="common-drawer" v-model:visible="visible" :title="getDrawerTitle()" :body-style="{ padding: 0 }"
        :width="1200" :footer="true" @cancel="handleClose" @close="handleClose" :mask-closable="true"
        :esc-to-close="false">
        <div class="contract-termination-main">
            <!-- 步骤条 - 只在退租并出场模式下显示 -->
            <div class="contract-termination-main-header" v-if="selectedMode === 'apply-and-exit'">
                <a-steps :current="currentStep">
                    <a-step title="退租申请" />
                    <a-step title="出场办理" />
                </a-steps>
            </div>

            <!-- 退租申请步骤 -->
            <div class="contract-termination-main-content" v-if="currentStep === 1">
                <contract-termination :contract-id="contractId" :terminate-id="terminateId" :union-id="unionId"
                    :mode="selectedMode" :refund-type="refundType" :view-mode="viewMode" :exit-data="localExitData" @save="handleSave" @submit="handleSubmit" @next="handleNext"
                    ref="terminationRef" />
            </div>

            <!-- 出场办理步骤 -->
            <div class="contract-termination-main-content" v-if="currentStep === 2">
                <exit-handler-new ref="exitHandlerRef" :data="exitData" :mode="exitMode" :from-terminate="true" @cancel="handleExitCancel" @refresh="handleExitDataRefresh" @mode-change="handleModeChange" />
            </div>
        </div>

        <!-- 底部操作按钮 -->
        <template #footer>
            <div class="drawer-footer-actions">
                <a-space>
                    <a-button @click="handleCancel">
                        {{ viewMode ? '关闭' : '取消' }}
                    </a-button>

                    <!-- 查看模式下不显示操作按钮 -->
                    <template v-if="!viewMode">
                        <!-- 退租申请步骤的按钮 -->
                        <template v-if="currentStep === 1">
                            <a-button type="primary" status="success" @click="handleSaveClickMethod" :loading="loading">
                                暂存
                            </a-button>
                            <a-button v-if="selectedMode === 'apply-only'" type="primary" @click="handleSubmitClick"
                                :loading="loading">
                                提交申请
                            </a-button>
                            <a-button v-else-if="selectedMode === 'apply-and-exit'" type="primary" @click="handleNextClick"
                                :loading="loading">
                                下一步
                            </a-button>
                            <a-button v-if="canCancel" type="primary" status="danger" @click="handleCancelTerminate"
                                :loading="loading">
                                取消退租
                            </a-button>
                        </template>

                        <!-- 出场办理步骤的按钮 -->
                        <template v-else-if="currentStep === 2">
                            <a-button @click="handlePrev">
                                上一步
                            </a-button>

                            <!-- property-only 模式：物业交割步骤显示进入下一步按钮 -->
                            <a-button type="primary" size="large" :disabled="!allRoomsConfirmed" @click="handleNextStep"
                                v-if="exitMode === 'property-only' && currentActiveTab === '2'">
                                进入下一步费用结算
                            </a-button>

                            <!-- property-only 模式：费用结算步骤显示暂存和提交按钮 -->
                            <a-button type="primary" status="success" @click="handleExitSaveClick" :loading="loading"
                                v-if="exitMode === 'property-only' && currentActiveTab === '3'">
                                暂存
                            </a-button>
                            <a-button type="primary" @click="handleExitSubmitClick" :loading="loading"
                                v-if="exitMode === 'property-only' && currentActiveTab === '3'">
                                提交
                            </a-button>

                            <!-- property-and-settlement 模式：直接显示暂存和提交按钮 -->
                            <a-button type="primary" status="success" @click="handleExitSaveClick" :loading="loading"
                                v-if="exitMode === 'property-and-settlement'">
                                暂存
                            </a-button>
                            <a-button type="primary" @click="handleExitSubmitClick" :loading="loading"
                                v-if="exitMode === 'property-and-settlement'">
                                提交
                            </a-button>
                        </template>
                    </template>
                </a-space>
            </div>
        </template>
    </a-drawer>

    <!-- 出场选择弹窗 - 用于退租并出场流程的第二步选择 -->
    <a-modal v-model:visible="exitModeModalVisible" title="选择出场模式" :footer="false" :width="600"
        @cancel="handleExitModeCancel">
        <exit-process :data="terminationData || {}" @cancel="handleExitModeCancel"
            @property-only="handleModalPropertyOnly" @property-and-settlement="handleModalPropertyAndSettlement"
            @refresh="handleRefreshList" />
    </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, nextTick, readonly } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
import contractTermination from './components/contractTermination.vue'

import exitProcess from '@/views/operationManage/components/exitProcess.vue'
import exitHandlerNew from '@/views/operationManage/components/exitHandlerNew.vue'
import { getContractTerminateExitDetail, saveContractTerminateWithExit } from '@/api/contractTerminate'
import type { ContractTerminateAddDTO, ContractTerminateWithExitDTO } from '@/api/contractTerminate'

// Props
interface Props {
    contractId?: string
    terminateId?: string
    unionId?: string
    modelValue?: boolean // 支持 v-model 控制显示/隐藏
    viewMode?: boolean // 查看模式
}
// 保证金
  // 截至退款日应收(元)
// 账单已收金额(元)          罚没金额 退最小的  预计退款金额等于最小

// import refundApplyForm from '@/views/operationManage/components/RefundApplyForm.vue'

const props = withDefaults(defineProps<Props>(), {
    contractId: '',
    terminateId: '',
    unionId: '',
    modelValue: false,
    viewMode: false
})

let canCancel = ref(false)

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    'save': [data: ContractTerminateAddDTO]
    'submit': [data: ContractTerminateAddDTO]
    'close': []
    'cancel': []
    'refresh': [] // 新增刷新事件
}>()

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const currentStep = ref(1)
const terminationRef = ref()
const exitHandlerRef = ref()
const visible = ref(false)

// 新增状态管理
const selectedMode = ref<'apply-only' | 'apply-and-exit'>('apply-only')
const terminationData = ref<any>(null)
const exitData = ref<any>(null)
const exitDetailData = ref<any>(null) // 新增：完整的出场详情数据
const exitMode = ref<'property-only' | 'property-and-settlement'>('property-only')
const exitModeModalVisible = ref(false)
const currentActiveTab = ref('1') // 当前活跃的tab
const savedTerminateFormData = ref<any>(null) // 保存第一步表单数据
const refundType = ref<0 | 1>(0)
const viewMode = ref(false) // 查看模式状态

// 内部状态
const contractId = ref('')
const terminateId = ref('')
const unionId = ref('')

// 计算属性
const canGoNext = computed(() => {
    // 可以根据表单验证状态决定是否允许下一步
    return true
})

// 新增本页面使用的数据处理
const localExitData = computed(() => {
    if (!exitData.value) return null
    return exitData.value.data || {}
})

// 检查所有房间是否都确认通过
const allRoomsConfirmed = computed(() => {
    console.log('===================exitData.value.exitRoomList', exitData.value?.exitRoomList, exitMode.value)

    if (!localExitData.value?.exitRoomList || localExitData.value.exitRoomList.length === 0) {
        return false
    }

    // 物业交割并结算模式下，不需要物业确认(isFinanceConfirmed)
    if (exitMode.value === 'property-and-settlement') {
        return localExitData.value.exitRoomList.every((room: any) =>
            room.isBusinessConfirmed &&
            room.isEngineeringConfirmed
        )
    }

    // 先物业交割，后续结算模式下，需要三方确认
    return localExitData.value.exitRoomList.every((room: any) =>
        room.isBusinessConfirmed &&
        room.isEngineeringConfirmed &&
        room.isFinanceConfirmed
    )
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
    visible.value = newVal
}, { immediate: true })

// 监听 visible 变化，同步给父组件
watch(visible, (newVal) => {
    emit('update:modelValue', newVal)
})

// 监听当前步骤变化，在切换到第一步时刷新数据
watch(currentStep, async (newStep, oldStep) => {
    if (newStep === 1 && oldStep === 2) {
        // 从第二步返回第一步时，刷新退租申请数据
        await nextTick() // 等待组件渲染
        if (terminationRef.value && typeof terminationRef.value.reload === 'function') {
            terminationRef.value.reload()
        }
    }
})

// 监听 props 变化
watch(() => props.contractId, (newVal) => {
    if (newVal) {
        contractId.value = newVal
    }
}, { immediate: true })

watch(() => props.viewMode, (newVal) => {
    viewMode.value = newVal || false
}, { immediate: true })

watch(() => props.terminateId, (newVal) => {
    if (newVal) {
        terminateId.value = newVal
    }
}, { immediate: true })

watch(() => props.unionId, (newVal) => {
    if (newVal) {
        unionId.value = newVal
    }
}, { immediate: true })

// 监听路由变化（如果作为页面使用）
watch(() => route.query, (newQuery) => {
    if (newQuery.contractId) {
        contractId.value = newQuery.contractId as string
    }
    if (newQuery.terminateId) {
        terminateId.value = newQuery.terminateId as string
    }
    if (newQuery.unionId) {
        unionId.value = newQuery.unionId as string
    }
}, { immediate: true })

// 公开方法 - 打开抽屉
const open = (params?: {
    contractId?: string;
    terminateId?: string;
    unionId?: string;
    mode?: 'apply-only' | 'apply-and-exit' ,
    refundType?: 0 | 1
    canCancel?: boolean
    viewMode?: boolean
}) => {
    if (params?.contractId) {
        contractId.value = params.contractId
    }
    if (params?.terminateId) {
        terminateId.value = params.terminateId
    }
    if (params?.unionId) {
        unionId.value = params.unionId
    }
    if (params?.mode) {
        selectedMode.value = params.mode
    }
    if (params?.canCancel) {
        canCancel.value = params.canCancel
    }else {
        canCancel.value = false
    }

    console.log('params?.refundType', params?.refundType)
    if (params?.refundType !== undefined && params?.refundType !== null) {
        refundType.value = params.refundType
        console.log('设置 refundType 为:', params.refundType)
    }

    if (params?.viewMode !== undefined) {
        viewMode.value = params.viewMode
    }

    // 重置状态
    currentStep.value = 1
    loading.value = false
    terminationData.value = null
    exitData.value = null
    exitDetailData.value = null

    // 打开抽屉
    visible.value = true

    // 等待DOM更新后，子组件会自动初始化数据
    // contractTermination 组件在 onMounted 中会自动调用 loadTerminateDetail()
}

// 新增：直接打开出场选择弹窗
const openExitModal = (data: any) => {
    terminationData.value = data
    exitModeModalVisible.value = true
}

// 关闭抽屉
const handleClose = () => {
    visible.value = false

    // 重置状态
    currentStep.value = 1
    loading.value = false
    contractId.value = ''
    terminateId.value = ''
    unionId.value = ''
    selectedMode.value = 'apply-only'
    terminationData.value = null
    exitData.value = null
    exitDetailData.value = null
    exitMode.value = 'property-only'

    emit('close')
    emit('cancel')
}

// 取消操作
const handleCancel = () => {
    // 如果有未保存的修改，提示用户确认
    if (currentStep.value > 1 || (terminationRef.value && typeof terminationRef.value.getFormData === 'function')) {
        // 可以在这里检查表单是否有修改
        // 暂时直接关闭，后续可以根据需要添加确认对话框
        handleClose()
    } else {
        handleClose()
    }
}

// 取消退租
const handleCancelTerminate = async () => {
    console.log('取消退租')

    // 确认对话框
    Modal.confirm({
        title: '确认取消退租',
        content: '确定要取消当前退租申请吗？取消后将无法恢复。',
        okText: '确认取消',
        cancelText: '返回',
        okButtonProps: {
            status: 'danger'
        },
        onOk: async () => {
            try {
                loading.value = true
                const { cancelContractTerminate } = await import('@/api/contractTerminate')

                // 获取退租ID - 优先使用terminateId，其次使用exitDetailData中的退租ID
                const currentTerminateId = terminateId.value || exitDetailData.value?.id || terminationData.value?.id

                if (!currentTerminateId) {
                    Message.error('缺少退租ID，无法取消退租')
                    return
                }

                const res = await cancelContractTerminate(currentTerminateId)
                if (res && res.code === 200) {
                    Message.success('退租申请已取消')

                    // 触发刷新事件并关闭抽屉
                    emit('refresh')
                    handleClose()
                } else {
                    // Message.error(res?.msg || '取消退租失败')
                }
            } catch (error: any) {
                console.error('取消退租失败:', error)
                // Message.error(error?.response?.data?.msg || error?.msg || '取消退租失败，请稍后重试')
            } finally {
                loading.value = false
            }
        }
    })
}

// 下一步
const handleNextClick = async () => {
    if (currentStep.value === 1) {
        // 调用子组件的 handleNext 方法，它会进行验证、暂存和提交
        if (terminationRef.value && typeof terminationRef.value.handleNext === 'function') {
            try {
                loading.value = true
                await terminationRef.value.handleNext()
                // handleNext 会触发 handleNext 事件，在事件处理中会打开出场选择弹窗
            } catch (error) {
                console.error('下一步处理失败:', error)
                // Message.error('下一步处理失败')
            } finally {
                loading.value = false
            }
        } else {
            // 如果子组件没有 handleNext 方法，则直接打开出场选择弹窗
            try {
                const isValid = await terminationRef.value.validate()
                if (!isValid) {
                    return
                }
                // 打开出场选择弹窗
                // exitModeModalVisible.value = true
                handleNext(terminationRef.value.getFormData())
            } catch (error) {
                console.error('表单验证失败:', error)
                return
            }
        }
    }
}

// 上一步
const handlePrev = async () => {
    if (currentStep.value > 1) {
        currentStep.value -= 1

        // 如果返回到第一步，刷新退租申请数据
        if (currentStep.value === 1) {
            await nextTick() // 等待组件渲染
            if (terminationRef.value && typeof terminationRef.value.reload === 'function') {
                terminationRef.value.reload()
            }
        }
    }
}

// 进入下一步费用结算
const handleNextStep = () => {
    // 检查所有房间是否都确认通过
    if (!allRoomsConfirmed.value) {
        Message.warning('请确保所有交割单都已确认通过')
        return
    }

    // 切换到费用结算标签页
    currentActiveTab.value = '3'

    // 通知子组件切换标签
    if (exitHandlerRef.value && typeof exitHandlerRef.value.setActiveTab === 'function') {
        exitHandlerRef.value.setActiveTab('3')
    }
}

const handleSaveClickMethod = async () => {
    if (currentStep.value === 1 && terminationRef.value) {
        try {
            loading.value = true
            await terminationRef.value.handleSave(false)
        } catch (error) {
            console.error('暂存失败:', error)
        } finally {
            loading.value = false
        }
    }
}

// 暂存点击
const handleSaveClick = async () => {
    if (currentStep.value === 1 && terminationRef.value) {
        try {
            loading.value = true
            await terminationRef.value.handleSave()
        } catch (error) {
            console.error('暂存失败:', error)
        } finally {
            loading.value = false
        }
    }
}

// 提交点击
const handleSubmitClick = async () => {
    try {
        loading.value = true

        if (currentStep.value === 1 && terminationRef.value) {
            // 第一步：提交退租申请
            await terminationRef.value.handleSubmit()
        } else if (currentStep.value === 2) {
            // 第二步：提交出场办理
            console.log('提交出场办理')
            // Message.success('出场办理提交成功')
            handleClose()
        }
    } catch (error) {
        console.error('提交失败:', error)
    } finally {
        loading.value = false
    }
}

// 处理子组件暂存事件
const handleSave = (data: ContractTerminateAddDTO) => {
    console.log('暂存数据:', data)
    // Message.success('退租申请暂存成功')
    emit('save', data)
}

// 处理子组件提交事件
const handleSubmit = (data: ContractTerminateAddDTO) => {
    console.log('提交数据:', data)
    // Message.success('退租申请提交成功')
    emit('submit', data)

    // 先退租后出场模式：直接关闭
    handleClose()
}

// 处理下一步事件
const handleNext = async (data: ContractTerminateAddDTO) => {
    console.log('下一步数据:', data)
    // 保存第一步的表单数据
    savedTerminateFormData.value = data

    // 如果有退租ID，调用getContractTerminateExitDetail获取完整出场数据
    if (data.id) {
        try {
            const res = await getContractTerminateExitDetail(data.id, true)
            if (res && res.code === 200) {
                const responseData = res.data // 完整的响应数据
                exitData.value = res // 恢复使用完整的res数据
                exitDetailData.value = responseData

                // 根据接口文档，ContractTerminateVo没有exitInfo字段
                // 直接从responseData构建exitInfo，如果有processType则使用，否则不设置
                if ((responseData as any).exitInfo) {
                    // 如果API返回中包含exitInfo（可能是接口升级后的新字段）
                    terminationData.value = (responseData as any).exitInfo
                } else {
                    // 兼容处理：从合同数据构建基础exitInfo
                    const exitInfo = {
                        id: responseData.id,
                        projectId: responseData.contract?.projectId || '',
                        contractId: contractId.value,
                        contractNo: responseData.contract?.contractNo || '',
                        contractUnionId: unionId.value,
                        terminateId: responseData.id,
                        customerId: responseData.contract?.customer?.customerId || '',
                        customerName: responseData.contract?.customer?.customerName || '',
                        terminateType: responseData.terminateType,
                        terminateDate: responseData.terminateDate,
                        contractPurpose: responseData.contract?.contractPurpose
                    }
                    terminationData.value = exitInfo
                }

                console.log('构建的出场详情:', exitDetailData.value)
            }
        } catch (error) {
            console.error('获取出场详情失败:', error)
            // 即使获取详情失败，也要打开弹窗，让用户选择
        }
    }

    // 如果 terminationData.value里面的 processType 已经存在 直接进入出场，参考 出场管理
    if (terminationData.value && terminationData.value.processType) {
        console.log('已存在processType，直接进入出场办理:', terminationData.value.processType)
        exitMode.value = terminationData.value.processType === 1 ? 'property-only' : 'property-and-settlement'

        // 设置初始tab
        if (exitMode.value === 'property-only') {
            currentActiveTab.value = '2' // 物业交割
        } else {
            currentActiveTab.value = '4' // 物业交割和费用结算
        }

        // 设置出场数据
        // exitData.value = {
        //     ...exitDetailData.value,
        //     mode: exitMode.value
        // }

    currentStep.value = 2
    } else {
        // 打开出场选择弹窗，而不是进入下一步
        exitModeModalVisible.value = true
    }
}

// 计算抽屉标题
const getDrawerTitle = () => {
    if (selectedMode.value === 'apply-only') {
        return '合同退租申请'
    } else {
        switch (currentStep.value) {
            case 1:
                return '合同退租申请'
            case 2:
                return '办理出场'
            default:
                return '合同退租'
        }
    }
}

// 出场选择相关方法
const handleExitCancel = () => {
    // 从出场办理返回退租申请
        currentStep.value = 1
    }

// 弹窗出场选择相关方法
const handleExitModeCancel = () => {
    exitModeModalVisible.value = false
    // 不重置 terminationData，保持数据
}

const handleModalPropertyOnly = () => {
    exitMode.value = 'property-only'
    exitModeModalVisible.value = false

    // 优先使用完整的出场详情数据，如果没有则使用基础数据
    const baseData = exitDetailData.value || {
        ...terminationData.value,
        contractId: contractId.value,
        terminateId: terminateId.value,
        id: terminationData.value?.id
    }

    // exitData.value = {
    //     ...baseData,
    //     mode: 'property-only'
    // }

    exitData.value.mode = 'property-only'

    // 设置初始tab为物业交割
    currentActiveTab.value = '2'

    // 跳转到出场办理步骤
    currentStep.value = 2
}

const handleModalPropertyAndSettlement = () => {
    exitMode.value = 'property-and-settlement'
    exitModeModalVisible.value = false

    // 优先使用完整的出场详情数据，如果没有则使用基础数据
    const baseData = exitDetailData.value || {
        ...terminationData.value,
        contractId: contractId.value,
        terminateId: terminateId.value,
        id: terminationData.value?.id
    }

    // exitData.value = {
    //     ...baseData,
    //     mode: 'property-and-settlement'
    // }
    exitData.value.mode = 'property-and-settlement'

    // 设置初始tab为物业交割和费用结算
    currentActiveTab.value = '4'

    // 跳转到出场办理步骤
    currentStep.value = 2
}

// 出场办理暂存点击
const handleExitSaveClick = async () => {
    try {
        loading.value = true

        // 1. 获取完整的退租数据
        let terminateAddDTO: any = {}

        console.log('当前步骤:', currentStep.value)
        console.log('terminationRef.value:', !!terminationRef.value)
        console.log('terminationRef.value.getFormData:', typeof terminationRef.value?.getFormData)
        console.log('savedTerminateFormData.value:', savedTerminateFormData.value)

        // 优先使用保存的表单数据
        if (savedTerminateFormData.value) {
            console.log('使用保存的表单数据')
            terminateAddDTO = { ...savedTerminateFormData.value }
            terminateAddDTO.isSubmit = false // 暂存
        } else if (terminationRef.value && typeof terminationRef.value.getFormData === 'function') {
            console.log('从表单组件获取退租数据')
            terminateAddDTO = terminationRef.value.getFormData()
            terminateAddDTO.isSubmit = false // 暂存
        } else {
            // Fallback: 使用已保存的数据，确保必要字段不为空
            console.log('使用已保存数据构建退租数据')
            console.log('exitDetailData.value:', exitDetailData.value)
            const terminateData = exitDetailData.value
            terminateAddDTO = {
                id: terminateData?.id,
                contractId: contractId.value,
                unionId: unionId.value || terminateData?.unionId || terminateData?.contractTerminateInfo?.unionId,
                terminateType: terminateData?.terminateType ?? 0, // 确保有默认值
                terminateDate: terminateData?.terminateDate || terminateData?.contract?.endDate || '',
                terminateReason: terminateData?.terminateReason || '1', // 默认到期退租
                otherReasonDesc: terminateData?.otherReasonDesc || '',
                hasOtherDeduction: terminateData?.hasOtherDeduction || false,
                otherDeductionDesc: terminateData?.otherDeductionDesc || '',
                terminateRemark: terminateData?.terminateRemark || '',
                terminateAttachments: terminateData?.terminateAttachments || '[]',
                bondReceivedAmount: terminateData?.bondReceivedAmount || 0,
                rentReceivedAmount: terminateData?.rentReceivedAmount || 0,
                rentOverdueAmount: terminateData?.rentOverdueAmount || 0,
                receivedPeriod: terminateData?.receivedPeriod || '',
                overduePeriod: terminateData?.overduePeriod || '',
                roomList: terminateData?.roomList || [],
                costList: terminateData?.costList || [],
                feeList: terminateData?.contract?.fees || [],
                isSubmit: false
            }
        }

        console.log('terminateAddDTO:', terminateAddDTO)
            if(!terminateAddDTO.id) {
                const exitInfo = exitDetailData.value.exitInfo
                terminateAddDTO.id = exitInfo?.terminateId
            }
            console.log('exitDetailDataexitDetailDataexitDetailData:', exitDetailData.value)

        console.log('构建的terminateAddDTO:', terminateAddDTO)

        // 2. 获取完整的出场结算数据
        let exitAddDTO: any = {}

        // 优先尝试从结算表单组件获取数据
        console.log('当前出场模式:', exitMode.value)
        console.log('exitHandlerRef.value:', !!exitHandlerRef.value)

        const settlementRef = exitMode.value === 'property-only'
            ? exitHandlerRef.value?.settlementFormRef
            : exitHandlerRef.value?.settlementFormRefAndProperty

        console.log('settlementRef:', !!settlementRef)
        console.log('settlementRef type:', typeof settlementRef?.getFormData)
        if (settlementRef) {
            console.log('settlementRef methods:', Object.getOwnPropertyNames(settlementRef))
        }

        if (settlementRef && typeof settlementRef.getFormData === 'function') {
            console.log('从结算表单组件获取数据')
            let getFormData = settlementRef.getFormData()
            let _exitAddDTO = exitHandlerRef.value.handleUpdateSettlementData(exitMode.value === 'property-only' ? 1 : 2, getFormData)

            exitAddDTO = _exitAddDTO
            // console.log('从结算表单获取的数据:', exitAddDTO)
            // console.log('exitCostList:', exitAddDTO?.exitCostList)
            // if (exitAddDTO) {
            //     exitAddDTO.isSubmit = false
            //     // 补充必要的基础字段
            //     exitAddDTO.id = exitAddDTO.id || exitDetailData.value?.id || ''
            //     exitAddDTO.contractId = exitAddDTO.contractId || contractId.value
            //     exitAddDTO.contractUnionId = exitAddDTO.contractUnionId || terminateAddDTO.unionId
            //     exitAddDTO.terminateId = exitAddDTO.terminateId || terminateAddDTO.id
            // }
        } else {
            console.log('使用基础数据构建exitAddDTO')
            // 使用exitDetailData构建完整的exitAddDTO
            const baseData = exitDetailData.value || {}
            console.log('baseData:', baseData)
            console.log('baseData.exitCostList:', baseData.exitCostList)
            exitAddDTO = {
                id: baseData.id || '',
                projectId: baseData.projectId || terminationData.value?.projectId || '',
                contractId: contractId.value,
                contractNo: baseData.contractNo || terminationData.value?.contractNo || '',
                contractUnionId: terminateAddDTO.unionId || '',
                terminateId: terminateAddDTO.id || '',
                refundId: baseData.refundId || '',
                customerId: baseData.customerId || terminationData.value?.customerId || '',
                customerName: baseData.customerName || terminationData.value?.customerName || '',
                processType: exitMode.value === 'property-only' ? 1 : 2,
                progressStatus: baseData.progressStatus || 0,
                isDiscount: baseData.isDiscount || false,
                discountAmount: baseData.discountAmount || null,
                discountReason: baseData.discountReason || '',
                finalAmount: baseData.finalAmount || 0,
                refundProcessType: baseData.refundProcessType || null,
                payeeName: baseData.payeeName || '',
                payeeAccount: baseData.payeeAccount || '',
                bankName: baseData.bankName || '',
                licenseStatus: baseData.licenseStatus || null,
                taxCertStatus: baseData.taxCertStatus || null,
                refundApplyType: baseData.refundApplyType || null,
                signType: baseData.signType || null,
                signAttachments: baseData.signAttachments || null,
                exitCostList: baseData.exitCostList || [],
                isSubmit: false,
                signTime: baseData.signTime || null,
                copyTime: baseData.copyTime || null,
                copyBy: baseData.copyBy || '',
                copyByName: baseData.copyByName || '',
                settleTime: baseData.settleTime || null,
                settleBy: baseData.settleBy || '',
                settleByName: baseData.settleByName || '',
                createByName: baseData.createByName || '',
                updateByName: baseData.updateByName || '',
                isDel: false,
                contractPurpose: baseData.contractPurpose || terminationData.value?.contractPurpose || null,
                terminateType: terminateAddDTO.terminateType,
                terminateDate: terminateAddDTO.terminateDate,
                terminateRoomName: baseData.terminateRoomName || null,
                terminateRoomCount: baseData.terminateRoomCount || null,
                engineeringCount: baseData.engineeringCount || null,
                financeCount: baseData.financeCount || null
            }
        }
        console.log('terminateAddDTO:', terminateAddDTO)
            if(!exitAddDTO.id) {
                const exitInfo = exitDetailData.value.exitInfo
                exitAddDTO.id = exitInfo?.id
            }
            exitAddDTO.signAttachments = exitAddDTO.signAttachments || null
            console.log('exitDetailDataexitDetailDataexitDetailData:', exitDetailData.value)

        console.log('构建的terminateAddDTO:', terminateAddDTO)
        console.log('构建的exitAddDTO:', exitAddDTO)

        // 3. 构建完整保存数据
        const saveData: ContractTerminateWithExitDTO = {
            terminateAddDTO,
            exitAddDTO
        }

        console.log('saveData', saveData)

        const res = await saveContractTerminateWithExit(saveData)
        if (res && res.code === 200) {
            Message.success('暂存成功')

            // 暂存成功后刷新第一步的退租申请数据
            await refreshTerminationData()
        }
    } catch (error) {
        console.error('暂存失败:', error)
        // Message.error('暂存失败')
    } finally {
        loading.value = false
    }
}

// 出场办理提交点击
const handleExitSubmitClick = async () => {
    try {
        loading.value = true

        // 1. 获取完整的退租数据
        let terminateAddDTO: any = {}

        // 优先使用保存的表单数据
        if (savedTerminateFormData.value) {
            console.log('使用保存的表单数据(提交)')
            terminateAddDTO = { ...savedTerminateFormData.value }
            terminateAddDTO.isSubmit = true // 提交
        } else if (terminationRef.value && typeof terminationRef.value.getFormData === 'function') {
            console.log('从表单组件获取退租数据(提交)')
            terminateAddDTO = terminationRef.value.getFormData()
            // if(!terminateAddDTO.id) {
            //     const terminateData = exitDetailData.value
            //     terminateAddDTO.id = terminateData?.id
            // }
            terminateAddDTO.isSubmit = true // 提交
        } else {
            // Fallback: 使用已保存的数据，确保必要字段不为空
            console.log('使用已保存数据构建退租数据(提交)')
            const terminateData = exitDetailData.value
            terminateAddDTO = {
                id: terminateData?.id,
                contractId: contractId.value,
                unionId: unionId.value || terminateData?.unionId || terminateData?.contractTerminateInfo?.unionId,
                terminateType: terminateData?.terminateType ?? 0, // 确保有默认值
                terminateDate: terminateData?.terminateDate || terminateData?.contract?.endDate || '',
                terminateReason: terminateData?.terminateReason || '1', // 默认到期退租
                otherReasonDesc: terminateData?.otherReasonDesc || '',
                hasOtherDeduction: terminateData?.hasOtherDeduction || false,
                otherDeductionDesc: terminateData?.otherDeductionDesc || '',
                terminateRemark: terminateData?.terminateRemark || '',
                terminateAttachments: terminateData?.terminateAttachments || '[]',
                bondReceivedAmount: terminateData?.bondReceivedAmount || 0,
                rentReceivedAmount: terminateData?.rentReceivedAmount || 0,
                rentOverdueAmount: terminateData?.rentOverdueAmount || 0,
                receivedPeriod: terminateData?.receivedPeriod || '',
                overduePeriod: terminateData?.overduePeriod || '',
                roomList: terminateData?.roomList || [],
                costList: terminateData?.costList || [],
                feeList: terminateData?.contract?.fees || [],
                isSubmit: true
            }
        }
        console.log('terminateAddDTO:', terminateAddDTO) 

        console.log('terminateAddDTO:', terminateAddDTO)
            if(!terminateAddDTO.id) {
                const exitInfo = exitDetailData.value.exitInfo
                terminateAddDTO.id = exitInfo?.terminateId
            }
            console.log('exitDetailDataexitDetailDataexitDetailData:', exitDetailData.value)

        console.log('构建的terminateAddDTO:', terminateAddDTO)

        // 2. 获取完整的出场结算数据
        let exitAddDTO: any = {}

        // 优先尝试从结算表单组件获取数据
        console.log('当前出场模式(提交):', exitMode.value)
        console.log('exitHandlerRef.value(提交):', !!exitHandlerRef.value)

        const settlementRef = exitMode.value === 'property-only'
            ? exitHandlerRef.value?.settlementFormRef
            : exitHandlerRef.value?.settlementFormRefAndProperty

        console.log('settlementRef(提交):', !!settlementRef)

        if (settlementRef && typeof settlementRef.getFormData === 'function') {
            console.log('从结算表单组件获取数据')
            let getFormData = settlementRef.getFormData()
            let _exitAddDTO = exitHandlerRef.value.handleUpdateSettlementData(exitMode.value === 'property-only' ? 1 : 2, getFormData)

            exitAddDTO = _exitAddDTO
            // console.log('从结算表单组件获取数据(提交)')
            // exitAddDTO = settlementRef.getFormData()
            // if (exitAddDTO) {
            //     exitAddDTO.isSubmit = true
            //     // 补充必要的基础字段
            //     exitAddDTO.id = exitAddDTO.id || exitDetailData.value?.id || ''
            //     exitAddDTO.contractId = exitAddDTO.contractId || contractId.value
            //     exitAddDTO.contractUnionId = exitAddDTO.contractUnionId || terminateAddDTO.unionId
            //     exitAddDTO.terminateId = exitAddDTO.terminateId || terminateAddDTO.id
            // }
        } else {
            console.log('使用基础数据构建exitAddDTO(提交)')
            // 使用exitDetailData构建完整的exitAddDTO
            const baseData = exitDetailData.value || {}
            exitAddDTO = {
                id: baseData.id || '',
                projectId: baseData.projectId || terminationData.value?.projectId || '',
                contractId: contractId.value,
                contractNo: baseData.contractNo || terminationData.value?.contractNo || '',
                contractUnionId: terminateAddDTO.unionId || '',
                terminateId: terminateAddDTO.id || '',
                refundId: baseData.refundId || '',
                customerId: baseData.customerId || terminationData.value?.customerId || '',
                customerName: baseData.customerName || terminationData.value?.customerName || '',
                processType: exitMode.value === 'property-only' ? 1 : 2,
                progressStatus: baseData.progressStatus || 0,
                isDiscount: baseData.isDiscount || false,
                discountAmount: baseData.discountAmount || null,
                discountReason: baseData.discountReason || '',
                finalAmount: baseData.finalAmount || 0,
                refundProcessType: baseData.refundProcessType || null,
                payeeName: baseData.payeeName || '',
                payeeAccount: baseData.payeeAccount || '',
                bankName: baseData.bankName || '',
                licenseStatus: baseData.licenseStatus || null,
                taxCertStatus: baseData.taxCertStatus || null,
                refundApplyType: baseData.refundApplyType || null,
                signType: baseData.signType || null,
                signAttachments: baseData.signAttachments || null,
                exitCostList: baseData.exitCostList || [],
                isSubmit: true,
                signTime: baseData.signTime || null,
                copyTime: baseData.copyTime || null,
                copyBy: baseData.copyBy || '',
                copyByName: baseData.copyByName || '',
                settleTime: baseData.settleTime || null,
                settleBy: baseData.settleBy || '',
                settleByName: baseData.settleByName || '',
                createByName: baseData.createByName || '',
                updateByName: baseData.updateByName || '',
                isDel: false,
                contractPurpose: baseData.contractPurpose || terminationData.value?.contractPurpose || null,
                terminateType: terminateAddDTO.terminateType,
                terminateDate: terminateAddDTO.terminateDate,
                terminateRoomName: baseData.terminateRoomName || null,
                terminateRoomCount: baseData.terminateRoomCount || null,
                engineeringCount: baseData.engineeringCount || null,
                financeCount: baseData.financeCount || null
            }
        }

        console.log('构建的exitAddDTO(提交):', exitAddDTO)

        console.log('terminateAddDTO:', terminateAddDTO)
            if(!exitAddDTO.id) {
                const exitInfo = exitDetailData.value.exitInfo
                exitAddDTO.id = exitInfo?.id
            }
            exitAddDTO.signAttachments = exitAddDTO.signAttachments || null
            console.log('exitDetailDataexitDetailDataexitDetailData:', exitDetailData.value)

        // 3. 构建完整提交数据
        const submitData: ContractTerminateWithExitDTO = {
            terminateAddDTO,
            exitAddDTO
        }

        const res = await saveContractTerminateWithExit(submitData)
        if (res && res.code === 200) {
            Message.success('提交成功')
            emit('refresh') // 触发刷新事件
            handleClose()
        }
    } catch (error) {
        console.error('提交失败:', error)
        // Message.error('提交失败')
    } finally {
        loading.value = false
    }
}

// 刷新退租申请数据
const refreshTerminationData = async () => {
    try {
        const res = await getContractTerminateExitDetail(savedTerminateFormData.value.id, true)
        exitData.value = res // 恢复使用完整的res数据
        if (exitHandlerRef.value && typeof exitHandlerRef.value.initData === 'function') {
            exitHandlerRef.value.initData()
        }
    } catch (error) {
        console.error('刷新退租数据失败:', error)
    }
}

// 处理出场数据刷新事件
const handleExitDataRefresh = async () => {
    console.log('出场数据已刷新，同步刷新第一步退租申请数据')
    // 当出场数据刷新时，也刷新第一步的退租申请数据
    await refreshTerminationData()
}

// 处理刷新列表事件
const handleRefreshList = () => {
    emit('refresh')
}

// 获取当前步骤数据
const getCurrentStepData = () => {
    if (currentStep.value === 1 && terminationRef.value) {
        return terminationRef.value.getFormData()
    }
    return null
}

// 验证当前步骤
const validateCurrentStep = async () => {
    if (currentStep.value === 1 && terminationRef.value) {
        return await terminationRef.value.validate()
    }
    return true
}

// 处理模式变更
const handleModeChange = (newMode: 'property-only' | 'property-and-settlement') => {
    exitMode.value = newMode
}

// 暴露方法给父组件
defineExpose({
    open,
    openExitModal,
    close: handleClose,
    handleSaveClick,
    handleSubmitClick,
    handleNextClick,
    handlePrev,
    getCurrentStepData,
    validateCurrentStep,
    // 暴露一些状态
    visible: readonly(visible),
    currentStep: readonly(currentStep),
    loading: readonly(loading),
    selectedMode: readonly(selectedMode)
})
</script>

<style scoped lang="less">
.contract-termination-main {
    width: 100%;
    height: calc(100vh - 120px);
    position: relative;
    display: flex;
    flex-direction: column;

    .contract-termination-main-header {
        width: 100%;
        height: 60px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        border-bottom: 1px solid #e5e6eb;
        background-color: #fff;

        .arco-steps {
            width: 50%;
        }
    }

    .contract-termination-main-content {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        background-color: #f7f8fa;
    }

    .footer {
        width: 100%;
        height: 60px;
        box-sizing: border-box;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 24px;
        background-color: #fff;
        border-top: 1px solid #e5e6eb;
        flex-shrink: 0;
        gap: 8px;
    }
}

// 抽屉样式优化
:deep(.arco-drawer) {
    .arco-drawer-header {
        border-bottom: 1px solid #e5e6eb;
    }

    .arco-drawer-body {
        padding: 0;
        background-color: #f7f8fa;
    }
}

// 步骤条样式优化
:deep(.arco-steps) {
    .arco-steps-item-title {
        font-weight: 500;
    }

    .arco-steps-item-process .arco-steps-item-title {
        color: #165dff;
        font-weight: 600;
    }
}

// 底部操作按钮样式
.drawer-footer-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
}
</style>
