<template>
  <a-modal
    v-model:visible="visible"
    :title="'创建房源--选择房源'"
    :width="700"
    @cancel="handleCancel"
  >
    <div>
      <!-- 固定在顶部的筛选表单 -->
      <a-form label-align="right" :model="form" layout="inline" style="margin-bottom: 16px;">
        <a-col :span="8">
          <a-form-item label="用途" field="roomUsage">
            <a-tree-select
              v-model="form.roomUsage"
              :data="propertyTypeOptions"
              placeholder="请选择用途"
              allow-clear
              style="width: 180px"
              :field-names="{
                key: 'dictValue',
                title: 'dictLabel',
                children: 'childList'
              }"
              @change="handleUsageChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="是否定价" field="isPriced">
            <a-select v-model="form.isPriced" placeholder="请选择" allow-clear style="width: 180px" @change="handlePricedChange">
              <a-option :value="0">否</a-option>
              <a-option :value="1">是</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="房源名称" field="roomName">
            <a-input v-model="form.roomName" style="width: 180px;" placeholder="请输入" @input="handleRoomNameInputDebounce" />
          </a-form-item>
        </a-col>
      </a-form>

      <!-- 可滚动的房源选择区域 -->
      <div class="room-selection-container">
        <div v-if="loading" class="loading-container">
          <a-spin />
          <div class="loading-text">加载房源数据中...</div>
        </div>
        
        <div v-else class="room-selection">
          <!-- 全选 -->
          <div class="global-select-all-container" v-if="buildings.length > 0">
            <a-checkbox v-model="allChecked" @change="toggleAll">全选</a-checkbox>
          </div>

          <!-- 没有数据时显示空状态 -->
          <div v-if="buildings.length === 0" class="empty-data">
            <a-empty description="暂无房间数据" />
          </div>
          
          <!-- 楼栋及房源 -->
          <div v-for="building in buildings" :key="building.buildingId" class="building-container">
            <div class="building-header">
              <a-checkbox v-model="building.checked" @change="() => toggleBuilding(building)">
                {{ building.buildingName }}
              </a-checkbox>
            </div>
            
            <div class="room-table">
              <!-- 表头 -->
              <div class="table-header">
                <div class="floor-header">楼层</div>
                <div class="room-header">房间</div>
              </div>
              
              <!-- 表格内容 -->
              <div class="table-content">
                <template v-if="building.children && building.children.length">
                  <div v-for="floor in building.children" :key="floor.floorId" class="table-row">
                    <div class="floor-cell">
                      <a-checkbox v-model="floor.checked" @change="() => toggleFloor(building, floor)">
                        {{ floor.floorName }}
                      </a-checkbox>
                    </div>
                    <div class="room-cell">
                      <template v-if="floor.children && floor.children.length">
                        <a-checkbox
                          v-for="room in floor.children"
                          :key="room.roomId"
                          v-model="room.checked"
                          @change="() => toggleRoom(building, floor, room)"
                          class="room-checkbox"
                        >{{ room.roomName }}</a-checkbox>
                      </template>
                      <template v-else>
                        <div class="empty-room-tip">暂无房间</div>
                      </template>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="empty-floors">该楼栋暂无楼层信息</div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div style="text-align: center;">
        <a-button @click="handlePrev">上一步</a-button>
        <a-button type="primary" :disabled="selectedRoomIds.length === 0" @click="handleNext" style="margin-left: 16px;">
          {{ callMode === 'drawer' ? '保存' : '下一步' }}
        </a-button>
        <a-button style="margin-left: 16px;" @click="handleCancel">取消</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, onUnmounted, onMounted } from 'vue'
import { getPricingRoomTree, PricingRoomTreeVo, PricingRoomTreeDTO } from '@/api/projectPricing'
import { useDictSync } from '@/utils/dict'
import { Message } from '@arco-design/web-vue'

// 字典数据接口
interface DictData {
  dictCode: string
  dictLabel: string
  dictValue: string
  dictSort: number
  parentCode?: string
  childList?: DictData[]
}

// 扩展 PricingRoomTreeVo 接口，添加 checked 属性
interface ProjectRoomTreeNodeWithCheck extends PricingRoomTreeVo {
  checked: boolean
  children?: ProjectRoomTreeNodeWithCheck[]
}



const visible = ref(false)
const emit = defineEmits(['prev', 'next', 'cancel'])
const loading = ref(false)
const propertyTypeOptions = ref<DictData[]>([])

// 定义防抖定时器和延迟时间
let debounceTimer: number | null = null
const DEBOUNCE_DELAY = 300 // 300ms

// 防抖处理函数
const debounce = (fn: Function, delay: number) => {
  return (...args: any[]) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
    debounceTimer = window.setTimeout(() => {
      fn(...args)
      debounceTimer = null
    }, delay)
  }
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
})

// 加载多经用途字典数据
const loadPropertyTypeDicts = async () => {
  try {
    const dictData = await useDictSync('diversification_purpose')
    if (dictData.diversification_purpose) {
      // 处理树形结构数据
      const dictList = dictData.diversification_purpose as DictData[]
      // 组织成树形结构
      const treeData = dictList.filter(item => !item.parentCode)
      treeData.forEach(parent => {
        parent.childList = dictList.filter(child => child.parentCode === parent.dictCode)
      })
      propertyTypeOptions.value = treeData
    } else {
      propertyTypeOptions.value = []
    }
  } catch (error) {
    console.error('获取多经用途字典数据失败:', error)
    propertyTypeOptions.value = []
  }
}

// 组件挂载时加载字典数据
onMounted(() => {
  loadPropertyTypeDicts()
})

const form = reactive({
  roomUsage: '',
  isPriced: 0 as number | undefined, // 默认为否
  roomName: '',
  projectId: '',
  buildingIds: [] as string[]
})

// 地块信息
const plotsInfo = ref<{
  plots: Array<{id: string, name: string}>,
  buildingToPlotMap: Record<string, {plotId: string, plotName: string}>
}>({
  plots: [],
  buildingToPlotMap: {}
})

// 调用模式：'list' 表示从列表调用，'drawer' 表示从抽屉调用
const callMode = ref<'list' | 'drawer'>('list')

const allChecked = ref(false)
const buildings = ref<ProjectRoomTreeNodeWithCheck[]>([])

// 获取选中的房间ID列表
const selectedRoomIds = computed(() => {
  const result: string[] = []
  
  // 遍历所有楼栋
  buildings.value.forEach(building => {
    // 遍历楼层
    if (building.children) {
      building.children.forEach(floor => {
        // 遍历房间
        if (floor.children) {
          floor.children.forEach(room => {
            if (room.checked) {
              result.push(room.roomId || '')
            }
          })
        }
      })
    }
  })
  
  return result
})

// 获取选中的房间完整信息
const selectedRooms = computed(() => {
  const result: ProjectRoomTreeNodeWithCheck[] = []

  // 遍历所有楼栋
  buildings.value.forEach(building => {
    // 遍历楼层
    if (building.children) {
      building.children.forEach(floor => {
        // 遍历房间
        if (floor.children) {
          floor.children.forEach(room => {
            if (room.checked) {
              // 获取楼栋对应的地块信息
              const plotInfo = plotsInfo.value.buildingToPlotMap[building.buildingId || ''] || {}

              // 保存完整的房间信息，包括楼栋、楼层和地块信息
              const roomInfo = {
                ...room,
                id: room.roomId,
                name: room.roomName,
                buildingId: building.buildingId,
                buildingName: building.buildingName,
                floorId: floor.floorId,
                floorName: floor.floorName,
                plotId: plotInfo.plotId || room.parcelId || '',
                plotName: plotInfo.plotName || room.parcelName || ''
              }
              result.push(roomInfo)
            }
          })
        }
      })
    }
  })

  return result
})

// 加载房源树数据
const loadRoomTree = async () => {
  if (!form.projectId || !form.buildingIds.length) return
  
  loading.value = true
  try {
    const params: PricingRoomTreeDTO = {
      projectId: form.projectId,
      buildingIds: form.buildingIds,
      roomUsage: form.roomUsage || undefined, // 空字符串时不传
      isPriced: form.isPriced,
      roomName: form.roomName || undefined
    }

    const { data } = await getPricingRoomTree(params)
    if (data && Array.isArray(data)) {
      // 为树节点添加选中状态
      const processedData = processTreeData(data)
      buildings.value = processedData
    } else {
      buildings.value = []
    }
  } catch (error) {
    console.error('Failed to load room tree:', error)
    buildings.value = []
  } finally {
    loading.value = false
  }
}

// 处理树数据，添加选中状态
const processTreeData = (treeData: PricingRoomTreeVo[]): ProjectRoomTreeNodeWithCheck[] => {
  return treeData.map(building => {
    // 创建楼栋节点
    const buildingWithChecked: ProjectRoomTreeNodeWithCheck = {
      ...building,
      checked: false,
      children: [] // 初始化为空数组
    }
    
    // 处理楼栋的子节点（楼层）
    if (building.children && building.children.length) {
      buildingWithChecked.children = building.children.map(floor => {
        // 创建楼层节点
        const floorWithChecked: ProjectRoomTreeNodeWithCheck = {
          ...floor,
          checked: false,
          children: [] // 初始化为空数组
        }
        
        // 处理楼层的子节点（房间）
        if (floor.children && floor.children.length) {
          floorWithChecked.children = floor.children.map(room => {
            // 创建房间节点
            return {
              ...room,
              checked: false
            } as ProjectRoomTreeNodeWithCheck
          })
        }
        
        return floorWithChecked
      })
    }
    
    return buildingWithChecked
  })
}

// 处理用途变更
const handleUsageChange = () => {
  loadRoomTree()
}

// 处理是否定价变更
const handlePricedChange = () => {
  loadRoomTree()
}

// 房源名称输入处理（带防抖）
const handleRoomNameInputDebounce = debounce(() => {
  loadRoomTree()
}, DEBOUNCE_DELAY)

// 显示模态框
const show = (init: any, mode: 'list' | 'drawer' = 'list') => {
  if (!init) {
    console.error('初始化数据为空')
    return
  }

  const buildingKeys = init.buildingKeys || []
  if (!Array.isArray(buildingKeys) || buildingKeys.length === 0) {
    console.error('未选择任何楼栋')
    return
  }

  // 设置调用模式
  callMode.value = mode

  // 重置表单和选中状态
  form.roomUsage = '' // 默认为全部（不传值）
  form.isPriced = 0 // 默认为否
  form.roomName = ''
  form.projectId = init.projectId || ''
  form.buildingIds = buildingKeys
  allChecked.value = false

  // 存储地块信息
  if (init.plotsInfo) {
    plotsInfo.value = init.plotsInfo
  }

  // 加载房源数据
  loadRoomTree()

  visible.value = true
}

// 全选/取消全选
function toggleAll() {
  // 遍历所有楼栋，设置选中状态
  buildings.value.forEach(building => {
    building.checked = allChecked.value
    
    // 遍历楼层
    if (building.children) {
      building.children.forEach(floor => {
        floor.checked = allChecked.value
        
        // 遍历房间
        if (floor.children) {
          floor.children.forEach(room => {
            room.checked = allChecked.value
          })
        }
      })
    }
  })
}

// 选中/取消选中楼栋
function toggleBuilding(building: ProjectRoomTreeNodeWithCheck) {
  // 遍历楼层，设置选中状态
  if (building.children) {
    building.children.forEach((floor) => {
      floor.checked = building.checked
      
      // 遍历房间
      if (floor.children) {
        floor.children.forEach((room) => {
          room.checked = building.checked
        })
      }
    })
  }
  
  updateAllChecked()
}

// 选中/取消选中楼层
function toggleFloor(building: ProjectRoomTreeNodeWithCheck, floor: ProjectRoomTreeNodeWithCheck) {
  // 设置该楼层下所有房间的选中状态
  if (floor.children) {
    floor.children.forEach(room => {
      room.checked = floor.checked
    })
  }
  
  // 检查楼栋内所有楼层是否都被选中，更新楼栋状态
  updateBuildingCheckedStatus(building)
  
  // 更新全选状态
  updateAllChecked()
}

// 选中/取消选中房间
function toggleRoom(building: ProjectRoomTreeNodeWithCheck, floor: ProjectRoomTreeNodeWithCheck, _room: ProjectRoomTreeNodeWithCheck) {
  // 检查楼层内所有房间是否都被选中
  updateFloorCheckedStatus(floor)
  
  // 检查楼栋内所有楼层是否都被选中
  updateBuildingCheckedStatus(building)
  
  // 更新全选状态
  updateAllChecked()
}

// 更新楼层选中状态
function updateFloorCheckedStatus(floor: ProjectRoomTreeNodeWithCheck) {
  if (!floor.children || floor.children.length === 0) {
    floor.checked = false
    return
  }
  
  floor.checked = floor.children.every(room => room.checked)
}

// 更新楼栋选中状态
function updateBuildingCheckedStatus(building: ProjectRoomTreeNodeWithCheck) {
  if (!building.children || building.children.length === 0) {
    building.checked = false
    return
  }
  
  building.checked = building.children.every(floor => floor.checked)
}

// 更新全选状态
function updateAllChecked() {
  if (buildings.value.length === 0) {
    allChecked.value = false
    return
  }
  
  // 确保返回布尔值
  allChecked.value = buildings.value.every(building => !!building.checked)
}

const handleNext = () => {
  if (selectedRoomIds.value.length === 0) {
    Message.warning('请至少选择一个房间')
    return
  }
  
  emit('next', {
    selectedRooms: selectedRooms.value, // 传递完整的房间信息
    selectedRoomIds: selectedRoomIds.value, // 保留原有的ID列表，以保持兼容性
    projectId: form.projectId,
    buildingIds: form.buildingIds
  })
  visible.value = false
}

const handlePrev = () => {
  // 收集当前选择的数据，传递回上一步
  const prevStepData = {
    projectId: form.projectId,
    // 确保与SelectBuildingModal期望的字段完全一致
    buildingName: form.roomName, // 将房源名称作为楼栋名称传回，如果有筛选的话
    checkedKeys: form.buildingIds, // 已选择的楼栋ID，这是树组件需要的
    buildingKeys: form.buildingIds, // 兼容性字段
    selectedBuildings: buildings.value.map(b => ({
      id: b.buildingId || '',
      name: b.buildingName || ''
    })).filter(b => b.id && form.buildingIds.includes(b.id)) // 包含已选楼栋的详细信息
  }
  
  console.log('从房间选择返回楼栋选择，传递数据:', prevStepData)
  

  // 发送prev事件，并传递当前选择状态
  emit('prev', prevStepData)
  // 关闭当前弹窗
  visible.value = false
}

const handleCancel = () => {
  setTimeout(() => {
    emit('cancel')
  }, 300);
  visible.value = false
}

defineExpose({ show })
</script>

<style scoped>
.room-selection-container {
  max-height: 50vh;
  overflow-y: auto;
  padding-right: 8px;
  border: 1px solid #f2f3f5;
  border-radius: 4px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}
.loading-text {
  margin-top: 16px;
  color: #666;
}

/* 房源选择样式 */
.room-selection {
  .global-select-all-container {
    padding: 12px 16px;
    background-color: #f0f8ff;
    border: 1px solid var(--color-primary-3);
    border-radius: 4px;
    margin-bottom: 16px;
  }

  .building-container {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .building-header {
    font-weight: bold;
    font-size: 16px;
    color: rgb(var(--primary-6));
    padding: 8px;
    background-color: #f5f7fa;
    border-radius: 4px 4px 0 0;
    border: 1px solid var(--color-neutral-3);
  }

  .room-table {
    border: 1px solid var(--color-neutral-3);
    border-top: none;
    border-radius: 0 0 4px 4px;
    overflow: hidden;

    .table-header {
      display: flex;
      background-color: #e8f3ff;
      border-bottom: 1px solid var(--color-neutral-3);

      .floor-header,
      .room-header {
        padding: 8px 16px;
        font-weight: bold;
      }

      .floor-header {
        width: 100px;
        border-right: 1px solid var(--color-neutral-3);
      }

      .room-header {
        flex: 1;
      }
    }

    .table-content {
      max-height: 300px;
      overflow-y: auto;

      .table-row {
        display: flex;
        border-bottom: 1px solid var(--color-neutral-3);

        &:last-child {
          border-bottom: none;
        }

        .floor-cell,
        .room-cell {
          padding: 8px 16px;
        }

        .floor-cell {
          width: 100px;
          border-right: 1px solid var(--color-neutral-3);
        }

        .room-cell {
          flex: 1;
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
          gap: 8px;
        }
      }
    }
  }

  .empty-data {
    padding: 16px;
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    margin-top: 16px;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .empty-floors {
    padding: 16px;
    color: #999;
    text-align: center;
  }

  .empty-room-tip {
    color: #999;
    font-size: 14px;
  }

  .room-checkbox {
    margin-right: 8px;
  }
}
</style>