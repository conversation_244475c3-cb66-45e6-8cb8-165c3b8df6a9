<template>
    <div class="container">
        <div class="content">
            <!-- 顶部状态切换 -->
            <a-tabs v-model:activeKey="activeType" size="large" hide-content @change="handleChangeType"
                style="margin-bottom: 16px;">
                <template #extra>
                    <a-space>
                        <a-button v-permission="['rent:booking:add']" type="primary" @click="handleAdd" style="margin-right: 16px;">
                            <template #icon>
                                <icon-plus />
                            </template>
                            新增
                        </a-button>
                    </a-space>
                </template>
                <a-tab-pane v-for="item in typeOptions" :key="item.value" :title="item.label" />
            </a-tabs>
            <!-- 搜索区域 -->
            <a-card class="general-card">
                <a-row>
                    <a-col :flex="1">
                        <a-form :model="formModel" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }"
                        label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="projectId" label="项目">
                                    <ProjectTreeSelect v-model="formModel.projectId"
                                        @change="handleProjectChange" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="customerName" label="客户名称">
                                    <a-input v-model="formModel.customerName" placeholder="请输入" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType === '3'">
                                <a-form-item field="contractNo" label="合同编号" >
                                    <a-input v-model="formModel.contractNo" placeholder="请输入" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType === '3'">
                                <a-form-item field="signDate" label="合同签订日期">
                                    <a-range-picker v-model="signDateRange" style="width: 100%"
                                        @change="handleSignDateChange" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType === '3'">
                                <a-form-item field="contractLeaseUnit" label="合同租赁单元">
                                    <a-input v-model="formModel.contractLeaseUnit" placeholder="请输入" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType === '3'">
                                <a-form-item field="lesseeName" label="承租人名称">
                                    <a-input v-model="formModel.lesseeName" placeholder="请输入" allow-clear />
                                </a-form-item>
                            </a-col>


                            <a-col :span="8" v-if="activeType !== '3'">
                                <a-form-item field="roomName" label="意向房源" >
                                    <a-input v-model="formModel.roomName" placeholder="请输入意向房源" allow-clear />
                                    <!-- <RoomTreeSelect v-model="formModel.roomName" value-type="name" placeholder="请选择" :type="0" allow-clear /> -->
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType === '4'">
                                <a-form-item field="cancelReason" label="作废原因">
                                    <a-select v-model="formModel.cancelReason" placeholder="请选择" allow-clear>
                                        <!-- 退定退款、退定不退款、未收款取消预定、转签合同作废 -->
                                         <!--  0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废| -->
                                        <a-option value="0">退定退款</a-option>
                                        <a-option value="1">退定不退款</a-option>
                                        <a-option value="2">未收款取消预定</a-option>
                                        <a-option value="3">转签合同作废</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType === '4'">
                                <a-form-item field="cancelByName" label="作废人">
                                    <a-input v-model="formModel.cancelByName" placeholder="请输入" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType === '4'">
                                <a-form-item field="cancelTime" label="作废日期">
                                    <a-range-picker v-model="cancelTimeRange" style="width: 100%"
                                        @change="handleCancelTimeChange" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType === '2'">
                                <a-form-item field="receivedDate" label="实收日期">
                                    <a-range-picker v-model="receivedDateRange" style="width: 100%"
                                        @change="handleReceivedDateChange" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType !== '4'">
                                <a-form-item field="createByName" label="创建人">
                                    <a-input v-model="formModel.createByName" placeholder="请输入" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8" v-if="activeType !== '4'">
                                <a-form-item field="createTime" label="创建日期">
                                    <a-range-picker v-model="createTimeRange" style="width: 100%"
                                        @change="handleCreateTimeChange" />
                                </a-form-item>
                            </a-col>

                            <a-col :span="8" v-if="activeType === '' || activeType === '1'">
                                <a-form-item field="status" label="状态">
                                    <a-select v-model="formModel.status" placeholder="请选择" allow-clear>
                                        <a-option :value="0">草稿</a-option>
                                        <a-option :value="1">待收费</a-option>
                                        <a-option :value="2">已生效</a-option>
                                        <a-option :value="3">已转签</a-option>
                                        <a-option :value="4">已作废</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin-top: 0" />
            <!-- 表格区域 -->
            <a-table ref="tableRef" row-key="id" :loading="loading" :pagination="pagination" :columns="columns" :scroll="{ x: 1 }"
                :data="tableData" :bordered="{ cell: true }" @page-change="onPageChange"
                @page-size-change="onPageSizeChange">
                <template #index="{ rowIndex }">
                    {{
                        rowIndex +
                        1 +
                        (pagination.current - 1) * pagination.pageSize
                    }}
                </template>
                <template #status="{ record }">
                    <a-tag :color="getStatusColor(record.status)">
                        {{
                            record.status === 0 ? '草稿' :
                                record.status === 1 ? '待收费' :
                                    record.status === 2 ? '已生效' :
                                        record.status === 3 ? '已转签' :
                                            record.status === 4 ? '已作废' : '未知'
                        }}
                    </a-tag>
                </template>
                <template #isRefundable="{ record }">
                    <a-tag :color="record.isRefundable === 1 ? 'green' : 'red'">
                        {{ record.isRefundable === 1 ? '是' : '否' }}
                    </a-tag>
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <!-- 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废 -->
                        <!-- 主要操作：始终显示的重要操作 -->
                        <!-- 查看定单 - 所有状态都可查看 -->
                        <a-button v-permission="['rent:booking:detail']" type="text" size="mini" @click="handleView(record)"
                            v-if="record.status === 0 || record.status === 1 || record.status === 2 || record.status === 3 || record.status === 4">
                            查看定单
                        </a-button>
                        
                        <!-- 编辑 - 草稿状态 -->
                        <a-button v-permission="['rent:booking:add']" type="text" size="mini" @click="handleEdit(record)"
                            v-if="record.status === 0" style="width: 60px;">
                            编辑
                        </a-button>
                        
                        <!-- 收款码 - 待收费状态 -->
                        <a-button v-permission="['rent:booking:paymentCode']" type="text" size="mini" @click="handleCollect(record)"
                            v-if="record.status === 1">
                            收款码
                        </a-button>
                        
                        <!-- 转签约 - 已生效状态的重要操作 -->
                        <a-button v-permission="['rent:booking:contract']" type="text" size="mini" @click="handleContract(record)"
                            v-if="record.status === 2">
                            转签约
                        </a-button>
                        
                        <!-- 查看合同 - 已转签状态 -->
                        <a-button v-permission="['rent:booking:contract:query']" type="text" size="mini" @click="handleViewContract(record)"
                            v-if="record.status === 3">
                            查看合同
                        </a-button>

                        <!-- 打印定单 - 重要操作，在主要位置显示 -->
                        <!-- <a-button type="text" size="mini" @click="handlePrint(record)"
                            v-if="record.status === 2 || record.status === 3">
                            打印定单
                        </a-button> -->

                        <!-- { key: 'refundApply', label: '退款申请' }, -->

                        <a-button v-permission="['rent:booking:refund:apply']" type="text" size="mini" @click="handleRefundApply(record)"
                            v-if="(record.status === 4 && record.isRefund === 1) && (record.cancelReason === 0)">
                            退款申请
                        </a-button>

                        
                        <!-- 更多操作下拉菜单 -->
                        <a-dropdown @select="handleMoreAction($event, record)" 
                            v-if="getMoreActions(record).length > 0">
                            <a-button type="text" size="mini" color="#333">
                                更多
                                <icon-down />
                            </a-button>
                            <template #content>
                                <a-doption 
                                    v-for="action in getMoreActions(record)" 
                                    :key="action.key"
                                    :value="action.key"
                                    :class="action.danger ? 'danger-action' : ''">
                                    {{ action.label }}
                                </a-doption>
                            </template>
                        </a-dropdown>
                    </a-space>
                </template>
            </a-table>
            </a-card>
            <!-- 新增定单抽屉 -->
            <add-form ref="formRef" @success="onSuccess" />
            <!-- 退定单抽屉 -->
            <refund-form ref="refundFormRef" @success="onSuccess" />
            <!-- 作废弹窗 -->
            <a-modal :visible="voidModalVisible" title="确认作废" @cancel="handleVoidCancel" @ok="handleVoidConfirm">
                <div>
                    <p>确定要作废该订单吗？作废后无法恢复。</p>
                </div>
            </a-modal>
            <!-- 退款申请抽屉 -->
            <orderRefundApplyForm ref="orderRefundApplyFormRef" @success="onSuccess" />

            <!-- 订单记账组件 -->
            <OrderAccountModal ref="orderAccountModalRef" :data="currentOrderData" 
                @cancel="handleAccountCancel" @save="handleAccountSave" />

            <!-- 收款码弹窗 -->
            <a-modal v-model:visible="collectCodeVisible" title="定单收款码" :footer="false" @cancel="handleCollectCancel" :width="500">
                <div class="collect-code-container">
                    <div class="collect-code-header">
                        <div class="title">扫码支付定金</div>
                        <div class="order-info">
                            <div class="info-item">
                                <span class="label">项目名称：</span>
                                <span class="value">{{ currentRecord?.projectName }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">意向房源：</span>
                                <span class="value">{{ currentRecord?.roomName || currentRecord?.intentRoom }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">客户名称：</span>
                                <span class="value">{{ currentRecord?.customerName }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">支付金额：</span>
                                <span class="value amount">¥{{ formatAmount(currentRecord?.depositAmount || currentRecord?.bookingAmount) }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="qrcode-display">
                        <QRCode
                            :value="currentRecord?.paymentUrl || ''"
                            :size="200"
                            :show-placeholder="true"
                            :show-download="!!currentRecord?.paymentUrl"
                            placeholder-text="暂无收款码"
                            @generated="handleQRCodeGenerated"
                            @error="handleQRCodeError"
                        />
                    </div>
                    
                    <!-- 支付链接信息 -->
                    <!-- <div v-if="currentRecord?.paymentUrl" class="payment-link">
                        <a-input 
                            :model-value="currentRecord.paymentUrl" 
                            readonly 
                            placeholder="支付链接"
                            size="small"
                        >
                            <template #append>
                                <a-button @click="copyPaymentUrl" size="small">
                                    复制链接
                                </a-button>
                            </template>
                        </a-input>
                    </div> -->
                </div>
            </a-modal>

            <!-- 订单审批弹窗 -->
            <a-modal v-model:visible="approveModalVisible" title="订单审批" @cancel="handleApproveCancel">
                <template #footer>
                    <a-space>
                        <a-button @click="handleApproveCancel">取消</a-button>
                        <a-button type="primary" status="danger" @click="handleRejectConfirm">拒绝</a-button>
                        <a-button type="primary" @click="handleApproveConfirm">通过</a-button>
                    </a-space>
                </template>
                <a-form :model="approveForm" ref="approveFormRef">
                    <a-form-item field="reason" label="审批意见">
                        <a-textarea v-model="approveForm.reason" placeholder="请输入审批意见（拒绝时必填）"
                            :auto-size="{ minRows: 3, maxRows: 5 }" />
                    </a-form-item>
                </a-form>
            </a-modal>

            <!-- 拒绝理由弹窗 -->
            <a-modal v-model:visible="rejectModalVisible" title="拒绝原因" @cancel="handleRejectCancel"
                @ok="handleRejectSubmit">
                <a-form :model="rejectForm" ref="rejectFormRef">
                    <a-form-item field="reason" label="拒绝原因" :rules="[{ required: true, message: '请输入拒绝原因' }]">
                        <a-textarea v-model="rejectForm.reason" placeholder="请输入拒绝原因"
                            :auto-size="{ minRows: 3, maxRows: 5 }" />
                    </a-form-item>
                </a-form>
            </a-modal>

            <!-- 合同新增组件 -->
            <addContractDrawer ref="contractRef" @submit="handleContractSubmit" />
            
            <!-- 合同详情组件 -->
            <contractDetailDrawer ref="contractDetailRef" @submit="handleContractDetailUpdate" />

            <!-- 合同类型选择弹窗 -->
            <a-modal 
                v-model:visible="contractTypeModalVisible" 
                title="选择合同类型" 
                width="400px"
                @cancel="handleContractTypeCancel"
                :footer="false">
                <div class="contract-type-selection">
                    <p class="selection-tip">请选择要创建的合同类型：</p>
                    <div class="contract-type-options">
                        <div 
                            v-for="option in contractTypeOptions" 
                            :key="option.value"
                            class="contract-type-option"
                            @click="handleContractTypeConfirm(option.value)">
                            <div class="option-content">
                                <div class="option-icon">
                                    <icon-file />
                                </div>
                                <div class="option-text">
                                    <span class="option-label">{{ option.label }}</span>
                                </div>
                                <div class="option-arrow">
                                    <icon-right />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </a-modal>

        </div>
        <WordViewer
            v-model="previewVisible"
            :file-url="previewFileUrl"
            :title="previewTitle"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, getCurrentInstance } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconSearch, IconRefresh, IconPlus, IconStar, IconDelete, IconFile, IconRight, IconDown } from '@arco-design/web-vue/es/icon'
import cloneDeep from 'lodash/cloneDeep'
// import { useRouter } from 'vue-router'
import addForm from './components/addForm.vue'
import refundForm from './components/refundForm.vue'
import orderRefundApplyForm from './components/orderRefundApplyForm.vue'
import CollectCodeModal from '@/components/collectCodeModal/index.vue'
import QRCode from '@/components/QRCode/index.vue'
import RoomTreeSelect from '@/components/roomTreeSelect/index.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import OrderAccountModal from './components/orderAccountModal.vue'
import addContractDrawer from '@/views/contract/addContractDrawer.vue'
import contractDetailDrawer from '@/views/contract/contractDetailDrawer.vue'
import {
    getOrderList,
    deleteOrder,
    batchDeleteOrder,
    invalidOrder,
    getOrderPaymentCode,
    getOrderStatusStatistics,
    changeOrderStatus,
    approveOrder,
    rejectOrder,
    getOrderDetail,
    printBooking,
    type BookingPrintDto,
    type EnclosureInfo
} from '@/api/orderManagement'
import { getFinancialRefundDetail } from '@/api/refundManage'
import { getRoomDetail } from '@/api/room'

const { proxy } = getCurrentInstance() as any
const appBaseUrl = proxy.$appBaseUrl

// 日期范围选择器
const signDateRange = ref<[Date | null, Date | null]>([null, null])       // 合同签订日期
const cancelTimeRange = ref<[Date | null, Date | null]>([null, null])     // 作废日期
const receivedDateRange = ref<[Date | null, Date | null]>([null, null])   // 实收日期
const createTimeRange = ref<[Date | null, Date | null]>([null, null])     // 创建日期


// 处理合同签订日期变化
const handleSignDateChange = (dates: [Date | null, Date | null]) => {
    if (dates && dates.length === 2) {
        const [start, end] = dates
        formModel.signDateStart = !start ? '' : String(start)
        formModel.signDateEnd = !end ? '' : String(end)
    } else {
        formModel.signDateStart = ''
        formModel.signDateEnd = ''
    }
}

// 处理作废日期变化
const handleCancelTimeChange = (dates: [Date | null, Date | null]) => {
    if (dates && dates.length === 2) {
        const [start, end] = dates
        formModel.cancelTimeStart = !start ? '' : String(start)
        formModel.cancelTimeEnd = !end ? '' : String(end)
    } else {
        formModel.cancelTimeStart = ''
        formModel.cancelTimeEnd = ''
    }
}

// 处理实收日期变化
const handleReceivedDateChange = (dates: [Date | null, Date | null]) => {
    if (dates && dates.length === 2) {
        const [start, end] = dates
        formModel.actualReceiveTimeStart = !start ? '' : String(start)
        formModel.actualReceiveTimeEnd = !end ? '' : String(end)
    } else {
        formModel.actualReceiveTimeStart = ''
        formModel.actualReceiveTimeEnd = ''
    }
}

// 处理创建日期变化
const handleCreateTimeChange = (dates: [Date | null, Date | null]) => {
    if (dates && dates.length === 2) {
        const [start, end] = dates
        formModel.createTimeStart = !start ? '' : String(start)
        formModel.createTimeEnd = !end ? '' : String(end)
    } else {
        formModel.createTimeStart = ''
        formModel.createTimeEnd = ''
    }
}

// 视图类型 - 默认设置为"待生效"
const activeType = ref('1')

// 视图类型选项
const typeOptions = [
    { label: '全部', field: 'searchType', value: '', color: 'blue' },
    { label: '待生效', field: 'searchType', value: '1', color: 'orange' },
    { label: '生效中', field: 'searchType', value: '2', color: 'green' },
    { label: '已转签', field: 'searchType', value: '3', color: 'red' },
    { label: '已作废', field: 'searchType', value: '4', color: 'gray' }
]

// 查询表单数据
const formModel = reactive({
    projectId: '', // 项目id
    searchType: '1', // 页签类型:1-待生效 2生效中 3已转签 4已作废 - 默认设置为待生效
    customerName: '', // 客户名称
    roomName: '', // 意向房源
    status: '', // 状态
    createTimeStart: '', // 创建日期开始
    createTimeEnd: '', // 创建日期结束
    createByName: '', // 创建人姓名
    actualReceiveTimeStart: '', // 实收日期开始
    actualReceiveTimeEnd: '', // 实收日期结束
    contractNo: '', // 合同编号
    signDateStart: '', // 合同签订日期开始
    signDateEnd: '', // 合同签订日期结束
    contractLeaseUnit: '', // 合同租赁单元
    lesseeName: '', // 承租人名称
    cancelReason: '', // 作废原因
    cancelByName: '', // 作废人姓名
    cancelTimeStart: '', // 作废日期开始
    cancelTimeEnd: '' // 作废日期结束
})

const tableRef = ref()
// 表格配置
let columns = ref<any[]>([
    // { title: '序号', dataIndex: 'index', slotName: 'index', width: 80, align: 'center', ellipsis: true, tooltip: true },
    // { title: '客户', dataIndex: 'customerName', width: 120, align: 'center', ellipsis: true, tooltip: true },
    // { title: '意向房源', dataIndex: 'roomName', width: 160, align: 'center', ellipsis: true, tooltip: true },
    // // { title: '意向物业类型', dataIndex: 'propertyType', width: 120, ellipsis: true, tooltip: true },
    // { title: '定金应收金额', dataIndex: 'bookingAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
    // // { title: '定金实收金额', dataIndex: 'depositAmount', width: 120 },
    // // { title: '定单金额', dataIndex: 'bookingAmount', width: 120 },
    // { title: '应收日期', dataIndex: 'receivableDate', width: 120, align: 'center', ellipsis: true, tooltip: true },
    // // { title: '预计签约日期', dataIndex: 'expectSignDate', width: 120 },
    // { title: '是否可退', dataIndex: 'isRefundable', width: 90, slotName: 'isRefundable', ellipsis: true, tooltip: true },
    // { title: '状态', dataIndex: 'status', width: 100, slotName: 'status', ellipsis: true, tooltip: true },
    // { title: '创建人', dataIndex: 'createByName', width: 120, align: 'center', ellipsis: true, tooltip: true },
    // { title: '创建日期', dataIndex: 'createTime', width: 120, align: 'center', ellipsis: true, tooltip: true },
    // { title: '单号', dataIndex: 'bookingNo', width: 190, align: 'center', ellipsis: true, tooltip: true },

    // {
    //     title: '操作',
    //     width: 220,
    //     ellipsis: false,
    //     tooltip: false,
    //     dataIndex: 'operations',
    //     slotName: 'operations',
    //     fixed: 'right',
    //     align: 'center'
    // }
])
let cloneColumns = ref(columns.value)

const loading = ref(false)
const tableData = ref<any[]>([])

// 分页配置
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
    showTotal: true,
    showJumper: true,
    showPageSize: true
})

// 获取状态颜色
const getStatusColor = (status: number | string) => {
    const colorMap: Record<string, string> = {
        '0': 'gray',      // 草稿
        '1': 'orange',    // 待收费
        '2': 'green',     // 已生效
        '3': 'blue',      // 已转签
        '4': 'red'        // 已作废
    }
    return colorMap[status.toString()] || 'default'
}

// 查询方法
const search = () => {
    pagination.current = 1
    fetchData()
}

// 重置方法
const reset = () => {
    // 保留当前页签状态和项目ID
    const currentSearchType = formModel.searchType
    const currentProjectId = formModel.projectId
    
    // 重置所有查询条件
    Object.keys(formModel).forEach(key => {
        formModel[key as keyof typeof formModel] = key === 'status' || key === 'cancelReason' ? '' : ''
    })
    
    // 恢复页签状态和项目ID
    formModel.searchType = currentSearchType
    formModel.projectId = currentProjectId
    
    // 重置日期范围
    signDateRange.value = [null, null]
    cancelTimeRange.value = [null, null]
    receivedDateRange.value = [null, null]
    createTimeRange.value = [null, null]
    
    console.log('重置查询条件，保持当前页签:', currentSearchType)
    
    // 重新查询
    search()
}

// 分页方法
const onPageChange = (current: number) => {
    pagination.current = current
    fetchData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    fetchData()
}

const baseColumns = [
    { title: '序号', dataIndex: 'index', slotName: 'index', width: 70, align: 'center', ellipsis: true, tooltip: true },
    { title: '客户', dataIndex: 'customerName', width: 120, align: 'center', ellipsis: true, tooltip: true },
    { title: '意向房源', dataIndex: 'roomName', width: 180, align: 'center', ellipsis: true, tooltip: true },
    { title: '定金应收金额', dataIndex: 'bookingAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
]
const handleColumns = [
    {
        title: '操作',
        width: 310,
        ellipsis: false,
        tooltip: false,
        dataIndex: 'operations',
        slotName: 'operations',
        fixed: 'right',
        align: 'center'
    }
]



// 视图类型切换
const handleChangeType = (key: string) => {
    console.log('key', key)
    activeType.value = key
    formModel.searchType = key
    
    // 页签切换时清空查询条件，避免前一个页签的条件影响当前页签
    resetFormForTabChange(key)
    
    search()

    // 生效中的table 多了 字段 { title: '定金实收金额', dataIndex: 'depositAmount', width: 120 }, 在定金应收金额之后 在之后添加
    if (key === '') {
        // 待生效
        columns.value = [
            ...baseColumns,
            { title: '定金已收金额', dataIndex: 'receivedAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },

            // { title: '定金实收金额', dataIndex: 'depositAmount', width: 120 },
            // { title: '定单金额', dataIndex: 'bookingAmount', width: 120 },
            // { title: '应收日期', dataIndex: 'receivableDate', width: 120, align: 'center', ellipsis: true, tooltip: true },
            // { title: '预计签约日期', dataIndex: 'expectSignDate', width: 120 },
            // { title: '是否可退', dataIndex: 'isRefundable', width: 90, slotName: 'isRefundable', align: 'center', ellipsis: true, tooltip: true },
            { title: '状态', dataIndex: 'status', width: 100, slotName: 'status', ellipsis: true, tooltip: true },
            { title: '创建人', dataIndex: 'createByName', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '创建日期', dataIndex: 'createTime', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '单号', dataIndex: 'bookingNo', width: 190, align: 'center', ellipsis: true, tooltip: true },
            ...handleColumns
        ]
    } else if (key === '1') {
        // 待生效
        columns.value = [
            ...baseColumns,
            // { title: '定金实收金额', dataIndex: 'depositAmount', width: 120 },
            // { title: '定单金额', dataIndex: 'bookingAmount', width: 120 },
            { title: '应收日期', dataIndex: 'receivableDate', width: 120, align: 'center', ellipsis: true, tooltip: true },
            // { title: '预计签约日期', dataIndex: 'expectSignDate', width: 120 },
            { title: '是否可退', dataIndex: 'isRefundable', width: 90, slotName: 'isRefundable', align: 'center', ellipsis: true, tooltip: true },
            { title: '状态', dataIndex: 'status', width: 100, slotName: 'status', ellipsis: true, tooltip: true },
            { title: '创建人', dataIndex: 'createByName', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '创建日期', dataIndex: 'createTime', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '单号', dataIndex: 'bookingNo', width: 190, align: 'center', ellipsis: true, tooltip: true },
            ...handleColumns
        ]
    } else if (key === '2') {
        //生效中 多了：定金实收金额，实收日期； 去掉 是否可退，状态
        columns.value = [
            ...baseColumns,
            { title: '定金实收金额', dataIndex: 'receivedAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '应收日期', dataIndex: 'receivableDate', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '实收日期', dataIndex: 'receivedDate', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '创建人', dataIndex: 'createByName', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '创建日期', dataIndex: 'createTime', width: 120, align: 'center',  ellipsis: true, tooltip: true },
            { title: '单号', dataIndex: 'bookingNo', width: 190, align: 'center', ellipsis: true, tooltip: true },
            ...handleColumns
        ]
    } else if (key === '3') {
        // 已转签
        columns.value = [
            ...baseColumns,
            { title: '定金已收金额', dataIndex: 'receivedAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '实收日期', dataIndex: 'receivedDate', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '合同编号', dataIndex: 'contractNo', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '签订日期', dataIndex: 'signDate', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '租赁单元', dataIndex: 'contractLeaseUnit', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '承租人名称', dataIndex: 'lesseeName', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '定单创建人', dataIndex: 'createByName', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '定单创建日期', dataIndex: 'createTime', width: 120, align: 'center',  ellipsis: true, tooltip: true },
            { title: '单号', dataIndex: 'bookingNo', width: 190, align: 'center', ellipsis: true, tooltip: true },
            ...handleColumns
        ]
    } else if (key === '4') {
        // 已作废
        columns.value = [
            ...baseColumns,
            { title: '定金已收金额', dataIndex: 'receivedAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '作废日期', dataIndex: 'cancelTime', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { title: '作废人', dataIndex: 'cancelByName', width: 120, align: 'center', ellipsis: true, tooltip: true },
            { 
                title: '作废原因', 
                dataIndex: 'cancelReason', 
                width: 120, 
                align: 'center', 
                ellipsis: true, 
                tooltip: true,
                render: ({ record }: { record: any }) => formatCancelReason(record.cancelReason)
            },
            { title: '单号', dataIndex: 'bookingNo', width: 190, align: 'center', ellipsis: true, tooltip: true },
            ...handleColumns
        ]
    } else {

    }
}

// 格式化作废原因
const formatCancelReason = (cancelReason: number | string) => {
    const reasonMap: Record<string, string> = {
        '0': '退定退款',
        '1': '退定不退款', 
        '2': '未收款取消预定',
        '3': '转签合同作废'
    }
    return reasonMap[String(cancelReason)] || cancelReason
}

// 页签切换时重置表单数据
const resetFormForTabChange = (tabKey: string) => {
    // 保留项目ID，清空其他查询条件
    const projectId = formModel.projectId
    
    // 重置所有查询条件
    Object.keys(formModel).forEach(key => {
        if (key !== 'projectId' && key !== 'searchType') {
            formModel[key as keyof typeof formModel] = key === 'status' || key === 'cancelReason' ? '' : ''
        }
    })
    
    // 恢复项目ID
    formModel.projectId = projectId
    formModel.searchType = tabKey
    
    // 重置日期范围选择器
    signDateRange.value = [null, null]
    cancelTimeRange.value = [null, null]
    receivedDateRange.value = [null, null]
    createTimeRange.value = [null, null]
    
    console.log('页签切换，已重置查询条件，当前页签:', tabKey)
}

// 表格操作方法
const handleView = async (record: any) => {
    // Message.info(`查看定单: ${record.id}`)
    // 打开查看抽屉
    // drawerVisible.value = true
    // 等待DOM更新后初始化表单数据 

    const res = await getOrderDetail(record.id)
    nextTick(() => {
        formRef.value?.view(res.data)
    })
}

const handleEdit = async (record: any) => {
    // Message.info(`编辑定单: ${record.id}`)
    // 打开新增抽屉
    // drawerVisible.value = true
    // 等待DOM更新后初始化表单数据
    const res = await getOrderDetail(record.id)
    nextTick(() => {
        formRef.value?.edit(res.data)
    })
}

// 收款码弹窗控制
const collectCodeVisible = ref(false)
const currentRecord = ref<any>({})

// 打开收款码弹窗
const handleCollect = async (record: any) => {
    console.log('appBaseUrl', appBaseUrl)
    try {
        // 获取完整的订单详情
        const res = await getOrderDetail(record.id)
        if (res.code === 200) {
            // 生成支付链接
            const baseUrl = appBaseUrl
            const orderId = record.id
            const amount = record.depositAmount || record.bookingAmount
            const customerName = encodeURIComponent(record.customerName || '')
            
            const paymentLink = `${baseUrl}/order-payment?id=${orderId}&amount=${amount}&customer=${customerName}&type=deposit`
            
            currentRecord.value = {
                ...record,
                ...res.data,
                paymentUrl: paymentLink
            }
            collectCodeVisible.value = true
        }
    } catch (error) {
        console.error('获取订单详情失败:', error)
        Message.error('获取订单详情失败')
    }
}

// 关闭收款码弹窗
const handleCollectCancel = () => {
    collectCodeVisible.value = false
    currentRecord.value = {}
}

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) {
        return '0.00'
    }
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 处理二维码生成成功
const handleQRCodeGenerated = (dataUrl: string) => {
    console.log('二维码生成成功:', dataUrl)
}

// 处理二维码生成失败
const handleQRCodeError = (error: Error) => {
    Message.error('二维码生成失败: ' + error.message)
    console.error('二维码生成失败:', error)
}

// 复制支付链接
const copyPaymentUrl = async () => {
    if (!currentRecord.value?.paymentUrl) {
        Message.warning('暂无支付链接')
        return
    }
    
    try {
        await navigator.clipboard.writeText(currentRecord.value.paymentUrl)
        Message.success('支付链接已复制到剪贴板')
    } catch (error) {
        // 降级方案：使用传统方法复制
        const textArea = document.createElement('textarea')
        textArea.value = currentRecord.value.paymentUrl
        document.body.appendChild(textArea)
        textArea.select()
        try {
            document.execCommand('copy')
            Message.success('支付链接已复制到剪贴板')
        } catch (err) {
            Message.error('复制失败，请手动复制')
        }
        document.body.removeChild(textArea)
    }
}

const handleDelete = (record: any) => {
    Modal.warning({
        title: '确认删除',
        content: '确定要删除该定单吗？删除后无法恢复。',
        okText: '确认',
        cancelText: '取消',
        closable: true,
        onOk: async () => {
            try {
                await deleteOrder(record.id)
                Message.success('删除成功')
                fetchData() // 刷新列表
            } catch (error) {
                console.error('删除失败:', error)
            }
        },
        onCancel: () => {
            console.log('取消')
        }
    })
}

// 抽屉控制
const drawerVisible = ref(false)
const refundDrawerVisible = ref(false)
const formRef = ref()
const refundFormRef = ref()

// 打开新增抽屉
const handleAdd = () => {
    if (!formModel.projectId) {
        Message.warning('请先选择项目')
        return
    }
    formRef.value?.open(formModel.projectId)
}

// 取消操作
const handleCancel = () => {
    // drawerVisible.value = false
}



// 表单提交成功回调
const onSuccess = () => {
    // drawerVisible.value = false
    fetchData() // 刷新列表
}

// 打开退定单抽屉
const handleRefund = async (record: any) => {
    // 打开退定单抽屉
    let res = await getOrderDetail(record.id)
    refundFormRef.value?.open(res.data)
}

// 退定单相关操作
const handleRefundCancel = () => {
    refundDrawerVisible.value = false
}

const handleRefundSave = async () => {
    try {
        await refundFormRef.value?.save()
        Message.success('暂存成功')
        refundDrawerVisible.value = false
        fetchData() // 刷新列表
    } catch (error) {
        console.error('暂存失败:', error)
    }
}

const handleRefundSubmit = async () => {
    try {
        await refundFormRef.value?.submit()
        Message.success('生成成功')
        refundDrawerVisible.value = false
        fetchData() // 刷新列表
    } catch (error) {
        console.error('生成失败:', error)
    }
}

const handleRefundPrint = () => {
    refundFormRef.value?.print()
}

const onRefundSuccess = () => {
    refundDrawerVisible.value = false
    fetchData() // 刷新列表
}

// 作废弹窗相关
const voidModalVisible = ref(false)
const voidForm = reactive({
    recordId: null // 存储当前操作的记录ID
})

// 打开作废弹窗
const handleVoid = (record: any) => {
    voidForm.recordId = record.id
    voidModalVisible.value = true
}

// 取消作废
const handleVoidCancel = () => {
    voidModalVisible.value = false
    voidForm.recordId = null
}

// 确认作废
const handleVoidConfirm = async () => {
    try {
        await invalidOrder({
            id: voidForm.recordId,
            cancelRemark: '' // 不再需要作废原因
        })
        Message.success('作废成功')
        voidModalVisible.value = false
        fetchData() // 刷新列表
    } catch (error) {
        console.error('作废失败:', error)
    }
}

// 退款申请抽屉控制
const refundApplyDrawerVisible = ref(false)
const orderRefundApplyFormRef = ref()

// 打开退款申请抽屉
const handleRefundApply = async (record: any) => {
    try {
        orderRefundApplyFormRef.value?.open({
            record: record,
            mode: 'create'
        })
    } catch (error) {
        console.error('获取退款申请信息失败:', error)
        // Message.error('获取退款申请信息失败')
    }
}

// 退款申请相关操作
const handleRefundApplyCancel = () => {
    orderRefundApplyFormRef.value?.handleCancel()
}

const handleRefundApplySave = async () => {
    try {
        await orderRefundApplyFormRef.value?.handleSave()
    } catch (error) {
        console.error('暂存失败:', error)
    }
}

const handleRefundApplySubmit = async () => {
    try {
        await orderRefundApplyFormRef.value?.handleSubmit()
    } catch (error) {
        console.error('发起审批失败:', error)
    }
}

const onRefundApplySuccess = () => {
    fetchData()
}

// 表格选择配置
const selectedRowKeys = ref<(string | number)[]>([])

// 表格选择方法
const onSelect = (selected: boolean, record: any, selectedRows: any[]) => {
    console.log('选择：', selected, record, selectedRows)
}

const onSelectAll = (selected: boolean, selectedRows: any[]) => {
    console.log('全选：', selected, selectedRows)
}



// 审批弹窗相关
const approveModalVisible = ref(false)
const approveFormRef = ref()
const approveForm = reactive({
    reason: '',
    recordId: null // 存储当前操作的记录ID
})

// 打开审批弹窗
const handleApproveModal = (record: any) => {
    approveForm.recordId = record.id
    approveForm.reason = ''
    approveModalVisible.value = true
}

// 取消审批
const handleApproveCancel = () => {
    approveModalVisible.value = false
    approveForm.reason = ''
    approveForm.recordId = null
}

// 确认通过
const handleApproveConfirm = async () => {
    try {
        await approveOrder({
            id: approveForm.recordId,
            reason: approveForm.reason
        })
        Message.success('审批通过成功')
        approveModalVisible.value = false
        fetchData() // 刷新列表
    } catch (error) {
        console.error('审批失败:', error)
    }
}

// 拒绝弹窗相关
const rejectModalVisible = ref(false)
const rejectFormRef = ref()
const rejectForm = reactive({
    reason: '',
    recordId: null // 存储当前操作的记录ID
})

// 点击拒绝按钮
const handleRejectConfirm = () => {
    rejectForm.recordId = approveForm.recordId
    rejectForm.reason = approveForm.reason
    approveModalVisible.value = false
    rejectModalVisible.value = true
}

// 取消拒绝
const handleRejectCancel = () => {
    rejectModalVisible.value = false
    rejectForm.reason = ''
    rejectForm.recordId = null
}

// 确认拒绝提交
const handleRejectSubmit = async () => {
    try {
        await rejectFormRef.value.validate()
        await rejectOrder({
            id: rejectForm.recordId,
            cancelRemark: rejectForm.reason
        })
        Message.success('拒绝成功')
        rejectModalVisible.value = false
        fetchData() // 刷新列表
    } catch (error) {
        console.error('拒绝失败:', error)
    }
}

// 审批相关方法
const handleApprove = async (record: any) => {
    handleApproveModal(record)
}

// 打印定单
const handlePrint = async (record: any) => {
    try {
        const printData: BookingPrintDto = {
            id: record.id,
            type: 10, // 定单类型
            // bookingData: record
        }
        
        const response = await printBooking(printData)
        
        if (response.data?.fileUrl) {
            // 打开新窗口预览/下载打印文件
            previewFileUrl.value = response.data.fileUrl
            previewTitle.value = '定单打印文件'
            previewVisible.value = true
            // window.open(response.data.fileUrl, '_blank')
            Message.success('打印文件生成成功')
        } else {
            // Message.error('打印文件生成失败')
        }
    } catch (error) {
        console.error('打印失败:', error)
        // Message.error('打印失败')
    }
}

const previewVisible = ref(false)
const previewFileUrl = ref('')
const previewTitle = ref('')

// 打印退订单
const handlePrintRefund = async (record: any) => {
    try {
        const printData: BookingPrintDto = {
            id: record.id,
            type: 100, // 退订单类型
            // bookingData: record
        }
        
        const response = await printBooking(printData)
        
        if (response.data?.fileUrl) {
            // 打开新窗口预览/下载打印文件
            previewFileUrl.value = response.data.fileUrl
            previewTitle.value = '退订单打印文件'
            previewVisible.value = true
            // Message.success('退订单打印文件生成成功')
        } else {
            // Message.error('退订单打印文件生成失败')
        }
    } catch (error) {
        console.error('打印退订单失败:', error)
        // Message.error('打印退订单失败')
    }
}

// 获取数据方法
const fetchData = async () => {
    loading.value = true
    try {
        // 构建API查询参数
        const queryParams = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            searchType: formModel.searchType,
            projectId: formModel.projectId || undefined,
            customerName: formModel.customerName || undefined,
            roomName: formModel.roomName || undefined,
            status: formModel.status !== '' ? formModel.status : undefined,
            createTimeStart: formModel.createTimeStart || undefined,
            createTimeEnd: formModel.createTimeEnd || undefined,
            createByName: formModel.createByName || undefined,
            actualReceiveTimeStart: formModel.actualReceiveTimeStart || undefined,
            actualReceiveTimeEnd: formModel.actualReceiveTimeEnd || undefined,
            contractNo: formModel.contractNo || undefined,
            signDateStart: formModel.signDateStart || undefined,
            signDateEnd: formModel.signDateEnd || undefined,
            contractLeaseUnit: formModel.contractLeaseUnit || undefined,
            lesseeName: formModel.lesseeName || undefined,
            cancelReason: formModel.cancelReason !== '' ? formModel.cancelReason : undefined,
            cancelByName: formModel.cancelByName || undefined,
            cancelTimeStart: formModel.cancelTimeStart || undefined,
            cancelTimeEnd: formModel.cancelTimeEnd || undefined
        }

        // 移除undefined的参数
        Object.keys(queryParams).forEach(key => {
            if (queryParams[key as keyof typeof queryParams] === undefined) {
                delete queryParams[key as keyof typeof queryParams]
            }
        })

        console.log('查询参数:', queryParams)

        const res = await getOrderList(queryParams)
        if (res.rows && Array.isArray(res.rows)) {
            tableData.value = res.rows
            pagination.total = res.total || 0
        } else if (res.data?.list && Array.isArray(res.data.list)) {
            // 兼容不同的返回格式
            tableData.value = res.data.list
            pagination.total = res.data.total || 0
        } else {
            tableData.value = []
            pagination.total = 0
        }
    } catch (error) {
        console.error('获取定单列表失败:', error)
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}


watch(
    () => columns.value,
    (val) => {
        cloneColumns.value = cloneDeep(val)
        // cloneColumns.value.forEach((item, index) => {
        //     item.checked = true;
        // });
        // showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
);


// 初始化 - 设置默认页签的列配置
const initDefaultTab = () => {
    // 设置待生效页签的列配置
    handleChangeType('1')
}

// 页面初始化
initDefaultTab()

// 处理项目选择变化
const handleProjectChange = (value: string) => {
    formModel.projectId = value
    search()
}

// 订单记账相关
const orderAccountModalRef = ref()
const currentOrderData = reactive<any>({})

// 打开订单记账
const handleAccount = async (record: any) => {
    // 清理之前的数据，确保没有污染
    Object.keys(currentOrderData).forEach(key => delete currentOrderData[key])
    Object.assign(currentOrderData, {id: record.id, viewMode: false})
    orderAccountModalRef.value?.open()
    // try {
    //     // // 获取完整的订单详情
    //     // const res = await getOrderDetail(record.id)
    //     // if (res.code === 200 && res.data) {
    //     //     Object.assign(currentOrderData, res.data)
    //     //     orderAccountModalRef.value?.open()
    //     // } else {
    //     //     Message.error('获取订单详情失败')
    //     // }
    // } catch (error) {
    //     console.error('获取订单详情失败:', error)
    //     Message.error('获取订单详情失败')
    // }
}

// 关闭订单记账
const handleAccountCancel = () => {
    // 组件内部已经处理了关闭逻辑
}

// 保存订单记账
const handleAccountSave = async (accountData: any[]) => {
    try {
        // TODO: 调用保存记账的API
        console.log('保存记账数据:', accountData)
        Message.success('记账保存成功')
        fetchData() // 刷新列表
    } catch (error) {
        console.error('保存记账失败:', error)
        // Message.error('保存记账失败')
    }
}

// 查看记账记录
const handleViewAccountRecord = async (record: any) => {
    try {
        // 清理之前的数据，确保没有污染
        Object.keys(currentOrderData).forEach(key => delete currentOrderData[key])
        // 打开记账组件，但设置为查看模式
        Object.assign(currentOrderData, {id: record.id, viewMode: true})
        orderAccountModalRef.value?.open()
    } catch (error) {
        console.error('查看记账记录失败:', error)
        // Message.error('查看记账记录失败')
    }
}

// 合同组件引用
const contractRef = ref()
const contractDetailRef = ref()

// 合同类型选择弹窗
const contractTypeModalVisible = ref(false)
const currentContractRecord = ref<any>(null)

// 合同类型选项
const contractTypeOptions = [
    { label: '非宿舍合同', value: 0 },
    { label: '宿舍合同', value: 1 },
    { label: '多经合同', value: 2 }
]

// 转签约处理
const handleContract = async (record: any) => {
    try {
        // 保存当前订单记录
        currentContractRecord.value = record
        
        // 判断是否为暂未确认房源的订单
        const isUnknownSource = record.roomName === '暂不确认房源' || !record.roomId
        
        if (isUnknownSource) {
            // 暂未确认房源的订单可以选择合同类型
            contractTypeModalVisible.value = true
        } else {
            // 已确认房源的订单，先查询房间详情，然后根据物业类型打开对应合同
            const contractType = record.propertyType === '10' ? 1 : 0  // '10'是宿舍类，其他都是非宿舍类
            
            // 构建项目信息
            const project = {
                id: record.projectId,
                name: record.projectName
            }
            
            // 查询房间详情
            let roomInfo = record
            if (record.roomId) {
                try {
                    const roomDetailRes = await getRoomDetail({ id: record.roomId })
                    if (roomDetailRes.code === 200 && roomDetailRes.data) {
                        // 构建房间信息作为参数，参考roomStatusSimple的构建方式
                        roomInfo = {
                            roomId: record.roomId,
                            roomName: record.roomName,
                            projectId: record.projectId,
                            projectName: record.projectName,
                            isRenewalContract: true, // 添加预定转签约标识
                            // 添加房间详情信息
                            ...roomDetailRes.data,
                            // 保留订单的其他信息
                            ...record
                        }
                    }
                } catch (error) {
                    console.error('获取房间详情失败:', error)
                    // 如果获取房间详情失败，仍然使用原有订单信息
                }
            }
            
            // 直接打开对应类型的合同新增页面
            contractRef.value?.open('create', contractType, roomInfo, project)
        }
    } catch (error) {
        console.error('转签约失败:', error)
        // Message.error('转签约失败')
    }
}

// 确认选择合同类型
const handleContractTypeConfirm = async (contractType: number) => {
    try {
        if (currentContractRecord.value) {
            // 构建项目信息
            const project = {
                id: currentContractRecord.value.projectId,
                name: currentContractRecord.value.projectName
            }
            
            // 查询房间详情
            let roomInfo = currentContractRecord.value
            if (currentContractRecord.value.roomId) {
                try {
                    const roomDetailRes = await getRoomDetail({ id: currentContractRecord.value.roomId })
                    if (roomDetailRes.code === 200 && roomDetailRes.data) {
                        // 构建房间信息作为参数，参考roomStatusSimple的构建方式
                        roomInfo = {
                            roomId: currentContractRecord.value.roomId,
                            roomName: currentContractRecord.value.roomName,
                            projectId: currentContractRecord.value.projectId,
                            projectName: currentContractRecord.value.projectName,
                            isDirectContract: true, // 添加直接签约标识
                            // 添加房间详情信息
                            ...roomDetailRes.data,
                            // 保留订单的其他信息
                            ...currentContractRecord.value
                        }
                    }
                } catch (error) {
                    console.error('获取房间详情失败:', error)
                    // 如果获取房间详情失败，仍然使用原有订单信息
                }
            }
            
            // 打开合同新增页面，传入订单信息和选择的合同类型
            contractRef.value?.open('create', contractType, roomInfo, project)
            
            // 关闭选择弹窗
            contractTypeModalVisible.value = false
            currentContractRecord.value = null
        }
    } catch (error) {
        console.error('转签约失败:', error)
        // Message.error('转签约失败')
    }
}

// 取消选择合同类型
const handleContractTypeCancel = () => {
    contractTypeModalVisible.value = false
    currentContractRecord.value = null
}

// 合同提交处理
const handleContractSubmit = () => {
    // 合同提交成功后，刷新订单列表
    fetchData()
    Message.success('合同创建成功')
}

// 查看合同处理
const handleViewContract = (record: any) => {
    try {
        if (!record.contractId) {
            Message.warning('该订单暂无关联合同')
            return
        }
        // 打开合同详情
        contractDetailRef.value?.open({ id: record.contractId })
    } catch (error) {
        console.error('查看合同失败:', error)
        // Message.error('查看合同失败')
    }
}

// 合同详情更新处理
const handleContractDetailUpdate = () => {
    // 合同详情更新后，刷新订单列表
    fetchData()
}

// 更多操作类型定义
interface MoreAction {
    key: string
    label: string
    danger?: boolean
}

// 更多操作下拉菜单
const handleMoreAction = (key: string, record: any) => {
    console.log('更多操作:', key, record)
    
    switch (key) {
        case 'refund':
            handleRefund(record)
            break
        case 'refundApply':
            handleRefundApply(record)
            break
        case 'viewAccountRecord':
            handleViewAccountRecord(record)
            break
        case 'account':
            handleAccount(record)
            break
        case 'void':
            handleVoid(record)
            break
        case 'delete':
            handleDelete(record)
            break
        case 'approve':
            handleApprove(record)
            break
        case 'print':
            handlePrint(record)
            break
        case 'printRefund':
            handlePrintRefund(record)
            break
        default:
            console.warn('未知操作:', key)
    }
}

// 获取更多操作列表
const getMoreActions = (record: any): MoreAction[] => {
    const actions: MoreAction[] = []
    
    // 根据订单状态添加对应的操作
    switch (record.status) {
        case 0: // 草稿状态
            actions.push(
                // { key: 'print', label: '打印定单' },
                { key: 'void', label: '作废', danger: true },
                { key: 'delete', label: '删除', danger: true }
            )
            break
            
        case 1: // 待收费状态
            actions.push(
                // { key: 'print', label: '打印定单' },
                { key: 'account', label: '记账' },
                { key: 'void', label: '作废', danger: true }
            )
            break
            
        case 2: // 已生效状态

        //当退定选择【是否退定金】=“是”时，生效后出现按钮"发起退款"
// 当退定选择【是否退定金】=“是”时，生效后列表出现按钮"退款申请"
// '0': '退定退款',
//         '1': '退定不退款', 
//         '2': '未收款取消预定',
//         '3': '转签合同作废'

            if (record.isRefund === 1) {
                actions.push(
                    // { key: 'print', label: '打印定单' },
                    { key: 'refund', label: '退定单' },
                    // { key: 'printRefund', label: '打印退订单' },
                    // { key: 'refundApply', label: '退款申请' },
                    { key: 'viewAccountRecord', label: '查看记账记录' }
                )
            } else {
                actions.push(
                    // { key: 'print', label: '打印定单' },
                    { key: 'refund', label: '退定单' },
                    { key: 'viewAccountRecord', label: '查看记账记录' }
                )
            }
            break
            
        case 3: // 已转签状态
            actions.push(
                { key: 'print', label: '打印定单' }
            )
            break
            
        case 4: // 已作废状态
            // 已作废状态可以打印定单
            actions.push(
                { key: 'print', label: '打印定单' }
            )
            break
            
        default:
            break
    }
    
    return actions
}
</script>

<style scoped lang="less">
:deep(.arco-dropdown-option) {
    // padding: 0 16px;
    background: #fff;
}
.container {
    padding: 0 16px;
    .content {
        background-color: #fff;
        border-radius: 4px;
        // padding: 0 16px;
    }


    .general-card {
        box-sizing: border-box;
        // padding-top: 12px;
    }
}

:deep(.arco-table-th) {
    &:last-child {
        .arco-table-th-item-title {
            margin-left: 16px;
        }
    }
}

.action-icon {
    margin-left: 12px;
    cursor: pointer;
}

.active {
    color: #0960bd;
    background-color: #e3f4fc;
}

.collect-code-container {
    padding: 24px;
    text-align: center;

    .collect-code-header {
        margin-bottom: 24px;

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #1d2129;
        }

        .order-info {
            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                padding: 0 16px;

                .label {
                    color: #86909c;
                    font-size: 14px;
                }

                .value {
                    color: #1d2129;
                    font-size: 14px;
                    font-weight: 500;

                    &.amount {
                        color: #f53f3f;
                        font-weight: 600;
                        font-size: 16px;
                    }
                }
            }
        }
    }

    .qrcode-display {
        margin-bottom: 24px;
        display: flex;
        justify-content: center;
    }

    .payment-link {
        margin-top: 16px;
    }
}

// 合同类型选择弹窗样式
.contract-type-selection {
    padding: 0 0 16px 0;

    .selection-tip {
        color: #86909c;
        font-size: 14px;
        margin-top: 0 !important;
        margin-bottom: 20px;
        text-align: center;
    }

    .contract-type-options {
        .contract-type-option {
            margin-bottom: 12px;
            border: 1px solid #e5e6eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                border-color: #4080ff;
                background-color: #f8faff;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(64, 128, 255, 0.15);
            }

            &:last-child {
                margin-bottom: 0;
            }

            .option-content {
                display: flex;
                align-items: center;
                padding: 16px 20px;

                .option-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 8px;
                    background: linear-gradient(135deg, #4080ff 0%, #2e5bff 100%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 16px;

                    :deep(.arco-icon) {
                        color: white;
                        font-size: 18px;
                    }
                }

                .option-text {
                    flex: 1;

                    .option-label {
                        font-size: 16px;
                        font-weight: 500;
                        color: #1d2129;
                    }
                }

                .option-arrow {
                    color: #86909c;
                    transition: all 0.3s ease;

                    :deep(.arco-icon) {
                        font-size: 16px;
                    }
                }
            }

            &:hover .option-content .option-arrow {
                color: #4080ff;
                transform: translateX(2px);
            }
        }
    }
}

// 危险操作样式
:deep(.danger-action) {
    color: #f53f3f !important;
    
    &:hover {
        background-color: #ffece8;
        color: #cb2634 !important;
    }
}
</style>
