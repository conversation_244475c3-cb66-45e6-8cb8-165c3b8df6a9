<template>
    <div class="contract-termination-display">
        <!-- 加载状态 -->
        <a-spin :loading="loading" style="width: 100%;" v-if="loading">
            <div style="height: 200px; display: flex; align-items: center; justify-content: center;">
                加载中...
            </div>
        </a-spin>
        
        <!-- 主要内容 -->
        <div v-else>
        <!-- 承租方信息 -->
        <section-title title="承租方信息" style="margin-bottom: 16px;"/>
        <div class="info-section">
            <a-row :gutter="16">
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">承租方名称:</span>
                        <span class="value">{{ tenantInfo?.name || '-' }}</span>
                    </div>
                </a-col>
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">个人/企业:</span>
                        <span class="value">{{ tenantInfo?.type || '-' }}</span>
                    </div>
                </a-col>
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">合同用途:</span>
                        <span class="value">{{ tenantInfo?.purpose || '-' }}</span>
                    </div>
                </a-col>
            </a-row>
        </div>

        <!-- 租赁房源 -->
        <section-title title="租赁房源" style="margin-bottom: 16px;"/>
        <a-table 
            :columns="houseColumns" 
            :data="houseList" 
            :pagination="false"
            :bordered="{ cell: true }"
        >
            <template #area="{ record }">
                {{ record.area }}
            </template>
        </a-table>

        <!-- 基础信息 -->
        <section-title title="基础信息" style="margin-bottom: 16px;"/>
        <div class="info-section">
            <a-row :gutter="16">
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">合同起止日期:</span>
                        <span class="value">{{ baseInfo?.contractPeriod || '-' }}</span>
                    </div>
                </a-col>
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">合同总金额:</span>
                        <span class="value">{{ baseInfo?.totalAmount ? baseInfo.totalAmount + '元' : '-' }}</span>
                    </div>
                </a-col>
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">免租期:</span>
                        <span class="value">{{ baseInfo?.freePeriod || '-' }}</span>
                    </div>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">已收保证金:</span>
                        <span class="value">{{ baseInfo?.deposit ? baseInfo.deposit + '元' : '-' }}</span>
                    </div>
                </a-col>
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">已收租金:</span>
                        <span class="value">{{ baseInfo?.paidRent ? baseInfo.paidRent + '元' : '-' }}</span>
                    </div>
                </a-col>
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">已收款账期:</span>
                        <span class="value">{{ baseInfo?.paidPeriod || '-' }}</span>
                    </div>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="8">
                    <div class="info-item highlight-red">
                        <span class="label">逾期未收租金:</span>
                        <span class="value">{{ baseInfo?.unpaidRent ? baseInfo.unpaidRent + '元' : '-' }}</span>
                    </div>
                </a-col>
                <a-col :span="8">
                    <div class="info-item highlight-red">
                        <span class="label">逾期账期:</span>
                        <span class="value">{{ baseInfo?.terminationPeriod || '-' }}</span>
                    </div>
                </a-col>
            </a-row>
        </div>

        <!-- 退租信息 -->
        <section-title title="退租信息" style="margin-bottom: 16px;"/>
        <div class="info-section">
            <a-row :gutter="16">
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">退租类型:</span>
                        <span class="value">{{ getTerminateTypeText(terminationInfo?.terminateType) }}</span>
                    </div>
                </a-col>
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">退租日期:</span>
                        <span class="value">{{ terminationInfo?.terminateDate || '-' }}</span>
                    </div>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="24">
                    <div class="info-item">
                        <span class="label">退租原因:</span>
                        <span class="value">{{ getTerminateReasonsText(terminationInfo?.terminateReason) }}</span>
                    </div>
                </a-col>
            </a-row>
            <a-row :gutter="16" v-if="terminationInfo?.otherReasonDesc">
                <a-col :span="24">
                    <div class="info-item">
                        <span class="label">其他原因说明:</span>
                        <span class="value">{{ terminationInfo.otherReasonDesc }}</span>
                    </div>
                </a-col>
            </a-row>
        </div>

        <!-- 免租期是否收费 -->
        <section-title title="免租期是否收费" v-if="freeList && freeList.length > 0" style="margin-bottom: 16px;"/>
        <a-table 
            :columns="freeColumns" 
            :data="freeList" 
            :pagination="false"
            :bordered="{ cell: true }"
            v-if="freeList && freeList.length > 0"
        >
            <template #operation="{ record }">
                <span>{{ record.isCharge ? '是' : '否' }}</span>
            </template>
        </a-table>

        <!-- 预计退款信息 -->
        <section-title title="预计退款信息" v-if="refundList && refundList.length > 0" style="margin-bottom: 16px;"/>
        <a-table 
            :columns="refundColumns" 
            :data="refundList" 
            :pagination="false"
            :bordered="{ cell: true }"
            v-if="refundList && refundList.length > 0"
        >
            <template #penaltyAmount="{ record }">
                {{ formatAmount(record.penaltyAmount) }}
            </template>
            <template #refundAmount="{ record }">
                {{ formatAmount(record.refundAmount) }}
            </template>
        </a-table>
        
        <!-- 合计信息 -->
        <div class="total-info" v-if="refundList && refundList.length > 0">
            <a-row :gutter="16">
                <a-col :span="8">
                    <span>总罚没金额：<strong>{{ formatAmount(totalPenaltyAmount) }}</strong></span>
                </a-col>
                <a-col :span="8">
                    <span>总预计退款金额：<strong>{{ formatAmount(totalRefundAmount) }}</strong></span>
                </a-col>
            </a-row>
        </div>

        <!-- 其他扣款 -->
        <section-title title="其他扣款" v-if="deductionInfo?.hasOtherDeduction" style="margin-bottom: 16px;"/>
        <div class="info-section" v-if="deductionInfo?.hasOtherDeduction">
            <a-row :gutter="16">
                <a-col :span="8">
                    <div class="info-item">
                        <span class="label">其他扣款:</span>
                        <span class="value">{{ deductionInfo.hasOtherDeduction ? '是' : '否' }}</span>
                    </div>
                </a-col>
                <a-col :span="8" v-if="deductionInfo.hasOtherDeduction">
                    <div class="info-item">
                        <span class="label">扣款金额:</span>
                        <span class="value">{{ deductionInfo.deductionAmount ? formatAmount(deductionInfo.deductionAmount) + '元' : '-' }}</span>
                    </div>
                </a-col>
            </a-row>
            <a-row :gutter="16" v-if="deductionInfo.hasOtherDeduction && deductionInfo.otherDeductionDesc">
                <a-col :span="24">
                    <div class="info-item">
                        <span class="label">说明:</span>
                        <span class="value">{{ deductionInfo.otherDeductionDesc }}</span>
                    </div>
                </a-col>
            </a-row>
        </div>

        <!-- 备注 -->
        <section-title title="备注" v-if="terminationInfo?.terminateRemark" style="margin-bottom: 16px;"/>
        <div class="info-section" v-if="terminationInfo?.terminateRemark">
            <a-row>
                <a-col :span="24">
                    <div class="info-item">
                        <span class="label">备注:</span>
                        <span class="value">{{ terminationInfo.terminateRemark }}</span>
                    </div>
                </a-col>
            </a-row>
        </div>

        <!-- 附件 -->
        <section-title title="附件" v-if="attachmentList && attachmentList.length > 0" style="margin-bottom: 16px;" />
        <div class="attachment-section" v-if="attachmentList && attachmentList.length > 0">
            <a-list :data="attachmentList" size="small">
                <template #item="{ item }">
                    <a-list-item>
                        <a-link :href="item.url" target="_blank">{{ item.name }}</a-link>
                    </a-list-item>
                </template>
            </a-list>
        </div>
        
        </div> <!-- 结束主要内容的div -->
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { 
    getContractTerminateDetail,
    type ContractTerminateVo
} from '@/api/contractTerminate'
import { getDictLabel } from '@/dict'
import { useDictSync } from '@/utils/dict'

// Props
interface Props {
    terminateId?: string
    terminateData?: ContractTerminateVo // 直接传入退租数据
}

const props = withDefaults(defineProps<Props>(), {
    terminateId: '',
    terminateData: undefined
})

// 响应式数据
const loading = ref(false)
const terminateDetail = ref<ContractTerminateVo>()
const terminateReasonOptions = ref<any[]>([])

// 计算属性 - 承租方信息
const tenantInfo = computed(() => {
    const data = terminateDetail.value || props.terminateData
    if (!data?.contract?.customer) return null
    
    return {
        name: data.contract.customer.customerName || '',
        type: data.contract.customer.customerType === 1 ? '个人' : '企业',
        purpose: getDictLabel('diversification_purpose', data.contract.contractPurpose?.toString() || '') || ''
    }
})

// 计算属性 - 房源列表
const houseList = computed(() => {
    const data = terminateDetail.value || props.terminateData
    return data?.roomList || data?.contract?.rooms || []
})

// 计算属性 - 基础信息
const baseInfo = computed(() => {
    const data = terminateDetail.value || props.terminateData
    if (!data?.contract) return null
    
    const contract = data.contract
    let freePeriod = ''
    if (contract.fees && contract.fees.length > 0) {
        const firstFee = contract.fees[0]
        const lastFee = contract.fees[contract.fees.length - 1]
        if (firstFee.startDate && lastFee.endDate) {
            freePeriod = `${firstFee.startDate} 至 ${lastFee.endDate}`
        }
    }
    
    return {
        contractPeriod: `${contract.startDate} 至 ${contract.endDate}`,
        totalAmount: contract.totalPrice ? formatAmount(contract.totalPrice) : '',
        freePeriod,
        deposit: data.bondReceivedAmount ? formatAmount(data.bondReceivedAmount) : '',
        paidRent: data.rentReceivedAmount ? formatAmount(data.rentReceivedAmount) : '',
        paidPeriod: data.receivedPeriod || '',
        unpaidRent: data.rentOverdueAmount ? formatAmount(data.rentOverdueAmount) : '',
        terminationPeriod: data.overduePeriod || ''
    }
})

// 计算属性 - 退租信息
const terminationInfo = computed(() => {
    const data = terminateDetail.value || props.terminateData
    if (!data) return null
    
    return {
        terminateType: data.terminateType,
        terminateDate: data.terminateDate,
        terminateReason: data.terminateReason,
        otherReasonDesc: data.otherReasonDesc,
        terminateRemark: data.terminateRemark
    }
})

// 计算属性 - 免租期列表
const freeList = computed(() => {
    const data = terminateDetail.value || props.terminateData
    return data?.contract?.fees || []
})

// 计算属性 - 退款信息列表
const refundList = computed(() => {
    const data = terminateDetail.value || props.terminateData
    return data?.costList || []
})

// 计算属性 - 其他扣款信息
const deductionInfo = computed(() => {
    const data = terminateDetail.value || props.terminateData
    if (!data) return null
    
    return {
        hasOtherDeduction: data.hasOtherDeduction,
        deductionAmount: 0, // ContractTerminateVo 中没有 otherDeductionAmount 字段
        otherDeductionDesc: data.otherDeductionDesc
    }
})

// 计算属性 - 附件列表
const attachmentList = computed(() => {
    const data = terminateDetail.value || props.terminateData
    if (!data?.terminateAttachments) return []
    
    try {
        return JSON.parse(data.terminateAttachments)
    } catch (e) {
        console.warn('解析附件失败:', e)
        return []
    }
})

// 计算属性 - 总罚没金额
const totalPenaltyAmount = computed(() => {
    return refundList.value.reduce((sum, item) => {
        const penaltyAmount = Number(item.penaltyAmount) || 0
        return sum + penaltyAmount
    }, 0)
})

// 计算属性 - 总预计退款金额
const totalRefundAmount = computed(() => {
    return refundList.value.reduce((sum, item) => {
        const refundAmount = Number(item.refundAmount) || 0
        return sum + refundAmount
    }, 0)
})

// 房源表格列配置
const houseColumns = [
    { 
        title: '房源信息', 
        dataIndex: 'roomName',
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    { 
        title: '租赁面积(㎡)', 
        dataIndex: 'area', 
        slotName: 'area',
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 免租期表格列配置
const freeColumns = [
    { 
        title: '免租类型', 
        dataIndex: 'freeType',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => {
            const freeTypeMap: Record<number, string> = {
                0: '装修免租',
                1: '经营免租',
                2: '合同免租'
            }
            return freeTypeMap[record.freeType] || '未知'
        }
    },
    { 
        title: '开始日期', 
        dataIndex: 'startDate',
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    { 
        title: '免租天数', 
        dataIndex: 'freeRentDay',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => {
            const months = record.freeRentMonth || 0
            const days = record.freeRentDay || 0
            return `${months}月 ${days}天`
        }
    },
    { 
        title: '结束日期', 
        dataIndex: 'endDate',
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    { 
        title: '备注', 
        dataIndex: 'remark',
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    { 
        title: '是否收费', 
        dataIndex: 'operation', 
        slotName: 'operation',
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 退款信息表格列配置
const refundColumns = [
    { 
        title: '费项', 
        dataIndex: 'subjectName',
        align: 'center',
        width: 90,
        ellipsis: true,
        tooltip: true,
    },
    { 
        title: '期限', 
        dataIndex: 'period',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => {
            if (record.startDate && record.endDate) {
                return `${record.startDate} 至 ${record.endDate}`
            }
            return record.period || '-'
        }
    },
    { 
        title: '应收日期', 
        dataIndex: 'receivableDate',
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    { 
        title: '账单应收金额(元)', 
        dataIndex: 'actualReceivable',
        align: 'center',
        width: 150,
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => formatAmount(record.actualReceivable)
    },
    { 
        title: '截至退款日应收(元)', 
        dataIndex: 'terminateReceivable',
        width: 160,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => formatAmount(record.terminateReceivable)
    },
    { 
        title: '账单已收金额(元)', 
        dataIndex: 'receivedAmount',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => formatAmount(record.receivedAmount)
    },
    { 
        title: '欠款金额(元)', 
        dataIndex: '',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: any) => formatAmount(record.terminateReceivable - record.receivedAmount)
    },
    { 
        title: '罚没金额(元)', 
        dataIndex: 'penaltyAmount', 
        slotName: 'penaltyAmount',
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    { 
        title: '预计退款金额(元)', 
        dataIndex: 'refundAmount',
        slotName: 'refundAmount',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 方法
const formatAmount = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined || isNaN(Number(amount))) {
        return '0.00'
    }
    return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 获取退租类型文本
const getTerminateTypeText = (type?: number) => {
    switch (type) {
        case 0:
            return '到期退租'
        case 1:
            return '提前退租'
        default:
            return '未知'
    }
}

// 获取退租原因文本
const getTerminateReasonsText = (reasons?: string | string[]) => {
    if (!reasons || !terminateReasonOptions.value.length) return '-'
    
    let reasonArray: string[] = []
    if (typeof reasons === 'string') {
        reasonArray = reasons.split(',').filter(item => item.trim() !== '')
    } else if (Array.isArray(reasons)) {
        reasonArray = reasons.filter(item => item && item.trim() !== '')
    }
    
    if (reasonArray.length === 0) return '-'
    
    const reasonTexts = reasonArray.map(reasonValue => {
        const option = terminateReasonOptions.value.find(opt => opt.dictValue === reasonValue)
        return option ? option.dictLabel : reasonValue
    }).filter(text => text)
    
    return reasonTexts.join('、')
}

// 加载字典数据
const loadDictData = async () => {
    try {
        const dictData = await useDictSync('terminate_reason')
        if (dictData.terminate_reason) {
            terminateReasonOptions.value = dictData.terminate_reason
        }
    } catch (error) {
        console.error('加载退租原因字典数据失败:', error)
    }
}

// 加载退租详情
const loadTerminateDetail = async () => {
    // 如果没有 terminateId 或者已经有数据，则不需要加载
    if (!props.terminateId || props.terminateData) {
        console.log('跳过加载退租详情:', { 
            hasTerminateId: !!props.terminateId, 
            hasTerminateData: !!props.terminateData 
        })
        return
    }
    
    try {
        loading.value = true
        console.log('开始加载退租详情:', props.terminateId)
        
        const res = await getContractTerminateDetail({ id: props.terminateId })
        
        if (res && res.code === 200) {
            terminateDetail.value = res.data
            console.log('退租详情加载成功:', res.data)
        } else {
            console.error('退租详情接口返回错误:', res)
        }
    } catch (error) {
        console.error('加载退租详情失败:', error)
        // 可以在这里添加用户友好的错误提示
        // Message.error('加载退租详情失败，请稍后重试')
    } finally {
        loading.value = false
    }
}

// 监听 props 变化
watch(() => props.terminateId, async (newId, oldId) => {
    if (newId !== oldId && newId) {
        console.log('terminateId 发生变化，重新加载数据:', { oldId, newId })
        await loadTerminateDetail()
    }
}, { immediate: false })

watch(() => props.terminateData, (newData, oldData) => {
    if (newData !== oldData) {
        console.log('terminateData 发生变化:', { 
            hasOldData: !!oldData, 
            hasNewData: !!newData 
        })
        // 如果直接传入了新数据，清空通过 API 加载的数据
        if (newData) {
            terminateDetail.value = undefined
        }
    }
}, { immediate: false })

// 生命周期
onMounted(async () => {
    await loadDictData()
    await loadTerminateDetail()
})

// 手动刷新数据
const refreshData = async () => {
    console.log('手动刷新退租详情数据')
    // 清空现有数据
    terminateDetail.value = undefined
    // 重新加载
    await loadTerminateDetail()
}

// 获取当前显示的数据（优先使用传入的数据）
const getCurrentData = () => {
    return props.terminateData || terminateDetail.value
}

// 暴露方法给父组件
defineExpose({
    loadTerminateDetail,
    refreshData,
    getCurrentData
})
</script>

<style scoped lang="less">
.contract-termination-display {
    width: 100%;
    //padding: 0 12px;

    .info-section {
        margin-bottom: 16px;
        
        .info-item {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            
            .label {
                color: #86909c;
                margin-right: 8px;
                min-width: 120px;
                text-align: right;
                flex-shrink: 0;
            }
            
            .value {
                color: #1d2129;
                flex: 1;
                text-align: left;
                word-break: break-all;
            }
            
            &.highlight-red .value {
                color: var(--color-danger);
            }
        }
    }

    .total-info {
        margin: 16px 0;
        padding: 12px;
        background-color: #f7f8fa;
        border-radius: 4px;
        
        span {
            font-size: 14px;
            
            strong {
                color: #165dff;
                font-weight: 600;
            }
        }
    }

    .attachment-section {
        margin-bottom: 24px;
    }

    :deep(.arco-table) {
        margin-bottom: 24px;
    }
}
</style> 