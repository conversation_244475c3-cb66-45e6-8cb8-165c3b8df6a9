<template>
  <a-modal
    v-model:visible="visible"
    title="选择新责任人"
    :width="800"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <!-- 查询区域 -->
    <div class="search-area">
      <a-row :gutter="16">
        <a-col :flex="1">
          <a-input
            v-model="searchForm.keyword"
            placeholder="请输入姓名/手机号/账号"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-col>
        <a-col :flex="'auto'">
          <a-button type="primary" @click="handleSearch">
            <template #icon><icon-search /></template>
            查询
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 表格区域 -->
    <div class="table-area">
      <a-table
        :columns="columns"
        :data="tableData"
        :pagination="pagination"
        :bordered="{ cell: true }"
        :stripe="true"
        :loading="loading"
        :scroll="{ x: 1 }"
        row-key="userId"
        :row-selection="rowSelection"
        v-model:selectedKeys="selectedMaintainerKeys"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #status="{ record }">
          <a-tag :color="record.status === '0' ? 'green' : 'red'">
            {{ record.status === '0' ? '启用' : '停用' }}
          </a-tag>
        </template>
      </a-table>
    </div>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk" :loading="confirmLoading">
          确定
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { IconSearch } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import { getUserList } from '@/api/user'

interface UserInfo {
  userId: number
  userName: string
  nickName: string
  phonenumber: string
  status: string
  postName: string | null
  dataPermissions: any | null
  menuPermissions: any | null
}

interface SearchForm {
  keyword: string
}

const emit = defineEmits(['success'])

const visible = ref(false)
const loading = ref(false)
const confirmLoading = ref(false)
const selectedCustomerIds = ref<string[]>([])

// 查询表单
const searchForm = reactive<SearchForm>({
  keyword: ''
})

// 表格数据
const tableData = ref<UserInfo[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
})

// 选中的维护人
const selectedMaintainerKeys = ref<string[]>([])

// 表格选择配置
const rowSelection = {
  type: 'radio',
  showCheckedAll: false,
}

// 表格列配置
const columns = [
  {
    title: '姓名',
    dataIndex: 'nickName',
    width: 100,
    ellipsis: true,
    tooltip: true,
    align: 'center'
  },
  {
    title: '账号',
    dataIndex: 'userName',
    width: 120,
    ellipsis: true,
    tooltip: true,
    align: 'center'
  },
  {
    title: '岗位',
    dataIndex: 'postName',
    width: 120,
    ellipsis: true,
    tooltip: true,
    align: 'center'
  },
  {
    title: '手机号',
    dataIndex: 'phonenumber',
    width: 130,
    ellipsis: true,
    tooltip: true,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 80,
    ellipsis: true,
    tooltip: true,
    align: 'center'
  }
]

// 获取用户列表
const getUserListData = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      searchParam: searchForm.keyword || undefined,
      status: 0, // 只查询正常状态的用户，使用数字类型
      menuRoles: [],
      dataRoles: []
    }
    
    const response = await getUserList(params)
    
    if (response) {
      tableData.value = response.rows || []
      pagination.total = response.total || 0
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 方法
const handleSearch = () => {
  pagination.current = 1
  getUserListData()
}

const onPageChange = (current: number) => {
  pagination.current = current
  getUserListData()
}

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  getUserListData()
}

const show = (customerIds: string[]) => {
  selectedCustomerIds.value = customerIds
  visible.value = true
  searchForm.keyword = ''
  selectedMaintainerKeys.value = []
  pagination.current = 1
  getUserListData() // 初始化加载数据
}

const handleCancel = () => {
  visible.value = false
  searchForm.keyword = ''
  selectedMaintainerKeys.value = []
  tableData.value = []
}

const handleOk = async () => {
  if (!selectedMaintainerKeys.value.length) {
    Message.warning('请选择维护人')
    return
  }
  
  const selectedMaintainer = tableData.value.find(item => 
    item.userId.toString() === selectedMaintainerKeys.value[0].toString()
  )
  
  if (!selectedMaintainer) {
    Message.error('选中的维护人不存在')
    return
  }
  
  try {
    confirmLoading.value = true
    
    // 传递正确的维护人信息给父组件
    const maintainerData = {
      customerIds: selectedCustomerIds.value,
      ownerId: selectedMaintainer.userId.toString(), // 确保是字符串类型
      ownerName: selectedMaintainer.nickName
    }
    
    // 触发成功事件，传递维护人信息和客户ID列表
    emit('success', maintainerData)
    
    visible.value = false
  } catch (error) {
    console.error('选择维护人失败:', error)
  } finally {
    confirmLoading.value = false
  }
}

defineExpose({
  show
})
</script>

<style scoped lang="less">
.search-area {
  margin-bottom: 16px;
  padding: 16px;
  background-color: var(--color-fill-1);
  border-radius: 4px;
}

.table-area {
  margin-top: 16px;
}

:deep(.arco-modal-body) {
  padding: 20px;
}

:deep(.arco-table-th) {
  background-color: var(--color-fill-2);
}
</style> 