---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/客户管理

<a id="opIdlist_8"></a>

## POST 客户列表查询接口

POST /customer/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "customerType": 0,
  "customerName": "string",
  "ownerName": "string",
  "createByName": "string",
  "creditCode": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CustomerQueryDTO](#schemacustomerquerydto)| 是 |none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","projectName":"string","customerName":"string","customerType":0,"creditCode":"string","legalName":"string","contactPhone":"string","idType":"string","idNumber":"string","idValidityStart":"2019-08-24T14:15:22Z","idValidityEnd":"2019-08-24T14:15:22Z","idFront":"string","idBack":"string","businessLicense":"string","contactAddress":"string","ownerId":"string","ownerName":"string","attachmentFiles":"string","remark":"string","createByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"contactList":[{"id":"string","customerId":"string","name":"string","phone":"string","gender":0,"relationship":0,"idNumber":"string","position":"string","department":"string","isPreferred":true,"remark":"string","createByName":"string","updateByName":"string","isDel":true}],"guarantorList":[{"id":"string","customerId":"string","name":"string","phone":"string","idType":0,"idNumber":"string","address":"string","remark":"string","createByName":"string","updateByName":"string","isDel":true}],"bankAccountList":[{"id":"string","customerId":"string","bankName":"string","accountNumber":"string","accountRemark":"string","createByName":"string","updateByName":"string","isDel":true}],"invoiceList":[{"id":"string","customerId":"string","title":"string","taxNumber":"string","phone":"string","address":"string","bankName":"string","accountNumber":"string","createByName":"string","updateByName":"string","isDel":true}]}]
```
