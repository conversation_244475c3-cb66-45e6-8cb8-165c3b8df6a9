import http from './index'

// 套打模板查询参数接口（基于最新接口文档）
export interface PrintTemplateQueryDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  id?: string
  templateName?: string
  projectId?: string
  applyLevel?: string // 适用层级（1集团 2项目）
  applyScope?: string // 适用范围
  printType?: string // 套打类型（1合同 2协议）
  contractPurpose?: string // 合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）
  effectiveDate?: string
  expirationDate?: string
  status?: string // 状态（0新增 1生效 2失效）
  linkUrl?: string
  remark?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
}

// 套打模板新增/修改参数接口（基于最新接口文档更新）
export interface PrintTemplateAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  templateName?: string
  // 【变更】删除了 projectId 字段，改为使用 projectIdList
  // projectId?: string
  applyLevel?: number // 【变更】类型从 string 改为 number - 适用层级（1集团 2项目）
  // 【变更】删除了 applyScope 字段
  // applyScope?: string
  printType?: number // 【变更】类型从 string 改为 number - 套打类型（1合同 2协议 3通知单）
  contractPurpose?: number // 【变更】类型从 string 改为 number - 合同/协议用途（根据套打类型有不同选项）
  effectiveDate?: string
  expirationDate?: string
  status?: number // 【变更】类型从 string 改为 number - 状态（0新增 1生效 2失效）
  linkUrl?: string
  remark?: string
  projectIdList?: string[] // 【新增】项目ID列表，替代原来的单个 projectId
}

// 套打模板状态修改参数接口（基于最新接口文档更新）
export interface PrintTemplateStatusDto {
  templateId: string // 模版id
  status: number // 【变更】类型从 string 改为 number - 模版状态
}

// 通用响应接口
export interface AjaxResult {
  error?: boolean
  success?: boolean
  warn?: boolean
  empty?: boolean
  [key: string]: any
}

// 分页数据接口
export interface TableDataInfo<T = any> {
  total?: number
  rows?: T[]
  code?: number
  msg?: string
}

// 查询打印模版列表
export function getTemplateList(data: PrintTemplateQueryDTO) {
  // 【变更】接口方法从 GET 改为 POST，参数从 query 改为 body
  return http.post<TableDataInfo>('/business-rent-admin/template/list', data)
}

// 获取打印模版详细信息
export function getTemplateDetail(templateId: string) {
  return http.get<AjaxResult>(`/business-rent-admin/template/detail?id=${templateId}`)
}

// 【新增】根据业务ID和模板ID获取打印模板
export function printTemplateById(bizId: string, templateId: string) {
  // 注意：接口URL中的拼写是 printTempalteById（Template拼写错误，但要与后端保持一致）
  return http.get<AjaxResult>(`/business-rent-admin/template/printTempalteById?bizId=${bizId}&templateId=${templateId}`)
}

// 新增打印模版
export function createTemplate(data: PrintTemplateAddDTO) {
  return http.post<AjaxResult>('/business-rent-admin/template', data)
}

// 修改打印模版
export function updateTemplate(data: PrintTemplateAddDTO) {
  return http.put<AjaxResult>('/business-rent-admin/template', data)
}

// 修改打印模版状态
export function updateTemplateStatus(data: PrintTemplateStatusDto) {
  return http.post<AjaxResult>('/business-rent-admin/template/updateStatus', data)
}

// 导出打印模版列表
export function exportTemplate(params: PrintTemplateQueryDTO) {
  // 【注意】导出接口虽然是POST方法，但参数作为query参数传递，不是body
  return http.post<Blob>('/business-rent-admin/template/export', {}, { 
    params,
    responseType: 'blob' 
  })
}

// 删除打印模版
export function deleteTemplate(id: string[]) {
  return http.delete<AjaxResult>('/business-rent-admin/template/delete', {ids: id})
}

// 字典查询参数接口
export interface DictQueryParams {
  name?: string
  type?: number
  code?: string
  pageNum: number
  pageSize: number
}

// 字典数据接口
export interface DictData {
  id: string
  parentId: string
  name: string
  type: number
  code: string
  dbField: string
  sqlText: string
  remark: string
  attachments?: Array<{
    fileId: string
    name: string
    url: string
  }>
  createBy: string
  createByName: string
  createTime: string
  updateBy: string
  updateByName: string
  updateTime: string
}

// 获取字段字典列表
export function getTemplateFieldsList(params: DictQueryParams) {
  return http.get<TableDataInfo<DictData>>('/business-rent-admin/template/dict/list', params)
}

// 获取打印模板字典详细信息
export function getTemplateDictDetail(id: string) {
  return http.get<AjaxResult>(`/business-rent-admin/template/dict/detail?id=${id}`)
}

// 创建字段字典
export function createTemplateDict(data: Partial<DictData>) {
  return http.post<AjaxResult>('/business-rent-admin/template/dict', data)
}

// 更新字段字典
export function updateTemplateDict(data: Partial<DictData>) {
  return http.put<AjaxResult>('/business-rent-admin/template/dict', data)
}

// 删除字段字典
export function deleteTemplateDict(id: string[]) {
  return http.delete<AjaxResult>(`/business-rent-admin/template/dict/delete`, {ids: id})
}

// 导出打印模板字典列表
export function exportTemplateDict(data: DictQueryParams) {
  return http.post<Blob>('/business-rent-admin/template/dict/export', data, { responseType: 'blob' })
}

// 通用查询接口
export function commonQuery(name: string, data: any) {
  return http.post<AjaxResult>(`/business-rent-admin/common/query?name=${name}`, data)
}

// 通用上传接口
export function commonUpload(name: string, data: any) {
  return http.post<AjaxResult>(`/business-rent-admin/common/upload?name=${name}`, data)
} 