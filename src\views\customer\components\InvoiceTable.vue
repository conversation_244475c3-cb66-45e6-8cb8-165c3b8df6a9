<template>
  <a-table
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :bordered="{ cell: true }"
    :scroll="{ x: 1 }"
    row-key="id"
  >
    <template #titleTitle>
      <span style="color: red;">*</span>抬头名称
    </template>
    
    <template #taxNumberTitle>
      <span style="color: red;">*</span>税号
    </template>
    
    <template #title="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model.trim="record.title" 
        placeholder="请输入抬头名称"
        size="small"
      />
      <span v-else>{{ record.title }}</span>
    </template>
    
    <template #taxNumber="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.taxNumber" 
        placeholder="请输入税号"
        size="small"
      />
      <span v-else>{{ record.taxNumber }}</span>
    </template>
    
    <template #address="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.address" 
        placeholder="请输入单位地址"
        size="small"
      />
      <span v-else>{{ record.address }}</span>
    </template>
    
    <template #phone="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.phone" 
        placeholder="请输入电话号码"
        size="small"
      />
      <span v-else>{{ record.phone }}</span>
    </template>
    
    <template #bankName="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.bankName" 
        placeholder="请输入开户银行"
        size="small"
      />
      <span v-else>{{ record.bankName }}</span>
    </template>
    
    <template #accountNumber="{ record, rowIndex }">
      <a-input 
        v-if="record.isEditing" 
        v-model="record.accountNumber" 
        placeholder="请输入银行账号"
        size="small"
      />
      <span v-else>{{ record.accountNumber }}</span>
    </template>
    
    <template #operations="{ record, rowIndex }">
      <a-space v-if="!readonly">
        <a-button 
          v-if="record.isEditing" 
          type="text" 
          size="small" 
          @click="saveRow(record, rowIndex)"
        >
          保存
        </a-button>
        <a-button 
          v-else
          type="text" 
          size="small" 
          @click="editRow(record)"
        >
          修改
        </a-button>
        
        <a-button 
          type="text" 
          size="small" 
          status="danger" 
          @click="deleteRow(rowIndex)"
        >
          删除
        </a-button>
      </a-space>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Message } from '@arco-design/web-vue'

interface Invoice {
  id: number
  title: string
  taxNumber: string
  address: string
  phone: string
  bankName: string
  accountNumber: string
  isEditing?: boolean
}

interface Props {
  modelValue: Invoice[]
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  readonly: false
})

const emit = defineEmits(['update:modelValue'])

const tableData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const columns = computed(() => {
  const baseColumns: any[] = [
    {
      title: '抬头名称',
      dataIndex: 'title',
      slotName: 'title',
      width: 140,
      titleSlotName: 'titleTitle',
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '税号',
      dataIndex: 'taxNumber',
      slotName: 'taxNumber',
      width: 180,
      titleSlotName: 'taxNumberTitle',
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '单位地址',
      dataIndex: 'address',
      slotName: 'address',
      width: 180,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '电话号码',
      dataIndex: 'phone',
      slotName: 'phone',
      width: 140,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '开户银行',
      dataIndex: 'bankName',
      slotName: 'bankName',
      width: 140,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    },
    {
      title: '银行账号',
      dataIndex: 'accountNumber',
      slotName: 'accountNumber',
      width: 180,
      ellipsis: true,
      tooltip: true,
      align: 'center'
    }
  ]
  
  // 只有非只读模式才显示操作列
  if (!props.readonly) {
    baseColumns.push({
      title: '操作',
      slotName: 'operations',
      width: 120,
      fixed: 'right',
      align: 'center'
    })
  }
  
  return baseColumns
})

const editRow = (record: Invoice) => {
  record.isEditing = true
}

const saveRow = (record: Invoice, index: number) => {
  if (!record.title) {
    Message.warning('请输入抬头名称')
    return
  }
  if (!record.taxNumber) {
    Message.warning('请输入税号')
    return
  }
  
  record.isEditing = false
  Message.success('保存成功')
}

const deleteRow = (index: number) => {
  const newData = [...tableData.value]
  newData.splice(index, 1)
  tableData.value = newData
  Message.success('删除成功')
}
</script>

<style scoped lang="less">
:deep(.arco-table-td) {
  padding: 8px !important;
}

:deep(.arco-input),
:deep(.arco-select),
:deep(.arco-textarea) {
  border: none;
  box-shadow: none;
}
</style> 