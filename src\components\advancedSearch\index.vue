<template>
    <div class="advanced-search-container">
        <div class="advanced-search-content" @click="toggle">
            <span class="advanced-search-content-text">高级检索</span>
            <icon-caret-down v-if="!visible" />
            <icon-caret-up v-else />
        </div>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  const emit = defineEmits(['toggle'])
  const visible = ref(false)
  function toggle() {
    visible.value = !visible.value
    emit('toggle', visible.value)
  }
  </script>
  
  <style scoped>
  .advanced-search-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 12px;
    .advanced-search-content{
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f0f0;
        font-size: 12px;
        color: rgb(var(--primary-6));
        padding: 4px 14px;
        border-radius: 2px;
        cursor: pointer;
        .advanced-search-content-text{
            margin-right: 4px;
        }
        &:hover{
            background: #e5e5e5;
        }
    }
  }
  </style>
  