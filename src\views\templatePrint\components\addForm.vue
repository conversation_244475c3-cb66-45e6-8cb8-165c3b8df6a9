<template>
    <a-drawer :visible="drawerVisible" :title="drawerTitle" :mask-closable="true" @cancel="handleCancel" class="common-drawer-small">
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" @click="handleSubmit">确认</a-button>
            </a-space>
        </template>
        <a-form 
            ref="formRef"
            :model="formData"
            :rules="rules"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
        >
            <section-title title="基础信息" style="margin-bottom: 16px;" />
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="templateName"
                        label="模板名称"
                        required
                        :rules="[{ required: true, message: '请输入模板名称' }]"
                    >
                        <a-input
                            v-model="formData.templateName"
                            placeholder="请输入模板名称"
                            allow-clear
                        />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="applyLevel"
                        label="适用层级"
                        required
                        :rules="[{ required: true, message: '请选择适用层级' }]"
                    >
                        <a-radio-group v-model="formData.applyLevel">
                            <a-radio :value="1">集团级</a-radio>
                            <a-radio :value="2">项目级</a-radio>
                        </a-radio-group>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16" v-if="formData.applyLevel === 2">
                <a-col :span="24">
                    <a-form-item
                        field="projectIdList"
                        label="适用项目"
                        required
                        :rules="[{ required: true, message: '请选择适用项目' }]"
                    >
                        <ProjectTreeSelect
                            v-model="formData.projectIdList"
                            :multiple="true"
                            placeholder="请选择适用项目"
                            allow-clear
                        />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="printType"
                        label="套打类型"
                        required
                        :rules="[{ required: true, message: '请选择套打类型' }]"
                    >
                        <!-- 【更新】使用数据字典数据 -->
                        <a-radio-group v-model="formData.printType">
                            <a-radio 
                                v-for="item in printTypeOptions" 
                                :key="item.value" 
                                :value="item.value"
                            >
                                {{ item.label }}
                            </a-radio>
                        </a-radio-group>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="contractPurpose"
                        label="用途"
                        required
                        :rules="[{ required: true, message: '请选择用途' }]"
                    >
                        <!-- 【更新】使用数据字典数据，根据套打类型动态显示 -->
                        <a-select v-model="formData.contractPurpose" placeholder="请选择用途">
                            <a-option 
                                v-for="item in currentPurposeOptions" 
                                :key="item.value" 
                                :value="item.value"
                            >
                                {{ item.label }}
                            </a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="effectiveDate"
                        label="生效日期"
                        required
                        :rules="[{ required: true, message: '请选择生效日期' }]"
                    >
                        <a-date-picker
                            v-model="formData.effectiveDate"
                            style="width: 100%"
                        />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="expirationDate"
                        label="失效日期"
                        required
                        :rules="[{ required: true, message: '请选择失效日期' }]"
                    >
                        <a-date-picker
                            v-model="formData.expirationDate"
                            style="width: 100%"
                        />
                    </a-form-item>
                </a-col>
            </a-row> -->

            <!-- <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="status"
                        label="状态"
                        required
                        :rules="[{ required: true, message: '请选择状态' }]"
                    >
                        <a-select v-model="formData.status" placeholder="请选择状态">
                            <a-option value="0">新增</a-option>
                            <a-option value="1">生效</a-option>
                            <a-option value="2">失效</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row> -->

            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="remark"
                        label="备注"
                    >
                        <a-textarea
                            v-model="formData.remark"
                            placeholder="请输入备注"
                            :auto-size="{ minRows: 3, maxRows: 5 }"
                        />
                    </a-form-item>
                </a-col>
            </a-row>

            <section-title title="模板上传" style="margin-bottom: 16px;" />
            <a-row :gutter="16">
                <a-col :span="24">
                    <a-form-item
                        field="linkUrl"
                        label="模板文件"
                        :rules="[{ required: !formData.id, message: '请上传模板文件' }]"
                    >
                        <upload-file 
                            v-model="formData.linkUrl"
                        />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import uploadFile from '@/components/upload/uploadFile.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import { createTemplate, updateTemplate, commonUpload } from '@/api/templatePrint'
import { TemplateData } from '@/views/templatePrint/types'
import { DictType, dictData } from '@/dict/data'

const emit = defineEmits(['success', 'cancel'])
const drawerVisible = ref(false)
const drawerTitle = ref('新增模板')
const formRef = ref()
const uploadedFiles = ref<any[]>([])

// 表单数据
const formData = reactive<Partial<TemplateData>>({
    id: '',
    templateName: '',
    applyLevel: 1, // 默认集团级
    printType: 1, // 默认合同
    contractPurpose: undefined,
    effectiveDate: '',
    expirationDate: '',
    status: 0, // 默认新增状态
    linkUrl: '',
    remark: '',
    projectIdList: [] // 新增项目ID列表
})

// 表单校验规则
const rules = computed(() => ({
    templateName: [{ required: true, message: '请输入模板名称' }],
    applyLevel: [{ required: true, message: '请选择适用层级' }],
    printType: [{ required: true, message: '请选择套打类型' }],
    contractPurpose: [{ required: true, message: '请选择用途' }],
    // 【修正】项目级时需要选择适用项目
    projectIdList: formData.applyLevel === 2 
        ? [{ required: true, message: '请选择适用项目' }] 
        : [],
    effectiveDate: [{ required: true, message: '请选择生效日期' }],
    expirationDate: [{ required: true, message: '请选择失效日期' }],
    status: [{ required: true, message: '请选择状态' }]
}))

// 【新增】从数据字典获取选项数据
const printTypeOptions = computed(() => dictData[DictType.TEMPLATE_PRINT_TYPE] || [])

// 【新增】根据选择的套打类型动态获取用途选项
const currentPurposeOptions = computed(() => {
	const printType = formData.printType
	if (printType === 1) {
		return dictData[DictType.TEMPLATE_PURPOSE_CONTRACT] || []
	} else if (printType === 2) {
		return dictData[DictType.TEMPLATE_PURPOSE_AGREEMENT] || []
	} else if (printType === 3) {
		return dictData[DictType.TEMPLATE_PURPOSE_NOTICE] || []
	}
	// 默认显示合同用途
	return dictData[DictType.TEMPLATE_PURPOSE_CONTRACT] || []
})

// 【新增】监听适用层级变化，集团级时清空项目选择
watch(() => formData.applyLevel, (newValue) => {
    if (newValue === 1) {
        // 集团级时清空项目选择
        formData.projectIdList = []
    }
})

// 【新增】监听套打类型变化，清空用途选择
watch(() => formData.printType, () => {
    // 套打类型变化时清空用途选择，让用户重新选择
    formData.contractPurpose = undefined
})

// 文件上传API
const uploadTemplateFile = async (formData: FormData) => {
    try {
        const res = await commonUpload('template', formData)
        return res
    } catch (error) {
        throw error
    }
}

// 上传成功处理
const handleUploadSuccess = (response: any, fileItem: any) => {
    console.log(response)
    // 保存文件链接到表单数据
//     {
//     "msg": "操作成功",
//     "code": 200,
//     "data": {
//         "fileName": "screenshot-20250521-150139.png",
//         "fileUrl": "nullnull/2025/05/22/screenshot-20250521-150139_20250522172631A002.png"
//     }
// }
    if (response && response.data) {
        formData.linkUrl = JSON.stringify(response.data)
    }
}

// 上传失败处理
const handleUploadError = (error: any, fileItem: any) => {
    console.error('文件上传失败:', error)
}

// 初始化表单数据
const initFormData = (record?: Partial<TemplateData>) => {
    if (record && record.id) {
        Object.assign(formData, record)
        
        // 如果有文件信息，需要设置文件列表
        // if (record.linkUrl) {
        //     uploadedFiles.value = [{
        //         fileId: record.id,
        //         name: record.templateName || '模板文件',
        //         url: record.linkUrl
        //     }]
        // } else {
        //     uploadedFiles.value = []
        // }
        if (!!record.linkUrl) {
            if (!record.linkUrl.includes('[')) {
                formData.linkUrl = JSON.stringify([JSON.parse(record.linkUrl)])
            } else {
                formData.linkUrl = record.linkUrl
            }
        }
    } else {
        // 新增时的默认值
        Object.assign(formData, {
            id: '',
            templateName: '',
            applyLevel: 1,
            printType: 1,
            contractPurpose: undefined,
            effectiveDate: '',
            expirationDate: '',
            status: 0,
            linkUrl: '',
            remark: '',
            projectIdList: [] // 新增项目ID列表
        })
        uploadedFiles.value = []
    }
    
    if (formRef.value) {
        formRef.value.clearValidate()
    }
}

// 取消
const handleCancel = () => {
    drawerVisible.value = false
    emit('cancel')
}

// 提交
const handleSubmit = async () => {
    try {
        // await formRef.value.validate()
        const errors = await formRef.value.validate() 
        if (errors) return
        
        if (formData.id) {
            // 编辑
            await updateTemplate(formData)
            Message.success('更新成功')
        } else {
            // 新增
            await createTemplate(formData)
            Message.success('新增成功')
        }
        
        emit('success')
        drawerVisible.value = false
    } catch (error) {
        console.error('提交失败:', error)
        Message.error('提交失败')
    }
}

// 暴露方法给父组件
defineExpose({
    open() {
        drawerTitle.value = '新增模板'
        drawerVisible.value = true
        initFormData()
    },
    edit(record: Partial<TemplateData>) {
        drawerTitle.value = '编辑模板'
        drawerVisible.value = true
        initFormData(record)
    }
})
</script>

<style scoped>
.upload-demo {
  width: 100%;
}
</style> 