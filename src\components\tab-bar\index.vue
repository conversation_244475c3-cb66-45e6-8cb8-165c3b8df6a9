<template>
    <div class="tab-bar-container">
        <a-affix ref="affixRef" :offset-top="offsetTop">
            <div class="tab-bar-box">
                <!-- 左滚动按钮 -->
                <div 
                    v-if="showScrollButtons" 
                    class="scroll-button scroll-left"
                    :class="{ disabled: !canScrollLeft }"
                    @click="handleScrollLeft"
                >
                    <icon-left />
                </div>
                
                <div class="tab-bar-scroll">
                    <div class="tags-wrap" ref="scrollContainer">
                        <TabItem
                            v-for="(tag, index) in tagList"
                            :key="tag.fullPath"
                            :index="index"
                            :itemData="tag"
                        />
                    </div>
                </div>
                
                <!-- 右滚动按钮 -->
                <div 
                    v-if="showScrollButtons" 
                    class="scroll-button scroll-right"
                    :class="{ disabled: !canScrollRight }"
                    @click="handleScrollRight"
                >
                    <icon-right />
                </div>
                
                <!-- <div class="tag-bar-operation"></div> -->
            </div>
        </a-affix>
    </div>
</template>

<script lang="ts" setup>
    import {
        ref,
        computed,
        watch,
        onMounted,
        onUnmounted,
        nextTick,
    } from 'vue';
    import type { RouteLocationNormalized } from 'vue-router';
    import { IconLeft, IconRight } from '@arco-design/web-vue/es/icon';
    import {
        listenerRouteChange,
        removeRouteListener,
    } from '@/utils/route-listener';
    import { useAppStore, useTabBarStore } from '@/store';
    import TabItem from './tab-item.vue';

    const appStore = useAppStore();
    const tabBarStore = useTabBarStore();

    const affixRef = ref();
    const scrollContainer = ref();
    const isReady = ref(false);
    
    // 滚动相关状态
    const showScrollButtons = ref(false);
    const canScrollLeft = ref(false);
    const canScrollRight = ref(false);
    const scrollStep = 200; // 每次滚动的距离

    const tagList = computed(() => {
        return tabBarStore.getTabList;
    });
    
    const offsetTop = computed(() => {
        return appStore.navbar ? 60 : 0;
    });

    // 检查是否需要显示滚动按钮
    const checkScrollButtons = () => {
        if (!scrollContainer.value) {
            showScrollButtons.value = false;
            return;
        }
        
        const container = scrollContainer.value;
        const containerWidth = container.clientWidth;
        const contentWidth = container.scrollWidth;
        const scrollLeft = container.scrollLeft;
        
        // 判断是否需要显示滚动按钮
        showScrollButtons.value = contentWidth > containerWidth;
        
        if (showScrollButtons.value) {
            // 判断是否可以向左滚动
            canScrollLeft.value = scrollLeft > 1; // 留一点余量
            // 判断是否可以向右滚动
            canScrollRight.value = scrollLeft < (contentWidth - containerWidth - 1);
        } else {
            canScrollLeft.value = false;
            canScrollRight.value = false;
        }
    };

    // 滚动到指定位置
    const scrollToPosition = (position: number) => {
        if (!scrollContainer.value) return;
        
        scrollContainer.value.scrollTo({
            left: position,
            behavior: 'smooth'
        });
        
        // 滚动后延迟检查状态
        setTimeout(() => {
            checkScrollButtons();
        }, 300);
    };

    // 向左滚动
    const handleScrollLeft = () => {
        if (!scrollContainer.value || !canScrollLeft.value) return;
        
        const currentScrollLeft = scrollContainer.value.scrollLeft;
        const newScrollLeft = Math.max(0, currentScrollLeft - scrollStep);
        scrollToPosition(newScrollLeft);
    };

    // 向右滚动
    const handleScrollRight = () => {
        if (!scrollContainer.value || !canScrollRight.value) return;
        
        const currentScrollLeft = scrollContainer.value.scrollLeft;
        const containerWidth = scrollContainer.value.clientWidth;
        const contentWidth = scrollContainer.value.scrollWidth;
        const maxScrollLeft = contentWidth - containerWidth;
        const newScrollLeft = Math.min(maxScrollLeft, currentScrollLeft + scrollStep);
        scrollToPosition(newScrollLeft);
    };

    // 滚动到当前活跃的标签
    const scrollToActiveTab = () => {
        if (!scrollContainer.value || !showScrollButtons.value) return;
        
        const activeTab = scrollContainer.value.querySelector('.link-activated');
        if (!activeTab) return;
        
        const container = scrollContainer.value;
        const containerWidth = container.clientWidth;
        const containerScrollLeft = container.scrollLeft;
        const tabOffsetLeft = (activeTab as HTMLElement).offsetLeft;
        const tabWidth = (activeTab as HTMLElement).offsetWidth;
        
        // 检查标签是否在可视区域内
        const tabLeft = tabOffsetLeft;
        const tabRight = tabOffsetLeft + tabWidth;
        const visibleLeft = containerScrollLeft;
        const visibleRight = containerScrollLeft + containerWidth;
        
        // 如果标签不在可视区域内，滚动到合适位置
        if (tabLeft < visibleLeft) {
            // 标签在左侧不可见，滚动到标签位置
            scrollToPosition(Math.max(0, tabLeft - 20));
        } else if (tabRight > visibleRight) {
            // 标签在右侧不可见，滚动到标签右侧可见
            scrollToPosition(tabRight - containerWidth + 20);
        }
    };

    // 监听滚动事件
    const handleScroll = () => {
        checkScrollButtons();
    };

    // 监听窗口大小变化
    const handleResize = () => {
        nextTick(() => {
            checkScrollButtons();
        });
    };

    // 键盘事件处理
    const handleKeydown = (event: KeyboardEvent) => {
        // 只有在按住Ctrl键时才响应左右箭头键
        if (event.ctrlKey && showScrollButtons.value) {
            if (event.key === 'ArrowLeft') {
                event.preventDefault();
                handleScrollLeft();
            } else if (event.key === 'ArrowRight') {
                event.preventDefault();
                handleScrollRight();
            }
        }
    };

    onMounted(() => {
        isReady.value = true;
        
        // 添加事件监听
        if (scrollContainer.value) {
            scrollContainer.value.addEventListener('scroll', handleScroll);
        }
        window.addEventListener('resize', handleResize);
        window.addEventListener('keydown', handleKeydown);
        
        // 初始检查 - 延迟执行确保DOM完全渲染
        setTimeout(() => {
            checkScrollButtons();
        }, 100);
        
        // 使用ResizeObserver监听容器大小变化
        if (scrollContainer.value && window.ResizeObserver) {
            const resizeObserver = new ResizeObserver(() => {
                checkScrollButtons();
            });
            resizeObserver.observe(scrollContainer.value);
            
            // 在组件卸载时清理
            onUnmounted(() => {
                resizeObserver.disconnect();
            });
        }
    });

    onUnmounted(() => {
        // 移除事件监听
        if (scrollContainer.value) {
            scrollContainer.value.removeEventListener('scroll', handleScroll);
        }
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('keydown', handleKeydown);
        removeRouteListener();
    });

    // 监听标签列表变化
    watch(tagList, () => {
        nextTick(() => {
            checkScrollButtons();
            // 延迟一点时间确保DOM更新完成后再滚动
            setTimeout(() => {
                scrollToActiveTab();
            }, 100);
        });
    }, { deep: true });

    watch(
        () => appStore.navbar,
        () => {
            if (isReady.value && affixRef.value) {
                nextTick(() => {
                    affixRef.value?.updatePosition();
                });
            }
        }
    );
    
    listenerRouteChange((route: RouteLocationNormalized) => {
        // console.log('route', route);
        if (
            !route.meta.noAffix &&
            !tagList.value.some((tag) => tag.fullPath === route.fullPath)
        ) {
            tabBarStore.updateTabList(route);
        }
        
        // 路由变化后，滚动到当前活跃的标签
        nextTick(() => {
            scrollToActiveTab();
        });
    }, true);
</script>

<style scoped lang="less">
    .tab-bar-container {
        height: 33px;
        overflow: hidden;
        position: relative;
        background-color: var(--color-bg-2);
        // margin-bottom: 16px;

        .tab-bar-box {
            display: flex;
            padding: 0 0 0 16px;
            background-color: var(--color-bg-2);
            border-bottom: 1px solid var(--color-border);

            .scroll-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                cursor: pointer;
                color: var(--color-text-2);
                background-color: var(--color-bg-2);
                // border: 1px solid var(--color-border);
                transition: all 0.2s ease;
                flex-shrink: 0;
                position: relative;
                z-index: 1;

                &:hover:not(.disabled) {
                    color: rgb(var(--primary-6));
                    background-color: var(--color-fill-2);
                    border-color: rgb(var(--primary-6));
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                &:active:not(.disabled) {
                    transform: translateY(0);
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                }

                &.disabled {
                    cursor: not-allowed;
                    color: var(--color-text-4);
                    opacity: 0.5;
                    background-color: var(--color-fill-1);
                }

                &.scroll-left {
                    border-right: none;
                    border-radius: 4px 0 0 4px;
                    margin-left: -16px;
                }

                &.scroll-right {
                    border-left: none;
                    border-radius: 0 4px 4px 0;
                    margin-right: 8px;
                }

                // 添加图标大小
                .arco-icon {
                    font-size: 14px;
                }
            }

            .tab-bar-scroll {
                height: 32px;
                flex: 1;
                overflow: hidden;
                position: relative;

                .tags-wrap {
                    padding: 4px 0;
                    height: 34px;
                    white-space: nowrap;
                    overflow-x: auto;
                    overflow-y: hidden;
                    scrollbar-width: none; /* Firefox */
                    -ms-overflow-style: none; /* IE and Edge */
                    display: flex;
                    align-items: center;

                    &::-webkit-scrollbar {
                        display: none; /* Chrome, Safari, Opera */
                    }

                    :deep(.arco-tag) {
                        display: inline-flex;
                        align-items: center;
                        margin-right: 6px;
                        cursor: pointer;
                        flex-shrink: 0;

                        &:first-child {
                            .arco-tag-close-btn {
                                display: none;
                            }
                        }
                    }
                }
            }
        }

        .tag-bar-operation {
            width: 100px;
            height: 32px;
        }
    }
</style>
