<template>
    <router-view v-slot="{ Component, route }">
        <!-- {{ JSON.stringify(cacheList) }} {{ !!route.meta.ignoreCache }} {{ route.fullPath }} -->
        <transition name="fade-transform" mode="out-in" appear>
            <!-- v-if="!!route.meta.noCache" -->
            <component :is="Component" :key="route.fullPath" />
            <!-- :include="cacheList" -->
            <!-- <keep-alive v-else :include="cacheList">
                <component :is="createComponentWrapper(Component, route)" :key="route.fullPath" />
            </keep-alive> -->
        </transition>
    </router-view>
</template>

<script lang="ts" setup>
import { computed, h } from 'vue';
import { useTabBarStore } from '@/store';
import { useRoute } from 'vue-router';

const tabBarStore = useTabBarStore();

const cacheList = computed(() => {
    return tabBarStore.getCacheList;
    // const list = tabBarStore.getCacheList;
    // // if (!list.includes('systemUserRouter')) {
    // // 	list.push('systemUserRouter');
    // // }
    // // if (!list.includes('systemRoleRouter')) {
    // // 	list.push('systemRoleRouter');
    // // }
    // return list.join(',');
});

// 解决详情页 keep-alive 问题
const wrapperMap = new Map();
function createComponentWrapper(component: any, route: any) {
    if (!component) return;
    const wrapperName = route.fullPath;
    let wrapper = wrapperMap.get(wrapperName);
    if (!wrapper) {
        wrapper = { name: wrapperName, render: () => h(component) };
        wrapperMap.set(wrapperName, wrapper);
    }
    return h(wrapper);
}

const route = useRoute();
</script>

<style scoped lang="less"></style>
