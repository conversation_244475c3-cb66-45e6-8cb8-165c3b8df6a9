<template>
	<div class="template-fields-container">
		<a-card class="general-card">
			<!-- 搜索区域 -->
			<a-row>
				<a-col :flex="1">
					<a-form :model="queryParams" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" label-align="right">
						<a-row :gutter="16">
							<a-col :span="8">
								<a-form-item field="name" label="字段名称">
									<a-input v-model="queryParams.name" placeholder="请输入字段名称" allow-clear />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="type" label="字典类型">
									<a-select v-model="queryParams.type" placeholder="请选择字典类型" allow-clear>
										<a-option :value="0">字段</a-option>
										<a-option :value="1">表格</a-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="code" label="模板标识符">
									<a-input v-model="queryParams.code" placeholder="请输入模板标识符" allow-clear />
								</a-form-item>
							</a-col>
						</a-row>
					</a-form>
				</a-col>
				<a-divider style="height: 84px" direction="vertical" />
				<a-col :flex="'86px'" style="text-align: right">
					<a-space direction="vertical" :size="18">
						<a-button type="primary" @click="handleQuery">
							<template #icon>
								<icon-search />
							</template>
							查询
						</a-button>
						<a-button @click="resetQuery">
							<template #icon>
								<icon-refresh />
							</template>
							重置
						</a-button>
					</a-space>
				</a-col>
			</a-row>
			<a-divider style="margin-top: 0" />

			<!-- 数据表格 -->
			<a-row style="margin-bottom: 16px">
				<a-col :span="24" style="text-align: right">
					<a-space>
						<a-button v-permission="['print:template:dict:add']" type="primary" @click="handleAdd">
							<template #icon>
								<icon-plus />
							</template>
							新增字段
						</a-button>
					</a-space>
				</a-col>
			</a-row>
			<a-table :columns="columns" :data="tableData" :loading="loading" :pagination="pagination"
				@page-change="onPageChange" @page-size-change="onPageSizeChange" :bordered="{ cell: true }" :scroll="{ x: 1 }">
                <template #index="{ rowIndex }">
                    {{
                        rowIndex +
                        1 +
                        (pagination.current - 1) * pagination.pageSize
                    }}
                </template>
				<!-- 字典类型 -->
				<template #type="{ record }">
					{{ record.type === 0 ? '字段' : '表格' }}
				</template>
				
				<!-- 操作列 -->
				<template #operations="{ record }">
					<a-space>
						<a-button v-permission="['print:template:dict:edit']" type="text" @click="handleEdit(record)" size="mini">编辑</a-button>
						<a-popconfirm content="确定要删除该字段吗？" @ok="handleDelete(record)">
							<a-button v-permission="['print:template:dict:remove']" type="text" status="danger" size="mini">删除</a-button>
						</a-popconfirm>
					</a-space>
				</template>
			</a-table>
		</a-card>

		<!-- 新增/编辑字段抽屉 -->
		<dict-form ref="formRef" @success="onSuccess" @cancel="onCancel" />
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconSearch, IconRefresh, IconPlus } from '@arco-design/web-vue/es/icon'
import {
	getTemplateFieldsList,
	deleteTemplateDict
} from '@/api/templatePrint'
import { DictData, DictQueryParams } from './types'
import dictForm from './components/dictForm.vue'

// 查询参数
const queryParams = reactive<Partial<DictQueryParams>>({
	name: '',
	type: undefined,
	code: '',
})

// 表格列定义
// 序号


// 字典名称


// 字典类型


// 模版对应标识


// 套打类型


// 合同/协议用途


// 字典解释说明
const columns = [
{ title: '序号', dataIndex: 'index', slotName: 'index', width: 80, align: 'center', ellipsis: true, tooltip: true },
{ title: '字典名称', dataIndex: 'name' ,width: 180, ellipsis: true, tooltip: true, align: 'center'},
	{ title: '字典类型', dataIndex: 'type', slotName: 'type' ,width: 100,ellipsis: true, tooltip: true, align: 'center'},
	{ title: '模版对应标识', dataIndex: 'code' ,width: 180, ellipsis: true, tooltip: true, align: 'center'},
	{ title: '套打类型', dataIndex: 'printType' ,width: 100,ellipsis: true, tooltip: true, align: 'center'},
	{ title: '合同/协议用途', dataIndex: 'contractPurpose' ,width: 180, ellipsis: true, tooltip: true, align: 'center'},
	// { title: '数据库字段', dataIndex: 'dbField' ,width: 180, ellipsis: true, tooltip: true},
	{ title: '字典解释说明', dataIndex: 'remark' ,width: 180, ellipsis: true, tooltip: true, align: 'center'},
	// { title: '创建人', dataIndex: 'createByName' ,width: 100},
	// { title: '创建时间', dataIndex: 'createTime' },
	{ title: '操作', slotName: 'operations', width: 200, align: 'center',fixed: 'right',ellipsis: true, tooltip: true}
]

// 表格数据
const tableData = ref<DictData[]>([])
const loading = ref(false)
const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showTotal: true,
	showJumper: true,
	showPageSize: true
})

// 表单引用
const formRef = ref()

// 获取表格数据
const getList = async () => {
	loading.value = true
	try {
		const res = await getTemplateFieldsList({
			...queryParams,
			pageNum: pagination.current,
			pageSize: pagination.pageSize
		} as DictQueryParams)
		
		if (res && res.rows) {
			tableData.value = res.rows || []
			pagination.total = res.total || 0
		}
	} catch (error) {
		console.error('获取字段列表失败:', error)
		// Message.error('获取字段列表失败')
	} finally {
		loading.value = false
	}
}

// 查询
const handleQuery = () => {
	pagination.current = 1
	getList()
}

// 重置查询
const resetQuery = () => {
	// 重置所有查询条件
	Object.keys(queryParams).forEach(key => {
		queryParams[key as keyof typeof queryParams] = undefined
	})
	// 重新查询
	handleQuery()
}

// 分页方法
const onPageChange = (current: number) => {
	pagination.current = current
	getList()
}

const onPageSizeChange = (pageSize: number) => {
	pagination.pageSize = pageSize
	getList()
}

// 新增字段
const handleAdd = () => {
	formRef.value?.open()
}

// 编辑字段
const handleEdit = (record: any) => {
	console.log(record)
	formRef.value?.edit(record)
}

// 删除字段
const handleDelete = async (record: any) => {
	try {
		await deleteTemplateDict(record.id)
		Message.success('删除成功')
		getList()
	} catch (error) {
		console.error('删除失败:', error)
		// Message.error('删除失败')
	}
}

// 操作成功回调
const onSuccess = () => {
	getList()
}

// 取消回调
const onCancel = () => {
	console.log('取消操作')
}

// 页面加载时获取数据
onMounted(() => {
	getList()
})
</script>

<style scoped>
.template-fields-container {
	padding: 16px 0;
}

.general-card {
	box-sizing: border-box;
}
</style>