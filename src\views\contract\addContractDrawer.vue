<template>
    <a-drawer class="common-drawer" v-model:visible="drawerVisible" :title="drawerTitle" :body-style="{ padding: 0 }"
        unmount-on-close>
        <a-card class="flex-card" :bordered="false" :header-style="{ border: 'none' }"
            :body-style="{ padding: '0 16px' }">
            <template #title>
                <a-steps type="arrow" :current="current" small>
                    <a-step v-for="(item, index) in steps" :key="index">
                        <icon-check-circle-fill v-if="current >= (index + 1)" size="24" />
                        <icon-more v-else size="20"
                            style="background-color: #97A3AA; border-radius: 50%; color: #fff;" />
                        {{ item }}
                    </a-step>
                </a-steps>
            </template>
            <a-card v-if="contractStore.editType === 'change' || contractStore.editType === 'changeDetail'"
                :bordered="false" :body-style="{ padding: '0' }">
                <changeInfo ref="changeInfoRef" />
            </a-card>
            <baseInfo v-if="drawerVisible && current === 1" ref="baseInfoRef" />
            <receivablePlan v-if="drawerVisible && current === 2" ref="receivablePlanRef" />
            <!-- 合同预览组件 -->
            <ContractPreview v-if="current === 3 && !isDetailMode" :file-url="printFileUrl" />
        </a-card>
        <template #footer>
            <a-space v-if="!isDetailMode && current < 3">
                <a-button v-if="current < 3" @click="() => handleSave('save').catch(() => {})" :loading="saveLoading">暂存</a-button>
                <a-button v-if="current >= 2" @click="current--" type="primary">上一步</a-button>
                <a-button v-if="current === 2" @click="() => handlePreview().catch(() => {})" type="primary"
                    :loading="printLoading">预览&打印</a-button>
                <a-button v-if="current < 2" type="primary" @click="() => handleReceive().catch(() => {})"
                    :loading="generateLoading">生成应收计划</a-button>
            </a-space>
            <!-- 预览页面的操作按钮 -->
            <a-space v-else-if="!isDetailMode && current === 3">
                <a-button @click="handlePreviewBack">上一步</a-button>
                <a-button type="primary" @click="() => handleSubmit().catch(() => {})" :loading="submitLoading">提交审批</a-button>
            </a-space>
            <!-- 查看模式的按钮 -->
            <a-space v-else>
                <a-button v-if="current >= 2" @click="current--" type="primary">上一步</a-button>
                <a-button v-if="current < 2" type="primary" @click="current++">下一步</a-button>
            </a-space>
        </template>

        <!-- 使用模版选择组件 -->
        <TemplateSelectModal v-model="templateModalVisible" :contract-id="contractStore.contractData.id || ''"
            @select="handleTemplateSelected" />
    </a-drawer>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue';
import baseInfo from './addContract/baseInfo/index.vue';
import receivablePlan from './addContract/receivablePlan/index.vue';
import { useContractStore } from '@/store/modules/contract/index';
// 导入合同API接口和类型定义
import { saveContract, getContractById, ContractAddDTO, ContractVo, getContractPermission, generateCost, saveContractChange, print, PrintVo, getContractTemplateList } from '@/api/contract';
import { getProjectDetail } from '@/api/project'
import dayjs from 'dayjs';
import useUserStore from '@/store/modules/user';
import changeInfo from './addContract/changeInfo/index.vue'
// 导入模版选择组件 - 移到前面
import TemplateSelectModal from './components/TemplateSelectModal.vue'
// 导入合同预览组件
import ContractPreview from './components/ContractPreview.vue'

// 引用子组件
const baseInfoRef = ref();
const receivablePlanRef = ref();
const changeInfoRef = ref();

const drawerVisible = ref(false);

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData as ContractAddDTO)
const contractCosts = computed(() => contractStore.contractCosts)
const isDetailMode = computed(() => contractStore.editType === 'detail' || contractStore.editType === 'changeDetail')

const userStore = useUserStore()

// 标识是否是生成应收计划后重新获取详情
const isAfterGeneratePlan = ref(false)

// 续签时保存的合同ID，用于预览和打印
// 在变量定义区域添加变更合同ID变量
const renewContractId = ref('')
const changeContractId = ref('')

const templateModalVisible = ref(false)
// 添加WordViewer相关变量
const wordViewerVisible = ref(false)
const printFileUrl = ref('')
const printLoading = ref(false)

// 打开抽屉，设置编辑类型，并初始化数据
const open = async (type = 'create', contractType: number = 1, record: any, project?: any, fromDetailPage: boolean = false) => {
    current.value = 1
    contractStore.editType = type
    drawerVisible.value = true
    
    // 对于变更类型，在resetContract之前保存changeType
    const savedChangeType = (type === 'change' || type === 'changeDetail') ? [...contractStore.changeType] : []
    contractStore.resetContract()
    
    // 如果是变更类型且有保存的changeType，恢复它
    if ((type === 'change' || type === 'changeDetail') && savedChangeType.length > 0) {
        contractStore.changeType = savedChangeType
    }

    // 重置续签合同ID
    renewContractId.value = ''
    // 重置变更合同ID
    changeContractId.value = ''

    contractData.value.activityDate = []
    contractData.value.signerName = userStore.nickName
    contractData.value.signerId = userStore.id
    contractData.value.contractType = contractType
    contractData.value.signDate = dayjs().format('YYYY-MM-DD')
    contractData.value.handoverDate = dayjs().format('YYYY-MM-DD')
    contractData.value.projectId = type === 'create' ? project?.id : record.projectId
    contractData.value.projectName = type === 'create' ? project?.name : record.projectName
    contractStore.setCurrentProjectId(contractData.value.projectId as string)

    //保证金信息  应收日期默认写死字段
    // contractData.value.bondReceivableType = 0
    // contractData.value.bondReceivableDate = '3'

    // 重置标识
    isAfterGeneratePlan.value = false
    getUserPermission()
    getProjectDetailData(contractData.value.projectId as string)

    // 如果是直接签约且record包含房源信息（从租控管理的租客签约进来）
    if (type === 'create' && record && project && record.isDirectContract) {
        // 存储房源ID到store中，供roomInfo组件使用
        contractStore.preSelectedRoomId = record.roomId
    }
    // 如果是从订单转签约(record有订单信息且project存在)
    else if (type === 'create' && record && project && record.id && record.isRenewalContract) {
        // 存储房源ID到store中，供roomInfo组件使用
        contractStore.preSelectedRoomId = record.roomId
        contractStore.editType = 'toSign'
        // 设置关联订单信息
        contractData.value.bookingRelType = 2 // 其他定单
        contractData.value.bookings = [{
            bookingId: record.id,
            bookedRoom: record.roomName || record.intentRoom,
            bookerName: record.customerName,
            bookingReceivedAmount: record.receivedAmount || record.depositAmount || record.bookingAmount,
            bookingPaymentDate: record.receivedDate || record.createTime,
        }]

        // 设置客户信息
        // if (record.customerName) {
        //     contractData.value.customer = {
        //         customerName: record.customerName,
        //         // 其他客户信息可以根据需要添加
        //     }
        // }
    }

    if (type === 'renew' || type === 'change' || type === 'edit' || type === 'changeDetail') {
        // 续签或变更合同，需要获取原合同详情
        if (record.id) {
            fetchContractDetail(record.id, type, fromDetailPage);
        }
    }
}

const landUsageMap = new Map([
    [1, '工业用地'],
    [2, '商业用地'],
    [3, '商住用地']
])
const getProjectDetailData = async (projectId: string) => {
    const { data } = await getProjectDetail(projectId)
    contractStore.landUsageList = data.landUsageList ? data.landUsageList.map((item: any) => ({
        label: landUsageMap.get(item),
        value: item
    })) : []
    contractStore.merchantList = data.merchantList ?? []
    
    // 如果是新增合同，设置默认值
    if (contractStore.editType === 'create' && data.merchantList && data.merchantList.length > 0) {
        contractData.value.ourSigningId = data.merchantList[0].id
        contractData.value.ourSigningParty = data.merchantList[0].orgCompanyName
    }
    
    // 修复：如果已有 ourSigningParty 但没有 ourSigningId，根据公司名称查找对应的ID
    if (contractData.value.ourSigningParty && !contractData.value.ourSigningId && data.merchantList && data.merchantList.length > 0) {
        const merchant = data.merchantList.find(item => item.orgCompanyName === contractData.value.ourSigningParty)
        if (merchant) {
            contractData.value.ourSigningId = merchant.id
        }
    }
    
    if (contractStore.landUsageList.length > 0) {
        contractData.value.landUsage = contractStore.landUsageList[0].label
    }
}

// 获取用户相关权限
const getUserPermission = async () => {
    const { data } = await getContractPermission();
    // TODO: 非标合同权限
    data.canSelectNonStandard = true
    contractStore.contractPermission = data
}

// 获取合同详情
const fetchContractDetail = async (id: string, type: string, fromDetailPage: boolean) => {
    try {
        const { data } = await getContractById(id);
        if (data) {
            // 更新合同数据
            const contractInfo = data as ContractVo;
            
            contractInfo.fees = contractInfo.fees || []
            // 对优惠条款按开始日期正序排列
            if (contractInfo.fees.length > 0) {
                contractInfo.fees.sort((a, b) => {
                    const dateA = dayjs(a.startDate)
                    const dateB = dayjs(b.startDate)
                    return dateA.isBefore(dateB) ? -1 : 1
                })
            }
            
            contractInfo.costs = contractInfo.costs || []
            contractInfo.rooms = contractInfo.rooms || []
            contractInfo.rentReceivableDate = Number(contractInfo.rentReceivableDate)
            if (contractInfo.dailyActivityEndTime && contractInfo.dailyActivityStartTime) {
                (contractInfo as any).activityDate = [contractInfo.dailyActivityStartTime, contractInfo.dailyActivityEndTime]
            }

            // 处理续签或变更的特殊逻辑
            if (contractStore.editType === 'renew') {
                // 续签时设置源合同ID
                const renewData = {
                    ...contractStore.contractData,
                    ...contractInfo as any,
                    originId: contractInfo.originId || contractInfo.id, // 保持原始的originId
                    signType: 1, // 续签类型
                    // 复制其他需要保留的字段
                    signDate: dayjs().format('YYYY-MM-DD'),
                    handoverDate: dayjs().format('YYYY-MM-DD'),
                    // 关键修改：如果已经有renewContractId，则使用它，否则清空id
                    id: renewContractId.value || contractStore.contractData.id || undefined,
                    // 续签时清空优惠条款
                    fees: [],
                    // 续签的时候清空contractNo和unionId
                    contractNo: undefined,
                    unionId: undefined
                }

                // 只有首次进入续签时才设置开始日期和清空结束日期
                if (!isAfterGeneratePlan.value && !renewContractId.value) {
                    // 开始日期为原合同结束日期+1
                    renewData.startDate = dayjs(contractInfo.endDate).add(1, 'day').format('YYYY-MM-DD')
                    renewData.endDate = ''
                    // 清空租赁日期的年月日字段
                    renewData.rentYear = undefined
                    renewData.rentMonth = undefined
                    renewData.rentDay = undefined
                } else {
                    // 生成应收计划后或已有合同ID时，保持当前页面的开始日期和结束日期不变
                    renewData.startDate = contractStore.contractData.startDate
                    renewData.endDate = contractStore.contractData.endDate
                    renewData.rentYear = contractStore.contractData.rentYear
                    renewData.rentMonth = contractStore.contractData.rentMonth
                    renewData.rentDay = contractStore.contractData.rentDay
                }

                contractStore.contractData = renewData
                
                // 修复：根据 ourSigningParty 查找对应的 ourSigningId
                if (contractInfo.ourSigningParty && contractStore.merchantList.length > 0) {
                    const merchant = contractStore.merchantList.find(item => item.orgCompanyName === contractInfo.ourSigningParty)
                    if (merchant) {
                        contractStore.contractData.ourSigningId = merchant.id
                    }
                }
            }
            else if (contractStore.editType === 'change' || contractStore.editType === 'changeDetail') {
                // 变更时设置源合同ID
                const { changeType: _, ...contractInfoWithoutChangeType } = contractInfo as any;
                const changeData = {
                    ...contractStore.contractData,
                    ...contractInfoWithoutChangeType,
                    // 复制其他需要保留的字段
                    signDate: dayjs().format('YYYY-MM-DD'),
                    handoverDate: dayjs().format('YYYY-MM-DD'),
                }
                
                // 处理ID逻辑
                if (contractStore.editType === 'change') {
                    // 从合同详情页面编辑进入的变更：不清空id，保持原有逻辑，从详情新增变更进来的也是一个新的变更
                    if (fromDetailPage) {
                        changeData.id = contractInfo.id;
                    } else {
                        // 从列表进入或者从详情新增进入的变更：如果已经有changeContractId，则使用它，否则清空id
                        changeData.id = changeContractId.value || contractStore.contractData.id || undefined;
                        changeData.changeFromId = contractInfo.id
                        // 新变更时清空变更说明，避免带出上次变更说明
                        changeData.changeExplanation = '';
                    }
                } else if (contractStore.editType === 'changeDetail') {
                    // 从详情进入的变更：保留原有id
                    changeData.id = contractInfo.changeType ? contractInfo.id : undefined;
                }
                
                // 使用Object.assign确保响应式更新
                Object.assign(contractStore.contractData, changeData);
                
                // 修复：根据 ourSigningParty 查找对应的 ourSigningId
                if (contractInfo.ourSigningParty && contractStore.merchantList.length > 0) {
                    const merchant = contractStore.merchantList.find(item => item.orgCompanyName === contractInfo.ourSigningParty)
                    if (merchant) {
                        contractStore.contractData.ourSigningId = merchant.id
                    }
                }
                
                // 处理changeType的设置
                if (contractStore.editType === 'changeDetail') {
                    // 查看变更详情：直接使用contractInfo中的changeType
                    if (contractInfo.changeType) {
                        contractStore.contractData.changeType = contractInfo.changeType
                        // 同时设置 store 中的 changeType 数组，供组件使用
                        contractStore.changeType = contractInfo.changeType.split(',').map(Number)
                    }
                } else if (contractStore.editType === 'change') {
                    // 变更模式
                    if (contractInfo.changeType && fromDetailPage) {
                        // 编辑已有变更（从详情页面进入）：使用contractInfo中的changeType
                        contractStore.contractData.changeType = contractInfo.changeType
                        // 同时设置 store 中的 changeType 数组，供组件使用
                        contractStore.changeType = contractInfo.changeType.split(',').map(Number)
                    } else {
                        // 新增变更（从弹窗进入）：使用弹窗选择的changeType，不被接口数据覆盖
                        if (contractStore.changeType.length > 0) {
                            contractStore.contractData.changeType = contractStore.changeType.join(',')
                        }
                    }
                }
            } else {
                // 编辑时直接使用原数据
                contractStore.contractData = { ...contractStore.contractData, ...contractInfo as any };
                
                // 修复：根据 ourSigningParty 查找对应的 ourSigningId
                if (contractInfo.ourSigningParty && contractStore.merchantList.length > 0) {
                    const merchant = contractStore.merchantList.find(item => item.orgCompanyName === contractInfo.ourSigningParty)
                    if (merchant) {
                        contractStore.contractData.ourSigningId = merchant.id
                    }
                }
            }
            
            // 对于查看模式（detail 或 changeDetail），确保 costs 数据被正确设置到 contractCosts 中
            if (type === 'detail' || type === 'changeDetail') {
                contractStore.contractCosts = {
                    costs: contractInfo.costs || [],
                    bookings: contractInfo.bookings || []
                };
            }
            
            // //保证金信息  应收日期默认写死字段
            // contractStore.contractData.bondReceivableType = 0
            // contractStore.contractData.bondReceivableDate = '3'
            
            // 合同变更新增的时候-变更执行日期需要默认当天，当前是空的 ,合同变更编辑的时候，则不要做此处理    
            if (type === 'change' && !fromDetailPage) {
                contractStore.contractData.changeDate = dayjs().format('YYYY-MM-DD')
            }
            
            // 设置变更来源合同ID
            if (contractStore.editType === 'change' && !fromDetailPage && contractInfo.id) {
                changeContractId.value = contractInfo.id
            }
        }
    } catch (error) {
        console.error(error);
    }
};

// 抽屉标题
const drawerTitle = computed(() => {
    switch (contractStore.editType) {
        case 'create':
            return '新增合同';
        case 'renew':
            return '续签合同';
        case 'change':
            return '变更合同';
        case 'edit':
            return '编辑合同';
        case 'changeDetail':
            return '变更合同';
        default:
            return '新增合同';
    }
});

// 步骤数组 - 改为计算属性
const steps = computed(() => {
    if (isDetailMode.value) {
        return ['基础信息', '应收计划'];
    }
    return ['基础信息', '应收计划', '预览'];
});
const current = ref<number>(1);

const getParams = (data: any) => {
    if (data.rooms && data.rooms.length > 0) {
        data.rooms.forEach((item: any) => {
            item.startDate = item.startDate ? dayjs(item.startDate).format('YYYY-MM-DD') : ''
            item.endDate = item.endDate ? dayjs(item.endDate).format('YYYY-MM-DD') : ''
            delete item.id
        })
    }
    if (data.fees && data.fees.length > 0) {
        data.fees.forEach((item: any) => {
            delete item.id
            item.startDate = item.startDate ? dayjs(item.startDate).format('YYYY-MM-DD') : ''
            item.endDate = item.endDate ? dayjs(item.endDate).format('YYYY-MM-DD') : ''
        })
    }
    if (data.bookings && data.bookings.length > 0) {
        data.bookings.forEach((item: any) => {
            delete item.id
        })
    }
    if (data.costs && data.costs.length > 0) {
        data.costs.forEach((item: any) => {
            delete item.id
        })
    }
    if (data.activityDate && data.activityDate.length > 0) {
        data.dailyActivityStartTime = data.activityDate[0]
        data.dailyActivityEndTime = data.activityDate[1]
        delete data?.activityDate
    }
    delete data?.createTime
    delete data?.customer?.id

    // 转换为API所需的数据格式
    const apiData = data;

    // 设置提交状态（暂存=false, 提交=true）
    apiData.isSubmit = false;

    
    return apiData
}

// 生成应收计划
const handleReceive = async () => {
    if (generateLoading.value) {
        return // 防止重复提交
    }

    generateLoading.value = true

    try {
        await baseInfoRef.value.validateAll()
        // 标记为生成应收计划操作
        isAfterGeneratePlan.value = true
        // 先暂存
        // 合同生成应收计划，去除先调用暂存
        // await handleSave('generate')

        // 调用子组件方法生成应收计划
        const params = toRaw(contractData.value);
        const apiData = getParams(params)
        
        // 非标合同的处理逻辑
        if (contractData.value.contractMode === 1 && contractData.value.costs && contractData.value.costs.length > 0) {
            // 如果是变更合同或编辑合同，每次都调用generateCost接口
            if (contractStore.editType === 'change' || contractStore.editType === 'edit') {
                // 在调用generateCost之前，先保存变更执行日期之前的租金数据
                
                
                contractStore.contractCosts = {
                    costs: JSON.parse(JSON.stringify(contractData.value.costs)),
                    bookings: JSON.parse(JSON.stringify(contractData.value.bookings))
                }
                
                // 直接实现保存变更执行日期之前的租金数据逻辑
                if (contractStore.editType === 'change' && contractData.value.changeDate) {
                    const changeDate = contractData.value.changeDate;
                    const rentCosts = contractStore.contractCosts.costs?.filter(cost => cost.costType === 2) || [];
                    

                    
                    // 筛选出变更执行日期之前的租金数据
                    // 只有当租期结束日期严格小于变更执行日期时，才视为历史数据
                    contractStore.preChangeRentCosts = rentCosts.filter(cost => {
                        if (!cost.endDate) {

                            return false;
                        }
                        
                        // 严格基于结束日期判断：只有结束日期小于变更执行日期的数据才是历史数据
                        const isBeforeChange = new Date(cost.endDate) < new Date(changeDate);
                        

                        
                        return isBeforeChange;
                    });
                    

                }
                
                const { data: result, code } = await generateCost(apiData);
                if (code === 200) {
                    // 对于变更合同，需要合并历史数据
                    if (contractStore.editType === 'change' && contractStore.preChangeRentCosts.length > 0) {
                        
                        
                        // 获取新生成的租金数据
                        const newRentCosts = result.costs?.filter(cost => cost.costType === 2) || [];
                        const nonRentCosts = result.costs?.filter(cost => cost.costType !== 2) || [];
                        

                        
                        // 合并历史数据和新数据
                        const mergedRentCosts = [...contractStore.preChangeRentCosts, ...newRentCosts];
                        
                        // 更新contractCosts，确保历史数据被保留
                        contractStore.contractCosts = {
                            costs: [...nonRentCosts, ...mergedRentCosts],
                            bookings: result.bookings || []
                        };
                        

                    } else {
                        // 非变更合同，直接使用返回结果
                        contractStore.contractCosts = result;
                    }
                    
                    // 执行定金结转逻辑
                    contractStore.transferBookingAmount()
                    
                    // 确保历史数据在generateCost调用后仍然被保留
    
                    
                    // 切换到应收计划页面
                    current.value = 2;
                }
            } else {
                // 其他情况，如果已经生成过应收计划，不再重新生成，带入已保存的数据
                contractStore.contractCosts = {
                    costs: JSON.parse(JSON.stringify(contractData.value.costs)),
                    bookings: JSON.parse(JSON.stringify(contractData.value.bookings))
                }
                // 执行定金结转逻辑
                contractStore.transferBookingAmount()
                
                // 检查是否有计算后的租金数据，如果有则自动设置hasCalculatedMoney为true
                const rentCosts = contractData.value.costs.filter(cost => cost.costType === 2)
                const hasCalculatedRent = rentCosts.some(cost => 
                    cost.totalAmount && cost.totalAmount > 0 && 
                    cost.actualReceivable && cost.actualReceivable > 0
                )
                if (hasCalculatedRent) {
                    contractStore.hasCalculatedMoney = true
                    // 加载已保存的数据时，重置用户自定义数据标识
                    contractStore.hasUserCustomData = false
                }
                
                // 切换到应收计划页面
                current.value = 2;
            }
        } else {
            // 标准合同或没有应收计划数据的情况，都调用generateCost接口
            const { data: result, code } = await generateCost(apiData);
            if (code === 200) {
                contractStore.contractCosts = result
                // 执行定金结转逻辑
                contractStore.transferBookingAmount()
                // 切换到应收计划页面
                current.value = 2;
            }
        }
    } catch (error) {
        console.error('生成应收计划失败:', error);
    } finally {
        generateLoading.value = false
    }
};


// 保存状态
const saveLoading = ref(false)
// 添加生成应收计划的loading状态
const generateLoading = ref(false)
// 添加提交审批的loading状态
const submitLoading = ref(false)
const emits = defineEmits(['submit'])


// 获取打印模版
const handlePreview = async () => {
    // 先校验应收计划页面（包括完整的校验逻辑）
    try {
        // 1. 应收计划组件校验
        const isValid = receivablePlanRef.value?.validate()
        if (!isValid) {
            return
        }
        
        // 2. 金额一致性校验
        const infoOverviewRef = receivablePlanRef.value?.infoOverviewRef
        if (!infoOverviewRef) {
            Message.error('信息总览组件引用不存在')
            return
        } else if (typeof infoOverviewRef.getInitialRentData !== 'function') {
            Message.error('信息总览组件方法不存在')
            return
        } else {
            // 获取初始租金数据（固定值）
            const initialRentData = infoOverviewRef.getInitialRentData()
            
            if (!initialRentData) {
                Message.error('初始租金数据不存在')
                return
            } else {
                // 校验账单总额（元）不能是0
                if (initialRentData.rentAmount === 0) {
                    Message.error('租金账单总额不能为0')
                    return
                }
                
                // 校验表格合计与信息总览的一致性
                const currentRentCosts = contractCosts.value.costs?.filter(item => item.costType === 2) || []
                const currentTotalAmount = currentRentCosts.reduce((sum, item) => sum + (item.totalAmount || 0), 0)
                const currentDiscountAmount = currentRentCosts.reduce((sum, item) => sum + (item.discountAmount || 0), 0)
                const currentNetAmount = currentTotalAmount - currentDiscountAmount
                
                // 信息总览的固定值
                const initialTotalAmount = initialRentData.rentAmount
                const initialDiscountAmount = initialRentData.rentDiscountAmount
                const initialNetAmount = initialTotalAmount - initialDiscountAmount
                
                // 校验净金额一致性（允许小数点精度误差）
                const tolerance = 0.01
                if (Math.abs(currentNetAmount - initialNetAmount) > tolerance) {
                    Message.error(`表格租金净额与信息总览不一致: 表格净额 ${currentNetAmount.toFixed(2)} 元，信息总览净额 ${initialNetAmount.toFixed(2)} 元`)
                    return
                }
            }
        }
    } catch (error) {
        Message.error('校验失败：' + (error as Error).message)
        return
    }
    
    let contractId = '';

    // 续签模式下优先使用renewContractId
    if (contractStore.editType === 'renew' && renewContractId.value) {
        contractId = renewContractId.value;
    }
    // 变更模式下优先使用changeContractId
    else if (contractStore.editType === 'change' && changeContractId.value) {
        contractId = changeContractId.value;
    }
    else if (contractStore.contractData.id) {
        contractId = contractStore.contractData.id;
    } else {
        // 如果没有合同ID，先保存获取ID
        try {
            await handleSave('save');
            // 续签模式使用renewContractId，变更模式使用changeContractId，其他模式使用contractData.id
            if (contractStore.editType === 'renew') {
                contractId = renewContractId.value || '';
            } else if (contractStore.editType === 'change') {
                contractId = changeContractId.value || '';
            } else {
                contractId = contractStore.contractData.id || '';
            }
        } catch (error) {
            Message.error('保存合同失败，无法预览');
            return;
        }
    }

    if (!contractId) {
        Message.error('合同ID不能为空');
        return;
    }

    try {
        printLoading.value = true;

        // 获取合同模板列表
        const { data: templates, code: templateCode } = await getContractTemplateList(contractId);

        if (templateCode === 200 && templates && templates.length > 0) {
            if (templates.length === 1) {
                // 只有一个模板，直接使用
                await handleTemplateSelected(templates[0].id, contractId);
            } else {
                // 多个模板，显示选择弹窗
                templateModalVisible.value = true;
            }
        } else {
            Message.error('未找到可用的合同模板');
        }
    } catch (error) {
        console.error('获取合同模板失败:', error);
        Message.error('获取合同模板失败');
    } finally {
        printLoading.value = false;
    }
};

const handleTemplateSelected = async (templateId: string, passedContractId?: string) => {
    let contractId = passedContractId;

    if (!contractId) {
        // 续签模式下优先使用renewContractId
        if (contractStore.editType === 'renew' && renewContractId.value) {
            contractId = renewContractId.value;
        }
        // 变更模式下优先使用changeContractId
        else if (contractStore.editType === 'change' && changeContractId.value) {
            contractId = changeContractId.value;
        }
        else if (contractStore.contractData.id) {
            contractId = contractStore.contractData.id;
        } else {
            // 如果没有合同ID，先保存获取ID
            try {
                await handleSave('save');
                if (contractStore.editType === 'renew') {
                    contractId = renewContractId.value;
                } else if (contractStore.editType === 'change') {
                    contractId = changeContractId.value;
                } else {
                    contractId = contractStore.contractData.id;
                }
            } catch (error) {
                Message.error('保存合同失败，无法预览');
                return;
            }
        }
    }

    if (!contractId) {
        Message.error('合同ID不能为空');
        return;
    }

    try {
        printLoading.value = true;

        // 调用print接口
        const { data: result, code } = await print(contractId, templateId);

        if (code === 200 && result) {
            // 假设print接口返回的数据结构中包含fileUrl字段
            printFileUrl.value = result.fileUrl || '';

            if (printFileUrl.value) {
                // 关闭模板选择弹窗
                templateModalVisible.value = false;
                // 进入预览步骤
                current.value = 3;
                Message.success('合同预览生成成功');
            } else {
                Message.error('未获取到合同预览文件');
            }
        } else {
            Message.error('生成合同预览失败');
        }
    } catch (error) {
        console.error('生成合同预览失败:', error);
        Message.error('生成合同预览失败');
    } finally {
        printLoading.value = false;
    }
};

// 添加预览页面返回处理
const handlePreviewBack = () => {
    current.value = 2
}

// 保存合同数据
const handleSave = async (type: string = 'save') => {
    return new Promise(async (resolve, reject) => {
        if (saveLoading.value) {
            reject(new Error('操作正在进行中，请勿重复提交'))
            return // 防止重复提交
        }

        saveLoading.value = true

        try {
            // 调用子组件的验证方法
            if (current.value === 1) {
                await baseInfoRef.value.validateAll()
                if (contractStore.editType === 'change') {
                    await changeInfoRef.value.validate()
                }
            }

            const params = JSON.parse(JSON.stringify(contractData.value)) as ContractAddDTO;

            // 确保变更模式下，如果已经有contractId，保持传递给API
            if ((contractStore.editType === 'change' || contractStore.editType === 'changeDetail') && contractStore.contractData.id) {
                params.id = contractStore.contractData.id;
            }

            // 应收计划校验（只在应收计划页面执行）
            if (current.value === 2) {
                if (!receivablePlanRef.value) {
                    throw new Error('应收计划组件引用不存在')
                }
                if (typeof receivablePlanRef.value.validate !== 'function') {
                    throw new Error('应收计划校验方法不存在')
                }
                const isValid = receivablePlanRef.value.validate()
                if (!isValid) {
                    return
                }
                // 金额一致性校验
                const infoOverviewRef = receivablePlanRef.value?.infoOverviewRef
                if (!infoOverviewRef) {
                    throw new Error('信息总览组件引用不存在')
                } else if (typeof infoOverviewRef.getInitialRentData !== 'function') {
                    throw new Error('信息总览组件方法不存在')
                } else {
                    // 获取初始租金数据（固定值）
                    const initialRentData = infoOverviewRef.getInitialRentData()
                    
                    if (!initialRentData) {
                        throw new Error('初始租金数据不存在')
                    } else {
                        // 校验账单总额（元）不能是0
                        if (initialRentData.rentAmount === 0) {
                            Message.error('租金账单总额不能为0')
                            throw new Error('租金账单总额不能为0')
                        }
                        
                        // 校验表格合计与信息总览的一致性
                        const currentRentCosts = contractCosts.value.costs?.filter(item => item.costType === 2) || []
                        const currentTotalAmount = currentRentCosts.reduce((sum, item) => sum + (item.totalAmount || 0), 0)
                        const currentDiscountAmount = currentRentCosts.reduce((sum, item) => sum + (item.discountAmount || 0), 0)
                        const currentNetAmount = currentTotalAmount - currentDiscountAmount
                        
                        // 信息总览的固定值
                        const initialTotalAmount = initialRentData.rentAmount
                        const initialDiscountAmount = initialRentData.rentDiscountAmount
                        const initialNetAmount = initialTotalAmount - initialDiscountAmount
                        
                        // 校验净金额一致性（允许小数点精度误差）
                        const tolerance = 0.01
                        if (Math.abs(currentNetAmount - initialNetAmount) > tolerance) {
                            Message.error(`表格租金净额与信息总览不一致: 表格净额 ${currentNetAmount.toFixed(2)} 元，信息总览净额 ${initialNetAmount.toFixed(2)} 元`)
                            return
                        }
                    }
                }
            }
            
            // 3. 准备数据和参数
            // 如果是变更合同，需要合并历史数据
            if (contractStore.editType === 'change' && contractStore.preChangeRentCosts.length > 0) {
                const currentCosts = contractCosts.value.costs || []
                const rentCosts = currentCosts.filter(item => Number(item.costType) === 2)
                const nonRentCosts = currentCosts.filter(item => Number(item.costType) !== 2)
                
                // 合并历史租金数据和当前租金数据
                const mergedRentCosts = [...contractStore.preChangeRentCosts, ...rentCosts]
                params.costs = [...nonRentCosts, ...mergedRentCosts]
            } else {
                params.costs = contractCosts.value.costs || []
            }
            
            params.bookings = contractCosts.value.bookings || []

            // 4. 校验租金账单总额不能为0（标准合同和非标合同都需要校验）
            const rentCosts = params.costs?.filter(item => item.costType === 2) || []
            
            if (rentCosts.length > 0) {
                // 先按period分组合并，避免相同period的记录重复验证
                const rentCostsGroup = rentCosts.reduce((acc, item) => {
                    if (!acc[item.period]) {
                        acc[item.period] = []
                    }
                    acc[item.period].push(item)
                    return acc
                }, {} as Record<number, any[]>)
                
                // 合并相同period的数据用于验证
                const mergedRentCosts = Object.values(rentCostsGroup).map(items => {
                    const firstItem = items[0]
                    const lastItem = items[items.length - 1]
                    const totalAmount = items.reduce((sum, item) => sum + (item.totalAmount || 0), 0)
                    return {
                        ...firstItem,
                        startDate: firstItem.startDate,
                        endDate: lastItem.endDate || firstItem.endDate,
                        totalAmount: totalAmount,
                        discountAmount: items.reduce((sum, item) => sum + (item.discountAmount || 0), 0),
                        actualReceivable: items.reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
                    }
                })
                
                // 校验每期租金账单总额不能为0
                for (let i = 0; i < mergedRentCosts.length; i++) {
                    const cost = mergedRentCosts[i]
                    
                    // 校验账单总额不能为0
                    if (Number(cost.totalAmount) === 0) {
                        Message.error(`第${cost.period}期租金账单的总额不能为0`)
                        throw new Error(`第${cost.period}期租金账单的总额不能为0`)
                    }
                }
            }

            // 如果为非标合同，需要校验应收计划
            if (params.contractMode === 1) {
                // 继续其他非标合同的校验逻辑...
                // 这里省略了其他校验代码，保持原有逻辑
            }

            // 5. 执行保存操作
            const apiData = getParams(params)
            
            if (type === 'submit') {
                apiData.isSubmit = true
            }
            
            // 调用API保存合同数据
            const api = contractStore.editType === 'change' || contractStore.editType === 'changeDetail' ? saveContractChange : saveContract
            
            const { data: result, code, msg } = await api(apiData);

            if (code === 200) {
                // 如果是续签模式，保存返回的contractId
                if (contractStore.editType === 'renew') {
                    renewContractId.value = result.contractId
                    // 重要：将返回的contractId更新到contractData中，供后续操作使用
                    contractStore.contractData.id = result.contractId
                }
                // 如果是变更模式，保存返回的contractId
                else if (contractStore.editType === 'change') {
                    // 从列表进来的变更，保存返回的contractId
                    changeContractId.value = result.contractId
                    // 重要：将返回的contractId更新到contractData中，供后续操作使用
                    contractStore.contractData.id = result.contractId
                }
                else if (contractStore.editType === 'changeDetail') {
                    // 从详情进来的变更，不对ID做特殊处理，保持原有逻辑
                    if (result.contractId) {
                        contractStore.contractData.id = result.contractId
                    }
                }
                // 对于其他模式，也要更新contractData的id
                else if (!contractStore.contractData.id && result.contractId) {
                    contractStore.contractData.id = result.contractId
                }

                if (type === 'submit') {
                    Message.success('提交成功')
                    emits('submit')
                    drawerVisible.value = false
                    resolve(true)
                    return
                }
                type === 'save' && Message.success('暂存成功')

                // 关键修改：续签和变更模式下，如果是暂存或生成应收计划操作，不要重新获取详情以避免重置数据
                if ((type === 'save' || type === 'generate') && (
                    contractStore.editType === 'renew' ||
                    contractStore.editType === 'change' ||
                    contractStore.editType === 'changeDetail'
                )) {
                    // 暂存或生成应收计划后，不重新获取详情，避免重置用户输入的变更内容
                    emits('submit')
                    resolve(true)
                } else {
                    // 获取合同详情
                    await fetchContractDetail(result.contractId, type, false)
                    emits('submit')
                    resolve(true)
                }
            } else {
                // API调用失败的情况
                const errorMessage = msg || '保存失败'
                Message.error(errorMessage)
                reject(new Error(errorMessage))
            }

        } catch (error: any) {
            console.error('保存合同失败:', error);
            Message.error(error.message || '保存失败');
            reject(error);
        } finally {
            saveLoading.value = false;
        }
    });
};

// 提交审批
const handleSubmit = async () => {
    if (submitLoading.value) {
        return // 防止重复提交
    }

    submitLoading.value = true

    try {
        // 提交前，先暂存应收计划
        await handleSave('submit')
    } catch (error) {
        console.error('提交审批失败:', error);
    } finally {
        submitLoading.value = false
    }
}

defineExpose({
    open
});
</script>

<style lang="less" scoped>
.arco-card {
    :deep(.arco-steps) {
        .arco-steps-item {
            justify-content: center;
            background: linear-gradient(270deg, #dce3ea 0%, #e9edf4 100%);

            &::after {
                border-left: 20px solid #dce3ea !important;
            }

            &.arco-steps-item-active,
            &.arco-steps-item-finish {
                .arco-steps-item-title {
                    color: rgb(var(--success-6));
                }
            }
        }

        .arco-steps-item-title {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            gap: 8px;
        }
    }
}
</style>
