# 解绑接口参数描述统一功能实现说明

## 修改概述
统一了智能门锁和智能水电解绑接口的参数名称和描述，将参数名从 `roomIds` 改为 `ids`，描述从"房间ID列表"改为"设备ID列表"。

## 修改背景
在智能设备管理系统中，解绑操作实际上是基于设备ID进行的，而不是房间ID。为了保持接口描述的准确性和一致性，需要统一参数的命名和描述。

## 修改内容

### 1. 智能门锁解绑接口

**文件**: `src/api/smartLock.ts`

**修改前：**
```typescript
/**
 * 解绑房间与设备的绑定关系
 * @param roomIds 房间ID列表
 * @returns Promise<AjaxResult>
 */
export function unbindRoomDevice(roomIds: string[]) {
    return http.post<AjaxResult>(`${systemUrl}/ttlock/device/room/bind/delete`, roomIds)
}
```

**修改后：**
```typescript
/**
 * 解绑房间与设备的绑定关系
 * @param ids 设备ID列表
 * @returns Promise<AjaxResult>
 */
export function unbindRoomDevice(ids: string[]) {
    return http.post<AjaxResult>(`${systemUrl}/ttlock/device/room/bind/delete`, ids)
}
```

### 2. 智能水电解绑接口

**文件**: `src/api/smartWaterElectricity.ts`

**修改前：**
```typescript
/**
 * 解绑房间与运营系统的关联关系
 * @param roomIds 房间ID列表
 * @returns Promise<boolean>
 */
export function deleteWaterElectricityBindRelation(roomIds: string[]) {
    return http.post<boolean>(`${systemUrl}/water/electricity/room/bind/delete`, roomIds)
}
```

**修改后：**
```typescript
/**
 * 解绑房间与运营系统的关联关系
 * @param ids 设备ID列表
 * @returns Promise<boolean>
 */
export function deleteWaterElectricityBindRelation(ids: string[]) {
    return http.post<boolean>(`${systemUrl}/water/electricity/room/bind/delete`, ids)
}
```

## 修改对比

### 参数名称变更
| 接口 | 修改前 | 修改后 |
|------|--------|--------|
| `unbindRoomDevice` | `roomIds: string[]` | `ids: string[]` |
| `deleteWaterElectricityBindRelation` | `roomIds: string[]` | `ids: string[]` |

### 参数描述变更
| 接口 | 修改前 | 修改后 |
|------|--------|--------|
| `unbindRoomDevice` | `@param roomIds 房间ID列表` | `@param ids 设备ID列表` |
| `deleteWaterElectricityBindRelation` | `@param roomIds 房间ID列表` | `@param ids 设备ID列表` |

## 修改原因

### 1. 语义准确性
- 解绑操作实际上是基于设备的唯一标识（id）进行的
- 参数传递的是设备ID，而不是房间ID
- 统一使用 `ids` 更准确地反映了参数的实际含义

### 2. 代码一致性
- 在调用这些接口时，传递的都是 `record.id` 或 `selectedKeys`（包含id值）
- 参数名称应该与实际传递的数据类型保持一致
- 提高代码的可读性和可维护性

### 3. 接口规范统一
- 两个解绑接口现在使用相同的参数命名规范
- 便于开发者理解和使用
- 减少因参数名称不一致导致的混淆

## 实际调用场景

### 1. 智能门锁解绑调用
```typescript
// 单个设备解绑
await unbindRoomDevice([record.id])

// 批量设备解绑
await unbindRoomDevice(doorLockSelectedKeys.value)
```

### 2. 智能水电解绑调用
```typescript
// 单个设备解绑
await deleteWaterElectricityBindRelation([record.id])

// 批量设备解绑
await deleteWaterElectricityBindRelation(waterElectricSelectedKeys.value)
```

## 数据流分析

### 1. 数据来源
- 表格中的 `record.id`：设备的唯一标识
- `selectedKeys`：选中设备的ID列表

### 2. 数据传递
- 前端：`ids` 参数包含设备ID列表
- 后端：接收设备ID列表进行解绑操作

### 3. 数据处理
- 后端根据设备ID查找对应的绑定关系
- 执行解绑操作
- 返回操作结果

## 影响范围

### 1. 直接影响
- 接口文档的参数描述更加准确
- 代码注释与实际功能保持一致
- 提高代码的可读性

### 2. 无影响范围
- 接口的实际功能不变
- 参数类型不变（仍然是 `string[]`）
- 调用方式不变
- 后端接口处理逻辑不变

## 技术优势

### 1. 语义清晰
- 参数名称准确反映数据内容
- 减少开发者的理解成本
- 提高代码的自文档化程度

### 2. 维护便利
- 统一的命名规范
- 一致的接口设计
- 便于代码审查和维护

### 3. 扩展性好
- 为后续类似接口提供了命名规范
- 便于接口的标准化管理
- 支持更好的代码复用

## 验证要点

### 1. 功能验证
- ✅ 智能门锁解绑功能正常
- ✅ 智能水电解绑功能正常
- ✅ 批量解绑和单个解绑都正常工作

### 2. 代码一致性验证
- ✅ 参数名称与实际传递数据一致
- ✅ 接口描述与功能实现一致
- ✅ 两个解绑接口使用统一的命名规范

### 3. 文档准确性验证
- ✅ JSDoc注释准确描述参数含义
- ✅ 参数类型定义正确
- ✅ 返回值类型描述准确

## 注意事项

1. **向后兼容**: 此修改只是参数名称和描述的变更，不影响接口的实际功能
2. **类型安全**: 参数类型仍然是 `string[]`，保持类型安全
3. **调用方式**: 所有现有的调用代码都不需要修改
4. **文档同步**: 如果有其他相关文档，也应该同步更新参数描述

现在两个解绑接口的参数描述已经统一，都使用 `ids` 作为参数名，描述为"设备ID列表"，更准确地反映了接口的实际功能和数据流。
