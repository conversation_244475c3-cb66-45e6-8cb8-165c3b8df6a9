<template>
	<header class="header-container">
		<div class="header-image-list">
			<div class="header-image-left"></div>
			<div class="header-image-middle"></div>
			<div class="header-image-right"></div>
		</div>
		<div class="header-position">
			<div class="header-content">
			<!-- 左侧欢迎信息和中心切换 -->
			<div class="welcome-section">
				<div class="welcome-text">欢迎回来，{{ userStore.name || '张一凡' }}</div>
				<div class="date-info">
					<span class="today">今天是 {{ currentDate }} {{ weekDay }}</span>
				</div>
				<!-- 中心切换功能 -->
				<div class="center-switcher">
					<a-dropdown trigger="click" @select="handleCenterSwitch">
						<div class="switcher-trigger">
							<span class="current-center-text">{{ centerStore.getCenterName }}</span>
							<icon-swap class="switcher-icon" />
						</div>
						<template #content>
							<a-doption v-for="center in centerOptions" :key="center.value" :value="center.value"
								:class="{ 'selected-option': currentCenter === center.value }">
								<div class="dropdown-item">
									<span>{{ center.label }}</span>
									<icon-check v-if="currentCenter === center.value" class="check-icon" />
								</div>
							</a-doption>
						</template>
					</a-dropdown>
				</div>
			</div>



			<!-- 右侧用户操作区 -->
			<div class="user-section">
				<!-- 消息中心 -->
				<!-- <div class="message-center">
					<a-badge :count="messageCount" :max-count="99">
						<a-button type="text" class="message-btn">
							<template #icon>
								<icon-notification class="message-icon" />
							</template>
							消息中心
						</a-button>
					</a-badge>
				</div> -->

				<!-- 全屏切换 -->
				<div class="fullscreen-toggle">
					<a-tooltip :content="isFullscreen ? '退出全屏' : '进入全屏'">
						<a-button type="text" class="control-btn" @click="toggleFullScreen">
							<template #icon>
								<icon-fullscreen-exit v-if="isFullscreen" />
								<icon-fullscreen v-else />
							</template>
						</a-button>
					</a-tooltip>
				</div>

				<!-- 设置 -->
				<!-- <div class="settings">
					<a-tooltip content="系统设置">
						<a-button type="text" class="control-btn" @click="setVisible">
							<template #icon>
								<icon-settings />
							</template>
						</a-button>
					</a-tooltip>
				</div> -->

				<!-- 用户信息 -->
				<a-dropdown>
					<div class="user-info">
						<a-avatar :size="32" class="user-avatar">
							<img alt="avatar" :src="userStore.avatar || '/src/assets/images/user/avatar.png'" />
						</a-avatar>
						<span class="username">{{ userStore.name || '张一凡' }}</span>
						<icon-down class="dropdown-icon" />
					</div>
					<template #content>
						<a-doption @click="switchRoles">
							<template #icon>
								<icon-tag />
							</template>
							切换角色
						</a-doption>
						<a-doption @click="$router.push({ name: 'Info' })">
							<template #icon>
								<icon-user />
							</template>
							个人中心
						</a-doption>
						<a-doption @click="$router.push({ name: 'Setting' })">
							<template #icon>
								<icon-settings />
							</template>
							用户设置
						</a-doption>
						<a-divider style="margin: 4px 0;" />
						<a-doption @click="handleLogout">
							<template #icon>
								<icon-export />
							</template>
							退出登录
						</a-doption>
					</template>
				</a-dropdown>
			</div>
		</div>


		</div>
	</header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { useFullscreen } from '@vueuse/core'
import {
	IconNotification,
	IconUser,
	IconDown,
	IconSettings,
	IconExport,
	IconSwap,
	IconCheck,
	IconTag,
	IconFullscreen,
	IconFullscreenExit
} from '@arco-design/web-vue/es/icon'
import dayjs from 'dayjs'
import { useAppStore, useUserStore, useCenterStore } from '@/store'
import useUser from '@/hooks/user'
import type { CenterType } from '@/store/modules/center'

// Store 和 Router
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const centerStore = useCenterStore()
const { logout } = useUser()
const { isFullscreen, toggle: toggleFullScreen } = useFullscreen()

// 响应式数据
const currentTime = ref(new Date())
const messageCount = ref(5) // 消息数量

// 计算属性
const currentDate = computed(() => {
	return dayjs(currentTime.value).format('YYYY年MM月DD日')
})

const weekDay = computed(() => {
	const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
	return weekDays[currentTime.value.getDay()]
})

// 中心切换相关
const centerOptions = computed(() => centerStore.centerList)

const currentCenter = computed(() => centerStore.getCurrentCenter)
// 定时器
let timer: ReturnType<typeof setInterval> | null = null

// 生命周期
onMounted(() => {
	// 每分钟更新时间
	timer = setInterval(() => {
		currentTime.value = new Date()
	}, 60000)
})

onUnmounted(() => {
	if (timer) {
		clearInterval(timer)
	}
})

// 方法
const handleLogout = () => {
	logout()
}

const handleCenterSwitch = (value: CenterType) => {
	centerStore.setCurrentCenter(value)
	Message.success(`已切换到${centerStore.getCenterName}`)
}

const setVisible = () => {
	appStore.updateSettings({ globalSettings: true })
}

const switchRoles = async () => {
	const res = await userStore.switchRoles()
	if (typeof res === 'string') {
		Message.success(res)
	}
}
</script>

<style scoped lang="less">
.header-container {
	width: 100%;
	height: 60px;
	position: relative;
	overflow: hidden;
	box-sizing: border-box;
	z-index: 1;

	.header-image-list {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: space-between;

		.header-image-left {
			// width: 100px;
			flex: 1;
			height: 60px;
			background: url(../../assets/images/dashboard/banner4-l.jpg) left top/auto 60px repeat;
			z-index: 2;
		}

		.header-image-middle {
			// flex: 1;
			width: 660px;
			height: 60px;
			background: url(../../assets/images/dashboard/banner4.jpg);
			background-repeat: no-repeat;
			background-size: auto 90px;
			z-index: 2;
		}

		.header-image-right {
			// width: 100px;
			flex: 1;
			height: 60px;
			background: url(../../assets/images/dashboard/banner4-r.jpg) right top/auto 60px repeat;
			z-index: 2;
		}
	}
	.header-position {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 3;
	}



	// // 背景图片适度拉伸，避免过度失真
	// background: url(../../assets/images/dashboard/banner4.jpg) center/cover no-repeat;

	// // 左右边缘使用固定图片覆盖
	// &::before {
	// 	content: '';
	// 	position: absolute;
	// 	left: 0;
	// 	top: 0;
	// 	width: 100px;
	// 	height: 60px;
	// 	background: url(../../assets/images/dashboard/banner4.jpg) left top/auto 60px no-repeat;
	// 	z-index: 2;
	// }

	// &::after {
	// 	content: '';
	// 	position: absolute;
	// 	right: 0;
	// 	top: 0;
	// 	width: 100px;
	// 	height: 60px;
	// 	background: url(../../assets/images/dashboard/banner4.jpg) right top/auto 60px no-repeat;
	// 	z-index: 2;
	// }

}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 100%;
	padding: 0 16px;
	// max-width: 1200px;
	margin: 0 auto;
	position: relative;
	z-index: 1;
}

.welcome-section {
	display: flex;
	align-items: center;
	gap: 24px;
	color: white;

	.welcome-text {
		font-size: 16px;
		font-weight: 500;
		margin-bottom: 2px;
	}

	.date-info {
		font-size: 12px;
		opacity: 0.9;

		.today {
			color: rgba(255, 255, 255, 0.85);
		}
	}

	.center-switcher {
		.switcher-trigger {
			display: flex;
			align-items: center;
			padding: 8px 16px;
			background: rgba(255, 255, 255, 0.15);
			border-radius: 6px;
			cursor: pointer;
			transition: all 0.3s ease;

			&:hover {
				background: rgba(255, 255, 255, 0.2);
			}

			.current-center-text {
				font-size: 14px;
				font-weight: 500;
				color: white;
				margin-right: 8px;
				white-space: nowrap;
			}

			.switcher-icon {
				font-size: 14px;
				color: white;
				opacity: 0.8;
				transition: all 0.3s ease;
			}

			&:hover .switcher-icon {
				opacity: 1;
				transform: rotate(90deg);
			}
		}
	}
}

.user-section {
	display: flex;
	align-items: center;
	gap: 12px;
}

.message-center {
	.message-btn {
		color: white;
		border: none;
		padding: 8px 12px;
		border-radius: 6px;
		transition: all 0.3s ease;

		&:hover {
			background-color: rgba(255, 255, 255, 0.1);
		}

		.message-icon {
			font-size: 16px;
			margin-right: 4px;
		}
	}
}

.fullscreen-toggle,
.settings {
	.control-btn {
		color: white;
		border: none;
		padding: 8px;
		border-radius: 6px;
		transition: all 0.3s ease;

		&:hover {
			background-color: rgba(255, 255, 255, 0.1);
		}

		:deep(.arco-icon) {
			font-size: 16px;
		}
	}
}

.user-info {
	display: flex;
	align-items: center;
	gap: 8px;
	cursor: pointer;
	padding: 6px 12px;
	border-radius: 6px;
	transition: all 0.3s ease;
	color: white;

	&:hover {
		background-color: rgba(255, 255, 255, 0.1);
	}

	.user-avatar {
		border: 2px solid rgba(255, 255, 255, 0.3);

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.username {
		font-size: 14px;
		font-weight: 500;
	}

	.dropdown-icon {
		font-size: 12px;
		opacity: 0.8;
		transition: transform 0.3s ease;
	}

	&:hover .dropdown-icon {
		transform: rotate(180deg);
	}
}

// 全局样式（不使用scoped）
:global(.dropdown-item) {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;

	.check-icon {
		color: #4a90e2;
		font-size: 14px;
	}
}

:global(.selected-option) {
	background-color: rgba(74, 144, 226, 0.1);

	.dropdown-item span {
		color: #4a90e2;
		font-weight: 500;
	}
}

// 响应式设计
@media (max-width: 768px) {
	.header-content {
		padding: 0 12px;
	}

	.welcome-section {
		gap: 16px;

		.welcome-text {
			font-size: 14px;
		}

		.date-info {
			font-size: 11px;
		}

		.center-switcher {
			.switcher-trigger {
				padding: 6px 12px;

				.current-center-text {
					font-size: 12px;
				}
			}
		}
	}

	.user-section {
		gap: 8px;
	}

	.message-center .message-btn {
		padding: 6px 8px;
		font-size: 12px;
	}

	.fullscreen-toggle,
	.settings {
		.control-btn {
			padding: 6px;
		}
	}

	.user-info {
		padding: 4px 8px;

		.username {
			font-size: 13px;
		}
	}
}
</style>
