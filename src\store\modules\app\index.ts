import { defineStore } from 'pinia';
import { Notification } from '@arco-design/web-vue';
import type { NotificationReturn } from '@arco-design/web-vue/es/notification/interface';
import type { RouteRecordNormalized } from 'vue-router';
import defaultSettings from '@/config/settings.json';
import { getMenuList } from '@/api/user';
import { getRouters } from '@/api/auth/router';
import { AppState } from './types';

const useAppStore = defineStore('app', {
    state: (): AppState => ({ ...defaultSettings }),

    getters: {
        appCurrentSetting(state: AppState): AppState {
            return { ...state };
        },
        appDevice(state: AppState) {
            return state.device;
        },
        appAsyncMenus(state: AppState): RouteRecordNormalized[] {
            return state.serverMenu as unknown as RouteRecordNormalized[];
        },
    },

    actions: {
        // Update app settings
        updateSettings(partial: Partial<AppState>) {
            // @ts-ignore-next-line
            this.$patch(partial);
        },

        // Change theme color
        toggleTheme(dark: boolean) {
            if (dark) {
                this.theme = 'dark';
                document.body.setAttribute('arco-theme', 'dark');
            } else {
                this.theme = 'light';
                document.body.removeAttribute('arco-theme');
            }
        },
        toggleDevice(device: string) {
            this.device = device;
        },
        toggleMenu(value: boolean) {
            this.hideMenu = value;
        },
        async fetchServerMenuConfig() {
            // const loadView = (view: string) => {
            //     return () => import(`@/views/system/menu/index.vue`)
            //     // const importPath = `@/views/${view}`;
            //     // console.log('Loading view:', importPath); // 调试输出
            //     // return () => import(/* @vite-ignore */ importPath);
            // };
            let notifyInstance: NotificationReturn | null = null;
            try {
                // notifyInstance = Notification.info({
                //     id: 'menuNotice', // Keep the instance id the same
                //     content: 'loading',
                //     closable: true,
                // });
                const { data } = await getMenuList();
                // this.serverMenu = data
                // console.log('Current routes:', router.getRoutes().map(r => ({
                //     path: r.path,
                //     name: r.name,
                //     component: r.component?.name
                // })))
                // return
                // 去除 menuType 不是 M 和C的
                // data.filter((menu: any) => menu.menuType === 'M' || menu.menuType === 'C');
                // console.log('data', data);
                // {
                //     path: 'post',
                //     name: 'systemPostRouter',
                //     component: () => import('@/views/system/post.vue'),
                //     meta: {
                //         locale: '岗位用户管理',
                //         requiresAuth: true,
                //         roles: ['*'],
                //     },
                // },
                let _menuList: any[] = [];
                data.forEach((menu: any) => {
                    if (menu.menuType === 'M') {
                        menu.icon = 'icon-list';
                        menu.children = [];
                        _menuList.push(menu);
                    } else if (menu.menuType === 'C') {
                        menu.children = null;
                        menu.icon = null;
                        // menu.component = loadView(menu.component)
                        if (menu.path) {
                            menu.name = `${menu.path}Router`;
                        } else {
                            menu.name = menu.path;
                        }
                        // 显式处理组件路径并打印调试信息
                        // const rawComponent = menu.component;
                        // const componentPath = rawComponent.replace(/\.vue$/, '');
                        // const importPath = `@/views/${componentPath}.vue`;
                        // console.log('Processing component:', { rawComponent, componentPath, importPath });

                        // menu.component = () => import(/* @vite-ignore */ importPath);
                        _menuList.push(menu);
                    }
                });
                console.log('menuList', _menuList);
                // return
                // todo 添加岗位用户管理
                // _menuList.push({
                //     path: 'post',
                //     name: 'systemPostRouter',
                //     component: () => import('@/views/system/post.vue'),
                //     menuName: '岗位用户管理',
                //     menuType: 'C',
                //     parentId: 1,
                //     orderNum: 0,
                // })
                // _menuList.push({
                //     path: 'ttt',
                //     name: 'systemMenuRouter',
                //     component: () => import('@/views/system/menu.vue'),
                //     menuName: '岗位用户管理22',
                //     menuType: 'C',
                //     parentId: 1,
                //     orderNum: 0,
                // })

                // 创建映射表并初始化children数组
                const idMap = new Map<number, any>();
                _menuList.forEach((menu: any) => {
                    menu.meta = {
                        locale: menu.menuName,
                        requiresAuth: true,
                        order: menu.orderNum,
                        roles: ['*'],
                        icon: menu.icon,
                    };
                    // menu.children = [];
                    idMap.set(menu.menuId, menu);
                });

                // 构建树形结构
                _menuList.forEach((menu: any) => {
                    if (menu.parentId !== 0 && idMap.has(menu.parentId)) {
                        idMap.get(menu.parentId).children.push(menu);
                    }
                });

                // 过滤出顶级菜单（parentId === 0）
                this.serverMenu = _menuList.filter(
                    (menu: any) => menu.parentId === 0
                );
                // this.serverMenu.push({
                //     path: '/dashboard',
                //     name: 'dashboard',
                //     meta: {
                //         locale: '工作台',
                //         requiresAuth: true,
                //         icon: 'icon-dashboard',
                //         order: 0,
                //     },
                //     children: [
                //         {
                //             path: 'workplace',
                //             name: 'Workplace',
                //             meta: {
                //                 locale: '工作台',
                //                 requiresAuth: true,
                //             },
                //         },
                //     ],
                // });
                console.log('获取菜单', this.serverMenu);

                // 根据 data 的 menuId 和 parentId 来组装serverMenu, 写到 children， data是一个数组
                // data 的模拟数据 [{menuId: 1, parentId: 0, menuName: '系统监控', menuType: 'M', orderNum: 2, children: [{menuId: 2, parentId: 1, menuName: '系统监控', menuType: 'M', orderNum: 2}]}, {menuId: 2, parentId: 0, menuName: '系统监控', menuType: 'M', orderNum: 2}]

                // this.serverMenu = data
                // notifyInstance = Notification.success({
                //     id: 'menuNotice',
                //     content: 'success',
                //     closable: true,
                // });

                // 在注册完成后打印当前路由表
                // console.log('Current routes:', router.getRoutes().map(r => ({
                //     path: r.path,
                //     name: r.name,
                //     component: r.component?.name
                // })))
            } catch (error) {
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                notifyInstance = Notification.error({
                    id: 'menuNotice',
                    content: 'error',
                    closable: true,
                });
            }
        },
        clearServerMenu() {
            this.serverMenu = [];
        },
    },
});

export default useAppStore;
