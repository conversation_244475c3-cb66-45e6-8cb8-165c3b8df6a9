# SectionTitle 组件替换说明

## 修改概述

将 `BatchBindDrawer.vue` 组件中的所有 `h3` 标题替换为统一的 `SectionTitle` 组件，以保持项目中标题样式的一致性。

## 修改内容

### 1. 导入 SectionTitle 组件

```typescript
import SectionTitle from '@/components/sectionTitle/index.vue'
```

### 2. 替换的标题部分

#### 设备品牌标题
**修改前:**
```vue
<h3>设备品牌</h3>
```

**修改后:**
```vue
<SectionTitle title="设备品牌" />
```

#### 房源标题（带右侧操作按钮）
**修改前:**
```vue
<div class="panel-header">
    <h3>房源</h3>
    <div class="header-actions">
        <a-button type="text" @click="handleAutoMatch">自动匹配</a-button>
        <a-button type="text" @click="handleExportRooms">导出房源</a-button>
        <a-button type="text" @click="handleImportBinding">导入绑定关系</a-button>
    </div>
</div>
```

**修改后:**
```vue
<SectionTitle title="房源">
    <template #right>
        <div class="header-actions">
            <a-button type="text" @click="handleAutoMatch">自动匹配</a-button>
            <a-button type="text" @click="handleExportRooms">导出房源</a-button>
            <a-button type="text" @click="handleImportBinding">导入绑定关系</a-button>
        </div>
    </template>
</SectionTitle>
```

#### 设备信息标题（带右侧操作按钮）
**修改前:**
```vue
<div class="panel-header">
    <h3>设备信息</h3>
    <div class="header-actions">
        <a-button type="text" @click="handleSyncDevices">同步设备数据</a-button>
        <a-button type="text" @click="handleExportDevices">导出设备</a-button>
    </div>
</div>
```

**修改后:**
```vue
<SectionTitle title="设备信息">
    <template #right>
        <div class="header-actions">
            <a-button type="text" @click="handleSyncDevices">同步设备数据</a-button>
            <a-button type="text" @click="handleExportDevices">导出设备</a-button>
        </div>
    </template>
</SectionTitle>
```

### 3. 样式调整

#### 删除自定义 h3 样式
**删除的样式:**
```less
h3 {
    margin-bottom: 12px;
    color: #1890ff;
    border-left: 3px solid #1890ff;
    padding-left: 8px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
        margin: 0;
        color: #1890ff;
        border-left: 3px solid #1890ff;
        padding-left: 8px;
    }
}
```

#### 调整间距
**品牌卡片间距:**
```less
.brand-cards {
    display: flex;
    gap: 12px;
    margin-top: 16px; // 新增
}
```

**筛选区域间距:**
```vue
<!-- 房源筛选 -->
<div class="filter-section" style="margin-top: 16px;">

<!-- 设备筛选 -->
<div class="filter-section" style="margin-top: 16px;">
```

## SectionTitle 组件特性

### 1. 基本用法
```vue
<SectionTitle title="标题文本" />
```

### 2. 带右侧内容
```vue
<SectionTitle title="标题文本">
    <template #right>
        <div>右侧内容</div>
    </template>
</SectionTitle>
```

### 3. 组件样式特点
- **背景**: 线性渐变 `linear-gradient(270deg, #ffffff, #e9edf4)`
- **高度**: 40px
- **左侧标记**: 4px 宽的主题色竖条
- **字体**: 加粗显示
- **布局**: flex 布局，支持左右分布

## 优势

### 1. 样式统一
- 所有标题使用相同的 SectionTitle 组件
- 保持项目整体视觉一致性
- 符合设计规范

### 2. 维护性
- 统一的组件便于后续样式调整
- 减少重复的 CSS 代码
- 遵循组件化开发原则

### 3. 功能完整
- 支持纯标题显示
- 支持右侧操作按钮
- 自动处理间距和布局

### 4. 符合项目规范
- 遵循项目开发规范中的标题使用要求
- 与其他页面保持一致的用户体验

## 对比效果

### 修改前
- 自定义 h3 样式
- 手动处理布局和间距
- 样式不统一

### 修改后
- 统一的 SectionTitle 组件
- 自动处理布局和样式
- 符合项目设计规范
- 更好的视觉效果

## 注意事项

1. **间距调整**: SectionTitle 组件有自己的内边距，需要调整相邻元素的间距
2. **右侧内容**: 使用 `#right` 插槽来放置右侧操作按钮
3. **样式继承**: 删除原有的自定义 h3 样式，避免样式冲突

这次修改使批量绑定抽屉的标题样式与项目中其他页面保持一致，提升了整体的用户体验和代码维护性。
