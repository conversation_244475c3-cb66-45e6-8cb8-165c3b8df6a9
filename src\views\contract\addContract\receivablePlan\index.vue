<template>
    <div class="receivable-plan">
        <a-card :bordered="false" :body-style="{ padding: '0 16px 16px' }">
            <infoOverview ref="infoOverviewRef" />
            <earnestInfo v-if="contractCosts.bookings && contractCosts.bookings.length > 0" />
            <depositInfo />
            <rentInfo v-if="contractData.contractMode === 0" ref="rentInfoRef" />
            <nonStandardRentInfo v-else-if="contractData.contractMode === 1" ref="nonStandardRentInfoRef" />
            <otherFee />
        </a-card>
    </div>
</template>

<script setup>
import infoOverview from './components/infoOverview.vue';
import earnestInfo from './components/earnestInfo.vue';
import depositInfo from './components/depositInfo.vue';
import rentInfo from './components/rentInfo.vue';
import nonStandardRentInfo from './components/nonStandardRentInfo.vue';
import otherFee from './components/otherFee.vue';
import { useContractStore } from '@/store/modules/contract'

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractCosts)
const contractData = computed(() => contractStore.contractData)

// 获取组件引用
const infoOverviewRef = ref()
const rentInfoRef = ref()
const nonStandardRentInfoRef = ref()

// 校验方法
const validate = () => {
    console.log('开始应收计划校验:', {
        contractMode: contractData.value.contractMode,
        hasStandardRef: !!rentInfoRef.value,
        hasNonStandardRef: !!nonStandardRentInfoRef.value
    })
    
    try {
        // 根据合同模式进行不同的校验
        if (contractData.value.contractMode === 0) {
            // 标准合同：校验标准租金信息组件
            if (!rentInfoRef.value) {
                console.error('标准租金信息组件引用不存在')
                return false
            }
            console.log('调用标准租金信息组件校验')
            const result = rentInfoRef.value.validate()
            console.log('标准租金信息组件校验结果:', result)
            return result !== false
        } else if (contractData.value.contractMode === 1) {
            // 非标准合同：校验非标准租金信息组件
            if (!nonStandardRentInfoRef.value) {
                console.error('非标准租金信息组件引用不存在')
                return false
            }
            console.log('调用非标准租金信息组件校验')
            const result = nonStandardRentInfoRef.value.validate()
            console.log('非标准租金信息组件校验结果:', result)
            return result !== false
        }
        
        console.log('未知合同模式，跳过校验')
        return true
    } catch (error) {
        console.error('应收计划校验异常:', error)
        return false
    }
}

// 暴露校验方法和组件引用给父组件
defineExpose({
    validate,
    infoOverviewRef,
    rentInfoRef,
    nonStandardRentInfoRef
})
</script>

<style lang="less" scoped></style>