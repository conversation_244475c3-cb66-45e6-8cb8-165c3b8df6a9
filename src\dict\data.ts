import type { DictItem } from './types';

/** 字典类型枚举 */
export const DictType = {
    CONTRACT_STATUS: 'contract_status',
    CONTRACT_PURPOSE: 'contract_purpose',
    DIVERGENCE_PURPOSE: 'diversification_purpose',
    CONTRACT_TYPE: 'contract_type',
    CONTRACT_SIGN_TYPE: 'contract_sign_type',
    CONTRACT_APPROVE_STATUS: 'contract_approve_status',
    CONTRACT_OPERATE_TYPE: 'contract_operate_type',
    CONTRACT_SIGN_WAY: 'contract_sign_way',
    COST_TYPE_AND_TAX_RATE: 'cost_type_and_tax_rate',
    CONTRACT_STATUS_DETAIL: 'contract_status_detail',
    // 套打模板相关字典
    TEMPLATE_PRINT_TYPE: 'template_print_type',
    TEMPLATE_PURPOSE_CONTRACT: 'template_purpose_contract',
    TEMPLATE_PURPOSE_AGREEMENT: 'template_purpose_agreement',
    TEMPLATE_PURPOSE_NOTICE: 'template_purpose_notice',
    // 保证金类型字典
    DEPOSIT_TYPE: 'deposit_type',
} as const;

export type DictTypeKeys = (typeof DictType)[keyof typeof DictType];

/** 所有字典数据配置 */
export const dictData: Record<string, DictItem[]> = {
    /** 合同状态字典 */
    [DictType.CONTRACT_STATUS]: [
        { label: '草稿', value: 10 },
        { label: '待生效', value: 20 },
        { label: '生效', value: 30 },
        { label: '失效', value: 40 },
        { label: '作废', value: 50 },
    ],

    /** 合同用途字典（支持嵌套） */
    [DictType.CONTRACT_PURPOSE]: [
        {
            label: '宿舍',
            value: 10,
        },
        {
            label: '厂房',
            value: 20,
        },
        {
            label: '商业',
            value: 30,
            children: [
                {
                    label: '商铺',
                    value: 31,
                },
                {
                    label: '综合体',
                    value: 32,
                },
                {
                    label: '中央食堂',
                    value: 33,
                },
            ],
        },
        {
            label: '车位',
            value: 40,
        },
        {
            label: '办公',
            value: 50,
        },
    ],

    /** 多经用途字典 */
    [DictType.DIVERGENCE_PURPOSE]: [
        {
            label: '宿舍',
            value: 10,
        },
        {
            label: '厂房',
            value: 20,
        },
        {
            label: '商业',
            value: 30,
            children: [
                {
                    label: '商铺',
                    value: 31,
                },
                {
                    label: '综合体',
                    value: 32,
                },
                {
                    label: '中央食堂',
                    value: 33,
                },
            ],
        },
        {
            label: '车位',
            value: 40,
        },
        {
            label: '办公',
            value: 50,
        },
        {
            label: '广告位',
            value: 60,
        },
        {
            label: '设备类',
            value: 70,
        },
        {
            label: '日租房',
            value: 80,
        },
        {
            label: '其他',
            value: 90,
        }
    ],

    /** 合同类型 */
    [DictType.CONTRACT_TYPE]: [
        { label: '非宿舍', value: 0 },
        { label: '宿舍', value: 1 },
        { label: '多经', value: 2 },
        { label: '日租房', value: 3 },
    ],

    /** 合同签约类型 */
    [DictType.CONTRACT_SIGN_TYPE]: [
        { label: '新签', value: 0 },
        { label: '续签', value: 1 }
    ],

    /** 合同审批状态 */
    [DictType.CONTRACT_APPROVE_STATUS]: [
        { label: '待审核', value: 0 },
        { label: '审核中', value: 1 },
        { label: '审核通过', value: 2 },
        { label: '审核拒绝', value: 3 }
    ],

    /** 合同操作类型 */
    [DictType.CONTRACT_OPERATE_TYPE]: [
        { label: '新签', value: 0 },
        { label: '变更条款', value: 1 },
        { label: '退租', value: 2 },
        { label: '扩租', value: 3 },
        { label: '换房', value: 4 },
    ],

    /** 合同签约方式 */
    [DictType.CONTRACT_SIGN_WAY]: [
        { label: '电子合同', value: 0 },
        { label: '纸质合同（甲方电子章）', value: 1 },
        { label: '纸质合同（双方实体章）', value: 2 }
    ],

    /** 费用类型&税率 */
    [DictType.COST_TYPE_AND_TAX_RATE]: [
        { label: '定金', value: 10 },
        { label: '保证金', value: 20 },
        { label: '租金', value: 30 },
        { label: '罚没金', value: 40 },
        { label: '代收水电', value: 50 },
    ],

    /** 合同状态详细 */
    [DictType.CONTRACT_STATUS_DETAIL]: [
        { label: '草稿', value: 0 },
        { label: '待生效（合同期未开始）', value: 10 },
        { label: '待生效（保证金未收齐）', value: 20 },
        { label: '待生效（合同期未开始&保证金未收齐）', value: 30 },
        { label: '生效', value: 40 },
        { label: '失效（正常退租）', value: 50 },
        { label: '失效（提前退租）', value: 60 },
        { label: '失效（换房）', value: 70 },
        { label: '失效（续签）', value: 80 },
        { label: '失效（正常退租待出场结算）', value: 90 },
        { label: '失效（提前退租待出场结算）', value: 100 },
        { label: '失效（待退租）', value: 110 },
        { label: '作废', value: 120 },
        // { label: '作废（重签）', value: 130 },
    ],

    /** 套打模板类型 */
    [DictType.TEMPLATE_PRINT_TYPE]: [
        { label: '合同', value: 1 },
        { label: '协议', value: 2 },
        { label: '通知单', value: 3 },
    ],

    /** 套打模板用途 - 合同 */
    [DictType.TEMPLATE_PURPOSE_CONTRACT]: [
        { label: '宿舍', value: 10 },
        { label: '厂房', value: 20 },
        { label: '商铺', value: 31 },
        { label: '综合体', value: 32 },
        { label: '中央食堂', value: 33 },
        { label: '车位', value: 40 },
        { label: '办公', value: 50 },
        { label: '多经', value: 90 },
    ],

    /** 套打模板用途 - 协议 */
    [DictType.TEMPLATE_PURPOSE_AGREEMENT]: [
        { label: '定单', value: 10 },
        { label: '费用条款及价格变更', value: 20 },
        { label: '条款变更', value: 30 },
        { label: '主体变更', value: 40 },
        { label: '补充协议', value: 50 },
        { label: '退租', value: 60 },
        { label: '换房', value: 70 },
        { label: '缩租', value: 80 },
        { label: '扩租', value: 90 },
        { label: '租金减免', value: 100 },
    ],

    /** 套打模板用途 - 通知单 */
    [DictType.TEMPLATE_PURPOSE_NOTICE]: [
        { label: '定金结转（租金）', value: 10 },
        { label: '定金结转（保证金）', value: 20 },
        { label: '退定通知单', value: 25 },
        { label: '延期支付租金申请', value: 30 },
        { label: '催款函', value: 40 },
        { label: '缴款通知单', value: 50 },
    ],

    /** 保证金类型字典 */
    [DictType.DEPOSIT_TYPE]: [
        { label: '自定义金额', value: 0 },
        { label: '押一', value: 1 },
        { label: '押二', value: 2 },
        { label: '押三', value: 3 },
        { label: '押四', value: 4 },
        { label: '押五', value: 5 },
        { label: '押六', value: 6 },
        { label: '押七', value: 7 },
        { label: '押八', value: 8 },
        { label: '押九', value: 9 },
        { label: '押十', value: 10 },
        { label: '押十一', value: 11 },
        { label: '押十二', value: 12 },
    ],
};
