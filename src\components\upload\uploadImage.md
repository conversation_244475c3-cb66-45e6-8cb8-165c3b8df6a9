# UploadImage 图片上传组件

基于 Arco Design Pro 的图片上传组件，支持单图或多图上传、预览、编辑、删除等操作。
组件使用 JSON 字符串格式进行双向绑定，自动处理数据序列化。

## 功能特性

- 支持单图和多图两种模式
- 支持图片上传、预览、编辑和删除
- 支持双向绑定，使用 v-model 管理图片列表（JSON 字符串格式）
- 支持自定义上传接口
- 支持限制图片类型、大小和数量
- 支持只读模式和禁用状态
- 支持自定义图片对象属性名映射
- 美观的图片列表展示
- 自动处理图片数据的序列化和反序列化

## 基本用法

```vue
<template>
  <upload-image v-model="imageListStr" :limit="1" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UploadImage from '@/components/upload/uploadImage.vue';

// 注意：现在使用字符串格式，组件内部会自动处理 JSON 序列化
const imageListStr = ref('');
</script>
```

## 配置自定义参数

```vue
<template>
  <upload-image
    v-model="imageListStr"
    :multiple="true"
    accept=".jpg,.jpeg,.png,.gif"
    :maxSize="2"
    :limit="5"
    :readonly="isReadOnly"
    :disabled="isDisabled"
    :fileItemProps="{ fileId: 'id', name: 'fileName', url: 'fileUrl' }"
    @success="handleSuccess"
    @error="handleError"
  />
</template>

<script setup>
import { ref } from 'vue';

const imageListStr = ref('');
</script>
```

## 只读模式

```vue
<template>
  <upload-image
    v-model="imageListStr"
    :readonly="true"
  />
</template>
```

## 属性说明

| 属性名      | 类型                    | 默认值                              | 说明                            |
| ----------- | ----------------------- | ----------------------------------- | ------------------------------- |
| modelValue  | String                  | ''                                  | 绑定值，图片列表的 JSON 字符串   |
| multiple    | Boolean                 | false                               | 是否为多图模式                  |
| uploadApi   | Function                | -                                   | 上传接口函数，默认使用公共上传接口  |
| accept      | String                  | 'image/*'                           | 允许上传的图片类型，如 '.jpg,.png' |
| maxSize     | Number                  | 5                                   | 图片大小限制，单位 MB           |
| limit       | Number                  | 1                                   | 图片数量限制，0 表示不限制      |
| readonly    | Boolean                 | false                               | 只读模式，不显示上传和删除按钮  |
| disabled    | Boolean                 | false                               | 是否禁用                        |
| fileItemProps | Object                | { fileId: 'fileId', name: 'name', url: 'url' } | 自定义图片对象属性名映射        |

### fileItemProps 参数说明

| 属性名 | 类型   | 默认值  | 说明               |
| ------ | ------ | ------- | ------------------ |
| fileId | String | 'fileId' | 图片ID属性名      |
| name   | String | 'name'  | 图片名属性名       |
| url    | String | 'url'   | 图片URL属性名      |

## 事件说明

| 事件名        | 参数                              | 说明                     |
| ------------- | --------------------------------- | ------------------------ |
| update:modelValue | (imageListStr: String)            | 图片列表更新时触发，返回 JSON 字符串 |
| success       | (response: any, fileItem: Object)   | 图片上传成功时触发       |
| error         | (error: any, fileItem: Object)   | 图片上传失败时触发       |
| exceed-limit  | (files: Array)                   | 超出图片数量限制时触发   |
| exceed-size   | (file: Object)                   | 超出图片大小限制时触发   |

## 数据格式说明

组件使用 JSON 字符串格式进行数据绑定，内部会自动处理序列化和反序列化。

### 输入格式（v-model 绑定的值）

```js
// 空字符串表示无图片
''

// 或者 JSON 字符串格式
'[{"fileName": "图片.jpg", "fileUrl": "https://example.com/image.jpg"}]'
```

### 输出格式（组件内部的图片对象结构）

```js
[
  {
    fileName: "图片1.jpg",
    fileUrl: "https://example.com/image1.jpg"
  },
  {
    fileName: "图片2.png", 
    fileUrl: "https://example.com/image2.png"
  }
]
```

## 进阶用法

### 自定义图片属性名

组件默认使用 `fileId`、`name`、`url` 作为图片对象的属性名，你可以通过 `fileItemProps` 属性自定义：

```vue
<template>
  <upload-image
    v-model="imageListStr"
    :fileItemProps="{
      fileId: 'id',
      name: 'fileName',
      url: 'imageUrl'
    }"
  />
</template>
```

这样，组件输出的图片对象结构将变为：

```js
{
  id: '123',       // 而不是 fileId: '123'
  fileName: '图片.jpg', // 而不是 name: '图片.jpg'
  imageUrl: 'https://example.com/image.jpg' // 而不是 url: 'https://example.com/image.jpg'
}
```

### 自定义上传接口

```vue
<template>
  <upload-image
    v-model="imageListStr"
    :uploadApi="uploadImage"
  />
</template>

<script setup>
import { ref } from 'vue';
import { uploadImage } from '@/api/common';

const imageListStr = ref('');
</script>
```

### 完整示例

```vue
<template>
  <div>
    <h3>图片上传</h3>
    
    <div class="mode-switch">
      <a-radio-group v-model="uploadMode">
        <a-radio :value="1">单图模式</a-radio>
        <a-radio :value="9">多图模式</a-radio>
      </a-radio-group>
    </div>
    
    <upload-image
      v-model="imageListStr"
      :limit="uploadMode"
      :uploadApi="uploadImage"
      accept=".jpg,.jpeg,.png,.gif"
      :maxSize="2"
      :readonly="readonly"
      :fileItemProps="{
        fileId: 'id',
        name: 'fileName',
        url: 'imageUrl'
      }"
      @success="handleSuccess"
      @error="handleError"
      @exceed-limit="handleExceedLimit"
      @exceed-size="handleExceedSize"
    />
    
    <div class="actions">
      <a-switch v-model="readonly" />
      <span>只读模式</span>
    </div>
    
    <div class="image-preview" v-if="imageListStr">
      <h4>图片列表（JSON 字符串）</h4>
      <pre>{{ imageListStr }}</pre>
      
      <h4>解析后的图片列表</h4>
      <pre>{{ JSON.stringify(parsedImageList, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import UploadImage from '@/components/upload/uploadImage.vue';
import { uploadImage } from '@/api/common';

const uploadMode = ref(1); // 默认单图模式
const imageListStr = ref('');
const readonly = ref(false);

// 解析图片列表用于显示
const parsedImageList = computed(() => {
  if (!imageListStr.value) return [];
  try {
    return JSON.parse(imageListStr.value);
  } catch {
    return [];
  }
});

const handleSuccess = (response, file) => {
  Message.success(`图片 ${file.name} 上传成功`);
};

const handleError = (error, file) => {
  Message.error(`图片 ${file.name} 上传失败`);
};

const handleExceedLimit = (files) => {
  Message.warning('超出图片数量限制');
};

const handleExceedSize = (file) => {
  Message.warning(`图片 ${file.name} 超出大小限制`);
};
</script>
```

## 注意事项

1. **数据格式**：组件现在使用 JSON 字符串格式进行双向绑定，而不是数组
2. **空值处理**：空字符串 `''` 表示没有图片
3. **数据解析**：如果需要在父组件中操作图片列表，使用 `JSON.parse()` 解析字符串
4. **表单集成**：可以直接绑定到表单字段，方便与后端 API 交互
5. **向后兼容**：如果您的项目之前使用数组格式，需要相应调整绑定的数据类型
6. **默认限制**：默认限制为 1 张图片，适合单图场景

### 与表单集成

```vue
<template>
  <a-form :model="formData" @submit="handleSubmit">
    <a-form-item label="上传图片" field="images">
      <upload-image v-model="formData.images" :limit="3" />
    </a-form-item>
    <a-form-item>
      <a-button html-type="submit">提交</a-button>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from 'vue';

const formData = ref({
  images: '' // 字符串格式
});

const handleSubmit = () => {
  // formData.images 是 JSON 字符串
  console.log(formData.value.images);
  
  // 如果需要解析为数组，可以使用 JSON.parse
  if (formData.value.images) {
    const imageList = JSON.parse(formData.value.images);
    console.log(imageList);
  }
};
</script>
``` 