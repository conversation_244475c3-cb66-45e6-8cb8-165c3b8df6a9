<template>
    <div class="expand-apply">
        <section>
            <sectionTitle title="基本信息" />
            <a-card :bordered="false" :body-style="{ padding: '16px 32px' }">
                <a-form :model="formData" :rules="rules" :wrapper-col-props="{ span: 16 }" auto-label-width>
                    <a-grid :cols="3" :col-gap="16" :row-gap="0">
                        <a-grid-item>
                            <a-form-item field="contractNo" label="合同编号">
                                <a-input v-model="contractData.contractNo" disabled />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="contractPeriod" label="合同周期">
                                <a-input v-model="contractPeriod" disabled />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="signDate" label="签订时间">
                                <a-input v-model="contractData.signDate" disabled />
                            </a-form-item>
                        </a-grid-item>
                    </a-grid>
                    <a-grid :cols="3" :col-gap="16" :row-gap="0">
                        <a-grid-item>
                            <a-form-item field="contractPurposeLabel" label="合同用途">
                                <a-input v-model="contractPurposeLabel" disabled />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="ourSigningParty" label="承租方名称">
                                <a-input v-model="contractData.ourSigningParty" disabled />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="customerTypeLabel" label="承租类型">
                                <a-input v-model="customerTypeLabel" disabled />
                            </a-form-item>
                        </a-grid-item>
                    </a-grid>
                    <a-grid :cols="3" :col-gap="16" :row-gap="0">
                        <a-grid-item>
                            <a-form-item field="changeDate" label="扩租日期">
                                <a-date-picker v-model="formData.changeDate" style="width: 100%" placeholder="请选择日期"
                                :disabled-date="disabledChangeDate" format="YYYY-MM-DD" :disabled="props.isViewMode" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="remark" label="备注">
                                <a-input v-model="formData.remark" placeholder="请输入" :disabled="props.isViewMode" />
                            </a-form-item>
                        </a-grid-item>
                    </a-grid>
                </a-form>
            </a-card>
        </section>
        <section>
            <sectionTitle title="扩租原有房源信息" />
            <a-card :bordered="false" :body-style="{ padding: '16px 0 0' }">
                <a-table :data="originalRooms" :columns="columns" :bordered="{ cell: true }" rowKey="roomId"></a-table>
            </a-card>
        </section>
        <section>
            <sectionTitle title="扩租新房源选择" />
            <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
                <div class="content">
                    <a-space>
                        <a-input v-model="roomNames" readonly @click="handleOpenRoomDrawer"
                            placeholder="请选择房源" />
                        <a-button type="primary" @click="handleOpenRoomDrawer" :disabled="props.isViewMode">选择房源</a-button>
                    </a-space>
                    <div class="table-content">
                        <a-table :data="tableData" :columns="newColumns" :bordered="{ cell: true }"
                            :pagination="{ showJumper: true }" :scroll="{ x: 1200 }">
                            <template #roomName="{ record }">
                                {{ record.parcelName }}-{{ record.buildingName }}-{{ record.roomName }}
                            </template>
                            <template #area="{ record }">
                                {{ record.area }}
                            </template>
                            <template #standardUnitPrice="{ record }">
                                {{ record.standardUnitPrice }}
                            </template>
                            <template #discount="{ record }">
                                <a-input-number
                                    v-model="record.discount" style="background-color: #fff;" @change="onDiscountChange(record, $event)" :disabled="props.isViewMode">
                                    <template #suffix>%</template>
                                </a-input-number>
                            </template>
                            <template #signedUnitPrice="{ record }">
                                <a-input-number v-model="record.signedUnitPrice" style="background-color: #fff;" @change="onSignedUnitPriceChange(record, $event)" :disabled="props.isViewMode">
                                    <template #suffix>{{ unitMap.get(record.priceUnit) }}</template>
                                </a-input-number>
                            </template>
                            <template #signedMonthlyPrice="{ record }">
                                {{ formatAmount(record.signedMonthlyPrice) }}
                            </template>
                            <template #operations="{ record }">
                                <a-button type="text" size="mini" @click="removeRoom(record)" :disabled="props.isViewMode">移除</a-button>
                            </template>
                        </a-table>
                    </div>
                </div>
            </a-card>
        </section>
        <selectRoomsDrawer ref="selectRoomRef" @submit="getRoomList" />
    </div>
</template>
<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue';
import { ref, computed, watch, nextTick, useTemplateRef } from 'vue';
import dayjs from 'dayjs';
import { useContractStore } from '@/store/modules/contract/index';
import { ContractAddDTO, ContractVo, getContractDetailBill, print, getContractTemplateList } from '@/api/contract';
import { getDictLabel } from '@/dict'
import selectRoomsDrawer from '../../components/selectRoomsDrawer.vue'
import { ContractRoomDTO } from '@/api/contract';
import { getOrderList } from '@/api/orderManagement'

// 定义props接收contractChangeDetail数据和查看模式
interface Props {
    contractChangeDetail?: any;
    isViewMode?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    contractChangeDetail: () => ({}),
    isViewMode: false
});

// 表单数据，支持双向绑定
const formData = ref({
    remark: '',
    changeDate: '' as string | any,
    changeRooms: [] as ContractRoomDTO[]
});

// 只在初始化时监听props变化，更新本地表单数据
watch(() => props.contractChangeDetail, (newVal) => {
    console.log('newVal', newVal);
    if (newVal) {
        formData.value = {
            remark: newVal.remark || '',
            changeDate: newVal.changeDate ? dayjs(newVal.changeDate) : '',
            changeRooms: newVal.changeRooms || []
        };
        // 使用nextTick确保tableData已经初始化
        nextTick(() => {
            if (newVal.changeRooms && newVal.changeRooms.length > 0) {
                // 扩租时changeRooms只包含新房源，直接显示所有房源
                let tempData = newVal.changeRooms.filter((room: ContractRoomDTO) => {
                    // 确保房源类型为新房源
                    room.type = 1;
                    return true; // 显示在tableData中
                });
                tableData.value = [...tempData];
            }
        });
    }
}, { immediate: true, deep: false });

const contractStore = useContractStore();
const contractData = computed(() => contractStore.contractData as ContractAddDTO);

const contractPeriod = computed(() => {
    return contractData.value.startDate + ' - ' + contractData.value.endDate;
});

const contractPurposeLabel = computed(() => {
    return getDictLabel('diversification_purpose', Number(contractData.value.contractPurpose))
})
const customerTypeLabel = computed(() => {
    return  contractData.value.customer.customerType === 1 ? '个人' : '企业'
})

// 禁用变更执行日期的函数
const disabledChangeDate = (date: Date) => {
    const currentDate = dayjs(date)
    const startDate = contractData.value.startDate
    const endDate = contractData.value.endDate
    
    // 如果合同开始日期和结束日期都存在，则限制在这个范围内
    // 扩租日期必须晚于合同开始日期，早于合同结束日期（不包含开始和结束日期）
    if (startDate && endDate) {
        return !currentDate.isAfter(dayjs(startDate), 'day') || !currentDate.isBefore(dayjs(endDate), 'day')
    }
    
    // 如果都没有，则不限制
    return false
}

const roomNames = computed(() => {
    // 扩租时changeRooms只包含新房源，直接显示所有房源名称
    const newRooms = formData.value.changeRooms || [];
    return newRooms.map(item => item.roomName).join(',');
})

// 过滤出原有房源，排除新房源
const originalRooms = computed(() => {
    const allRooms = contractData.value.rooms || [];
    const newRoomIds = formData.value.changeRooms?.map(room => room.roomId) || [];
    return allRooms.filter(room => !newRoomIds.includes(room.roomId));
});

const tableData = ref<ContractRoomDTO[]>([]);



const rules = ref({
    changeDate: [
        { required: true, message: '请选择扩租日期' },
        {
            validator: (value: string, callback: (error?: string) => void) => {
                if (value) {
                    const changeDate = dayjs(value)
                    const startDate = contractData.value.startDate
                    const endDate = contractData.value.endDate
                    
                    if (startDate && endDate) {
                        if (!changeDate.isAfter(dayjs(startDate), 'day')) {
                            callback(`扩租日期必须晚于合同开始日期（${startDate}）`)
                            return
                        }
                        if (!changeDate.isBefore(dayjs(endDate), 'day')) {
                            callback(`扩租日期必须早于合同结束日期（${endDate}）`)
                            return
                        }
                    } else if (startDate) {
                        if (!changeDate.isAfter(dayjs(startDate), 'day')) {
                            callback(`扩租日期必须晚于合同开始日期（${startDate}）`)
                            return
                        }
                    } else if (endDate) {
                        if (!changeDate.isBefore(dayjs(endDate), 'day')) {
                            callback(`扩租日期必须早于合同结束日期（${endDate}）`)
                            return
                        }
                    }
                }
                callback()
            }
        }
    ]
});

const columns = ref([
    {
        title: '房源名称',
        dataIndex: 'name',
        width: 300,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'name',
        render: ({ record }: { record: any }) => {
            return record.parcelName + '-' + record.buildingName + "-" + record.roomName
        },
    },
    {
        title: '租赁面积（㎡）',
        dataIndex: 'area',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'area',
        width: 120
    },
]);

// 新房源表格列定义
const newColumns = ref([
    {
        title: '房源名称',
        dataIndex: 'roomName',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'roomName'
    },
    {
        title: '租赁面积（㎡）',
        dataIndex: 'area',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'area'
    },
    {
        title: '标准租金',
        dataIndex: 'standardUnitPrice',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'standardUnitPrice'
    },
    {
        title: '折扣',
        dataIndex: 'discount',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'discount'
    },
    {
        title: '签约单价',
        dataIndex: 'signedUnitPrice',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'signedUnitPrice'
    },
    {
        title: '签约月总价（元/月）',
        dataIndex: 'signedMonthlyPrice',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'signedMonthlyPrice'
    },
    {
        title: '操作',
        dataIndex: 'operations',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'operations',
        fixed: 'right',
    },
]);

// 选择房源
const selectRoomRef = useTemplateRef('selectRoomRef')
const handleOpenRoomDrawer = () => {
    selectRoomRef.value?.openDrawer(contractData.value.rooms || [], formData.value.changeRooms || [])
}

const getRoomList = (data: ContractRoomDTO[]) => {
    console.log('获取到的房源数据:', data)
    // 过滤出新房源，只保留不在原有合同中的房源
    let tempData = data.filter(room => {
        // 检查该房源是否在原有合同中存在
        const existingRoom = contractData.value.rooms.find(item => item.roomId === room.roomId);
        if (existingRoom) {
            // 如果存在，设置type为2（原有房源），但不显示在tableData中
            room.type = 2;
            return false; // 不显示在tableData中
        } else {
            // 如果不存在，设置type为1（新房源）
            room.type = 1;
            // 初始化价格相关字段
            if (!room.discount) {
                room.discount = 100; // 默认折扣为100%
            }
            room.signedUnitPrice = (room.standardUnitPrice ?? 0) * (room.discount / 100);
            calcSignedMonthlyPrice(room);
            return true; // 显示在tableData中
        }
    });
    
    tableData.value = tempData;
    // 扩租时changeRooms只包含新房源
    const newRooms = tempData.map(room => ({
        ...room,
        type: 1, // 新房源
        isChecked: true
    }));
    formData.value.changeRooms = newRooms;
    loadValidOrders()
}

// 加载房间有效定单 - 只处理新房源的定单
const loadValidOrders = async () => {
    // 扩租时changeRooms只包含新房源，直接获取所有房源的ID
    const newRoomIds = formData.value.changeRooms?.map(item => item.roomId) || [];
    
    if (newRoomIds.length === 0) {
        // 如果没有新房源，清空定单
        contractData.value.bookings = [];
        contractData.value.bookingRelType = 1;
        return;
    }
    
    const { rows } = await getOrderList({
        pageNum: 1,
        pageSize: 10000,
        status: 2,
        roomIds: newRoomIds.join(','),
        projectId: contractStore.currentProjectId,
    })
    
    if (rows.length === 0) {
        contractData.value.bookings = []
        contractData.value.bookingRelType = 1
    } else {
        const bookings = rows.map((item: any) => {
            return {
                bookingId: item.id,
                bookedRoom: item.roomName,
                bookerName: item.customerName,
                bookingReceivedAmount: item.receivedAmount,
                bookingPaymentDate: item.receivedDate,
            }
        })

        if (contractStore.editType === 'toSign' && !!contractData.value.bookings && contractData.value.bookings.length > 0) {
            // 转签约需要判断房源获取的定单中是否包含转签约带过来的定单，如果包含，则类型为房间有效定单，否则为其他定单
            const isInclude = bookings.some((item: any) => item.bookingId === contractData.value.bookings?.[0]?.bookingId)
            if (isInclude) {
                contractData.value.bookingRelType = 0
                contractData.value.bookings = bookings
            } else {
                contractData.value.bookingRelType = 2
                contractData.value.bookings = contractData.value.bookings?.concat(bookings)
            }
        } else {
            contractData.value.bookingRelType = 0
            // 格式化定单列表
            contractData.value.bookings = bookings
        }
    }
}

// 移除房源
const removeRoom = (room: ContractRoomDTO) => {
    const index = tableData.value.findIndex(r => r.roomId === room.roomId);
    if (index > -1) {
        tableData.value.splice(index, 1);
        // 扩租时changeRooms只包含新房源
        const newRooms = tableData.value.map(room => ({
            ...room,
            type: 1, // 新房源
            isChecked: true
        }));
        formData.value.changeRooms = newRooms;
    }
}

const isDiscountEditing = ref(false);
const isSignedUnitPriceEditing = ref(false);

// 更新签约单价
const updateSignedUnitPrice = (room: ContractRoomDTO) => {
    if (isSignedUnitPriceEditing.value) return; // 避免循环依赖
    isDiscountEditing.value = true;
    // 保留8位小数
    room.signedUnitPrice = Number(((room.standardUnitPrice ?? 0) * (room.discount / 100)).toFixed(8));
    calcSignedMonthlyPrice(room);
    isDiscountEditing.value = false;
    // 扩租时changeRooms只包含新房源
    const newRooms = tableData.value.map(room => ({
        ...room,
        type: 1, // 新房源
        isChecked: true
    }));
    formData.value.changeRooms = newRooms;
};

// 更新折扣
const updateDiscount = (room: ContractRoomDTO) => {
    if (isDiscountEditing.value) return; // 避免循环依赖
    isSignedUnitPriceEditing.value = true;
    if (room.standardUnitPrice) {
        room.discount = Number(((room.signedUnitPrice / room.standardUnitPrice) * 100).toFixed(2));
    } else {
        room.discount = 100; // 标准租金为0时，默认折扣为100%
    }
    calcSignedMonthlyPrice(room);
    isSignedUnitPriceEditing.value = false;
    // 扩租时changeRooms只包含新房源
    const newRooms = tableData.value.map(room => ({
        ...room,
        type: 1, // 新房源
        isChecked: true
    }));
    formData.value.changeRooms = newRooms;
};

// 折扣输入框变化
const onDiscountChange = (record: ContractRoomDTO, value: number) => {
    record.discount = value;
    updateSignedUnitPrice(record);
};

// 签约单价输入框变化
const onSignedUnitPriceChange = (record: ContractRoomDTO, value: number) => {
    record.signedUnitPrice = value;
    updateDiscount(record);
};

const unitMap = new Map([
    [1, '元/平方米/月'],
    [2, '元/月']
])

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 计算签约月总价
const calcSignedMonthlyPrice = (room: ContractRoomDTO) => {
    if (room.signedUnitPrice && room.area) {
        if (room.priceUnit === 1) {
            // 元/平方米/月，需要乘以面积
            room.signedMonthlyPrice = Number((room.signedUnitPrice * room.area).toFixed(2))
        } else if (room.priceUnit === 2) {
            // 元/月，总价等于单价
            room.signedMonthlyPrice = room.signedUnitPrice
        }
    } else {
        room.signedMonthlyPrice = 0
    }
}

// 获取当前表单数据的方法，供父组件调用
const getFormData = () => {
    // 扩租时changeRooms只包含新房源
    const newRooms = tableData.value.map(room => ({
        ...room,
        type: 1, // 新房源
        isChecked: true
    }));
    
    return {
        ...props.contractChangeDetail,
        ...formData.value,
        changeRooms: newRooms,
        changeDate: formData.value.changeDate ? dayjs(formData.value.changeDate).format('YYYY-MM-DD') : ''
    };
};

// 暴露方法给父组件
defineExpose({
    formData,
    tableData,
    getFormData
});
</script>
<style lang="less" scoped>
.table-content {
    margin-top: 16px;
}

.margin-top-16 {
    margin-top: 16px;
}
</style>