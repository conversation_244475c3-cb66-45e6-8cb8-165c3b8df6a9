<template>
    <div class="container">
        <!-- 左侧背景区域 -->
         <div class="logo-img-content">
            <img class="logo-img" src="@/assets/images/logo-white.png" alt="万洋资管平台" />
         </div>
        <div class="banner">
            <div class="banner-content">
                <div class="logo-section">
                    <h1 class="logo-title">万洋资管平台</h1>
                    <p class="logo-subtitle">WANYANG ASSET MANAGEMENT PLATFORM</p>
                </div>
                <div class="banner-decoration">
                    <div class="decoration-text">
                        <h2>智慧管理 · 高效运营</h2>
                        <p>专业的资产管理解决方案</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧登录表单区域 -->
        <div class="content">
            <!-- 装饰性几何图形 -->
            <div class="decoration-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>
            
            <div class="content-inner">
                <div class="login-form-wrapper">
                    <div class="login-header">
                        <div class="login-form-title">欢迎登录</div>
                        <div class="login-form-sub-title">请输入您的账号密码</div>
                    </div>
                    <div class="login-form-error-msg">{{ errorMessage }}</div>
                    <a-form ref="loginForm" :model="userInfo" class="login-form" layout="vertical"
                        @submit="handleSubmit">
                        <a-form-item field="username" :rules="[
                            {
                                required: true,
                                message: $t('login.form.userName.errMsg'),
                            },
                        ]" :validate-trigger="['change', 'blur']" hide-label>
                            <a-input 
                                v-model="userInfo.username" 
                                :placeholder="$t('login.form.userName.placeholder')"
                                size="large"
                                class="login-input">
                                <template #prefix>
                                    <icon-user />
                                </template>
                            </a-input>
                        </a-form-item>
                        <a-form-item field="password" :rules="[
                            {
                                required: true,
                                message: $t('login.form.password.errMsg'),
                            },
                        ]" :validate-trigger="['change', 'blur']" hide-label>
                            <a-input-password 
                                v-model="userInfo.password" 
                                :placeholder="$t('login.form.password.placeholder')"
                                size="large"
                                class="login-input"
                                allow-clear>
                                <template #prefix>
                                    <icon-lock />
                                </template>
                            </a-input-password>
                        </a-form-item>
                        <a-space :size="20" direction="vertical">
                            <div class="login-form-password-actions">
                                <a-checkbox 
                                    checked="rememberPassword" 
                                    :model-value="loginConfig.rememberPassword"
                                    @change="setRememberPassword as any">
                                    {{ $t('login.form.rememberPassword') }}
                                </a-checkbox>
                                <a-link class="forgot-password">{{
                                    $t('login.form.forgetPassword')
                                }}</a-link>
                            </div>
                            <a-button 
                                type="primary" 
                                html-type="submit" 
                                long 
                                size="large"
                                class="login-btn"
                                :loading="loading">
                                {{ $t('login.form.login') }}
                            </a-button>
                            <!-- <a-button type="text" long size="large" class="login-form-register-btn">
                                {{ $t('login.form.register') }}
                            </a-button> -->
                        </a-space>
                    </a-form>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
// import Footer from '@/components/footer/index.vue';
// import LoginBanner from './components/banner.vue';
// import LoginForm from './components/login-form.vue';
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
import { useI18n } from 'vue-i18n';
import { useStorage } from '@vueuse/core';
import { useUserStore } from '@/store';
import useProjectStore from '@/store/modules/project';
import useLoading from '@/hooks/loading';
import type { LoginData } from '@/api/user';

const router = useRouter();
const { t } = useI18n();
const errorMessage = ref('');
const { loading, setLoading } = useLoading();
const userStore = useUserStore();
const projectStore = useProjectStore();

const loginConfig = useStorage('login-config', {
    rememberPassword: true,
    username: 'admin', // 演示默认值
    password: 'admin', // demo default value
});
const userInfo = reactive({
    username: loginConfig.value.username,
    password: loginConfig.value.password,
});

const handleSubmit = async ({
    errors,
    values,
}: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
}) => {
    if (loading.value) return;
    if (!errors) {
        setLoading(true);
        try {
            await userStore.login(values as LoginData);
            // 清空项目缓存，因为不同用户的项目权限不一致
            projectStore.setAssetProjectId('');
            projectStore.setRentProjectId('');
            // localStorage.setItem('token', 'test')
            const { redirect, ...othersQuery } =
                router.currentRoute.value.query;
            router.push({
                name: 'Workplace',
            });
            Message.success(t('login.form.login.success'));
            const { rememberPassword } = loginConfig.value;
            const { username, password } = values;
            // 实际生产环境需要进行加密存储。
            // The actual production environment requires encrypted storage.
            loginConfig.value.username = rememberPassword ? username : '';
            loginConfig.value.password = rememberPassword ? password : '';
        } catch (err) {
            errorMessage.value = (err as Error).message;
        } finally {
            setLoading(false);
        }
    }
};
const setRememberPassword = (value: boolean) => {
    loginConfig.value.rememberPassword = value;
};
</script>

<style lang="less" scoped>
.container {
    display: flex;
    height: 100vh;
    min-height: 600px;
    background: #f5f7fa;

    position: relative;

    .logo-img-content {
        position: absolute;
        z-index: 100;
        top: 20px;
        left: 20px;
        .logo-img {
            width: 160px;
            //height: 80px;
            margin-bottom: 20px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }
    }

    .banner {
        width: 55%;
        min-width: 500px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-image: url('@/assets/images/dashboard/banner.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
            z-index: 1;
        }

        .banner-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            color: white;

            .logo-section {
                text-align: center;
                margin-bottom: 60px;

                .logo-img {
                    // width: 180px;
                    // //height: 80px;
                    // margin-bottom: 20px;
                    // filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
                }

                .logo-title {
                    font-size: 36px;
                    font-weight: 700;
                    margin: 0 0 10px 0;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                    letter-spacing: 2px;
                }

                .logo-subtitle {
                    font-size: 14px;
                    opacity: 0.9;
                    margin: 0;
                    letter-spacing: 1px;
                    font-weight: 300;
                }
            }

            .banner-decoration {
                .decoration-text {
                    text-align: center;

                    h2 {
                        font-size: 28px;
                        font-weight: 600;
                        margin: 0 0 16px 0;
                        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                    }

                    p {
                        font-size: 16px;
                        opacity: 0.9;
                        margin: 0;
                        font-weight: 300;
                    }
                }
            }
        }
    }

    .content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        position: relative;

        // 添加装饰性背景图案
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 20%, rgba(64, 128, 255, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.03) 0%, transparent 50%);
            z-index: 1;
        }

        // 添加几何装饰元素
        &::after {
            content: '';
            position: absolute;
            top: 10%;
            right: 5%;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, rgba(64, 128, 255, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 50%;
            z-index: 1;
        }

        .content-inner {
            width: 100%;
            max-width: 400px;
            position: relative;
            z-index: 2;
        }

        // 装饰性几何图形
        .decoration-shapes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            pointer-events: none;

            .shape {
                position: absolute;
                border-radius: 50%;
                opacity: 0.6;
                animation: float 6s ease-in-out infinite;
            }

            .shape-1 {
                top: 15%;
                left: 10%;
                width: 60px;
                height: 60px;
                background: linear-gradient(135deg, rgba(64, 128, 255, 0.2), rgba(102, 126, 234, 0.1));
                animation-delay: 0s;
            }

            .shape-2 {
                top: 60%;
                right: 15%;
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, rgba(118, 75, 162, 0.2), rgba(64, 128, 255, 0.1));
                animation-delay: 2s;
            }

            .shape-3 {
                bottom: 20%;
                left: 15%;
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.1));
                animation-delay: 4s;
            }

            .shape-4 {
                top: 30%;
                right: 8%;
                width: 50px;
                height: 50px;
                background: linear-gradient(135deg, rgba(64, 128, 255, 0.15), rgba(102, 126, 234, 0.2));
                animation-delay: 1s;
            }
        }
    }

    // 浮动动画
    @keyframes float {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        25% {
            transform: translateY(-10px) rotate(90deg);
        }
        50% {
            transform: translateY(-5px) rotate(180deg);
        }
        75% {
            transform: translateY(-15px) rotate(270deg);
        }
    }
}

.login-form-wrapper {
    width: 100%;
    padding: 40px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 1px 0px rgba(255, 255, 255, 0.5) inset;
    border: 1px solid rgba(255, 255, 255, 0.3);

    .login-header {
        text-align: center;
        margin-bottom: 32px;

        .login-form-title {
            color: #1d2129;
            font-weight: 600;
            font-size: 28px;
            line-height: 36px;
            margin-bottom: 8px;
        }

        .login-form-sub-title {
            color: #86909c;
            font-size: 14px;
            line-height: 20px;
            margin: 0;
        }
    }

    .login-form-error-msg {
        height: 32px;
        color: rgb(var(--red-6));
        line-height: 32px;
        text-align: center;
        font-size: 14px;
        margin-bottom: 16px;
    }

    .login-form {
        .login-input {
            height: 48px;
            border-radius: 8px;
            margin-bottom: 16px;

            :deep(.arco-input) {
                height: 48px;
                font-size: 14px;
                border-radius: 8px;
                // border: 1px solid #e5e6eb;
                transition: all 0.3s ease;

                &:hover {
                    // border-color: #4080ff;
                }

                &:focus {
                    // border-color: #4080ff;
                    // box-shadow: 0 0 0 2px rgba(64, 128, 255, 0.1);
                }
            }

            :deep(.arco-input-prefix) {
                color: #86909c;
                margin-right: 8px;
            }
        }

        .login-form-password-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .forgot-password {
                color: #4080ff;
                font-size: 14px;
                text-decoration: none;

                &:hover {
                    color: #2e5bff;
                }
            }
        }

        .login-btn {
            height: 48px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            background: linear-gradient(135deg, #4080ff 0%, #2e5bff 100%);
            border: none;
            transition: all 0.3s ease;

            &:hover {
                background: linear-gradient(135deg, #2e5bff 0%, #1a4bff 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(64, 128, 255, 0.3);
            }

            &:active {
                transform: translateY(0);
            }
        }

        .login-form-register-btn {
            color: #86909c !important;
            font-size: 14px;
            height: 40px;
            
            &:hover {
                color: #4080ff !important;
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .container {
        .banner {
            width: 50%;
            min-width: 400px;
        }
    }
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;

        .banner {
            width: 100%;
            height: 300px;
            min-width: auto;

            .banner-content {
                padding: 20px;

                .logo-section {
                    margin-bottom: 30px;

                    .logo-img {
                        width: 60px;
                        height: 60px;
                    }

                    .logo-title {
                        font-size: 24px;
                    }

                    .logo-subtitle {
                        font-size: 12px;
                    }
                }

                .banner-decoration {
                    .decoration-text {
                        h2 {
                            font-size: 20px;
                        }

                        p {
                            font-size: 14px;
                        }
                    }
                }
            }
        }

        .content {
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

            .decoration-shapes {
                .shape {
                    opacity: 0.3;
                }
            }
        }
    }

    .login-form-wrapper {
        padding: 24px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(8px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);

        .login-header {
            margin-bottom: 24px;

            .login-form-title {
                font-size: 24px;
            }
        }
    }
}

@media (max-width: 480px) {
    .container {
        .banner {
            height: 250px;
        }

        .content {
            padding: 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

            .decoration-shapes {
                .shape {
                    opacity: 0.2;
                    
                    &.shape-1, &.shape-3 {
                        display: none; // 在小屏幕上隐藏大的装饰元素
                    }
                }
            }
        }
    }

    .login-form-wrapper {
        padding: 20px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(6px);

        .login-header {
            .login-form-title {
                font-size: 22px;
            }
        }

        .login-form {
            .login-input {
                height: 44px;

                :deep(.arco-input) {
                    height: 44px;
                }
            }

            .login-btn {
                height: 44px;
                font-size: 15px;
            }
        }
    }
}
</style>
