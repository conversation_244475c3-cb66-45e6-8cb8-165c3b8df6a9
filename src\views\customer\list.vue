<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form label-align="right" :model="filterForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
                        auto-label-width>
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="project" label="项目">
                                    <ProjectTreeSelect 
                                        v-model="filterForm.projectId" 
                                        :min-level="4"
                                        @change="handleProjectChange"
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="customerName" label="客户名称">
                                    <a-input v-model="filterForm.customerName" placeholder="请输入客户名称" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="customerType" label="类型">
                                <a-select v-model="filterForm.customerType" placeholder="请选择类型" allow-clear>
                                    <a-option :value="1">个人</a-option>
                                    <a-option :value="2">企业</a-option>
                                </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="maintainer" label="维护人">
                                    <a-input v-model="filterForm.ownerName" placeholder="请输入维护人" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="creator" label="创建人">
                                    <a-input v-model="filterForm.createByName" placeholder="请输入创建人" allow-clear />
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin-top: 0" />

            <!-- 操作按钮 -->
            <div class="action-bar">
                <a-space>
                    <a-button v-permission="['rent:customer:add']" type="primary" @click="handleAdd">
                        <template #icon><icon-plus /></template>
                        新增
                    </a-button>
                    <a-button v-permission="['rent:customer:export']" @click="handleDownloadTemplate">
                        <template #icon><icon-download /></template>
                        导出
                    </a-button>
                    <a-button v-permission="['rent:customer:updateOwner']" @click="handleUpdateMaintainer">
                        <template #icon><icon-upload /></template>
                        更新维护人
                    </a-button>
                </a-space>
            </div>
            <!-- 表格区域 -->
            <a-table
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :bordered="{ cell: true }"
            :scroll="{ x: 1 }"
            :stripe="true"
            :row-selection="rowSelection"
            row-key="id"
            v-model:selectedKeys="selectedKeys"
            @page-change="onPageChange"
            @page-size-change="onPageSizeChange"
            >
                <template #customerType="{ record }">
                    <a-tag :color="record.customerType === 1 ? 'blue' : 'green'">
                        {{ record.customerType === 1 ? '个人' : '企业' }}
                    </a-tag>
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <a-button v-permission="['rent:customer:detail']" type="text" size="mini" @click="handleView(record)">
                            查看
                        </a-button>
                        <a-button v-permission="['rent:customer:edit']" type="text" size="mini" @click="handleEdit(record)">
                            编辑
                        </a-button>
                    </a-space>
                </template>
            </a-table>
        </a-card>

        <!-- 新增/编辑客户抽屉 -->
        <customer-drawer 
            ref="customerDrawerRef" 
            @success="handleDrawerSuccess"
        />

        <!-- 更新维护人弹框 -->
        <update-maintainer-modal 
            ref="updateMaintainerModalRef" 
            @success="handleUpdateMaintainerSuccess"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { IconSearch, IconRefresh, IconPlus, IconDownload, IconUpload } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import CustomerDrawer from './components/CustomerDrawer.vue'
import UpdateMaintainerModal from './components/UpdateMaintainerModal.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import customerApi, { type CustomerQueryDTO, type CustomerVo, type CustomerUpdateDTO } from '@/api/customer'

// 筛选表单数据
const filterForm = reactive<CustomerQueryDTO>({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    customerName: '',
    customerType: undefined,
    ownerName: '',
    createByName: ''
})

// 表格数据
const tableData = ref<CustomerVo[]>([])

const loading = ref(false)
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
})

// 选中的行keys
const selectedKeys = ref<string[]>([])

// 表格选择配置
const rowSelection = {
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
}

// 表格列配置
const columns = [
    {
        title: '客户名称',
        dataIndex: 'customerName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 150
    },
    {
        title: '个人/企业',
        dataIndex: 'customerType',
        slotName: 'customerType',
        align: 'center',
        width: 100
    },
    {
        title: '联系电话',
        dataIndex: 'contactPhone',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 130
    },
    {
        title: '维护人',
        dataIndex: 'ownerName',
        align: 'center',
        width: 100
    },
    {
        title: '创建人',
        dataIndex: 'createByName',
        align: 'center',
        width: 100
    },
    {
        title: '更新日期',
        dataIndex: 'updateTime',
        align: 'center',
        width: 160
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 120,
        ellipsis: false,
        tooltip: false,
        fixed: 'right',
        align: 'center'
    }
]

// 抽屉相关
const customerDrawerRef = ref()

// 更新维护人弹框
const updateMaintainerModalRef = ref()

// 方法
const search = async () => {
    try {
        loading.value = true
        const params: CustomerQueryDTO = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            projectId: filterForm.projectId,
            customerName: filterForm.customerName || undefined,
            customerType: filterForm.customerType,
            ownerName: filterForm.ownerName || undefined,
            createByName: filterForm.createByName || undefined
        }
        
        const response = await customerApi.getCustomerList(params)
        if (response) {
            // 假设API返回的格式包含rows和total字段
            tableData.value = (response as any).rows || response.data || []
            pagination.total = (response as any).total || response.data?.length || 0
        }
    } catch (error) {
        console.error('获取客户列表失败:', error)
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 分页变化
const onPageChange = (current: number) => {
    pagination.current = current
    search()
}

// 每页条数变化
const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    search()
}

const reset = () => {
    Object.assign(filterForm, {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        customerName: '',
        customerType: undefined,
        ownerName: '',
        createByName: ''
    })
    pagination.current = 1
    search()
}

const handleAdd = () => {
    customerDrawerRef.value?.show('add', currentSelectedOrg.value)
}

const handleDownloadTemplate = () => {
    try {

        // 导出当前页数据，拷贝tableData全部数据
        const exportData = JSON.parse(JSON.stringify(tableData.value))
        
        if (!exportData.length) {
            Message.warning('没有找到要导出的客户数据')
            return
        }

        // 获取所有标题
        const headers = [
            "客户名称", "个人/企业", "联系电话", "维护人", "创建人", "更新日期"
        ]

        // 创建HTML表格
        let htmlContent = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">'
        htmlContent += '<head><meta charset="UTF-8"></head><body>'
        htmlContent += '<table border="1">'

        // 添加表头（带背景色）
        htmlContent += '<tr style="background-color: #4C8BF5; color: white; font-weight: bold;">'
        headers.forEach(header => {
            htmlContent += `<th>${header}</th>`
        })
        htmlContent += '</tr>'

        // 添加数据行
        exportData.forEach((row: any) => {
            // 处理客户类型
            const customerTypeText = row.customerType === 1 ? '个人' : '企业'

            // 构建数据行
            htmlContent += '<tr>'
            htmlContent += `<td>${row.customerName || ''}</td>`
            htmlContent += `<td>${customerTypeText}</td>`
            htmlContent += `<td>${row.contactPhone || ''}</td>`
            htmlContent += `<td>${row.ownerName || ''}</td>`
            htmlContent += `<td>${row.createByName || ''}</td>`
            htmlContent += `<td>${row.updateTime || ''}</td>`
            htmlContent += '</tr>'
        })

        htmlContent += '</table></body></html>'

        // 创建Blob对象
        const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)

        // 创建下载链接
        const link = document.createElement('a')
        link.href = url
        link.download = `客户列表_${new Date().getTime()}.xls`
        document.body.appendChild(link)
        link.click()

        // 清理
        window.URL.revokeObjectURL(url)
        document.body.removeChild(link)
        
        Message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
    }
}

const handleUpdateMaintainer = () => {
    if (!selectedKeys.value.length) {
        Message.warning('请选择要更新维护人的客户')
        return
    }
    updateMaintainerModalRef.value?.show(selectedKeys.value)
}

const handleUpdateMaintainerSuccess = async (data: {
    customerIds: string[]
    ownerId: string
    ownerName: string
}) => {
    try {
        const updateParams: CustomerUpdateDTO = {
            customerIds: data.customerIds,
            ownerId: data.ownerId,
            ownerName: data.ownerName
        }
        
        await customerApi.updateCustomerOwner(updateParams)
        Message.success('更新维护人成功')
        
        // 重新加载数据
        search()
        selectedKeys.value = []
    } catch (error) {
        console.error('更新维护人失败:', error)
    }
}

const handleView = (record: CustomerVo) => {
    customerDrawerRef.value?.show('view', record)
}

const handleEdit = (record: CustomerVo) => {
    customerDrawerRef.value?.show('edit', record)
}

const handleDrawerSuccess = () => {
    search()
}

const currentSelectedOrg = ref<any>(null)
const isInit = ref(false)
const handleProjectChange = (value: string | number, selectedOrg: any) => {
    console.log('项目变化时只更新表单数据，不自动触发搜索', value, selectedOrg)
    // 项目变化时只更新表单数据，不自动触发搜索
    // 用户需要点击查询按钮才执行查询
    currentSelectedOrg.value = selectedOrg
    // 项目选择后自动触发搜索
    if (!isInit.value) {
        isInit.value = true
        pagination.current = 1
        search()
    }
}


</script>

<style scoped lang="less">
:deep(.section-title) {
    margin: 16px 0;
}

.container {
    padding: 0 16px 16px 16px;
}

.general-card {
    border-radius: 4px;

    :deep(.arco-card-body) {
        padding: 16px;
    }
}

.action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
}
</style>