---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/房间水电用量

<a id="opIdgetRoomList"></a>

## POST 房源信息列表

POST /water/electricity/room/list

根据条件查询房源信息列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "searchParam": "string",
  "roomName": "string",
  "bindFlag": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[WaterElectricityQueryDTO](#schemawaterelectricityquerydto)| 否 |none|

> 返回示例

> 200 Response

```
[{"roomId":"string","roomName":"string","fullName":"string","projectId":"string","projectName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","opsSysRoomId":"string","opsSysRoomName":"string"}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomSimpleForWaterVo](#schemaroomsimpleforwatervo)]|false|none||none|
|» roomId|string|false|none|房间id|房间id|
|» roomName|string|false|none|房间名称|房间名称|
|» fullName|string|false|none|房间名称|房间名称|
|» projectId|string|false|none|项目id|项目id|
|» projectName|string|false|none|项目名称|项目名称|
|» parcelId|string|false|none|地块id|地块id|
|» parcelName|string|false|none|地块名称|地块名称|
|» buildingId|string|false|none|楼栋id|楼栋id|
|» buildingName|string|false|none|楼栋名称|楼栋名称|
|» floorId|string|false|none|楼层id|楼层id|
|» floorName|string|false|none|楼层名称|楼层名称|
|» opsSysRoomId|string|false|none|运营系统房间id|运营系统房间id|
|» opsSysRoomName|string|false|none|运营系统房间名称|运营系统房间名称|

<a id="opIdimportRoomList"></a>

## POST 房源信息列表导入

POST /water/electricity/room/import

通过Excel文件导入房源信息列表

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|string(binary)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport"></a>

## POST 导出询房间水电用量列表

POST /water/electricity/room/export

导出询房间水电用量列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|projectId|query|string| 否 | 项目id|项目id|
|parcelId|query|string| 否 | 地块id|地块id|
|buildingId|query|string| 否 | 楼栋id|楼栋id|
|searchParam|query|string| 否 | 搜索参数|楼栋/房间名称|
|roomName|query|string| 否 | 房间名称|房间名称（模糊搜索）|
|bindFlag|query|integer(int32)| 否 | 绑定flag|0：未绑定，1：已绑定|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdsaveBindRelation"></a>

## POST 保存关联关系接口

POST /water/electricity/room/bind

保存房间与运营系统的关联关系

> Body 请求参数

```json
[
  {
    "roomId": "string",
    "roomName": "string",
    "fullName": "string",
    "projectId": "string",
    "projectName": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "opsSysRoomId": "string",
    "opsSysRoomName": "string"
  }
]
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomSimpleForWaterVo](#schemaroomsimpleforwatervo)| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIddeleteBindRelation"></a>

## POST 解绑

POST /water/electricity/room/bind/delete

解绑房间与运营系统的关联关系

> Body 请求参数

```json
[
  "string"
]
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|array[string]| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdgetWaterElectricityLogList"></a>

## POST 智能水电记录

POST /water/electricity/log/detail

根据条件查询智能水电记录列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "roomId": "string",
  "coolUseNum": 0,
  "coolTotal": 0,
  "hotUseNum": 0,
  "hotTotal": 0,
  "eleUseNum": 0,
  "eleTotal": 0,
  "totalReserve": 0,
  "total": 0,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "dateType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[RoomWaterElectricityLogQueryDTO](#schemaroomwaterelectricitylogquerydto)| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","roomId":"string","coolUseNum":0,"coolTotal":0,"hotUseNum":0,"hotTotal":0,"eleUseNum":0,"eleTotal":0,"totalReserve":0,"total":0,"createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomWaterElectricityLogVo](#schemaroomwaterelectricitylogvo)]|false|none||[房间的水电用量历史记录]|
|» 水电用量日志列表|[RoomWaterElectricityLogVo](#schemaroomwaterelectricitylogvo)|false|none|水电用量日志列表|房间的水电用量历史记录|
|»» id|string|false|none|主键ID|none|
|»» roomId|string|false|none|房间id|房间id|
|»» coolUseNum|number|false|none|当日冷水用量|当日冷水用量|
|»» coolTotal|number|false|none|总冷水用量|总冷水用量|
|»» hotUseNum|number|false|none|当日热水用量|当日热水用量|
|»» hotTotal|number|false|none|总热水用量|总热水用量|
|»» eleUseNum|number|false|none|当日用电量|当日用电量|
|»» eleTotal|number|false|none|总用电量|总用电量|
|»» totalReserve|number|false|none|总储备|总储备|
|»» total|number|false|none|总量|总量|
|»» createByName|string|false|none|创建人姓名|创建人姓名|
|»» createTime|string(date-time)|false|none|创建时间|创建时间|
|»» updateByName|string|false|none|更新人姓名|更新人姓名|
|»» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIdlist_15"></a>

## GET 智能水电列表接口

GET /water/electricity/list

根据条件查询智能水电列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 否 | 项目id|项目id|
|parcelId|query|string| 否 | 地块id|地块id|
|buildingId|query|string| 否 | 楼栋id|楼栋id|
|searchParam|query|string| 否 | 搜索参数|楼栋/房间名称|
|roomName|query|string| 否 | 房间名称|房间名称（模糊搜索）|
|bindFlag|query|integer(int32)| 否 | 绑定flag|0：未绑定，1：已绑定|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","roomName":"string","type":0,"propertyType":"string","propertyTypeName":"string","projectId":"string","projectName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomCode":"string","roomId":"string","status":0,"isLock":true,"isDirty":true,"isMaintain":true,"remark":"string","mergeSplitId":"string","planEffectDate":"2019-08-24T14:15:22Z","actualEffectDate":"2019-08-24T14:15:22Z","projectPriceId":"string","tablePrice":0,"bottomPrice":0,"areaType":0,"buildArea":0,"innerArea":0,"rentAreaType":0,"rentArea":0,"storey":0,"value":0,"orientation":"string","houseTypeId":"string","houseTypeName":"string","smartWaterMeter":"string","smartLock":"string","assetOperationMode":"string","assetOperationType":"string","propertyStatus":"string","paymentStatus":0,"specialTag":"string","latestRentPrice":0,"latestRentStartDate":"2019-08-24T14:15:22Z","latestRentEndDate":"2019-08-24T14:15:22Z","firstRentPrice":0,"firstRentStartDate":"2019-08-24T14:15:22Z","firstRentEndDate":"2019-08-24T14:15:22Z","operationSubject":0,"rentalStartDate":"2019-08-24T14:15:22Z","externalRentStartDate":"2019-08-24T14:15:22Z","isSelfUse":true,"selfUseSubject":0,"selfUsePurpose":"string","djBindRoomId":"string","djBindName":"string","createByName":"string","updateByName":"string","isDel":true,"orientationName":"string","priceUnit":0,"baseRent":0,"additionalFee":0}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomVo](#schemaroomvo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» roomName|string|false|none|房源名称|房源名称|
|» type|integer(int32)|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|» propertyType|string|false|none|物业类型，二级字典|物业类型，二级字典|
|» propertyTypeName|string|false|none|物业类型名称|物业类型名称|
|» projectId|string|false|none|项目id|项目id|
|» projectName|string|false|none||none|
|» parcelId|string|false|none|地块id|地块id|
|» parcelName|string|false|none|地块名称|地块名称|
|» buildingId|string|false|none|楼栋id|楼栋id|
|» buildingName|string|false|none|楼栋名称|楼栋名称|
|» floorId|string|false|none|楼层id|楼层id|
|» floorName|string|false|none|楼层名称|楼层名称|
|» roomCode|string|false|none|合成编码|合成编码|
|» roomId|string|false|none|房间id， sys_room表的id|房间id， sys_room表的id|
|» status|integer(int32)|false|none|状态（0草稿 10待生效 20生效中 30已失效 ）|状态（0草稿 10待生效 20生效中 30已失效 ）|
|» isLock|boolean|false|none|是否锁房（0否 1是）|是否锁房（0否 1是）|
|» isDirty|boolean|false|none|是否脏房（0否 1是）|是否脏房（0否 1是）|
|» isMaintain|boolean|false|none|是否维修（0否 1是）|是否维修（0否 1是）|
|» remark|string|false|none|描述|描述|
|» mergeSplitId|string|false|none|拆分合并id|拆分合并id|
|» planEffectDate|string(date-time)|false|none|预计生效日期|预计生效日期|
|» actualEffectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|» projectPriceId|string|false|none|最新的立项定价id|最新的立项定价id|
|» tablePrice|number|false|none|表价|表价|
|» bottomPrice|number|false|none|底价|底价|
|» areaType|integer(int32)|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|» buildArea|number|false|none|建筑面积|建筑面积|
|» innerArea|number|false|none|套内面积|套内面积|
|» rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|» rentArea|number|false|none|计租面积|计租面积|
|» storey|number|false|none|层高|层高|
|» value|number|false|none|荷载值|荷载值|
|» orientation|string|false|none|朝向|朝向|
|» houseTypeId|string|false|none|户型id|户型id|
|» houseTypeName|string|false|none|户型名称|户型名称|
|» smartWaterMeter|string|false|none|智能水电表|智能水电表|
|» smartLock|string|false|none|智能锁|智能锁|
|» assetOperationMode|string|false|none|资产运营模式（自持、可售、代招商）|资产运营模式（自持、可售、代招商）|
|» assetOperationType|string|false|none|资产运营分类|资产运营分类|
|» propertyStatus|string|false|none|产权情况|产权情况|
|» paymentStatus|integer(int32)|false|none|交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|» specialTag|string|false|none|特殊标签（1保障房）|特殊标签（1保障房）|
|» latestRentPrice|number|false|none|商管承租成本单价（最新）|商管承租成本单价（最新）|
|» latestRentStartDate|string(date-time)|false|none|商管承租起计日期（最新）|商管承租起计日期（最新）|
|» latestRentEndDate|string(date-time)|false|none|商管承租到期日期（最新）|商管承租到期日期（最新）|
|» firstRentPrice|number|false|none|商管承租成本单价第一次|商管承租成本单价第一次|
|» firstRentStartDate|string(date-time)|false|none|商管承租起计日期第一次|商管承租起计日期第一次|
|» firstRentEndDate|string(date-time)|false|none|商管承租到期日期第一次|商管承租到期日期第一次|
|» operationSubject|integer(int32)|false|none|运营主体（1商服 2众创城）,字典|运营主体（1商服 2众创城）,字典|
|» rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|» externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|» isSelfUse|boolean|false|none|是否自用（0否 1是）|是否自用（0否 1是）|
|» selfUseSubject|integer(int32)|false|none|自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|» selfUsePurpose|string|false|none|自用用途|自用用途|
|» djBindRoomId|string|false|none|多经绑定房源id|多经绑定房源id|
|» djBindName|string|false|none|多经绑定房源名称|多经绑定房源名称|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|» orientationName|string|false|none|朝向名称|朝向名称|
|» priceUnit|integer(int32)|false|none|计租单位(1元/平方米/月 2元/月 3元/日)|计租单位(1元/平方米/月 2元/月 3元/日)|
|» baseRent|number|false|none|基础租金|基础租金|
|» additionalFee|number|false|none|附加费用|附加费用|

<a id="opIdgetWaterElectricityDetail"></a>

## GET 智能水电详情

GET /water/electricity/detail

根据房间ID获取智能水电详情信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|roomId|query|string| 是 ||房间ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"roomInfo":{"id":"string","roomName":"string","type":0,"propertyType":"string","propertyTypeName":"string","projectId":"string","projectName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","roomCode":"string","roomId":"string","status":0,"isLock":true,"isDirty":true,"isMaintain":true,"remark":"string","mergeSplitId":"string","planEffectDate":"2019-08-24T14:15:22Z","actualEffectDate":"2019-08-24T14:15:22Z","projectPriceId":"string","tablePrice":0,"bottomPrice":0,"areaType":0,"buildArea":0,"innerArea":0,"rentAreaType":0,"rentArea":0,"storey":0,"value":0,"orientation":"string","houseTypeId":"string","houseTypeName":"string","smartWaterMeter":"string","smartLock":"string","assetOperationMode":"string","assetOperationType":"string","propertyStatus":"string","paymentStatus":0,"specialTag":"string","latestRentPrice":0,"latestRentStartDate":"2019-08-24T14:15:22Z","latestRentEndDate":"2019-08-24T14:15:22Z","firstRentPrice":0,"firstRentStartDate":"2019-08-24T14:15:22Z","firstRentEndDate":"2019-08-24T14:15:22Z","operationSubject":0,"rentalStartDate":"2019-08-24T14:15:22Z","externalRentStartDate":"2019-08-24T14:15:22Z","isSelfUse":true,"selfUseSubject":0,"selfUsePurpose":"string","djBindRoomId":"string","djBindName":"string","createByName":"string","updateByName":"string","isDel":true,"orientationName":"string","priceUnit":0,"baseRent":0,"additionalFee":0},"waterElectricityLogList":[{"id":"string","roomId":"string","coolUseNum":0,"coolTotal":0,"hotUseNum":0,"hotTotal":0,"eleUseNum":0,"eleTotal":0,"totalReserve":0,"total":0,"createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[RoomWaterElectricityDetailVo](#schemaroomwaterelectricitydetailvo)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_WaterElectricityQueryDTO">WaterElectricityQueryDTO</h2>

<a id="schemawaterelectricityquerydto"></a>
<a id="schema_WaterElectricityQueryDTO"></a>
<a id="tocSwaterelectricityquerydto"></a>
<a id="tocswaterelectricityquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "searchParam": "string",
  "roomName": "string",
  "bindFlag": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|地块id|地块id|
|buildingId|string|false|none|楼栋id|楼栋id|
|searchParam|string|false|none|搜索参数|楼栋/房间名称|
|roomName|string|false|none|房间名称|房间名称（模糊搜索）|
|bindFlag|integer(int32)|false|none|绑定flag|0：未绑定，1：已绑定|

<h2 id="tocS_RoomSimpleForWaterVo">RoomSimpleForWaterVo</h2>

<a id="schemaroomsimpleforwatervo"></a>
<a id="schema_RoomSimpleForWaterVo"></a>
<a id="tocSroomsimpleforwatervo"></a>
<a id="tocsroomsimpleforwatervo"></a>

```json
{
  "roomId": "string",
  "roomName": "string",
  "fullName": "string",
  "projectId": "string",
  "projectName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "opsSysRoomId": "string",
  "opsSysRoomName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roomId|string|false|none|房间id|房间id|
|roomName|string|false|none|房间名称|房间名称|
|fullName|string|false|none|房间名称|房间名称|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|parcelId|string|false|none|地块id|地块id|
|parcelName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋id|楼栋id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层id|楼层id|
|floorName|string|false|none|楼层名称|楼层名称|
|opsSysRoomId|string|false|none|运营系统房间id|运营系统房间id|
|opsSysRoomName|string|false|none|运营系统房间名称|运营系统房间名称|

<h2 id="tocS_RoomWaterElectricityLogQueryDTO">RoomWaterElectricityLogQueryDTO</h2>

<a id="schemaroomwaterelectricitylogquerydto"></a>
<a id="schema_RoomWaterElectricityLogQueryDTO"></a>
<a id="tocSroomwaterelectricitylogquerydto"></a>
<a id="tocsroomwaterelectricitylogquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "roomId": "string",
  "coolUseNum": 0,
  "coolTotal": 0,
  "hotUseNum": 0,
  "hotTotal": 0,
  "eleUseNum": 0,
  "eleTotal": 0,
  "totalReserve": 0,
  "total": 0,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "dateType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|roomId|string|false|none|房间id|房间id|
|coolUseNum|number|false|none|当日冷水用量|当日冷水用量|
|coolTotal|number|false|none|总冷水用量|总冷水用量|
|hotUseNum|number|false|none|当日热水用量|当日热水用量|
|hotTotal|number|false|none|总热水用量|总热水用量|
|eleUseNum|number|false|none|当日用电量|当日用电量|
|eleTotal|number|false|none|总用电量|总用电量|
|totalReserve|number|false|none|总储备|总储备|
|total|number|false|none|总量|总量|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|dateType|integer(int32)|false|none|日期类型：1-近7天，2-近半月，3-近一月，4-近三月，5-近半年，6-近一年|日期类型：1-近7天，2-近半月，3-近一月，4-近三月，5-近半年，6-近一年|
|startDate|string(date-time)|false|none|有效期开始时间|有效期开始时间|
|endDate|string(date-time)|false|none|有效期结束时间|有效期结束时间|

<h2 id="tocS_RoomWaterElectricityLogVo">RoomWaterElectricityLogVo</h2>

<a id="schemaroomwaterelectricitylogvo"></a>
<a id="schema_RoomWaterElectricityLogVo"></a>
<a id="tocSroomwaterelectricitylogvo"></a>
<a id="tocsroomwaterelectricitylogvo"></a>

```json
{
  "id": "string",
  "roomId": "string",
  "coolUseNum": 0,
  "coolTotal": 0,
  "hotUseNum": 0,
  "hotTotal": 0,
  "eleUseNum": 0,
  "eleTotal": 0,
  "totalReserve": 0,
  "total": 0,
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "isDel": true
}

```

水电用量日志列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|roomId|string|false|none|房间id|房间id|
|coolUseNum|number|false|none|当日冷水用量|当日冷水用量|
|coolTotal|number|false|none|总冷水用量|总冷水用量|
|hotUseNum|number|false|none|当日热水用量|当日热水用量|
|hotTotal|number|false|none|总热水用量|总热水用量|
|eleUseNum|number|false|none|当日用电量|当日用电量|
|eleTotal|number|false|none|总用电量|总用电量|
|totalReserve|number|false|none|总储备|总储备|
|total|number|false|none|总量|总量|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_RoomVo">RoomVo</h2>

<a id="schemaroomvo"></a>
<a id="schema_RoomVo"></a>
<a id="tocSroomvo"></a>
<a id="tocsroomvo"></a>

```json
{
  "id": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "propertyTypeName": "string",
  "projectId": "string",
  "projectName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "roomCode": "string",
  "roomId": "string",
  "status": 0,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true,
  "remark": "string",
  "mergeSplitId": "string",
  "planEffectDate": "2019-08-24T14:15:22Z",
  "actualEffectDate": "2019-08-24T14:15:22Z",
  "projectPriceId": "string",
  "tablePrice": 0,
  "bottomPrice": 0,
  "areaType": 0,
  "buildArea": 0,
  "innerArea": 0,
  "rentAreaType": 0,
  "rentArea": 0,
  "storey": 0,
  "value": 0,
  "orientation": "string",
  "houseTypeId": "string",
  "houseTypeName": "string",
  "smartWaterMeter": "string",
  "smartLock": "string",
  "assetOperationMode": "string",
  "assetOperationType": "string",
  "propertyStatus": "string",
  "paymentStatus": 0,
  "specialTag": "string",
  "latestRentPrice": 0,
  "latestRentStartDate": "2019-08-24T14:15:22Z",
  "latestRentEndDate": "2019-08-24T14:15:22Z",
  "firstRentPrice": 0,
  "firstRentStartDate": "2019-08-24T14:15:22Z",
  "firstRentEndDate": "2019-08-24T14:15:22Z",
  "operationSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isSelfUse": true,
  "selfUseSubject": 0,
  "selfUsePurpose": "string",
  "djBindRoomId": "string",
  "djBindName": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "orientationName": "string",
  "priceUnit": 0,
  "baseRent": 0,
  "additionalFee": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|roomName|string|false|none|房源名称|房源名称|
|type|integer(int32)|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|string|false|none|物业类型，二级字典|物业类型，二级字典|
|propertyTypeName|string|false|none|物业类型名称|物业类型名称|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none||none|
|parcelId|string|false|none|地块id|地块id|
|parcelName|string|false|none|地块名称|地块名称|
|buildingId|string|false|none|楼栋id|楼栋id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorId|string|false|none|楼层id|楼层id|
|floorName|string|false|none|楼层名称|楼层名称|
|roomCode|string|false|none|合成编码|合成编码|
|roomId|string|false|none|房间id， sys_room表的id|房间id， sys_room表的id|
|status|integer(int32)|false|none|状态（0草稿 10待生效 20生效中 30已失效 ）|状态（0草稿 10待生效 20生效中 30已失效 ）|
|isLock|boolean|false|none|是否锁房（0否 1是）|是否锁房（0否 1是）|
|isDirty|boolean|false|none|是否脏房（0否 1是）|是否脏房（0否 1是）|
|isMaintain|boolean|false|none|是否维修（0否 1是）|是否维修（0否 1是）|
|remark|string|false|none|描述|描述|
|mergeSplitId|string|false|none|拆分合并id|拆分合并id|
|planEffectDate|string(date-time)|false|none|预计生效日期|预计生效日期|
|actualEffectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|projectPriceId|string|false|none|最新的立项定价id|最新的立项定价id|
|tablePrice|number|false|none|表价|表价|
|bottomPrice|number|false|none|底价|底价|
|areaType|integer(int32)|false|none|面积类型（1实测 2预测）|面积类型（1实测 2预测）|
|buildArea|number|false|none|建筑面积|建筑面积|
|innerArea|number|false|none|套内面积|套内面积|
|rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|计租面积类型（1建筑面积 2套内面积）|
|rentArea|number|false|none|计租面积|计租面积|
|storey|number|false|none|层高|层高|
|value|number|false|none|荷载值|荷载值|
|orientation|string|false|none|朝向|朝向|
|houseTypeId|string|false|none|户型id|户型id|
|houseTypeName|string|false|none|户型名称|户型名称|
|smartWaterMeter|string|false|none|智能水电表|智能水电表|
|smartLock|string|false|none|智能锁|智能锁|
|assetOperationMode|string|false|none|资产运营模式（自持、可售、代招商）|资产运营模式（自持、可售、代招商）|
|assetOperationType|string|false|none|资产运营分类|资产运营分类|
|propertyStatus|string|false|none|产权情况|产权情况|
|paymentStatus|integer(int32)|false|none|交付状态（1未交付 2已交付）|交付状态（1未交付 2已交付）|
|specialTag|string|false|none|特殊标签（1保障房）|特殊标签（1保障房）|
|latestRentPrice|number|false|none|商管承租成本单价（最新）|商管承租成本单价（最新）|
|latestRentStartDate|string(date-time)|false|none|商管承租起计日期（最新）|商管承租起计日期（最新）|
|latestRentEndDate|string(date-time)|false|none|商管承租到期日期（最新）|商管承租到期日期（最新）|
|firstRentPrice|number|false|none|商管承租成本单价第一次|商管承租成本单价第一次|
|firstRentStartDate|string(date-time)|false|none|商管承租起计日期第一次|商管承租起计日期第一次|
|firstRentEndDate|string(date-time)|false|none|商管承租到期日期第一次|商管承租到期日期第一次|
|operationSubject|integer(int32)|false|none|运营主体（1商服 2众创城）,字典|运营主体（1商服 2众创城）,字典|
|rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|isSelfUse|boolean|false|none|是否自用（0否 1是）|是否自用（0否 1是）|
|selfUseSubject|integer(int32)|false|none|自用主体（1运营 2商服 3众创 4其他）|自用主体（1运营 2商服 3众创 4其他）|
|selfUsePurpose|string|false|none|自用用途|自用用途|
|djBindRoomId|string|false|none|多经绑定房源id|多经绑定房源id|
|djBindName|string|false|none|多经绑定房源名称|多经绑定房源名称|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|orientationName|string|false|none|朝向名称|朝向名称|
|priceUnit|integer(int32)|false|none|计租单位(1元/平方米/月 2元/月 3元/日)|计租单位(1元/平方米/月 2元/月 3元/日)|
|baseRent|number|false|none|基础租金|基础租金|
|additionalFee|number|false|none|附加费用|附加费用|

<h2 id="tocS_RoomWaterElectricityDetailVo">RoomWaterElectricityDetailVo</h2>

<a id="schemaroomwaterelectricitydetailvo"></a>
<a id="schema_RoomWaterElectricityDetailVo"></a>
<a id="tocSroomwaterelectricitydetailvo"></a>
<a id="tocsroomwaterelectricitydetailvo"></a>

```json
{
  "roomInfo": {
    "id": "string",
    "roomName": "string",
    "type": 0,
    "propertyType": "string",
    "propertyTypeName": "string",
    "projectId": "string",
    "projectName": "string",
    "parcelId": "string",
    "parcelName": "string",
    "buildingId": "string",
    "buildingName": "string",
    "floorId": "string",
    "floorName": "string",
    "roomCode": "string",
    "roomId": "string",
    "status": 0,
    "isLock": true,
    "isDirty": true,
    "isMaintain": true,
    "remark": "string",
    "mergeSplitId": "string",
    "planEffectDate": "2019-08-24T14:15:22Z",
    "actualEffectDate": "2019-08-24T14:15:22Z",
    "projectPriceId": "string",
    "tablePrice": 0,
    "bottomPrice": 0,
    "areaType": 0,
    "buildArea": 0,
    "innerArea": 0,
    "rentAreaType": 0,
    "rentArea": 0,
    "storey": 0,
    "value": 0,
    "orientation": "string",
    "houseTypeId": "string",
    "houseTypeName": "string",
    "smartWaterMeter": "string",
    "smartLock": "string",
    "assetOperationMode": "string",
    "assetOperationType": "string",
    "propertyStatus": "string",
    "paymentStatus": 0,
    "specialTag": "string",
    "latestRentPrice": 0,
    "latestRentStartDate": "2019-08-24T14:15:22Z",
    "latestRentEndDate": "2019-08-24T14:15:22Z",
    "firstRentPrice": 0,
    "firstRentStartDate": "2019-08-24T14:15:22Z",
    "firstRentEndDate": "2019-08-24T14:15:22Z",
    "operationSubject": 0,
    "rentalStartDate": "2019-08-24T14:15:22Z",
    "externalRentStartDate": "2019-08-24T14:15:22Z",
    "isSelfUse": true,
    "selfUseSubject": 0,
    "selfUsePurpose": "string",
    "djBindRoomId": "string",
    "djBindName": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true,
    "orientationName": "string",
    "priceUnit": 0,
    "baseRent": 0,
    "additionalFee": 0
  },
  "waterElectricityLogList": [
    {
      "id": "string",
      "roomId": "string",
      "coolUseNum": 0,
      "coolTotal": 0,
      "hotUseNum": 0,
      "hotTotal": 0,
      "eleUseNum": 0,
      "eleTotal": 0,
      "totalReserve": 0,
      "total": 0,
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roomInfo|[RoomVo](#schemaroomvo)|false|none||none|
|waterElectricityLogList|[[RoomWaterElectricityLogVo](#schemaroomwaterelectricitylogvo)]|false|none|水电用量日志列表|房间的水电用量历史记录|

