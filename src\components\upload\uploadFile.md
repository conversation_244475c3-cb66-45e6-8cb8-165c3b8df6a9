# UploadFile 文件上传组件

基于 Arco Design Pro 的文件上传组件，支持文件上传、预览、删除等操作。
组件使用 JSON 字符串格式进行双向绑定，自动处理数据序列化。

## 功能特性

- 支持文件上传、预览和删除
- 支持双向绑定，使用 v-model 管理文件列表（JSON 字符串格式）
- 支持自定义上传接口
- 支持限制文件类型、大小和数量
- 支持只读模式和禁用状态
- 支持自定义文件对象属性名映射
- 美观的文件列表展示
- 自动处理文件数据的序列化和反序列化

## 基本用法

```vue
<template>
  <upload-file v-model="fileListStr" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UploadFile from '@/components/upload/uploadFile.vue';

// 注意：现在使用字符串格式，组件内部会自动处理 JSON 序列化
const fileListStr = ref('');
</script>
```

## 配置自定义参数

```vue
<template>
  <upload-file
    v-model="fileListStr"
    accept=".pdf,.doc,.docx"
    :maxSize="10"
    :limit="5"
    :readonly="isReadOnly"
    :disabled="isDisabled"
    @success="handleSuccess"
    @error="handleError"
  />
</template>

<script setup>
import { ref } from 'vue';

const fileListStr = ref('');
</script>
```

## 只读模式

```vue
<template>
  <upload-file
    v-model="fileListStr"
    :readonly="true"
  />
</template>
```

## 属性说明

| 属性名      | 类型                    | 默认值                              | 说明                            |
| ----------- | ----------------------- | ----------------------------------- | ------------------------------- |
| modelValue  | String                  | ''                                  | 绑定值，文件列表的 JSON 字符串   |
| uploadApi   | Function                | -                                   | 上传接口函数，默认使用公共上传接口  |
| accept      | String                  | ''                                  | 允许上传的文件类型，如 '.pdf,.doc' |
| maxSize     | Number                  | 5                                   | 文件大小限制，单位 MB           |
| limit       | Number                  | 0                                   | 文件数量限制，0 表示不限制      |
| readonly    | Boolean                 | false                               | 只读模式，不显示上传和删除按钮  |
| disabled    | Boolean                 | false                               | 是否禁用                        |

## 事件说明

| 事件名        | 参数                              | 说明                     |
| ------------- | --------------------------------- | ------------------------ |
| update:modelValue | (fileListStr: String)            | 文件列表更新时触发，返回 JSON 字符串 |
| success       | (response: any, fileItem: Object)   | 文件上传成功时触发       |
| error         | (error: any, fileItem: Object)   | 文件上传失败时触发       |
| exceed-limit  | (files: Array)                   | 超出文件数量限制时触发   |
| exceed-size   | (file: Object)                   | 超出文件大小限制时触发   |

## 数据格式说明

组件使用 JSON 字符串格式进行数据绑定，内部会自动处理序列化和反序列化。

### 输入格式（v-model 绑定的值）

```js
// 空字符串表示无文件
''

// 或者 JSON 字符串格式
'[{"fileName": "文档.pdf", "fileUrl": "https://example.com/doc.pdf"}]'
```

### 输出格式（组件内部的文件对象结构）

```js
[
  {
    fileName: "文档.pdf",
    fileUrl: "https://example.com/doc.pdf"
  },
  {
    fileName: "报告.docx", 
    fileUrl: "https://example.com/report.docx"
  }
]
```

## 进阶用法

### 自定义文件属性名

组件默认使用 `fileId`、`name`、`url` 作为文件对象的属性名，你可以通过 `fileItemProps` 属性自定义：

```vue
<template>
  <upload-file
    v-model="fileListStr"
    :fileItemProps="{
      fileId: 'id',
      name: 'fileName',
      url: 'fileUrl'
    }"
  />
</template>
```

这样，组件输出的文件对象结构将变为：

```js
{
  id: '123',       // 而不是 fileId: '123'
  fileName: '文档.pdf', // 而不是 name: '文档.pdf'
  fileUrl: 'https://example.com/doc.pdf' // 而不是 url: 'https://example.com/doc.pdf'
}
```

### 自定义上传接口

```vue
<template>
  <upload-file
    v-model="fileListStr"
    :uploadApi="uploadFile"
  />
</template>

<script setup>
import { ref } from 'vue';
import { uploadFile } from '@/api/common';

const fileListStr = ref('');
</script>
```

### 完整示例

```vue
<template>
  <div>
    <h3>文件上传</h3>
    <upload-file
      v-model="fileListStr"
      :uploadApi="uploadFile"
      accept=".pdf,.doc,.docx,.xls,.xlsx"
      :maxSize="10"
      :limit="5"
      :readonly="readonly"
      :fileItemProps="{
        fileId: 'id',
        name: 'fileName',
        url: 'fileUrl'
      }"
      @success="handleSuccess"
      @error="handleError"
      @exceed-limit="handleExceedLimit"
      @exceed-size="handleExceedSize"
    />
    
    <div class="actions">
      <a-switch v-model="readonly" />
      <span>只读模式</span>
    </div>
    
    <div class="file-preview" v-if="fileListStr">
      <h4>文件列表（JSON 字符串）</h4>
      <pre>{{ fileListStr }}</pre>
      
      <h4>解析后的文件列表</h4>
      <pre>{{ JSON.stringify(parsedFileList, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import UploadFile from '@/components/upload/uploadFile.vue';
import { uploadFile } from '@/api/common';

const fileListStr = ref('');
const readonly = ref(false);

// 解析文件列表用于显示
const parsedFileList = computed(() => {
  if (!fileListStr.value) return [];
  try {
    return JSON.parse(fileListStr.value);
  } catch {
    return [];
  }
});

const handleSuccess = (response, file) => {
  Message.success(`文件 ${file.name} 上传成功`);
};

const handleError = (error, file) => {
  Message.error(`文件 ${file.name} 上传失败`);
};

const handleExceedLimit = (files) => {
  Message.warning('超出文件数量限制');
};

const handleExceedSize = (file) => {
  Message.warning(`文件 ${file.name} 超出大小限制`);
};
</script>
```

## 注意事项

1. **数据格式**：组件现在使用 JSON 字符串格式进行双向绑定，而不是数组
2. **空值处理**：空字符串 `''` 表示没有文件
3. **数据解析**：如果需要在父组件中操作文件列表，使用 `JSON.parse()` 解析字符串
4. **表单集成**：可以直接绑定到表单字段，方便与后端 API 交互
5. **向后兼容**：如果您的项目之前使用数组格式，需要相应调整绑定的数据类型

## 与表单集成

```vue
<template>
  <a-form :model="formData" @submit="handleSubmit">
    <a-form-item label="上传附件" field="attachments">
      <upload-file v-model="formData.attachments" />
    </a-form-item>
    <a-form-item>
      <a-button html-type="submit">提交</a-button>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from 'vue';

const formData = ref({
  attachments: '' // 字符串格式
});

const handleSubmit = () => {
  // formData.attachments 是 JSON 字符串
  console.log(formData.value.attachments);
  
  // 如果需要解析为数组，可以使用 JSON.parse
  if (formData.value.attachments) {
    const fileList = JSON.parse(formData.value.attachments);
    console.log(fileList);
  }
};
</script>
``` 