<template>
    <a-drawer
        v-model:visible="visible"
        title="智能水电表详情"
        class="common-drawer"
        :footer="false"
        @cancel="handleCancel"
    >
        <a-spin :loading="loading" style="width: 100%;">
            <div v-if="roomDetail" class="device-detail-container">
                <!-- 房间信息 -->
                <section-title title="房间信息" style="margin-bottom: 16px;" />
                <a-form
                    :model="roomDetail.roomInfo"
                    label-align="right"
                    :label-col-props="{ span: 6 }"
                    :wrapper-col-props="{ span: 18 }"
                    auto-label-width
                >
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="所属房源">
                                <a-input v-model="roomDetail.roomInfo.roomName" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="对应运营房间">
                                <a-input v-model="roomDetail.roomInfo.opsSysRoomName" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-button type="primary" @click="handleUnbindRoom">解绑</a-button>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="智能电表">
                                暂无字段
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="智能冷水表">
                                暂无字段
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="智能热水表">
                                暂无字段
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
                <!-- 用量记录 -->
                <section-title title="用量记录" style="margin-bottom: 16px;"/>
                <!-- 时间筛选 -->
                <div class="filter-section" style="margin-bottom: 16px;">
                    <a-row :gutter="16">
                        <a-col :span="12">
                            <a-select
                                v-model="dateType"
                                @change="handleDateTypeChange"
                                style="width: 120px;"
                            >
                                <a-option value="">全部</a-option>
                                <a-option :value="1">近7天</a-option>
                                <a-option :value="2">近半月</a-option>
                                <a-option :value="3">近一月</a-option>
                                <a-option :value="4">近三月</a-option>
                                <a-option :value="5">近半年</a-option>
                                <a-option :value="6">近一年</a-option>
                            </a-select>
                            <a-range-picker
                                v-model="dateRange"
                                style="margin-left: 16px;"
                                @change="handleDateChange"
                            />
                            <a-button style="margin-left: 16px;" type="primary" @click="handleSearchLog">查询</a-button>
                        </a-col>
                    </a-row>
                </div>

                <!-- 用量记录表格 -->
                <a-table
                    :data="waterElectricityLogList"
                    :columns="logColumns"
                    :pagination="logPagination"
                    :loading="logLoading"
                    row-key="id"
                    size="small"
                    :bordered="{ cell: true }"
                    @page-change="handleLogPageChange"
                    @page-size-change="handleLogPageSizeChange"
                />
            </div>
        </a-spin>
    </a-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import SectionTitle from '@/components/sectionTitle/index.vue'
import {
    getWaterElectricityRoomDetail,
    getWaterElectricityLogList,
    deleteWaterElectricityBindRelation,
    type RoomWaterElectricityDetailVo,
    type RoomWaterElectricityLogVo,
    type RoomWaterElectricityLogQueryDTO
} from '@/api/smartWaterElectricity'

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const logLoading = ref(false)
const roomDetail = ref<RoomWaterElectricityDetailVo | null>(null)
const waterElectricityLogList = ref<RoomWaterElectricityLogVo[]>([])
const dateRange = ref<string[]>([])
const dateType = ref<number | string>(1) // 默认近7天

// 分页配置
const logPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true
})

// 用量记录表格列配置
const logColumns = [
    {
        title: '日期',
        dataIndex: 'createTime',
        width: 150,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '电用量',
        dataIndex: 'eleUseNum',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '电费用',
        dataIndex: 'eleTotal',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '冷水用量',
        dataIndex: 'coolUseNum',
        width: 130,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '冷水费用',
        dataIndex: 'coolTotal',
        width: 130,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '热水用量',
        dataIndex: 'hotUseNum',
        width: 130,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '热水费用',
        dataIndex: 'hotTotal',
        width: 130,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    }
]

// Emits
const emit = defineEmits<{
    'cancel': []
    'refresh': []
}>()

// 显示抽屉
const show = async (roomData: any) => {
    visible.value = true
    loading.value = true

    // 初始化日期类型和日期范围
    dateType.value = 1
    dateRange.value = calculateDateRange(1)
    console.log(roomData)
    try {
        // 获取房间水电详情
        const response = await getWaterElectricityRoomDetail(roomData.id)
        if (response && response.data) {
            roomDetail.value = response.data
        }

        // 加载用量记录
        await loadWaterElectricityLog()
    } catch (error) {
        console.error('获取房间详情失败:', error)
    } finally {
        loading.value = false
    }
}

// 加载用量记录
const loadWaterElectricityLog = async () => {
    if (!roomDetail.value) return

    logLoading.value = true
    try {
        const params: RoomWaterElectricityLogQueryDTO = {
            pageNum: logPagination.current,
            pageSize: logPagination.pageSize,
            roomId: roomDetail.value.roomInfo.id,
            dateType: dateType.value === '' ? undefined : Number(dateType.value),
            startDate: (dateRange.value && dateRange.value[0]) || undefined,
            endDate: (dateRange.value && dateRange.value[1]) || undefined
        }

        const response = await getWaterElectricityLogList(params)
        if (response && response.data && Array.isArray(response.data)) {
            waterElectricityLogList.value = response.data
            logPagination.total = response.data.length
        }
    } catch (error) {
        console.error('获取用量记录失败:', error)
    } finally {
        logLoading.value = false
    }
}

// 解绑房间
const handleUnbindRoom = () => {
    if (!roomDetail.value) return

    Modal.confirm({
        title: '确认解绑',
        content: `确定要解绑房间"${roomDetail.value.roomInfo.roomName}"吗？解绑后将无法恢复。`,
        onOk: async () => {
            try {
                await deleteWaterElectricityBindRelation([roomDetail.value!.roomInfo.id])
                Message.success('解绑成功')
                // 关闭抽屉并刷新列表
                handleCancel()
                emit('refresh')
            } catch (error) {
                console.error('解绑失败:', error)
            }
        }
    })
}

// 根据日期类型计算日期范围
const calculateDateRange = (type: number | string) => {
    const numType = Number(type)
    const now = new Date()
    const endDate = now.toISOString().split('T')[0] // 今天
    let startDate = ''

    switch (numType) {
        case 1: // 近7天
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 2: // 近半月
            startDate = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 3: // 近一月
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 4: // 近三月
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 5: // 近半年
            startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 6: // 近一年
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        default:
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }

    return [startDate, endDate]
}

// 日期类型变化
const handleDateTypeChange = () => {
    if (dateType.value === '') {
        // 选择"全部"时清空日期选择
        dateRange.value = []
    } else {
        // 其他选项时计算对应的日期范围
        dateRange.value = calculateDateRange(dateType.value)
    }
    logPagination.current = 1
}

// 时间范围变化
const handleDateChange = () => {
    logPagination.current = 1
}

// 查询用量记录
const handleSearchLog = () => {
    logPagination.current = 1
    loadWaterElectricityLog()
}

// 分页变化
const handleLogPageChange = (page: number) => {
    logPagination.current = page
    loadWaterElectricityLog()
}

const handleLogPageSizeChange = (pageSize: number) => {
    logPagination.pageSize = pageSize
    logPagination.current = 1
    loadWaterElectricityLog()
}

// 取消处理
const handleCancel = () => {
    visible.value = false
    roomDetail.value = null
    waterElectricityLogList.value = []
    dateRange.value = []
    dateType.value = 1
    logPagination.current = 1
    emit('cancel')
}

// 暴露方法给父组件
defineExpose({
    show
})
</script>

<style lang="less" scoped>
.device-detail-container {
    .filter-section {
        margin-bottom: 16px;
    }
    
    .header-actions {
        display: flex;
        gap: 8px;
    }
}
</style>
