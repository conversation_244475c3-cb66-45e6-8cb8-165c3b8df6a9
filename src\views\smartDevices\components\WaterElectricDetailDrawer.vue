<template>
    <a-drawer
        v-model:visible="visible"
        title="智能门锁详情"
        class="common-drawer"
        :footer="false"
        @cancel="handleCancel"
    >
        <a-spin :loading="loading" style="width: 100%;">
            <div v-if="deviceDetail" class="device-detail-container">
                <!-- 设备信息 -->
                <section-title title="绑定信息" style="margin-bottom: 16px;" />
                <a-form
                    :model="deviceDetail"
                    label-align="right"
                    :label-col-props="{ span: 6 }"
                    :wrapper-col-props="{ span: 18 }"
                    auto-label-width
                >
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="所属房源">
                                <a-input v-model="deviceDetail.roomName" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="设备品牌">
                                <a-input v-model="deviceBrand" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-button type="primary" @click="handleUnbindDevice">解绑</a-button>
                            <a-button style="margin-left: 16px;" type="primary" @click="handleGeneratePassword">获取临时密码</a-button>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="设备门牌号">
                                <a-input v-model="deviceDetail.lockName" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="设备ID">
                                <a-input v-model="deviceDetail.lockId" disabled />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
                <!-- 操作记录 -->
                <section-title title="密码记录" style="margin-bottom: 16px;"/>
                <!-- 时间筛选 -->
                <div class="filter-section" style="margin-bottom: 16px;">
                    <a-row :gutter="16">
                        <a-col :span="12">
                            <a-select
                                v-model="dateType"
                                @change="handleDateTypeChange"
                                style="width: 120px;"
                            >
                                <a-option value="">全部</a-option>
                                <a-option :value="1">近7天</a-option>
                                <a-option :value="2">近半月</a-option>
                                <a-option :value="3">近一月</a-option>
                                <a-option :value="4">近三月</a-option>
                                <a-option :value="5">近半年</a-option>
                                <a-option :value="6">近一年</a-option>
                            </a-select>
                            <a-range-picker
                                v-model="dateRange"
                                style="margin-left: 16px;"
                                @change="handleDateChange"
                            />
                            <a-button style="margin-left: 16px;" type="primary" @click="handleSearchLog">查询</a-button>
                        </a-col>
                    </a-row>
                </div>

                <!-- 操作记录表格 -->
                <a-table
                    :data="operateLogList"
                    :columns="logColumns"
                    :pagination="logPagination"
                    :loading="logLoading"
                    row-key="id"
                    size="small"
                    :bordered="{ cell: true }"
                    @page-change="handleLogPageChange"
                    @page-size-change="handleLogPageSizeChange"
                >
                    <template #type="{ record }">
                        {{ getPasswordTypeText(record.type) }}
                    </template>
                    <template #validPeriod="{ record }">
                        {{ getValidPeriodText(record.startDate, record.endDate) }}
                    </template>
                </a-table>
            </div>
        </a-spin>
    </a-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import SectionTitle from '@/components/sectionTitle/index.vue'
import { getTTLockDetail, getLockOperateLog, generateTempPassword, unbindRoomDevice, type TTLockDeviceVo, type TTLockOperateLogVo } from '@/api/smartLock'

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const logLoading = ref(false)
const deviceDetail = ref<TTLockDeviceVo | null>(null)
const operateLogList = ref<TTLockOperateLogVo[]>([])
const dateRange = ref<string[]>([])
const dateType = ref<number | string>(1) // 默认近7天
const deviceBrand = ref('通通锁') // 设备品牌固定显示

// 分页配置
const logPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true
})

// 操作记录表格列配置
const logColumns = [
    {
        title: '生成日期',
        dataIndex: 'createTime',
        width: 150,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '密码',
        dataIndex: 'password',
        width: 120,
        align: 'center'
    },
    {
        title: '密码类型',
        dataIndex: 'type',
        width: 100,
        align: 'center',
        slotName: 'type'
    },
    {
        title: '有效期',
        dataIndex: 'validPeriod',
        width: 200,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        slotName: 'validPeriod'
    },
    {
        title: '生成者',
        dataIndex: 'createByName',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    }
]

// 获取密码类型文本
const getPasswordTypeText = (type: number) => {
    switch (type) {
        case 1:
            return '单次'
        case 2:
            return '永久'
        case 3:
            return '限期'
        default:
            return '未知'
    }
}

// 获取有效期文本
const getValidPeriodText = (startDate: string, endDate: string) => {
    if (!startDate && !endDate) {
        return '-'
    }
    if (!startDate) {
        return `~ ${endDate}`
    }
    if (!endDate) {
        return `${startDate} ~`
    }
    return `${startDate} ~ ${endDate}`
}

// Emits
const emit = defineEmits<{
    'cancel': []
    'refresh': []
}>()

// 显示抽屉
const show = async (record: TTLockDeviceVo) => {
    visible.value = true
    loading.value = true

    // 初始化日期类型和日期范围
    dateType.value = 1
    dateRange.value = calculateDateRange(1)

    try {
        // 获取设备详情
        const response = await getTTLockDetail(record.id)
        if (response && response.data) {
            deviceDetail.value = { ...record, ...response.data }
        } else {
            deviceDetail.value = record
        }

        // 加载操作记录
        await loadOperateLog()
    } catch (error) {
        console.error('获取设备详情失败:', error)
        deviceDetail.value = record
    } finally {
        loading.value = false
    }
}

// 加载操作记录
const loadOperateLog = async () => {
    if (!deviceDetail.value) return
    
    logLoading.value = true
    try {
        const params = {
            pageNum: logPagination.current,
            pageSize: logPagination.pageSize,
            deviceId: deviceDetail.value.id,
            dateType: dateType.value === '' ? undefined : Number(dateType.value),
            startDate: (dateRange.value && dateRange.value[0]) || undefined,
            endDate: (dateRange.value && dateRange.value[1]) || undefined
        }
        
        const response = await getLockOperateLog(params)
        if (response && response.data && Array.isArray(response.data)) {
            operateLogList.value = response.data
            // 由于接口没有返回total字段，暂时设置为数据长度
            logPagination.total = response.data.length
        }
    } catch (error) {
        console.error('获取操作记录失败:', error)
    } finally {
        logLoading.value = false
    }
}

// 生成临时密码
const handleGeneratePassword = async () => {
    if (!deviceDetail.value) return
    
    try {
        await generateTempPassword(deviceDetail.value.id)
        Message.success('临时密码生成成功')
        // 刷新操作记录
        await loadOperateLog()
    } catch (error) {
        console.error('生成临时密码失败:', error)
    }
}

// 解绑设备
const handleUnbindDevice = () => {
    if (!deviceDetail.value) return

    Modal.confirm({
        title: '确认解绑',
        content: `确定要解绑设备"${deviceDetail.value.roomName}"吗？解绑后将无法恢复。`,
        onOk: async () => {
            try {
                await unbindRoomDevice([deviceDetail.value!.id])
                Message.success('解绑成功')
                // 关闭抽屉并刷新列表
                handleCancel()
                emit('refresh')
            } catch (error) {
                console.error('解绑失败:', error)
            }
        }
    })
}

// 根据日期类型计算日期范围
const calculateDateRange = (type: number | string) => {
    const numType = Number(type)
    const now = new Date()
    const endDate = now.toISOString().split('T')[0] // 今天
    let startDate = ''

    switch (numType) {
        case 1: // 近7天
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 2: // 近半月
            startDate = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 3: // 近一月
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 4: // 近三月
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 5: // 近半年
            startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        case 6: // 近一年
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            break
        default:
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }

    return [startDate, endDate]
}

// 日期类型变化
const handleDateTypeChange = () => {
    if (dateType.value === '') {
        // 选择"全部"时清空日期选择
        dateRange.value = []
    } else {
        // 其他选项时计算对应的日期范围
        dateRange.value = calculateDateRange(dateType.value)
    }
    logPagination.current = 1
}

// 时间范围变化
const handleDateChange = () => {
    logPagination.current = 1
}

// 查询操作记录
const handleSearchLog = () => {
    logPagination.current = 1
    loadOperateLog()
}

// 分页变化
const handleLogPageChange = (page: number) => {
    logPagination.current = page
    loadOperateLog()
}

const handleLogPageSizeChange = (pageSize: number) => {
    logPagination.pageSize = pageSize
    logPagination.current = 1
    loadOperateLog()
}

// 取消处理
const handleCancel = () => {
    visible.value = false
    deviceDetail.value = null
    operateLogList.value = []
    dateRange.value = []
    dateType.value = 1
    logPagination.current = 1
    emit('cancel')
}

// 暴露方法给父组件
defineExpose({
    show
})
</script>

<style lang="less" scoped>
.device-detail-container {
    .filter-section {
        margin-bottom: 16px;
    }
    
    .header-actions {
        display: flex;
        gap: 8px;
    }
}
</style>
