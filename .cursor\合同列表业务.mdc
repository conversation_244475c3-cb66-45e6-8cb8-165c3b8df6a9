---
description: 
globs: 
alwaysApply: false
---
# 合同类型页签
1. 合同类型(contractType)包含: null-全部、0-非宿舍、1-宿舍类、2-多经、3-日租房
2. 默认选中全部
3. 切换合同类型,重置分页,获取合同列表

# 筛选条件
1. 根据【接口XXX】的入参,绑定上对应属性名
2. 点击查询,重置分页,获取合同列表
3. 点击重置,清空筛选值,保留合同类型、合同状态、项目,重置分页,获取合同列表
4. 涉及日期的,格式为YYYY-MM-DD
5. 合同用途数据来源于【接口xxx】
6. 最新操作类型包含: 0-新签、1-变更条款、2-退租, 多选
7. 合同二级状态包含: 
8. 签约类型包含: 0-新签、1-续签,多选
9. 签约方式包含: 0-电子合同、1-纸质合同(甲方电子章)、2-纸质合同（双方实体章）
10. 是否上传签署文件、纸质文件是否确收、是否完全结束包含: 0-否、1-是

# 合同状态页签
1. 合同状态(status)包含: null-全部、10-草稿、20-待生效、30-生效中、40-失效
2. 默认选中草稿
3. 切换合同类型,重新获取合同列表,需要重置分页

# 更新责任人
1. TODO

# 新增合同
1. 打开创建合同抽屉

# 导出
1. 不调用接口,前端实现excel导出

# 合同列表
1. 根据【接口XXX】的出参,绑定上每一列的属性名
2. 单元格一行展示,不换行: 分析列名,判断它的值是否是固定长度,如果是固定长度,则设置列宽；如果不确定长度,则设置一个基础列宽,增加列属性ellipsis:  true 和 toooltip:  true
3. 操作列最多展示3个按钮,超过三个则使用“更多”下拉

