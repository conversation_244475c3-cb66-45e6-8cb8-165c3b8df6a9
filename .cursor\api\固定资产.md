---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁后台/固定资产

<a id="opIdedit_8"></a>

## PUT 修改固定资产

PUT /fixed/assets

修改固定资产

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "category": 0,
  "name": "string",
  "specification": "string",
  "attachments": "string",
  "usageScope": "string",
  "usageScopeName": "string",
  "usageScopeList": [
    {
      "dictCode": 0,
      "parentCode": 0,
      "dictSort": 0,
      "dictLabel": "string",
      "dictValue": "string",
      "dictType": "string",
      "cssClass": "string",
      "listClass": "string",
      "isDefault": "string",
      "status": "string",
      "remark": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "childList": [
        {
          "dictCode": 0,
          "parentCode": 0,
          "dictSort": 0,
          "dictLabel": "string",
          "dictValue": "string",
          "dictType": "string",
          "cssClass": "string",
          "listClass": "string",
          "isDefault": "string",
          "status": "string",
          "remark": "string",
          "createTime": "2019-08-24T14:15:22Z",
          "childList": [
            {}
          ]
        }
      ]
    }
  ],
  "remark": "string",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FixedAssetsAddDTO](#schemafixedassetsadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_7"></a>

## POST 新增固定资产

POST /fixed/assets

新增固定资产

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "category": 0,
  "name": "string",
  "specification": "string",
  "attachments": "string",
  "usageScope": "string",
  "usageScopeName": "string",
  "usageScopeList": [
    {
      "dictCode": 0,
      "parentCode": 0,
      "dictSort": 0,
      "dictLabel": "string",
      "dictValue": "string",
      "dictType": "string",
      "cssClass": "string",
      "listClass": "string",
      "isDefault": "string",
      "status": "string",
      "remark": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "childList": [
        {
          "dictCode": 0,
          "parentCode": 0,
          "dictSort": 0,
          "dictLabel": "string",
          "dictValue": "string",
          "dictType": "string",
          "cssClass": "string",
          "listClass": "string",
          "isDefault": "string",
          "status": "string",
          "remark": "string",
          "createTime": "2019-08-24T14:15:22Z",
          "childList": [
            {}
          ]
        }
      ]
    }
  ],
  "remark": "string",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FixedAssetsAddDTO](#schemafixedassetsadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport_3"></a>

## POST 导出询固定资产列表

POST /fixed/assets/export

导出询固定资产列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|category|query|string| 否 | 种类|种类|
|name|query|string| 否 | 物品名称|物品名称|
|specification|query|string| 否 | 规格|规格|
|attachments|query|string| 否 | 附件|附件|
|usageScope|query|string| 否 | 使用范围|使用范围|
|remark|query|string| 否 | 备注|备注|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|integer(int32)| 否 | 0-否,1-是|0-否,1-是|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIdlist_15"></a>

## GET 查询固定资产列表

GET /fixed/assets/list

根据种类、物品名称（模糊查询）分页查询固定资产列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|category|query|integer(int32)| 否 | 种类|种类|
|name|query|string| 否 | 物品名称|物品名称|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"id":"string","category":0,"name":"string","specification":"string","attachments":"string","usageScope":"string","usageScopeList":["string"],"remark":"string","createByName":"string","updateByName":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[FixedAssetsVo](#schemafixedassetsvo)]|false|none||none|
|» id|string|false|none|主键ID|none|
|» category|integer(int32)|false|none|种类，字典值item_type|种类|
|» name|string|false|none|物品名称|物品名称|
|» specification|string|false|none|规格|规格|
|» attachments|string|false|none|附件|附件|
|» usageScope|string|false|none|使用范围，字典值diversification_purpose|使用范围|
|» usageScopeList|[string]|false|none||none|
|» remark|string|false|none|备注|备注|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<a id="opIdgetInfo_9"></a>

## GET 获取固定资产详细信息

GET /fixed/assets/detail

获取固定资产详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||固定资产id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_7"></a>

## DELETE 删除固定资产

DELETE /fixed/assets/delete

删除固定资产

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|query|array[string]| 是 ||固定资产ID列表|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_FixedAssetsAddDTO">FixedAssetsAddDTO</h2>

<a id="schemafixedassetsadddto"></a>
<a id="schema_FixedAssetsAddDTO"></a>
<a id="tocSfixedassetsadddto"></a>
<a id="tocsfixedassetsadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "category": 0,
  "name": "string",
  "specification": "string",
  "attachments": "string",
  "usageScope": "string",
  "usageScopeName": "string",
  "usageScopeList": [
    {
      "dictCode": 0,
      "parentCode": 0,
      "dictSort": 0,
      "dictLabel": "string",
      "dictValue": "string",
      "dictType": "string",
      "cssClass": "string",
      "listClass": "string",
      "isDefault": "string",
      "status": "string",
      "remark": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "childList": [
        {
          "dictCode": 0,
          "parentCode": 0,
          "dictSort": 0,
          "dictLabel": "string",
          "dictValue": "string",
          "dictType": "string",
          "cssClass": "string",
          "listClass": "string",
          "isDefault": "string",
          "status": "string",
          "remark": "string",
          "createTime": "2019-08-24T14:15:22Z",
          "childList": [
            {}
          ]
        }
      ]
    }
  ],
  "remark": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|category|integer(int32)|false|none|种类|种类|
|name|string|false|none|物品名称|物品名称|
|specification|string|false|none|规格|规格|
|attachments|string|false|none|附件|附件|
|usageScope|string|false|none|使用范围|使用范围|
|usageScopeName|string|false|none|使用范围|使用范围|
|usageScopeList|[[SysDictDataDTO](#schemasysdictdatadto)]|false|none|使用范围数组|使用范围数组|
|remark|string|false|none|备注|备注|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_SysDictDataDTO">SysDictDataDTO</h2>

<a id="schemasysdictdatadto"></a>
<a id="schema_SysDictDataDTO"></a>
<a id="tocSsysdictdatadto"></a>
<a id="tocssysdictdatadto"></a>

```json
{
  "dictCode": 0,
  "parentCode": 0,
  "dictSort": 0,
  "dictLabel": "string",
  "dictValue": "string",
  "dictType": "string",
  "cssClass": "string",
  "listClass": "string",
  "isDefault": "string",
  "status": "string",
  "remark": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "childList": [
    {
      "dictCode": 0,
      "parentCode": 0,
      "dictSort": 0,
      "dictLabel": "string",
      "dictValue": "string",
      "dictType": "string",
      "cssClass": "string",
      "listClass": "string",
      "isDefault": "string",
      "status": "string",
      "remark": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "childList": [
        {
          "dictCode": 0,
          "parentCode": 0,
          "dictSort": 0,
          "dictLabel": "string",
          "dictValue": "string",
          "dictType": "string",
          "cssClass": "string",
          "listClass": "string",
          "isDefault": "string",
          "status": "string",
          "remark": "string",
          "createTime": "2019-08-24T14:15:22Z",
          "childList": [
            {}
          ]
        }
      ]
    }
  ]
}

```

使用范围数组

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|dictCode|integer(int64)|false|none||none|
|parentCode|integer(int64)|false|none||none|
|dictSort|integer(int64)|false|none||none|
|dictLabel|string|false|none||none|
|dictValue|string|false|none||none|
|dictType|string|false|none||none|
|cssClass|string|false|none||none|
|listClass|string|false|none||none|
|isDefault|string|false|none||none|
|status|string|false|none||none|
|remark|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|childList|[[SysDictDataDTO](#schemasysdictdatadto)]|false|none||[使用范围数组]|

<h2 id="tocS_FixedAssetsVo">FixedAssetsVo</h2>

<a id="schemafixedassetsvo"></a>
<a id="schema_FixedAssetsVo"></a>
<a id="tocSfixedassetsvo"></a>
<a id="tocsfixedassetsvo"></a>

```json
{
  "id": "string",
  "category": 0,
  "name": "string",
  "specification": "string",
  "attachments": "string",
  "usageScope": "string",
  "usageScopeList": [
    "string"
  ],
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|category|integer(int32)|false|none|种类，字典值item_type|种类|
|name|string|false|none|物品名称|物品名称|
|specification|string|false|none|规格|规格|
|attachments|string|false|none|附件|附件|
|usageScope|string|false|none|使用范围，字典值diversification_purpose|使用范围|
|usageScopeList|[string]|false|none||none|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

