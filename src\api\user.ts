import http from './index';
import type { RouteRecordNormalized } from 'vue-router';
import { UserState } from '@/store/modules/user/types';

export interface LoginData {
    username: string;
    password: string;
}

export interface LoginRes {
    access_token: string;
}
export function login(data: LoginData) {
    return http.post<LoginRes>('/auth/login', data);
}

export function logout() {
    return http.delete<LoginRes>('/auth/logout');
}
export function getUserInfo() {
    return http.get<UserState>(`/business-platform/user/getInfo`);
}
export function getMenuList() {
    return http.get(`/business-platform/menu/list`);
}

export function getUserList(data: any) {
    return http.post(`/business-platform/user/list`, data);
}

export function setUserAuthRole(data: any) {
    return http.post(`/business-platform/user/authRole`, data);
}

export function addUser(data: any) {
    return http.post(`/business-platform/user/add`, data);
}

export function updateUser(data: any) {
    return http.put(`/business-platform/user/update`, data);
}
//POST
// /user/changeStatus
// 用户状态修改
export function changeUserStatus(data: any) {
    return http.post(`/business-platform/user/changeStatus`, data);
}
// GET
// /user/info/{username}
// 获取用户信息
export function getUserAuthRoleByUsername(data: any) {
    return http.get(`/business-platform/user/authRole/${data}`);
}
