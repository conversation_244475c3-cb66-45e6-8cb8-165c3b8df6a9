<template>
    <section>
        <sectionTitle title="补充条款" />
        <div class="content">
            <a-form :disabled="isFormDisabled" :model="contractData" :layout="isDetailMode ? 'horizontal' : 'vertical'" auto-label-width>
                <a-grid :cols="1" :col-gap="16" :row-gap="0">
                    <a-grid-item>
                        <a-form-item>
                                <a-textarea v-model="contractData.otherInfo" placeholder="请输入" allow-clear
                                    maxlength="1000" :disabled="isFormDisabled" />
                        </a-form-item>
                    </a-grid-item>
                </a-grid>
            </a-form>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { ref, computed, toRef, inject } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { useContractStore } from '@/store/modules/contract'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail')

// 表单禁用逻辑：详情模式 或者 变更模式且包含费用条款变更（changeType 包含 3）
const isFormDisabled = computed(() => {
    if (isDetailMode.value) {
        return true
    }
    // 只有变更类型为“仅费用条款变更”时禁用，组合变更允许编辑
    if (editType.value === 'change' && changeType.value.includes(3) && changeType.value.length === 1) {
        return true
    }
    if (editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo') {
        return true
    }
    return false
})

const formItemMargin = computed(() => isDetailMode.value ? '16px' : '16px')

const validate = async () => {
    return new Promise((resolve) => {
        // 补充条款没有必填字段，直接通过验证
        resolve(true)
    })
}

defineExpose({
    validate
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.arco-form-item {
    margin-bottom: v-bind(formItemMargin);
}

.detail-text {
    padding: 4px 0;
    color: #333;
}
</style>