# 智能水电Tab组件联调功能实现说明

## 更新概述
参考智能门锁Tab组件 `src/views/smartDevices/components/DoorLockTab.vue` 的功能，更新了智能水电Tab组件 `src/views/smartDevices/components/WaterElectricTab.vue`，并联调了智能水电的接口文件 `src/api/smartWaterElectricity.ts`。

## 主要修改内容

### 1. 模板结构更新

#### 表格配置优化
**修改前：**
```vue
<a-table
  row-key="id"
  :scroll="{ x: '100%', y: '100%' }"
>
  <template #columns>
    <a-table-column title="序号" :width="80" align="center">
      <!-- 使用 a-table-column 方式 -->
    </a-table-column>
  </template>
</a-table>
```

**修改后：**
```vue
<a-table
  row-key="roomId"
  :columns="columns"
  :bordered="{ cell: true }"
  :scroll="{ x: 1 }"
  :stripe="true"
>
  <template #index="{ rowIndex }">
    {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
  </template>
  <template #action="{ record }">
    <!-- 操作按钮 -->
  </template>
</a-table>
```

### 2. 接口集成

#### API导入和类型定义
```typescript
import { getWaterElectricityList, type RoomVo } from '@/api/smartWaterElectricity'

// 响应式数据类型更新
const tableData = ref<RoomVo[]>([])
```

#### 接口调用逻辑
```typescript
const fetchTableData = async () => {
  // 如果没有项目ID，不调用接口
  if (!props.filterForm.projectId) {
    tableData.value = []
    pagination.total = 0
    return
  }

  const params = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    projectId: props.filterForm.projectId,
    searchParam: props.filterForm.buildingOrRoom || undefined,
    roomName: props.filterForm.buildingOrRoom || undefined,
    bindFlag: 1 // 只显示已绑定的设备
  }

  const response = await getWaterElectricityList(params)
}
```

### 3. 表格列配置

#### 智能水电专用列配置
根据智能水电业务需求，配置了以下表格列：

```typescript
const columns = [
    { title: '序号', slotName: 'index', width: 80, align: 'center' },
    { title: '房号', dataIndex: 'roomName', width: 100, ellipsis: true, tooltip: true, align: 'center' },
    { title: '项目', dataIndex: 'projectName', width: 150, ellipsis: true, tooltip: true, align: 'center' },
    { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
    { title: '楼栋', dataIndex: 'buildingName', width: 100, ellipsis: true, tooltip: true, align: 'center' },
    { title: '用途', dataIndex: 'propertyTypeName', width: 100, ellipsis: true, tooltip: true, align: 'center' },
    { title: '智能水电表', dataIndex: 'smartWaterMeter', width: 120, ellipsis: true, tooltip: true, align: 'center' },
    { title: '计租面积㎡', dataIndex: 'rentArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
    { title: '基础租金', dataIndex: 'baseRent', width: 120, ellipsis: true, tooltip: true, align: 'center' },
    { title: '操作', slotName: 'action', width: 120, align: 'center', fixed: 'right' }
]
```

### 4. 数据处理逻辑

#### 移除模拟数据
- 删除了原有的 `mockData` 模拟数据
- 移除了基于模拟数据的筛选和分页逻辑

#### 真实API调用
- 使用 `getWaterElectricityList` 接口获取数据
- 支持项目ID、搜索参数、房间名称等筛选条件
- 只显示已绑定的智能水电设备（`bindFlag: 1`）

### 5. 事件处理优化

#### 类型安全的事件处理
```typescript
// 编辑处理
const handleEdit = (record: RoomVo) => {
  emit('edit-device', record)
}

// 详情处理
const handleDetail = (record: RoomVo) => {
  emit('view-detail', record)
}
```

### 6. 组件功能特性

#### 与智能门锁Tab组件保持一致的功能
1. **分页支持**：完整的分页功能，包括页码和页大小变更
2. **行选择**：支持多选，与父组件双向绑定
3. **加载状态**：统一的加载状态管理
4. **错误处理**：统一的错误提示机制
5. **响应式设计**：支持不同屏幕尺寸

#### 智能水电特有功能
1. **设备信息展示**：显示智能水电表、计租面积、基础租金等信息
2. **绑定状态筛选**：只显示已绑定的设备
3. **业务字段映射**：使用 `propertyTypeName`（用途）、`smartWaterMeter`（智能水电表）等字段

## 技术实现细节

### 1. 接口参数映射
- `projectId`：项目ID（必填）
- `searchParam`：搜索参数（楼栋/房间名称）
- `roomName`：房间名称（模糊搜索）
- `bindFlag: 1`：只显示已绑定设备

### 2. 数据字段映射
| 显示列名 | 数据字段 | 说明 |
|---------|---------|------|
| 房号 | roomName | 房间名称 |
| 项目 | projectName | 项目名称 |
| 所属地块 | parcelName | 地块名称 |
| 楼栋 | buildingName | 楼栋名称 |
| 用途 | propertyTypeName | 物业类型名称 |
| 智能水电表 | smartWaterMeter | 智能水电表信息 |
| 计租面积㎡ | rentArea | 计租面积 |
| 基础租金 | baseRent | 基础租金 |

### 3. 组件通信
- **Props**：接收筛选表单和选中行数据
- **Emits**：发送加载状态、选中行变更、编辑和详情事件
- **Expose**：暴露数据获取和分页重置方法

### 4. 生命周期管理
- 组件挂载时不自动获取数据
- 由父组件控制数据加载时机
- 支持外部调用 `fetchTableData` 方法刷新数据

## 与智能门锁Tab的差异

### 1. 数据源不同
- **智能门锁**：使用 `getTTLockDeviceList` 接口，数据类型为 `TTLockDeviceVo[]`
- **智能水电**：使用 `getWaterElectricityList` 接口，数据类型为 `RoomVo[]`

### 2. 表格列不同
- **智能门锁**：显示锁名称、锁别名、锁ID等门锁特有信息
- **智能水电**：显示智能水电表、计租面积、基础租金等水电特有信息

### 3. 业务逻辑不同
- **智能门锁**：使用 `roomOrBuildingName` 参数进行搜索
- **智能水电**：使用 `searchParam` 和 `roomName` 参数进行搜索

## 注意事项

1. **接口响应格式**：确保 `getWaterElectricityList` 接口返回的是 `RoomVo[]` 数组格式
2. **分页处理**：当前实现为前端分页，如需后端分页需要调整接口参数
3. **错误处理**：所有API调用都包含了错误处理和用户提示
4. **性能优化**：使用了防循环检查避免不必要的数据更新

## 后续优化建议

1. **后端分页**：如果数据量大，建议改为后端分页
2. **缓存机制**：可以添加数据缓存减少重复请求
3. **虚拟滚动**：对于大量数据可以考虑虚拟滚动优化
4. **实时更新**：可以考虑添加WebSocket实现数据实时更新
