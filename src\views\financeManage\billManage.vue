<template>
    <div class="container">
        <div class="content">
            <!-- 顶部状态切换 -->
            <a-tabs v-model:activeKey="activeType" size="large" hide-content @change="handleChangeType"
                style="margin-bottom: 16px;">
                <a-tab-pane v-for="item in typeOptions" :key="item.value" :title="item.label" />
            </a-tabs>
            <!-- 搜索区域 -->
            <a-card class="general-card">
                <a-row>
                    <a-col :flex="1">
                        <a-form :model="formModel" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }"
                            label-align="right">
                            <a-row :gutter="16">
                                <a-col :span="8">
                                    <a-form-item field="projectId" label="项目">
                                        <ProjectTreeSelect v-model="formModel.projectId" placeholder="请选择项目" allow-clear
                                            @change="handleProjectChange" />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="unitName" label="租赁房名称">
                                        <a-input v-model="formModel.roomName" placeholder="请输入租赁房名称" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="customerName" label="承租方名称">
                                        <a-input v-model="formModel.customerName" placeholder="请输入承租方名称" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="contractNo" label="合同号">
                                        <a-input v-model="formModel.bizNo" placeholder="请输入合同号" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="statusList" label="账单状态">
                                        <a-select v-model="formModel.statusList" placeholder="请选择账单状态" multiple
                                            allow-clear>
                                            <a-option value="0">待收</a-option>
                                            <a-option value="1">待付</a-option>
                                            <a-option value="2">已收</a-option>
                                            <a-option value="3">已付</a-option>
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="dateRange" label="应收/付日期">
                                        <a-range-picker v-model="formModel.dateRange" style="width: 100%"
                                            :default-value="defaultDateRange" />
                                    </a-form-item>
                                </a-col>
                                <template v-if="advancedSearchVisible">
                                    <a-col :span="8">
                                        <a-form-item field="costType" label="账单类型">
                                            <a-select v-model="formModel.costType" placeholder="请选择账单类型" allow-clear>
                                                <a-option value="1">保证金</a-option>
                                                <a-option value="2">租金</a-option>
                                                <a-option value="3">其他费用</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="8">
                                        <a-form-item field="confirmStatus" label="确认状态">
                                            <a-select v-model="formModel.confirmStatus" placeholder="请选择确认状态"
                                                allow-clear>
                                                <a-option value="0">待确认</a-option>
                                                <a-option value="1">已确认</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-col>
                                </template>
                            </a-row>
                        </a-form>
                    </a-col>
                    <a-divider style="height: 84px" direction="vertical" />
                    <a-col :flex="'86px'" style="text-align: right">
                        <a-space direction="vertical" :size="18">
                            <a-button type="primary" @click="search">
                                <template #icon>
                                    <icon-search />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="reset">
                                <template #icon>
                                    <icon-refresh />
                                </template>
                                重置
                            </a-button>
                        </a-space>
                    </a-col>
                </a-row>
                <a-row>
                    <AdvancedSearch @toggle="handleToggle" />
                </a-row>
                <!-- 状态-表头操作区 -->
                <div class="table-header">
                    <a-tabs v-model:activeKey="activeStatus" type="rounded" hide-content @change="handleChangeStatus">
                        <a-tab-pane v-for="item in statusOptions" :key="item.value" :title="item.label" />
                        <template #extra>
                            <a-space v-if="activeStatus === '0' || activeStatus === '1'">
                                <a-typography-text :class="{ active: activeFilter === 'overdue' }"
                                    style="cursor: pointer;" @click="handleFilterClick('overdue')">
                                    逾期（{{ summaryData.overdueCount || 0 }}）
                                </a-typography-text>
                                <a-typography-text :class="{ active: activeFilter === 'today' }"
                                    style="cursor: pointer;" @click="handleFilterClick('today')">
                                    今日应收（{{ summaryData.todayCount || 0 }}）
                                </a-typography-text>
                                <a-typography-text :class="{ active: activeFilter === 'sevenDays' }"
                                    style="cursor: pointer;" @click="handleFilterClick('sevenDays')">
                                    近7天应收（{{ summaryData.sevenDaysCount || 0 }}）
                                </a-typography-text>
                            </a-space>
                            <a-space v-if="activeStatus === '2'">
                                <a-typography-text :class="{ active: activeFilter === 'pendingConfirm' }"
                                    style="cursor: pointer;" @click="handleFilterClick('pendingConfirm')">
                                    待确认（{{ summaryData.pendingConfirmCount || 0 }}）
                                </a-typography-text>
                                <a-typography-text :class="{ active: activeFilter === 'confirmed' }"
                                    style="cursor: pointer;" @click="handleFilterClick('confirmed')">
                                    已确认（{{ summaryData.confirmedCount || 0 }}）
                                </a-typography-text>
                            </a-space>
                            <a-space>
                                <a-button v-permission="['rent:billManage:export']" type="primary" @click="handleExport">导出</a-button>
                            </a-space>
                        </template>
                    </a-tabs>
                </div>

                <!-- 表格区域 -->
                <a-table row-key="id" :loading="loading" :pagination="pagination" :columns="columns" :data="tableData"
                    :bordered="{ cell: true }" :scroll="{ x: 1 }" @page-change="onPageChange"
                    @page-size-change="onPageSizeChange">
                    <template #index="{ rowIndex }">
                        {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
                    </template>
                    <template #bizNo="{ record }">
                        <!-- <div class="ellipsis-link" @click="handleContractDetail(record.bizId)">
                            {{ record.bizNo }}
                        </div> -->
                       <PermissionLink v-permission="['rent:billManage:contract']" @click="handleContractDetail(record.bizId)">{{ record.bizNo }}</PermissionLink>
                    </template>

                    <template #costType="{ record }">
                        <!-- 如果是租金类型，显示期数 -->
                        <template v-if="record.costType === 2">
                            租金第{{ record.period }}期
                        </template>
                        <template v-else>
                            {{ getCostTypeText(record.costType) }}
                        </template>
                    </template>

                    <template #status="{ record }">
                        <span>
                            {{ getStatusText(record.status) }}
                        </span>
                    </template>

                    <template #totalAmount="{ record }">
                        {{ formatAmount(record.totalAmount) }}
                    </template>

                    <template #discountAmount="{ record }">
                        {{ record.discountAmount ? formatAmount(record.discountAmount) : '0.00' }}
                    </template>

                    <template #actualReceivable="{ record }">
                        {{ formatAmount(record.actualReceivable) }}
                    </template>

                    <template #receivedAmount="{ record }">
                        {{ formatAmount(record.receivedAmount) }}
                    </template>

                    <template #unpaidAmount="{ record }">
                        {{ formatAmount(record.actualReceivable - record.receivedAmount) }}
                    </template>

                    <template #confirmStatus="{ record }">
                        <span>
                            {{ getConfirmStatusText(record.confirmStatus) }}
                        </span>
                    </template>

                    <template #contractType="{ record }">
                        <span>
                            {{ getContractTypeText(record.contractType) }}
                        </span>
                    </template>

                    <template #contractStatus="{ record }">
                        <span>
                            {{ getContractStatusText(record.contractStatus) }}
                        </span>
                    </template>

                    <template #roomName="{ record }">
                        <span>
                            {{ extractFirstTwoValues(record.roomName) }}
                        </span>
                    </template>

                    <template #operations="{ record }">
                        <a-space>
                            <!-- 查看流水 - 所有状态 -->
                            <a-button v-permission="['rent:billManage:flow']" type="text" size="mini" @click="handleViewFlow(record)">
                                <span v-if="record.status === 2">查看流水</span>
                                <span v-else>查看记账记录</span>
                            </a-button>

                            <!-- 账单码 - 待收状态 -->
                            <a-button v-permission="['rent:billManage:code']" v-if="record.status === 0" type="text" size="mini"
                                @click="handleCollectCode(record)">
                                账单码
                            </a-button>

                            <!-- 记账 - 待收/已收状态 -->
                            <a-button v-permission="['rent:billManage:handle']" v-if="record.status === 0" type="text" size="mini"
                                @click="handleAccount(record)">
                                记账
                            </a-button>

                            <!-- 查看收据 - 所有状态 -->
                            <a-button type="text" size="mini" @click="handleViewRecipt(record)">
                                <span>查看收据</span>
                            </a-button>
                            <!-- 催缴 - 待收状态 -->
                            <!-- <a-button v-if="record.status === 0" type="text" size="mini" @click="handleUrge(record)">
                                催缴
                            </a-button> -->

                            <!-- 减免缓 - 待收状态 -->
                            <!-- <a-button v-if="record.status === 0" type="text" size="mini"
                                @click="handleDiscount(record)">
                                减免缓
                            </a-button> -->

                            <!-- 取消记账 - 所有状态 -->
                            <!-- <a-button type="text" size="mini"
                                @click="handleCancelAccount(record)">
                                取消记账
                            </a-button> -->

                            <!-- 查看记账记录 - 已收状态 -->
                            <!-- <a-button v-if="record.status === 2" type="text" size="mini" @click="handleViewAccountRecord(record)">
                                查看记账记录
                            </a-button> -->
                        </a-space>
                    </template>
                </a-table>
            </a-card>
        </div>

        <!-- 流水详情抽屉 -->
        <a-drawer v-model:visible="flowDrawerVisible" title="查看流水" width="1200px" @cancel="handleFlowCancel">
            <flow-detail v-if="flowDrawerVisible" :data="currentFlowData" @refresh="handleFlowRefresh" />
        </a-drawer>

        <!-- 取消记账确认弹窗 -->
        <a-modal v-model:visible="cancelAccountVisible" title="提示" width="380px" @ok="handleCancelAccountConfirm"
            @cancel="handleCancelAccountCancel">
            <p><icon-exclamation-circle-fill style="color: #F56C6C;font-size: 16px;" /> 是否确认执行当前取消记账操作？</p>
        </a-modal>

        <!-- 收款码弹窗 -->
        <a-modal v-model:visible="collectCodeVisible" title="账单收款码" :footer="false" @cancel="handleCollectCancel"
            :width="500">
            <div class="collect-code-container">
                <div class="collect-code-header">
                    <div class="title">扫码支付账单</div>
                    <div class="bill-info">
                        <!-- <div class="info-item">
                            <span class="label">合同号：</span>
                            <span class="value">{{ currentRecord?.bizNo }}</span>
                        </div> -->
                        <div class="info-item">
                            <span class="label">项目名称：</span>
                            <span class="value">{{ currentRecord?.projectName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">承租方：</span>
                            <span class="value">{{ currentRecord?.customerType === 1 ? currentRecord?.customerName + '('
                                +
                                currentRecord?.customerPhone + ')' : currentRecord?.customerName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">租赁资源：</span>
                            <a-tooltip v-if="getRoomDisplayInfo(currentRecord?.roomName).needEllipsis"
                                       :content="currentRecord?.roomName"
                                       position="top">
                                <span class="value room-ellipsis">{{ getRoomDisplayInfo(currentRecord?.roomName).displayText }}</span>
                            </a-tooltip>
                            <span v-else class="value">{{ currentRecord?.roomName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">账单类型：</span>
                            <span class="value">{{ getCostTypeText(currentRecord?.costType) }}</span>
                        </div>
                        <!-- <div class="info-item">
                            <span class="label">支付金额：</span>
                            <span class="value amount">¥{{ formatAmount(currentRecord?.actualReceivable) }}</span>
                        </div> -->
                    </div>
                </div>

                <div class="qrcode-display">
                    <QRCode :value="currentRecord?.paymentUrl || ''" :size="200" :show-placeholder="true"
                        :show-download="!!currentRecord?.paymentUrl" placeholder-text="暂无收款码"
                        @generated="handleQRCodeGenerated" @error="handleQRCodeError" />
                </div>
            </div>
        </a-modal>

        <!-- 账单记账组件 -->
        <BillAccountModal ref="billAccountModalRef" :data="currentBillData" @cancel="handleAccountCancel"
            @save="handleAccountSave" @refresh="handleAccountRefresh" />

        <!-- 记账记录抽屉 -->
        <a-drawer v-model:visible="recordDrawerVisible" title="记账记录" width="1200px" @cancel="handleRecordCancel">
            <account-record />
        </a-drawer>

        <!-- 合同详情抽屉 -->
        <ContractDetailDrawer ref="contractDetailDrawerRef" @submit="handleContractDetailUpdate" />

        <!-- 收据抽屉 -->
        <ReceiptDrawer ref="receiptDrawerRef" :cost-id="currentReceiptCostId" @cancel="handleReceiptCancel" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import dayjs from 'dayjs'
import { Message } from '@arco-design/web-vue'
import FlowDetail from './components/flowDetail.vue'
import QRCode from '@/components/QRCode/index.vue'
import AccountModal from './components/accountModal.vue'
import BillAccountModal from './components/billAccountModal.vue'
import AdvancedSearch from '@/components/advancedSearch/index.vue'
import AccountRecord from './components/accountRecord.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import ContractDetailDrawer from '@/views/contract/contractDetailDrawer.vue'
import ReceiptDrawer from './components/receiptDrawer.vue'
import {
    getCostList,
    getCostSummary,
    exportCostList,
    getCostDetail,
    getCostQrcode,
    cancelCostRecord,
    type CostQueryDTO,
    type CostVo,
    type CostSummaryVo,
    CostStatus,
    CostType,
    ConfirmStatus,
    ContractType
} from '@/api/cost'
import { exportExcel } from '@/utils/exportUtil'
import customer from '@/api/customer'

// 扩展CostVo接口以包含qrcodeUrl和paymentUrl
interface ExtendedCostVo extends CostVo {
    paymentUrl?: string
    qrcodeUrl?: string
    customerPhone?: string
    customerType?: number
}

// 表单模型接口
interface FormModel {
    projectId?: string
    customerName?: string
    roomName?: string
    bizNo?: string
    statusList?: string[]
    confirmStatus?: string
    costType?: string
    dateRange?: any[]
    contractType?: string
    subStatus?: number
}

// 流水详情数据接口
interface FlowDetailData {
    projectName: string
    contractNo: string
    tenantName: string
    billType: string
    billPeriod: string
    dueDate: string
    totalAmount: number
    discountAmount: number
    actualAmount: number
    paidAmount: number
    unpaidAmount: number
    flowList: any[]
    flowLogList: any[]
    costId?: string
}

// 视图类型 全部 非宿舍 宿舍类 多经 日租房
const activeType = ref('')
const typeOptions = [
    { label: '全部', value: '' },
    { label: '非宿舍', value: '0' },
    { label: '宿舍类', value: '1' },
    { label: '多经', value: '2' },
    { label: '日租房', value: '3' }
]
const activeStatus = ref('0')
const statusOptions = [
    { label: '全部', value: '' },
    { label: '待收', value: '0' },
    { label: '待付', value: '1' },
    { label: '已收', value: '2' },
    { label: '已付', value: '3' }
]

// 筛选状态变量
const activeFilter = ref('')

// 处理筛选点击事件
const handleFilterClick = (filter: string) => {
    activeFilter.value = filter
    // 刷新数据
    fetchData()
    fetchSummary()
}

const handleProjectChange = (value: string) => {
    formModel.projectId = value
        // 重置分页并重新查询
        pagination.current = 1
        fetchData()
        fetchSummary()
}

// 统计数据
const summaryData = ref<CostSummaryVo>({})

// 状态切换
const handleChangeStatus = (key: string) => {
    console.log('状态切换:', key)
    fetchData()
    fetchSummary()
}

// 类型切换
const handleChangeType = (key: string) => {
    console.log('类型切换:', key)
    fetchData()
}

// 账单码弹窗控制
const collectCodeVisible = ref(false)
const currentRecord = reactive<ExtendedCostVo>({})

// 项目选项
const projectOptions = ref([
    { id: 1, name: '项目A' },
    { id: 2, name: '项目B' }
])

// 默认日期范围（近3个月）
const defaultDateRange = [
    dayjs().subtract(3, 'month').startOf('day'),
    dayjs().endOf('day')
]

const formModel = reactive<FormModel>({
    projectId: '',
    customerName: '',
    roomName: '',
    bizNo: '',
    statusList: [],
    confirmStatus: undefined,
    costType: undefined,
    dateRange: [...defaultDateRange],
    contractType: undefined
})

const loading = ref(false)
const tableData = ref<CostVo[]>([])

const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
})

const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        slotName: 'index',
        width: 70,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '项目',
        dataIndex: 'projectName',
        width: 140,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '合同号',
        dataIndex: 'bizNo',
        slotName: 'bizNo',
        width: 280,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '承租方名称',
        dataIndex: 'customerName',
        width: 160,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '账单类型',
        dataIndex: 'costType',
        slotName: 'costType',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '账单周期',
        dataIndex: 'billPeriod',
        width: 220,
        render: ({ record }: { record: CostVo }) => {
            if (record.startDate && record.endDate) {
                return `${record.startDate} ~ ${record.endDate}`
            }
            return ''
        }
    },
    {
        title: '应收/付日期',
        dataIndex: 'receivableDate',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '账单状态',
        dataIndex: 'status',
        slotName: 'status',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '账单总额',
        dataIndex: 'totalAmount',
        slotName: 'totalAmount',
        width: 140,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '优惠金额',
        dataIndex: 'discountAmount',
        slotName: 'discountAmount',
        width: 140,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '实际应收/付金额',
        dataIndex: 'actualReceivable',
        slotName: 'actualReceivable',
        width: 140,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '已收/付金额',
        dataIndex: 'receivedAmount',
        slotName: 'receivedAmount',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '待收/付金额',
        dataIndex: 'unpaidAmount',
        slotName: 'unpaidAmount',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '确认状态',
        dataIndex: 'confirmStatus',
        slotName: 'confirmStatus',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '合同类型',
        dataIndex: 'contractType',
        slotName: 'contractType',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '租赁资源',
        dataIndex: 'roomName',
        slotName: 'roomName',
        width: 180,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '合同状态',
        dataIndex: 'contractStatus',
        slotName: 'contractStatus',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '操作',
        dataIndex: 'operations',
        slotName: 'operations',
        width: 380,
        fixed: 'right',
        align: 'center'
    }
]

// 高级搜索控制
const advancedSearchVisible = ref(false)

// 高级搜索
const handleToggle = (visible: boolean) => {
    advancedSearchVisible.value = visible
}

// 记账记录
const handleRecord = (record: CostVo) => {
    console.log('记账记录:', record)
    recordDrawerVisible.value = true
}

// 导出
const handleExport = async () => {
    try {
        const params = buildQueryParams()
        exportExcel(exportCostList, params, '账单列表')
    } catch (error) {
        console.error('导出失败:', error)
    }
}

// 构建查询参数
const buildQueryParams = (): CostQueryDTO => {
    const params: CostQueryDTO = {
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        projectId: formModel.projectId || undefined,
        roomName: formModel.roomName || undefined,
        bizNo: formModel.bizNo || undefined,
        confirmStatus: formModel.confirmStatus !== undefined ? Number(formModel.confirmStatus) as ConfirmStatus : undefined,
        costType: formModel.costType !== undefined ? Number(formModel.costType) as CostType : undefined,
        contractType: activeType.value ? Number(activeType.value) as ContractType : undefined,
        status: activeStatus.value ? Number(activeStatus.value) as CostStatus : undefined,
        subStatus: formModel.subStatus || undefined,  // 新增
    }

    // 根据激活的过滤器添加额外参数
    if (activeFilter.value) {
        switch (activeFilter.value) {
            case 'overdue':
                params.subStatus = 0
                break
            case 'today':
                params.subStatus = 1
                break
            case 'sevenDays':
                params.subStatus = 2
                break
            case 'pendingConfirm':
                params.subStatus = 3
                break
            case 'confirmed':
                params.subStatus = 4
                break
        }
    }

    // 处理账单状态多选
    if (formModel.statusList && formModel.statusList.length > 0) {
        params.statusList = formModel.statusList.join(',')
    }

    // 处理日期范围
    if (formModel.dateRange && formModel.dateRange.length === 2) {
        params.receivableDateStart = dayjs(formModel.dateRange[0]).format('YYYY-MM-DD')
        params.receivableDateEnd = dayjs(formModel.dateRange[1]).format('YYYY-MM-DD')
    }

    return params
}

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 获取账单状态文本
const getStatusText = (status: CostStatus | undefined) => {
    switch (status) {
        case CostStatus.PENDING_RECEIVE: return '待收'
        case CostStatus.PENDING_PAY: return '待付'
        case CostStatus.RECEIVED: return '已收'
        case CostStatus.PAID: return '已付'
        default: return ''
    }
}

// 获取账单类型文本
const getCostTypeText = (costType: CostType | undefined) => {
    switch (costType) {
        case CostType.DEPOSIT: return '保证金'
        case CostType.RENT: return '租金'
        case CostType.OTHER: return '其他费用'
        default: return ''
    }
}

// 获取确认状态文本
const getConfirmStatusText = (confirmStatus: ConfirmStatus | undefined) => {
    switch (confirmStatus) {
        case ConfirmStatus.PENDING: return '待确认'
        case ConfirmStatus.CONFIRMED: return '已确认'
        default: return ''
    }
}

// 获取合同类型文本
const getContractTypeText = (contractType: ContractType | undefined) => {
    switch (contractType) {
        case ContractType.NON_DORMITORY: return '非宿舍'
        case ContractType.DORMITORY: return '宿舍'
        case ContractType.MULTIPLE: return '多经'
        case ContractType.DAILY_RENT: return '日租房'
        default: return ''
    }
}

// 获取合同状态文本
const getContractStatusText = (status: number | undefined) => {
    switch (status) {
        case 10: return '草稿'
        case 20: return '待生效'
        case 30: return '生效中'
        case 40: return '失效'
        case 50: return '作废'
        default: return ''
    }
}

// 合同详情抽屉引用
const contractDetailDrawerRef = ref()

// 收据抽屉相关
const receiptDrawerRef = ref()
const currentReceiptCostId = ref('')

// 查看合同详情
const handleContractDetail = (contractId: string | number) => {
    console.log('查看合同详情:', contractId)
    contractDetailDrawerRef.value?.open({ id: contractId })
}

// 合同详情更新回调
const handleContractDetailUpdate = () => {
    // 合同详情更新后可能需要刷新账单数据
    fetchData()
    fetchSummary()
}

// 查询
const search = () => {
    pagination.current = 1
    fetchData()
    fetchSummary()
}

// 重置
const reset = () => {
    formModel.projectId = ''
    formModel.customerName = ''
    formModel.roomName = ''
    formModel.bizNo = ''
    formModel.statusList = []
    formModel.confirmStatus = undefined
    formModel.costType = undefined
    formModel.dateRange = [...defaultDateRange]
    activeFilter.value = ''
    pagination.current = 1
    fetchData()
    fetchSummary()
}

// 分页
const onPageChange = (current: number) => {
    pagination.current = current
    fetchData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    fetchData()
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = buildQueryParams()
        const response = await getCostList(params)
        tableData.value = response.rows || []
        pagination.total = response.total || 0
    } catch (error) {
        console.error('获取账单列表失败:', error)
    } finally {
        loading.value = false
    }
}

// 获取统计数据
const fetchSummary = async () => {
    summaryData.value = {}
    try {
        const params = buildQueryParams()
        const response = await getCostSummary(params)
        if (response.data) {
            summaryData.value = response.data
        } else {
            summaryData.value = {

            }
        }
    } catch (error) {
        console.error('获取统计数据失败:', error)
    }
}

// 获取项目列表
const getProjects = async () => {
    try {
        // TODO: 调用接口获取项目列表
        // projectOptions.value = await getProjectList()
    } catch (error) {
        console.error('获取项目列表失败:', error)
    }
}

// 流水抽屉控制
const flowDrawerVisible = ref(false)
const currentFlowData = reactive<FlowDetailData>({
    projectName: '',
    contractNo: '',
    tenantName: '',
    billType: '',
    billPeriod: '',
    dueDate: '',
    totalAmount: 0,
    discountAmount: 0,
    actualAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0,
    flowList: [],
    flowLogList: [],
    costId: ''
})

// 查看记账记录（使用记账页面的查看模式）
const handleViewFlow = async (record: CostVo) => {
    try {
        // 清理之前的数据，确保没有污染
        Object.keys(currentBillData).forEach(key => delete currentBillData[key])
        // 打开记账组件，但设置为查看模式
        Object.assign(currentBillData, { id: record.id, viewMode: true })
        billAccountModalRef.value?.open()
    } catch (error) {
        console.error('查看记账记录失败:', error)
        Message.error('查看记账记录失败')
    }
}

// 关闭流水抽屉
const handleFlowCancel = () => {
    flowDrawerVisible.value = false
}

const handleCollectCode = async (record: CostVo) => {
    try {
        // 生成支付链接
        const baseUrl = import.meta.env.VITE_APP_BASE_URL
        const billId = record.bizId
        const amount = record.actualReceivable
        const customerName = encodeURIComponent(record.customerName || '')
        const billType = getCostTypeText(record.costType)

        const paymentLink = `${baseUrl}/bill-payment?id=${billId}&amount=${amount}&customer=${customerName}&type=${billType}`

        // 创建扩展的记录对象
        const extendedRecord: ExtendedCostVo = {
            ...record,
            paymentUrl: paymentLink
        }
        Object.assign(currentRecord, extendedRecord)
        collectCodeVisible.value = true
    } catch (error) {
        console.error('生成账单收款码失败:', error)
        Message.error('生成账单收款码失败')
    }
}

// 关闭账单码弹窗
const handleCollectCancel = () => {
    collectCodeVisible.value = false
    // 清空当前记录
    Object.assign(currentRecord, {})
}

// 处理二维码生成成功
const handleQRCodeGenerated = (dataUrl: string) => {
    console.log('二维码生成成功:', dataUrl)
}

// 处理二维码生成失败
const handleQRCodeError = (error: Error) => {
    Message.error('二维码生成失败: ' + error.message)
    console.error('二维码生成失败:', error)
}

// 账单记账相关
const billAccountModalRef = ref()
const currentBillData = reactive<any>({})

// 点击记账按钮
const handleAccount = async (record: CostVo) => {
    // 清理之前的数据，确保没有污染
    Object.keys(currentBillData).forEach(key => delete currentBillData[key])
    Object.assign(currentBillData, { id: record.id, viewMode: false })
    billAccountModalRef.value?.open()
}

// 关闭账单记账
const handleAccountCancel = () => {
    // 组件内部已经处理了关闭逻辑
}

// 保存账单记账
const handleAccountSave = async (accountData: any[]) => {
    try {
        console.log('保存记账数据:', accountData)
        Message.success('记账保存成功')
        fetchData() // 刷新列表
        fetchSummary()
    } catch (error) {
        console.error('保存记账失败:', error)
        Message.error('保存记账失败')
    }
}

// 账单记账刷新
const handleAccountRefresh = () => {
    fetchData()
    fetchSummary()
}

// 查看记账记录
const handleViewAccountRecord = async (record: CostVo) => {
    try {
        // 清理之前的数据，确保没有污染
        Object.keys(currentBillData).forEach(key => delete currentBillData[key])
        // 打开记账组件，但设置为查看模式
        Object.assign(currentBillData, { id: record.id, viewMode: true })
        billAccountModalRef.value?.open()
    } catch (error) {
        console.error('查看记账记录失败:', error)
        Message.error('查看记账记录失败')
    }
}

// 取消记账弹窗控制
const cancelAccountVisible = ref(false)
const currentCancelRecord = reactive<CostVo>({})

// 点击取消记账按钮
const handleCancelAccount = (record: CostVo) => {
    Object.assign(currentCancelRecord, record)
    cancelAccountVisible.value = true
}

// 确认取消记账
const handleCancelAccountConfirm = async () => {
    try {
        // TODO: 需要获取flowRelId，这里需要根据实际业务逻辑调整
        await cancelCostRecord(currentCancelRecord.id!)
        Message.success('取消记账成功')
        cancelAccountVisible.value = false
        // 刷新数据
        fetchData()
        fetchSummary()
    } catch (error) {
        console.error('取消记账失败:', error)
    }
}

// 取消操作
const handleCancelAccountCancel = () => {
    cancelAccountVisible.value = false
}

const handleUrge = (record: CostVo) => {
    // TODO: 实现催缴逻辑
    console.log('催缴:', record)
}

const handleDiscount = (record: CostVo) => {
    // TODO: 实现减免缓逻辑
    console.log('减免缓:', record)
}

// 记账记录抽屉控制
const recordDrawerVisible = ref(false)

// 处理记账记录抽屉取消
const handleRecordCancel = () => {
    recordDrawerVisible.value = false
}

// 流水详情刷新
const handleFlowRefresh = () => {
    fetchData()
    fetchSummary()
}

// 查看收据
const handleViewRecipt = (record: CostVo) => {
    currentReceiptCostId.value = record.id || ''
    receiptDrawerRef.value?.open()
}

// 收据抽屉取消
const handleReceiptCancel = () => {
    currentReceiptCostId.value = ''
}

// 页面初始化
// onMounted(() => {
//     fetchData()
//     fetchSummary()
// })

const extractFirstTwoValues = (str: string) => {
    console.log('str', str)
    // 按顿号分割字符串
    if (!str) {
        return ''
    }
    const values = str.split('、');
    // 提取前两个值（如果存在）
    const valuesArr = values.slice(0, 3);
    // 合并为字符串
    console.log(valuesArr)
    return valuesArr.join('、');
}

// 处理租赁资源显示逻辑
const getRoomDisplayInfo = (roomName: string | undefined) => {
    if (!roomName) {
        return { displayText: '', needEllipsis: false }
    }

    const rooms = roomName.split('、')
    if (rooms.length <= 1) {
        return { displayText: roomName, needEllipsis: false }
    }

    const displayRooms = rooms.slice(0, 1)
    const displayText = displayRooms.join('、') + '...'
    return { displayText, needEllipsis: true }
}


</script>

<style scoped lang="less">
.container {
    padding: 0 16px 0 16px;

    .content {
        background-color: #fff;
        border-radius: 4px;
    }

    .table-header {
        margin-bottom: 16px;

        .operation-group {
            display: flex;
            gap: 8px;
        }
    }

    .general-card {
        box-sizing: border-box;
        // padding: 16px 0 0 0;
    }

    .active {
        background-color: var(--color-fill-2);
        color: rgb(var(--primary-6));
        border-radius: 32px;
        padding: 5px 16px;
    }
}

.ellipsis-link {
    color: rgb(var(--primary-6));
    cursor: pointer;
    text-decoration: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    display: block;

    &:hover {
        color: rgb(var(--primary-5));
        text-decoration: underline;
    }
}

.collect-code-container {
    // padding: 24px;
    text-align: center;

    .collect-code-header {
        margin-bottom: 24px;

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #1d2129;
        }

        .bill-info {
            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
                padding: 0 0;

                .label {
                    width: 80px;
                    text-align: right;
                    color: #86909c;
                    font-size: 14px;
                    min-width: 80px;
                }

                .value {
                    width: 0;
                    flex: 1;
                    text-align: center;
                    color: #1d2129;
                    font-size: 14px;
                    font-weight: 500;

                    &.amount {
                        color: #f53f3f;
                        font-weight: 600;
                        font-size: 16px;
                    }

                    &.room-ellipsis {
                        cursor: pointer;
                        color: rgb(var(--primary-6));

                        &:hover {
                            color: rgb(var(--primary-5));
                        }
                    }
                }
            }
        }
    }

    .qrcode-display {
        margin-bottom: 24px;
        display: flex;
        justify-content: center;
    }
}
</style>
