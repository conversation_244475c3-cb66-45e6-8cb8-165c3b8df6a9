<template>
    <div class="common-page">
        <div class="approval-form" v-if="approvalType === 'refund'">
            <refundDetailForm ref="refundDetailFormRef" :refundData="refundData" :isEditMode="isEditMode"/>
        </div>
    </div>
</template>

<script setup lang="ts">
import refundDetailForm from '@/views/financeManage/components/refundDetailForm.vue'
import { useContractStore } from '@/store/modules/contract/index';
import { getContractById, ContractAddDTO, ContractVo, getContractDetailBill } from '@/api/contract';
import customerApi from '@/api/customer'
import { Message } from '@arco-design/web-vue';
import { getFinancialRefundDetail, FinancialRefundVo } from '@/api/refundManage';
import { getToken, setToken, clearToken } from '@/utils/auth';

const route = useRoute()

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

// 退款数据
const refundData = reactive<FinancialRefundVo>({
    id: '',
    projectName: '',
    refundType: 0,
    bizId: '',
    refundTarget: '',
    applyTime: '',
    refundAmount: 0,
    feeType: '',
    refundWay: 0,
    receiverName: '',
    receiverBank: '',
    receiverAccount: '',
    refundRemark: '',
    refundTime: '',
    refundStatus: 0,
    approveStatus: 0,
    attachments: ''
})
const isEditMode = ref(false)
const attachmentList = ref<any[]>([])
const settlementDetailData = ref<any[]>([])
const settlementInfo = ref<any>({})
const orderInfo = ref<any>({})
const flowInfo = ref<any>({})
const refundFlowData = ref<any[]>([])
const refundDetailFormRef = ref<any>(null)

// 获取合同详情
const fetchContractDetail = async (id?: string) => {
    try {
        const { data } = await getContractById(id || contractData.value.id as string);
        if (data) {
            // 更新合同数据
            const contractInfo = data as ContractVo;

            contractInfo.fees = contractInfo.fees || []
            contractInfo.costs = contractInfo.costs || []
            contractInfo.rooms = contractInfo.rooms || []
            contractInfo.rentReceivableDate = Number(contractInfo.rentReceivableDate)

            contractStore.contractDetail = { ...contractStore.contractDetail, ...contractInfo as any };
            fetchBill()
            fetchLesseeInfo()
        }
    } catch (error) {
        console.error(error);
    }
};

// 获取账单
const fetchBill = async () => {
    const { data } = await getContractDetailBill(contractData.value.id as string)
    contractStore.contractDetailCosts = data
}

// 获取承租人信息
const fetchLesseeInfo = async () => {
    const { data } = await customerApi.getCustomerDetail(contractData.value.customer.customerId as string)
    contractStore.contractLesseeInfo = data
}


const isLoaded = ref(false)
const approvalType = ref('')
onMounted(() => {
    // http://localhost:5551/approval/index?type=refund&id=891f29acbc59a9a48da48e040b93c54c&token=1
    // contractStore.resetContractDetail()
    // contractStore.isApproval = true
    isLoaded.value = true
    approvalType.value = route.query.type as string
    if (route.query.token) {
        setToken(route.query.token as string)
    }
    // if (route.query.refundId) {
        // loadRefundDetail(route.query.id as string)
        // fetchContractDetail(route.query.id as string)
        nextTick(() => {
            setTimeout(() => {
                if (approvalType.value === 'refund') {
                    refundDetailFormRef.value?.open(route.query.refundId as string, 'view', {})
                }
            }, 333)
        })
    // } else {
    //     // Message.error('合同ID不存在')
    // }
})
</script>

<style scoped lang="less">
.common-page {
    height: 100vh;
    overflow: hidden;
    padding: 0;
}
.approval-form {
    width: 100%;
    box-sizing: border-box;
    padding: 16px;
}
</style>