import http from './index';

// 收款票据相关接口类型定义

// 查询收款票据列表请求参数
export interface ReceiptQueryDTO {
  costFlowRelIds?: string[]; // 账单流水关联id列表
  costId?: string; // 账单id
}

// 收款票据详情请求参数
export interface ReceiptDetailDTO {
  id: string; // 收款票据ID
}

// 生成收据请求参数
export interface ReceiptCreateDTO {
  costFlowRelId?: string; // 账单流水关联表id
}

// 删除收据请求参数
export interface ReceiptDeleteDTO {
  costFlowRelId: string; // 账单流水关联表id
}

// 收款票据基础信息
export interface ReceiptVo {
  id?: string;
  costFlowRelId?: string;
  costId?: string;
  receiptNo?: string; // 票据编号
  receiptAmount?: number; // 票据金额
  receiptDate?: string; // 票据日期
  receiptStatus?: number; // 票据状态
  customerName?: string; // 客户名称
  projectName?: string; // 项目名称
  roomName?: string; // 房间名称
  costType?: number; // 账单类型: 1-保证金,2-租金,3-其他费用
  createBy?: string;
  createByName?: string;
  createTime?: string;
  updateBy?: string;
  updateByName?: string;
  updateTime?: string;
}

// 通用响应结果
export interface AjaxResult<T = any> {
  error?: boolean;
  success?: boolean;
  warn?: boolean;
  empty?: boolean;
  code?: number;
  msg?: string;
  data?: T;
  [key: string]: any;
}

// 分页响应结果
export interface TableDataInfo<T = any> {
  total?: number;
  rows?: T[];
  code?: number;
  msg?: string;
}

// 票据状态枚举
export enum ReceiptStatus {
  DRAFT = 0,      // 草稿
  GENERATED = 1,  // 已生成
  PRINTED = 2,    // 已打印
  CANCELLED = 3   // 已取消
}

// 查询收款票据列表
export const getReceiptList = (params: ReceiptQueryDTO) => {
  return http.get<TableDataInfo<ReceiptVo>>('/business-rent-admin/receipt/list', params);
};

// 获取收款票据详细信息
export const getReceiptDetail = (params: ReceiptDetailDTO) => {
  return http.get<AjaxResult<ReceiptVo>>('/business-rent-admin/receipt/detail', params);
};

// 定时任务生成收据
export const createReceipt = (params: ReceiptCreateDTO) => {
  return http.get<AjaxResult>('/business-rent-admin/receipt/create', params);
};

// 删除收据
export const deleteReceipt = (params: ReceiptDeleteDTO) => {
  return http.get<AjaxResult>('/business-rent-admin/receipt/delete', params);
};

// 默认导出（保持兼容性）
export default {
  getReceiptList,
  getReceiptDetail,
  createReceipt,
  deleteReceipt
};
