<template>
    <section>
        <sectionTitle title="合同基本信息">
            <template #right>
                <div class="detail-text">{{ contractData.contractMode === 0 ? '标准合同' : '非标合同' }}</div>
            </template>
        </sectionTitle>
        <div class="content">
            <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">合同号</label>
                        <div class="detail-text">{{ contractData.contractNo || '--' }}</div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">纸质合同编号</label>
                        <div class="detail-text">{{ contractData.paperContractNo || '--' }}</div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">签订日期</label>
                        <div class="detail-text">{{ contractData.signDate || '--' }}</div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">交房日期</label>
                        <div class="detail-text">{{ contractData.handoverDate || '--' }}</div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">{{ contractData.contractType === 2 ? '多经合同用途' : '合同用途' }}</label>
                        <div class="detail-text">{{ contractPurposeLabel }}</div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">成交渠道</label>
                        <div class="detail-text">
                            {{ contractData.dealChannel === 0 ? '中介推荐' : contractData.dealChannel === 1 ? '自拓客户'
                                : contractData.dealChannel === 2 ? '招商代理' : contractData.dealChannel === 3 ? '全员招商'
                                    : '--' }}
                        </div>
                    </div>
                </a-grid-item>
                <a-grid-item v-if="contractData.dealChannel === 3">
                    <div class="form-item">
                        <label class="form-label">协助人</label>
                        <div class="detail-text">{{ contractData.assistantName || '--' }}</div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">开始日期</label>
                        <div class="detail-text">{{ contractData.startDate || '--' }}</div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">租赁日期</label>
                        <div class="detail-text">
                            {{ contractData.rentYear || 0 }}年 {{ contractData.rentMonth || 0 }}月 {{
                                contractData.rentDay || 0 }}天
                        </div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">结束日期</label>
                        <div class="detail-text">{{ contractData.endDate || '--' }}</div>
                    </div>
                </a-grid-item>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract/index'
import { getDictLabel } from '@/dict'
import { ContractAddDTO } from '@/api/contract'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

const contractPurposeLabel = computed(() => {
    return getDictLabel('diversification_purpose', Number(contractData.value.contractPurpose))
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.form-item {
    margin-bottom: 16px;

    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>