# 智能水电批量绑定组件调整

## 概述
根据现有接口情况，对 `WaterElectricBatchBindDrawer.vue` 组件进行了调整，使其适配智能水电表的批量绑定功能。

## 主要调整内容

### 1. 接口替换
- **原接口**: 使用智能门锁相关接口 (`smartLock.ts`)
- **新接口**: 使用智能水电相关接口 (`smartWaterElectricity.ts`)
  - `getWaterElectricityRoomList` - 查询房源信息列表
  - `exportWaterElectricityList` - 导出房源列表
  - `importWaterElectricityRoomList` - 导入绑定关系
  - `saveWaterElectricityBindRelation` - 保存关联关系

### 2. 数据结构调整
- **房源类型**: `RoomSimpleVo` → `RoomSimpleForWaterVo`
- **绑定字段**: 
  - `ttlockDeviceId/ttlockId/ttlockName` → `opsSysRoomId/opsSysRoomName`
- **全局绑定映射**: 调整为运营房间绑定结构

### 3. 功能模拟
由于部分接口暂未提供，采用模拟数据方式：

#### 运营项目列表（模拟）
```javascript
const opsProjectList = [
    { id: '1', name: '运营项目A' },
    { id: '2', name: '运营项目B' },
    { id: '3', name: '运营项目C' }
]
```

#### 运营房间列表（模拟）
```javascript
const mockOpsRooms = [
    { id: 'ops_room_1', name: '运营房间001', roomCode: 'OPS001' },
    { id: 'ops_room_2', name: '运营房间002', roomCode: 'OPS002' },
    // ...更多模拟数据
]
```

### 4. 暂无接口功能
对于没有对应接口的功能，显示提示信息：
- **同步运营房间数据**: `handleSyncOpsRooms()` - 显示"暂无接口"提示
- **导出运营房间**: `handleExportOpsRooms()` - 显示"暂无接口"提示

### 5. 保留的核心逻辑
- 房源列表查询和分页
- 绑定关系的跨分页保持
- 导出房源功能
- 导入绑定关系功能
- 保存关联关系功能
- 地块和楼栋筛选

## 组件使用方式

### 调用方法
```javascript
// 在父组件中
const drawerRef = ref()

const openDrawer = () => {
    drawerRef.value?.show({
        projectId: 'project_id',
        projectName: '项目名称'
    })
}
```

### 事件监听
```javascript
// 成功回调
const handleSuccess = () => {
    console.log('绑定成功')
    // 刷新列表等操作
}

// 取消回调
const handleCancel = () => {
    console.log('取消操作')
}
```

## 技术要点

### 1. 响应式数据管理
- 使用 `globalBindingMap` 维护跨分页的绑定关系
- 分页切换时保持已选择的绑定状态

### 2. 模拟数据处理
- 运营项目和运营房间使用本地模拟数据
- 支持分页展示模拟数据

### 3. 接口适配
- 查询参数适配 `WaterElectricityQueryDTO` 类型
- 响应数据适配 `RoomSimpleForWaterVo` 类型

## 待完善功能
1. 运营项目接口对接（待后端提供）
2. 运营房间接口对接（待后端提供）
3. 同步运营房间数据接口（待后端提供）
4. 导出运营房间接口（待后端提供）

## 注意事项
1. 组件已移除智能门锁相关的品牌账号管理功能
2. 绑定逻辑从设备绑定调整为运营房间绑定
3. 保存时验证至少绑定一个运营房间
4. 模拟数据仅用于前端展示，实际使用需要对接真实接口
