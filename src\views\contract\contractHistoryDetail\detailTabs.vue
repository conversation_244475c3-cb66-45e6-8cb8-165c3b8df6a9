<template>
    <div class="contract-detail-tabs">
        <a-tabs v-model:activeKey="activeTab" type="capsule" size="large">
            <a-tab-pane key="1" :title="'基本信息'">
                <tab-base-info />
            </a-tab-pane>
            <a-tab-pane key="2" :title="'承租人信息'">
                <tab-lessee-info />
            </a-tab-pane>
            <a-tab-pane key="3" :title="'账单信息'">
                <tab-bill />
            </a-tab-pane>
            <a-tab-pane key="4" :title="'流水信息'">
                <tab-flow />
            </a-tab-pane>
            <!-- <a-tab-pane key="5" :title="'签署详情'">
            </a-tab-pane> -->
            <a-tab-pane key="6" :title="'变更'"
                v-if="(contractData.status as number) >= 30 && (contractData.approveStatus as number) === 2">
                <tab-change />
            </a-tab-pane>
            <!-- <a-tab-pane key="7" :title="'进场&退租出场信息'">
            </a-tab-pane>
            <a-tab-pane key="8" :title="'临时收费'">
            </a-tab-pane>
            <a-tab-pane key="9" :title="'减免缓'">
            </a-tab-pane> -->
            <a-tab-pane key="10" :title="'修改记录'">
                <tab-change-record />
            </a-tab-pane>
            <!-- <a-tab-pane key="11" :title="'补充协议'">
            </a-tab-pane> -->
            <a-tab-pane key="12" :title="'附件'"
                v-if="(contractData.status as number) >= 30 && (contractData.approveStatus as number) === 2">
                <tab-attachments />
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import tabBaseInfo from './tabs/tabBaseInfo/index.vue'
import tabLesseeInfo from './tabs/tabLesseeInfo/index.vue'
import tabBill from './tabs/tabBill/index.vue'
import tabAttachments from './tabs/tabAttachments/index.vue'
import tabChange from './tabs/tabChange/index.vue'
import tabChangeRecord from '../contractDetail/tabs/tabChangeRecord/index.vue'
import tabFlow from '../contractDetail/tabs/tabFlow/index.vue'
import { useContractStore } from '@/store/modules/contract/index'
import { ContractAddDTO } from '@/api/contract'

const activeTab = ref('1')
const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

</script>

<style scoped lang="less">
.contract-detail-tabs {
    flex: 1;
    height: 100%;
    overflow: hidden;

    :deep(.arco-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;

        .arco-tabs-nav {
            flex-shrink: 0;
            margin-bottom: 16px;

            .arco-tabs-nav-tab {
                justify-content: flex-start;
                background-color: #F0F4FB;

                .arco-tabs-tab {
                    font-weight: 600;

                    &.arco-tabs-tab-active {
                        background-color: rgb(var(--arcoblue-6));
                        color: #FFFFFF;
                        border-radius: 4px;
                    }

                    &:hover:not(.arco-tabs-tab-active) {
                        background-color: rgba(var(--arcoblue-6), 0.1);
                        color: rgba(0, 0, 0, 0.9);
                        border-radius: 4px;
                    }
                }
            }

            .arco-tabs-nav-tab-list {
                padding: 6px;
            }

            .arco-tabs-tab::before {
                display: none;
            }
        }

        .arco-tabs-content {
            flex: 1;
            overflow-y: auto;
            padding-top: 0;

            .arco-tabs-content-list {
                height: 100%;

                .arco-tabs-pane {
                    height: 100%;
                    overflow-y: auto;
                    overflow-x: hidden;
                }
            }
        }
    }
}
</style>