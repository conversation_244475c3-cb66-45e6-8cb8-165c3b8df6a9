# 批量绑定门锁功能实现

## 功能概述

实现了智能设备管理页面的批量绑定门锁功能，包含设备品牌管理、房源列表、设备信息列表、手动绑定、自动绑定等完整功能。

## 文件结构

```
src/views/smartDevices/
├── index.vue                          # 主页面
├── components/
│   ├── BatchBindDrawer.vue            # 批量绑定抽屉组件
│   ├── AddAccountModal.vue            # 添加账号弹窗组件
│   ├── DoorLockTab.vue               # 智能门锁列表组件
│   └── WaterElectricTab.vue          # 智能水电列表组件
```

## 核心功能实现

### 1. 主页面 (index.vue) 修改

#### 添加批量绑定入口
```typescript
// 批量绑定设备
const handleBindDevice = () => {
  if (!filterForm.projectId) {
    Message.warning('请先选择项目')
    return
  }
  
  if (activeTab.value !== 'door-lock') {
    Message.warning('批量绑定功能仅支持智能门锁')
    return
  }
  
  batchBindVisible.value = true
}
```

#### 添加抽屉组件
```vue
<BatchBindDrawer
  v-model:visible="batchBindVisible"
  :project-id="filterForm.projectId"
  :project-name="currentProject.projectName"
  @success="handleBatchBindSuccess"
/>
```

### 2. 批量绑定抽屉 (BatchBindDrawer.vue)

#### 主要功能模块

**设备品牌选择**
- 支持多个门锁品牌（通通锁、芯华等）
- 可添加新的授权账号
- 品牌切换时自动刷新设备列表

**房源列表管理**
- 展示当前项目宿舍类型的生效房源
- 默认显示未绑定门锁的数据
- 支持按绑定状态、地块、楼栋、房源名称筛选
- 分页显示，支持手动选择设备

**设备信息列表**
- 展示选定品牌的未绑定智能门锁设备
- 支持按设备名称筛选
- 分页显示，支持选择绑定到房源

**绑定方式**
1. **手动绑定**: 点击房源的设备ID输入框，然后在设备列表中选择设备
2. **自动绑定**: 根据房源名称自动匹配设备（要求设备名称与房源名称一致）
3. **导入绑定**: 支持导出房源模板，填写设备ID后导入

#### 核心方法实现

**设备选择逻辑**
```typescript
const handleSelectDevice = (record: any, rowIndex: number) => {
    currentSelectingRoom.value = record
    currentSelectingIndex.value = rowIndex
    Message.info('请在右侧设备列表中选择一个设备')
}

const handleSelectDeviceFromList = (device: any) => {
    if (!currentSelectingRoom.value) {
        Message.warning('请先选择要绑定的房源')
        return
    }
    
    // 填充设备信息到房源列表
    currentSelectingRoom.value.deviceId = device.lockId
    currentSelectingRoom.value.deviceName = device.lockName
    
    // 清除选择状态
    currentSelectingRoom.value = null
    currentSelectingIndex.value = -1
    
    Message.success('设备选择成功')
}
```

**自动匹配功能**
```typescript
const handleAutoMatch = async () => {
    try {
        await autoBindRoomDevice(props.projectId)
        Message.success('自动匹配完成')
        fetchRoomList()
    } catch (error) {
        console.error('自动匹配失败:', error)
    }
}
```

**保存绑定关系**
```typescript
const handleSave = async () => {
    const bindingData = roomList.value.filter(room => room.deviceId && room.deviceName)
    
    if (bindingData.length === 0) {
        Message.warning('请至少绑定一个设备')
        return
    }
    
    try {
        const data = bindingData.map(room => ({
            roomId: room.roomId,
            roomName: room.roomName,
            ttlockDeviceId: room.deviceId,
            ttlockName: room.deviceName
        }))
        
        await bindRoomDevice(data)
        Message.success('绑定成功')
        emit('success')
        handleCancel()
    } catch (error) {
        console.error('绑定失败:', error)
    }
}
```

### 3. 添加账号弹窗 (AddAccountModal.vue)

#### 功能特点
- 支持选择设备品牌（通通锁、芯华等）
- 录入管理员账号和密码
- 表单验证确保数据完整性
- 与项目关联，支持多项目管理

#### 表单验证
```typescript
const rules = {
    brandType: [
        { required: true, message: '请选择品牌' }
    ],
    username: [
        { required: true, message: '请输入登录账号' },
        { minLength: 2, message: '账号长度不能少于2位' }
    ],
    password: [
        { required: true, message: '请输入账号密码' },
        { minLength: 6, message: '密码长度不能少于6位' }
    ]
}
```

## API接口集成

### 使用的API接口
- `getRoomList`: 获取房源列表
- `getTTLockDeviceList`: 获取智能门锁设备列表
- `bindRoomDevice`: 批量绑定房源和设备
- `autoBindRoomDevice`: 自动匹配绑定
- `syncDeviceData`: 同步设备数据
- `addTTLockAccount`: 添加门锁账号

### 数据流转
1. 选择项目 → 加载房源列表和设备列表
2. 选择品牌 → 刷新对应品牌的设备列表
3. 手动绑定 → 更新房源的设备信息
4. 保存绑定 → 调用API批量保存绑定关系

## 用户交互流程

### 1. 打开绑定界面
- 点击"批量绑定设备"按钮
- 验证项目已选择且当前为门锁tab
- 打开批量绑定抽屉

### 2. 设备品牌管理
- 选择设备品牌（通通锁、芯华等）
- 可点击"+绑定账号"添加新的管理员账号
- 品牌切换时自动加载对应设备

### 3. 房源和设备绑定
- 左侧显示房源列表，可筛选查询
- 右侧显示设备列表，可搜索设备
- 手动绑定：点击房源设备ID框 → 选择右侧设备
- 自动绑定：点击"自动匹配"按钮

### 4. 数据管理
- 支持导出房源模板
- 支持导入绑定关系
- 支持同步最新设备数据
- 支持导出设备信息

### 5. 保存绑定
- 验证至少有一个绑定关系
- 批量保存所有绑定关系
- 成功后刷新主列表数据

## 技术特点

### 1. 组件化设计
- 抽屉组件独立封装，可复用
- 弹窗组件模块化，职责单一
- 父子组件通信清晰

### 2. 状态管理
- 使用 ref 和 reactive 管理组件状态
- 合理的数据流转和状态同步
- 及时清理临时状态

### 3. 用户体验
- 清晰的操作提示和反馈
- 合理的加载状态显示
- 友好的错误处理

### 4. 数据验证
- 前端表单验证
- 业务逻辑验证
- API调用错误处理

## 扩展性

### 1. 多品牌支持
- 品牌配置化，易于添加新品牌
- 统一的API接口，支持不同品牌设备

### 2. 功能扩展
- 预留导入导出功能接口
- 支持批量操作扩展
- 可扩展到其他智能设备类型

### 3. 界面适配
- 响应式布局设计
- 支持不同屏幕尺寸
- 组件样式可定制

这个实现完整覆盖了您提到的所有功能需求，提供了完整的批量绑定门锁解决方案。
