<template>
    <div class="exit-settlement-form">
        <!-- 费用结算信息 -->
        <div class="section-header">
            <section-title title="费用结算" />
            <a-space v-if="!readonly">
                <a-button type="primary" size="small" @click="handleAddFeeItem">
                    添加费项
                </a-button>
            </a-space>
        </div>
        
        <a-table 
            :data="feeItems" 
            :columns="feeColumns" 
            :pagination="false" 
            :bordered="{ cell: true }"
            :scroll="{ x: 1 }"
            row-key="id"
        >
            <template #payType="{ record }">
                <a-select 
                    v-if="record.type === 2"
                    v-model="record.payType" 
                    :disabled="record.type === 2 || readonly"
                >
                    <a-option :value="1">收</a-option>
                    <a-option :value="2">支</a-option>
                </a-select>
                <span v-else>{{ ['', '收', '支'][record.payType] }}</span>
            </template>
            <template #subjectName="{ record }">
                <a-select 
                    v-if="record.type === 2" 
                    v-model="record.subjectId" 
                    placeholder="请选择费用类型"
                    :disabled="readonly"
                    @change="(value: string) => handleSubjectChange(record, value)"
                    style="width: 100%"
                >
                    <a-option v-for="subject in feeSubjects" :key="subject.id" :value="subject.id">
                        {{ subject.name }}
                    </a-option>
                </a-select>
                <span v-else>{{ record.subjectName }}</span>
            </template>
            <template #amount="{ record }">
                <a-input-number 
                    v-model="record.amount" 
                    placeholder="0" 
                    :min="0" 
                    :precision="2"
                    :step="0.01"
                    :disabled="record.type !== 2 || readonly"
                />
            </template>
            <template #startDate="{ record }">
                <a-range-picker 
                    v-if="record.type === 2"
                    :model-value="[record.startDate, record.endDate]"
                    @change="(dates: [string, string] | null) => handleDateRangeChange(record, dates)"
                    placeholder="选择费用周期"
                    :disabled="readonly"
                    style="width: 100%"
                />
                <span v-else>{{ formatDateRange(record.startDate, record.endDate) }}</span>
            </template>
            <template #remark="{ record }">
                <a-input 
                    v-model="record.remark" 
                    placeholder="请输入费用说明"
                    :disabled="readonly"
                />
            </template>
            <template #operations="{ record }">
                <!-- 
                费用项类型说明：
                type = 0: 接口返回的系统费用（如租金、保证金等），不可修改删除
                type = 1: 系统计算生成的费用（如房间配套损坏罚没金），不可删除，金额由系统自动更新
                type = 2: 用户手动添加的费用，可以修改和删除
                -->
                <a-button 
                    v-if="(record.type === 2) && !readonly" 
                    type="text" 
                    size="mini" 
                    status="danger"
                    @click="removeFeeItem(record)"
                >
                    移除
                </a-button>
                <span v-else-if="record.type === 1" style="color: #86909c; font-size: 12px;">系统生成</span>
                <span v-else style="color: #86909c; font-size: 12px;">系统费用</span>
            </template>
        </a-table>

        <!-- 费用汇总 -->
        <div class="fee-summary">
            <div class="total">
                费用合计：<span class="amount">{{ formatAmount(totalFeeAmount) }}</span>元{{ feeDescription }}
            </div>
            <div class="reduction">
                <a-checkbox v-model="formData.isDiscount" :disabled="readonly">是否减免</a-checkbox>
                <a-space v-if="formData.isDiscount">
                    <a-input-number 
                        v-model="formData.discountAmount" 
                        placeholder="减免金额" 
                        :min="0" 
                        :precision="2"
                        :step="0.01"
                        :disabled="readonly"
                    />元
                    <a-input v-model="formData.discountReason" placeholder="减免原因" style="width: 200px" :disabled="readonly" />
                </a-space>
            </div>
            <div class="final-amount">
                最终费用金额：<span class="amount">{{ formatAmount(finalFeeAmount) }}</span>元{{ finalFeeDescription }}
            </div>
        </div>

        <!-- 承租方收款信息 -->
        <section-title title="承租方收款信息" style="margin: 16px 0 16px 0;" v-if="finalFeeAmount < 0"/>
        <a-form :model="formData" layout="horizontal" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" label-align="right" v-if="finalFeeAmount < 0">
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="收款人" required>
                        <a-input v-model="formData.payeeName" placeholder="请输入收款人姓名" :disabled="readonly" />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="收款账号" required>
                        <a-input v-model="formData.payeeAccount" placeholder="请输入收款账号" :disabled="readonly" />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="开户银行" required>
                        <a-input v-model="formData.bankName" placeholder="请输入开户银行" :disabled="readonly" />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>

        <!-- 手续办理情况 -->
        <section-title title="手续办理情况" style="margin-bottom: 16px;" />
        <a-form :model="formData" layout="horizontal" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" label-align="right">
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="营业执照">
                        <a-select v-model="formData.licenseStatus" placeholder="请选择" :disabled="readonly">
                            <a-option :value="1">未办理执照</a-option>
                            <a-option :value="2">需注销</a-option>
                            <a-option :value="3">已注销</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="税务登记证">
                        <a-select v-model="formData.taxCertStatus" placeholder="请选择" :disabled="readonly">
                            <a-option :value="1">未办理执照</a-option>
                            <a-option :value="2">需注销</a-option>
                            <a-option :value="3">已注销</a-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>

        <!-- 退款处理方式 -->
        <!-- <section-title title="退款处理方式" style="margin-bottom: 16px;" />
        <div class="settlement-options">
            <a-radio-group v-model="formData.refundProcessType">
                <a-radio :value="1">退款</a-radio>
                <a-radio :value="2">暂存客户账户</a-radio>
            </a-radio-group>
        </div> -->

        <!-- 退款申请方式 -->
        <template v-if="finalFeeAmount < 0">
            <section-title title="退款" style="margin: 16px 0 16px 0;" />
            <a-form :model="formData" layout="horizontal" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" label-align="right">
                <a-row :gutter="16">
                    <a-col :span="8">
                        <a-form-item label="退款处理方式">
                            <a-select v-model="formData.refundProcessType" placeholder="请选择" :disabled="readonly">
                                <a-option :value="1">退款</a-option>
                                <a-option :value="2">暂存客户账户</a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="退款申请方式" v-if="formData.refundProcessType === 1">
                            <a-select v-model="formData.refundApplyType" placeholder="请选择" :disabled="readonly">
                                <a-option 
                                    v-for="option in refundApplyOptions" 
                                    :key="option.value" 
                                    :value="option.value"
                                    :disabled="option.disabled"
                                >
                                    {{ option.label }}
                                </a-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </template>

        <!-- 按钮组 -->
        <!-- <div class="form-actions">
            <a-space>
                <a-button type="primary" @click="handleSave(true)">提交结算</a-button>
                <a-button @click="handleSave(false)">暂存</a-button>
            </a-space>
        </div> -->
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import type { ExitInfo, ExitCost, ExitAddDTO } from '@/types/exit'
import sectionTitle from '@/components/sectionTitle/index.vue'

// Props定义
const props = defineProps<{
    exitInfo?: ExitInfo
    costList: ExitCost[]
    exitRoomList?: any[] // 新增：房间列表，用于计算罚没金额
    readonly?: boolean // 是否只读模式
}>()

const emit = defineEmits(['save'])

// 表单数据
const formData = reactive<ExitAddDTO>({
    id: '',
    projectId: '',
    contractId: '',
    contractNo: '',
    contractUnionId: '',
    terminateId: '',
    refundId: '',
    customerId: '',
    customerName: '',
    processType: 1,
    progressStatus: 1,
    isDiscount: false,
    discountAmount: 0,
    discountReason: '',
    finalAmount: 0,
    refundProcessType: 1,
    payeeName: '',
    payeeAccount: '',
    bankName: '',
    licenseStatus: 1,
    taxCertStatus: 1,
    refundApplyType: 1,
    signType: 1,
    signAttachments: '',
    exitCostList: [],
    isSubmit: false
})

// 费用项列表
const feeItems = ref<ExitCost[]>([])

/**
 * 	费用类型名称	税率
10	定金	
20	保证金	
30	租金	9%
40	罚没金	6%
50	代收水电	*/ 
// 费用科目列表
const feeSubjects = ref([
    { id: '10', name: '定金' },
    { id: '20', name: '保证金' },
    { id: '30', name: '租金' },
    { id: '40', name: '罚没金' },
    { id: '50', name: '代收水电' }
    // { id: '1', name: '房租' },
    // { id: '2', name: '水费' },
    // { id: '3', name: '电费' },
    // { id: '4', name: '物业费' },
    // { id: '5', name: '垃圾费' },
    // { id: '6', name: '网络费' },
    // { id: '7', name: '停车费' },
    // { id: '8', name: '押金' },
    // { id: '9', name: '违约金' },
    // { id: '10', name: '维修费' },
    // { id: '11', name: '清洁费' },
    // { id: '12', name: '其他费用' }
])

// 费用表格列定义
const feeColumns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 70,
        render: ({ rowIndex }: any) => rowIndex + 1
    },
    {
        title: '收支类型',
        slotName: 'payType',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '费用类型',
        slotName: 'subjectName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '金额(元)',
        slotName: 'amount',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '费用周期',
        slotName: 'startDate',
        width: 260,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    // {
    //     title: '费用周期',
    //     slotName: 'endDate',
    //     width: 120,
    //     align: 'center',
    //     ellipsis: true,
    //     tooltip: true
    // },
    // {
    //     title: '应收日期',
    //     slotName: 'receivableDate',
    //     width: 120,
    //     align: 'center',
    //     ellipsis: true,
    //     tooltip: true
    // },
    {
        title: '费用说明',
        slotName: 'remark',
        width: 180,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 100,
        fixed: 'right',
        align: 'center',
        ellipsis: true,
        tooltip: true
    }
]

// 计算总费用
const totalFeeAmount = computed(() => {
    const total = feeItems.value.reduce((sum, item) => {
        const amount = parseFloat(String(item.amount || 0))
        if (item.payType === 1) { // 收
            return sum + amount
        } else { // 支
            return sum - amount
        }
    }, 0)
    // 保留2位小数并转为数字
    return Math.round(total * 100) / 100
})

// 费用描述
const feeDescription = computed(() => {
    return totalFeeAmount.value >= 0 ? '(应收)' : '(应退)'
})

// 最终费用金额
const finalFeeAmount = computed(() => {
    let amount = totalFeeAmount.value
    if (formData.isDiscount && formData.discountAmount) {
        const discountAmount = parseFloat(String(formData.discountAmount || 0))
        amount -= discountAmount
    }
    // 保留2位小数并转为数字
    return Math.round(amount * 100) / 100
})

// 最终费用描述
const finalFeeDescription = computed(() => {
    return finalFeeAmount.value >= 0 ? '(应收)' : '(应退)'
})

// 是否需要注销（营业执照或税务登记证任何一个为需注销）
const isNeedCancel = computed(() => {
    return formData.licenseStatus === 2 || formData.taxCertStatus === 2
})

// 退款申请方式选项
const refundApplyOptions = computed(() => {
    const options = [
        { value: 1, label: '只结算，暂不退款' },
        { value: 2, label: '结算并申请退款', disabled: isNeedCancel.value }
    ]
    return options
})

// 强制更新罚没金额的事件处理器
const handleForceUpdatePenalty = (event: CustomEvent) => {
    console.log('收到强制更新罚没金额事件:', event.detail)
    // 使用事件中的最新数据进行计算
    if (event.detail && event.detail.exitRoomList) {
        updatePenaltyAmountWithData(event.detail.exitRoomList)
    } else {
        updatePenaltyAmount()
    }
}

// 生命周期
onMounted(() => {
    initFormData()
    // 监听强制更新事件
    window.addEventListener('forceUpdatePenalty', handleForceUpdatePenalty as EventListener)
})

onUnmounted(() => {
    // 清理事件监听器
    window.removeEventListener('forceUpdatePenalty', handleForceUpdatePenalty as EventListener)
})

// 监听props变化
watch(() => props.exitInfo, () => {
    initFormData()
}, { deep: true })

watch(() => props.costList, () => {
    initCostList()
}, { deep: true })

watch(() => props.exitRoomList, (newRoomList, oldRoomList) => {
    console.log('费用结算表单检测到房间列表变化')
    console.log('新房间列表:', newRoomList)
    console.log('旧房间列表:', oldRoomList)
    // 房间列表变化时重新计算罚没金额
    updatePenaltyAmount()
}, { deep: true, immediate: false })

// 专门监听房间赔偿金相关字段的变化
watch(() => {
    if (!props.exitRoomList) return []
    return props.exitRoomList.map(room => ({
        id: room.id,
        doorWindowPenalty: room.doorWindowPenalty,
        keyPenalty: room.keyPenalty,
        cleaningPenalty: room.cleaningPenalty,
        assetsCount: room.exitRoomAssetsList?.length || 0,
        assetsPenalties: room.exitRoomAssetsList?.map((asset: any) => asset.penalty) || []
    }))
}, (newPenalties, oldPenalties) => {
    console.log('检测到房间赔偿金字段变化')
    console.log('新赔偿金数据:', newPenalties)
    console.log('旧赔偿金数据:', oldPenalties)
    // 赔偿金相关字段变化时重新计算罚没金额
    updatePenaltyAmount()
}, { deep: true, immediate: false })

// 监听营业执照和税务登记证状态变化
watch([() => formData.licenseStatus, () => formData.taxCertStatus], () => {
    // 如果任何一个为需注销，自动设置为只结算，暂不退款
    if (isNeedCancel.value && formData.refundApplyType === 2) {
        formData.refundApplyType = 1
        Message.info('营业执照或税务登记证需要注销，退款申请方式已自动调整为"只结算，暂不退款"')
    }
})

// 初始化表单数据
const initFormData = () => {
    console.log('==== initFormData 被调用 ====', props.exitInfo)
    if (props.exitInfo) {
        Object.assign(formData, props.exitInfo)
    }
    initCostList()
}

// 计算罚没金额
const calculatePenaltyAmount = (roomList?: any[]) => {
    const targetRoomList = roomList || props.exitRoomList
    console.log('开始计算罚没金额, exitRoomList:', targetRoomList)
    
    if (!targetRoomList || targetRoomList.length === 0) {
        console.log('房间列表为空，返回0')
        return 0
    }
    
    let totalPenalty = 0
    
    targetRoomList.forEach((room, index) => {
        console.log(`房间${index + 1} (${room.roomName}):`, {
            doorWindowPenalty: room.doorWindowPenalty,
            keyPenalty: room.keyPenalty,
            cleaningPenalty: room.cleaningPenalty,
            exitRoomAssetsListLength: room.exitRoomAssetsList?.length || 0
        })
        
        // 计算房间配套资产的赔偿金
        if (room.exitRoomAssetsList && Array.isArray(room.exitRoomAssetsList)) {
            room.exitRoomAssetsList.forEach((asset: any) => {
                if (asset.penalty && asset.penalty > 0) {
                    const penaltyAmount = parseFloat(asset.penalty) || 0
                    totalPenalty += penaltyAmount
                    console.log(`  配套资产赔偿: ${asset.name} = ${penaltyAmount}`)
                }
            })
        }
        
        // 计算房屋其他情况的赔偿金
        // 门、窗、墙体及其他损坏赔偿
        if (room.doorWindowPenalty && room.doorWindowPenalty > 0) {
            const penaltyAmount = parseFloat(room.doorWindowPenalty) || 0
            totalPenalty += penaltyAmount
            console.log(`  门窗赔偿: ${penaltyAmount}`)
        }
        
        // 钥匙未交齐赔偿
        if (room.keyPenalty && room.keyPenalty > 0) {
            const penaltyAmount = parseFloat(room.keyPenalty) || 0
            totalPenalty += penaltyAmount
            console.log(`  钥匙赔偿: ${penaltyAmount}`)
        }
        
        // 清洁卫生费用
        if (room.cleaningPenalty && room.cleaningPenalty > 0) {
            const penaltyAmount = parseFloat(room.cleaningPenalty) || 0
            totalPenalty += penaltyAmount
            console.log(`  清洁费用: ${penaltyAmount}`)
        }
    })
    
    // 保留2位小数
    const finalAmount = Math.round(totalPenalty * 100) / 100
    console.log(`总罚没金额: ${finalAmount}`)
    return finalAmount
}

// 执行罚没金额更新的共用逻辑
const doUpdatePenaltyAmount = (newPenaltyAmount: number) => {
    // 查找现有的系统生成的罚没金额项（只更新type=1的罚没金，不影响接口返回的罚没金）
    const penaltyIndex = feeItems.value.findIndex(item => 
        (item.subjectName === '罚没金' || item.subjectId === '40') && item.type === 1
    )
    
    console.log(`计算得到新罚没金额: ${newPenaltyAmount}, 现有系统生成罚没金额项索引: ${penaltyIndex}`)
    
    if (newPenaltyAmount > 0) {
        if (penaltyIndex !== -1) {
            // 更新现有的系统生成罚没金额
            console.log(`更新现有系统生成罚没金额项，从 ${feeItems.value[penaltyIndex].amount} 到 ${newPenaltyAmount}`)
            feeItems.value[penaltyIndex].amount = newPenaltyAmount
            feeItems.value[penaltyIndex].remark = '房间配套和其他情况赔偿金总和'
        } else {
            // 添加新的系统生成罚没金额项
            console.log('添加新的系统生成罚没金额项')
            const penaltyCost: ExitCost = {
                id: `penalty_${Date.now()}`,
                exitId: formData.id,
                costId: '',
                startDate: '',
                endDate: '',
                payType: 1, // 收
                subjectId: '40',
                subjectName: '罚没金',
                receivableDate: '',
                amount: newPenaltyAmount,
                remark: '房间配套和其他情况赔偿金总和',
                type: 1, // 1-交割单生成，不可操作
                createByName: '',
                updateByName: '',
                isDel: false
            }
            feeItems.value.push(penaltyCost)
        }
    } else if (penaltyIndex !== -1) {
        // 如果没有罚没金额了，移除该系统生成的罚没金项（只移除type=1的）
        console.log('移除系统生成的罚没金额项')
        feeItems.value.splice(penaltyIndex, 1)
    }
    
    // 强制触发响应式更新
    const tempItems = [...feeItems.value]
    feeItems.value = tempItems
    formData.exitCostList = [...feeItems.value]
    
    console.log('罚没金额更新完成，当前费用项数量:', feeItems.value.length)
    const allPenaltyItems = feeItems.value.filter(item => item.subjectName === '罚没金' || item.subjectId === '40')
    console.log('所有罚没金项目详情:', allPenaltyItems.map(item => ({
        id: item.id,
        amount: item.amount,
        type: item.type,
        costId: item.costId,
        remark: item.remark,
        typeDesc: item.type === 0 ? '接口返回' : item.type === 1 ? '系统计算' : '手动添加'
    })))
}

// 初始化费用列表
const initCostList = () => {
    // feeItems.value = JSON.parse(JSON.stringify(props.costList))
    // [...props.costList]
    feeItems.value =  [...props.costList]
    
    // 计算罚没金额
    const penaltyAmount = calculatePenaltyAmount()
    
    // 如果有罚没金额，则使用 doUpdatePenaltyAmount 来处理（区分不同类型的罚没金）
    if (penaltyAmount > 0) {
        doUpdatePenaltyAmount(penaltyAmount)
    } else {
        // 如果没有罚没金额，移除系统生成的罚没金项
        doUpdatePenaltyAmount(0)
    }
    
    formData.exitCostList = [...feeItems.value]
}

// 使用指定房间数据更新罚没金额
const updatePenaltyAmountWithData = (roomList: any[]) => {
    console.log('==== updatePenaltyAmountWithData 被调用，使用最新房间数据 ====')
    const newPenaltyAmount = calculatePenaltyAmount(roomList)
    doUpdatePenaltyAmount(newPenaltyAmount)
}

// 更新罚没金额
const updatePenaltyAmount = () => {
    console.log('==== updatePenaltyAmount 被调用 ====')
    const newPenaltyAmount = calculatePenaltyAmount()
    doUpdatePenaltyAmount(newPenaltyAmount)
}

// 添加费项
const handleAddFeeItem = () => {
    const newFeeItem: ExitCost = {
        id: `temp_${Date.now()}`,
        exitId: formData.id,
        costId: '',
        startDate: '',
        endDate: '',
        payType: 1,
        subjectId: '',
        subjectName: '',
        receivableDate: '',
        amount: 0,
        remark: '',
        type: 2, // 手动添加
        createByName: '',
        updateByName: '',
        isDel: false
    }
    feeItems.value.push(newFeeItem)
    formData.exitCostList = [...feeItems.value]
}

// 移除费项
const removeFeeItem = (feeItem: ExitCost) => {
    const index = feeItems.value.findIndex(item => item.id === feeItem.id)
    if (index !== -1) {
        feeItems.value.splice(index, 1)
        formData.exitCostList = [...feeItems.value]
    }
}

// 处理日期范围变化
const handleDateRangeChange = (record: ExitCost, dates: [string, string] | null) => {
    if (dates && dates.length === 2) {
        record.startDate = dates[0]
        record.endDate = dates[1]
    } else {
        record.startDate = ''
        record.endDate = ''
    }
    // 更新表单数据
    formData.exitCostList = [...feeItems.value]
}

// 格式化日期范围显示
const formatDateRange = (startDate?: string, endDate?: string) => {
    if (!startDate && !endDate) {
        return '未设置'
    }
    if (startDate && endDate) {
        return `${startDate} ~ ${endDate}`
    }
    if (startDate) {
        return `${startDate} ~`
    }
    if (endDate) {
        return `~ ${endDate}`
    }
    return '未设置'
}

// 处理费用科目变化
const handleSubjectChange = (record: ExitCost, value: string) => {
    const subject = feeSubjects.value.find(item => item.id === value)
    if (subject) {
        record.subjectId = value
        record.subjectName = subject.name
    }
    // 确保金额为数字类型
    if (typeof record.amount === 'string') {
        record.amount = parseFloat(record.amount) || 0
    }
    // 更新表单数据
    formData.exitCostList = [...feeItems.value]
}

// 保存
const handleSave = async (isSubmit: boolean) => {
    // 验证
    if (finalFeeAmount.value < 0 && !formData.payeeName) {
        Message.warning('应退款项，请填写承租方收款信息')
        return
    }

    // 确保所有费用项的金额都是数字类型
    feeItems.value.forEach(item => {
        if (typeof item.amount === 'string') {
            item.amount = parseFloat(item.amount) || 0
        }
        // 保留2位小数
        item.amount = Math.round((item.amount || 0) * 100) / 100
    })

    // 更新最终金额
    formData.finalAmount = finalFeeAmount.value
    formData.isSubmit = isSubmit
    formData.exitCostList = [...feeItems.value]

    emit('save', { ...formData })
}

// 获取表单数据
const getFormData = () => {
    // 确保所有费用项的金额都是数字类型
    feeItems.value.forEach(item => {
        if (typeof item.amount === 'string') {
            item.amount = parseFloat(item.amount) || 0
        }
        // 保留2位小数
        item.amount = Math.round((item.amount || 0) * 100) / 100
    })

    // 更新最终金额和费用列表
    const currentFormData = { ...formData }
    currentFormData.finalAmount = finalFeeAmount.value
    currentFormData.exitCostList = [...feeItems.value]

    return currentFormData
}

// 手动触发罚没金更新（供外部调用）
const forceUpdatePenalty = () => {
    console.log('手动触发罚没金更新')
    updatePenaltyAmount()
}

// 暴露方法给父组件
defineExpose({
    handleSave,
    getFormData,
    forceUpdatePenalty,
    updatePenaltyAmount
})

// 格式化金额显示
const formatAmount = (amount: number) => {
    return amount.toLocaleString(undefined, { minimumFractionDigits: 2 })
}
</script>

<style scoped>
.exit-settlement-form {
    /* padding: 16px; */
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
}

.fee-summary {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.total,
.final-amount {
    text-align: right;
    font-size: 14px;
}

.amount {
    color: #f53f3f;
    font-weight: bold;
    font-size: 16px;
}

.reduction {
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: flex-end;
}

.settlement-options {
    margin-top: 16px;
    /* display: flex;· */
    /* justify-content: center; */
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e6eb;
}
</style> 