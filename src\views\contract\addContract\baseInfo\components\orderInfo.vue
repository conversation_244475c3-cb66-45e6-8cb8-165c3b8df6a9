<template>
    <section>
        <sectionTitle title="定单信息" />
        <div class="content">
            <a-form :disabled="isDetailMode" :model="formData" layout="vertical" ref="formRef">
                <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2, xxl: 4 }" :col-gap="16" :row-gap="0">
                    <a-grid-item>
                        <a-form-item label="关联定单">
                            <a-select :disabled="isDetailMode || (changeType.length > 0 && changeType.includes(3))" v-model="contractData.bookingRelType" placeholder="请选择"
                                @change="handleOrderTypeChange">
                                <a-option :value="0">房间有效定单</a-option>
                                <a-option :value="1">不关联定单</a-option>
                                <a-option :value="2">其他定单</a-option>
                            </a-select>
                            <a-button :disabled="isDetailMode || (changeType.length > 0 && changeType.includes(3))" style="margin-left: 10px;" v-if="contractData.bookingRelType === 2" type="primary"
                                @click="chooseOrder">选择定单</a-button>
                        </a-form-item>
                    </a-grid-item>
                </a-grid>

                <!-- 定单列表 -->
                <a-table v-if="contractData.bookingRelType !== 1" :data="contractData.bookings"
                    :bordered="{ cell: true }">
                    <template #columns>
                        <a-table-column title="预定房间" data-index="bookedRoom" align="center" />
                        <a-table-column title="预订人姓名" data-index="bookerName" align="center" />
                        <a-table-column title="定单已收金额" align="right">
                            <template #cell="{ record }">
                                {{ formatAmount(record.bookingReceivedAmount) }} 元
                            </template>
                        </a-table-column>
                        <a-table-column title="定单收款日期" data-index="bookingPaymentDate" align="center" />
                        <a-table-column v-if="!isDetailMode && (changeType.length === 0 || !changeType.includes(3))" title="操作" align="center">
                            <template #cell="{ record, rowIndex }">
                                <a-button type="text" size="mini" status="danger" @click="handleDeleteOrder(rowIndex)">
                                    删除
                                </a-button>
                            </template>
                        </a-table-column>
                    </template>
                </a-table>
            </a-form>
        </div>

        <!-- 其他定单选择侧滑框 -->
        <a-modal v-model:visible="modalVisible" :width="1200" title="选择其他定单" :on-before-ok="handleBeforeOk"
            @ok="handleSelectOrder" @cancel="modalVisible = false">
            <a-space direction="vertical" fill>
                <!-- 搜索表单 -->
                <a-form :model="searchForm" layout="inline">
                    <a-form-item field="bookingType" label="定单类型">
                        <a-select v-model="searchForm.bookingType" style="width: 180px;" placeholder="请选择定单类型">
                            <a-option value="">全部</a-option>
                            <a-option :value="1">确认房源</a-option>
                            <a-option :value="0">暂不确认房源</a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item field="roomName" label="预定房源">
                        <a-input v-model="searchForm.roomName" placeholder="请输入预定房源" />
                    </a-form-item>
                    <a-form-item field="customerName" label="预定客户名称">
                        <a-input v-model="searchForm.customerName" placeholder="请输入预定客户名称" />
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary" @click="handleSearch">搜索</a-button>
                    </a-form-item>
                </a-form>

                <!-- 定单列表 -->
                <a-table ref="orderTableRef" row-key="id" :data="otherOrderList" :pagination="pagination"
                    :bordered="{ cell: true }" @page-change="onPageChange" v-model:selectedKeys="selectedRowKeys"
                    :row-selection="!isDetailMode ? rowSelection : undefined" @selection-change="handleSelectionChange">
                    <template #columns>
                        <a-table-column title="客户" data-index="customerName" align="center" />
                        <a-table-column title="预定房源" data-index="roomName" align="center" />
                        <a-table-column title="定金应收金额" data-index="bookingAmount" align="right">
                            <template #cell="{ record }">
                                {{ formatAmount(record.bookingAmount) }}
                            </template>
                        </a-table-column>
                        <a-table-column title="定金已收金额" data-index="receivedAmount" align="right">
                            <template #cell="{ record }">
                                {{ formatAmount(record.receivedAmount) }}
                            </template>
                        </a-table-column>
                        <a-table-column title="应收日期" data-index="receivableDate" align="center" />
                        <a-table-column title="实收日期" data-index="receivedDate" align="center" />
                        <a-table-column title="创建人" data-index="createByName" align="center" />
                        <a-table-column title="创建日期" data-index="createTime" align="center" />
                    </template>
                </a-table>
            </a-space>
        </a-modal>
    </section>
</template>

<script lang="ts" setup>
import { ref, reactive, toRef, computed, inject } from 'vue'
import { Message, type FormInstance, type TableInstance } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { EditType } from '@/views/contract/type'
import { useContractStore } from '@/store/modules/contract'
import { getOrderList } from '@/api/orderManagement'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

/**
 * @description 编辑类型
 * 1. 详情
 *    - 关联定单类型、定单列表不可修改
 * 2. 变更
 *    - 包含费用条款变更时，定单信息不可修改
 */
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => {
    // 详情模式或变更详情模式
    if (editType.value === 'detail' || editType.value === 'changeDetail') {
        return true
    }
    // 变更模式且包含费用条款变更（changeType 包含 3）时，定单信息不可修改
    if (editType.value === 'change' && changeType.value.includes(3)) {
        return true
    }
    if (editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo') {
        return true
    }
    return false
})

// 表单引用
const formRef = ref<FormInstance>()

// 抽屉显示状态
const modalVisible = ref(false)

// 表单数据
const formData = reactive({})

// 定单列表
interface OrderItem {
    id: string;
    orderNo: string;
    roomName: string;
    customerName: string;
    receivedAmount: number;
    receivedDate: string;

}

const orderList = ref<OrderItem[]>([])

// 其他定单列表
const otherOrderList = ref<OrderItem[]>([])

// 搜索表单
const searchForm = reactive({
    bookingType: '',
    roomName: '',
    customerName: ''
})

// 分页配置
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
})

// 处理定单类型变化
const handleOrderTypeChange = (value: number) => {
    if (value === 0) {
        if (contractData.value.rooms.length === 0) {
            Message.warning('请先选择房源')
            contractData.value.bookingRelType = contractStore.editType !== 'toSign' ? 1 : 2
            return
        }
        // 加载房间有效定单
        loadValidOrders()
    } else if (value === 2) {
        // 打开侧滑选择其他定单
        contractData.value.bookings = []
    } else {
        // 清空定单列表
        contractData.value.bookings = []
    }
}

// 选择定单
const chooseOrder = () => {
    modalVisible.value = true
    loadOtherOrders()
}

// 加载房间有效定单
const loadValidOrders = async () => {
    const { rows } = await getOrderList({
        pageNum: pagination.current,
        pageSize: 10000,
        status: 2,
        roomIds: contractData.value.rooms.map(item => item.roomId).join(','),
        projectId: contractStore.currentProjectId,
    })
    if (rows.length === 0) {
        if (contractStore.editType !== 'toSign') {
            contractData.value.bookings = []
            contractData.value.bookingRelType = 1
            Message.warning('房间有效定单为空，请选择其他定单')
        }
    } else {
        const bookings = rows.map((item: any) => {
            return {
                bookingId: item.id,
                bookedRoom: item.roomName,
                bookerName: item.customerName,
                bookingReceivedAmount: item.receivedAmount,
                bookingPaymentDate: item.receivedDate,
            }
        })

        if (contractStore.editType === 'toSign') {
            // 转签约需要判断房源获取的定单中是否包含转签约带过来的定单，如果包含，则类型为房间有效定单，否则为其他定单
            const isInclude = bookings.some((item: any) => item.bookingId === contractData.value.bookings?.[0]?.bookingId)
            if (isInclude) {
                contractData.value.bookingRelType = 0
                contractData.value.bookings = bookings
            } else {
                contractData.value.bookingRelType = 2
                contractData.value.bookings = contractData.value.bookings?.concat(bookings)
            }
        } else {
            contractData.value.bookingRelType = 0
            // 格式化定单列表
            contractData.value.bookings = bookings
        }
    }
}

// 加载其他定单
const selectedRows = ref<OrderItem[]>([])
const loadOtherOrders = async () => {
    const { rows, total } = await getOrderList({
        pageNum: pagination.current,
        pageSize: 10,
        status: 2,
        projectId: contractStore.currentProjectId,
        ...searchForm
    })
    otherOrderList.value = rows
    pagination.total = total
}
const handleSelectionChange = (rowKeys: string[]) => {
    selectedRows.value = otherOrderList.value.filter(item => rowKeys.includes(item.id))
}

// 处理搜索
const handleSearch = () => {
    pagination.current = 1
    loadOtherOrders()
}

// 处理分页变化
const onPageChange = (page: number) => {
    pagination.current = page
    loadOtherOrders()
}

// 处理删除定单
const handleDeleteOrder = (rowIndex: number) => {
    contractData.value.bookings?.splice(rowIndex, 1)
}

// 表单验证
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('定单信息', errors);
            reject()
        } else {
            resolve(true)
        }
    })
}

// 表格多选配置
const rowSelection = ref({
    type: 'checkbox',
    showCheckedAll: true,
})
const selectedRowKeys = ref([])


// 处理选择其他定单
const orderTableRef = ref<TableInstance>()
const handleBeforeOk = () => {
    if (selectedRows.value.length === 0) {
        Message.warning('请选择定单')
        return false
    }
    return true
}
const handleSelectOrder = () => {
    contractData.value.bookings = selectedRows.value.map(item => ({
        bookingId: item.id,
        bookedRoom: item.roomName,
        bookerName: item.customerName,
        bookingReceivedAmount: item.receivedAmount,
        bookingPaymentDate: item.receivedDate,
    }))
    modalVisible.value = false
}

// 暴露方法给父组件
defineExpose({
    validate,
})

// watch(() => contractData.value.rooms, (newVal, oldVal) => {
//     if (newVal.length > 0) {
//         const newRoomIds = newVal.map(item => item.roomId)
//         const oldRoomIds = oldVal.map(item => item.roomId)
//         if (!oldVal || oldVal.length === 0) {

//         }
//         if (newRoomIds.some(id => !oldRoomIds.includes(id))) {
//             console.log(oldRoomIds);
//             contractData.value.bookingRelType = 0
//             loadValidOrders()
//         }
//     } else {
//         contractData.value.bookingRelType = 1
//     }
// })

</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}
</style>