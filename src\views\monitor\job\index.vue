<template>
  <div class="container">
    <a-card class="general-card" title="定时任务">
      <!-- 搜索表单 -->
      <a-row>
        <a-col :flex="1">
          <a-form :model="queryParams" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
            label-align="right">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="任务名称">
                  <a-input v-model="queryParams.jobName" placeholder="请输入任务名称" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="任务组名">
                  <a-select v-model="queryParams.jobGroup" placeholder="请选择任务组名" allow-clear>
                    <a-option v-for="dict in sys_job_group" :key="dict.value" :value="dict.value">
                      {{ dict.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="任务状态">
                  <a-select v-model="queryParams.status" placeholder="请选择任务状态" allow-clear>
                    <a-option v-for="dict in sys_job_status" :key="dict.value" :value="dict.value">
                      {{ dict.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 42px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="horizontal" :size="18">
            <a-button type="primary" @click="handleQuery">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="resetQuery">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <!-- 操作按钮 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="16">
          <a-space>
            <a-button type="primary" @click="handleAdd">
              <template #icon>
                <icon-plus />
              </template>
              新增
            </a-button>
            <a-button type="primary" status="success" :disabled="selectedKeys.length !== 1" @click="handleUpdate">
              <template #icon>
                <icon-edit />
              </template>
              修改
            </a-button>
            <a-button type="primary" status="danger" :disabled="selectedKeys.length === 0" @click="handleDelete">
              <template #icon>
                <icon-delete />
              </template>
              删除
            </a-button>
            <a-button type="primary" status="warning" @click="handleExport">
              <template #icon>
                <icon-download />
              </template>
              导出
            </a-button>
            <a-button type="primary" status="success" @click="handleJobLog">
              <template #icon>
                <icon-code-block />
              </template>
              日志
            </a-button>
          </a-space>
        </a-col>
        <a-col :span="8" style="text-align: right">
          <a-space>
            <a-tooltip content="刷新">
              <div class="action-icon" @click="handleQuery">
                <icon-refresh size="18" />
              </div>
            </a-tooltip>
            <a-dropdown @select="handleSelectDensity">
              <a-tooltip content="密度">
                <div class="action-icon"><icon-line-height size="18" /></div>
              </a-tooltip>
              <template #content>
                <a-doption v-for="item in densityList" :key="item.value" :value="item.value"
                  :class="{ active: item.value === size }">
                  <span>{{ item.name }}</span>
                </a-doption>
              </template>
            </a-dropdown>
            <a-tooltip content="列设置">
              <a-popover trigger="click" position="bl" @popup-visible-change="popupVisibleChange">
                <div class="action-icon"><icon-settings size="18" /></div>
                <template #content>
                  <div id="tableSetting">
                    <div v-for="(item, index) in showColumns" :key="item.dataIndex" class="setting">
                      <div style="margin-right: 4px;cursor: move;">
                        <icon-drag-arrow />
                      </div>
                      <div>
                        <a-checkbox v-model="item.checked" @change="handleChange($event, item, index)">
                        </a-checkbox>
                      </div>
                      <div class="title">{{ item.title }}</div>
                    </div>
                  </div>
                </template>
              </a-popover>
            </a-tooltip>
          </a-space>
        </a-col>
      </a-row>

      <!-- 表格 -->
      <a-table row-key="jobId" :loading="loading" :pagination="pagination" column-resizable :bordered="{ cell: true }"
        :columns="cloneColumns" :data="jobList" :size="size" :row-selection="rowSelection"
        v-model:selected-keys="selectedKeys" @page-change="onPageChange" :scroll="{x: 1}">
        <template #jobGroup="{ record }">
          <DictTag :options="sys_job_group" :value="record.jobGroup" />
        </template>
        <template #status="{ record }">
          <a-switch v-model="record.status" checked-value="0" unchecked-value="1"
            @change="handleStatusChange(record)" />
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button type="text" size="small" @click="handleUpdate(record)">
              修改
            </a-button>
            <a-button type="text" size="small" @click="handleDelete(record)">
              删除
            </a-button>
            <a-button type="text" size="small" @click="handleRun(record)">
              执行一次
            </a-button>
            <a-button type="text" size="small" @click="handleView(record)">
              任务详细
            </a-button>
            <a-button type="text" size="small" @click="handleJobLog(record)">
              调度日志
            </a-button>
          </a-space>
        </template>
      </a-table>

      <!-- 添加或修改对话框 -->
      <a-modal v-model:visible="open" :title="title" width="780px" @before-ok="submitForm" @cancel="cancel">
        <a-form ref="jobRef" :model="form" :rules="rules" label-align="right" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="jobName" label="任务名称">
                <a-input v-model="form.jobName" placeholder="请输入任务名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="jobGroup" label="任务分组">
                <a-select v-model="form.jobGroup" placeholder="请选择">
                  <a-option v-for="dict in sys_job_group" :key="dict.value" :value="dict.value">
                    {{ dict.label }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item field="invokeTarget" label="调用方法">
                <template #label>
                  调用方法
                  <a-tooltip position="top">
                    <template #content>
                      <div>
                        Bean调用示例：ryTask.ryParams('ry')
                        <br />Class类调用示例：com.ruoyi.quartz.task.RyTask.ryParams('ry')
                        <br />参数说明：支持字符串，布尔类型，长整型，浮点型，整型
                      </div>
                    </template>
                    <icon-question-circle-fill />
                  </a-tooltip>
                </template>
                <a-input v-model="form.invokeTarget" placeholder="请输入调用目标字符串" style="width: 100%;">
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item field="cronExpression" label="cron表达式">
                <a-input v-model="form.cronExpression" placeholder="请输入cron执行表达式" />
                <a-button type="primary" @click="handleShowCron">
                  生成表达式
                  <template #icon>
                    <icon-calendar />
                  </template>
                </a-button>
              </a-form-item>
            </a-col>
            <a-col :span="24" v-if="form.jobId !== undefined">
              <a-form-item label="状态">
                <a-radio-group v-model="form.status">
                  <a-radio v-for="dict in sys_job_status" :key="dict.value" :value="dict.value">
                    {{ dict.label }}
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="misfirePolicy" label="执行策略">
                <a-radio-group v-model="form.misfirePolicy" type="button">
                  <a-radio value="1">立即执行</a-radio>
                  <a-radio value="2">执行一次</a-radio>
                  <a-radio value="3">放弃执行</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="concurrent" label="是否并发" type="button">
                <a-radio-group v-model="form.concurrent">
                  <a-radio value="0">允许</a-radio>
                  <a-radio value="1">禁止</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>

      <!-- Cron表达式生成器 -->
      <a-modal v-model:visible="openCron" width="800px" title="Cron表达式生成器" :footer="false">
        <crontab ref="crontabRef" @hide="openCron = false" @fill="crontabFill" :expression="expression" />
      </a-modal>

      <!-- 任务详细信息 -->
      <a-modal v-model:visible="openView" title="任务详细" width="700px" @cancel="openView = false">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="任务编号">{{ form.jobId }}</a-descriptions-item>
          <a-descriptions-item label="任务名称">{{ form.jobName }}</a-descriptions-item>
          <a-descriptions-item label="任务分组">{{ jobGroupFormat(form) }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
          <a-descriptions-item label="cron表达式">{{ form.cronExpression }}</a-descriptions-item>
          <a-descriptions-item label="下次执行时间">{{ parseTime(form.nextValidTime) }}</a-descriptions-item>
          <a-descriptions-item label="调用目标方法" :span="2">{{ form.invokeTarget }}</a-descriptions-item>
          <a-descriptions-item label="任务状态">
            {{ form.status === '0' ? '正常' : '暂停' }}
          </a-descriptions-item>
          <a-descriptions-item label="是否并发">
            {{ form.concurrent === '0' ? '允许' : '禁止' }}
          </a-descriptions-item>
          <a-descriptions-item label="执行策略">
            {{ form.misfirePolicy === '1' ? '立即执行' : form.misfirePolicy === '2' ? '执行一次' : '放弃执行' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-modal>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, reactive, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import useLoading from '@/hooks/loading'
import { listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus, exportJob } from '@/api/monitor/job'
import Crontab from '@/components/Crontab/index.vue'
import cloneDeep from 'lodash/cloneDeep'
import Sortable from 'sortablejs'
import { useDict } from '@/utils/dict'
import { Pagination } from '@/types/global';
import DictTag from '@/components/DictTag/index.vue'
import { selectDictLabel, parseTime } from "@/utils";
import { exportExcel } from '@/utils/exportUtil'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()
const { sys_job_group, sys_job_status } = useDict('sys_job_group', 'sys_job_status')

// 遮罩层
const { loading, setLoading } = useLoading(true)
// 弹出层标题
const title = ref('')
// 是否显示弹出层
const open = ref(false)
// 是否显示详细弹出层
const openView = ref(false)
// 是否显示Cron表达式弹出层
const openCron = ref(false)
// Cron表达式
const expression = ref<string | undefined>('')
// 表格尺寸
const size = ref<SizeProps>('medium')

// 表单参数
const generateQueryParams = () => {
  return {
    jobName: '',
    jobGroup: '',
    status: ''
  }
}
const queryParams = ref(generateQueryParams())
const basePagination: Pagination = {
  pageNum: 1,
  pageSize: 20,
};
const pagination = reactive({
  ...basePagination,
});
const jobList = ref<any[]>([]);
const fetchData = async (
  params = { pageNum: 1, pageSize: 20 }
) => {
  setLoading(true);
  try {
    const { rows, total } = await listJob(params);
    jobList.value = rows;
    pagination.pageNum = params.pageNum;
    pagination.total = total;
  } catch (err) {
    // you can report use errorHandler or other
  } finally {
    setLoading(false);
  }
};
/** 搜索按钮操作 */
const handleQuery = () => {
  fetchData({ ...basePagination, ...queryParams.value });
}
const onPageChange = (pageNum: number) => {
  fetchData({ ...basePagination, pageNum });
};
/** 重置按钮操作 */
function resetQuery() {
  queryParams.value = generateQueryParams()
}
fetchData()

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const selectedKeys = ref<Array<string | number>>([])

// 表单校验
const rules = {
  jobName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
  invokeTarget: [{ required: true, message: '调用目标字符串不能为空', trigger: 'blur' }],
  cronExpression: [{ required: true, message: 'cron执行表达式不能为空', trigger: 'change' }]
}

// 表单参数
interface FormParams {
  jobId?: string | number;
  jobName?: string;
  jobGroup?: string;
  invokeTarget?: string;
  cronExpression?: string;
  misfirePolicy?: string;
  concurrent?: string;
  status?: string;
  createTime?: string;
  nextValidTime?: string | number | Date;
}
const form = ref<FormParams>({
  jobId: undefined,
  jobName: undefined,
  jobGroup: undefined,
  invokeTarget: undefined,
  cronExpression: undefined,
  misfirePolicy: '1',
  concurrent: '1',
  status: '0'
})

// 表格列定义
const columns = computed<TableColumnData[]>(() => [
  { title: '任务编号', dataIndex: 'jobId', width: 100 },
  { title: '任务名称', dataIndex: 'jobName', width: 200 },
  { title: '任务组名', dataIndex: 'jobGroup', slotName: 'jobGroup', width: 200 },
  { title: '调用目标字符串', dataIndex: 'invokeTarget', width: 200 },
  { title: 'cron执行表达式', dataIndex: 'cronExpression', width: 200 },
  { title: '状态', dataIndex: 'status', slotName: 'status', width: 200 },
  { title: '操作', dataIndex: 'operations', slotName: 'operations', width: 450, fixed: 'right' }
])

const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])

const densityList = computed(() => [
  { name: '迷你', value: 'mini' },
  { name: '偏小', value: 'small' },
  { name: '中等', value: 'medium' },
  { name: '偏大', value: 'large' }
])

/** 任务组名字典翻译 */
function jobGroupFormat(row: any) {
  return selectDictLabel(sys_job_group.value, row.jobGroup)
}

/** 表单重置 */
function reset() {
  form.value = {
    jobId: undefined,
    jobName: undefined,
    jobGroup: undefined,
    invokeTarget: undefined,
    cronExpression: undefined,
    misfirePolicy: '1',
    concurrent: '1',
    status: '0'
  }
  // proxy.resetForm('jobRef')
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 任务状态修改 */
function handleStatusChange(row: any) {
  const text = row.status === '0' ? '启用' : '停用'
  Modal.confirm({
    title: '确认',
    content: `确认要"${text}""${row.jobName}"任务吗?`,
    onOk: () => {
      return changeJobStatus({ jobId: row.jobId, status: row.status }).then(() => {
        Message.success(`${text}成功`)
      }).catch(() => {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
    onCancel: () => {
      row.status = row.status === '0' ? '1' : '0'
    }
  })
}

/** 立即执行一次 */
function handleRun(row: any) {
  Modal.confirm({
    title: '确认',
    content: `确认要立即执行一次"${row.jobName}"任务吗?`,
    onOk: () => {
      return runJob({ jobId: row.jobId, jobGroup: row.jobGroup }).then(() => {
        Message.success('执行成功')
      })
    }
  })
}

/** 任务详细信息 */
function handleView(row: any) {
  getJob(row.jobId).then(response => {
    form.value = response.data
    openView.value = true
  })
}

/** cron表达式按钮操作 */
function handleShowCron() {
  expression.value = form.value.cronExpression
  openCron.value = true
}

/** 确定后回传值 */
function crontabFill(value: string) {
  form.value.cronExpression = value
}

/** 任务日志列表查询 */
function handleJobLog(row?: any) {
  const jobId = row?.jobId || 0
  router.push({ path: '/monitor/job-log/index', query: { jobId } })
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = '添加任务'
}

/** 修改按钮操作 */
function handleUpdate(row?: any) {
  reset()
  const jobId = row?.jobId || selectedKeys.value[0]
  getJob(jobId).then(response => {
    form.value = response.data
    open.value = true
    title.value = '修改任务'
  })
}

/** 提交按钮 */
const jobRef = ref()
const submitForm = async () => {
  try {
    const errors = await jobRef.value.validate()
    if (errors) {
      console.log(errors, '验证失败')
      return false
    }
    // 验证通过，继续处理保存逻辑
    if (form.value.jobId) {
      await updateJob(form.value)
      Message.success('修改成功')
    } else {
      await addJob(form.value)
      Message.success('新增成功')
    }
    // 刷新列表
    fetchData()
    return true
  } catch (err) {
    console.log(err, '验证失败')
    return false
  } finally {
    // submitLoading.value = false
  }
}

/** 删除按钮操作 */
function handleDelete(row?: any) {
  const jobIds = row?.jobId || selectedKeys.value.join(',')
  Modal.confirm({
    title: '确认',
    content: `是否确认删除定时任务编号为"${jobIds}"的数据项?`,
    onOk: () => {
      return delJob(jobIds).then(() => {
        fetchData()
        Message.success('删除成功')
      })
    }
  })
}

/** 导出按钮操作 */
function handleExport() {
  exportExcel(exportJob, { ...pagination, ...queryParams.value }, '定时任务列表')
}

/** 表格密度选择 */
function handleSelectDensity(val: string) {
  size.value = val as SizeProps
}

/** 列设置勾选事件 */
function handleChange(checked: boolean, column: Column, index: number) {
  if (!checked) {
    cloneColumns.value = showColumns.value.filter(
      (item) => item.dataIndex !== column.dataIndex
    )
  } else {
    cloneColumns.value.splice(index, 0, column)
  }
}

/** 列设置拖拽排序 */
function popupVisibleChange(val: boolean) {
  if (val) {
    nextTick(() => {
      const el = document.getElementById('tableSetting') as HTMLElement
      const sortable = new Sortable(el, {
        onEnd(e: any) {
          const { oldIndex, newIndex } = e
          if (oldIndex !== newIndex) {
            const targetColumn = cloneColumns.value.splice(oldIndex, 1)[0]
            cloneColumns.value.splice(newIndex, 0, targetColumn)
            const targetShowColumn = showColumns.value.splice(oldIndex, 1)[0]
            showColumns.value.splice(newIndex, 0, targetShowColumn)
          }
        }
      })
    })
  }
}

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="less">
.container {
  padding: 0 16px 16px 16px;
}

:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}

.action-icon {
  margin-left: 12px;
  cursor: pointer;
}

.active {
  color: #0960bd;
  background-color: #e3f4fc;
}

.setting {
  display: flex;
  align-items: center;
  width: 200px;

  .title {
    margin-left: 12px;
    cursor: pointer;
  }
}
</style>
