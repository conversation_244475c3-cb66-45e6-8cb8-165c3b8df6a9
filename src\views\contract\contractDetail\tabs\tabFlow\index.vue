<template>
    <div class="contract-flow">
        <section>
            <sectionTitle title="流水信息"></sectionTitle>
            
            <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
                <a-table
                    :columns="columns"
                    :data="tableData"
                    :pagination="pagination"
                    :scroll="{ x: 1200 }"
                    :bordered="{ cell: true }"
                    :loading="loading"
                    :empty="emptyText"
                    @page-change="onPageChange"
                    @page-size-change="onPageSizeChange"
                >
                    <template #type="{ record }">
                        <span>{{ getTypeText(record.type) }}</span>
                    </template>
                    <template #confirmStatus="{ record }">
                        <a-tag :color="getConfirmStatusColor(record.confirmStatus)">
                            {{ getConfirmStatusText(record.confirmStatus) }}
                        </a-tag>
                    </template>
                    <template #payAmount="{ record }">
                        <span class="money-text">¥{{ formatMoney(record.payAmount) }}</span>
                    </template>
                    <template #acctAmount="{ record }">
                        <span class="money-text">¥{{ formatMoney(record.acctAmount) }}</span>
                    </template>
                    <template #payType="{ record }">
                        <span>{{ getPayTypeText(record.payType) }}</span>
                    </template>
                    <template #payMethod="{ record }">
                        <span>{{ record.payMethod }}</span>
                    </template>
                    <template #entryTime="{ record }">
                        <span>{{ formatDateTime(record.entryTime) }}</span>
                    </template>
                    <template #confirmTime="{ record }">
                        <span>{{ formatDateTime(record.confirmTime) }}</span>
                    </template>
                </a-table>
            </a-card>
        </section>
    </div>
</template>

<script setup lang="ts">
    import sectionTitle from '@/components/sectionTitle/index.vue';
    import { getContractDetailFlow, CostFlowRelVo } from '@/api/contract';
    import { useContractStore } from '@/store/modules/contract/index';
    import dayjs from 'dayjs';

    const contractStore = useContractStore();
    const contractData = computed(() => contractStore.contractDetail as any);
    
    const tableData = ref<CostFlowRelVo[]>([]);
    const loading = ref(false);
    const emptyText = '暂无流水信息';
    
    // 分页相关
    const pagination = ref({
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: true,
        showJumper: true,
        showPageSize: true,
        pageSizeOptions: [10, 20, 50, 100]
    });

    // 账单类型映射
    // 1-手动记账（收款）、2-自动记账（收款）、3-手动记账（退款）、4-自动记账（退款）、5-结转（转入）、6-结转（转出）
    const typeMap = new Map([
        [1, '手动记账（收款）'],
        [2, '自动记账（收款）'],
        [3, '手动记账（退款）'],
        [4, '自动记账（退款）'],
        [5, '结转（转入）'],
        [6, '结转（转出）']
    ]);

    // 确认状态映射
    const confirmStatusMap = new Map([
        [0, '未确认'],
        [1, '自动确认'],
        [2, '手动确认']
    ]);

    // 支付类型映射
    const payTypeMap = new Map([
        [0, '线上支付'],
        [1, '线下支付']
    ]);

    // 支付方式映射
    const payMethodMap = new Map([
        [1, '微信'],
        [2, '支付宝'],
        [3, '银行卡'],
        [4, '现金']
    ]);

    const getTypeText = (type: number) => {
        return typeMap.get(type) || '未知类型';
    };

    const getConfirmStatusText = (status: number) => {
        return confirmStatusMap.get(status) || '未知状态';
    };

    const getConfirmStatusColor = (status: number) => {
        const colorMap = new Map([
            [0, 'orange'],
            [1, 'blue'],
            [2, 'green']
        ]);
        return colorMap.get(status) || 'default';
    };

    const getPayTypeText = (payType: number) => {
        return payTypeMap.get(payType) || '未知';
    };

    const getPayMethodText = (payMethod: number) => {
        return payMethodMap.get(payMethod) || '未知';
    };

    const formatMoney = (amount: number) => {
        return amount ? amount.toFixed(2) : '0.00';
    };

    const formatDateTime = (dateTime: string) => {
        return dateTime ? dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss') : '';
    };

    const columns = computed(() => {
        return [
            {
                title: '账单类型',
                dataIndex: 'type',
                width: 140,
                align: 'center',
                slotName: 'type',
            },
            {
                title: '实际支付时间',
                dataIndex: 'entryTime',
                width: 180,
                align: 'center',
                slotName: 'entryTime',
            },
            {
                title: '支付类型',
                dataIndex: 'payType',
                width: 120,
                align: 'center',
                slotName: 'payType',
            },
            {
                title: '支付方式',
                dataIndex: 'payMethod',
                width: 150,
                align: 'center',
                slotName: 'payMethod',
            },
            {
                title: '支付单号',
                dataIndex: 'orderNo',
                width: 150,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '实际支付金额',
                dataIndex: 'payAmount',
                width: 140,
                align: 'center',
                slotName: 'payAmount',
            },
            {
                title: '本次记账金额',
                dataIndex: 'acctAmount',
                width: 120,
                align: 'center',
                slotName: 'acctAmount',
            },
            {
                title: '确认状态',
                dataIndex: 'confirmStatus',
                width: 100,
                align: 'center',
                slotName: 'confirmStatus',
            },
            {
                title: '确认时间',
                dataIndex: 'confirmTime',
                width: 175,
                align: 'center',
                slotName: 'confirmTime',
            },
            {
                title: '确认人',
                dataIndex: 'confirmUserName',
                width: 120,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
        ];
    });

    // 获取流水信息数据
    const loadFlowData = async () => {
        if (!contractData.value?.id) {
            tableData.value = [];
            pagination.value.total = 0;
            return;
        }

        try {
            loading.value = true;
            const response = await getContractDetailFlow({
                id: contractData.value.id,
                pageNum: pagination.value.current,
                pageSize: pagination.value.pageSize
            });
            if (response.code === 200) {
                tableData.value = response.data?.rows || [];
                pagination.value.total = response.data?.total || 0;
            } else {
                tableData.value = [];
                pagination.value.total = 0;
                console.error('获取流水信息失败:', response.msg);
            }
        } catch (error) {
            console.error('获取流水信息异常:', error);
            tableData.value = [];
            pagination.value.total = 0;
        } finally {
            loading.value = false;
        }
    };

    // 页码变化处理
    const onPageChange = (page: number) => {
        pagination.value.current = page;
        loadFlowData();
    };

    // 每页数量变化处理
    const onPageSizeChange = (pageSize: number) => {
        pagination.value.pageSize = pageSize;
        pagination.value.current = 1; // 重置到第一页
        loadFlowData();
    };

    // 监听合同数据变化，自动刷新流水信息
    watch(
        () => contractData.value?.id,
        (newId) => {
            if (newId) {
                // 重置分页到第一页
                pagination.value.current = 1;
                loadFlowData();
            }
        },
        { immediate: true }
    );
</script>

<style scoped lang="less">
    .contract-flow {
        .money-text {
            color: #f53f3f;
            font-weight: 500;
        }
    }
</style>
