<template>
    <div class="contract-flow">
        <section>
            <sectionTitle title="流水信息"></sectionTitle>
            
            <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
                <a-table
                    :columns="columns"
                    :data="tableData"
                    :pagination="false"
                    :scroll="{ x: 1200 }"
                    :bordered="{ cell: true }"
                    :loading="loading"
                    :empty="emptyText"
                >
                    <template #type="{ record }">
                        <span>{{ getTypeText(record.type) }}</span>
                    </template>
                    <template #confirmStatus="{ record }">
                        <a-tag :color="getConfirmStatusColor(record.confirmStatus)">
                            {{ getConfirmStatusText(record.confirmStatus) }}
                        </a-tag>
                    </template>
                    <template #payAmount="{ record }">
                        <span class="money-text">¥{{ formatMoney(record.payAmount) }}</span>
                    </template>
                    <template #acctAmount="{ record }">
                        <span class="money-text">¥{{ formatMoney(record.acctAmount) }}</span>
                    </template>
                    <template #payType="{ record }">
                        <span>{{ getPayTypeText(record.payType) }}</span>
                    </template>
                    <template #payMethod="{ record }">
                        <span>{{ getPayMethodText(record.payMethod) }}</span>
                    </template>
                    <template #entryTime="{ record }">
                        <span>{{ formatDateTime(record.entryTime) }}</span>
                    </template>
                    <template #confirmTime="{ record }">
                        <span>{{ formatDateTime(record.confirmTime) }}</span>
                    </template>
                </a-table>
            </a-card>
        </section>
    </div>
</template>

<script setup lang="ts">
    import sectionTitle from '@/components/sectionTitle/index.vue';
    import { getContractDetailFlow, CostFlowRelVo } from '@/api/contract';
    import { useContractStore } from '@/store/modules/contract/index';
    import dayjs from 'dayjs';

    const contractStore = useContractStore();
    const contractData = computed(() => contractStore.contractDetail as any);
    
    const tableData = ref<CostFlowRelVo[]>([]);
    const loading = ref(false);
    const emptyText = '暂无流水信息';

    // 账单类型映射
    const typeMap = new Map([
        [1, '收款'],
        [2, '转入'],
        [3, '转出'],
        [4, '退款']
    ]);

    // 确认状态映射
    const confirmStatusMap = new Map([
        [0, '未确认'],
        [1, '自动确认'],
        [2, '手动确认']
    ]);

    // 支付类型映射
    const payTypeMap = new Map([
        [1, '线上支付'],
        [2, '线下支付']
    ]);

    // 支付方式映射
    const payMethodMap = new Map([
        [1, '微信-账单支付'],
        [2, '支付宝-账单支付'],
        [3, '银行卡-账单支付'],
        [4, '现金'],
        [5, '转账'],
        [6, '微信-房源码支付'],
        [7, '支付宝-房源码支付']
    ]);

    const getTypeText = (type: number) => {
        return typeMap.get(type) || '未知类型';
    };

    const getConfirmStatusText = (status: number) => {
        return confirmStatusMap.get(status) || '未知状态';
    };

    const getConfirmStatusColor = (status: number) => {
        const colorMap = new Map([
            [0, 'orange'],
            [1, 'blue'],
            [2, 'green']
        ]);
        return colorMap.get(status) || 'default';
    };

    const getPayTypeText = (payType: number) => {
        return payTypeMap.get(payType) || '未知';
    };

    const getPayMethodText = (payMethod: number) => {
        return payMethodMap.get(payMethod) || '未知';
    };

    const formatMoney = (amount: number) => {
        return amount ? amount.toFixed(2) : '0.00';
    };

    const formatDateTime = (dateTime: string) => {
        return dateTime ? dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss') : '';
    };

    const columns = computed(() => {
        return [
            {
                title: '账单类型',
                dataIndex: 'type',
                width: 100,
                align: 'center',
                slotName: 'type',
            },
            {
                title: '实际支付时间',
                dataIndex: 'entryTime',
                width: 180,
                align: 'center',
                slotName: 'entryTime',
            },
            {
                title: '支付类型',
                dataIndex: 'payType',
                width: 120,
                align: 'center',
                slotName: 'payType',
            },
            {
                title: '支付方式',
                dataIndex: 'payMethod',
                width: 150,
                align: 'center',
                slotName: 'payMethod',
            },
            {
                title: '支付单号',
                dataIndex: 'orderNo',
                width: 150,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '实际支付金额',
                dataIndex: 'payAmount',
                width: 120,
                align: 'center',
                slotName: 'payAmount',
            },
            {
                title: '本次记账金额',
                dataIndex: 'acctAmount',
                width: 120,
                align: 'center',
                slotName: 'acctAmount',
            },
            {
                title: '确认状态',
                dataIndex: 'confirmStatus',
                width: 100,
                align: 'center',
                slotName: 'confirmStatus',
            },
            {
                title: '确认时间',
                dataIndex: 'confirmTime',
                width: 160,
                align: 'center',
                slotName: 'confirmTime',
            },
            {
                title: '确认人',
                dataIndex: 'confirmUserName',
                width: 120,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
        ];
    });

    // 获取流水信息数据
    const loadFlowData = async () => {
        if (!contractData.value?.id) {
            tableData.value = [];
            return;
        }

        try {
            loading.value = true;
            const response = await getContractDetailFlow(contractData.value.id);
            if (response.code === 200) {
                tableData.value = response.data || [];
            } else {
                tableData.value = [];
                console.error('获取流水信息失败:', response.msg);
            }
        } catch (error) {
            console.error('获取流水信息异常:', error);
            tableData.value = [];
        } finally {
            loading.value = false;
        }
    };

    // 监听合同数据变化，自动刷新流水信息
    watch(
        () => contractData.value?.id,
        (newId) => {
            if (newId) {
                loadFlowData();
            }
        },
        { immediate: true }
    );
</script>

<style scoped lang="less">
    .contract-flow {
        .money-text {
            color: #f53f3f;
            font-weight: 500;
        }
    }
</style>
