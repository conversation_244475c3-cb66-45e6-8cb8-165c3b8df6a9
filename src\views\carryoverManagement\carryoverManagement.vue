<template>
	<div class="carryover-management">
		<a-card>
			<a-row>
				<a-col :flex="1">

					<a-form :model="queryParams" layout="inline">
						<a-row :gutter="16">

							<a-col :span="8">
								<a-form-item label="项目名称">
									<a-select v-model:value="queryParams.projectId" placeholder="请选择项目">
										<!-- 项目下拉选项 -->
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="转出业务">
									<a-select v-model:value="queryParams.fromBizType" placeholder="请选择转出业务">
										<a-option :value="0">定单</a-option>
										<a-option :value="1">合同</a-option>
										<a-option :value="2">未明流水</a-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="转入业务">
									<a-select v-model:value="queryParams.toBizType" placeholder="请选择转入业务">
										<a-option :value="0">定单</a-option>
										<a-option :value="1">合同</a-option>
										<a-option :value="2">未明流水</a-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="结转日期">
									<a-range-picker v-model:value="dateRange" @change="handleDateChange"
										style="width: 100%" />
								</a-form-item>
							</a-col>
						</a-row>
					</a-form>
				</a-col>
				<!-- </a-row> -->
				<!-- <a-row :gutter="16" class="mt-4"> -->
				<a-divider style="height: 84px" direction="vertical" />

				<a-col :flex="'86px'" style="text-align: right">
					<a-space direction="vertical">
						<a-button type="primary" @click="handleQuery">查询</a-button>
						<a-button @click="resetQuery">重置</a-button>
						<!-- <a-button type="primary" @click="handleExport">导出</a-button> -->
					</a-space>
				</a-col>
			</a-row>

			<div class="table-container">
				<a-table :columns="columns" :data="tableData" :pagination="pagination" 
					@page-change="onPageChange" @page-size-change="onPageSizeChange"
					row-key="id" :loading="loading" :scroll="{ x: 1 }">
					<template #index="{ rowIndex }">
						{{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
					</template>
					<template #fromBizType="{ record }">
                        <span>
							{{ record.fromBizType === 0 ? '定单' : record.fromBizType === 1 ? '合同' : '未明流水' }}
                        </span>
                    </template>
					<template #toBizType="{ record }">
                        <span>
							{{ record.toBizType === 0 ? '定单' : record.toBizType === 1 ? '合同' : '未明流水' }}
                        </span>
                    </template>
					<template #operations="{ record }">
						<a-button type="text" size="mini" @click="handleDetail(record)"
							style="color: #165dff;">详情</a-button>
					</template>
				</a-table>
			</div>
		</a-card>

		<carryover-detail v-if="detailVisible" :id="currentDetailId" @close="detailVisible = false" />
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
// @ts-ignore
import { listCarryover, exportCarryover, getCarryoverDetail } from '@/api/carryoverManagement'
import CarryoverDetail from './components/carryoverDetail.vue'

const queryParams = reactive({
	projectId: undefined,
	fromBizType: undefined,
	toBizType: undefined,
	carryoverTimeStart: '',
	carryoverTimeEnd: ''
})

const dateRange = ref([])
const tableData = ref([])
const loading = ref(false)
const detailVisible = ref(false)
const currentDetailId = ref<string | undefined>(undefined)

const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showSizeChanger: true,
	showQuickJumper: true
})

const columns = [
	{
		title: '序号',
		dataIndex: 'index',
		slotName: 'index',
		width: 70,
		align: 'center',
		ellipsis: true,
		tooltip: true
	}, {
		title: '项目', dataIndex: 'projectName', key: 'projectName',
		width: 190,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '结转单号', dataIndex: 'carryoverNo', key: 'carryoverNo', width: 190,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '结转日期', dataIndex: 'carryoverTime', key: 'carryoverTime', width: 190,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '转出业务', dataIndex: 'fromBizType', key: 'fromBizType', slotName: 'fromBizType',
		width: 190,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '转出业务单据', dataIndex: 'fromBizNo', key: 'fromBizNo',
		width: 190,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '转入业务', dataIndex: 'toBizType', key: 'toBizType', slotName: 'toBizType',
		width: 190,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '转入业务单据', dataIndex: 'toBizNo', key: 'toBizNo',
		width: 190,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '结转金额', dataIndex: 'carryoverAmount', key: 'carryoverAmount',
		width: 190,
		ellipsis: true,
		tooltip: true,
		align: 'center'
	},
	{
		title: '操作', dataIndex: 'operations',
		slotName: 'operations', fixed: 'right', width: 100, align: 'center'
	}
]

const fetchData = async () => {
	loading.value = true
	try {
		const params = {
			...queryParams,
			pageNum: pagination.current,
			pageSize: pagination.pageSize
		}
		const response = await listCarryover(params)
		
		if (response && response.data) {
			// 如果返回的数据在 data 属性中
			if (response.data.rows) {
				tableData.value = response.data.rows
				pagination.total = response.data.total || 0
			} else if (Array.isArray(response.data)) {
				tableData.value = response.data
				pagination.total = response.total || 0
			}
		} else if (response && response.rows) {
			// 直接返回 rows 格式
			tableData.value = response.rows
			pagination.total = response.total || 0
		} else {
			tableData.value = []
			pagination.total = 0
		}
	} catch (error) {
		console.error('获取结转列表失败', error)
		tableData.value = []
		pagination.total = 0
	} finally {
		loading.value = false
	}
}

const handleQuery = () => {
	pagination.current = 1
	fetchData()
}

const resetQuery = () => {
	queryParams.projectId = undefined
	queryParams.fromBizType = undefined
	queryParams.toBizType = undefined
	dateRange.value = []
	queryParams.carryoverTimeStart = ''
	queryParams.carryoverTimeEnd = ''
	pagination.current = 1
	fetchData()
}

const handleDateChange = (dates: any) => {
	if (dates && dates.length === 2) {
		queryParams.carryoverTimeStart = dates[0].format('YYYY-MM-DD')
		queryParams.carryoverTimeEnd = dates[1].format('YYYY-MM-DD')
	} else {
		queryParams.carryoverTimeStart = ''
		queryParams.carryoverTimeEnd = ''
	}
}

const onPageChange = (current: number) => {
	pagination.current = current
	fetchData()
}

const onPageSizeChange = (pageSize: number) => {
	pagination.pageSize = pageSize
	pagination.current = 1
	fetchData()
}

const handleDetail = (record: any) => {
	currentDetailId.value = record.id
	detailVisible.value = true
}

const handleExport = async () => {
	try {
		await exportCarryover({
			...queryParams,
			pageNum: pagination.current,
			pageSize: pagination.pageSize
		})
	} catch (error) {
		console.error('导出失败', error)
	}
}

onMounted(fetchData)
</script>

<style scoped>
.carryover-management {
	padding: 0 16px 16px 16px;

	.table-container {
		margin-top: 16px;
	}
}
</style>