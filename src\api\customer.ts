import http from './index';

// 客户管理相关类型定义

// 基础实体类型
export interface BaseEntity {
  createBy?: string;
  createByName?: string;
  createTime?: string;
  updateBy?: string;
  updateByName?: string;
  updateTime?: string;
  isDel?: boolean;
}

// 分页查询基础类型
export interface PageQuery {
  pageNum: number;
  pageSize: number;
  params?: Record<string, any>;
}

// 客户联系人类型
export interface CustomerContact extends BaseEntity {
  id?: string;
  customerId?: string;
  name?: string;
  phone?: string;
  gender?: number; // 1男 2女 3未知
  relationship?: number; // 关系（1夫妻 2合伙人 3父母 4朋友 5子女 6其他）
  idNumber?: string;
  position?: string; // 职务，仅企业客户适用
  department?: string; // 部门，仅企业客户适用
  isPreferred?: boolean; // 是否首选联系人
  remark?: string;
}

// 客户担保人类型
export interface CustomerGuarantor extends BaseEntity {
  id?: string;
  customerId?: string;
  name?: string;
  phone?: string;
  idType?: number; // 证件类型
  idNumber?: string;
  address?: string; // 地址
  remark?: string;
}

// 客户银行账号类型
export interface CustomerBankAccount extends BaseEntity {
  id?: string;
  customerId?: string;
  bankName?: string; // 开户银行
  accountNumber?: string; // 银行账号
  accountRemark?: string; // 账户备注
}

// 客户开票信息类型
export interface CustomerInvoice extends BaseEntity {
  id?: string;
  customerId?: string;
  title?: string; // 抬头名称
  taxNumber?: string; // 税号
  phone?: string;
  address?: string; // 单位地址
  bankName?: string; // 开户银行
  accountNumber?: string; // 银行账号
}

// 客户主表类型
export interface Customer extends BaseEntity {
  id?: string;
  projectId?: string; // 项目id
  projectName?: string; // 项目名称
  customerType?: number; // 客户类型，个人：1，企业：2
  customerName?: string; // 客户姓名
  creditCode?: string; // 统一社会信用代码
  legalName?: string; // 法人姓名
  contactPhone?: string; // 联系电话
  idType?: string; // 证件类型
  idNumber?: string; // 证件号码
  idValidityStart?: string; // 证件有效期开始
  idValidityEnd?: string; // 证件有效期结束
  idFront?: string; // 身份证正面地址
  idBack?: string; // 身份证反面地址
  businessLicense?: string; // 营业执照地址
  contactAddress?: string; // 联系地址
  ownerId?: string; // 维护人id
  ownerName?: string; // 维护人姓名
  attachmentFiles?: string | null; // 附件，支持null值
  remark?: string; // 备注
  contactList?: CustomerContact[]; // 联系人列表
  guarantorList?: CustomerGuarantor[]; // 担保人列表
  bankAccountList?: CustomerBankAccount[]; // 银行账号列表
  invoiceList?: CustomerInvoice[]; // 开票信息列表
}

// 客户查询DTO
export interface CustomerQueryDTO extends PageQuery {
  id?: string;
  projectId?: string;
  customerType?: number;
  customerName?: string;
  ownerName?: string;
  createByName?: string;
  phone?: string;
  creditCode?: string;
}

// 客户新增/编辑DTO
export interface CustomerAddDTO extends Customer {
  // 继承Customer的所有属性
}

// 客户维护人更新DTO
export interface CustomerUpdateDTO {
  customerIds: string[]; // 客户ID数组
  ownerId: string; // 维护人id
  ownerName: string; // 维护人姓名
}

// 客户简单信息VO（用于合同选择）
export interface CustomerSimpleVo {
  id?: string;
  customerName?: string;
  contactPhone?: string; // 联系电话，用于区分同名客户
}

// 客户详情VO
export interface CustomerVo extends Customer {
  // 继承Customer的所有属性，用于返回数据
}

// 客户联系人VO
export interface CustomerContactVo extends CustomerContact {
  // 继承CustomerContact的所有属性
}

// 客户担保人VO
export interface CustomerGuarantorVo extends CustomerGuarantor {
  // 继承CustomerGuarantor的所有属性
}

// 客户银行账号VO
export interface CustomerBankAccountVo extends CustomerBankAccount {
  // 继承CustomerBankAccount的所有属性
}

// 客户开票信息VO
export interface CustomerInvoiceVo extends CustomerInvoice {
  // 继承CustomerInvoice的所有属性
}

/**
 * 客户管理API接口
 */
export default {
  /**
   * 客户列表查询接口
   * @param params 查询参数
   * @returns 客户列表
   */
  getCustomerList(params: CustomerQueryDTO) {
    return http.post<CustomerVo[]>('/business-rent-admin/customer/list', params);
  },

  /**
   * 客户列表查询接口-合同
   * 用于合同模块选择客户
   * @param params 查询参数
   * @returns 客户简单信息列表
   */
  getCustomerListForContract(params: CustomerQueryDTO) {
    return http.post<CustomerSimpleVo[]>('/business-rent-admin/customer/listForContract', params);
  },

  /**
   * 客户详情接口
   * 根据客户ID查询客户完整信息，包括主表和子表数据
   * @param id 客户ID
   * @returns 客户详情
   */
  getCustomerDetail(id: string) {
    return http.get<CustomerVo>('/business-rent-admin/customer/detail', { id });
  },

  /**
   * 新增客户接口
   * 新增客户信息，包括主表和子表（联系人、担保人、银行账号、开票信息）
   * @param params 客户信息
   * @returns 操作结果
   */
  addCustomer(params: CustomerAddDTO) {
    return http.post<boolean>('/business-rent-admin/customer/add', params);
  },

  /**
   * 新增客户接口-合同
   * 新增客户信息，包括主表和子表（联系人、担保人、银行账号、开票信息）
   * @param params 客户信息
   * @returns 操作结果
   */
  addCustomerForContract(params: CustomerAddDTO) {
    return http.post<boolean>('/business-rent-admin/customer/saveForContract', params);
  },

  /**
   * 编辑客户接口
   * 编辑客户信息，包括主表和子表（联系人、担保人、银行账号、开票信息）
   * @param params 客户信息
   * @returns 操作结果
   */
  editCustomer(params: CustomerAddDTO) {
    return http.post<boolean>('/business-rent-admin/customer/edit', params);
  },

  /**
   * 更新维护人接口
   * 批量更新客户的维护人信息
   * @param params 更新参数
   * @returns 操作结果
   */
  updateCustomerOwner(params: CustomerUpdateDTO) {
    return http.post<boolean>('/business-rent-admin/customer/updateOwner', params);
  },
}; 