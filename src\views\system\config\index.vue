<template>
    <div class="common-page page-layout-column">
        <!-- 左侧菜单 -->
        <div class="page-layout-column-left">
            <div class="config-menu-container">
                <div class="config-menu-header">
                    <div class="config-menu-header-title">
                        <span class="common-header-tag"></span>
                        <span>业务参数列表</span>
                    </div>
                </div>
                <div class="config-menu-search">
                    <a-input
                        v-model="searchKeyword"
                        placeholder="请输入内容"
                        allow-clear
                        @input="handleSearch"
                    >
                        <template #prefix>
                            <icon-search />
                        </template>
                    </a-input>
                </div>
                <div class="config-menu-content">
                    <div class="config-menu-list">
                        <div
                            v-for="item in filteredMenuList"
                            :key="item.key"
                            class="common-flex config-menu-item"
                            :class="{ 'config-menu-item-active': selectedMenuKeys.includes(item.key) }"
                            @click="handleMenuClick(item.key)"
                        >
                            <div class="config-menu-item-left">{{ item.label }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="page-layout-column-right">
            <div class="common-card">
                <!-- 租赁中心配置 -->
                <div class="config-content" v-if="selectedMenuKeys.includes('leaseCenter')">
                    <!-- Tab切换 -->
                    <a-tabs
                        v-model:active-key="activeTabKey"
                        class="config-tabs"
                        @change="handleTabChange"
                        height="100%"
                    >
                        <a-tab-pane key="group" title="集团管控设置">
                            <GroupControlSettings />
                        </a-tab-pane>
                        <a-tab-pane key="project" title="项目管控设置">
                            <ProjectControlSettings />
                        </a-tab-pane>
                    </a-tabs>
                </div>

                <!-- 其他业务中心配置 -->
                <div v-else class="empty-config">
                    <a-empty description="暂无内容" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { IconSearch } from '@arco-design/web-vue/es/icon'
import GroupControlSettings from './components/GroupControlSettings.vue'
import ProjectControlSettings from './components/ProjectControlSettings.vue'

// 搜索关键词
const searchKeyword = ref('')

// 选中的菜单项
const selectedMenuKeys = ref(['leaseCenter'])

// 当前激活的tab
const activeTabKey = ref('group')

// 菜单列表
const menuList = ref([
    { key: 'assetPlatform', label: '资管平台' },
    { key: 'assetCenter', label: '资产中心' },
    { key: 'leaseCenter', label: '租赁中心' },
    { key: 'commissionCenter', label: '佣金中心' },
    { key: 'reportCenter', label: '报表中心' }
])

// 过滤后的菜单列表
const filteredMenuList = computed(() => {
    if (!searchKeyword.value) {
        return menuList.value
    }
    return menuList.value.filter(item =>
        item.label.includes(searchKeyword.value)
    )
})

// 处理搜索
const handleSearch = () => {
    // 搜索逻辑已在computed中处理
}

// 处理菜单点击
const handleMenuClick = (key: string) => {
    selectedMenuKeys.value = [key]
    // 这里可以根据选中的菜单项加载对应的配置数据
}

// 处理tab切换
const handleTabChange = (key: string) => {
    activeTabKey.value = key
}

onMounted(() => {
    // 初始化数据
})
</script>

<style scoped lang="less">
.page-layout-column {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .page-layout-column-left {
        width: 280px;
        height: 100%;
    }

    .page-layout-column-right {
        flex: 1;
        height: 100%;
        margin-left: 12px;
    }
}

.config-menu-container {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 12px;
    box-sizing: border-box;

    .config-menu-header {
        .config-menu-header-title {
            display: flex;
            align-items: center;
        }
    }

    .config-menu-search {
        margin-top: 12px;
    }

    .config-menu-content {
        margin-top: 12px;

        .config-menu-list {
            .config-menu-item {
                padding: 12px;
                border-bottom: 1px solid #f0f0f0;
                cursor: pointer;
                justify-content: space-between;

                &:hover {
                    background: var(--color-fill-2);
                }

                .config-menu-item-left {
                    flex: 1;
                }
            }

            .config-menu-item-active {
                background: rgb(var(--arcoblue-6));
                color: #fff;

                &:hover {
                    background: rgb(var(--arcoblue-6));
                }
            }
        }
    }
}

.config-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
    :deep(.arco-tabs-content){
        flex: 1;
        overflow: auto;
    }
}
.config-content{
    height: 100%;
}
.empty-config {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}
</style>