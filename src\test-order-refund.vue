<template>
  <div class="test-order-refund">
    <h1>订单退款申请单功能测试</h1>
    
    <div class="test-section">
      <h2>功能说明</h2>
      <ul>
        <li>✅ 从订单管理页面发起退款申请</li>
        <li>✅ 自动获取订单详情并填充表单</li>
        <li>✅ 支持退款金额、退款方式、收款信息等编辑</li>
        <li>✅ 表单验证（必填项、金额范围等）</li>
        <li>✅ 文件上传功能</li>
        <li>✅ 暂存和提交审批功能</li>
        <li>✅ 打印预览功能</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>使用流程</h2>
      <ol>
        <li>在订单管理页面找到状态为"已生效"的订单</li>
        <li>点击操作列中的"退款申请"按钮</li>
        <li>系统自动获取订单详情并打开退款申请单抽屉</li>
        <li>填写退款申请信息：
          <ul>
            <li>退款金额（默认为已收金额，可修改但不能超过已收金额）</li>
            <li>退款方式（原路退回/银行转账）</li>
            <li>收款方信息（姓名、银行、账号）</li>
            <li>退款申请说明（必填）</li>
            <li>附件（可选）</li>
          </ul>
        </li>
        <li>选择操作：
          <ul>
            <li>暂存：保存为草稿状态</li>
            <li>发起审批：提交审批流程</li>
            <li>预览&打印：打印退款申请单</li>
          </ul>
        </li>
      </ol>
    </div>

    <div class="test-section">
      <h2>API接口</h2>
      <ul>
        <li><code>POST /rent-admin/financialRefund/save</code> - 保存退款单</li>
        <li><code>GET /rent-admin/order/detail</code> - 获取订单详情</li>
        <li><code>POST /rent-admin/common/upload</code> - 文件上传</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>数据字典</h2>
      <h3>退款类型</h3>
      <ul>
        <li>1: 退定退款（订单管理中固定为此类型）</li>
      </ul>
      
      <h3>退款方式</h3>
      <ul>
        <li>0: 原路退回</li>
        <li>1: 银行转账</li>
      </ul>
      
      <h3>退款状态</h3>
      <ul>
        <li>0: 草稿（暂存）</li>
        <li>1: 待退款（已提交审批）</li>
      </ul>
      
      <h3>审批状态</h3>
      <ul>
        <li>0: 草稿（暂存）</li>
        <li>1: 审批中（已提交审批）</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>表单验证规则</h2>
      <ul>
        <li>退款金额：必填，必须大于0，不能超过已收金额</li>
        <li>退款方式：必填</li>
        <li>收款人姓名：必填</li>
        <li>退款申请说明：必填</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>文件上传</h2>
      <ul>
        <li>支持格式：jpg、jpeg、png、pdf、doc、docx</li>
        <li>最多上传：5个文件</li>
        <li>文件大小：单个文件不超过10MB</li>
        <li>上传接口：/rent-admin/common/upload</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>测试步骤</h2>
      <ol>
        <li>访问订单管理页面：<code>/orderManagement/list</code></li>
        <li>切换到"生效中"页签</li>
        <li>找到有已收金额的订单</li>
        <li>点击"退款申请"按钮</li>
        <li>验证表单数据自动填充</li>
        <li>测试表单验证</li>
        <li>测试文件上传</li>
        <li>测试暂存功能</li>
        <li>测试提交审批功能</li>
        <li>在退款管理页面查看创建的退款单</li>
      </ol>
    </div>

    <div class="test-section">
      <h2>注意事项</h2>
      <ul>
        <li>只有状态为"已生效"的订单才显示"退款申请"按钮</li>
        <li>退款金额默认等于已收金额，但可以修改</li>
        <li>退款金额不能超过已收金额</li>
        <li>收款人姓名默认为客户名称</li>
        <li>退款对象自动生成为"客户名称 + 房源名称"</li>
        <li>暂存的退款单可在退款管理页面继续编辑</li>
        <li>已提交审批的退款单将进入审批流程</li>
      </ul>
    </div>

    <div class="test-actions">
      <a-button type="primary" @click="goToOrderList">
        前往订单管理页面测试
      </a-button>
      <a-button @click="goToRefundManage" style="margin-left: 16px;">
        查看退款管理页面
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToOrderList = () => {
  router.push('/orderManagement/list')
}

const goToRefundManage = () => {
  router.push('/financeManage/refundManage')
}
</script>

<style scoped lang="less">
.test-order-refund {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    color: #1d2129;
    margin-bottom: 24px;
    text-align: center;
  }

  .test-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      color: #1d2129;
      margin-bottom: 16px;
      border-bottom: 2px solid #165dff;
      padding-bottom: 8px;
    }

    h3 {
      color: #4e5969;
      margin: 16px 0 8px 0;
    }

    ul, ol {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }

    code {
      background: #f7f8fa;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Monaco', 'Consolas', monospace;
      color: #e74c3c;
    }
  }

  .test-actions {
    text-align: center;
    margin-top: 32px;
  }
}
</style> 