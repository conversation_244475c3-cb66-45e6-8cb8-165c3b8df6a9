<template>
    <section>
        <sectionTitle title="保证金信息" />
        <div class="content">
            <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">应收日期</label>
                        <div class="detail-text">
                            <template v-if="contractData.bondReceivableType === 0">
                                合同签订后{{ contractData.bondReceivableDate }}天
                            </template>
                            <template v-else>
                                指定日期：{{ contractData.bondReceivableDate || '--' }}
                            </template>
                        </div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">保证金金额</label>
                        <div class="detail-text">{{bondPriceTypeOptions.find(item => item.value ===
                            contractData.bondPriceType)?.label || '--'}}</div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">应收金额</label>
                        <div class="detail-text">{{ contractData.bondPrice == null ? '--' :
                            formatAmount(contractData.bondPrice) + '元' }}</div>
                    </div>
                </a-grid-item>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import { ContractAddDTO } from '@/api/contract'

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const bondPriceTypeOptions = [
    { label: '自定义', value: 0 },
    { label: '押1个月', value: 1 },
    { label: '押2个月', value: 2 },
    { label: '押3个月', value: 3 },
    { label: '押4个月', value: 4 },
    { label: '押5个月', value: 5 },
    { label: '押6个月', value: 6 },
    { label: '押7个月', value: 7 },
    { label: '押8个月', value: 8 },
    { label: '押9个月', value: 9 },
    { label: '押10个月', value: 10 },
    { label: '押11个月', value: 11 },
    { label: '押12个月', value: 12 },
    { label: '按房源保证金', value: 30 },
]
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.form-item {
    margin-bottom: 16px;

    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>