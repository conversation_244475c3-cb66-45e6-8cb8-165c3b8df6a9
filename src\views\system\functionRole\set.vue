<template>
    <div class="data-role-set-container">
        <div class="data-role-set-content">
            <div class="menu-list">
                <div v-for="item in menuTreeList" :key="item.menuId" class="menu-block">
                    <div class="menu-header">
                        <a-checkbox :model-value="menuIds.includes(item.id)" @change="handleMenuChange(item)">{{
                            item.label }}</a-checkbox>
                    </div>
                    <div v-if="item.children && item.children.length" class="menu-item">
                        <div v-for="child in item.children" :key="child.menuId" class="menu-line">
                            <div class="menu-left">
                                <div class="menu-one">
                                    <a-checkbox :model-value="menuIds.includes(child.id)"
                                        @change="handleMenuChange(child)">{{ child.label }}</a-checkbox>
                                </div>
                            </div>
                            <div v-if="child.children && child.children.length" class="menu-right">
                                <div class="menu-grand" v-for="grandChild in child.children" :key="grandChild.menuId"
                                    :class="{ 'menu-grand-last': !grandChild.children }">
                                    <div class="menu-one">
                                        <a-checkbox :model-value="menuIds.includes(grandChild.id)"
                                            @change="handleMenuChange(grandChild)">{{ grandChild.label }}</a-checkbox>
                                    </div>
                                    <div v-if="grandChild.children && grandChild.children.length" class="menu-last">
                                        <div v-for="greatGrandChild in grandChild.children"
                                            :key="greatGrandChild.menuId" class="">
                                            <div class="menu-one">
                                                <a-checkbox :model-value="menuIds.includes(greatGrandChild.id)"
                                                    @change="handleMenuChange(greatGrandChild)">{{ greatGrandChild.label
                                                    }}</a-checkbox>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="common-flex data-role-set-footer">
            <a-button v-permission="['system:menu:role:bind']" type="primary" @click="handleSave">保存更新</a-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, defineComponent } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getMenuList, getMenuTreeList } from '@/api/menu'
import { getRoleDetail, bindRoleMenu, getRoleMenuList } from '@/api/role'

const props = defineProps({
    roleId: {
        type: String,
        required: true
    }
})

const menuIds = ref<any[]>([])
const menuTreeList = ref<any[]>([])
const handleMenuChange = (item: any) => {
    console.log(item, 'item')
    let isInclude = menuIds.value.includes(item.id)
    if (isInclude) {
        menuIds.value = menuIds.value.filter((id) => id !== item.id)
    } else {
        menuIds.value.push(item.id)
    }
    if (item.children) {
        item.children.forEach((child: any) => {
            if (isInclude) {
                menuIds.value = menuIds.value.filter((id) => id !== child.id)
            } else {
                menuIds.value.push(child.id)
            }
            if (child.children) {
                child.children.forEach((grandChild: any) => {
                    if (isInclude) {
                        menuIds.value = menuIds.value.filter((id) => id !== grandChild.id)
                    } else {
                        menuIds.value.push(grandChild.id)
                    }
                    if (grandChild.children) {
                        grandChild.children.forEach((greatGrandChild: any) => {
                            if (isInclude) {
                                menuIds.value = menuIds.value.filter((id) => id !== greatGrandChild.id)
                            } else {
                                menuIds.value.push(greatGrandChild.id)
                            }
                        })
                    }
                })
            }
        })
    }
}

const getMenuTreeListMethod = async () => {
    const { data } = await getMenuTreeList()
    console.log(data, 'data')

    menuTreeList.value = data
}
// 获取角色详情
const getRoleDetailMethod = async () => {
    const { data } = await getRoleMenuList({ roleId: props.roleId })
    console.log('getRoleMenuList', data)
    menuIds.value = data
    // menuIds
    // if (data) {
    //     if (data.roomPermissions) { 
    //         roomPermissions.value = data.roomPermissions.split(',') || []
    //     } else {
    //         roomPermissions.value = []
    //     }
    //     if (data.contractPermissions) {
    //         contractPermission.value = data.contractPermissions || ''
    //     } else {
    //         contractPermission.value = ''
    //     }
    // }
}

const fetchData = () => {
    getRoleDetailMethod()
    getMenuTreeListMethod()
}
// {
//   "roleId": 0,
//   "menuIdList": [
//     0
//   ]
// }
const handleSave = () => {
    // const menuIds = menuTreeList.value.map((item) => item.menuId)
    // 去重
    menuIds.value = [...new Set(menuIds.value)]
    bindRoleMenu({
        roleId: props.roleId,
        menuIdList: menuIds.value
    })
    Message.success('保存成功');
}
defineExpose({
    fetchData
})
</script>
<script lang="ts">
export default { name: 'functionRoleSetComponent' }
</script>

<style scoped lang="less">
.data-role-set-container {
    width: 100%;
    height: calc(100vh - 180px);

    .data-role-set-content {
        height: calc(100% - 50px);
        box-sizing: border-box;
        overflow-y: auto;

        .menu-list {
            box-sizing: border-box;
            padding: 0 12px;

            .menu-one {
                padding: 10px;
            }

            .menu-block {
                width: 100%;

                .menu-header {
                    width: 100%;
                    background-color: rgb(var(--gray-1));
                    padding: 14px 0;
                    color: rgb(var(--gray-10));
                    font-size: 15px;
                    font-weight: 500;
                }

                .menu-item {
                    width: 100%;
                    border: 1px solid rgb(var(--gray-3));
                    border-radius: 4px;
                }
            }


            .menu-line {
                width: 100%;
                border-bottom: 1px solid rgb(var(--gray-3));
                display: flex;
                align-content: center;

                &:last-child {
                    border-bottom: none;
                }

                .menu-left {
                    width: 200px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-sizing: border-box;
                    padding: 10px;
                    border-right: 1px solid rgb(var(--gray-3));
                }

                .menu-right {
                    width: 0;
                    flex: 1;

                    .menu-grand {
                        width: 100%;
                        display: flex;
                        border-bottom: 1px solid rgb(var(--gray-3));

                        .menu-last {
                            border-left: 1px solid rgb(var(--gray-3));
                            display: flex;
                        }

                        &:last-child {
                            border-bottom: none;
                        }
                    }

                    .menu-grand-last {
                        display: inline-block;
                        width: auto;
                        border-bottom: none;
                    }
                }
            }
        }
    }

    .data-role-set-footer {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
    }
}
</style>
