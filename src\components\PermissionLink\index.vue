<template>
  <a-link
    v-if="hasPermission"
    v-bind="$attrs"
    @click="handleClick"
  >
    <slot></slot>
  </a-link>
  <span
    v-else
    class="permission-link-disabled"
    :class="{ 'permission-link-disabled-clickable': allowClickWithoutPermission }"
    @click="handleDisabledClick"
  >
    <slot></slot>
  </span>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useUserStore } from '@/store';

interface Props {
  permissions?: string | string[];
  allowClickWithoutPermission?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  permissions: '',
  allowClickWithoutPermission: false
});

const emit = defineEmits<{
  click: [event: Event];
  disabledClick: [event: Event];
}>();

const userStore = useUserStore();

// 检查权限
const hasPermission = computed(() => {
  if (!props.permissions) {
    return true;
  }

  const userPermissions = userStore.permissions;
  
  // 如果用户拥有超级权限，直接返回true
  if (userPermissions.includes("*:*:*")) {
    return true;
  }

  // 处理单个权限或权限数组
  if (typeof props.permissions === 'string') {
    return userPermissions.includes(props.permissions);
  }
  
  if (Array.isArray(props.permissions)) {
    return props.permissions.some(permission => 
      userPermissions.includes(permission)
    );
  }

  return false;
});

// 处理有权限时的点击
const handleClick = (event: Event) => {
  emit('click', event);
};

// 处理无权限时的点击
const handleDisabledClick = (event: Event) => {
  if (props.allowClickWithoutPermission) {
    emit('disabledClick', event);
  }
};
</script>

<style scoped>
.permission-link-disabled {
  color: var(--color-text-3);
  cursor: default;
  user-select: none;
}

.permission-link-disabled-clickable {
  cursor: pointer;
}

.permission-link-disabled-clickable:hover {
  color: var(--color-text-2);
}
</style> 