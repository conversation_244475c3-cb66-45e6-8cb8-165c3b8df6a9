<template>
    <div class="hydropower-alert">
        <div class="alert-title">
            <div class="title-decorator"></div>
            水电预警
        </div>
        <div class="alert-cards">
            <div class="alert-card" v-for="item in cards" :key="item.period">
                <div class="period-badge">{{ item.period }}</div>
                <div class="alert-content">
                    <div class="alert-item">
                        <div class="alert-text">
                            <div class="alert-label"><img class="alert-icon" :src="alertIcon1" alt="异常" />异常</div>
                            <div class="alert-number">{{ item.abnormal }}</div>
                        </div>
                    </div>
                    <div class="alert-item">
                        <div class="alert-text">
                            <div class="alert-label"><img class="alert-icon" :src="alertIcon2" alt="抽检未通过" />抽检未通过</div>
                            <div class="alert-number">{{ item.checkFailed }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import alertIcon1 from '@/assets/images/dashboard/alert-1.png'
import alertIcon2 from '@/assets/images/dashboard/alert-2.png'

const cards = ref([
    {
        period: '本周',
        abnormal: 10,
        checkFailed: 2,
    },
    {
        period: '本月',
        abnormal: 20,
        checkFailed: 3,
    },
])
</script>

<style lang="less" scoped>
.hydropower-alert {
    background: #fff;
    border-radius: 10px;
    padding: 20px 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .alert-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
        color: #000;
        position: relative;
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .title-decorator {
            width: 5px;
            height: 19px;
            background-color: #0071ff;
            border-radius: 4px 0px 4px 0px;
            margin-right: 10px;
        }
    }

    .alert-cards {
        display: flex;
        flex-direction: column;
        height: 100%;
        gap: 8px;
        flex: 1;
    }

    .alert-card {
        position: relative;
        height: 100%;
        border-radius: 10px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: url('@/assets/images/dashboard/alert-bg.png') no-repeat center center;
        background-size: 100% 100%;
        padding: 0 16px;
        gap: 16px;

        .period-badge {
            flex-shrink: 0;
            flex-grow: 0;
            width: 46px;
            height: 46px;
            background: #EDF5FF;
            border: 3px solid #D3D9DF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #5E6874;
            z-index: 2;
        }

        .alert-content {
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;
            gap: 16px;
            z-index: 2;
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: 6px;

            .alert-icon {
                width: 13px;
                height: 14px;
                object-fit: contain;
            }

            .alert-text {
                text-align: center;

                .alert-label {
                    font-size: 14px;
                    color: #3A4252;
                    line-height: 1.4;
                    display: flex;
                    align-items: center;
                    gap: 3px;
                    margin-bottom: 4px;
                }

                .alert-number {
                    font-size: 20px;
                    font-weight: bold;
                    padding-left: 16px;
                }
            }
        }
    }
}
</style>