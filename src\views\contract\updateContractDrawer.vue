<template>
    <a-drawer class="common-drawer" v-model:visible="drawerVisible" :title="drawerTitle" :body-style="{ padding: 0 }"
        unmount-on-close>
        <a-card class="flex-card" :bordered="false" :header-style="{ border: 'none' }"
            :body-style="{ padding: '16px 16px' }">
            <baseInfo v-if="drawerVisible" ref="baseInfoRef" />
        </a-card>
        <template #footer>
            <a-button type="primary" @click="handleSave('save')" :loading="submitLoading">保存并更新</a-button>
            <a-button @click="drawerVisible = false">取消</a-button>
        </template>
    </a-drawer>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue';
import baseInfo from './addContract/baseInfo/index.vue';
import { useContractStore } from '@/store/modules/contract/index';
// 导入合同API接口和类型定义
import {
    saveContract,
    getContractById,
    ContractAddDTO,
    ContractVo,
    getContractPermission,
    modifyContract
} from '@/api/contract';
import { getProjectDetail } from '@/api/project';
import dayjs from 'dayjs';
import useUserStore from '@/store/modules/user';

// 引用子组件
const baseInfoRef = ref();
const modifyType = ref(0)
const drawerVisible = ref(false);


const contractStore = useContractStore();
const contractData = computed(
    () => contractStore.contractData as ContractAddDTO
);

const userStore = useUserStore();

// 打开抽屉，设置编辑类型，并初始化数据
const open = async (
    type = 'create',
    contractType: number = 1,
    record: any,
    project?: any,
    fromDetailPage: boolean = false
) => {
    contractStore.editType = type;
    drawerVisible.value = true;

    // 对于变更类型，在resetContract之前保存changeType
    const savedChangeType =
        type === 'change' ? [...contractStore.changeType] : [];
    contractStore.resetContract();

    // 如果是变更类型且有保存的changeType，恢复它
    if (type === 'change' && savedChangeType.length > 0) {
        contractStore.changeType = savedChangeType;
    }

    contractData.value.activityDate = [];
    contractData.value.signerName = userStore.nickName;
    contractData.value.signerId = userStore.id;
    contractData.value.contractType = contractType;
    contractData.value.signDate = dayjs().format('YYYY-MM-DD');
    contractData.value.handoverDate = dayjs().format('YYYY-MM-DD');
    contractData.value.projectId =
        type === 'create' ? project?.id : record.projectId;
    contractData.value.projectName =
        type === 'create' ? project?.name : record.projectName;
    contractStore.setCurrentProjectId(
        contractData.value.projectId as string
    );

    //保证金信息  应收日期默认写死字段
    contractData.value.bondReceivableType = 0;
    contractData.value.bondReceivableDate = '3';

    getUserPermission();
    getProjectDetailData(contractData.value.projectId as string);

    // 如果是直接签约且record包含房源信息（从租控管理的租客签约进来）
    if (type === 'create' && record && project && record.isDirectContract) {
        // 存储房源ID到store中，供roomInfo组件使用
        contractStore.preSelectedRoomId = record.roomId;
    }
    // 如果是从订单转签约(record有订单信息且project存在)
    else if (
        type === 'create' &&
        record &&
        project &&
        record.id &&
        record.isRenewalContract
    ) {
        // 存储房源ID到store中，供roomInfo组件使用
        contractStore.preSelectedRoomId = record.roomId;
        contractStore.editType = 'toSign';
        // 设置关联订单信息
        contractData.value.bookingRelType = 2; // 其他定单
        contractData.value.bookings = [
            {
                bookingId: record.id,
                bookedRoom: record.roomName || record.intentRoom,
                bookerName: record.customerName,
                bookingReceivedAmount:
                    record.receivedAmount ||
                    record.depositAmount ||
                    record.bookingAmount,
                bookingPaymentDate:
                    record.receivedDate || record.createTime,
            },
        ];

        // 设置客户信息
        // if (record.customerName) {
        //     contractData.value.customer = {
        //         customerName: record.customerName,
        //         // 其他客户信息可以根据需要添加
        //     }
        // }
    }

    if (
        type === 'updateBank' ||
        type === 'updateContact' ||
        type === 'updateSignMethod' ||
        type === 'updateRentNo'
    ) {
        // 更新付款账号 更新联系人 更新承租人手机号 更新签约方式，需要获取原合同详情
        if (record.id) {
            fetchContractDetail(record.id, type, fromDetailPage);
        }
    }
};

const landUsageMap = new Map([
    [1, '工业用地'],
    [2, '商业用地'],
    [3, '商住用地'],
]);
const getProjectDetailData = async (projectId: string) => {
    const { data } = await getProjectDetail(projectId);
    contractStore.landUsageList = data.landUsageList
        ? data.landUsageList.map((item: any) => ({
            label: landUsageMap.get(item),
            value: item,
        }))
        : [];
    contractStore.merchantList = data.merchantList ?? [];
    if (data.merchantList && data.merchantList.length > 0) {
        contractData.value.ourSigningParty =
            data.merchantList[0].orgCompanyName;
    }
    if (contractStore.landUsageList.length > 0) {
        contractData.value.landUsage = contractStore.landUsageList[0].label;
    }
};

// 获取用户相关权限
const getUserPermission = async () => {
    const { data } = await getContractPermission();
    // TODO: 非标合同权限
    data.canSelectNonStandard = true;
    contractStore.contractPermission = data;
};

// 获取合同详情
const fetchContractDetail = async (
    id: string,
    type: string,
    fromDetailPage: boolean
) => {
    try {
        const { data } = await getContractById(id);
        if (data) {
            // 更新合同数据
            const contractInfo = data as ContractVo;

            contractInfo.fees = contractInfo.fees || [];
            // 对优惠条款按开始日期正序排列
            if (contractInfo.fees.length > 0) {
                contractInfo.fees.sort((a, b) => {
                    const dateA = dayjs(a.startDate);
                    const dateB = dayjs(b.startDate);
                    return dateA.isBefore(dateB) ? -1 : 1;
                });
            }

            contractInfo.costs = contractInfo.costs || [];
            contractInfo.rooms = contractInfo.rooms || [];
            contractInfo.rentReceivableDate = Number(
                contractInfo.rentReceivableDate
            );
            if (
                contractInfo.dailyActivityEndTime &&
                contractInfo.dailyActivityStartTime
            ) {
                (contractInfo as any).activityDate = [
                    contractInfo.dailyActivityStartTime,
                    contractInfo.dailyActivityEndTime,
                ];
            }

            // 编辑时直接使用原数据
            contractStore.contractData = {
                ...contractStore.contractData,
                ...(contractInfo as any),
            };

            //保证金信息  应收日期默认写死字段
            contractStore.contractData.bondReceivableType = 0;
            contractStore.contractData.bondReceivableDate = '3';

            // 合同变更新增的时候-变更执行日期需要默认当天，当前是空的 ,合同变更编辑的时候，则不要做此处理
            if (type === 'change' && !fromDetailPage) {
                contractStore.contractData.changeDate =
                    dayjs().format('YYYY-MM-DD');
            }
        }
    } catch (error) {
        console.error(error);
    }
};

// 抽屉标题
const drawerTitle = computed(() => {
    switch (contractStore.editType) {
        case 'updateBank':
            modifyType.value = 0
            return '更新付款账号';
        case 'updateContact':
            modifyType.value = 1
            return '更新联系人';
        case 'updateSignMethod':
            modifyType.value = 3
            return '更新签约方式';
        case 'updateRentNo':
            modifyType.value = 2
            return '更新承租人手机号';
        default:
            return '新增合同';
    }
});

const getParams = (data: any) => {
    if (data.rooms && data.rooms.length > 0) {
        data.rooms.forEach((item: any) => {
            item.startDate = item.startDate
                ? dayjs(item.startDate).format('YYYY-MM-DD')
                : '';
            item.endDate = item.endDate
                ? dayjs(item.endDate).format('YYYY-MM-DD')
                : '';
            delete item.id;
        });
    }
    if (data.fees && data.fees.length > 0) {
        data.fees.forEach((item: any) => {
            delete item.id;
            item.startDate = item.startDate
                ? dayjs(item.startDate).format('YYYY-MM-DD')
                : '';
            item.endDate = item.endDate
                ? dayjs(item.endDate).format('YYYY-MM-DD')
                : '';
        });
    }
    if (data.bookings && data.bookings.length > 0) {
        data.bookings.forEach((item: any) => {
            delete item.id;
        });
    }
    if (data.costs && data.costs.length > 0) {
        data.costs.forEach((item: any) => {
            delete item.id;
        });
    }
    if (data.activityDate && data.activityDate.length > 0) {
        data.dailyActivityStartTime = data.activityDate[0];
        data.dailyActivityEndTime = data.activityDate[1];
        delete data?.activityDate;
    }
    delete data?.createTime;
    delete data?.customer?.id;

    // 转换为API所需的数据格式
    const apiData = data;

    // 设置提交状态（暂存=false, 提交=true）
    apiData.isSubmit = false;

    console.log('保存合同数据:', apiData);
    return apiData;
};

// 保存状态
const saveLoading = ref(false);
// 添加提交审批的loading状态
const submitLoading = ref(false);
const emits = defineEmits(['submit']);

// 保存合同数据
const handleSave = async (type: string = 'save') => {
    return new Promise(async (resolve, reject) => {
        if (saveLoading.value) {
            reject(new Error('操作正在进行中，请勿重复提交'));
            return; // 防止重复提交
        }

        saveLoading.value = true;

        try {
            const params = JSON.parse(
                JSON.stringify(contractData.value)
            ) as ContractAddDTO;

            params.id = contractStore.contractData.id;

            const apiData = getParams(params);

            apiData.isSubmit = false
            // 调用API保存合同数据
            const api = modifyContract;

            console.log('调用API前的数据:', {
                editType: contractStore.editType,
                apiDataId: apiData.id,
                contractDataId: contractStore.contractData.id,
                api: api.name,
            });
            const { data: result, code, msg } = await api(apiData, modifyType.value);

            if (code === 200) {
                Message.success('保存成功');
                // 获取合同详情
                await fetchContractDetail(contractStore.contractData.id as string, type, false);
                emits('submit');
                resolve(true);
            } else {
                // API调用失败的情况
                const errorMessage = msg || '保存失败';
                Message.error(errorMessage);
                reject(new Error(errorMessage));
            }
        } catch (error) {
            console.error('保存失败:', error);
            reject(error);
        } finally {
            saveLoading.value = false;
        }
    });
};

defineExpose({
    open,
});
</script>

<style lang="less" scoped>
.arco-card {
    :deep(.arco-steps) {
        .arco-steps-item {
            justify-content: center;
            background: linear-gradient(270deg, #dce3ea 0%, #e9edf4 100%);

            &::after {
                border-left: 20px solid #dce3ea !important;
            }

            &.arco-steps-item-active,
            &.arco-steps-item-finish {
                .arco-steps-item-title {
                    color: rgb(var(--success-6));
                }
            }
        }

        .arco-steps-item-title {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            gap: 8px;
        }
    }
}
</style>
