<template>
  <a-drawer
    v-model:visible="visible"
    title="宿舍户型配置"
    class="common-drawer-small"
    :footer="false"
    @cancel="handleCancel"
  >
    <div class="room-config-container">
      <div class="room-info">
        <section-title title="宿舍户型信息" class="first-child" />
        <div class="room-types" :class="{ loading: loading }" v-loading="loading">
          <div v-for="(item, index) in roomTypeList" :key="item.id || index" class="room-type-card">
            <div class="room-type-header">
              <div class="room-type-name">{{ item.houseTypeName }}</div>
              <div class="room-type-status">{{ getDecorationType(item.decorationType) }}</div>
            </div>
            <div class="room-info-row">
              <div class="room-structure">
                <icon-interaction />{{ item.roomNum }}室{{ item.hallNum }}厅{{ item.toiletNum }}卫
              </div>
              <div class="room-capacity">
                <icon-user-group />可住{{ item.livableNum }}人
              </div>
            </div>
            <div class="room-actions">
              <a-button type="text" @click="handleEditRoomType(item)">
                <template #icon>
                    <icon-edit />
                </template>
                编辑
              </a-button>
              <a-button type="text" status="danger" @click="handleDeleteRoomType(item)">
                <template #icon>
                    <icon-delete />
                </template>
                删除
              </a-button>
            </div>
          </div>

          <!-- 添加户型按钮 -->
          <div class="add-room-type" @click="handleAddRoomType">
            <icon-plus-circle class="icon-plus-circle" />
            <span>添加户型</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 户型编辑弹窗 -->
    <room-type-edit-dialog
      v-model:visible="roomTypeEditVisible"
      :title="editDialogTitle"
      :data="currentRoomType ?? undefined"
      :project-id="projectId"
      @save="handleRoomTypeSave"
    />
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import RoomTypeEditDialog from './RoomTypeEditDialog.vue'
import { useDictSync, getDictLabel } from '@/utils/dict'
import { 
  getHouseTypeList, 
  deleteHouseType,
  type ProjectHouseTypeVo 
} from '@/api/project'

interface Props {
  projectId?: string
}

const props = defineProps<Props>()

const visible = ref(false)
const loading = ref(false)

// 户型配置相关
const roomTypeList = ref<ProjectHouseTypeVo[]>([])
const roomTypeEditVisible = ref(false)
const currentRoomType = ref<ProjectHouseTypeVo | null>(null)
const editDialogTitle = ref('')

// 装修类型映射
const getDecorationType = (type: number | undefined) => {
  if (!type) return '未知'
  return getDictLabel(decorationTypeOptions.value, String(type))
}
const emit = defineEmits(['cancel']) 
// 数据字典相关
const decorationTypeOptions = ref<any[]>([])

// 加载数据字典
const loadDictData = async () => {
  try {
    // 加载装修类型字典
    const dictData = await useDictSync('decoration_type')
    if (dictData.decoration_type) {
      decorationTypeOptions.value = dictData.decoration_type
    }
  } catch (error) {
    console.error('加载数据字典失败:', error)
  }
}

// 加载户型列表
const loadHouseTypeList = async () => {
  if (!props.projectId) return
  
  try {
    loading.value = true
    const response = await getHouseTypeList(props.projectId)
    
    if (response && response.data) {
      roomTypeList.value = response.data
    }
  } catch (error) {
    console.error('加载户型列表失败:', error)
    roomTypeList.value = []
  } finally {
    loading.value = false
  }
}



const handleAddRoomType = () => {
  currentRoomType.value = null
  editDialogTitle.value = '新增户型'
  roomTypeEditVisible.value = true
}

const handleEditRoomType = (item: ProjectHouseTypeVo) => {
  currentRoomType.value = { ...item }
  editDialogTitle.value = '编辑户型'
  roomTypeEditVisible.value = true
}

const handleDeleteRoomType = (item: ProjectHouseTypeVo) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该户型吗？删除后无法恢复。',
    onOk: async () => {
      try {
        if (!item.id) {
          Message.error('户型ID不存在')
          return
        }
        
        await deleteHouseType(item.id)
        Message.success('删除成功')
        
        // 重新加载列表
        loadHouseTypeList()
      } catch (error) {
        console.error('删除户型失败:', error)
      }
    }
  })
}

const handleRoomTypeSave = () => {
  // 保存成功后重新加载列表
  loadHouseTypeList()
  roomTypeEditVisible.value = false
}

const handleCancel = () => {
  setTimeout(() => {
    emit('cancel')
  }, 300);
  visible.value = false
}

onMounted(() => {
  loadHouseTypeList()
  loadDictData()
})

// 暴露方法给父组件
defineExpose({
  show: () => {
    visible.value = true
  }
})
</script>

<style scoped lang="less">
.room-config-container {
  height: 100%;
  overflow-y: auto;
  .section-title {
    margin: 16px 0;
    &.first-child{
      margin-top: 8px;
    }
  }
}

.room-types {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 0 0 16px 0;
  justify-content: flex-start;
  .room-type-card {
    flex: 0 0 calc(33.333% - 16px);
    height: 115px;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    padding: 12px 0 0 10px;
    background: var(--color-bg-2);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .room-type-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 12px;
      .room-type-name {
        margin: 0;
        font-size: 16px;
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei-Bold;
        font-weight: Bold;
        text-align: left;
        color: #333333;
      }

      .room-type-status {
        font-size: 12px;
        color: rgb(var(--warning-6));
        background: rgb(var(--warning-2));
        border-radius: 4px;
        padding: 2px 10px;
        font-size: 12px;
        font-family: Microsoft YaHei, Microsoft YaHei-Regular;
        font-weight: Regular;
      }
    }

    .room-info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: var(--color-text-3);
      padding-right: 12px;
      margin: 7px 0 11px 0;
      .room-structure {
        font-size: 14px;
      }

      .room-capacity {
        font-size: 14px;
      }
    }

    .room-actions {
      display: flex;
      gap: 8px;
      height: 40px;
      border-top: 1px solid var(--color-border);
      :deep(.arco-btn) {
        height: 40px;
        font-size: 14px;
        color: rgb(var(--primary-6));
        .arco-btn-icon{
          margin-right: 4px;
        }
      }
    }
  }

  .add-room-type {
    flex: 0 0 calc(33.333% - 16px);
    height: 115px;
    border: 1px dashed rgb(var(--primary-6));
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: rgb(var(--primary-6));
    font-size: 14px;
    &:hover {
      border-color: var(--color-primary);
      color: var(--color-primary);
    }

    .icon-plus-circle {
      font-size: 32px;
      margin-bottom: 8px;
    }
  }
}
</style> 