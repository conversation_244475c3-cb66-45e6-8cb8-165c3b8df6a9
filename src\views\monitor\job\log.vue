<template>
   <div class="container">
      <a-card class="general-card" title="调度日志">
         <!-- 搜索表单 -->
         <a-row>
            <a-col :flex="1">
               <a-form ref="queryRef" :model="queryParams" :label-col-props="{ span: 6 }"
                  :wrapper-col-props="{ span: 18 }" label-align="right">
                  <a-row :gutter="16">
                     <a-col :span="8">
                        <a-form-item label="任务名称" field="jobName">
                           <a-input v-model="queryParams.jobName" placeholder="请输入任务名称" allow-clear />
                        </a-form-item>
                     </a-col>
                     <a-col :span="8">
                        <a-form-item label="任务组名" field="jobGroup">
                           <a-select v-model="queryParams.jobGroup" placeholder="请选择任务组名" allow-clear>
                              <a-option v-for="dict in sys_job_group" :key="dict.value" :value="dict.value">
                                 {{ dict.label }}
                              </a-option>
                           </a-select>
                        </a-form-item>
                     </a-col>
                     <a-col :span="8">
                        <a-form-item label="执行状态" field="status">
                           <a-select v-model="queryParams.status" placeholder="请选择执行状态" allow-clear>
                              <a-option v-for="dict in sys_common_status" :key="dict.value" :value="dict.value">
                                 {{ dict.label }}
                              </a-option>
                           </a-select>
                        </a-form-item>
                     </a-col>
                     <a-col :span="8">
                        <a-form-item label="执行时间">
                           <a-range-picker v-model="dateRange" style="width: 100%" value-format="YYYY-MM-DD" />
                        </a-form-item>
                     </a-col>
                  </a-row>
               </a-form>
            </a-col>
            <a-divider style="height: 42px" direction="vertical" />
            <a-col :flex="'86px'" style="text-align: right">
               <a-space direction="horizontal" :size="18">
                  <a-button type="primary" @click="handleQuery">
                     <template #icon><icon-search /></template>
                     搜索
                  </a-button>
                  <a-button @click="resetQuery">
                     <template #icon><icon-refresh /></template>
                     重置
                  </a-button>
               </a-space>
            </a-col>
         </a-row>

         <a-divider style="margin-top: 0" />

         <!-- 操作按钮 -->
         <a-row style="margin-bottom: 16px">
            <a-col :span="16">
               <a-space>
                  <a-button type="primary" status="danger" :disabled="selectedKeys.length === 0" @click="handleDelete">
                     <template #icon><icon-delete /></template>
                     删除
                  </a-button>
                  <a-button type="primary" status="danger" @click="handleClean">
                     <template #icon><icon-delete /></template>
                     清空
                  </a-button>
                  <a-button type="primary" status="warning" @click="handleExport">
                     <template #icon><icon-download /></template>
                     导出
                  </a-button>
               </a-space>
            </a-col>
            <a-col :span="8" style="text-align: right">
               <a-space>
                  <a-tooltip content="刷新">
                     <div class="action-icon" @click="getList">
                        <icon-refresh size="18" />
                     </div>
                  </a-tooltip>
                  <a-dropdown @select="handleSelectDensity">
                     <a-tooltip content="密度">
                        <div class="action-icon"><icon-line-height size="18" /></div>
                     </a-tooltip>
                     <template #content>
                        <a-doption v-for="item in densityList" :key="item.value" :value="item.value"
                           :class="{ active: item.value === size }">
                           <span>{{ item.name }}</span>
                        </a-doption>
                     </template>
                  </a-dropdown>
               </a-space>
            </a-col>
         </a-row>

         <!-- 表格 -->
         <a-table row-key="jobLogId" :loading="loading" :data="jobLogList" :pagination="pagination" :bordered="{ cell: true }"
            :size="size" :scroll="{x: 1}" :row-selection="rowSelection" v-model:selectedKeys="selectedKeys" @page-change="onPageChange">
            <template #columns>
               <a-table-column title="日志编号" data-index="jobLogId" :width="100" align="center" />
               <a-table-column title="任务名称" data-index="jobName" align="center" :ellipsis="true" :width="200" />
               <a-table-column title="任务组名" data-index="jobGroup" align="center" :ellipsis="true" :width="200">
                  <template #cell="{ record }">
                     <dict-tag :options="sys_job_group" :value="record.jobGroup" />
                  </template>
               </a-table-column>
               <a-table-column title="调用目标字符串" data-index="invokeTarget" align="center" :ellipsis="true" :width="200" />
               <a-table-column title="日志信息" data-index="jobMessage" align="center" :ellipsis="true" :width="200" />
               <a-table-column title="执行状态" data-index="status" align="center" :width="200">
                  <template #cell="{ record }">
                     <dict-tag :options="sys_common_status" :value="record.status" />
                  </template>
               </a-table-column>
               <a-table-column title="执行时间" data-index="createTime" :width="180" align="center">
                  <template #cell="{ record }">
                     {{ parseTime(record.createTime) }}
                  </template>
               </a-table-column>
               <a-table-column title="操作" align="center" :width="100" fixed="right">
                  <template #cell="{ record }">
                     <a-button type="text" @click="handleView(record)">
                        <template #icon><icon-eye /></template>
                        详细
                     </a-button>
                  </template>
               </a-table-column>
            </template>
         </a-table>

         <!-- 调度日志详细 -->
         <a-modal v-model:visible="open" title="调度日志详细" width="700px" @cancel="open = false">
            <a-descriptions :column="2" bordered>
               <a-descriptions-item label="日志序号">{{ form.jobLogId }}</a-descriptions-item>
               <a-descriptions-item label="任务名称">{{ form.jobName }}</a-descriptions-item>
               <a-descriptions-item label="任务分组">{{ selectDictLabel(sys_job_group, form.jobGroup) }}</a-descriptions-item>
               <a-descriptions-item label="执行时间">{{ form.createTime }}</a-descriptions-item>
               <a-descriptions-item label="调用方法" :span="2">{{ form.invokeTarget }}</a-descriptions-item>
               <a-descriptions-item label="日志信息" :span="2">{{ form.jobMessage }}</a-descriptions-item>
               <a-descriptions-item label="执行状态">
                  {{ selectDictLabel(sys_common_status, form.status) }}
               </a-descriptions-item>
               <a-descriptions-item v-if="form.status === '1'" label="异常信息" :span="2">
                  {{ form.exceptionInfo }}
               </a-descriptions-item>
            </a-descriptions>
            <template #footer>
               <a-button @click="open = false">关 闭</a-button>
            </template>
         </a-modal>
      </a-card>
   </div>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getJob } from "@/api/monitor/job";
import { listJobLog, delJobLog, cleanJobLog, exportJobLog } from "@/api/monitor/jobLog";
import { parseTime, selectDictLabel } from '@/utils';
import { useDict } from '@/utils/dict'
import DictTag from '@/components/DictTag/index.vue'
import { exportExcel } from '@/utils/exportUtil';
import { useTabBarStore } from '@/store';
import { Message, Modal } from '@arco-design/web-vue';

interface JobLogForm {
   jobLogId?: number;
   jobName?: string;
   jobGroup?: string;
   invokeTarget?: string;
   jobMessage?: string;
   status?: string;
   exceptionInfo?: string;
   createTime?: string;
}

interface QueryParams {
   pageNum: number;
   pageSize: number;
   jobName?: string;
   jobGroup?: string;
   status?: string | number;
   dictName?: string;
   dictType?: string;
}

const route = useRoute();
const router = useRouter();

const queryRef = ref();
const jobLogList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<string[]>([]);

const data = reactive({
   form: {} as JobLogForm,
   queryParams: {
      pageNum: 1,
      pageSize: 10,
      jobName: undefined,
      jobGroup: undefined,
      status: undefined
   } as QueryParams
});

const { queryParams, form } = toRefs(data);

const { sys_job_group, sys_common_status } = useDict('sys_job_group', 'sys_common_status')

/** 查询调度日志列表 */
function getList() {
   loading.value = true;
   const params = { ...queryParams.value, beginTime: dateRange.value[0], endTime: dateRange.value[1] };
   listJobLog(params).then(response => {
      jobLogList.value = response.rows;
      total.value = response.total;
      loading.value = false;
   });
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.pageNum = 1;
   getList();
}

/** 重置按钮操作 */
function resetQuery() {
   dateRange.value = [];
   queryRef.value?.resetFields();
   handleQuery();
}

/** 删除按钮操作 */
function handleDelete(row?: any) {
   const jobLogIds = selectedKeys.value.join(',');
   Modal.confirm({
    title: '确认',
    content: `是否确认删除调度日志编号为"${jobLogIds}"的数据项?`,
    onOk: () => {
      return delJobLog(jobLogIds).then(() => {
        getList()
        Message.success('删除成功')
      })
    }
  })
}

/** 清空按钮操作 */
function handleClean() {
   cleanJobLog();
}

/** 导出按钮操作 */
function handleExport() {
   exportExcel(exportJobLog, { ...queryParams.value, beginTime: dateRange.value[0], endTime: dateRange.value[1] }, '调度日志')
}

/** 查看详细按钮操作 */
function handleView(row: JobLogForm) {
   open.value = true;
   form.value = { ...row };
}

// 表格尺寸
const size = ref<'mini' | 'small' | 'medium' | 'large'>('medium')

// 密度选项
const densityList = computed(() => [
   { name: '迷你', value: 'mini' },
   { name: '偏小', value: 'small' },
   { name: '中等', value: 'medium' },
   { name: '偏大', value: 'large' }
])

// 分页配置
const pagination = reactive({
   pageNum: 1,
   pageSize: 20,
   total: 0
});

// 表格选择配置
const rowSelection = reactive({
   type: 'checkbox',
   showCheckedAll: true,
   onlyCurrent: false,
});
const selectedKeys = ref<Array<string>>([]);

/** 表格密度选择 */
function handleSelectDensity(val: string) {
   size.value = val as 'mini' | 'small' | 'medium' | 'large'
}

/** 分页变化 */
function onPageChange(pageNum: number) {
   pagination.pageNum = pageNum;
   getList();
}

onMounted(() => {
   console.log(123);
   const jobId: string = route.params && route.params.jobId as string
   if (jobId !== undefined && jobId !== '0') {
      getJob(jobId).then(response => {
         queryParams.value.jobName = response.data.jobName
         queryParams.value.jobGroup = response.data.jobGroup
         getList()
      })
   } else {
      getList()
   }
})
</script>

<style lang="less" scoped>
.container {
   padding: 0 16px 16px 16px;
}

.action-icon {
   margin-left: 12px;
   cursor: pointer;
}

.active {
   color: #0960bd;
   background-color: #e3f4fc;
}
</style>
