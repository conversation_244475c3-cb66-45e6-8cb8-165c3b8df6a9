---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/合同规则

<a id="opIdedit_10"></a>

## PUT 修改合同规则

PUT /contractRules

修改合同规则

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "projectId": "string",
  "projectName": "string",
  "signDateRange": 0,
  "days": 0,
  "priceDigit": 0,
  "totalDigit": 0,
  "areaDigit": 0,
  "dealType": 0,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractRulesAddDTO](#schemacontractrulesadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_7"></a>

## POST 新增合同规则

POST /contractRules

新增合同规则

> Body 请求参数

```json
{
  "id": "string",
  "type": 0,
  "projectId": "string",
  "projectName": "string",
  "signDateRange": 0,
  "days": 0,
  "priceDigit": 0,
  "totalDigit": 0,
  "areaDigit": 0,
  "dealType": 0,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractRulesAddDTO](#schemacontractrulesadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetInfo_13"></a>

## GET 获取合同规则详细信息

GET /contractRules/detail

获取合同规则详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|type|query|integer(int32)| 否 | 类型（1集团，2项目）|类型（1集团，2项目）|
|projectId|query|string| 否 | 项目ID|项目ID|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_ContractRulesAddDTO">ContractRulesAddDTO</h2>

<a id="schemacontractrulesadddto"></a>
<a id="schema_ContractRulesAddDTO"></a>
<a id="tocScontractrulesadddto"></a>
<a id="tocscontractrulesadddto"></a>

```json
{
  "id": "string",
  "type": 0,
  "projectId": "string",
  "projectName": "string",
  "signDateRange": 0,
  "days": 0,
  "priceDigit": 0,
  "totalDigit": 0,
  "areaDigit": 0,
  "dealType": 0,
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|type|integer(int32)|false|none|类型（1集团，2项目）|类型（1集团，2项目）|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|signDateRange|integer(int32)|false|none|签订日期固化(1不固化 2当月固化 3自定义)|签订日期固化(1不固化 2当月固化 3自定义)|
|days|integer(int32)|false|none|签订日期天数，当前日期+-days=签订日期可选范围|签订日期天数，当前日期+-days=签订日期可选范围|
|priceDigit|integer(int32)|false|none|单价位数(0元 1角 2分)|单价位数(0元 1角 2分)|
|totalDigit|integer(int32)|false|none|总价位数(0元 1角 2分)|总价位数(0元 1角 2分)|
|areaDigit|integer(int32)|false|none|面积保留位(默认保留2位)|面积保留位(默认保留2位)|
|dealType|integer(int32)|false|none|成交总价进位方式(1四舍五入 2直接舍去 3直接进位)|成交总价进位方式(1四舍五入 2直接舍去 3直接进位)|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

