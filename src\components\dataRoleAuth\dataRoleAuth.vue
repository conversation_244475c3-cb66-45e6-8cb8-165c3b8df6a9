<template>
    <div class="data-roles-list">
        <div class="common-flex data-roles-list-header">
            <div class="data-roles-list-header-title">
                <span class="common-header-tag"></span>
                <span>数据角色授权</span>
            </div>
        </div>
        <div class="data-roles-list-content">
            <a-transfer :data="transferData" :default-value="selectedData" @change="handleChange">
                <template #source-title="{
                    countTotal,
                    checked,
                    indeterminate,
                    onSelectAllChange,
                }">
                    <div>
                        <a-checkbox :model-value="checked" :indeterminate="indeterminate" @change="onSelectAllChange" />
                        全选 共 {{ countTotal }} 项
                    </div>
                </template>
                <template #target-title="{
                    countTotal
                }">
                    <div>
                        清除 共 {{ countTotal }} 项
                    </div>
                </template>
            </a-transfer>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { computed, ref, reactive, watch, nextTick, defineEmits } from 'vue'
import { getRoleList } from '@/api/role'



const emit = defineEmits(['change'])

const transferData = ref<any[]>([])
const selectedData = ref<any[]>([])

// 接收已经存在的数据
const props = defineProps({
    data: {
        type: Array,
        default: () => []
    }
})
console.log('---props.data', props.data)
if (props.data.length > 0) {
    selectedData.value = props.data
}else {
    selectedData.value = []
}

// watch(props, (newVal) => {
//     console.log('watch', newVal)
//     if (props.data.length > 0) {
//         selectedData.value = props.data
//     } else {
//         selectedData.value = []
//     }
// })


const handleChange = (value: any) => {
    console.log(value)
    selectedData.value = value
    emit('change', value)
}
const getSelectedData = () => {
    return selectedData.value
}
const fetchData = async () => {
    const { data } = await getRoleList({ type: 3 })
    console.log(data)
    transferData.value = data.map((item: any) => ({
        value: item.roleId,
        label: item.roleName
    }))
    console.log(transferData.value)
}
fetchData()
defineExpose({
    fetchData,
    getSelectedData
})
</script>

<script lang="ts">
export default { name: 'dataRoleAuth' }
</script>

<style scoped lang="less">
.data-roles-list {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 12px;
    box-sizing: border-box;

    .data-roles-list-header {
        justify-content: space-between;

        .data-roles-list-header-title {
            display: flex;
            align-items: center;
        }

        .data-roles-list-header-right {
            display: flex;
            align-items: center;
            color: rgb(var(--arcoblue-6));
            cursor: pointer;

            .data-roles-list-header-right-button {
                padding: 0 !important;
            }
        }
    }

    .data-roles-list-search {
        margin-top: 12px;
    }

    .data-roles-list-content {
        margin-top: 12px;

        .data-roles-list-content-list {
            .data-roles-list-content-item {
                padding: 12px;
                border-bottom: 1px solid #f0f0f0;
                cursor: pointer;
                justify-content: space-between;

                &:hover {
                    background: var(--color-fill-2);
                }

                .data-roles-list-content-item-right-handle {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    cursor: pointer;
                }
            }

            .data-roles-list-content-item-on {
                background: rgb(var(--arcoblue-6));
                color: #fff;

                &:hover {
                    background: rgb(var(--arcoblue-6));
                }
            }
        }
    }
}
</style>
