<template>
    <div class="base-info">
        <a-layout>
            <a-layout-sider :collapsed="collapsed" :width="300">
                <div :class="['collapsed-trigger', collapsed ? 'unfold' : '']" @click="toggleCollapsed">
                    <icon-menu-fold v-if="!collapsed" size="18" />
                    <icon-menu-unfold v-else size="18" />
                </div>
                <a-scrollbar v-show="!collapsed" style="height: 100%;overflow-y: auto;padding-right: 16px;">
                    <lesseeInfo ref="lesseeInfoRef" />
                    <ourContractInfo ref="ourContractInfoRef" />
                    <guarantorInfo ref="guarantorInfoRef" />
                    <invoiceInfo v-if="contractData.customer.customerType === 2" ref="invoiceInfoRef" />
                </a-scrollbar>
            </a-layout-sider>
            <a-layout-content>
                <a-scrollbar style="height: 100%;overflow-y: auto;padding: 0 16px;">
                    <roomInfo ref="roomInfoRef" />
                    <contractBaseInfo ref="contractBaseInfoRef" />
                    <orderInfo ref="orderInfoRef" />
                    <depositInfo ref="depositInfoRef" />
                    <rentInfo ref="rentInfoRef" />
                    <discountTerms ref="discountTermsRef" />
                    <contractSupplementInfo
                        v-if="contractData.contractType !== 1 && ['商铺', '中央食堂', '厂房', '综合体', '车位', '广告位', '设备类', '其他'].includes(contractPurposeLabel)"
                        ref="contractSupplementInfoRef" />
                    <supplementTerms ref="supplementTermsRef" />
                    <attachment ref="attachmentRef" />
                </a-scrollbar>
            </a-layout-content>
        </a-layout>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import lesseeInfo from './components/lesseeInfo.vue';
import ourContractInfo from './components/ourContractInfo.vue';
import guarantorInfo from './components/guarantorInfo.vue';
import invoiceInfo from './components/invoiceInfo.vue';
import roomInfo from './components/roomInfo.vue';
import contractBaseInfo from './components/contractBaseInfo.vue';
import orderInfo from './components/orderInfo.vue';
import depositInfo from './components/depositInfo.vue';
import rentInfo from './components/rentInfo.vue';
import discountTerms from './components/discountTerms.vue';
import contractSupplementInfo from './components/contractSupplementInfo.vue';
import supplementTerms from './components/supplementTerms.vue';
import attachment from './components/attachment.vue';
import { useContractStore } from '@/store/modules/contract/index'
import { getDictLabel } from '@/dict'
import { ContractAddDTO } from '@/api/contract';

const contractPurposeLabel = computed(() => {
    return getDictLabel('diversification_purpose', Number(contractData.value.contractPurpose))
})

const collapsed = ref(false);
const toggleCollapsed = () => {
    collapsed.value = !collapsed.value;
};

// 获取合同数据
const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)
</script>

<style lang="less" scoped>
.base-info {
    width: 100%;
    height: 100%;

    .arco-layout-has-sider {
        height: 100%;
    }

    .arco-layout-sider-light {
        box-shadow: none;
        border-right: 1px solid #e8e8e8;
        position: relative;

        .collapsed-trigger {
            position: absolute;
            top: 4px;
            right: 27px;
            background: #ebeef4;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 222;
            border-radius: 4px;
            cursor: pointer;

            &.unfold {
                right: 8px;
            }
        }

        :deep(.arco-layout-sider-children) {
            overflow: hidden;
        }

    }

    .arco-scrollbar {
        height: 100%;
    }
}
</style>