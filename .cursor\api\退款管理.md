---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁后台/财务退款

<a id="opIdedit_3"></a>

## PUT 修改财务退款

PUT /refund

修改财务退款

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "refundStatus": 0,
  "approveStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "isDel": true,
  "isSubmit": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialRefundAddDTO](#schemafinancialrefundadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_3"></a>

## POST 新增财务退款

POST /refund

新增财务退款

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "refundStatus": 0,
  "approveStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "isDel": true,
  "isSubmit": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialRefundAddDTO](#schemafinancialrefundadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdsave_2"></a>

## POST 保存退款单接口

POST /financialRefund/save

保存退款单接口

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "refundStatus": 0,
  "approveStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "isDel": true,
  "isSubmit": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialRefundAddDTO](#schemafinancialrefundadddto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_2"></a>

## POST 查询财务退款列表

POST /financialRefund/list

查询财务退款列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "refundTypes": "string",
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "refundStatus": 0,
  "refundStatuses": "string",
  "approveStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "applyTimeStart": "2019-08-24T14:15:22Z",
  "applyTimeEnd": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FinancialRefundQueryDTO](#schemafinancialrefundquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[TableDataInfo](#schematabledatainfo)|

<a id="opIdlist_15"></a>

## GET 查询财务退款列表

GET /financialRefund/list

查询财务退款列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|refundType|query|integer(int32)| 否 | 退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|refundTypes|query|string| 否 | 退款类型（多选逗号拼接）|退款类型（多选逗号拼接）|
|bizId|query|string| 否 | 业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|query|string| 否 | 退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|query|string(date-time)| 否 | 退款申请时间|退款申请时间|
|refundAmount|query|number| 否 | 退款金额|退款金额|
|feeType|query|string| 否 | 退款费用类型|退款费用类型|
|refundWay|query|integer(int32)| 否 | 退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|query|string| 否 | 收款方姓名|收款方姓名|
|receiverBank|query|string| 否 | 收款方开户行|收款方开户行|
|receiverAccount|query|string| 否 | 收款方银行账号|收款方银行账号|
|refundRemark|query|string| 否 | 退款申请说明|退款申请说明|
|refundTime|query|string(date-time)| 否 | 退款时间|退款时间|
|refundStatus|query|integer(int32)| 否 | 退款单状态:0-草稿、1-待退款、2-已退款、3-作废|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|
|refundStatuses|query|string| 否 | 退款单状态（多选逗号拼接）|退款单状态（多选逗号拼接）|
|approveStatus|query|integer(int32)| 否 | 审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|roomYardStatus|query|integer(int32)| 否 | 一房一码退款状态|一房一码退款状态|
|attachments|query|string| 否 | 附件|附件|
|cancelTime|query|string(date-time)| 否 | 作废时间|作废时间|
|cancelBy|query|string| 否 | 作废人|作废人|
|cancelByName|query|string| 否 | 作废人姓名|作废人姓名|
|cancelReason|query|string| 否 | 作废原因|作废原因|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|applyTimeStart|query|string(date-time)| 否 | 申请时间开始|申请时间开始|
|applyTimeEnd|query|string(date-time)| 否 | 申请时间结束|申请时间结束|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[TableDataInfo](#schematabledatainfo)|

<a id="opIdexport_4"></a>

## POST 导出询财务退款列表

POST /refund/export

导出询财务退款列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|refundType|query|integer(int32)| 否 | 退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|bizId|query|string| 否 | 业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|query|string| 否 | 退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|query|string(date-time)| 否 | 退款申请时间|退款申请时间|
|refundAmount|query|number| 否 | 退款金额|退款金额|
|feeType|query|string| 否 | 退款费用类型|退款费用类型|
|refundWay|query|integer(int32)| 否 | 退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|query|string| 否 | 收款方姓名|收款方姓名|
|receiverBank|query|string| 否 | 收款方开户行|收款方开户行|
|receiverAccount|query|string| 否 | 收款方银行账号|收款方银行账号|
|refundRemark|query|string| 否 | 退款申请说明|退款申请说明|
|refundTime|query|string(date-time)| 否 | 退款时间|退款时间|
|refundStatus|query|integer(int32)| 否 | 退款单状态:0-草稿、1-待退款、2-已退款、3-作废|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|
|approveStatus|query|integer(int32)| 否 | 审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|roomYardStatus|query|integer(int32)| 否 | 一房一码退款状态|一房一码退款状态|
|attachments|query|string| 否 | 附件|附件|
|cancelTime|query|string(date-time)| 否 | 作废时间|作废时间|
|cancelBy|query|string| 否 | 作废人|作废人|
|cancelByName|query|string| 否 | 作废人姓名|作废人姓名|
|cancelReason|query|string| 否 | 作废原因|作废原因|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdexport_7"></a>

## POST 导出询财务退款列表

POST /financialRefund/export

导出询财务退款列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|refundType|query|integer(int32)| 否 | 退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|refundTypes|query|string| 否 | 退款类型（多选逗号拼接）|退款类型（多选逗号拼接）|
|bizId|query|string| 否 | 业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|query|string| 否 | 退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|query|string(date-time)| 否 | 退款申请时间|退款申请时间|
|refundAmount|query|number| 否 | 退款金额|退款金额|
|feeType|query|string| 否 | 退款费用类型|退款费用类型|
|refundWay|query|integer(int32)| 否 | 退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|query|string| 否 | 收款方姓名|收款方姓名|
|receiverBank|query|string| 否 | 收款方开户行|收款方开户行|
|receiverAccount|query|string| 否 | 收款方银行账号|收款方银行账号|
|refundRemark|query|string| 否 | 退款申请说明|退款申请说明|
|refundTime|query|string(date-time)| 否 | 退款时间|退款时间|
|refundStatus|query|integer(int32)| 否 | 退款单状态:0-草稿、1-待退款、2-已退款、3-作废|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|
|refundStatuses|query|string| 否 | 退款单状态（多选逗号拼接）|退款单状态（多选逗号拼接）|
|approveStatus|query|integer(int32)| 否 | 审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|roomYardStatus|query|integer(int32)| 否 | 一房一码退款状态|一房一码退款状态|
|attachments|query|string| 否 | 附件|附件|
|cancelTime|query|string(date-time)| 否 | 作废时间|作废时间|
|cancelBy|query|string| 否 | 作废人|作废人|
|cancelByName|query|string| 否 | 作废人姓名|作废人姓名|
|cancelReason|query|string| 否 | 作废原因|作废原因|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|applyTimeStart|query|string(date-time)| 否 | 申请时间开始|申请时间开始|
|applyTimeEnd|query|string(date-time)| 否 | 申请时间结束|申请时间结束|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIdlist_9"></a>

## GET 查询财务退款列表

GET /refund/list

查询财务退款列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|refundType|query|integer(int32)| 否 | 退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|bizId|query|string| 否 | 业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|query|string| 否 | 退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|query|string(date-time)| 否 | 退款申请时间|退款申请时间|
|refundAmount|query|number| 否 | 退款金额|退款金额|
|feeType|query|string| 否 | 退款费用类型|退款费用类型|
|refundWay|query|integer(int32)| 否 | 退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|query|string| 否 | 收款方姓名|收款方姓名|
|receiverBank|query|string| 否 | 收款方开户行|收款方开户行|
|receiverAccount|query|string| 否 | 收款方银行账号|收款方银行账号|
|refundRemark|query|string| 否 | 退款申请说明|退款申请说明|
|refundTime|query|string(date-time)| 否 | 退款时间|退款时间|
|refundStatus|query|integer(int32)| 否 | 退款单状态:0-草稿、1-待退款、2-已退款、3-作废|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|
|approveStatus|query|integer(int32)| 否 | 审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|roomYardStatus|query|integer(int32)| 否 | 一房一码退款状态|一房一码退款状态|
|attachments|query|string| 否 | 附件|附件|
|cancelTime|query|string(date-time)| 否 | 作废时间|作废时间|
|cancelBy|query|string| 否 | 作废人|作废人|
|cancelByName|query|string| 否 | 作废人姓名|作废人姓名|
|cancelReason|query|string| 否 | 作废原因|作废原因|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdcancel"></a>

## POST 作废退款单接口

POST /financialRefund/cancel

作废退款单接口

> Body 请求参数

```json
{
  "refundId": "string",
  "cancelReason": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[FinancialFlowCancelDTO](#schemafinancialflowcanceldto)| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetInfo_4"></a>

## GET 获取财务退款详细信息

GET /refund/detail

获取财务退款详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|财务退款ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddetail_1"></a>

## GET 获取退款单详情接口

GET /financialRefund/detail

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|refundId|query|string| 否 ||退款单ID|
|refundType|query|integer(int32)| 否 ||退款类型|
|bizId|query|string| 否 ||业务ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "refund": {},
  "terminate": {
    "id": "string",
    "contractId": "string",
    "unionId": "string",
    "bondReceivedAmount": 0,
    "rentReceivedAmount": 0,
    "rentOverdueAmount": 0,
    "receivedPeriod": "string",
    "overduePeriod": "string",
    "terminateType": 0,
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateReason": "string",
    "otherReasonDesc": "string",
    "hasOtherDeduction": true,
    "otherDeductionDesc": "string",
    "terminateRemark": "string",
    "terminateAttachments": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "booking": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "customerName": "string",
    "propertyType": "string",
    "roomId": "string",
    "roomName": "string",
    "bookingNo": "string",
    "bookingAmount": 0,
    "receivableDate": "2019-08-24T14:15:22Z",
    "expectSignDate": "2019-08-24T14:15:22Z",
    "isRefundable": 0,
    "cancelTime": "2019-08-24T14:15:22Z",
    "cancelBy": "string",
    "cancelByName": "string",
    "cancelReason": 0,
    "isRefund": 0,
    "cancelEnclosure": "string",
    "cancelRemark": "string",
    "status": 0,
    "contractId": "string",
    "refundId": "string",
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "isDel": true,
    "contractNo": "string",
    "signDate": "2019-08-24T14:15:22Z",
    "contractLeaseUnit": "string",
    "lesseeName": "string",
    "receivedAmount": 0,
    "receivedDate": "2019-08-24T14:15:22Z",
    "payMethod": "string"
  },
  "flow": {
    "id": "string",
    "projectId": "string",
    "orderNo": "string",
    "payDirection": 0,
    "payType": 0,
    "payMethod": 0,
    "targetType": 0,
    "target": "string",
    "entryTime": "2019-08-24T14:15:22Z",
    "status": 0,
    "amount": 0,
    "usedAmount": 0,
    "payerName": "string",
    "payerPhone": "string",
    "payerAccount": "string",
    "payRemark": "string",
    "merchant": "string",
    "payChannel": "string",
    "sourceNo": "string",
    "isOtherIncome": true,
    "otherIncomeDesc": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "flowRels": [
    {
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "acctAmount": 0,
      "createByName": "string",
      "createTime": "string",
      "updateByName": "string",
      "isDel": true,
      "projectId": "string",
      "projectName": "string",
      "entryTime": "2019-08-24T14:15:22Z",
      "payType": 0,
      "payMethod": 0,
      "orderNo": "string",
      "usedAmount": 0,
      "payerName": "string",
      "target": "string",
      "merchant": "string"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[FinancialRefundDetailVo](#schemafinancialrefunddetailvo)|

<a id="opIdremove_3"></a>

## DELETE 删除财务退款

DELETE /refund/delete

删除财务退款

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|财务退款ID列表|query|array[string]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_7"></a>

## DELETE 删除退款单接口

DELETE /financialRefund/delete

删除退款单接口

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|退款单ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_FinancialRefundAddDTO">FinancialRefundAddDTO</h2>

<a id="schemafinancialrefundadddto"></a>
<a id="schema_FinancialRefundAddDTO"></a>
<a id="tocSfinancialrefundadddto"></a>
<a id="tocsfinancialrefundadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "refundStatus": 0,
  "approveStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "isDel": true,
  "isSubmit": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|refundType|integer(int32)|false|none|退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|bizId|string|false|none|业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|string|false|none|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|string(date-time)|false|none|退款申请时间|退款申请时间|
|refundAmount|number|false|none|退款金额|退款金额|
|feeType|string|false|none|退款费用类型|退款费用类型|
|refundWay|integer(int32)|false|none|退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|string|false|none|收款方姓名|收款方姓名|
|receiverBank|string|false|none|收款方开户行|收款方开户行|
|receiverAccount|string|false|none|收款方银行账号|收款方银行账号|
|refundRemark|string|false|none|退款申请说明|退款申请说明|
|refundTime|string(date-time)|false|none|退款时间|退款时间|
|refundStatus|integer(int32)|false|none|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|
|approveStatus|integer(int32)|false|none|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|roomYardStatus|integer(int32)|false|none|一房一码退款状态|一房一码退款状态|
|attachments|string|false|none|附件|附件|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|string|false|none|作废原因|作废原因|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|isSubmit|boolean|false|none|0-暂存,1-提交|0-暂存,1-提交|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_BookingVo">BookingVo</h2>

<a id="schemabookingvo"></a>
<a id="schema_BookingVo"></a>
<a id="tocSbookingvo"></a>
<a id="tocsbookingvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingNo": "string",
  "bookingAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "contractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "contractLeaseUnit": "string",
  "lesseeName": "string",
  "receivedAmount": 0,
  "receivedDate": "2019-08-24T14:15:22Z",
  "payMethod": "string"
}

```

定单基本信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|customerName|string|false|none|客户名称|客户名称|
|propertyType|string|false|none|意向物业类型|意向物业类型|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房源名称|房源名称|
|bookingNo|string|false|none|定单号|定单号|
|bookingAmount|number|false|none|定单金额|定单金额|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|cancelEnclosure|string|false|none|退定附件|退定附件|
|cancelRemark|string|false|none|退定说明|退定说明|
|status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|contractId|string|false|none|转签约合同id|转签约合同id|
|refundId|string|false|none|退款单id|退款单id|
|createBy|string|false|none|创建人|创建人|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建日期|创建日期|
|updateBy|string|false|none|更新人|更新人|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新日期|更新日期|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractNo|string|false|none|合同号|合同号|
|signDate|string(date-time)|false|none|合同签订日期|合同签订日期|
|contractLeaseUnit|string|false|none|租赁单元|租赁单元|
|lesseeName|string|false|none|承租人名称|承租人名称|
|receivedAmount|number|false|none|定单已收金额|定单已收金额|
|receivedDate|string(date-time)|false|none|实收日期|实收日期|
|payMethod|string|false|none|支付方式|支付方式|

<h2 id="tocS_FinancialRefundQueryDTO">FinancialRefundQueryDTO</h2>

<a id="schemafinancialrefundquerydto"></a>
<a id="schema_FinancialRefundQueryDTO"></a>
<a id="tocSfinancialrefundquerydto"></a>
<a id="tocsfinancialrefundquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "refundType": 0,
  "refundTypes": "string",
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "refundStatus": 0,
  "refundStatuses": "string",
  "approveStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "applyTimeStart": "2019-08-24T14:15:22Z",
  "applyTimeEnd": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|refundType|integer(int32)|false|none|退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|refundTypes|string|false|none|退款类型（多选逗号拼接）|退款类型（多选逗号拼接）|
|bizId|string|false|none|业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|string|false|none|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|string(date-time)|false|none|退款申请时间|退款申请时间|
|refundAmount|number|false|none|退款金额|退款金额|
|feeType|string|false|none|退款费用类型|退款费用类型|
|refundWay|integer(int32)|false|none|退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|string|false|none|收款方姓名|收款方姓名|
|receiverBank|string|false|none|收款方开户行|收款方开户行|
|receiverAccount|string|false|none|收款方银行账号|收款方银行账号|
|refundRemark|string|false|none|退款申请说明|退款申请说明|
|refundTime|string(date-time)|false|none|退款时间|退款时间|
|refundStatus|integer(int32)|false|none|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|
|refundStatuses|string|false|none|退款单状态（多选逗号拼接）|退款单状态（多选逗号拼接）|
|approveStatus|integer(int32)|false|none|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|roomYardStatus|integer(int32)|false|none|一房一码退款状态|一房一码退款状态|
|attachments|string|false|none|附件|附件|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|string|false|none|作废原因|作废原因|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|applyTimeStart|string(date-time)|false|none|申请时间开始|申请时间开始|
|applyTimeEnd|string(date-time)|false|none|申请时间结束|申请时间结束|

<h2 id="tocS_FinancialFlowCancelDTO">FinancialFlowCancelDTO</h2>

<a id="schemafinancialflowcanceldto"></a>
<a id="schema_FinancialFlowCancelDTO"></a>
<a id="tocSfinancialflowcanceldto"></a>
<a id="tocsfinancialflowcanceldto"></a>

```json
{
  "refundId": "string",
  "cancelReason": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|refundId|string|true|none|退款单id|退款单id|
|cancelReason|string|false|none|作废原因|作废原因|

<h2 id="tocS_CostFlowRelVo">CostFlowRelVo</h2>

<a id="schemacostflowrelvo"></a>
<a id="schema_CostFlowRelVo"></a>
<a id="tocScostflowrelvo"></a>
<a id="tocscostflowrelvo"></a>

```json
{
  "id": "string",
  "costId": "string",
  "refundId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "confirmStatus": 0,
  "confirmTime": "2019-08-24T14:15:22Z",
  "confirmUserId": "string",
  "confirmUserName": "string",
  "payAmount": 0,
  "acctAmount": 0,
  "createByName": "string",
  "createTime": "string",
  "updateByName": "string",
  "isDel": true,
  "projectId": "string",
  "projectName": "string",
  "entryTime": "2019-08-24T14:15:22Z",
  "payType": 0,
  "payMethod": 0,
  "orderNo": "string",
  "usedAmount": 0,
  "payerName": "string",
  "target": "string",
  "merchant": "string"
}

```

账单流水关系列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|costId|string|false|none|账单id|账单id|
|refundId|string|false|none|退款单id|退款单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|账单类型：1-收款 2-转入 3-转出 4-退款|账单类型：1-收款 2-转入 3-转出 4-退款|
|confirmStatus|integer(int32)|false|none|确认状态: 0-未确认、1-自动确认、2-手动确认|确认状态: 0-未确认、1-自动确认、2-手动确认|
|confirmTime|string(date-time)|false|none|确认时间|确认时间|
|confirmUserId|string|false|none|确认人id|确认人id|
|confirmUserName|string|false|none|确认人姓名|确认人姓名|
|payAmount|number|false|none|支付金额|支付金额|
|acctAmount|number|false|none|本次记账金额|本次记账金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string|false|none|创建时间|创建时间|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|entryTime|string(date-time)|false|none|入账时间|入账时间|
|payType|integer(int32)|false|none|支付类型|支付类型|
|payMethod|integer(int32)|false|none|支付方式|支付方式|
|orderNo|string|false|none|订单号|订单号|
|usedAmount|number|false|none|已使用金额|已使用金额|
|payerName|string|false|none|支付人姓名|支付人姓名|
|target|string|false|none|支付对象|支付对象|
|merchant|string|false|none|收款商户|收款商户|

<h2 id="tocS_ContractTerminateVo">ContractTerminateVo</h2>

<a id="schemacontractterminatevo"></a>
<a id="schema_ContractTerminateVo"></a>
<a id="tocScontractterminatevo"></a>
<a id="tocscontractterminatevo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "unionId": "string",
  "bondReceivedAmount": 0,
  "rentReceivedAmount": 0,
  "rentOverdueAmount": 0,
  "receivedPeriod": "string",
  "overduePeriod": "string",
  "terminateType": 0,
  "terminateDate": "2019-08-24T14:15:22Z",
  "terminateReason": "string",
  "otherReasonDesc": "string",
  "hasOtherDeduction": true,
  "otherDeductionDesc": "string",
  "terminateRemark": "string",
  "terminateAttachments": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

退租申请单信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|unionId|string|false|none|合同统一id|合同统一id|
|bondReceivedAmount|number|false|none|已收保证金（元）|已收保证金（元）|
|rentReceivedAmount|number|false|none|已收租金（元）|已收租金（元）|
|rentOverdueAmount|number|false|none|逾期租金（元）|逾期租金（元）|
|receivedPeriod|string|false|none|已收账期文字描述|已收账期文字描述|
|overduePeriod|string|false|none|逾期账期文字描述|逾期账期文字描述|
|terminateType|integer(int32)|false|none|退租类型:0-到期退租,1-提前退租|退租类型:0-到期退租,1-提前退租|
|terminateDate|string(date-time)|false|none|退租日期|退租日期|
|terminateReason|string|false|none|退租原因,字典逗号拼接|退租原因,字典逗号拼接|
|otherReasonDesc|string|false|none|其他原因说明|其他原因说明|
|hasOtherDeduction|boolean|false|none|是否有其他扣款|是否有其他扣款|
|otherDeductionDesc|string|false|none|其他扣款描述|其他扣款描述|
|terminateRemark|string|false|none|退租说明|退租说明|
|terminateAttachments|string|false|none|退租附件|退租附件|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_FinancialFlowVo">FinancialFlowVo</h2>

<a id="schemafinancialflowvo"></a>
<a id="schema_FinancialFlowVo"></a>
<a id="tocSfinancialflowvo"></a>
<a id="tocsfinancialflowvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "orderNo": "string",
  "payDirection": 0,
  "payType": 0,
  "payMethod": 0,
  "targetType": 0,
  "target": "string",
  "entryTime": "2019-08-24T14:15:22Z",
  "status": 0,
  "amount": 0,
  "usedAmount": 0,
  "payerName": "string",
  "payerPhone": "string",
  "payerAccount": "string",
  "payRemark": "string",
  "merchant": "string",
  "payChannel": "string",
  "sourceNo": "string",
  "isOtherIncome": true,
  "otherIncomeDesc": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

未明流水信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|orderNo|string|false|none|订单号|订单号|
|payDirection|integer(int32)|false|none|支付方向: 0-收入, 1-支出|支付方向: 0-收入, 1-支出|
|payType|integer(int32)|false|none|支付类型|支付类型|
|payMethod|integer(int32)|false|none|支付方式|支付方式|
|targetType|integer(int32)|false|none|支付对象类型|支付对象类型|
|target|string|false|none|支付对象|支付对象|
|entryTime|string(date-time)|false|none|入账时间|入账时间|
|status|integer(int32)|false|none|状态: 0-未记账, 1-部分记账, 2-已记账|状态: 0-未记账, 1-部分记账, 2-已记账|
|amount|number|false|none|支付金额|支付金额|
|usedAmount|number|false|none|已使用金额|已使用金额|
|payerName|string|false|none|支付人姓名|支付人姓名|
|payerPhone|string|false|none|支付人手机号|支付人手机号|
|payerAccount|string|false|none|支付人账号|支付人账号|
|payRemark|string|false|none|支付备注|支付备注|
|merchant|string|false|none|收款商户|收款商户|
|payChannel|string|false|none|收款渠道|收款渠道|
|sourceNo|string|false|none|退款流水的原单号|退款流水的原单号|
|isOtherIncome|boolean|false|none|是否是其他收入: 0-否,1-是|是否是其他收入: 0-否,1-是|
|otherIncomeDesc|string|false|none|其他收入说明|其他收入说明|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_FinancialRefundDetailVo">FinancialRefundDetailVo</h2>

<a id="schemafinancialrefunddetailvo"></a>
<a id="schema_FinancialRefundDetailVo"></a>
<a id="tocSfinancialrefunddetailvo"></a>
<a id="tocsfinancialrefunddetailvo"></a>

```json
{
  "refund": {},
  "terminate": {
    "id": "string",
    "contractId": "string",
    "unionId": "string",
    "bondReceivedAmount": 0,
    "rentReceivedAmount": 0,
    "rentOverdueAmount": 0,
    "receivedPeriod": "string",
    "overduePeriod": "string",
    "terminateType": 0,
    "terminateDate": "2019-08-24T14:15:22Z",
    "terminateReason": "string",
    "otherReasonDesc": "string",
    "hasOtherDeduction": true,
    "otherDeductionDesc": "string",
    "terminateRemark": "string",
    "terminateAttachments": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "booking": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "customerName": "string",
    "propertyType": "string",
    "roomId": "string",
    "roomName": "string",
    "bookingNo": "string",
    "bookingAmount": 0,
    "receivableDate": "2019-08-24T14:15:22Z",
    "expectSignDate": "2019-08-24T14:15:22Z",
    "isRefundable": 0,
    "cancelTime": "2019-08-24T14:15:22Z",
    "cancelBy": "string",
    "cancelByName": "string",
    "cancelReason": 0,
    "isRefund": 0,
    "cancelEnclosure": "string",
    "cancelRemark": "string",
    "status": 0,
    "contractId": "string",
    "refundId": "string",
    "createBy": "string",
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": "string",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "isDel": true,
    "contractNo": "string",
    "signDate": "2019-08-24T14:15:22Z",
    "contractLeaseUnit": "string",
    "lesseeName": "string",
    "receivedAmount": 0,
    "receivedDate": "2019-08-24T14:15:22Z",
    "payMethod": "string"
  },
  "flow": {
    "id": "string",
    "projectId": "string",
    "orderNo": "string",
    "payDirection": 0,
    "payType": 0,
    "payMethod": 0,
    "targetType": 0,
    "target": "string",
    "entryTime": "2019-08-24T14:15:22Z",
    "status": 0,
    "amount": 0,
    "usedAmount": 0,
    "payerName": "string",
    "payerPhone": "string",
    "payerAccount": "string",
    "payRemark": "string",
    "merchant": "string",
    "payChannel": "string",
    "sourceNo": "string",
    "isOtherIncome": true,
    "otherIncomeDesc": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "flowRels": [
    {
      "id": "string",
      "costId": "string",
      "refundId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "acctAmount": 0,
      "createByName": "string",
      "createTime": "string",
      "updateByName": "string",
      "isDel": true,
      "projectId": "string",
      "projectName": "string",
      "entryTime": "2019-08-24T14:15:22Z",
      "payType": 0,
      "payMethod": 0,
      "orderNo": "string",
      "usedAmount": 0,
      "payerName": "string",
      "target": "string",
      "merchant": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|refund|object|false|none||none|
|terminate|[ContractTerminateVo](#schemacontractterminatevo)|false|none||退租申请单信息，退款类型为0时有值|
|booking|[BookingVo](#schemabookingvo)|false|none||定单基本信息|
|flow|[FinancialFlowVo](#schemafinancialflowvo)|false|none||未明流水信息，退款类型为2时有值|
|flowRels|[[CostFlowRelVo](#schemacostflowrelvo)]|false|none|退款流水信息列表|退款流水信息列表|

<h2 id="tocS_FinancialRefundVo">FinancialRefundVo</h2>

<a id="schemafinancialrefundvo"></a>
<a id="schema_FinancialRefundVo"></a>
<a id="tocSfinancialrefundvo"></a>
<a id="tocsfinancialrefundvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "refundType": 0,
  "bizId": "string",
  "refundTarget": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "refundAmount": 0,
  "feeType": "string",
  "refundWay": 0,
  "receiverName": "string",
  "receiverBank": "string",
  "receiverAccount": "string",
  "refundRemark": "string",
  "refundTime": "2019-08-24T14:15:22Z",
  "refundStatus": 0,
  "approveStatus": 0,
  "roomYardStatus": 0,
  "attachments": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

退款单基本信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|refundType|integer(int32)|false|none|退款类型：0-退租退款、1-退定退款、2-未明流水退款|退款类型：0-退租退款、1-退定退款、2-未明流水退款|
|bizId|string|false|none|业务id: 根据退款类型退租申请单id,定单id,流水id|业务id: 根据退款类型退租申请单id,定单id,流水id|
|refundTarget|string|false|none|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|退款对象: 根据退款类型合同号,定单预定客户+房源,流水号|
|applyTime|string(date-time)|false|none|退款申请时间|退款申请时间|
|refundAmount|number|false|none|退款金额|退款金额|
|feeType|string|false|none|退款费用类型|退款费用类型|
|refundWay|integer(int32)|false|none|退款方式: 0-原路退回、1-银行转账|退款方式: 0-原路退回、1-银行转账|
|receiverName|string|false|none|收款方姓名|收款方姓名|
|receiverBank|string|false|none|收款方开户行|收款方开户行|
|receiverAccount|string|false|none|收款方银行账号|收款方银行账号|
|refundRemark|string|false|none|退款申请说明|退款申请说明|
|refundTime|string(date-time)|false|none|退款时间|退款时间|
|refundStatus|integer(int32)|false|none|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|退款单状态:0-草稿、1-待退款、2-已退款、3-作废|
|approveStatus|integer(int32)|false|none|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回|
|roomYardStatus|integer(int32)|false|none|一房一码退款状态|一房一码退款状态|
|attachments|string|false|none|附件|附件|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|string|false|none|作废原因|作废原因|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

