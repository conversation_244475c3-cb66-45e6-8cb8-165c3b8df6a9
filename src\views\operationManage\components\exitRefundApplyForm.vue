<template>
    <a-drawer :visible="drawerVisible" :footer="true" :title="drawerTitle" unmount-on-close @cancel="handleCancel" class="common-drawer">
        <div class="refund-form-container">
            <!-- 退款申请信息 -->
            <div class="form-section">
                <section-title title="退款申请信息" style="margin-bottom: 16px;"/>
                <a-form :model="formData" :rules="rules" :label-col-props="{ span: 9 }" :wrapper-col-props="{ span: 15 }"
                    label-align="right" ref="formRef">
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item field="orderNo" label="退款单号">
                                <a-input v-model="formData.orderNo" placeholder="请输入退款单号" allow-clear disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="refundType" label="退款类型">
                                <a-select v-model="formData.refundType" placeholder="请选择退款类型" disabled>
                                    <a-option :value="0">退租退款</a-option>
                                    <a-option :value="1">退定退款</a-option>
                                    <a-option :value="2">未明流水退款</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="applyTime" label="退款申请日期" required>
                                <a-date-picker v-model="formData.applyTime" style="width: 100%" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="refundAmount" label="退款金额" required>
                                <a-input-number v-model="formData.refundAmount" placeholder="请输入退款金额"
                                    style="width: 100%" :precision="2" disabled />
                                <template #append>元</template>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="feeType" label="退款费用类型">
                                <a-input v-model="formData.feeType" placeholder="请输入退款费用类型" allow-clear disabled />
                            </a-form-item>
                        </a-col>
                        <!-- isThirdAgent 是否第三方代收 -->
                        <a-col :span="8">
                            <a-form-item field="isThirdAgent" label="是否第三方代收" required>
                                <a-radio-group v-model="formData.isThirdAgent" :disabled="isViewMode" @change="handleIsThirdAgentChange">
                                    <a-radio :value="true">是</a-radio>
                                    <a-radio :value="false">否</a-radio>
                                </a-radio-group>
                            </a-form-item>
                            <div class="tip-text" style="margin-bottom: 8px;">请下载《退款申请单-代收》模板签字后上传</div>
                        </a-col>
                        <!-- 第三方代收名称 -->
                        <a-col :span="8">
                            <a-form-item field="refundWay" label="退款方式" required>
                                <a-select v-model="formData.refundWay" placeholder="请选择退款方式" disabled>
                                    <a-option :value="0">原路退回</a-option>
                                    <a-option :value="1">银行转账</a-option>
                                </a-select>
                            </a-form-item>
                            <div class="tip-text">默认原路退回，如果有特殊要求银行转账，请在退款申请中说明</div>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverName" label="收款方姓名" required>
                                <a-input v-model="formData.receiverName" placeholder="请输入收款方姓名" allow-clear disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverBank" label="收款方开户行" required>
                                <a-input v-model="formData.receiverBank" placeholder="请输入收款方开户行" allow-clear disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverAccount" label="收款方银行账号">
                                <a-input v-model="formData.receiverAccount" placeholder="请输入收款方银行账号" allow-clear disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item field="refundRemark" label="退款申请说明" :label-col-props="{ span: 3 }"
                                :wrapper-col-props="{ span: 21 }">
                                <a-textarea v-model="formData.refundRemark" placeholder="请输入退款申请说明" allow-clear
                                    :max-length="200" show-word-limit :disabled="isViewMode" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>

            <!-- 退款申请单-代收 -->
            <div class="form-section" v-if="formData.isThirdAgent">
                <section-title title="退款申请单-代收" style="margin-bottom: 16px;"/>
                <a-button type="text" @click="handleAgentAttachmentsDownload" style="margin-bottom: 10px;">《退款申请单-代收》模板下载</a-button>
                <div class="attachment-upload">
                    <upload-file v-model="formData.agentAttachments" :limit="0" :disabled="isViewMode" />
                </div>
            </div>

            <!-- 附件上传 -->
            <div class="form-section">
                <section-title title="附件" style="margin-bottom: 16px;"/>
                <div class="attachment-upload">
                    <upload-file v-model="formData.attachments" :limit="0" :disabled="isViewMode" />
                </div>
            </div>

            <!-- 退租信息 -->
            <div class="form-section">
                <section-title title="退租信息" style="margin-bottom: 16px;">
                    <template #right>
                        <a-button type="text" @click="handleViewContractDetail">查看详情</a-button>
                    </template>
                </section-title>
                    <!-- <a-button type="text" @click="handleViewContractDetail">查看详情</a-button> -->
                <div class="base-info">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">承租方：</span>
                            <span class="info-value">{{ contractInfo.tenantName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退租类型：</span>
                            <span class="info-value">{{ getRefundTypeName(contractInfo.refundType) }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退租日期：</span>
                            <span class="info-value">{{ contractInfo.refundDate }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">合同用途：</span>
                            <span class="info-value">{{ getContractPurposeText(contractInfo.contractPurpose) }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ contractInfo.contractNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">合同起止日期：</span>
                            <span class="info-value">{{ contractInfo.startDate }} - {{ contractInfo.endDate }}</span>
                        </div>
                    </div>
                    <!-- <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ contractInfo.contractNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">合同终止日期：</span>
                            <span class="info-value">{{ contractInfo.endDate }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">已收保证金：</span>
                            <span class="info-value">{{ formatMoney(contractInfo.bondReceivedAmount) }}元</span>
                        </div>
                    </div> -->
                    <!-- <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">已收租金：</span>
                            <span class="info-value">{{ formatMoney(contractInfo.rentReceivedAmount) }}元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">逾期租金：</span>
                            <span class="info-value">{{ formatMoney(contractInfo.rentOverdueAmount) }}元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">已收账期：</span>
                            <span class="info-value">{{ contractInfo.receivedPeriod }}</span>
                        </div>
                    </div> -->
                    <div class="info-row" v-if="contractInfo.overduePeriod">
                        <div class="info-item">
                            <span class="info-label">逾期账期：</span>
                            <span class="info-value">{{ contractInfo.overduePeriod }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退租原因：</span>
                            <span class="info-value">{{ contractInfo.terminateReason }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">其他扣款：</span>
                            <span class="info-value">{{ contractInfo.hasOtherDeduction ? '是' : '否' }}</span>
                        </div>
                    </div>
                    <div class="info-row" v-if="contractInfo.terminateRemark">
                        <div class="info-item full-width">
                            <span class="info-label">退租说明：</span>
                            <span class="info-value">{{ contractInfo.terminateRemark }}</span>
                        </div>
                    </div>
                    <!-- 退租房间信息 -->
                    <!-- <div class="room-info" v-if="contractInfo.roomList && contractInfo.roomList.length > 0">
                        <div class="room-title">退租房间：</div>
                        <div class="room-list">
                            <span v-for="(room, index) in contractInfo.roomList" :key="room.id || index" class="room-item">
                                {{ room.roomName }}{{ index < contractInfo.roomList.length - 1 ? '、' : '' }}
                            </span>
                        </div>
                    </div> -->
                </div>
            </div>
            <div class="form-section">
                <section-title title="退款结算明细" style="margin-bottom: 16px;"/>
                    <!-- <a-button type="text" @click="handleViewContractDetail">查看详情</a-button> -->
                                <!-- 退租费用明细表格 - 数据来源：退租详情接口的costList字段 -->
                                <div class="refund-detail">
                    <a-table :data="refundDetailData" :columns="refundDetailColumns" :pagination="false"
                        :bordered="{ cell: true }">
                        <template #feeExplain="{ record }">
                            <span>{{ record.feeExplain }}</span>
                        </template>
                    </a-table>
                    <div class="total-sum">
                        <span>费用合计：<span class="money">{{ formatMoney(totalFeeAmount) }}</span>元{{ feeDescription }}</span>
                        <span>减免金额：<span class="money">{{ formatMoney(discountAmount) }}</span>元</span>
                        <span>最终费用金额：<span class="money">{{ formatMoney(finalAmount) }}</span>元{{ finalFeeDescription }}</span>
                    </div>
                    <div class="discount-reason">
                        <span>减免原因：</span>
                        <span>{{ discountReason }}</span>
                    </div>
                </div>
            </div>


            <!-- 手续办理情况 -->
            <div class="form-section">
                <section-title title="手续办理情况" style="margin-bottom: 16px;"/>
                <div class="procedure-status">
                    <div class="procedure-item">
                        <span class="procedure-name">营业执照：</span>
                        <span class="procedure-value">{{ procedureStatus.businessLicense ? '已办理' : '未办理' }}</span>
                    </div>
                    <div class="procedure-item">
                        <span class="procedure-name">税务登记证：</span>
                        <span class="procedure-value">{{ procedureStatus.taxRegistration ? '已办理' : '未办理' }}</span>
                    </div>
                </div>
            </div>

        </div>

        <!-- 底部按钮 -->
        <template #footer>
            <div class="drawer-footer">
                <a-space>
                    <a-button @click="handleCancel">{{ isViewMode ? '关闭' : '取消' }}</a-button>
                    <template v-if="!isViewMode">
                        <a-button @click="handleSave" type="primary" status="success">暂存</a-button>
                        <a-button type="primary" @click="handleSubmit">发起审批</a-button>
                    </template>
                </a-space>
            </div>
        </template>
    </a-drawer>

    <!-- 退租详情抽屉 -->
    <contract-termination-main 
        v-model="terminationDetailVisible"
        :terminate-id="currentTerminateId"
        :view-mode="true"
        @close="handleTerminationDetailClose"
        ref="contractTerminationRef" />
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';
import type { TableColumnData } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue';
import dayjs from 'dayjs';
import UploadFile from '@/components/upload/uploadFile.vue';
import ContractTerminationMain from '@/views/contractManage/contractTerminationMain.vue';
import SectionTitle from '@/components/sectionTitle/index.vue';
import { getFinancialRefundDetail, saveFinancialRefund, type FinancialRefundDetailVo, type FinancialRefundAddDTO, downloadTemplate } from '@/api/refundManage';
import { getDictLabel } from '@/dict';

// 退款明细数据接口
interface RefundDetailItem {
    paymentType: '收' | '支';
    feeItem: string;
    amount: number;
    feePeriod: string;
    feeExplain: string;
}

// 抽屉控制
const drawerVisible = ref(false);
const drawerTitle = ref('退款申请单');
const isViewMode = ref(false); // 添加查看模式状态

// 退租详情弹窗控制
const terminationDetailVisible = ref(false);
const currentTerminateId = ref('');
const contractTerminationRef = ref();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<Omit<FinancialRefundAddDTO, 'isSubmit'> & { isSubmit: number; orderNo?: string }>({
    refundType: 0, // 默认退租退款
    applyTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    feeType: '',
    refundAmount: 0,
    refundWay: 0, // 默认原路退回
    receiverName: '',
    receiverAccount: '',
    receiverBank: '',
    refundRemark: '',
    attachments: '',
    isSubmit: 0,
    projectId: '',
    orderNo: '',
    bizId: ''
});

// 表单校验规则
const rules = {
    refundType: [
        { required: true, message: '请选择退款类型' }
    ],
    applyTime: [
        { required: true, message: '请选择退款申请日期' }
    ],
    refundAmount: [
        { required: true, message: '请输入退款金额' },
        { type: 'number', min: 0, message: '退款金额必须大于0' }
    ],
    refundWay: [
        { required: true, message: '请选择退款方式' }
    ],
    receiverName: [
        { required: true, message: '请输入收款方姓名' }
    ],
    receiverBank: [
        { required: true, message: '请输入收款方开户行' }
    ]
};

// 合同信息
const contractInfo = reactive({
    tenantName: '',
    refundType: '1',
    refundDate: '',
    contractPurpose: '',
    contractNo: '',
    startDate: '', // 添加startDate属性
    endDate: '',
    // 扩展更多退租详情信息
    bondReceivedAmount: 0,
    rentReceivedAmount: 0,
    rentOverdueAmount: 0,
    receivedPeriod: '',
    overduePeriod: '',
    terminateReason: '',
    otherReasonDesc: '',
    hasOtherDeduction: false,
    otherDeductionDesc: '',
    terminateRemark: '',
    roomList: [] as any[]
});

// 退租费用明细表格列定义
const refundDetailColumns = ref<TableColumnData[]>([
    {
        title: '收支类型',
        dataIndex: 'paymentType',
        align: 'center'
    },
    {
        title: '费用科目',
        dataIndex: 'feeItem',
        align: 'center'
    },
    {
        title: '金额（元）',
        dataIndex: 'amount',
        align: 'center',
        render: ({ record }: { record: RefundDetailItem }) => {
            return formatMoney(record.amount);
        }
    },
    {
        title: '费用周期',
        dataIndex: 'feePeriod',
        align: 'center'
    },
    {
        title: '费用说明',
        dataIndex: 'feeExplain',
        slotName: 'feeExplain',
        align: 'center'
    }
]);

// 获取合同用途文本
const getContractPurposeText = (purpose: string) => {
    return getDictLabel('diversification_purpose', purpose?.toString()) || '未知用途';
};

// 退款费用明细数据
const refundDetailData = ref<RefundDetailItem[]>([]);

// 计算减免金额和最终金额 - 参考出场管理模块的计算逻辑
const discountAmount = ref(0.00);

// 计算总费用金额（参考出场管理exitSettlementForm的totalFeeAmount计算逻辑）
const totalFeeAmount = computed(() => {
    const total = refundDetailData.value.reduce((sum, item) => {
        const amount = parseFloat(String(item.amount || 0))
        if (item.paymentType === '收') { // 收入项
            return sum + amount
        } else { // 支出项
            return sum - amount
        }
    }, 0)
    // 保留2位小数并转为数字，避免浮点数精度问题
    return Math.round(total * 100) / 100
})

// 费用描述
const feeDescription = computed(() => {
    return totalFeeAmount.value >= 0 ? '(应收)' : '(应退)'
})

// 最终费用金额（参考出场管理exitSettlementForm的finalFeeAmount计算逻辑）
const finalAmount = computed(() => {
    let amount = totalFeeAmount.value
    if (discountAmount.value) {
        const discount = parseFloat(String(discountAmount.value || 0))
        amount -= discount
    }
    // 保留2位小数并转为数字
    return Math.round(amount * 100) / 100
})

// 最终费用描述
const finalFeeDescription = computed(() => {
    return finalAmount.value >= 0 ? '(应收)' : '(应退)'
})

// 减免原因
const discountReason = ref('');

// 手续办理情况
const procedureStatus = reactive({
    businessLicense: false, // 营业执照
    taxRegistration: false  // 税务登记证
});

// 退款类型名称映射
const getRefundTypeName = (type: string) => {
    const typeMap: Record<string, string> = {
        '0': '到期退租',
        '1': '提前退租'
    };
    return typeMap[type] || '未知类型';
};

// 金额格式化 - 参考出场管理模块
const formatMoney = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return '0.00'
    // 保留2位小数并转为数字，避免浮点数精度问题  
    const roundedAmount = Math.round((amount || 0) * 100) / 100
    return roundedAmount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 下载退款申请单-代收模板
const handleAgentAttachmentsDownload = () => {
    console.log('下载退款申请单-代收模板');
    /**
     * {
    "msg": "获取模版URL成功",
    "code": 200,
    "templateUrl": "http://172.30.1.254:8570/statics/2025/06/27/退款申请单（代收）20250619_20250627132900A038.docx"
}*/ 
    downloadTemplate().then(res => {
        console.log('下载退款申请单-代收模板', res);
        window.open(res.templateUrl, '_blank');
    }).catch(err => {
        console.error('下载退款申请单-代收模板失败:', err);
    });
};

// 是否第三方代收
const handleIsThirdAgentChange = (value: number) => {
    // 是否第三方代收 0-否,1-是 是的话 ，退款方式改成 银行转账
    console.log('是否第三方代收', value);
    // formData.isThirdAgent = value === 1;
    if (value) {
        formData.refundWay = 1;
    } else {
        formData.refundWay = 0;
    }
}

// 加载退款详情
const loadRefundDetail = async (refundId: string) => {
    try {
        const response = await getFinancialRefundDetail(refundId, 0, '');
        if (response.code === 200 && response.data) {
            const detailData = response.data;
            
            // 填充表单数据 - 退款申请信息字段refund
            if (detailData.refund) {
                const refund = detailData.refund;

                formData.id = refundId; // 设置退款单ID
                formData.bizId = refund.bizId || '';
                formData.refundType = 0; // 固定为退租退款
                formData.applyTime = refund.applyTime || dayjs().format('YYYY-MM-DD HH:mm:ss');
                formData.feeType = refund.feeType || '';
                formData.refundAmount = refund.refundAmount || 0;
                formData.refundWay = refund.refundWay ?? 0;
                formData.receiverName = refund.receiverName || '';
                formData.receiverAccount = refund.receiverAccount || '';
                formData.receiverBank = refund.receiverBank || '';
                formData.refundRemark = refund.refundRemark || '';
                formData.attachments = refund.attachments || '';
                formData.orderNo = refund.refundNo || '';
                formData.projectId = refund.projectId || '';
                formData.isThirdAgent = refund.isThirdAgent || false;
                formData.agentAttachments = refund.agentAttachments || '';
                formData.agentName = refund.agentName || '';
                formData.agentAccount = refund.agentAccount || '';
                formData.agentBank = refund.agentBank || '';
                // formData.bizId = refund.bizId || '';
            }

            // 填充退租详情信息 - 使用 exit.contractTerminateInfo 字段
            if (detailData.exit?.contractTerminateInfo) {
                const terminate = detailData.exit.contractTerminateInfo;

                contractInfo.tenantName = ''; // 需要从其他地方获取客户名称
                contractInfo.refundType = terminate.terminateType?.toString() || '1';
                contractInfo.refundDate = terminate.terminateDate || '';
                contractInfo.contractPurpose = ''; // 从其他地方获取
                contractInfo.endDate = ''; // 从其他地方获取
                contractInfo.bondReceivedAmount = terminate.bondReceivedAmount || 0;
                contractInfo.rentReceivedAmount = terminate.rentReceivedAmount || 0;
                contractInfo.rentOverdueAmount = terminate.rentOverdueAmount || 0;
                contractInfo.receivedPeriod = terminate.receivedPeriod || '';
                contractInfo.overduePeriod = terminate.overduePeriod || '';
                contractInfo.terminateReason = terminate.terminateReason || '';
                contractInfo.otherReasonDesc = terminate.otherReasonDesc || '';
                contractInfo.hasOtherDeduction = terminate.hasOtherDeduction || false;
                contractInfo.otherDeductionDesc = terminate.otherDeductionDesc || '';
                contractInfo.terminateRemark = terminate.terminateRemark || '';
                
                // 从terminate.contract中获取合同信息（如果存在的话）
                const contract = (terminate as any).contract;
                if (contract) {
                    contractInfo.contractPurpose = contract.contractPurpose || '';
                    contractInfo.contractNo = contract.contractNo || '';
                    contractInfo.startDate = contract.startDate || '';
                    contractInfo.endDate = contract.endDate || '';
                }
            }

            // 填充出场信息 - 使用 exit.exitInfo 获取客户名称等信息
            if (detailData.exit?.exitInfo) {
                const exitInfo = detailData.exit.exitInfo;
                contractInfo.tenantName = exitInfo.customerName || '';
                contractInfo.contractNo = exitInfo.contractNo || '';

                // 更新其他可能的字段
                if (!formData.projectId) {
                    formData.projectId = exitInfo.projectId || '';
                }
            }

            // 填充退租房间信息 - 使用 exit.exitRoomList
            if (detailData.exit?.exitRoomList && Array.isArray(detailData.exit.exitRoomList)) {
                contractInfo.roomList = detailData.exit.exitRoomList.map(room => ({
                    roomName: room.roomName || '',
                    propertyType: room.propertyType || 0,
                    parcelName: room.parcelName || '',
                    buildingName: room.buildingName || '',
                    exitDate: room.exitDate || ''
                }));
            }

            // 填充退租费用明细数据 - 使用 exit.exitCostList 字段
            if (detailData.exit?.exitCostList && Array.isArray(detailData.exit.exitCostList)) {
                refundDetailData.value = detailData.exit.exitCostList.map(cost => {
                    // 根据 payType 字段判断收支类型：1-收入, 2-支出
                    // 参考出场管理exitSettlementForm的逻辑
                    const paymentType = cost.payType === 1 ? '收' : '支';
                    
                    return {
                        paymentType,
                        feeItem: cost.subjectName || '',
                        amount: cost.amount || 0,
                        feePeriod: cost.startDate && cost.endDate ? `${cost.startDate} ~ ${cost.endDate}` : '',
                        feeExplain: cost.remark || ''
                    };
                });
                console.log('退租费用明细:', refundDetailData.value);

                // 等待nextTick确保计算属性已更新，然后设置退款金额
                await nextTick();
                // 如果是新建模式且当前退款金额为0，自动设置退款金额为计算出的最终金额的绝对值
                if (!formData.id && formData.refundAmount === 0 && finalAmount.value < 0) {
                    formData.refundAmount = Math.abs(finalAmount.value);
                }
            }
            
            console.log('已加载退款详情:', detailData);
        } else {
            Message.error('加载退款详情失败');
        }
    } catch (error) {
        console.error('加载退款详情失败:', error);
        Message.error('加载退款详情失败');
    }
};

// 查看退租详情
const handleViewContractDetail = () => {
    console.log('查看退租详情', contractInfo.contractNo);
    
    // 从formData中获取bizId作为terminateId
    if (formData.bizId) {
        currentTerminateId.value = formData.bizId;
        terminationDetailVisible.value = true;
    } else {
        Message.warning('退租信息不完整，无法查看详情');
    }
};

// 关闭退租详情弹窗
const handleTerminationDetailClose = () => {
    terminationDetailVisible.value = false;
    currentTerminateId.value = '';
};

// 取消
const handleCancel = () => {
    drawerVisible.value = false;
};

// 保存为草稿
const handleSave = async () => {
    const validResult = await formRef.value?.validate();
    if (!validResult) {
        try {
            formData.isSubmit = 0;
            // attachments为空时传undefined，避免JSON转换错误
            if (!formData.attachments) {
                formData.attachments = undefined;
            }
            if (!formData.agentAttachments) {
                formData.agentAttachments = undefined;
            }
            // 如果isThirdAgent为true，则需要传agentAttachments
            if (formData.isThirdAgent && !formData.agentAttachments) {
                Message.error('请上传退款申请单-代收');
                return;
            }
            
            // 如果存在 id（退款单ID）就不需要传 bizId
            const requestData = { ...formData, isSubmit: 0 };

            console.log('保存申请单', requestData);

            // return
            
            await saveFinancialRefund(requestData);
            Message.success('保存成功');
            drawerVisible.value = false;
        } catch (error) {
            console.error('保存失败:', error);
            // Message.error('保存失败');
        }
    }
};

// 提交审批
const handleSubmit = async () => {
    const validResult = await formRef.value?.validate();
    if (!validResult) {
        try {
            formData.isSubmit = 1;
            // attachments为空时传undefined，避免JSON转换错误
            if (!formData.attachments) {
                formData.attachments = undefined;
            }
            if (!formData.agentAttachments) {
                formData.agentAttachments = undefined;
            }
            // 如果isThirdAgent为true，则需要传agentAttachments
            if (formData.isThirdAgent && !formData.agentAttachments) {
                Message.error('请上传退款申请单-代收');
                return;
            }
            

            const requestData = { ...formData, isSubmit: 1 };

            
            await saveFinancialRefund(requestData);
            Message.success('提交成功');
            drawerVisible.value = false;
        } catch (error) {
            console.error('提交失败:', error);
            Message.error('提交失败');
        }
    }
};

// 暴露方法给父组件
defineExpose({
    async open(options?: { record?: any; refundId?: string; terminateId?: string; id?: string; mode?: 'create' | 'edit' | 'view' }) {
        console.log('options', options, options?.id);
        
        // 清空表单验证状态
        formRef.value?.clearValidate();
        
        // 设置模式
        isViewMode.value = options?.mode === 'view';
        
        // 重置表单数据
        Object.assign(formData, {
            id: '',
            refundType: 0, // 固定为退租退款
            applyTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            feeType: '',
            refundAmount: 0,
            refundWay: 0,
            receiverName: '',
            receiverAccount: '',
            receiverBank: '',
            refundRemark: '',
            attachments: '',
            isSubmit: 0,
            orderNo: '',
            bizId: '',
            projectId: ''
        });

        // 重置合同信息
        Object.assign(contractInfo, {
            tenantName: '',
            refundType: '1',
            refundDate: '',
            contractPurpose: '',
            contractNo: '',
            startDate: '',
            endDate: '',
            bondReceivedAmount: 0,
            rentReceivedAmount: 0,
            rentOverdueAmount: 0,
            receivedPeriod: '',
            overduePeriod: '',
            terminateReason: '',
            otherReasonDesc: '',
            hasOtherDeduction: false,
            otherDeductionDesc: '',
            terminateRemark: '',
            roomList: []
        });

        // 重置明细数据
        refundDetailData.value = [];
        
        // 重置减免金额和原因
        discountAmount.value = 0;
        discountReason.value = '';
        
        // 重置手续办理情况
        Object.assign(procedureStatus, {
            businessLicense: false,
            taxRegistration: false
        });

        // 如果传入了 record，设置基本的业务ID和项目ID
        if (options?.record) {
            const record = options.record;
            formData.bizId = record.id || '';
            formData.projectId = record.projectId || '';
            
            // 可以从record中获取基本信息（如果需要的话）
            // contractInfo.tenantName = record.tenantName || '';
            // contractInfo.contractNo = record.contractNo || '';
        }

        // 加载退款详情 - 优先使用refundId，否则使用bizId或terminateId
        if (options?.record?.refundId) {
            await loadRefundDetail(options.record.refundId);
        } else if (options?.record?.id) {
            // 使用 terminateId 或 bizId 调用接口获取退租退款详情
            console.log('根据id获取的退款详情:', options?.record);
            try {
                const bizId = options?.record?.id;
                
                // 直接调用 getFinancialRefundDetail 获取所有数据
                const response = await getFinancialRefundDetail(undefined, 0, bizId);
                
                if (response.code === 200 && response.data) {
                    // 处理获取到的数据
                    const detailData = response.data;
                    // const booking = detailData.booking;
                    // console.log('根据bizId获取的退款详情:', detailData, booking);
                    
                    // 填充表单数据
                    if (detailData.refund) {
                        const refund = detailData.refund;
                        formData.id = refund.id || ''; // 设置退款单ID
                        formData.refundType = 0;
                        formData.applyTime = refund.applyTime || dayjs().format('YYYY-MM-DD HH:mm:ss');
                        formData.feeType = refund.feeType || '';
                        formData.refundAmount = refund.refundAmount || 0;
                        formData.refundWay = refund.refundWay ?? 0;
                        formData.receiverName = refund.receiverName || '';
                        formData.receiverAccount = refund.receiverAccount || '';
                        formData.receiverBank = refund.receiverBank || '';
                        formData.refundRemark = refund.refundRemark || '';
                        formData.attachments = refund.attachments || '';
                        formData.orderNo = refund.refundNo || '';
                        formData.projectId = refund.projectId || '';
                        formData.bizId = refund.bizId || bizId;
                    }else {
                        // formData.receiverName = booking?.customerName || '';
                    }

                    // 填充退租详情信息 - 使用 exit.contractTerminateInfo 字段
                    if (detailData.exit?.contractTerminateInfo) {
                        const terminate = detailData.exit.contractTerminateInfo;
                        contractInfo.refundType = terminate.terminateType?.toString() || '1';
                        contractInfo.refundDate = terminate.terminateDate || '';
                        contractInfo.contractNo = terminate.contractId || '';
                        contractInfo.bondReceivedAmount = terminate.bondReceivedAmount || 0;
                        contractInfo.rentReceivedAmount = terminate.rentReceivedAmount || 0;
                        contractInfo.rentOverdueAmount = terminate.rentOverdueAmount || 0;
                        contractInfo.receivedPeriod = terminate.receivedPeriod || '';
                        contractInfo.overduePeriod = terminate.overduePeriod || '';
                        contractInfo.terminateReason = terminate.terminateReason || '';
                        contractInfo.otherReasonDesc = terminate.otherReasonDesc || '';
                        contractInfo.hasOtherDeduction = terminate.hasOtherDeduction || false;
                        contractInfo.otherDeductionDesc = terminate.otherDeductionDesc || '';
                        contractInfo.terminateRemark = terminate.terminateRemark || '';
                    }

                    // 填充出场信息 - 使用 exit.exitInfo 获取客户名称等信息
                    if (detailData.exit?.exitInfo) {
                        const exitInfo = detailData.exit.exitInfo;
                        contractInfo.tenantName = exitInfo.customerName || '';
                        contractInfo.contractNo = exitInfo.contractNo || contractInfo.contractNo;
                        if (!formData.projectId) {
                            formData.projectId = exitInfo.projectId || '';
                        }
                    }

                    // 填充退租房间信息 - 使用 exit.exitRoomList
                    if (detailData.exit?.exitRoomList && Array.isArray(detailData.exit.exitRoomList)) {
                        contractInfo.roomList = detailData.exit.exitRoomList.map(room => ({
                            roomName: room.roomName || '',
                            propertyType: room.propertyType || 0,
                            parcelName: room.parcelName || '',
                            buildingName: room.buildingName || '',
                            exitDate: room.exitDate || ''
                        }));
                    }

                    // 填充退租费用明细数据 - 使用 exit.exitCostList 字段
                    if (detailData.exit?.exitCostList && Array.isArray(detailData.exit.exitCostList)) {
                        refundDetailData.value = detailData.exit.exitCostList.map(cost => {
                            // 根据 payType 字段判断收支类型：1-收入, 2-支出
                            // 参考出场管理exitSettlementForm的逻辑
                            const paymentType = cost.payType === 1 ? '收' : '支';
                            
                            return {
                                paymentType,
                                feeItem: cost.subjectName || '',
                                amount: cost.amount || 0,
                                feePeriod: cost.startDate && cost.endDate ? `${cost.startDate} ~ ${cost.endDate}` : '',
                                feeExplain: cost.remark || ''
                            };
                        });
                        console.log('退租费用明细:', refundDetailData.value);
                    }
                    
                    // 如果没有从退款详情中获取到bizId，使用传入的bizId
                    if (!formData.bizId) {
                        formData.bizId = bizId;
                    }

                    // 等待nextTick确保计算属性已更新，然后设置退款金额
                    await nextTick();
                    // 如果是新建模式，自动设置退款金额为计算出的最终金额的绝对值
                    if (!formData.id && finalAmount.value < 0) {
                        formData.refundAmount = Math.abs(finalAmount.value);
                    }
                } else {
                    Message.error('获取退款详情失败');
                }
            } catch (error) {
                console.error('获取退款详情失败:', error);
                Message.error('获取退款详情失败');
            }
        }
        
        // 根据模式设置抽屉标题
        if (isViewMode.value) {
            drawerTitle.value = '退款详情';
        } else {
            drawerTitle.value = '退款申请单';
        }
        
        // 确保所有数据加载完成后再显示抽屉
        await nextTick();
        drawerVisible.value = true;
    }
});
</script>

<style scoped lang="less">
.refund-form-container {
    padding: 0 16px;

    .form-section {
        margin-bottom: 24px;



        .base-info {
            background-color: #f9f9f9;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 16px;

            .info-row {
                display: flex;
                margin-bottom: 12px;

                &:last-child {
                    margin-bottom: 0;
                }

                                    .info-item {
                        flex: 1;
                        display: flex;

                        &.full-width {
                            flex: 3;
                        }

                        .info-label {
                            color: #646a73;
                            margin-right: 8px;
                            min-width: 90px;
                        }

                        .info-value {
                            color: #1d2129;
                            flex: 1;
                        }
                    }
                }
                .room-info {
                    margin-top: 12px;
                    padding-top: 12px;
                    border-top: 1px solid #e6e8eb;

                    .room-title {
                        color: #646a73;
                        margin-bottom: 8px;
                        font-weight: 500;
                    }

                    .room-list {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 8px;

                        .room-item {
                            color: #1d2129;
                            padding: 4px 8px;
                            background-color: #f2f3f5;
                            border-radius: 4px;
                            font-size: 12px;
                        }
                    }
            }
        }

        .refund-detail {
            margin-top: 16px;

            .total-sum {
                display: flex;
                justify-content: flex-end;
                margin-top: 16px;
                gap: 24px;

                .money {
                    font-weight: 600;
                    color: #f56c6c;
                }
            }

            .discount-reason {
                display: flex;
                justify-content: flex-end;
                margin-top: 12px;
                color: #646a73;
            }
        }

        .procedure-status {
            display: flex;
            gap: 24px;
            background-color: #f9f9f9;
            padding: 16px;
            border-radius: 4px;

            .procedure-item {
                display: flex;
                align-items: center;

                .procedure-name {
                    color: #646a73;
                    margin-right: 8px;
                }

                .procedure-value {
                    color: #1d2129;
                    font-weight: 500;
                }
            }
        }
    }
}

.drawer-footer {
    display: flex;
    justify-content: flex-end;
}

.tip-text {
    position: relative;
    left: 140px;
    top: -10px;
    width: 60%;
    color: #F53F3F;
    font-size: 11px;
    // line-height: 20px;
}
</style> 