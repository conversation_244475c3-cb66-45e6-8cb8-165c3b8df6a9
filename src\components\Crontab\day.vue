<template>
    <a-form :model="{}" :label-col-props="{ span: 1 }">
        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="1"> </a-radio>
                <span class="radio-span">日，允许的通配符[, - * ? / L W]</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="2"> </a-radio>
                <span class="radio-span">不指定</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="3"> </a-radio>
                <span class="radio-span">周期从</span>
                <a-input-number v-model:model-value="cycle01" :min="1" :max="30" />
                -
                <a-input-number v-model:model-value="cycle02" :min="cycle01 + 1" :max="31" />
                <span class="radio-span">日</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="4"> </a-radio>
                <span class="radio-span">从</span>
                <a-input-number v-model:model-value="average01" :min="1" :max="30" />
                <span class="radio-span">号开始，每</span>
                <a-input-number v-model:model-value="average02" :min="1" :max="31 - average01" />
                <span class="radio-span">日执行一次</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="5"> </a-radio>
                <span class="radio-span">每月</span>
                <a-input-number v-model:model-value="workday" :min="1" :max="31" />
                <span class="radio-span">号最近的那个工作日</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="6"> </a-radio>
                <span class="radio-span">本月最后一天</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="7"> </a-radio>
                <span class="radio-span">指定</span>
                <a-select v-model:model-value="checkboxList" placeholder="可多选" multiple :max-tag-count="10" allow-clear>
                    <a-option v-for="item in dayList" :key="item.value" :value="item.value" :label="item.label" />
                </a-select>
            </a-space>
        </a-form-item>
    </a-form>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';

interface CronProps {
    second: string;
    min: string;
    hour: string;
    day: string;
    month: string;
    week: string;
    year: string;
}

interface Props {
    cron: CronProps;
    check: (value: number, min: number, max: number) => number;
}

const emit = defineEmits(['update'])

const props = withDefaults(defineProps<Props>(), {
    cron: () => ({
        second: "*",
        min: "*",
        hour: "*",
        day: "*",
        month: "*",
        week: "?",
        year: "",
    }),
    check: () => (value: number) => value,
});

const radioValue = ref(1);
const cycle01 = ref(1);
const cycle02 = ref(2);
const average01 = ref(1);
const average02 = ref(1);
const workday = ref(1);
const checkboxList = ref<number[]>([]);
const checkCopy = ref<number[]>([1]);

const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, 1, 30);
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, 31);
    return `${cycle01.value}-${cycle02.value}`;
});

const averageTotal = computed(() => {
    average01.value = props.check(average01.value, 1, 30);
    average02.value = props.check(average02.value, 1, 31 - average01.value);
    return `${average01.value}/${average02.value}`;
});

const workdayTotal = computed(() => {
    workday.value = props.check(workday.value, 1, 31);
    return `${workday.value}W`;
});

const checkboxString = computed(() => {
    return checkboxList.value.join(',');
});

watch(() => props.cron.day, value => changeRadioValue(value))
watch([radioValue, cycleTotal, averageTotal, workdayTotal, checkboxString], () => onRadioChange())
function changeRadioValue(value: string): void {
    if (value === "*") {
        radioValue.value = 1
    } else if (value === "?") {
        radioValue.value = 2
    } else if (value.indexOf("-") > -1) {
        const indexArr = value.split('-')
        cycle01.value = Number(indexArr[0])
        cycle02.value = Number(indexArr[1])
        radioValue.value = 3
    } else if (value.indexOf("/") > -1) {
        const indexArr = value.split('/')
        average01.value = Number(indexArr[0])
        average02.value = Number(indexArr[1])
        radioValue.value = 4
    } else if (value.indexOf("W") > -1) {
        const indexArr = value.split("W")
        workday.value = Number(indexArr[0])
        radioValue.value = 5
    } else if (value === "L") {
        radioValue.value = 6
    } else {
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))]
        radioValue.value = 7
    }
}

function onRadioChange(): void {
    if (radioValue.value === 2 && props.cron.week === '?') {
        emit('update', 'week', '*', 'day')
    }
    if (radioValue.value !== 2 && props.cron.week !== '?') {
        emit('update', 'week', '?', 'day')
    }
    switch (radioValue.value) {
        case 1:
            emit('update', 'day', '*', 'day')
            break
        case 2:
            emit('update', 'day', '?', 'day')
            break
        case 3:
            emit('update', 'day', cycleTotal.value, 'day')
            break
        case 4:
            emit('update', 'day', averageTotal.value, 'day')
            break
        case 5:
            emit('update', 'day', workdayTotal.value, 'day')
            break
        case 6:
            emit('update', 'day', 'L', 'day')
            break
        case 7:
            if (checkboxList.value.length === 0) {
                checkboxList.value.push(checkCopy.value[0])
            } else {
                checkCopy.value = checkboxList.value
            }
            emit('update', 'day', checkboxString.value, 'day')
            break
    }
}

interface DayOption {
    label: string;
    value: number;
}

const dayList = computed(() => {
    const arr: DayOption[] = [];
    for (let i = 1; i <= 31; i++) {
        arr.push({
            label: String(i),
            value: i,
        });
    }
    return arr;
});
</script>

<style lang="less" scoped>
.arco-input-number {
    margin: 0 0.2rem;
}

.arco-select {
    margin: 0 0.2rem;
    width: 18.8rem;
}

.radio-span {
    flex-shrink: 0;
}
</style>