<template>
    <div>
        <a-modal
            v-model:visible="showDialog"
            title="选择位置"
            :width="'65%'"
            :mask-closable="true"
            @cancel="closeDialog"
            @after-open="opened"
            :key="mapDialogVisibleKey"
        >
            <baidu-map
                class="bm-view"
                :map-click="false"
                :center="center"
                :zoom="zoom"
                :scroll-wheel-zoom="true"
                ref="myMap"
                @ready="mapReady"
                @click="clickMap"
            ></baidu-map>
            <div class="search-box">
                <a-input
                    id="suggest-id"
                    v-model="searchValue"
                    placeholder="搜索"
                    allow-clear
                    @clear="clearMap"
                    class="input-with-select" @press-enter="handleInputSearch"
                />
            </div>

            <template #footer>
                <a-space>
                    <a-button type="primary" @click="confirmDialog">确 定</a-button>
                    <a-button @click="closeDialog">取 消</a-button>
                </a-space>
            </template>
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Message } from '@arco-design/web-vue';

// 定义百度地图相关类型
interface BMapPoint {
    lng: number;
    lat: number;
}

interface BMapMarker {
    point: BMapPoint;
    enableDragging: () => void;
    addEventListener: (event: string, callback: (e: any) => void) => void;
}

interface BMapInstance {
    Point: new (lng: number, lat: number) => BMapPoint;
    Marker: new (point: BMapPoint) => BMapMarker;
    Autocomplete: new (options: { input: string; location: any }) => {
        addEventListener: (event: string, callback: (e: any) => void) => void;
    };
    LocalSearch: new (map: any, options: { onSearchComplete: () => void }) => {
        search: (keyword: string) => void;
        getResults: () => {
            getPoi: (index: number) => {
                point: BMapPoint;
            };
        };
    };
}

interface MapInstance {
    disableDoubleClickZoom: () => void;
    clearOverlays: () => void;
    panTo: (point: BMapPoint) => void;
    addOverlay: (marker: BMapMarker) => void;
}

interface Props {
    visibleDialog: boolean;
    mapCenter: {
        lng?: number;
        lat?: number;
        name?: string;
        showMarker?: boolean;
    };
}

const props = defineProps<Props>();
const emit = defineEmits(['closeDialog', 'confirmDialog']);

const showDialog = ref(false);
const map = ref<MapInstance | null>(null);
const bmap = ref<BMapInstance | null>(null);
const searchValue = ref('');
const center = ref({
    lng: null as number | null,
    lat: null as number | null
});
const marker = ref<BMapMarker | null>(null);
const zoom = ref(15);
const mapDialogVisibleKey = ref(0);

watch(() => props.visibleDialog, (val) => {
    showDialog.value = val;
});

/**
 * 地图初始化
 */
const mapReady = ({ BMap, map: mapInstance }: { BMap: BMapInstance; map: MapInstance }) => {
    map.value = mapInstance;
    bmap.value = BMap;
    if (!map.value || !bmap.value) return;
    
    map.value.disableDoubleClickZoom();
    changeMapCenter();
    
    // 搜索
    const ac = new bmap.value.Autocomplete({ input: 'suggest-id', location: map.value });
    ac.addEventListener('onconfirm', (e: any) => {
        const value = e.item.value;
        searchValue.value = `${value.province}${value.city}${value.district}${value.street}${value.business}`;
        setPlace(true);
    });
};

/**
 * 点击地图
 */
const clickMap = (e: { point: BMapPoint }) => {
    if (!marker.value) {
        addMarker(e.point);
    }
};

/**
 * 搜索
 * @param showMarker {Boolean} 是否显示打点
 */
const setPlace = (showMarker: boolean) => {
    if (!map.value || !bmap.value) return;
    
    // 清除地图上所有覆盖物
    map.value.clearOverlays();
    const local = new bmap.value.LocalSearch(map.value, {
        // 智能搜索
        onSearchComplete: () => {
            // 获取第一个智能搜索的结果
            const result = local.getResults() && local.getResults().getPoi(0);
            console.log('result',result)
            console.log('center',center)
            if (result) {
                const pp = result.point;
                center.value.lng = Number(pp.lng);
                center.value.lat = Number(pp.lat);
                zoom.value = 15;
                map.value?.panTo(pp);
                if (showMarker) {
                    // 添加标注
                    addMarker(pp);
                }
            }
        }
    });
    local.search(searchValue.value ? searchValue.value : props.mapCenter.name || '');
};

/**
 * 改变地图中心点
 */
const changeMapCenter = () => {
    if (!map.value || !bmap.value) return;
    
    marker.value = null;
    if (props.mapCenter.lng && props.mapCenter.lat) {
        map.value.clearOverlays();
        center.value.lng = Number(props.mapCenter.lng);
        center.value.lat = Number(props.mapCenter.lat);
        zoom.value = 15;
        const pp = new bmap.value.Point(center.value.lng, center.value.lat);
        setTimeout(() => {
            map.value?.panTo(pp);
        }, 0);
        if (props.mapCenter.showMarker) {
            addMarker(pp);
        }
        return;
    }
    setPlace(false);
};

/**
 * 打点
 */
const addMarker = (point: BMapPoint) => {
    if (!map.value || !bmap.value) return;
    
    const newMarker = new bmap.value.Marker(point);
    newMarker.enableDragging();
    map.value.addOverlay(newMarker);
    newMarker.addEventListener('dragend', (e: any) => {
        const lat = e.point.lat;
        const lng = e.point.lng;
        map.value?.panTo(new bmap.value!.Point(lng, lat));
    });
    marker.value = newMarker;
};

/**
 * 清空选中的点
 */
const clearMap = () => {
    if (!map.value) return;
    
    map.value.clearOverlays();
    marker.value = null;
};

const opened = () => {
    searchValue.value = '';
    changeMapCenter();
};

const closeDialog = () => {
    emit('closeDialog');
};

const confirmDialog = () => {
    if (!marker.value) {
        Message.warning('请选择位置');
        return;
    }
    // 使用百度地图逆地理编码获取地址
    if (bmap.value && map.value && marker.value) {
        const geocoder = new (window as any).BMap.Geocoder();
        geocoder.getLocation(marker.value.point, (result: any) => {
            const address = result ? result.address : '';
            emit('confirmDialog', {
                lng: marker.value!.point.lng,
                lat: marker.value!.point.lat,
                address
            });
        });
    } else {
        emit('confirmDialog', {
            lng: marker.value.point.lng,
            lat: marker.value.point.lat,
            address: ''
        });
    }
};
const handleInputSearch = () => {
  setPlace(true);
};
</script>

<style lang="less" scoped>
.bm-view {
    width: 100%;
    height: 350px;
}

.search-box {
    position: absolute;
    top: 100px;
    left: 50px;
    width: 300px;
    .arco-input, .input-with-select {
        background: #fff !important;
        color: #222 !important;
    }
    :deep(.arco-input) {
        background: #fff !important;
        color: #222 !important;
    }
}
</style>
