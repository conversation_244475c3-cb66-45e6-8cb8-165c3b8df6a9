<template>
  <a-drawer
    v-model:visible="visible"
    class="common-drawer"
    :title="drawerTitle"
    :footer="!isReadOnly"
    @cancel="handleCancel"
  >
    <div class="add-property-container">
      <!-- 左侧菜单 -->
      <a-menu
        :selected-keys="[currentTab]"
        mode="vertical"
        :style="{ width: '160px', height: '100%' }"
        @menu-item-click="handleMenuClick"
      >
        <a-menu-item key="base">房源信息</a-menu-item>
        <a-menu-item key="other">其他信息</a-menu-item>
        <template v-if="isReadOnly&&!isAdjustMode">
          <a-menu-item key="pricing" style="display: none;">定价信息</a-menu-item>
          <a-menu-item key="history">变更历史</a-menu-item>
        </template>
      </a-menu>
      <!-- 右侧内容区 -->
      <div class="content-section scrollable-content">
        <div v-show="currentTab === 'base'">
          <section-title title="房源信息" class="first-child" />
          <a-form
            ref="baseFormRef"
            label-align="right"
            :model="baseForm"
            layout="horizontal"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            auto-label-width
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="房源名称" field="name" :rules="[{ required: true, message: '请输入房源名称' }]">
                  <a-input v-model="baseForm.name" placeholder="请输入房源名称" :disabled="isReadOnly" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="物业类型" field="propertyType" :rules="[{ required: true, message: '请选择物业类型' }]">
                  <a-tree-select
                    v-model="baseForm.propertyType"
                    :data="propertyTypeOptions"
                    placeholder="请选择物业类型"
                    allow-clear
                    :disabled="isReadOnly"
                    :field-names="{
                      key: 'dictValue',
                      title: 'dictLabel',
                      children: 'childList'
                    }"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属项目" field="project" :rules="[{ required: true, message: '请选择所属项目' }]">
                  <a-select v-model="baseForm.project" placeholder="请选择所属项目" :disabled="true">
                    <a-option :value="currentSelection?.projectId">{{ currentSelection?.projectName || '请选择项目' }}</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="地块" field="block" :rules="[{ required: true, message: '请选择地块' }]">
                  <a-select v-model="baseForm.block" placeholder="请选择地块" :disabled="isReadOnly||isAdjustMode">
                    <a-option v-for="item in parcelOptions" :key="item.id" :value="item.id">
                      {{ item.parcelName }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="楼栋" field="building" :rules="[{ required: true, message: '请选择楼栋' }]">
                  <a-select v-model="baseForm.building" placeholder="请选择楼栋" :disabled="isReadOnly||isAdjustMode">
                    <a-option v-for="item in buildingOptions" :key="item.id" :value="item.id">
                      {{ item.buildingName }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="楼层" field="floor" :rules="[{ required: true, message: '请选择楼层' }]">
                  <a-select v-model="baseForm.floor" placeholder="请选择楼层" :disabled="isReadOnly||isAdjustMode">
                    <a-option v-for="item in floorOptions" :key="item.id" :value="item.id" :label="item.floorName">
                      {{ item.floorName || '未命名楼层' }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="合成编码" field="code">
                  <a-input v-model="baseForm.code" placeholder="自动生成" :disabled="true" />
                </a-form-item>
              </a-col>
            </a-row>
            <section-title title="面积信息" />
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="面积类型" field="areaType" :rules="[{ required: true, message: '请选择面积类型' }]">
                  <a-select v-model="baseForm.areaType" placeholder="请选择面积类型" :disabled="isReadOnly">
                    <a-option value="实测">实测</a-option>
                    <a-option value="预测">预测</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="建筑面积" field="buildingArea" :rules="[{ required: true, message: '请输入建筑面积' }]">
                  <a-input v-model="baseForm.buildingArea" @blur="handleBlur('buildingArea')" placeholder="请输入建筑面积" :disabled="isReadOnly">
                    <template #append>㎡</template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="套内面积" field="innerArea">
                  <a-input v-model="baseForm.innerArea" @blur="handleBlur('innerArea')" placeholder="请输入套内面积" :disabled="isReadOnly">
                    <template #append>㎡</template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="计租面积类型" field="rentAreaType" :rules="[{ required: true, message: '请选择计租面积类型' }]">
                  <a-select v-model="baseForm.rentAreaType" placeholder="请选择计租面积类型" :disabled="true">
                    <a-option value="建筑面积">建筑面积</a-option>
                    <a-option value="套内面积">套内面积</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="计租面积" field="rentArea" :rules="[{ required: true, message: '请输入计租面积' }]">
                  <a-input v-model="baseForm.rentArea" :disabled="baseForm.rentAreaType === '建筑面积' || isReadOnly">
                    <template #append>㎡</template>
                  </a-input>
                </a-form-item>
              </a-col>

            </a-row>
            <section-title title="房型信息" />
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="层高" field="height">
                  <a-input v-model="baseForm.height" @blur="handleBlur('height')" placeholder="请输入层高" :disabled="isReadOnly">
                    <template #append>m</template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="荷载值" field="load">
                  <a-input v-model="baseForm.load" @blur="handleBlur('load')" placeholder="请输入荷载值" :disabled="isReadOnly">
                    <template #append>kg/㎡</template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="朝向" field="direction">
                  <a-select v-model="baseForm.direction" placeholder="请选择朝向" :disabled="isReadOnly">
                    <a-option v-for="item in roomOrientationOptions" :key="item.dictValue" :value="item.dictValue">
                      {{ item.dictLabel }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8" v-if="checkIsDormitory(baseForm.propertyType)">
                <a-form-item label="户型" field="layout">
                  <a-select v-model="baseForm.layout" placeholder="请选择户型" :disabled="isReadOnly">
                    <a-option v-for="item in houseTypeOptions" :key="item.id" :value="item.id">
                      {{ item.houseTypeName }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8" v-if="checkIsDormitory(baseForm.propertyType)" style="display: flex; align-items: center;height: 32px;">
                <a-link style="margin-left: 8px;" @click="handleViewRoomType">户型信息展开</a-link>
              </a-col>
            </a-row>
            <section-title title="智能设备" />
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="智能水电表" field="meter">
                  <div class="input-link-row">
                    <a-input v-model="baseForm.meter" placeholder="请输入智能水电表" :disabled="isReadOnly" />
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12" v-if="checkIsDormitory(baseForm.propertyType)">
                <a-form-item label="智能锁" field="lock">
                  <div class="input-link-row">
                    <a-input v-model="baseForm.lock" placeholder="请输入智能锁" :disabled="isReadOnly" />
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div v-show="currentTab === 'other'">
          <section-title title="资产属性" class="first-child" />
          <a-form
            ref="otherFormRef"
            label-align="right"
            :model="otherForm"
            layout="horizontal"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            auto-label-width
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="资产运营模式" field="operationMode" :rules="[{ required: true, message: '请选择资产运营模式' }]">
                  <a-select v-model="otherForm.operationMode" placeholder="请选择资产运营模式" :disabled="isReadOnly">
                    <a-option v-for="item in assetOperationModeOptions" :key="item.dictValue" :value="item.dictValue">
                      {{ item.dictLabel }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产运营分类" field="operationType" :rules="[{ required: true, message: '请选择资产运营分类' }]">
                  <a-select v-model="otherForm.operationType" placeholder="请选择资产运营分类" :disabled="isReadOnly">
                    <a-option v-for="item in assetOperationTypeOptions" :key="item.dictValue" :value="item.dictValue">
                      {{ item.dictLabel }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="产权情况" field="propertyStatus" :rules="[{ required: true, message: '请选择产权情况' }]">
                  <a-select v-model="otherForm.propertyStatus" placeholder="请选择产权情况" :disabled="isReadOnly">
                    <a-option value="无证">无证</a-option>
                    <a-option value="有证">有证</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="交付状态" field="deliveryStatus" :rules="[{ required: true, message: '请选择交付状态' }]">
                  <a-select v-model="otherForm.deliveryStatus" placeholder="请选择交付状态" :disabled="isReadOnly">
                    <a-option value="未交付">未交付</a-option>
                    <a-option value="已交付">已交付</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="特殊标签" field="tags">
                  <a-checkbox v-model="otherForm.isGuarantee" :disabled="isReadOnly">保障房</a-checkbox>
                </a-form-item>
              </a-col>
            </a-row>
            <section-title title="承租信息" />
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="商管承租成本单价(第一次)" field="firstRentCost">
                  <a-input v-model="otherForm.firstRentCost" type="number" placeholder="请输入商管承租成本单价(第一次)" :disabled="isReadOnly">
                    <template #append>元/平米/月</template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="商管承租日期(第一次)" field="firstRentDate">
                  <a-range-picker v-model="otherForm.firstRentDate" style="width:100%" :disabled="isReadOnly" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="商管承租成本单价(最新)" field="latestRentCost">
                  <a-input v-model="otherForm.latestRentCost" type="number" placeholder="请输入商管承租成本单价(最新)" :disabled="isReadOnly">
                    <template #append>元/平米/月</template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="商管承租日期(最新)" field="latestRentDate">
                  <a-range-picker v-model="otherForm.latestRentDate" style="width:100%" :disabled="isReadOnly" />
                </a-form-item>
              </a-col>
            </a-row>
            <section-title title="运营现状" />
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="运营主体" field="mainBody" :rules="[{ required: true, message: '请选择运营主体' }]">
                  <a-select v-model="otherForm.mainBody" placeholder="请选择运营主体" :disabled="isReadOnly">
                    <a-option :value="1">商服</a-option>
                    <a-option :value="2">众创城</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="可招商日期" field="recruitDate">
                  <a-date-picker v-model="otherForm.recruitDate" style="width:100%" :disabled="isReadOnly" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="对外出租起始日期" field="leaseStartDate">
                  <a-date-picker v-model="otherForm.leaseStartDate" style="width:100%" :disabled="isReadOnly" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否自用" field="isSelfUse">
                  <a-select v-model="otherForm.isSelfUse" placeholder="请选择是否自用" :disabled="isReadOnly">
                    <a-option value="是">是</a-option>
                    <a-option value="否">否</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8" v-if="otherForm.isSelfUse === '是'">
                <a-form-item label="自用主体" field="selfUseMainBody" :rules="[{ required: true, message: '请选择自用主体' }]">
                  <a-select v-model="otherForm.selfUseMainBody" placeholder="请选择自用主体" :disabled="isReadOnly">
                    <a-option v-for="item in selfUseSubjectOptions" :key="item.dictValue" :value="item.dictValue">
                      {{ item.dictLabel }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="24" v-if="otherForm.isSelfUse === '是'">
                <a-form-item label="自用用途" field="selfUsePurpose" :rules="[{ required: true, message: '请输入自用用途' }]">
                  <a-textarea v-model="otherForm.selfUsePurpose" placeholder="请输入自用用途" :disabled="isReadOnly" :max-length="200" allow-clear show-word-limit/>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div v-show="currentTab === 'pricing'" v-if="isReadOnly">
          <section-title title="定价信息" class="first-child" />
          
          <!-- 基础定价信息 -->
          <div class="pricing-info">
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="pricing-item">
                  <span class="label">规划业态：</span>
                  <span class="value">{{ pricingForm.planType }}</span>
                </div>
              </a-col>
              <a-col :span="10">
                <div class="pricing-item">
                  <span class="label">基础租金：</span>
                  <span class="value">{{ pricingForm.baseRent }}元</span>
                </div>
              </a-col>
              <a-col :span="6">
                <div class="pricing-item">
                  <span class="label">附加费用：</span>
                  <span class="value">{{ pricingForm.additionalFee }}元</span>
                </div>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="pricing-item">
                  <span class="label">表价：</span>
                  <span class="value">{{ pricingForm.listPrice }}元</span>
                  <span class="price-note">（表价=基础租金+附加费用）</span>
                </div>
              </a-col>
              <a-col :span="10">
                <div class="pricing-item">
                  <span class="label">底价(不含免租期)：</span>
                  <span class="value">{{ pricingForm.basePrice }}元</span>
                  <span class="price-note">（底价（不含免租期）=表价*最大折扣）</span>
                </div>
              </a-col>
              <a-col :span="6">
                <div class="pricing-item">
                  <span class="label">计租单位：</span>
                  <span class="value">{{ pricingForm.rentUnit }}</span>
                  <a-link class="expand-link" @click="showPricingDetail = !showPricingDetail">
                    {{ showPricingDetail ? '收起' : '定价详情展开' }}
                  </a-link>
                </div>
              </a-col>
            </a-row>
          </div>

          <!-- 展开后的详细定价信息 -->
          <div v-if="showPricingDetail" style="margin-top: 16px;">
            <section-title title="免租期及底价" />
            <div class="pricing-info">
              <div class="pricing-row">
                <div class="pricing-item">
                  <span class="label">参考因素：</span>
                  <span class="value">租赁期限</span>
                </div>
              </div>
            </div>
            <a-table :columns="rentPeriodColumns" :data="rentPeriodData" :pagination="false" :bordered="{ cell: true }">
              <template #header>租赁期限对应免租期及底价</template>
            </a-table>

            <section-title title="其他信息" style="margin-top: 16px;" />
            <div class="pricing-info">
              <div class="pricing-row">
                <div class="pricing-item">
                  <span class="label">租金是否递增：</span>
                  <span class="value">{{ pricingDetailForm.hasIncrease === 'yes' ? '是' : '否' }}</span>
                </div>
                <template v-if="pricingDetailForm.hasIncrease === 'yes'">
                  <div class="pricing-item">
                    <span class="label">递增周期：</span>
                    <span class="value">{{ pricingDetailForm.increasePeriod }}年</span>
                  </div>
                  <div class="pricing-item">
                    <span class="label">单价递增率：</span>
                    <span class="value">{{ pricingDetailForm.increaseRate }}%</span>
                  </div>
                </template>
              </div>
              <div class="pricing-row">
                <div class="pricing-item">
                  <span class="label">保证金：</span>
                  <span class="value">{{ pricingDetailForm.depositAmount }}元</span>
                </div>
                <div class="pricing-item">
                  <span class="label">支付方式：</span>
                  <span class="value">{{ pricingDetailForm.paymentMethod === 'monthly' ? '月付' : '' }}</span>
                </div>
                <div class="pricing-item">
                  <span class="label">租赁期限：</span>
                  <span class="value">{{ pricingDetailForm.minLeaseTerm }}-{{ pricingDetailForm.maxLeaseTerm }}{{ pricingDetailForm.leaseUnit === 'month' ? '月' : '' }}</span>
                </div>
              </div>
            </div>
          </div>

          <section-title title="相关定价申请单" style="margin-top: 16px;"/>
          <a-table
            :columns="pricingColumns"
            :data="pricingHistory"
            :pagination="false"
            :bordered="{ cell: true }"
            :scroll="{x: 1}"
          >
            <template #operations="{ record, index }">
              <a-link @click="handlePricingViewDetail(record)">详情</a-link>
            </template>
          </a-table>
        </div>
        <div v-show="currentTab === 'history'" v-if="isReadOnly">
          <section-title title="变更历史" class="first-child" />
          <a-table
            :columns="historyColumns"
            :data="changeHistory"
            :pagination="false"
            :bordered="{ cell: true }"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="footer-btns">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <template v-if="currentTab === 'base'">
            <a-button type="primary" @click="handleNext">保存并完善其他信息</a-button>
          </template>
          <template v-if="currentTab === 'other'">
            <a-button type="primary" @click="handleSave">保存</a-button>
          </template>
        </a-space>
      </div>
    </template>
    <room-type-edit-dialog
      v-model:visible="roomTypeDetailVisible"
      title="户型详情"
      :data="{ id: baseForm.layout }"
      :readonly="true"
    />
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import uploadFile from '@/components/upload/uploadFile.vue'
import { useDictSync } from '@/utils/dict'
import RoomTypeEditDialog from '@/views/property/projects/components/RoomTypeEditDialog.vue'
import { 
  addRoom, 
  editRoom, 
  getRoomDetail, 
  getRoomChangeHistory,
  type RoomAddDTO, 
  type RoomVo, 
  type RoomChangeRecordVo,
  AreaType,
  RentAreaType,
  PaymentStatus,
  OperationSubject,
  SelfUseSubject,
  RoomType
} from '@/api/room'
import { 
  getParcelList,
  getBuildingSelectList,
  getFloorList,
  getHouseTypeList,
  getHouseTypeDetail,
  type SysParcel,
  type SysBuilding,
  type SysFloor,
  type ProjectHouseTypeVo
} from '@/api/project'

const visible = ref(false)
const isReadOnly = ref(false)
const isEdit = ref(false)
const loading = ref(false)
const roomId = ref<string | null>(null)

const drawerTitle = computed(() => {
  if (isReadOnly.value) return '房源详情'
  if (isEdit.value) return '编辑房源'
  return '新增房源'
})

const currentTab = ref<'base' | 'other' | 'pricing' | 'history'>('base')

const baseFormRef = ref()
const otherFormRef = ref()

const baseForm = reactive({
  name: '',
  propertyType: '',
  project: '',
  block: '',
  building: '',
  floor: '',
  code: '',
  areaType: '',
  buildingArea: '',
  rentAreaType: '',
  rentArea: '',
  innerArea: '',
  height: '',
  layout: '',
  load: '',
  direction: '',
  meter: '',
  lock: ''
})

const otherForm = reactive({
  operationMode: '',
  operationType: '',
  propertyStatus: '',
  deliveryStatus: '',
  isGuarantee: false,
  firstRentCost: '',
  firstRentDate: [] as string[],
  latestRentCost: '',
  latestRentDate: [] as string[],
  mainBody: 1,
  recruitDate: '',
  isSelfUse: '',
  selfUsePurpose: '',
  selfUseMainBody: '',
  leaseStartDate: ''
})

// 定价历史表格列定义
const pricingColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '立项定价申请名称', dataIndex: 'name', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '定价楼栋', dataIndex: 'building', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '定价房源数', dataIndex: 'amount', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '审批通过时间', dataIndex: 'approvalTime', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '操作', slotName: 'operations',  width: 100, ellipsis: false, tooltip: false, align: 'center',fixed: 'right'  }
]

// 变更历史表格列定义
const historyColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '变更类型', dataIndex: 'type', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '变更时间', dataIndex: 'date', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '变更内容', dataIndex: 'remark', width: 200, ellipsis: true, tooltip: true, align: 'center' }
]

// 定价历史数据
const pricingHistory = ref([
  {
    index: 1,
    name: '定价申请单1',
    building: '楼栋1',
    amount: '2000元/月',
    approvalTime: '2024-04-01'
  }
])

// 变更历史数据
const changeHistory = ref<any[]>([])
const changeHistoryData = ref<RoomChangeRecordVo[]>([])

const handlePricingViewDetail = (record: any) => {
  console.log('record', record) 
  Message.info('定价申请单详情')
}

const handleMenuClick = async (key: string) => {
  // 如果是从base切换到其他标签，需要先校验基础表单
  if (currentTab.value === 'base' && key !== 'base' && !isReadOnly.value) {
    try {
      // 校验基础表单
      const errors = await baseFormRef.value?.validate()
      if (errors) {
        // 如果有错误，不允许切换标签
        Message.warning('请先完善房源信息的必填字段')
        return
      }
    } catch (error) {
      // 如果校验失败，不允许切换标签
      Message.warning('请先完善房源信息的必填字段')
      return
    }
  }
  
  // 校验通过或者是查看模式，允许切换标签
  currentTab.value = key as 'base' | 'other' | 'pricing' | 'history'
}

const handleCancel = () => {
  setTimeout(() => {
    emit('cancel')
  }, 300);
  visible.value = false
  isReadOnly.value = false
}

// 将表单数据转换为API数据格式
const mapFormToApiData = (): RoomAddDTO => {
  // 获取地块名称、楼栋名称和楼层名称
  const parcelName = parcelOptions.value.find(item => item.id === baseForm.block)?.parcelName || '';
  const buildingName = getBuildingName(baseForm.building) || '';
  const floorName = getFloorName(baseForm.floor) || '';
  
  return {
    id: roomId.value || undefined,
    // 默认设置房源类型为普通(1)
    type: RoomType.NORMAL,
    roomName: baseForm.name,
    propertyType: baseForm.propertyType,
    projectId: baseForm.project,
    parcelId: baseForm.block,
    parcelName: parcelName,
    buildingId: baseForm.building,
    buildingName: buildingName,
    floorId: baseForm.floor,
    floorName: floorName,
    roomCode: baseForm.code,
    areaType: baseForm.areaType === '实测' ? AreaType.MEASURED : AreaType.ESTIMATED,
    buildArea: parseFloat(baseForm.buildingArea) || 0,
    innerArea: parseFloat(baseForm.innerArea) || 0,
    rentAreaType: baseForm.rentAreaType === '建筑面积' ? RentAreaType.BUILD_AREA : RentAreaType.INNER_AREA,
    rentArea: parseFloat(baseForm.rentArea) || 0,
    storey: parseFloat(baseForm.height) || 0,
    value: parseFloat(baseForm.load) || 0,
    orientation: baseForm.direction,
    houseTypeId: baseForm.layout,
    smartWaterMeter: baseForm.meter,
    smartLock: baseForm.lock,
    
    // 其他表单字段
    assetOperationMode: otherForm.operationMode,
    assetOperationType: otherForm.operationType,
    propertyStatus: otherForm.propertyStatus,
    paymentStatus: otherForm.deliveryStatus === '已交付' ? PaymentStatus.DELIVERED : PaymentStatus.NOT_DELIVERED,
    specialTag: otherForm.isGuarantee ? '1' : '',
    firstRentPrice: parseFloat(otherForm.firstRentCost) || 0,
    firstRentStartDate: otherForm.firstRentDate?.[0] || undefined,
    firstRentEndDate: otherForm.firstRentDate?.[1] || undefined,
    latestRentPrice: parseFloat(otherForm.latestRentCost) || 0,
    latestRentStartDate: otherForm.latestRentDate?.[0] || undefined,
    latestRentEndDate: otherForm.latestRentDate?.[1] || undefined,
    operationSubject: Number(otherForm.mainBody) || OperationSubject.BUSINESS, // 默认商服
    rentalStartDate: otherForm.recruitDate || undefined,
    externalRentStartDate: otherForm.leaseStartDate || undefined,
    isSelfUse: otherForm.isSelfUse === '是',
    selfUsePurpose: otherForm.selfUsePurpose,
    selfUseSubject: otherForm.selfUseMainBody ? Number(otherForm.selfUseMainBody) : undefined
  }
}

// 将API数据转换为表单格式
const mapApiDataToForm = (data: RoomVo) => {
  roomId.value = data.id || null
  console.log('开始映射API数据到表单格式:', JSON.stringify(data))

  // 基础表单数据
  baseForm.name = data.roomName || ''
  baseForm.propertyType = data.propertyType || ''
  
  // 项目、地块、楼栋、楼层在loadRoomDetail中已经设置，这里不再重复设置
  // 避免清除已加载好的级联关系
  if (!baseForm.project) {
    baseForm.project = data.projectId || ''
  }
  
  if (!baseForm.block) {
    baseForm.block = data.parcelId || ''
  }
  
  if (!baseForm.building) {
    baseForm.building = data.buildingId ? String(data.buildingId) : ''
  }
  
  if (!baseForm.floor) {
    baseForm.floor = data.floorId ? String(data.floorId) : ''
  }
  
  baseForm.code = data.roomCode || ''
  baseForm.areaType = data.areaType === AreaType.MEASURED ? '实测' : '预测'
  baseForm.buildingArea = data.buildArea?.toString() || ''
  baseForm.rentAreaType = data.rentAreaType === RentAreaType.BUILD_AREA ? '建筑面积' : '套内面积'
  baseForm.rentArea = data.rentArea?.toString() || ''
  baseForm.innerArea = data.innerArea?.toString() || ''
  baseForm.height = data.storey?.toString() || ''
  baseForm.load = data.value?.toString() || ''
  baseForm.direction = data.orientation || ''
  baseForm.layout = data.houseTypeId || ''
  baseForm.meter = data.smartWaterMeter || ''
  baseForm.lock = data.smartLock || ''
  
  // 其他表单数据
  otherForm.operationMode = data.assetOperationMode || ''
  otherForm.operationType = data.assetOperationType || ''
  otherForm.propertyStatus = data.propertyStatus || '无证' // 默认无证
  otherForm.deliveryStatus = data.paymentStatus === PaymentStatus.DELIVERED ? '已交付' : '未交付'
  otherForm.isGuarantee = data.specialTag === '1'
  otherForm.firstRentCost = data.firstRentPrice?.toString() || ''
  otherForm.firstRentDate = data.firstRentStartDate && data.firstRentEndDate ? 
                          [data.firstRentStartDate, data.firstRentEndDate] : []
  otherForm.latestRentCost = data.latestRentPrice?.toString() || ''
  otherForm.latestRentDate = data.latestRentStartDate && data.latestRentEndDate ? 
                           [data.latestRentStartDate, data.latestRentEndDate] : []
  
  otherForm.mainBody = data.operationSubject || 1 // 默认商服，确保为数字类型
  
  otherForm.recruitDate = data.rentalStartDate || ''
  otherForm.leaseStartDate = data.externalRentStartDate || ''
  otherForm.isSelfUse = data.isSelfUse ? '是' : '否'
  otherForm.selfUsePurpose = data.selfUsePurpose || ''
  otherForm.selfUseMainBody = data.selfUseSubject ? data.selfUseSubject.toString() : ''
}

// 加载房源详情
const loadRoomDetail = async (id: string) => {
  try {
    loading.value = true
    
    // 声明变量存储房源数据
    let responseData: any = null;
    
    // 根据是否有ID决定从API获取数据还是使用调整模式的数据
    if (id) {
      // 从API获取房源详情
      const response = await getRoomDetail({ id })
      if (response && response.data) {
        responseData = response.data;
      }
    } else if (isAdjustMode.value && adjustProperty.value) {
      // 使用调整模式传入的数据
      responseData = adjustProperty.value;
    }
    
    // 处理获取到的数据
    if (responseData) {
      console.log('获取到房源详情数据:', JSON.stringify(responseData))
      
      // 将数据存储为临时变量，等所有下拉列表加载完成后再设置表单数据
      const detailData = responseData
      
      // 先保存原始楼栋和楼层ID，以便后续使用
      const originalBuildingId = detailData.buildingId ? String(detailData.buildingId) : ''
      const originalFloorId = detailData.floorId ? String(detailData.floorId) : ''
      
      console.log('原始楼栋ID:', originalBuildingId, '原始楼层ID:', originalFloorId)
      
      // 先加载项目相关数据，确保下拉列表有数据可供匹配
      if (detailData.projectId) {
        console.log('开始加载项目相关数据，项目ID:', detailData.projectId)
        
        // 设置项目ID
        baseForm.project = detailData.projectId
        
        // 加载地块列表
        await loadParcelList(detailData.projectId)
        // 加载户型列表
        await loadHouseTypeList(detailData.projectId)
        
        // 如果有地块ID，设置地块并加载楼栋数据
        if (detailData.parcelId) {
          console.log('设置地块ID:', detailData.parcelId)
          baseForm.block = detailData.parcelId
          
          // 等待楼栋数据加载完成
          await loadBuildingList(detailData.parcelId)
          
          // 检查楼栋数据是否加载成功
          console.log('楼栋数据加载完成，数量:', buildingOptions.value.length)
          
          // 如果有楼栋ID，设置楼栋并加载楼层数据
          if (originalBuildingId) {
            console.log('设置楼栋ID:', originalBuildingId)
            // 设置楼栋ID
            baseForm.building = originalBuildingId
            
            // 验证楼栋是否在列表中
            const buildingExists = buildingOptions.value.some(b => String(b.id) === originalBuildingId)
            console.log('楼栋是否存在于列表中:', buildingExists)
            
            // 等待楼层数据加载完成
            await loadFloorList(originalBuildingId)
            
            // 检查楼层数据是否加载成功
            console.log('楼层数据加载完成，数量:', floorOptions.value.length, '列表:', floorOptions.value.map(f => ({id: f.id, name: f.floorName})))
            
            // 最后设置楼层ID
            if (originalFloorId) {
              console.log('设置楼层ID:', originalFloorId)
              baseForm.floor = originalFloorId
              
              // 验证楼层是否在列表中
              const floorExists = floorOptions.value.some(f => String(f.id) === originalFloorId)
              console.log('楼层是否存在于列表中:', floorExists)
            }
          }
        }
      }
      
      // 设置其他表单字段
      mapApiDataToForm(detailData)
      
      // 生成合成编码
      generateRoomCode()
      
      // 打印最终表单状态
      console.log('最终表单状态 - 楼栋ID:', baseForm.building, '楼栋名称:', getBuildingName(baseForm.building))
      console.log('最终表单状态 - 楼层ID:', baseForm.floor, '楼层名称:', getFloorName(baseForm.floor))
    } else {
      // 只有在ID存在但获取不到数据时才提示失败
      if (id) {
        console.log('获取房源详情失败')
      }
    }
  } catch (error) {
    console.error('获取房源详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载房源变更历史
const loadRoomChangeHistory = async (id: string) => {
  try {
    loading.value = true
    const response = await getRoomChangeHistory({ roomId: id })
    if (response && response.data) {
      changeHistoryData.value = response.data
      
      // 转换为表格显示格式
      changeHistory.value = changeHistoryData.value.map((item, index) => {
        let remarkText = item.changeContent || ''
        
        // 尝试解析JSON字符串，提取changeContent值
        try {
          if (remarkText && typeof remarkText === 'string' && remarkText.startsWith('{')) {
            const parsedData = JSON.parse(remarkText)
            if (parsedData.changeContent) {
              remarkText = parsedData.changeContent
            }
          }
        } catch (error) {
          // 如果解析失败，保持原始值
          console.warn('解析变更内容JSON失败:', error)
        }
        
        return {
        index: index + 1,
        date: item.changeTime || '',
        type: item.changeType === 1 ? '生效' : 
              item.changeType === 2 ? '信息变更' : 
              item.changeType === 3 ? '拆分' : 
              item.changeType === 4 ? '合并' : 
              item.changeType === 5 ? '作废' : '其他',
          remark: remarkText
        }
      })
    } else {
      changeHistory.value = []
    }
  } catch (error) {
    console.error('获取房源变更历史失败:', error)
    changeHistory.value = []
  } finally {
    loading.value = false
  }
}

// 重置表单数据
const resetForm = () => {
  roomId.value = null
  
  // 重置基础表单
  Object.keys(baseForm).forEach(key => {
    if (typeof baseForm[key as keyof typeof baseForm] === 'string') {
      (baseForm as any)[key] = ''
    } else if (typeof baseForm[key as keyof typeof baseForm] === 'boolean') {
      (baseForm as any)[key] = false
    }
  })
  
  // 重置其他表单
  Object.keys(otherForm).forEach(key => {
    if (key === 'firstRentDate' || key === 'latestRentDate') {
      (otherForm as any)[key] = []
    } else if (key === 'mainBody') {
      // 运营主体特殊处理，保持为数字类型
      otherForm.mainBody = 1
    } else if (typeof otherForm[key as keyof typeof otherForm] === 'string') {
      (otherForm as any)[key] = ''
    } else if (typeof otherForm[key as keyof typeof otherForm] === 'boolean') {
      (otherForm as any)[key] = false
    }
  })
  
  // 设置默认值
  otherForm.isSelfUse = '否'
  otherForm.deliveryStatus = '未交付'
  otherForm.propertyStatus = '无证'
  otherForm.operationMode = '3'//代招商
}

const handleNext = async () => {
  try {
    // 校验baseForm
    const errors = await baseFormRef.value?.validate()
    if (errors) {
      // 如果有错误，显示提示
      Message.warning('请先完善房源信息的必填字段')
      return
    }
    
    // 校验通过，切换到其他信息标签页
    currentTab.value = 'other'
  } catch (error) {
    // 如果校验失败，显示提示
    Message.warning('请先完善房源信息的必填字段')
  }
}

const emit = defineEmits(['confirm','cancel'])

const handleSave = async () => {
  try {
    // 校验表单
    const baseFormValid = await baseFormRef.value?.validate().catch(() => true)
    const otherFormValid = await otherFormRef.value?.validate().catch(() => true)
    
    if (baseFormValid || otherFormValid) {
      return
    }
    
    loading.value = true
    
    // 生成表单数据
    const formData = mapFormToApiData()
    console.log('提交的表单数据:', formData)
    
    // 如果是调整编辑模式，直接将编辑后的房源数据返回给调整组件
    if (isAdjustMode.value) {
      console.log('调整编辑模式，返回数据:', formData)
      console.log('当前调整索引值:', adjustIndex.value, '类型:', typeof adjustIndex.value)
      
      // 通过emit将数据返回给调整抽屉
      setTimeout(() => {
        emit('confirm', { 
          type: 'adjust-edit', 
          data: formData, 
          index: adjustIndex.value 
        })
      }, 300);
      
      Message.success('编辑房源成功')
      visible.value = false
      return
    }
    
    // 正常编辑或新增房源逻辑
    let response
    if (isEdit.value) {
      // 编辑房源
      response = await editRoom(formData)
    } else {
      // 新增房源
      response = await addRoom(formData)
    }
    
    if (response && response.code === 200) {
      Message.success(isEdit.value ? '编辑房源成功' : '新增房源成功')
      setTimeout(() => {
        // 触发confirm事件通知父组件刷新列表
        emit('confirm', { type: isEdit.value ? 'edit' : 'add', data: formData })
      }, 300);
      visible.value = false
    }
  } catch (error) {
    console.error(isEdit.value ? '编辑房源失败:' : '新增房源失败:', error)
  } finally {
    loading.value = false
  }
}

// 添加当前选择的项目信息
const currentSelection = ref({
  projectId: '',
  projectName: '',
  blockId: '',
  blockName: '',
  buildingId: '',
  buildingName: ''
})
// 添加保存调整房源的索引
const adjustIndex = ref(-1)
const adjustProperty = ref({})
const isAdjustMode = ref(false)
// 展示抽屉
const show = async (mode: 'add' | 'edit' | 'view' | 'adjust-edit' | 'adjust-view' = 'add', id?: string | null, selection?: any, adjustPropertyParam?: any, adjustIndexParam?: number) => {
  resetForm()
  visible.value = true
  currentTab.value = 'base'
  isReadOnly.value = mode === 'view' || mode === 'adjust-view'
  isEdit.value = mode === 'edit' || mode === 'adjust-edit'
  
  // 设置当前选择的项目信息
  if (selection) {
    console.log("接收到的selection数据:", JSON.stringify(selection))
    currentSelection.value = selection
  }
  if(mode === 'adjust-view'||mode === 'adjust-edit'){
    isAdjustMode.value = true
    // 确保adjustIndexParam值为数字，包括0
    adjustIndex.value = adjustIndexParam !== undefined && adjustIndexParam !== null ? adjustIndexParam : -1
    adjustProperty.value = adjustPropertyParam || {}
  }
  // 如果是编辑或查看模式，先加载详情
  if (id || isAdjustMode.value) {
    roomId.value = id || ''
    await loadRoomDetail(id || '')
    
    if (mode === 'view') {
      // 查看模式下加载变更历史
      await loadRoomChangeHistory(id || '')
    }
  } else if (mode === 'add') {
    // 新增模式下，按照selection设置项目相关信息并加载级联数据
    if (selection && selection.projectId) {
      try {
        // 设置项目ID
        baseForm.project = selection.projectId
        
        // 加载地块列表
        await loadParcelList(selection.projectId)
        
        // 加载户型列表
        await loadHouseTypeList(selection.projectId)
        
        // 如果有地块ID，设置地块并加载楼栋列表
        if (selection.blockId) {
          console.log("设置地块ID:", selection.blockId)
          baseForm.block = selection.blockId
          
          // 加载楼栋列表
          await loadBuildingList(selection.blockId)
          
          // 如果有楼栋ID，设置楼栋并加载楼层列表
          if (selection.buildingId) {
            console.log("设置楼栋ID:", selection.buildingId)
            baseForm.building = String(selection.buildingId)
            
            // 加载楼层列表
            await loadFloorList(selection.buildingId)
          }
        }
      } catch (error) {
        console.error("加载下拉数据失败:", error)
      }
    }
    
    // 设置默认值
    // 默认设置计租面积类型为建筑面积
    baseForm.rentAreaType = '建筑面积'
    
    // 默认设置为实测
    baseForm.areaType = '实测'
    
    // 默认设置是否自用为否
    otherForm.isSelfUse = '否'
    
    // 默认设置交付状态为未交付
    otherForm.deliveryStatus = '未交付'
    
    // 默认设置产权情况为无证
    otherForm.propertyStatus = '无证'
    
    // 默认设置运营主体为商服(1)
    otherForm.mainBody = 1
    
    // 尝试生成合成编码
    generateRoomCode()
  }
}

// 监听面积类型和建筑面积变化，更新计租面积
watch([() => baseForm.areaType, () => baseForm.buildingArea, () => baseForm.rentAreaType], () => {
  // 当计租面积类型为建筑面积时，计租面积等于建筑面积
  if (baseForm.rentAreaType === '建筑面积' && baseForm.buildingArea) {
    baseForm.rentArea = baseForm.buildingArea
  }
})

// 定价基础信息
const pricingForm = reactive({
  planType: '餐饮',
  baseRent: 900,
  additionalFee: 50,
  listPrice: 950,
  basePrice: 800,
  rentUnit: '元/月'
})

// 展开状态
const showPricingDetail = ref(false)

// 租赁期限表格列定义
const rentPeriodColumns = [
  { title: '租赁期限', dataIndex: 'period', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '免租期', dataIndex: 'freePeriod', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '底价(含免租期)', dataIndex: 'price', width: 200, ellipsis: true, tooltip: true, align: 'center' }
]

// 租赁期限数据
const rentPeriodData = ref([
  { period: '1-11月', freePeriod: '0月', price: '860元' },
  { period: '12-23月', freePeriod: '3月', price: '645元' },
  { period: '24月及以上', freePeriod: '7月', price: '619.72元' }
])

// 定价详细信息
const pricingDetailForm = reactive({
  factor: 'unlimited',
  hasIncrease: 'yes',
  increasePeriod: 2,
  increaseRate: 5,
  deposit: 'fixed',
  depositAmount: 100,
  paymentMethod: 'monthly',
  minLeaseTerm: 12,
  maxLeaseTerm: 24,
  leaseUnit: 'month'
})

// 户型信息相关
const roomTypeDetailVisible = ref(false)
const currentRoomType = reactive({
  id: '',
  projectId: '',
  houseTypeName: '标准单人间',
  roomNum: 1,
  hallNum: 0,
  toiletNum: 1,
  decorationType: 1, // 1-精装, 2-简装, 3-毛坯
  livableNum: 1,
  specialFeatures: '独卫,带阳台,可做饭',
  houseTypeDesc: '这是一个标准单人间的描述信息'
})

const handleViewRoomType = () => {
  // 检查是否选择了户型
  if (!baseForm.layout) {
    Message.warning('请先选择户型')
    return
  }
  
  // 设置当前户型ID
  currentRoomType.id = baseForm.layout
  currentRoomType.projectId = baseForm.project
  
  // 打开户型详情抽屉
  roomTypeDetailVisible.value = true
}

// 字典数据
const propertyTypeOptions = ref<any[]>([])
const roomOrientationOptions = ref<any[]>([])
const assetOperationModeOptions = ref<any[]>([])
const assetOperationTypeOptions = ref<any[]>([])
const selfUseSubjectOptions = ref<any[]>([])

// 地块、楼栋、楼层数据
const parcelOptions = ref<SysParcel[]>([])
const buildingOptions = ref<SysBuilding[]>([])
const floorOptions = ref<SysFloor[]>([])
const houseTypeOptions = ref<ProjectHouseTypeVo[]>([])

// 加载字典数据
const loadDictionaries = async () => {
  try {
    const dictData = await useDictSync('property_type','room_orientation','asset_operation_mode','asset_operation_type','self_use_subject');
    console.log('物业类型字典数据:', dictData)
    // 获取物业类型字典
    if (dictData.property_type) {
      const dictList = dictData.property_type
      // 组织成树形结构
      const treeData = dictList.filter((item: any) => !item.parentCode)
      treeData.forEach((parent: any) => {
        parent.childList = dictList.filter((child: any) => child.parentCode === parent.dictCode)
      })
      propertyTypeOptions.value = treeData
      console.log('物业类型字典数据:', propertyTypeOptions.value)
    }

    // 获取朝向字典
    if (dictData.room_orientation) {
      roomOrientationOptions.value = dictData.room_orientation
    }

    // 获取资产运营模式字典
    if (dictData.asset_operation_mode) {
      assetOperationModeOptions.value = dictData.asset_operation_mode
    }

    // 获取资产运营分类字典
    if (dictData.asset_operation_type) {
      assetOperationTypeOptions.value = dictData.asset_operation_type
    }

    // 获取自用主体字典
    if (dictData.self_use_subject) {
      selfUseSubjectOptions.value = dictData.self_use_subject
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 加载地块数据
const loadParcelList = async (projectId: string) => {
  if (!projectId) return
  try {
    console.log("开始加载地块列表, 项目ID:", projectId)
    const res = await getParcelList(projectId)
    if (res && res.data) {
      parcelOptions.value = res.data
      console.log("地块列表加载成功, 数量:", res.data.length)
      return res.data
    }
  } catch (error) {
    console.error('获取地块列表失败:', error)
  }
  return []
}

// 加载楼栋数据
const loadBuildingList = async (parcelId: string) => {
  if (!parcelId) return
  try {
    console.log("开始加载楼栋列表, 地块ID:", parcelId, "当前选中楼栋ID:", baseForm.building)
    const res = await getBuildingSelectList(parcelId)
    if (res && res.data) {
      // 确保每个楼栋对象都有正确的属性
      buildingOptions.value = res.data.map(item => ({
        ...item,
        id: item.id ? String(item.id) : '', // 确保ID是字符串类型
        buildingName: item.buildingName || ''
      }))
      console.log("楼栋列表加载成功, 数量:", buildingOptions.value.length)
      console.log("楼栋列表数据:", JSON.stringify(buildingOptions.value.map(b => ({ id: b.id, name: b.buildingName }))))
      
      // 检查当前选中的楼栋ID是否在列表中
      if (baseForm.building) {
        const found = buildingOptions.value.find(item => String(item.id) === String(baseForm.building))
        if (!found) {
          console.warn("当前选中的楼栋ID不在列表中:", baseForm.building)
        } else {
          console.log("成功匹配到楼栋:", found.buildingName)
        }
      }
      
      return buildingOptions.value
    } else {
      console.warn("加载楼栋列表返回无数据")
      return []
    }
  } catch (error) {
    console.error('获取楼栋列表失败:', error)
    return []
  }
}

// 加载楼层数据
const loadFloorList = async (buildingId: string) => {
  if (!buildingId) {
    console.warn("无法加载楼层列表：楼栋ID为空")
    floorOptions.value = []
    return []
  }
  
  try {
    console.log("开始加载楼层列表, 楼栋ID:", buildingId)
    const res = await getFloorList(buildingId)
    
    // 直接输出API返回的数据结构
    console.log("楼层列表API响应:", JSON.stringify(res))
    
    if (res && res.data && Array.isArray(res.data)) {
      // 处理楼层数据，确保ID和名称字段有效
      floorOptions.value = res.data.map(item => ({
        ...item,
        id: String(item.id || ''),
        floorName: item.floorName || '未命名楼层'
      }))
      
      console.log("楼层列表处理完成, 数量:", floorOptions.value.length)
      console.log("楼层列表数据:", floorOptions.value.map(f => ({ id: f.id, name: f.floorName })))
      
      return floorOptions.value
    } else {
      console.warn("楼层列表API响应格式错误:", res)
      floorOptions.value = []
      return []
    }
  } catch (error) {
    console.error('获取楼层列表失败:', error)
    floorOptions.value = []
    return []
  }
}

// 加载户型数据
const loadHouseTypeList = async (projectId: string) => {
  if (!projectId) return
  try {
    const res = await getHouseTypeList(projectId)
    if (res && res.data) {
      houseTypeOptions.value = res.data
    }
  } catch (error) {
    console.error('获取户型列表失败:', error)
  }
}

// 监听项目变化，加载地块列表
watch(() => baseForm.project, (newVal) => {
  if (newVal) {
    loadParcelList(newVal)
    loadHouseTypeList(newVal)
    // 重置相关字段
    baseForm.block = ''
    baseForm.building = ''
    baseForm.floor = ''
    buildingOptions.value = []
    floorOptions.value = []
  }
})

// 监听地块变化，加载楼栋列表
watch(() => baseForm.block, (newVal) => {
  if (newVal) {
    // 清空楼栋和楼层
    baseForm.building = ''
    baseForm.floor = ''
    buildingOptions.value = []
    floorOptions.value = []
    
    // 加载楼栋列表
    loadBuildingList(newVal)
  }
})

// 监听楼栋变化，加载楼层列表
watch(() => baseForm.building, (newVal) => {
  if (newVal) {
    // 清空楼层
    baseForm.floor = ''
    floorOptions.value = []
    
    // 加载楼层列表
    loadFloorList(newVal).then(() => {
      // 生成合成编码
      generateRoomCode()
    })
  }
})

// 监听房源名称变化，更新合成编码
watch(() => baseForm.name, (newVal) => {
  if (newVal) {
    generateRoomCode()
  }
})

const handleBlur = (fieldName: string) => {
  const value = baseForm[fieldName as keyof typeof baseForm]
  // 判断是否是数字
  const isNumber = !isNaN(parseFloat(value)) && isFinite(parseFloat(value))
  if (!isNumber) {
    baseForm[fieldName as keyof typeof baseForm] = ''
    return;
  }
  // 判断小数位数是否超过两位
  if (value.includes('.') && value.split('.')[1].length > 2) {
    // 保留两位小数
    baseForm[fieldName as keyof typeof baseForm] = parseFloat(value).toFixed(2)
  }
}

// 生成合成编码
const generateRoomCode = () => {
  // 项目简称+楼栋名称+房间名称
  if (baseForm.project && baseForm.building && baseForm.name) {
    // 使用当前选择的项目名称、楼栋名称和房源名称进行拼接
    const projectName = currentSelection.value.projectName || ''
    
    // 通过ID获取楼栋名称
    const buildingName = getBuildingName(baseForm.building)
    console.log('生成合成编码, 项目名称:', projectName, '楼栋名称:', buildingName, '房源名称:', baseForm.name)
    
    // 只有在所有条件都满足时才生成合成编码
    if(isReadOnly.value){
      return
    }
    if (projectName && buildingName && baseForm.name) {
      baseForm.code = `${projectName}-${buildingName}-${baseForm.name}`
      console.log('生成的合成编码:', baseForm.code)
    } else {
      baseForm.code = ''
      console.log('未能生成合成编码，缺少必要信息。项目名称:', projectName, '楼栋名称:', buildingName, '房源名称:', baseForm.name)
    }
  } else {
    // 如果缺少任何一个必要参数，则清空编码
    baseForm.code = ''
    console.log('未能生成合成编码，缺少必要字段。项目:', baseForm.project, '楼栋:', baseForm.building, '房源名称:', baseForm.name)
  }
}

// 根据ID获取楼栋名称
const getBuildingName = (buildingId: string) => {
  if (!buildingId) return ''
  
  console.log('尝试获取楼栋名称，楼栋ID:', buildingId, '类型:', typeof buildingId)
  console.log('当前楼栋列表:', buildingOptions.value.map(b => ({ id: b.id, type: typeof b.id, name: b.buildingName })))
  
  // 确保进行类型安全的比较
  const building = buildingOptions.value.find(item => {
    // 强制将两边都转为字符串后比较
    return String(item.id) === String(buildingId)
  })
  
  // 返回楼栋的中文名称，如果找不到则返回空字符串
  if (building) {
    console.log('找到匹配的楼栋:', building.buildingName)
    return building.buildingName || ''
  } else {
    console.warn('未找到匹配的楼栋, ID:', buildingId)
    return ''
  }
}

// 根据ID获取楼层名称
const getFloorName = (floorId: string | undefined) => {
  if (!floorId) return ''
  
  // 直接输出楼层ID和类型，便于调试
  console.log('获取楼层名称, ID:', floorId, '类型:', typeof floorId)
  
  // 如果楼层选项为空，先返回空字符串
  if (floorOptions.value.length === 0) {
    console.warn('楼层列表为空，无法获取楼层名称')
    return ''
  }
  
  // 打印所有楼层选项，便于检查
  console.log('当前所有楼层:', floorOptions.value.map(f => ({id: f.id, type: typeof f.id, name: f.floorName})))
  
  // 查找匹配的楼层
  const floor = floorOptions.value.find(item => String(item.id) === String(floorId))
  
  if (floor && floor.floorName) {
    console.log('找到楼层:', floor.floorName)
    return floor.floorName
  } else {
    console.warn('未找到匹配的楼层名称:', floorId)
    return ''
  }
}

// 选择物业类型时，检查是否为宿舍类型，并在控制台输出
watch(() => baseForm.propertyType, (newVal) => {
  console.log('物业类型变更为:', newVal, '类型:', typeof newVal)
  
  // 输出物业类型字典信息，用于调试
  console.log('物业类型字典:', propertyTypeOptions.value)
  
  // 检查是否为宿舍类型
  const isDormitory = checkIsDormitory(newVal)
  console.log('是否为宿舍类型:', isDormitory)
  
  // 如果是宿舍类型，加载户型列表
  if (isDormitory && baseForm.project) {
    loadHouseTypeList(baseForm.project)
  }
})

// 检查物业类型是否为宿舍
const checkIsDormitory = (propertyType: string): boolean => {
  if (!propertyType) return false
  
  // 检查物业类型值是否包含"宿舍"关键字
  if (propertyType.includes('宿舍')) return true
  
  // 从字典中查找对应的标签
  const findPropertyTypeLabel = (options: any[], value: string): string => {
    for (const option of options) {
      if (option.dictValue === propertyType) {
        return option.dictLabel || ''
      }
      if (option.childList && option.childList.length > 0) {
        const childLabel = findPropertyTypeLabel(option.childList, value)
        if (childLabel) return childLabel
      }
    }
    return ''
  }
  
  const typeLabel = findPropertyTypeLabel(propertyTypeOptions.value, propertyType)
  console.log('物业类型标签:', typeLabel)
  
  // 检查标签是否包含"宿舍"关键字
  return typeLabel.includes('宿舍')
}

// 组件挂载时初始化数据
onMounted(() => {
  loadDictionaries()
})

defineExpose({ show })
</script>

<style scoped lang="less">
.add-property-container {
  display: flex;
  height: 100%;

  .content-section {
    flex: 1;
    padding: 0 20px;
    overflow-y: auto;
  }
  .scrollable-content {
    overflow-y: auto;
  }
  .section-title {
    margin: 16px 0;
    &.first-child{
      margin-top: 8px;
    }
  }
  .footer-btns {
    display: flex;
    justify-content: flex-end;
    padding: 16px 24px 16px 0;
    background: #fff;
    border-top: 1px solid #f0f0f0;
  }
  .input-link-row {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    .arco-input {
      flex: 1 1 auto;
      min-width: 0;
    }
    .bind-link {
      white-space: nowrap;
      flex-shrink: 0;
    }
  }
  .pricing-info {
    .pricing-row {
      display: flex;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }
    .pricing-item {
      flex: 0 0 33%;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .label {
        color: var(--color-text-3);
        margin-right: 8px;
        white-space: nowrap;
      }
      .value {
        color: var(--color-text-1);
        margin-right: 4px;
      }
      .price-note {
        color: var(--color-text-3);
        font-size: 12px;
      }
      .expand-link {
        margin-left: 16px;
      }
    }
  }
}
</style> 