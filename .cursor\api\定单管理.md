---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁后台/定单管理

<a id="opIdsaveBooking"></a>

## POST 保存定单

POST /booking/save

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "isDel": 0,
  "isSubmit": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[BookingAddDTO](#schemabookingadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_2"></a>

## POST 定单列表查询接口

POST /booking/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "searchType": 0,
  "projectId": "string",
  "customerName": "string",
  "roomName": "string",
  "status": 0,
  "createTimeStart": "2019-08-24T14:15:22Z",
  "createTimeEnd": "2019-08-24T14:15:22Z",
  "createByName": "string",
  "actualReceiveTimeStart": "2019-08-24T14:15:22Z",
  "actualReceiveTimeEnd": "2019-08-24T14:15:22Z",
  "contractNo": "string",
  "signDateStart": "2019-08-24T14:15:22Z",
  "signDateEnd": "2019-08-24T14:15:22Z",
  "contractLeaseUnit": "string",
  "lesseeName": "string",
  "cancelReason": 0,
  "cancelByName": "string",
  "cancelTimeStart": "2019-08-24T14:15:22Z",
  "cancelTimeEnd": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[BookingQueryDTO](#schemabookingquerydto)| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","projectId":"string","customerName":"string","propertyType":"string","roomId":"string","roomName":"string","bookingAmount":0,"receivableDate":"2019-08-24T14:15:22Z","expectSignDate":"2019-08-24T14:15:22Z","isRefundable":0,"cancelTime":"2019-08-24T14:15:22Z","cancelBy":"string","cancelByName":"string","cancelReason":0,"isRefund":0,"cancelEnclosure":"string","cancelRemark":"string","status":0,"contractId":"string","refundId":"string","createByName":"string","updateByName":"string","isDel":0,"contractNo":"string","signDate":"2019-08-24T14:15:22Z","contractLeaseUnit":"string","lesseeName":"string"}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[BookingVo](#schemabookingvo)]|false|none||none|
|» id|string|false|none|$column.columnComment|none|
|» projectId|string|false|none|项目id|项目id|
|» customerName|string|false|none|客户名称|客户名称|
|» propertyType|string|false|none|意向物业类型|意向物业类型|
|» roomId|string|false|none|房源id|房源id|
|» roomName|string|false|none|房源名称|房源名称|
|» bookingAmount|number|false|none|定单金额|定单金额|
|» receivableDate|string(date-time)|false|none|应收日期|应收日期|
|» expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|» isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|» cancelTime|string(date-time)|false|none|作废时间|作废时间|
|» cancelBy|string|false|none|作废人|作废人|
|» cancelByName|string|false|none|作废人姓名|作废人姓名|
|» cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|» isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|» cancelEnclosure|string|false|none|退定附件|退定附件|
|» cancelRemark|string|false|none|退定说明|退定说明|
|» status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|» contractId|string|false|none|转签约合同id|转签约合同id|
|» refundId|string|false|none|退款单id|退款单id|
|» createByName|string|false|none|创建人姓名|创建人姓名|
|» updateByName|string|false|none|更新人姓名|更新人姓名|
|» isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|
|» contractNo|string|false|none|合同号|合同号|
|» signDate|string(date-time)|false|none|合同签订日期|合同签订日期|
|» contractLeaseUnit|string|false|none|租赁单元|租赁单元|
|» lesseeName|string|false|none|承租人名称|承租人名称|

<a id="opIdlist_7"></a>

## GET 查询定单列表

GET /booking/list

查询定单列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|searchType|query|integer(int32)| 否 | 页签类型|页签类型:1-待生效 2生效中 3已转签 4已作废|
|projectId|query|string| 否 | 项目id|项目id|
|customerName|query|string| 否 | 客户名称|客户名称|
|roomName|query|string| 否 | 意向房源|意向房源|
|status|query|integer(int64)| 否 | 状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|createTimeStart|query|string(date-time)| 否 | 创建日期开始|创建日期开始|
|createTimeEnd|query|string(date-time)| 否 | 创建日期结束|创建日期结束|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|actualReceiveTimeStart|query|string(date-time)| 否 | 实收日期开始|实收日期开始|
|actualReceiveTimeEnd|query|string(date-time)| 否 | 实收日期结束|实收日期结束|
|contractNo|query|string| 否 | 合同编号|合同编号|
|signDateStart|query|string(date-time)| 否 | 合同签订日期开始|合同签订日期开始|
|signDateEnd|query|string(date-time)| 否 | 合同签订日期结束|合同签订日期结束|
|contractLeaseUnit|query|string| 否 | 合同租赁单元|合同租赁单元|
|lesseeName|query|string| 否 | 承租人名称|承租人名称|
|cancelReason|query|integer(int32)| 否 | 作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|cancelByName|query|string| 否 | 作废人姓名|作废人姓名|
|cancelTimeStart|query|string(date-time)| 否 | 作废日期开始|作废日期开始|
|cancelTimeEnd|query|string(date-time)| 否 | 作废日期结束|作废日期结束|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdinvalidBooking"></a>

## POST 定单作废接口

POST /booking/invalid

> Body 请求参数

```json
{
  "id": "string",
  "cancelRemark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingInvalidDto](#schemabookinginvaliddto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[AjaxResult](#schemaajaxresult)|

<a id="opIdcancelBooking"></a>

## POST 退定接口

POST /booking/cancel

> Body 请求参数

```json
{
  "id": "string",
  "cancelRemark": "string",
  "cancelEnclosure": "string",
  "isRefund": 0,
  "isSubmit": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[BookingCancelDto](#schemabookingcanceldto)| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddetail"></a>

## GET 定单详情接口

GET /booking/detail/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||定单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","customerName":"string","propertyType":"string","roomId":"string","roomName":"string","bookingAmount":0,"receivableDate":"2019-08-24T14:15:22Z","expectSignDate":"2019-08-24T14:15:22Z","isRefundable":0,"cancelTime":"2019-08-24T14:15:22Z","cancelBy":"string","cancelByName":"string","cancelReason":0,"isRefund":0,"cancelEnclosure":"string","cancelRemark":"string","status":0,"contractId":"string","refundId":"string","createByName":"string","updateByName":"string","isDel":0,"contractNo":"string","signDate":"2019-08-24T14:15:22Z","contractLeaseUnit":"string","lesseeName":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[BookingVo](#schemabookingvo)|

<a id="opIddeleteBooking"></a>

## DELETE 删除定单接口

DELETE /booking/delete/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||定单ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_BookingAddDTO">BookingAddDTO</h2>

<a id="schemabookingadddto"></a>
<a id="schema_BookingAddDTO"></a>
<a id="tocSbookingadddto"></a>
<a id="tocsbookingadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "isDel": 0,
  "isSubmit": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|customerName|string|false|none|客户名称|客户名称|
|propertyType|string|false|none|意向物业类型|意向物业类型|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房源名称|房源名称|
|bookingAmount|number|false|none|定单金额|定单金额|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|cancelEnclosure|string|false|none|退定附件|退定附件|
|cancelRemark|string|false|none|退定说明|退定说明|
|status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|contractId|string|false|none|转签约合同id|转签约合同id|
|refundId|string|false|none|退款单id|退款单id|
|isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|
|isSubmit|integer(int32)|false|none|0-暂存,1-提交|0-暂存,1-提交|

<h2 id="tocS_BookingInvalidDto">BookingInvalidDto</h2>

<a id="schemabookinginvaliddto"></a>
<a id="schema_BookingInvalidDto"></a>
<a id="tocSbookinginvaliddto"></a>
<a id="tocsbookinginvaliddto"></a>

```json
{
  "id": "string",
  "cancelRemark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|定单ID|需要作废的定单ID|
|cancelRemark|string|false|none|作废说明|定单作废的说明|

<h2 id="tocS_BookingCancelDto">BookingCancelDto</h2>

<a id="schemabookingcanceldto"></a>
<a id="schema_BookingCancelDto"></a>
<a id="tocSbookingcanceldto"></a>
<a id="tocsbookingcanceldto"></a>

```json
{
  "id": "string",
  "cancelRemark": "string",
  "cancelEnclosure": "string",
  "isRefund": 0,
  "isSubmit": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|订单ID|需要退定的订单ID|
|cancelRemark|string|false|none|退定说明|退定的具体说明|
|cancelEnclosure|string|false|none|退定附件|退定相关的附件|
|isRefund|integer(int32)|false|none|是否退款|是否退款，0-否，1-是|
|isSubmit|integer(int32)|false|none|是否提交|使用该字段判断是暂存还是提交，0-暂存，1-提交|

<h2 id="tocS_BookingVo">BookingVo</h2>

<a id="schemabookingvo"></a>
<a id="schema_BookingVo"></a>
<a id="tocSbookingvo"></a>
<a id="tocsbookingvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "customerName": "string",
  "propertyType": "string",
  "roomId": "string",
  "roomName": "string",
  "bookingAmount": 0,
  "receivableDate": "2019-08-24T14:15:22Z",
  "expectSignDate": "2019-08-24T14:15:22Z",
  "isRefundable": 0,
  "cancelTime": "2019-08-24T14:15:22Z",
  "cancelBy": "string",
  "cancelByName": "string",
  "cancelReason": 0,
  "isRefund": 0,
  "cancelEnclosure": "string",
  "cancelRemark": "string",
  "status": 0,
  "contractId": "string",
  "refundId": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": 0,
  "contractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "contractLeaseUnit": "string",
  "lesseeName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|$column.columnComment|none|
|projectId|string|false|none|项目id|项目id|
|customerName|string|false|none|客户名称|客户名称|
|propertyType|string|false|none|意向物业类型|意向物业类型|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房源名称|房源名称|
|bookingAmount|number|false|none|定单金额|定单金额|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|expectSignDate|string(date-time)|false|none|预计签约日期|预计签约日期|
|isRefundable|integer(int32)|false|none|定单金额是否可退:0-否,1-是|定单金额是否可退:0-否,1-是|
|cancelTime|string(date-time)|false|none|作废时间|作废时间|
|cancelBy|string|false|none|作废人|作废人|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|isRefund|integer(int32)|false|none|是否退款:0-否,1-是|是否退款:0-否,1-是|
|cancelEnclosure|string|false|none|退定附件|退定附件|
|cancelRemark|string|false|none|退定说明|退定说明|
|status|integer(int32)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|contractId|string|false|none|转签约合同id|转签约合同id|
|refundId|string|false|none|退款单id|退款单id|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|integer(int32)|false|none|0-否,1-是|0-否,1-是|
|contractNo|string|false|none|合同号|合同号|
|signDate|string(date-time)|false|none|合同签订日期|合同签订日期|
|contractLeaseUnit|string|false|none|租赁单元|租赁单元|
|lesseeName|string|false|none|承租人名称|承租人名称|

<h2 id="tocS_BookingQueryDTO">BookingQueryDTO</h2>

<a id="schemabookingquerydto"></a>
<a id="schema_BookingQueryDTO"></a>
<a id="tocSbookingquerydto"></a>
<a id="tocsbookingquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "searchType": 0,
  "projectId": "string",
  "customerName": "string",
  "roomName": "string",
  "status": 0,
  "createTimeStart": "2019-08-24T14:15:22Z",
  "createTimeEnd": "2019-08-24T14:15:22Z",
  "createByName": "string",
  "actualReceiveTimeStart": "2019-08-24T14:15:22Z",
  "actualReceiveTimeEnd": "2019-08-24T14:15:22Z",
  "contractNo": "string",
  "signDateStart": "2019-08-24T14:15:22Z",
  "signDateEnd": "2019-08-24T14:15:22Z",
  "contractLeaseUnit": "string",
  "lesseeName": "string",
  "cancelReason": 0,
  "cancelByName": "string",
  "cancelTimeStart": "2019-08-24T14:15:22Z",
  "cancelTimeEnd": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|searchType|integer(int32)|false|none|页签类型|页签类型:1-待生效 2生效中 3已转签 4已作废|
|projectId|string|false|none|项目id|项目id|
|customerName|string|false|none|客户名称|客户名称|
|roomName|string|false|none|意向房源|意向房源|
|status|integer(int64)|false|none|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废|
|createTimeStart|string(date-time)|false|none|创建日期开始|创建日期开始|
|createTimeEnd|string(date-time)|false|none|创建日期结束|创建日期结束|
|createByName|string|false|none|创建人姓名|创建人姓名|
|actualReceiveTimeStart|string(date-time)|false|none|实收日期开始|实收日期开始|
|actualReceiveTimeEnd|string(date-time)|false|none|实收日期结束|实收日期结束|
|contractNo|string|false|none|合同编号|合同编号|
|signDateStart|string(date-time)|false|none|合同签订日期开始|合同签订日期开始|
|signDateEnd|string(date-time)|false|none|合同签订日期结束|合同签订日期结束|
|contractLeaseUnit|string|false|none|合同租赁单元|合同租赁单元|
|lesseeName|string|false|none|承租人名称|承租人名称|
|cancelReason|integer(int32)|false|none|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|作废原因: 0-退定退款, 1-退定不退款, 2-未收款取消预定, 3-转签合同作废|
|cancelByName|string|false|none|作废人姓名|作废人姓名|
|cancelTimeStart|string(date-time)|false|none|作废日期开始|作废日期开始|
|cancelTimeEnd|string(date-time)|false|none|作废日期结束|作废日期结束|

