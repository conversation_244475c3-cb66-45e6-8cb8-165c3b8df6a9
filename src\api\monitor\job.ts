import http from '../index';

// 查询定时任务调度列表
export const listJob = (params: any) => {
    return http.get('/business-job/job/list', params);
};

// 查询定时任务调度详细
export const getJob = (jobId: string) => {
    return http.get('/business-job/job/' + jobId);
};

// 新增定时任务调度
export const addJob = (params: any) => {
    return http.post('/business-job/job', params);
};

// 修改定时任务调度
export const updateJob = (params: any) => {
    return http.put('/business-job/job', params);
};

// 删除定时任务调度
export const delJob = (jobId: string) => {
    return http.delete('/business-job/job/' + jobId);
};

// 任务状态修改
export const changeJobStatus = (params: any) => {
    return http.put('/business-job/job/changeStatus', params);
};

// 定时任务立即执行一次
export const runJob = (params: any) => {
    return http.put('/business-job/job/run', params);
};

// 导出
export const exportJob = (params: any) => {
    return http.download('/business-job/job/export', params);
};
