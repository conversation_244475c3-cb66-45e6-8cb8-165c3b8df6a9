<template>
    <div class="contract-reduction">
        <section>
            <sectionTitle title="减免缓信息"></sectionTitle>
        </section>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table
                :columns="columns"
                :data="tableData"
                :pagination="pagination"
                :scroll="{ x: 1000 }"
                :bordered="{ cell: true }"
                @page-change="handlePageChange"
            >
                <template #type="{ record }">
                    {{ getTypeText(record.type) }}
                </template>
                <template #status="{ record }">
                    {{ getStatusText(record.status) }}
                </template>
                <template #approveStatus="{ record }">
                    {{ getApproveStatusText(record.approveStatus) }}
                </template>
                <template #contractPeriod="{ record }">
                    <span>{{
                        record.startDate && record.endDate
                            ? `${record.startDate} 至 ${record.endDate}`
                            : record.contractPeriod
                    }}</span>
                </template>
                <template #action="{ record }">
                    <a-space :key="`action-${record.id}-${record.status}`">
                        <template v-if="record.status === 0">
                            <a-button
                                type="text"
                                size="small"
                                @click="handleEdit(record)"
                                >编辑</a-button
                            >
                            <a-button
                                type="text"
                                size="small"
                                status="danger"
                                @click="handleDelete(record)"
                                >删除</a-button
                            >
                            <a-button
                                type="text"
                                size="small"
                                @click="handleApprove(record)"
                                >审批</a-button
                            >
                        </template>
                        <template v-else>
                            <a-button
                                type="text"
                                size="small"
                                @click="handleView(record)"
                                >查看</a-button
                            >
                            <a-button
                                type="text"
                                size="small"
                                @click="handleViewWorkflow(record)"
                                >查看工作流</a-button
                            >
                        </template>
                    </a-space>
                </template>
            </a-table>
        </a-card>
         <!-- 减免缓表单组件 -->
         <ReductionForm v-if="showReductionForm" ref="reductionFormRef" @success="handleFormSuccess" @cancel="handleFormCancel" />
    </div>
</template>

<script setup lang="ts">
    import { watch, onMounted } from 'vue';
    import sectionTitle from '@/components/sectionTitle/index.vue';
    import { Message, Modal } from '@arco-design/web-vue';
    import {
        getReductionList,
        deleteReduction,
        exportReductionList,
        type ReductionQueryDTO,
    } from '@/api/reduction';
    import ReductionForm from '@/views/reduction/components/ReductionForm.vue'
    import { useContractStore } from '@/store/modules/contract/index';

    // 组件引用和显示控制
const reductionFormRef = ref()
const showReductionForm = ref(false)

    const contractStore = useContractStore();
    const contractData = computed(() => contractStore.contractDetail as any);

    const tableData = ref<any[]>([]);

    // 分页配置
    const pagination = ref({
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: true,
        showJumper: true,
        showPageSize: true,
        pageSizeOptions: [10, 20, 50, 100],
    });

    const columns = computed(() => {
        return [
            {
                title: '序号',
                dataIndex: 'index',
                width: 70,
                align: 'center',
            },
            {
                title: '项目',
                dataIndex: 'projectName',
                width: 200,
                ellipsis: true,
                tooltip: true,
                align: 'center',
            },
            {
                title: '类型',
                dataIndex: 'type',
                slotName: 'type',
                width: 80,
                align: 'center',
            },
            {
                title: '合同号',
                dataIndex: 'contractNo',
                width: 240,
                ellipsis: true,
                tooltip: true,
                align: 'center',
            },
            {
                title: '租赁单元',
                dataIndex: 'roomName',
                width: 180,
                ellipsis: true,
                tooltip: true,
                align: 'center',
            },
            {
                title: '承租方',
                dataIndex: 'customerName',
                width: 160,
                ellipsis: true,
                tooltip: true,
                align: 'center',
            },
            {
                title: '合同租期',
                dataIndex: 'contractPeriod',
                slotName: 'contractPeriod',
                width: 220,
                ellipsis: true,
                tooltip: true,
                align: 'center',
            },
            {
                title: '单据状态',
                dataIndex: 'status',
                slotName: 'status',
                width: 100,
                align: 'center',
            },
            {
                title: '审批状态',
                dataIndex: 'approveStatus',
                slotName: 'approveStatus',
                width: 100,
                align: 'center',
            },
            {
                title: '审批通过时间',
                dataIndex: 'approveTime',
                width: 160,
                ellipsis: true,
                tooltip: true,
                align: 'center',
            },
            {
                title: '创建人',
                dataIndex: 'createByName',
                width: 100,
                ellipsis: true,
                tooltip: true,
                align: 'center',
            },
            {
                title: '创建日期',
                dataIndex: 'createTime',
                width: 180,
                ellipsis: true,
                tooltip: true,
                align: 'center',
            },
            {
                title: '操作',
                slotName: 'action',
                width: 260,
                align: 'center',
                fixed: 'right',
            },
        ];
    });
    // 审批状态相关方法
    const getApproveStatusText = (approveStatus: number) => {
        const statusMap: Record<number, string> = {
            0: '草稿',
            1: '审批中',
            2: '已通过',
            3: '已驳回',
            4: '作废',
        };
        return statusMap[approveStatus] || '-';
    };
    // 类型相关方法
    const getTypeText = (type: number) => {
        const typeMap: Record<number, string> = {
            1: '减免',
            2: '缓缴',
            3: '分期',
        };
        return typeMap[type] || '-';
    };
    // 状态相关方法
    const getStatusText = (status: number) => {
        const statusMap: Record<number, string> = {
            0: '草稿',
            1: '待生效',
            2: '生效',
            4: '作废',
        };
        return statusMap[status] || '-';
    };
    // 查询数据
    const loadData = async (page = 1, pageSize = 10) => {
        try {
            // 检查contractData是否有值
            if (!contractData.value || !contractData.value.contractNo) {
                console.log('contractData或contractNo为空，跳过加载减免缓数据');
                return;
            }

            const response = await getReductionList({
                pageNum: page,
                pageSize: pageSize,
                unionId: contractData.value.unionId, // 使用合同的unionId
            });
            // 为数据添加序号
            tableData.value = (response.rows || []).map((item: any, index: number) => ({
                ...item,
                index: (page - 1) * pageSize + index + 1
            }));
            // 更新分页信息
            pagination.value.total = response.total || 0;
            pagination.value.current = page;
            pagination.value.pageSize = pageSize;
        } catch (error) {
            console.error('获取减免缓列表失败:', error);
            tableData.value = [];
        }
    };
    // 删除
    const handleDelete = async (record: any) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除这条记录吗？删除后不可恢复。',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
                try {
                    await deleteReduction(record.id);
                    Message.success('删除成功');
                    loadData(1, pagination.value.pageSize);
                } catch (error) {
                    console.error('删除失败:', error);
                }
            },
        });
    };
    // 审批
    const handleApprove = (record: any) => {
        // TODO: 实现审批功能
        Message.info('审批功能待实现');
    };

    // 查看工作流
    const handleViewWorkflow = (record: any) => {
        // TODO: 实现查看工作流功能
        Message.info('查看工作流功能待实现');
    };
    // 查看
const handleView = async (record: any) => {
    showReductionForm.value = true
    await nextTick()
    // 传递记录信息，包含类型，用于调用对应的详情接口
    reductionFormRef.value?.show(record, 'view')
}

// 编辑
const handleEdit = async (record: any) => {
    showReductionForm.value = true
    await nextTick()
    // 传递记录信息，包含类型，用于调用对应的详情接口
    reductionFormRef.value?.show(record, 'edit')
}
// 分页变化处理
const handlePageChange = (page: number, pageSize: number) => {
    loadData(page, pageSize);
};

// 表单成功回调
const handleFormSuccess = () => {
    showReductionForm.value = false
    loadData(1, pagination.value.pageSize)
}

// 表单取消回调
const handleFormCancel = () => {
    showReductionForm.value = false
}

// 组件初始化时加载数据
onMounted(() => {
    // 如果contractData已经有值，直接加载数据
    if (contractData.value && contractData.value.contractNo) {
        loadData(1, pagination.value.pageSize);
    }
});

// 监听contractData的变化，当有值时加载数据
watch(
    () => contractData.value?.contractNo,
    (newContractNo) => {
        if (newContractNo) {
            loadData(1, pagination.value.pageSize);
        }
    },
    { immediate: true }
);
</script>
<style scoped lang="less"></style>
