* {
    box-sizing: border-box;
}

html,
body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-size: 14px;
    background-color: var(--color-bg-1);
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;

    --color-menu-primary-bg: rgb(var(--primary-6));
    --color-menu-primary-hover: rgb(var(--primary-4));
    --color-menu-primary-selected: var(--color-white);
    --color-menu-primary-selected-text: rgb(var(--primary-6));
    --color-menu-primary-text: var(--color-white);

    --arcoblue-6: 19, 94, 194 !important;
    --primary-6: 19, 94, 194 !important;

    --primary-5: 21, 75, 173 !important;
    --primary-5: 21, 75, 173 !important;

    --border-radius-small: 4px !important;
}

.echarts-tooltip-diy {
    background: linear-gradient(
        304.17deg,
        rgba(253, 254, 255, 0.6) -6.04%,
        rgba(244, 247, 252, 0.6) 85.2%
    ) !important;
    border: none !important;
    backdrop-filter: blur(10px) !important;
    /* Note: backdrop-filter has minimal browser support */

    border-radius: 6px !important;
    .content-panel {
        display: flex;
        justify-content: space-between;
        padding: 0 9px;
        background: rgba(255, 255, 255, 0.8);
        width: 164px;
        height: 32px;
        line-height: 32px;
        box-shadow: 6px 0px 16px rgba(34, 87, 188, 0.1);
        border-radius: 4px;
        margin-bottom: 4px;
    }
    .tooltip-title {
        margin: 0 0 10px 0;
    }
    p {
        margin: 0;
    }
    .tooltip-title,
    .tooltip-value {
        font-size: 13px;
        line-height: 15px;
        display: flex;
        align-items: center;
        text-align: right;
        color: #1d2129;
        font-weight: bold;
    }
    .tooltip-item-icon {
        display: inline-block;
        margin-right: 8px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
    }
}

.general-card {
    border-radius: 4px;
    border: none;
    & > .arco-card-header {
        height: auto;
        padding: 16px;
        border: none;
    }
    & > .arco-card-body {
        padding: 0 16px 16px 16px;
    }
}

.flex-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    & > .arco-card-header {
        height: auto;
        flex: 0 0 auto;
        padding: 16px;
    }
    & > .arco-card-body {
        flex: 1 1 auto;
        padding: 16px;
        overflow-y: auto;
    }
}

.split-line {
    border-color: rgb(var(--gray-2));
}

.arco-table-cell {
    .circle {
        display: inline-block;
        margin-right: 4px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: rgb(var(--blue-6));
        &.pass {
            background-color: rgb(var(--green-6));
        }
    }
    .arco-link {
        padding: 0 4px;
    }
}

.arco-table-th .arco-table-cell, .arco-table-td .arco-table-cell {
    justify-content: center !important;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
    transition: all 0.2s;
}
.fade-transform-enter-from {
    opacity: 0;
    transition: all 0.2s;
    transform: translateX(-30px);
}
.fade-transform-leave-to {
    opacity: 0;
    transition: all 0.2s;
    transform: translateX(30px);
}

.arco-form-item {
    margin-bottom: 16px;
}

// 侧边栏菜单主题色
.arco-menu-primary {
    // background-color: var(--color-menu-primary-bg)
    background-color: #154bad ;
}

.arco-menu-primary .arco-menu-item,.arco-menu-primary .arco-menu-group-title,.arco-menu-primary .arco-menu-pop-header,.arco-menu-primary .arco-menu-inline-header {
    color: var(--color-menu-primary-text);
    // background-color: var(--color-menu-primary-bg);
    background-color: #154bad;
}

.arco-menu-primary .arco-menu-item .arco-icon,.arco-menu-primary .arco-menu-group-title .arco-icon,.arco-menu-primary .arco-menu-pop-header .arco-icon,.arco-menu-primary .arco-menu-inline-header .arco-icon,.arco-menu-primary .arco-menu-item .arco-menu-icon,.arco-menu-primary .arco-menu-group-title .arco-menu-icon,.arco-menu-primary .arco-menu-pop-header .arco-menu-icon,.arco-menu-primary .arco-menu-inline-header .arco-menu-icon {
    color: var(--color-menu-primary-text);
}

.arco-menu-primary .arco-menu-item:hover,.arco-menu-primary .arco-menu-group-title:hover,.arco-menu-primary .arco-menu-pop-header:hover,.arco-menu-primary .arco-menu-inline-header:hover {
    color: var(--color-menu-primary-text);
    background-color: var(--color-menu-primary-hover)
}

.arco-menu-primary .arco-menu-item:hover .arco-icon,.arco-menu-primary .arco-menu-group-title:hover .arco-icon,.arco-menu-primary .arco-menu-pop-header:hover .arco-icon,.arco-menu-primary .arco-menu-inline-header:hover .arco-icon,.arco-menu-primary .arco-menu-item:hover .arco-menu-icon,.arco-menu-primary .arco-menu-group-title:hover .arco-menu-icon,.arco-menu-primary .arco-menu-pop-header:hover .arco-menu-icon,.arco-menu-primary .arco-menu-inline-header:hover .arco-menu-icon {
    color: var(--color-menu-primary-text);
}

.arco-menu-primary .arco-menu-item.arco-menu-selected,.arco-menu-primary .arco-menu-group-title.arco-menu-selected,.arco-menu-primary .arco-menu-pop-header.arco-menu-selected,.arco-menu-primary .arco-menu-inline-header.arco-menu-selected,.arco-menu-primary .arco-menu-item.arco-menu-selected .arco-icon,.arco-menu-primary .arco-menu-group-title.arco-menu-selected .arco-icon,.arco-menu-primary .arco-menu-pop-header.arco-menu-selected .arco-icon,.arco-menu-primary .arco-menu-inline-header.arco-menu-selected .arco-icon,.arco-menu-primary .arco-menu-item.arco-menu-selected .arco-menu-icon,.arco-menu-primary .arco-menu-group-title.arco-menu-selected .arco-menu-icon,.arco-menu-primary .arco-menu-pop-header.arco-menu-selected .arco-menu-icon,.arco-menu-primary .arco-menu-inline-header.arco-menu-selected .arco-menu-icon {
    color: var(--color-menu-primary-selected-text)
}

.arco-menu-primary .arco-menu-item.arco-menu-disabled,.arco-menu-primary .arco-menu-group-title.arco-menu-disabled,.arco-menu-primary .arco-menu-pop-header.arco-menu-disabled,.arco-menu-primary .arco-menu-inline-header.arco-menu-disabled {
    color: var(--color-text-2);
    background-color: var(--color-menu-primary-bg)
}

.arco-menu-primary .arco-menu-item.arco-menu-disabled .arco-icon,.arco-menu-primary .arco-menu-group-title.arco-menu-disabled .arco-icon,.arco-menu-primary .arco-menu-pop-header.arco-menu-disabled .arco-icon,.arco-menu-primary .arco-menu-inline-header.arco-menu-disabled .arco-icon,.arco-menu-primary .arco-menu-item.arco-menu-disabled .arco-menu-icon,.arco-menu-primary .arco-menu-group-title.arco-menu-disabled .arco-menu-icon,.arco-menu-primary .arco-menu-pop-header.arco-menu-disabled .arco-menu-icon,.arco-menu-primary .arco-menu-inline-header.arco-menu-disabled .arco-menu-icon {
    color: var(--color-text-2)
}

.arco-menu-primary .arco-menu-item.arco-menu-selected {
    background-color: var(--color-menu-primary-selected)
}

.arco-menu-primary .arco-menu-inline-header.arco-menu-selected,.arco-menu-primary .arco-menu-inline-header.arco-menu-selected .arco-icon,.arco-menu-primary .arco-menu-inline-header.arco-menu-selected .arco-menu-icon {
    color: #fff;
}

.arco-menu-primary .arco-menu-inline-header.arco-menu-selected:hover {
    background-color: var(--color-menu-primary-hover)
}

.arco-menu-primary.arco-menu-horizontal .arco-menu-item.arco-menu-selected,.arco-menu-primary.arco-menu-horizontal .arco-menu-group-title.arco-menu-selected,.arco-menu-primary.arco-menu-horizontal .arco-menu-pop-header.arco-menu-selected,.arco-menu-primary.arco-menu-horizontal .arco-menu-inline-header.arco-menu-selected {
    background: none;
    transition: color .2s cubic-bezier(0,0,1,1)
}

.arco-menu-primary.arco-menu-horizontal .arco-menu-item.arco-menu-selected:hover,.arco-menu-primary.arco-menu-horizontal .arco-menu-group-title.arco-menu-selected:hover,.arco-menu-primary.arco-menu-horizontal .arco-menu-pop-header.arco-menu-selected:hover,.arco-menu-primary.arco-menu-horizontal .arco-menu-inline-header.arco-menu-selected:hover {
    background-color: var(--color-menu-primary-selected)
}

.arco-menu-primary .arco-menu-group-title {
    color: var(--color-text-3);
    pointer-events: none
}

.arco-menu-primary .arco-menu-collapse-button {
    color: var(--color-white);
    background-color: rgb(var(--primary-7))
}

.arco-menu-primary .arco-menu-collapse-button:hover {
    background-color: rgb(var(--primary-7))
}

.arco-menu-primary .arco-menu-pop-header.arco-menu-selected .arco-menu-icon {
    color: rgb(var(--primary-6));
}

.arco-menu-primary .arco-menu-pop-header.arco-menu-selected {
    background-color: var(--color-white);
}

.arco-trigger-menu-primary {
    background-color: var(--color-menu-primary-bg);
    .arco-trigger-menu-item {
        color: var(--color-menu-primary-text);
    }
    
    .arco-trigger-menu-item:hover {
        background-color: var(--color-menu-primary-hover);
        color: var(--color-menu-primary-text);
    }
    
    .arco-trigger-menu-item.arco-trigger-menu-selected, .arco-trigger-menu-pop-header.arco-trigger-menu-selected {
        background-color: var(--color-menu-primary-selected);
        color: var(--color-menu-primary-selected-text);
    }
}
.arco-menu-pop-trigger[theme='primary'] .arco-trigger-arrow {
    background-color: var(--color-menu-primary-bg);
}

.arco-menu-dark .arco-menu-item.arco-menu-selected, .arco-menu-dark .arco-menu-group-title.arco-menu-selected, .arco-menu-dark .arco-menu-pop-header.arco-menu-selected, .arco-menu-dark .arco-menu-inline-header.arco-menu-selected {
    color: rgb(var(--primary-6));
}
