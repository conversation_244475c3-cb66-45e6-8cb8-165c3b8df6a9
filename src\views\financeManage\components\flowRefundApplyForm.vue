<template>
    <a-drawer :visible="drawerVisible" :footer="true" :title="drawerTitle" unmount-on-close @cancel="handleCancel"
        class="common-drawer">
        <div class="refund-form-container">
            <div class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>退款申请信息</span>
                </div>
                <a-form :model="formData" :rules="rules" :label-col-props="{ span: 9 }" :wrapper-col-props="{ span: 15 }"
                    label-align="right" ref="formRef">
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item field="refundNo" label="退款单号">
                                <a-input v-model="formData.refundNo" placeholder="请输入退款单号" allow-clear disabled/>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="refundType" label="退款类型" required>
                                <a-select v-model="formData.refundType" placeholder="请选择退款类型" disabled>
                                    <a-option :value="0">退租退款</a-option>
                                    <a-option :value="1">退定退款</a-option>
                                    <a-option :value="2">未明流水退款</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="applyTime" label="退款申请日期" required>
                                <a-date-picker v-model="formData.applyTime" style="width: 100%" show-time />
                            </a-form-item>
                        </a-col>

                        <a-col :span="8">
                            <a-form-item field="refundAmount" label="退款金额" required>
                                <a-input-number v-model="formData.refundAmount" placeholder="请输入退款金额"
                                    style="width: 100%" :precision="2" />
                                <template #append>元</template>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="feeType" label="退款费用类型">
                                <a-input v-model="formData.feeType" placeholder="请输入退款费用类型" allow-clear/>
                                <!-- <a-select v-model="formData.feeType" placeholder="请选择退款费用类型">
                                    <a-option value="1">保证金</a-option>
                                    <a-option value="2">租金</a-option>
                                    <a-option value="3">定金</a-option>
                                    <a-option value="4">未明流水</a-option>
                                </a-select> -->
                            </a-form-item>
                        </a-col>
                        <!-- 默认原路退回，如果有特殊要求需要银行转账，请在退款申请中说明 -->
                        <a-col :span="8">
                            <a-form-item field="refundWay" label="退款方式" required>
                                <a-select v-model="formData.refundWay" placeholder="请选择退款方式" disabled>
                                    <a-option :value="0">原路退回</a-option>
                                    <a-option :value="1">银行转账</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverName" label="收款方姓名" required>
                                <a-input v-model="formData.receiverName" placeholder="请输入收款方姓名" allow-clear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverBank" label="收款方开户行" required>
                                <a-input v-model="formData.receiverBank" placeholder="请输入收款方开户行" allow-clear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverAccount" label="收款方银行账号">
                                <a-input v-model="formData.receiverAccount" placeholder="请输入收款方银行账号" allow-clear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24" >
                            <a-form-item field="refundRemark" label="退款申请说明" :label-col-props="{ span: 3 }"
                                :wrapper-col-props="{ span: 21 }">
                                <a-textarea v-model="formData.refundRemark" placeholder="请输入退款申请说明" allow-clear
                                    :max-length="200" show-word-limit />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>

            <!-- 流水信息 -->
            <div class="form-section" v-if="formData.refundType === 2">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>流水信息</span>
                </div>
                <div class="base-info">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">流水号：</span>
                            <span class="info-value">{{ flowInfo.flowNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付时间：</span>
                            <span class="info-value">{{ flowInfo.payTime }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付金额：</span>
                            <span class="info-value">{{ formatMoney(flowInfo.amount) }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">支付方式：</span>
                            <span class="info-value">{{ flowInfo.payMethod }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付账号：</span>
                            <span class="info-value">{{ flowInfo.payAccount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">银行支行：</span>
                            <span class="info-value">{{ flowInfo.bankBranch }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 附件上传 -->
            <div class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>附件：</span>
                </div>
                <div class="attachment-upload">
                    <upload-file v-model="formData.attachments" :limit="0" />
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <template #footer>
            <div class="drawer-footer">
                <a-space>
                    <a-button @click="handleCancel">取消</a-button>
                    <a-button type="primary" status="success" @click="handleSave">暂存</a-button>
                    <a-button type="primary" @click="handleSubmit">发起审批</a-button>
                </a-space>
            </div>
        </template>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import type { TableColumnData } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue';
import dayjs from 'dayjs';
import UploadFile from '@/components/upload/uploadFile.vue';
import { getFinancialRefundDetail, saveFinancialRefund, type FinancialRefundDetailVo, type FinancialRefundAddDTO } from '@/api/refundManage';

// 抽屉控制
const drawerVisible = ref(false);
const drawerTitle = ref('退款申请单');

// 表单数据
const formData = reactive<FinancialRefundAddDTO & { refundNo: string }>({
    refundType: 2, // 默认未明流水退款
    refundNo: '', // 退款单号
    applyTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    feeType: '',
    refundAmount: 0,
    refundWay: 0, // 默认原路退回
    receiverName: '',
    receiverAccount: '', // 包含开户行信息的完整账号
    receiverBank: '', // 可选的额外银行信息
    refundRemark: '',
    attachments: undefined,
    isSubmit: 0,
    projectId: '',
    bizId: ''
});

// 合同信息 - 简化，仅保留必要字段
const contractInfo = reactive({
    tenantName: '',
    contractNo: ''
});

// 退款明细数据接口
interface RefundDetailItem {
    paymentType: '收' | '支';
    feeItem: string;
    amount: number;
    feePeriod: string;
    feeExplain: string;
}

// 流水信息接口
interface FlowInfo {
    flowNo: string;
    payTime: string;
    amount: number;
    payMethod: string;
    payAccount: string;
    bankBranch: string;
}

// 退款费用明细表格列定义 - 移除，因为只用于流水退款
// const refundDetailColumns = ref<TableColumnData[]>([]);

// 退款费用明细数据 - 移除，因为只用于流水退款
// const refundDetailData = ref<RefundDetailItem[]>([]);

// 计算减免金额和最终金额 - 移除，因为只用于流水退款
// const discountAmount = ref(0);
// const finalAmount = computed(() => {});

// 减免原因 - 移除，因为只用于流水退款
// const discountReason = ref('');

// 手续办理情况 - 移除，因为只用于流水退款
// const procedureStatus = reactive({});

// 流水信息
const flowInfo = reactive<FlowInfo>({
    flowNo: '',
    payTime: '',
    amount: 0,
    payMethod: '',
    payAccount: '',
    bankBranch: ''
});

// 退款类型名称映射 - 移除，因为只用于流水退款
// const getRefundTypeName = (type: string) => {};

// 金额格式化
const formatMoney = (amount: number) => {
    return amount.toFixed(2);
};

const formRef = ref<FormInstance>();

// 表单校验规则
const rules = {
    refundType: [
        { required: true, message: '请选择退款类型' }
    ],
    refundAmount: [
        { required: true, message: '请输入退款金额' },
        { type: 'number', min: 0, message: '退款金额必须大于0' }
    ],
    receiverName: [
        { required: true, message: '请输入收款方名称' }
    ],
    receiverAccount: [
        { required: true, message: '请输入收款方账号' }
    ],
    projectId: [
        { required: true, message: '请选择项目' }
    ]
};

// 查看合同详情 - 移除，因为只用于流水退款

// 取消
const handleCancel = () => {
    drawerVisible.value = false;
};

// 保存为草稿
const handleSave = async () => {
    const validResult = await formRef.value?.validate();
    if (!validResult) {
        formData.isSubmit = 0;
        //attachments":""  这个没有就传null，不然json转换会报错
        if (!formData.attachments) {
            formData.attachments = undefined;
        }
        console.log('formData', formData);

        await saveFinancialRefund({
            ...formData,
            isSubmit: 0
        });
        Message.success('保存成功');
        drawerVisible.value = false;
    }
};

// 提交审批
const handleSubmit = async () => {
    const validResult = await formRef.value?.validate();
    if (!validResult) {
        formData.isSubmit = 1;
        if (!formData.attachments) {
            formData.attachments = undefined;
        }
        await saveFinancialRefund({
            ...formData,
            isSubmit: 1
        });
        Message.success('提交成功');
        drawerVisible.value = false;
    }
};

// 暴露方法给父组件
defineExpose({
    async open(options?: { refundType?: number; refundId?: string; bizId?: string; flowData?: any }) {
        // 重置表单数据
        Object.assign(formData, {
            refundType: options?.refundType ?? 2,
            refundNo: '',
            applyTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            feeType: '',
            refundAmount: 0,
            refundWay: 0,
            receiverName: '',
            receiverAccount: '',
            receiverBank: '',
            refundRemark: '',
            attachments: undefined,
            isSubmit: 0,
            projectId: '',
            bizId: ''
        });

        // 如果传入了 refundId 或 bizId，则先加载退款详情
        if (options?.refundId || options?.bizId) {
            const response = await getFinancialRefundDetail(options?.refundId, options?.refundType, options?.bizId);
            if (response.data) {
                const detailData = response.data;
                
                // 填充表单数据
                if (detailData.flow) {
                    const flow = detailData.flow;
                    formData.projectId = flow.projectId || ''
                    formData.refundAmount = (flow.amount || 0) - (flow.usedAmount || 0);
                    formData.receiverName = flow.payerName || formData.receiverName;
                    formData.receiverAccount = flow.payerAccount || formData.receiverAccount;

                    // 初始化流水信息
                    flowInfo.flowNo = flow.orderNo || '';
                    flowInfo.payTime = flow.entryTime || '';
                    flowInfo.amount = flow.amount || 0;
                    flowInfo.payMethod = flow.payMethod || '';
                    flowInfo.payAccount = flow.payerAccount || '';
                    flowInfo.bankBranch = '';
                    formData.refundNo = flow.orderNo || '';
                    formData.bizId = flow.id || '';
                }
            }
        }

        
        drawerVisible.value = true
    }
})
</script>

<style scoped lang="less">
.refund-form-container {
    padding: 0 16px;

    .form-header {
        text-align: center;
        margin-bottom: 24px;

        .form-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }
    }

    .form-section {
        margin-bottom: 24px;

        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .section-marker {
                width: 4px;
                height: 16px;
                background-color: #1890ff;
                margin-right: 8px;
                border-radius: 2px;
            }

            span {
                font-size: 16px;
                font-weight: 500;
                flex: 1;
            }
        }

        .base-info {
            background-color: #f9f9f9;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 16px;

            .info-row {
                display: flex;
                margin-bottom: 12px;

                &:last-child {
                    margin-bottom: 0;
                }

                .info-item {
                    flex: 1;
                    display: flex;

                    .info-label {
                        color: #646a73;
                        margin-right: 8px;
                        min-width: 90px;
                    }

                    .info-value {
                        color: #1d2129;
                        flex: 1;
                    }
                }
            }
        }

        .refund-detail {
            margin-top: 16px;

            .total-sum {
                display: flex;
                justify-content: flex-end;
                margin-top: 16px;
                gap: 24px;

                .money {
                    font-weight: 600;
                    color: #f56c6c;
                }
            }

            .discount-reason {
                display: flex;
                justify-content: flex-end;
                margin-top: 12px;
                color: #646a73;
            }
        }

        .procedure-status {
            display: flex;
            gap: 24px;
            background-color: #f9f9f9;
            padding: 16px;
            border-radius: 4px;

            .procedure-item {
                display: flex;
                align-items: center;

                .procedure-name {
                    color: #646a73;
                    margin-right: 8px;
                }

                .procedure-value {
                    color: #1d2129;
                    font-weight: 500;
                }
            }
        }
    }
}

.drawer-footer {
    display: flex;
    justify-content: flex-end;
}
</style>