import type { DictItem } from './types'
import { dictData } from './data'
import { ref, toRefs } from 'vue';

// 创建字典映射
const dictMap = new Map<string, DictItem[]>(Object.entries(dictData))

/**
 * 获取字典数据
 * @param dictType 字典类型
 * @returns 字典数组
 */
export const getDict = (dictType: string) => {
    const res: any = ref({})
    res.value = dictMap.get(dictType) || []
    return res.value
}

/**
 * 递归查找字典项
 * @param items 字典项数组
 * @param value 要查找的值
 * @returns 找到的字典项或null
 */
const findDictItem = (items: DictItem[], value: string | number): DictItem | null => {
    for (const item of items) {
        // 检查当前项是否匹配
        if (item.value == value) {
            return item
        }
        // 如果有子项，递归查找
        if (item.children && item.children.length > 0) {
            const found = findDictItem(item.children, value)
            if (found) {
                return found
            }
        }
    }
    return null
}

/**
 * 递归获取字典标签
 * @param dictType 字典类型
 * @param value 字典值
 * @returns 字典标签
 */
export const getDictLabel = (dictType: string, value: string | number): string => {
    const dict = dictMap.get(dictType)
    if (!dict) {
        return ''
    }
    
    const item = findDictItem(dict, value)
    return item ? item.label : ''
}

// 导出类型和常量
export type { DictItem } from './types'
export { DictType } from './data'