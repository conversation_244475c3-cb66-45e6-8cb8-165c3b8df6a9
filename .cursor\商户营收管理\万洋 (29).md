---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/商户营收管理

<a id="opIdedit_4"></a>

## PUT 修改商户营收管理

PUT /revenue

修改商户营收管理

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "revenueMonth": "string",
  "revenueAmount": 0,
  "reportDate": "2019-08-24T14:15:22Z",
  "attachment": "string",
  "isConfirm": true,
  "confirmBy": "string",
  "confirmByName": "string",
  "confirmTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractRevenueAddDTO](#schemacontractrevenueadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_3"></a>

## POST 新增商户营收管理

POST /revenue

新增商户营收管理

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "revenueMonth": "string",
  "revenueAmount": 0,
  "reportDate": "2019-08-24T14:15:22Z",
  "attachment": "string",
  "isConfirm": true,
  "confirmBy": "string",
  "confirmByName": "string",
  "confirmTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractRevenueAddDTO](#schemacontractrevenueadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdimportContractRevenues"></a>

## POST 通过模版导入商户营收管理

POST /revenue/template/import

通过Excel文件批量导入商户营收管理

> Body 请求参数

```json
{
  "file": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdsave_4"></a>

## POST 保存营收

POST /revenue/save

保存营收

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "revenueMonth": "string",
  "revenueAmount": 0,
  "reportDate": "2019-08-24T14:15:22Z",
  "attachment": "string",
  "isConfirm": true,
  "confirmBy": "string",
  "confirmByName": "string",
  "confirmTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractRevenueAddDTO](#schemacontractrevenueadddto)| 是 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_2"></a>

## POST 查询商户营收管理列表

POST /revenue/list

查询商户营收管理列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "revenueMonth": "string",
  "revenueAmount": 0,
  "reportDate": "2019-08-24T14:15:22Z",
  "attachment": "string",
  "isConfirm": true,
  "confirmBy": "string",
  "confirmByName": "string",
  "confirmTime": "2019-08-24T14:15:22Z",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "contractNo": "string",
  "customerName": "string",
  "roomName": "string",
  "reportDateStart": "string",
  "reportDateEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractRevenueQueryDTO](#schemacontractrevenuequerydto)| 是 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdlist_15"></a>

## GET 查询商户营收管理列表

GET /revenue/list

查询商户营收管理列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|contractId|query|string| 否 | 合同id|合同id|
|contractUnionId|query|string| 否 | contract_union_id|contract_union_id|
|revenueMonth|query|string| 否 | 营收月|营收月|
|revenueAmount|query|number| 否 | 营收金额|营收金额|
|reportDate|query|string(date-time)| 否 | 提报日期|提报日期|
|attachment|query|string| 否 | 附件|附件|
|isConfirm|query|boolean| 否 | 是否审核 0-否,1-是|是否审核 0-否,1-是|
|confirmBy|query|string| 否 | 审核人账号|审核人账号|
|confirmByName|query|string| 否 | 审核人姓名|审核人姓名|
|confirmTime|query|string(date-time)| 否 | 审核时间|审核时间|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdexport_5"></a>

## POST 导出询商户营收管理列表

POST /revenue/export

导出询商户营收管理列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|contractId|query|string| 否 | 合同id|合同id|
|contractUnionId|query|string| 否 | contract_union_id|contract_union_id|
|revenueMonth|query|string| 否 | 营收月|营收月|
|revenueAmount|query|number| 否 | 营收金额|营收金额|
|reportDate|query|string(date-time)| 否 | 提报日期|提报日期|
|attachment|query|string| 否 | 附件|附件|
|isConfirm|query|boolean| 否 | 是否审核 0-否,1-是|是否审核 0-否,1-是|
|confirmBy|query|string| 否 | 审核人账号|审核人账号|
|confirmByName|query|string| 否 | 审核人姓名|审核人姓名|
|confirmTime|query|string(date-time)| 否 | 审核时间|审核时间|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|contractNo|query|string| 否 | 合同号|合同号|
|customerName|query|string| 否 | 承租人|承租人|
|roomName|query|string| 否 | 租赁单元|租赁单元|
|reportDateStart|query|string| 否 | 提报日期开始|提报日期开始|
|reportDateEnd|query|string| 否 | 提报日期结束|提报日期结束|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdcancelApprove"></a>

## POST 取消审批

POST /revenue/cancelApprove

取消审批

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||商户营收管理ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_4"></a>

## DELETE 删除商户营收管理

DELETE /revenue/delete

删除商户营收管理

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|query|array[string]| 是 ||商户营收管理ID列表|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdapprove_2"></a>

## POST 商户营收管理审批

POST /revenue/approve

商户营收管理审批

> Body 请求参数

```json
{
  "ids": [
    "string"
  ],
  "opinion": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[ContractRevenueApproveDTO](#schemacontractrevenueapprovedto)| 是 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddownloadTemplate"></a>

## GET 下载商户营收管理导入模板

GET /revenue/template/download

下载商户营收管理导入模板

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdgetInfo_6"></a>

## GET 获取商户营收管理详细信息

GET /revenue/detail

获取商户营收管理详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||商户营收管理ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddelete_2"></a>

## DELETE 删除商户营收管理接口

DELETE /revenue/delete/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||营收id|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_ContractRevenueAddDTO">ContractRevenueAddDTO</h2>

<a id="schemacontractrevenueadddto"></a>
<a id="schema_ContractRevenueAddDTO"></a>
<a id="tocScontractrevenueadddto"></a>
<a id="tocscontractrevenueadddto"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "revenueMonth": "string",
  "revenueAmount": 0,
  "reportDate": "2019-08-24T14:15:22Z",
  "attachment": "string",
  "isConfirm": true,
  "confirmBy": "string",
  "confirmByName": "string",
  "confirmTime": "2019-08-24T14:15:22Z",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractUnionId|string|false|none|contract_union_id|contract_union_id|
|revenueMonth|string|false|none|营收月|营收月|
|revenueAmount|number|false|none|营收金额|营收金额|
|reportDate|string(date-time)|false|none|提报日期|提报日期|
|attachment|string|false|none|附件|附件|
|isConfirm|boolean|false|none|是否审核 0-否,1-是|是否审核 0-否,1-是|
|confirmBy|string|false|none|审核人账号|审核人账号|
|confirmByName|string|false|none|审核人姓名|审核人姓名|
|confirmTime|string(date-time)|false|none|审核时间|审核时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_ContractRevenueQueryDTO">ContractRevenueQueryDTO</h2>

<a id="schemacontractrevenuequerydto"></a>
<a id="schema_ContractRevenueQueryDTO"></a>
<a id="tocScontractrevenuequerydto"></a>
<a id="tocscontractrevenuequerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "revenueMonth": "string",
  "revenueAmount": 0,
  "reportDate": "2019-08-24T14:15:22Z",
  "attachment": "string",
  "isConfirm": true,
  "confirmBy": "string",
  "confirmByName": "string",
  "confirmTime": "2019-08-24T14:15:22Z",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "contractNo": "string",
  "customerName": "string",
  "roomName": "string",
  "reportDateStart": "string",
  "reportDateEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractUnionId|string|false|none|contract_union_id|contract_union_id|
|revenueMonth|string|false|none|营收月|营收月|
|revenueAmount|number|false|none|营收金额|营收金额|
|reportDate|string(date-time)|false|none|提报日期|提报日期|
|attachment|string|false|none|附件|附件|
|isConfirm|boolean|false|none|是否审核 0-否,1-是|是否审核 0-否,1-是|
|confirmBy|string|false|none|审核人账号|审核人账号|
|confirmByName|string|false|none|审核人姓名|审核人姓名|
|confirmTime|string(date-time)|false|none|审核时间|审核时间|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractNo|string|false|none|合同号|合同号|
|customerName|string|false|none|承租人|承租人|
|roomName|string|false|none|租赁单元|租赁单元|
|reportDateStart|string|false|none|提报日期开始|提报日期开始|
|reportDateEnd|string|false|none|提报日期结束|提报日期结束|

<h2 id="tocS_ContractRevenueApproveDTO">ContractRevenueApproveDTO</h2>

<a id="schemacontractrevenueapprovedto"></a>
<a id="schema_ContractRevenueApproveDTO"></a>
<a id="tocScontractrevenueapprovedto"></a>
<a id="tocscontractrevenueapprovedto"></a>

```json
{
  "ids": [
    "string"
  ],
  "opinion": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ids|[string]|false|none|营收id列表|营收id列表|
|» 营收id列表|string|false|none|营收id列表|营收id列表|
|opinion|string|false|none|审核意见|审核意见|

