<template>
  <div class="test-permission-link">
    <a-card title="PermissionLink 组件测试">
      <a-space direction="vertical" size="large">
        <!-- 当前用户权限信息 -->
        <a-card title="当前用户权限" size="small">
          <div>
            <strong>权限列表：</strong>
            <a-tag v-for="permission in userPermissions" :key="permission" style="margin: 2px;">
              {{ permission }}
            </a-tag>
          </div>
        </a-card>

        <!-- 测试用例 -->
        <a-card title="测试用例" size="small">
          <a-space direction="vertical" size="medium">
            <!-- 测试1: 有权限的链接 -->
            <div class="test-case">
              <h4>测试1: 有权限的链接</h4>
              <PermissionLink 
                permissions="system:user:list" 
                @click="handleClick('有权限的链接')"
              >
                用户列表（system:user:list）
              </PermissionLink>
            </div>

            <!-- 测试2: 无权限的链接 -->
            <div class="test-case">
              <h4>测试2: 无权限的链接</h4>
              <PermissionLink 
                permissions="system:admin:delete" 
                @click="handleClick('无权限的链接')"
              >
                管理员删除（system:admin:delete）
              </PermissionLink>
            </div>

            <!-- 测试3: 权限数组（有其中一个权限） -->
            <div class="test-case">
              <h4>测试3: 权限数组（有其中一个权限）</h4>
              <PermissionLink 
                :permissions="['system:user:edit', 'system:admin:edit']" 
                @click="handleClick('权限数组-有权限')"
              >
                编辑功能（system:user:edit 或 system:admin:edit）
              </PermissionLink>
            </div>

            <!-- 测试4: 权限数组（都没有权限） -->
            <div class="test-case">
              <h4>测试4: 权限数组（都没有权限）</h4>
              <PermissionLink 
                :permissions="['system:admin:delete', 'system:root:access']" 
                @click="handleClick('权限数组-无权限')"
              >
                高级功能（system:admin:delete 或 system:root:access）
              </PermissionLink>
            </div>

            <!-- 测试5: 超级权限 -->
            <div class="test-case">
              <h4>测试5: 超级权限测试</h4>
                             <a-button @click="toggleSuperPermission" :type="hasSuperPermission ? 'primary' : 'secondary'">
                {{ hasSuperPermission ? '移除超级权限' : '添加超级权限' }}
              </a-button>
              <br />
              <PermissionLink 
                permissions="any:random:permission" 
                @click="handleClick('超级权限测试')"
                style="margin-top: 8px;"
              >
                任意权限测试（any:random:permission）
              </PermissionLink>
            </div>

            <!-- 测试6: 允许无权限点击 -->
            <div class="test-case">
              <h4>测试6: 允许无权限点击</h4>
              <PermissionLink 
                permissions="system:admin:delete" 
                :allow-click-without-permission="true"
                @click="handleClick('有权限点击')"
                @disabled-click="handleDisabledClick('无权限点击')"
              >
                删除功能（允许无权限点击）
              </PermissionLink>
            </div>

            <!-- 测试7: 传递额外属性 -->
            <div class="test-case">
              <h4>测试7: 传递额外属性</h4>
              <PermissionLink 
                permissions="system:user:list" 
                href="https://www.baidu.com"
                target="_blank"
                @click="handleClick('外部链接')"
              >
                外部链接（传递 href 和 target 属性）
              </PermissionLink>
            </div>
          </a-space>
        </a-card>

        <!-- 操作日志 -->
        <a-card title="操作日志" size="small">
          <div class="log-container">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <a-tag :color="log.type === 'success' ? 'green' : 'orange'">
                {{ log.time }}
              </a-tag>
              {{ log.message }}
            </div>
          </div>
        </a-card>
      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useUserStore } from '@/store';

const userStore = useUserStore();

// 用户权限
const userPermissions = computed(() => userStore.permissions);

// 是否有超级权限
const hasSuperPermission = computed(() => userPermissions.value.includes('*:*:*'));

// 操作日志
const logs = ref<Array<{ time: string; message: string; type: string }>>([]);

// 切换超级权限
const toggleSuperPermission = () => {
  if (hasSuperPermission.value) {
    // 移除超级权限
    userStore.permissions = userStore.permissions.filter(p => p !== '*:*:*');
    addLog('移除超级权限', 'info');
  } else {
    // 添加超级权限
    userStore.permissions = ['*:*:*', ...userStore.permissions];
    addLog('添加超级权限', 'success');
  }
};

// 处理点击事件
const handleClick = (source: string) => {
  addLog(`点击了: ${source}`, 'success');
};

// 处理无权限点击事件
const handleDisabledClick = (source: string) => {
  addLog(`无权限点击: ${source}`, 'info');
};

// 添加日志
const addLog = (message: string, type: string) => {
  const now = new Date();
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  logs.value.unshift({ time, message, type });
  
  // 保持最多20条日志
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20);
  }
};

// 初始化日志
addLog('页面加载完成', 'info');
</script>

<style scoped lang="less">
.test-permission-link {
  padding: 16px;
  
  .test-case {
    padding: 12px;
    border: 1px solid #e5e6ea;
    border-radius: 4px;
    background-color: #fafafa;
    
    h4 {
      margin: 0 0 8px 0;
      color: #1d2129;
    }
  }
  
  .log-container {
    max-height: 200px;
    overflow-y: auto;
    
    .log-item {
      margin-bottom: 4px;
      font-size: 12px;
      line-height: 1.5;
    }
  }
}
</style> 