<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form :model="formModel" auto-label-width label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="projectId" label="项目">
                                    <ProjectSelector v-model="formModel.projectId"
                                        @change="handleProjectSelectorChange" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="changeBuilding" label="楼栋名称">
                                    <a-input v-model="formModel.changeBuilding" placeholder="请输入楼栋名称" allow-clear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="approvalStatus" label="审批状态">
                                    <a-select v-model="formModel.approvalStatus" placeholder="请选择审批状态" allow-clear>
                                        <a-option value="0">草稿</a-option>
                                        <a-option value="1">审批中</a-option>
                                        <a-option value="2">审批通过</a-option>
                                        <a-option value="3">审批拒绝</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <!-- <a-divider style="height: 84px" direction="vertical" /> -->
                <a-col :flex="'86px'" style="text-align: right; margin-left: 16px;">
                    <a-space :size="18">
                        <a-button v-permission="['asset:change:list']" type="primary" @click="handleSearch">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="handleReset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin: 0 0 16px 0;" />

            <!-- 操作按钮区域 -->
            <a-row style="margin-bottom: 16px;text-align: right;">
                <a-col :span="24">
                    <a-space>
                        <a-button v-permission="['asset:change:add']" type="primary" @click="handleAdd">
                            <template #icon>
                                <icon-plus />
                            </template>
                            新增变更
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>

            <!-- 表格区域 -->
            <a-table :data="tableData" :columns="columns" :pagination="pagination" @page-change="onPageChange"
                :bordered="{ cell: true }" :loading="loading" :scroll="{ x: 1 }">
                <template #index="{ rowIndex }">
                    {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
                </template>
                <template #changeType="{ record }">
                    {{ getChangeTypeText(record.changeType) }}
                </template>
                <template #approvalStatus="{ record }">
                    <a-tag :color="getStatusColor(record.approvalStatus)">
                        {{ getStatusText(record.approvalStatus) }}
                    </a-tag>
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <template v-if="record.approvalStatus === '0'">
                            <a-button v-permission="['asset:change:edit']" type="text" size="mini" @click="handleEdit(record)">编辑</a-button>
                            <a-button v-permission="['asset:change:remove']" type="text" size="mini" status="danger"
                                @click="handleDelete(record)">删除</a-button>
                            <a-button v-permission="['asset:change:submit']" type="text" size="mini" @click="handleSubmit(record)">提交</a-button>
                        </template>
                        <template v-else>
                            <a-button v-permission="['asset:change:query']" type="text" size="mini" @click="handleDetail(record)">详情</a-button>
                        </template>
                    </a-space>
                </template>
            </a-table>
        </a-card>
        <chooseRoom ref="chooseRoomRef" @next="nextClick" />
        <changeForm ref="changeFormRef" v-model:visible="changeFormVisible" :project-id="selectedProjectId"
            :project-name="selectedProjectName" :selected-rooms="selectedRooms" :selected-room-data="selectedRoomData"
            :mode="formMode" :form-data="currentFormData" @submit="handleFormSubmit" @cancel="handleFormCancel"
            @add-asset="handleAddMoreAsset" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconSearch, IconRefresh, IconPlus } from '@arco-design/web-vue/es/icon'
import chooseRoom from './components/chooseRoom.vue'
import changeForm from './components/changeForm.vue'
import ProjectSelector from '@/components/projectSelector/index.vue'
import {
    getChangeList,
    getProjectList,
    deleteAssetChange,
    type AssetChangeQueryParams,
    type AssetChangeVo,
    type ProjectVo,
    submitOA
} from '@/api/asset/projectChange'

// 表单数据
const formModel = reactive<AssetChangeQueryParams>({
    projectId: '',
    changeBuilding: '',
    approvalStatus: '',
    pageNum: 1,
    pageSize: 10
})

// 项目选项
const projectOptions = ref<ProjectVo[]>([])

// 获取项目列表（用于ProjectSelector组件）
const loadProjectList = async () => {
    try {
        const response = await getProjectList({})

        // 处理不同的API响应格式
        if (response && response.data) {
            const projects = Array.isArray(response.data) ? response.data : []
            return {
                data: projects.map((item: any) => ({
                    id: item.id,
                    projectName: item.projectName || item.mdmName || item.name || ''
                }))
            }
        }

        return { data: [] }
    } catch (error) {
        console.error('获取项目列表失败', error)
        return { data: [] }
    }
}

const projectData = ref<any>()
// 处理项目选择变化（用于ProjectSelector组件）
const handleProjectSelectorChange = (projectId: string, project: any) => {
    projectData.value = project
    // 项目变化时重新查询数据
    handleSearch()
}

// 表格数据
const tableData = ref<AssetChangeVo[]>([])
const loading = ref(false)

// 表格列定义
const columns = [
    {
        title: '序号',
        slotName: 'index',
        width: 70,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '变更单编码',
        dataIndex: 'changeOrderCode',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '变更类型',
        dataIndex: 'changeType',
        slotName: 'changeType',
        width: 100,
        align: 'center'
    },
    {
        title: '项目名称',
        dataIndex: 'projectName',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '变更楼栋',
        dataIndex: 'changeBuilding',
        width: 140,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '变更房源数',
        dataIndex: 'changeRoomCount',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '变更原因',
        dataIndex: 'changeDescription',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '变更日期',
        dataIndex: 'changeDate',
        width: 140,
        align: 'center'
    },
    {
        title: '审批状态',
        dataIndex: 'approvalStatus',
        slotName: 'approvalStatus',
        width: 100,
        align: 'center'
    },
    {
        title: '审批通过时间',
        dataIndex: 'approvalTime',
        width: 140,
        align: 'center'
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 120,
        fixed: 'right',
        align: 'center'
    }
]

// 分页配置
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
    showTotal: true,
    showPageSize: true
})

// 表单相关
const chooseRoomRef = ref()
const changeFormVisible = ref(false)
const selectedProjectId = ref('')
const selectedProjectName = ref('')
const selectedRooms = ref<string[]>([])
const selectedRoomData = ref<any[]>([])
const formMode = ref<'add' | 'edit' | 'detail'>('add')
const currentFormData = ref<any>(null)
const changeFormRef = ref()

// 获取变更类型文本
const getChangeTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
        '1': '面积变更'
    }
    return typeMap[type] || type
}

// 获取状态文本
const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
        '0': '草稿',
        '1': '审批中',
        '2': '审批通过',
        '3': '审批拒绝'
    }
    return statusMap[status] || status
}

// 获取状态标签颜色
const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        '0': 'gray',
        '1': 'orange',
        '2': 'green',
        '3': 'red'
    }
    return colorMap[status] || 'blue'
}

// 页码变化
const onPageChange = (current: number) => {
    pagination.current = current
    formModel.pageNum = current
    fetchData()
}

// 查询
const handleSearch = () => {
    pagination.current = 1
    formModel.pageNum = 1
    fetchData()
}

// 重置
const handleReset = () => {
    formModel.projectId = ''
    formModel.changeBuilding = ''
    formModel.approvalStatus = ''
    handleSearch()
}

// 新增
const handleAdd = () => {
    if (!formModel.projectId) {
        Message.warning('请先选择项目')
        return
    }

    const selectedProject = projectOptions.value.find(p => p.id === formModel.projectId)
    selectedProjectId.value = formModel.projectId || ''
    selectedProjectName.value = selectedProject?.projectName || ''
    formMode.value = 'add'
    currentFormData.value = null
    chooseRoomRef.value?.show(formModel.projectId)
}

// 编辑
const handleEdit = (record: AssetChangeVo) => {
    console.log('编辑', record)

    try {
        // 设置项目信息
        selectedProjectId.value = record.projectId || ''
        const selectedProject = projectOptions.value.find(p => p.id === record.projectId)
        selectedProjectName.value = selectedProject?.projectName || ''

        // 打开详情弹窗
        changeFormVisible.value = true

        // 使用nextTick确保子组件挂载完成后再调用openDetail
        nextTick(() => {
            // 使用变更单ID，编辑模式可编辑
            const detailProps = {
                id: record.id,
                isEdit: true,
                projectId: record.projectId,
                projectName: selectedProjectName.value,
                readOnly: false // 可编辑模式
            }
            // 打开changeForm弹窗并传入详情ID
            changeFormRef.value?.openDetail(detailProps)
        })
    } catch (error) {
        console.error('获取资产变更详情失败:', error)
        Message.error('获取详情失败')
    }
}

// 删除
const handleDelete = (record: AssetChangeVo) => {
    Modal.confirm({
        title: '确认删除',
        content: '确定要删除这条资产变更记录吗？',
        onOk: async () => {
            try {
                await deleteAssetChange(record.id!)
                Message.success('删除成功')
                fetchData()
            } catch (error) {
                Message.error('删除失败')
            }
        }
    })
}

// 提交审批
const handleSubmit = (record: AssetChangeVo) => {
    Modal.confirm({
        title: '确认提交',
        content: '确定要提交这条资产变更记录进行审批吗？',
        onOk: async () => {
            try {
                const response = await submitOA(record.id)
                if (response && response.code === 200) {
                    Message.success('提交成功')
                    fetchData()
                } else {
                    Message.error('提交失败')
                }
            } catch (error) {
                Message.error('提交失败')
            }
        }
    })
}

// 详情
const handleDetail = (record: AssetChangeVo) => {
    console.log('查看详情', record)
    try {
        // 设置项目信息
        selectedProjectId.value = record.projectId || ''
        const selectedProject = projectOptions.value.find(p => p.id === record.projectId)
        selectedProjectName.value = selectedProject?.projectName || ''

        // 打开详情弹窗
        changeFormVisible.value = true

        nextTick(() => {
            // 使用变更单ID，详情模式为只读
            const detailProps = {
                id: record.id,
                isEdit: true,
                projectId: record.projectId,
                projectName: selectedProjectName.value,
                readOnly: true // 只读模式
            }

            // 打开changeForm弹窗并传入详情ID
            changeFormRef.value?.openDetail(detailProps)
        })
    } catch (error) {
        console.error('获取资产变更详情失败:', error)
        Message.error('获取详情失败')
    }
}

// 选择房源回调
const nextClick = (data: any) => {
    console.log('选择的房源数据:', data)

    if (data.roomIds) {
        // 如果changeForm已经打开，说明是在添加更多资产
        if (changeFormVisible.value) {
            // 合并新选择的房源到现有列表中，避免重复
            const existingRoomIds = selectedRooms.value
            const newRoomIds = data.roomIds.filter((id: string) => !existingRoomIds.includes(id))
            selectedRooms.value = [...existingRoomIds, ...newRoomIds]

            // 合并房源数据
            if (data.roomData) {
                const existingRoomData = selectedRoomData.value
                const newRoomData = data.roomData.filter((room: any) =>
                    !existingRoomData.some((existing: any) => existing.id === room.id)
                )
                selectedRoomData.value = [...existingRoomData, ...newRoomData]
            }
        } else {
            // 首次选择房源
            selectedRooms.value = data.roomIds
            if (data.roomData) {
                selectedRoomData.value = data.roomData
            }
        }
    }

    selectedProjectId.value = projectData.value.id
    selectedProjectName.value = projectData.value.projectName

    // 确保changeForm打开
    changeFormVisible.value = true
}

// 表单提交
const handleFormSubmit = () => {
    changeFormVisible.value = false
    fetchData()
}

// 表单取消
const handleFormCancel = () => {
    changeFormVisible.value = false
}

// 在changeForm中添加更多资产
const handleAddMoreAsset = () => {
    // 打开选择房源弹窗，传入当前项目ID
    chooseRoomRef.value?.show(selectedProjectId.value)
}

// 获取项目列表
const fetchProjectList = async () => {
    try {
        const response = await getProjectList({})
        if (response && response.rows) {
            const rows = response.rows;
            projectOptions.value = rows.map((item: any) => ({
                id: item.id,
                projectName: item.mdmName || item.name || ''
            }));
        }
    } catch (error) {
        console.error('获取项目列表失败:', error)
    }
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            ...formModel,
            pageNum: pagination.current,
            pageSize: pagination.pageSize
        }

        const response = await getChangeList(params)
        if (response && response.rows) {
            tableData.value = response.rows
            pagination.total = response.total || 0
        }
    } catch (error) {
        console.error('获取数据失败:', error)
        Message.error('获取数据失败')
    } finally {
        loading.value = false
    }
}

// 初始化
onMounted(() => {
    // fetchProjectList()
    // fetchData()
})
</script>

<style lang="less" scoped>
.container {
    padding: 0 16px 16px 16px;
}

.general-card {
    border-radius: 4px;

    :deep(.arco-card-body) {
        padding: 16px;
    }
}
</style>