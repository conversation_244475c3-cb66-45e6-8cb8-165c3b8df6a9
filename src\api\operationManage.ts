import http from './index'
import type { ExitProcessTypeDTO } from '@/types/exit'

const operationUrl = '/business-rent-admin'

// 进场管理API
export function getEntryList(data: any) {
  return http.post(`${operationUrl}/entry/list`, data)
}

export function batchAutoEntry(data: any) {
  return http.post(`${operationUrl}/entry/batchAuto`, data)
}

export function handleEntry(data: any) {
  return http.post(`${operationUrl}/entry/handle`, data)
}

// 出场管理API - 基础接口
export function getExitList(params: any) {
  return http.get(`${operationUrl}/exit/list`, params)
}

export function getExitDetail(id: string) {
  return http.get(`${operationUrl}/exit/detail?id=${id}`)
}

export function addExit(data: any) {
  return http.post(`${operationUrl}/exit`, data)
}

export function updateExit(data: any) {
  return http.put(`${operationUrl}/exit`, data)
}

export function deleteExit(ids: string[]) {
  return http.delete(`${operationUrl}/exit/delete`, ids)
}

// 出场管理API - 业务操作接口
export function saveExitSettlement(data: any) {
  return http.post(`${operationUrl}/exit/save`, data)
}

export function saveExitRoom(data: any) {
  return http.post(`${operationUrl}/exit/saveRoom`, data)
}

export function batchUpdateExitRoom(data: any) {
  return http.post(`${operationUrl}/exit/batchUpdateRoom`, data)
}

export function uploadExitSignature(data: any) {
  return http.post(`${operationUrl}/exit/uploadSignature`, data)
}

export function copyExitPropertyUrl(data: any) {
  return http.post(`${operationUrl}/exit/copyUrl?exitId=${data}`, data)
}

export function chooseExitProcessType(data: ExitProcessTypeDTO) {
  return http.post(`${operationUrl}/exit/chooseProcessType`, data)
}

export function cancelExit(data: any) {
  return http.post(`${operationUrl}/exit/cancel`, data)
}

export function printExit(exitId: string) {
  return http.get(`${operationUrl}/exit/print?exitId=${exitId}`)
}

export function exportExitList(params: any) {
  return http.post(`${operationUrl}/exit/export`, {}, { params })
}

// 获取项目列表
export function getProjectList() {
  return http.get(`${operationUrl}/project/list`)
} 