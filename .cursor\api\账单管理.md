---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁后台/账单

<a id="opIdedit_10"></a>

## PUT 修改账单

PUT /cost

修改账单

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "chargeType": 0,
  "bizId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerName": "string",
  "costType": 0,
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CostAddDTO](#schemacostadddto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd_9"></a>

## POST 新增账单

POST /cost

新增账单

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "chargeType": 0,
  "bizId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerName": "string",
  "costType": 0,
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CostAddDTO](#schemacostadddto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdsummary"></a>

## POST 账单统计

POST /cost/summary

账单统计

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "chargeType": 0,
  "bizId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerName": "string",
  "costType": 0,
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "contractType": 0,
  "subStatus": 0,
  "roomName": "string",
  "statusList": "string",
  "receivableDateStart": "string",
  "receivableDateEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CostQueryDTO](#schemacostquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"overdueCount":0,"todayCount":0,"sevenDaysCount":0,"pendingConfirmCount":0,"confirmedCount":0}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[CostSummaryVo](#schemacostsummaryvo)|

<a id="opIdsaveRecord"></a>

## POST 保存记账

POST /cost/saveRecord

保存记账

> Body 请求参数

```json
{
  "costId": "string",
  "flowRelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "costId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "acctAmount": 0,
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CostSaveRecordDTO](#schemacostsaverecorddto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|boolean|

<a id="opIdlist_3"></a>

## POST 查询账单列表

POST /cost/list

查询账单列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "chargeType": 0,
  "bizId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerName": "string",
  "costType": 0,
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "contractType": 0,
  "subStatus": 0,
  "roomName": "string",
  "statusList": "string",
  "receivableDateStart": "string",
  "receivableDateEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[CostQueryDTO](#schemacostquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdlist_16"></a>

## GET 查询账单列表

GET /cost/list

查询账单列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|chargeType|query|integer(int32)| 否 | 收费类别: 0-定单, 1-合同|收费类别: 0-定单, 1-合同|
|bizId|query|string| 否 | 业务id, 定单类别对应定单id, 合同类别对应合同id|业务id, 定单类别对应定单id, 合同类别对应合同id|
|bizNo|query|string| 否 | 业务单号|业务单号|
|customerId|query|string| 否 | 承租人id|承租人id|
|customerName|query|string| 否 | 承租人名称|承租人名称|
|costType|query|integer(int32)| 否 | 账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|period|query|integer(int32)| 否 | 账单期数|账单期数|
|subjectId|query|string| 否 | 收款用途id|收款用途id|
|subjectName|query|string| 否 | 收款用途名称|收款用途名称|
|startDate|query|string(date-time)| 否 | 开始日期|开始日期|
|endDate|query|string(date-time)| 否 | 结束日期|结束日期|
|receivableDate|query|string(date-time)| 否 | 应收日期|应收日期|
|status|query|integer(int32)| 否 | 账单状态: 0-待收、1-待付、2-已收、3-已付|账单状态: 0-待收、1-待付、2-已收、3-已付|
|canPay|query|boolean| 否 | 是否可收/付 0-否,1-是|是否可收/付 0-否,1-是|
|totalAmount|query|number| 否 | 账单总额|账单总额|
|discountAmount|query|number| 否 | 优惠金额|优惠金额|
|actualReceivable|query|number| 否 | 实际应收金额|实际应收金额|
|receivedAmount|query|number| 否 | 已收金额|已收金额|
|carryoverAmount|query|number| 否 | 结转金额|结转金额|
|confirmStatus|query|integer(int32)| 否 | 确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdexport_9"></a>

## POST 导出询账单列表

POST /cost/export

导出询账单列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|projectId|query|string| 否 | 项目id|项目id|
|chargeType|query|integer(int32)| 否 | 收费类别: 0-定单, 1-合同|收费类别: 0-定单, 1-合同|
|bizId|query|string| 否 | 业务id, 定单类别对应定单id, 合同类别对应合同id|业务id, 定单类别对应定单id, 合同类别对应合同id|
|bizNo|query|string| 否 | 业务单号|业务单号|
|customerId|query|string| 否 | 承租人id|承租人id|
|customerName|query|string| 否 | 承租人名称|承租人名称|
|costType|query|integer(int32)| 否 | 账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|period|query|integer(int32)| 否 | 账单期数|账单期数|
|subjectId|query|string| 否 | 收款用途id|收款用途id|
|subjectName|query|string| 否 | 收款用途名称|收款用途名称|
|startDate|query|string(date-time)| 否 | 开始日期|开始日期|
|endDate|query|string(date-time)| 否 | 结束日期|结束日期|
|receivableDate|query|string(date-time)| 否 | 应收日期|应收日期|
|status|query|integer(int32)| 否 | 账单状态: 0-待收、1-待付、2-已收、3-已付|账单状态: 0-待收、1-待付、2-已收、3-已付|
|canPay|query|boolean| 否 | 是否可收/付 0-否,1-是|是否可收/付 0-否,1-是|
|totalAmount|query|number| 否 | 账单总额|账单总额|
|discountAmount|query|number| 否 | 优惠金额|优惠金额|
|actualReceivable|query|number| 否 | 实际应收金额|实际应收金额|
|receivedAmount|query|number| 否 | 已收金额|已收金额|
|carryoverAmount|query|number| 否 | 结转金额|结转金额|
|confirmStatus|query|integer(int32)| 否 | 确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|isDel|query|boolean| 否 | 0-否,1-是|0-否,1-是|
|contractType|query|integer(int32)| 否 | 合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|subStatus|query|integer(int32)| 否 | 子状态|子状态(0-逾期,1-今日,2-近7天,3-待确认,4-已确认)|
|roomName|query|string| 否 | 房间名称|房间名称|
|statusList|query|string| 否 | 账单状态（多选拼接）|账单状态（多选拼接）|
|receivableDateStart|query|string| 否 | 应收日期开始|应收日期开始|
|receivableDateEnd|query|string| 否 | 应收日期结束|应收日期结束|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdconfirmRecord"></a>

## POST 确认记账

POST /cost/confirmRecord

确认记账

> Body 请求参数

```json
{
  "costId": "string",
  "isOneKeyConfirm": true,
  "flowRelId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|body|body|[CostConfirmRecordDTO](#schemacostconfirmrecorddto)| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|boolean|

<a id="opIdcancelRecord"></a>

## POST 取消记账

POST /cost/cancelRecord

取消记账

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|账单流水关联id|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|boolean|

<a id="opIdqrcode"></a>

## GET 查看账单码

GET /cost/qrcode

查看账单码

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|账单ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|string|

<a id="opIddetail_2"></a>

## GET 查看账单流水详情

GET /cost/detail

查看账单流水详情

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|账单ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"cost":{"id":"string","projectId":"string","projectName":"string","chargeType":0,"bizId":"string","bizNo":"string","customerId":"string","customerName":"string","costType":0,"period":0,"subjectId":"string","subjectName":"string","startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","receivableDate":"2019-08-24T14:15:22Z","status":0,"canPay":true,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"carryoverAmount":0,"confirmStatus":0,"createByName":"string","updateByName":"string","isDel":true,"contractType":0,"roomName":"string","contractStatus":0,"contractStatusTwo":0},"flowRelList":[{"id":"string","costId":"string","flowId":"string","flowNo":"string","type":0,"confirmStatus":0,"confirmTime":"2019-08-24T14:15:22Z","confirmUserId":"string","confirmUserName":"string","payAmount":0,"acctAmount":0,"createByName":"string","updateByName":"string","isDel":true,"projectId":"string","projectName":"string","entryTime":"2019-08-24T14:15:22Z","payType":0,"payMethod":0,"orderNo":"string","usedAmount":0,"payerName":"string","target":"string","merchant":"string"}],"flowLogList":[{"id":"string","costId":"string","flowId":"string","flowNo":"string","type":0,"carryoverId":"string","carryoverNo":"string","amount":0,"createByName":"string","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|[CostDetailVo](#schemacostdetailvo)|

<a id="opIdgetInfo_11"></a>

## GET 获取账单详细信息

GET /cost/getInfo

获取账单详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|账单ID|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_9"></a>

## DELETE 删除账单

DELETE /cost/delete

删除账单

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|账单ID列表|query|array[string]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CostFlowRelAddDTO">CostFlowRelAddDTO</h2>

<a id="schemacostflowreladddto"></a>
<a id="schema_CostFlowRelAddDTO"></a>
<a id="tocScostflowreladddto"></a>
<a id="tocscostflowreladddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "costId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "confirmStatus": 0,
  "confirmTime": "2019-08-24T14:15:22Z",
  "confirmUserId": "string",
  "confirmUserName": "string",
  "payAmount": 0,
  "acctAmount": 0,
  "isDel": true
}

```

记账记录列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|costId|string|false|none|账单id|账单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|账单类型：1-收款 2-转入 3-转出 4-退款|账单类型：1-收款 2-转入 3-转出 4-退款|
|confirmStatus|integer(int32)|false|none|确认状态: 0-未确认、1-自动确认、2-手动确认|确认状态: 0-未确认、1-自动确认、2-手动确认|
|confirmTime|string(date-time)|false|none|确认时间|确认时间|
|confirmUserId|string|false|none|确认人id|确认人id|
|confirmUserName|string|false|none|确认人姓名|确认人姓名|
|payAmount|number|false|none|支付金额|支付金额|
|acctAmount|number|false|none|本次记账金额|本次记账金额|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_CostAddDTO">CostAddDTO</h2>

<a id="schemacostadddto"></a>
<a id="schema_CostAddDTO"></a>
<a id="tocScostadddto"></a>
<a id="tocscostadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "projectId": "string",
  "chargeType": 0,
  "bizId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerName": "string",
  "costType": 0,
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|chargeType|integer(int32)|false|none|收费类别: 0-定单, 1-合同|收费类别: 0-定单, 1-合同|
|bizId|string|false|none|业务id, 定单类别对应定单id, 合同类别对应合同id|业务id, 定单类别对应定单id, 合同类别对应合同id|
|bizNo|string|false|none|业务单号|业务单号|
|customerId|string|false|none|承租人id|承租人id|
|customerName|string|false|none|承租人名称|承租人名称|
|costType|integer(int32)|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|period|integer(int32)|false|none|账单期数|账单期数|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途名称|收款用途名称|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|status|integer(int32)|false|none|账单状态: 0-待收、1-待付、2-已收、3-已付|账单状态: 0-待收、1-待付、2-已收、3-已付|
|canPay|boolean|false|none|是否可收/付 0-否,1-是|是否可收/付 0-否,1-是|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|carryoverAmount|number|false|none|结转金额|结转金额|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_CostQueryDTO">CostQueryDTO</h2>

<a id="schemacostquerydto"></a>
<a id="schema_CostQueryDTO"></a>
<a id="tocScostquerydto"></a>
<a id="tocscostquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "chargeType": 0,
  "bizId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerName": "string",
  "costType": 0,
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "contractType": 0,
  "subStatus": 0,
  "roomName": "string",
  "statusList": "string",
  "receivableDateStart": "string",
  "receivableDateEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|chargeType|integer(int32)|false|none|收费类别: 0-定单, 1-合同|收费类别: 0-定单, 1-合同|
|bizId|string|false|none|业务id, 定单类别对应定单id, 合同类别对应合同id|业务id, 定单类别对应定单id, 合同类别对应合同id|
|bizNo|string|false|none|业务单号|业务单号|
|customerId|string|false|none|承租人id|承租人id|
|customerName|string|false|none|承租人名称|承租人名称|
|costType|integer(int32)|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|period|integer(int32)|false|none|账单期数|账单期数|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途名称|收款用途名称|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|status|integer(int32)|false|none|账单状态: 0-待收、1-待付、2-已收、3-已付|账单状态: 0-待收、1-待付、2-已收、3-已付|
|canPay|boolean|false|none|是否可收/付 0-否,1-是|是否可收/付 0-否,1-是|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|carryoverAmount|number|false|none|结转金额|结转金额|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|subStatus|integer(int32)|false|none|子状态|子状态(0-逾期,1-今日,2-近7天,3-待确认,4-已确认)|
|roomName|string|false|none|房间名称|房间名称|
|statusList|string|false|none|账单状态（多选拼接）|账单状态（多选拼接）|
|receivableDateStart|string|false|none|应收日期开始|应收日期开始|
|receivableDateEnd|string|false|none|应收日期结束|应收日期结束|

<h2 id="tocS_CostSummaryVo">CostSummaryVo</h2>

<a id="schemacostsummaryvo"></a>
<a id="schema_CostSummaryVo"></a>
<a id="tocScostsummaryvo"></a>
<a id="tocscostsummaryvo"></a>

```json
{
  "overdueCount": 0,
  "todayCount": 0,
  "sevenDaysCount": 0,
  "pendingConfirmCount": 0,
  "confirmedCount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|overdueCount|integer(int64)|false|none|逾期数|逾期数|
|todayCount|integer(int64)|false|none|今日数|今日数|
|sevenDaysCount|integer(int64)|false|none|近七天数|近七天数|
|pendingConfirmCount|integer(int64)|false|none|待确认数|待确认数|
|confirmedCount|integer(int64)|false|none|已确认数|已确认数|

<h2 id="tocS_CostConfirmRecordDTO">CostConfirmRecordDTO</h2>

<a id="schemacostconfirmrecorddto"></a>
<a id="schema_CostConfirmRecordDTO"></a>
<a id="tocScostconfirmrecorddto"></a>
<a id="tocscostconfirmrecorddto"></a>

```json
{
  "costId": "string",
  "isOneKeyConfirm": true,
  "flowRelId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|costId|string|true|none|账单id|账单id|
|isOneKeyConfirm|boolean|true|none|是否一键确认|是否一键确认|
|flowRelId|string|false|none|记账记录id|记账记录id（非一键确认时必填）|

<h2 id="tocS_CostDetailVo">CostDetailVo</h2>

<a id="schemacostdetailvo"></a>
<a id="schema_CostDetailVo"></a>
<a id="tocScostdetailvo"></a>
<a id="tocscostdetailvo"></a>

```json
{
  "cost": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "chargeType": 0,
    "bizId": "string",
    "bizNo": "string",
    "customerId": "string",
    "customerName": "string",
    "costType": 0,
    "period": 0,
    "subjectId": "string",
    "subjectName": "string",
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "receivableDate": "2019-08-24T14:15:22Z",
    "status": 0,
    "canPay": true,
    "totalAmount": 0,
    "discountAmount": 0,
    "actualReceivable": 0,
    "receivedAmount": 0,
    "carryoverAmount": 0,
    "confirmStatus": 0,
    "createByName": "string",
    "updateByName": "string",
    "isDel": true,
    "contractType": 0,
    "roomName": "string",
    "contractStatus": 0,
    "contractStatusTwo": 0
  },
  "flowRelList": [
    {
      "id": "string",
      "costId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "acctAmount": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "projectId": "string",
      "projectName": "string",
      "entryTime": "2019-08-24T14:15:22Z",
      "payType": 0,
      "payMethod": 0,
      "orderNo": "string",
      "usedAmount": 0,
      "payerName": "string",
      "target": "string",
      "merchant": "string"
    }
  ],
  "flowLogList": [
    {
      "id": "string",
      "costId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "carryoverId": "string",
      "carryoverNo": "string",
      "amount": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|cost|[CostVo](#schemacostvo)|false|none||账单信息|
|flowRelList|[[CostFlowRelVo](#schemacostflowrelvo)]|false|none|账单流水关系列表|账单流水关系列表|
|flowLogList|[[CostFlowLogVo](#schemacostflowlogvo)]|false|none|账单流水记录列表|账单流水记录列表|

<h2 id="tocS_CostFlowLogVo">CostFlowLogVo</h2>

<a id="schemacostflowlogvo"></a>
<a id="schema_CostFlowLogVo"></a>
<a id="tocScostflowlogvo"></a>
<a id="tocscostflowlogvo"></a>

```json
{
  "id": "string",
  "costId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "carryoverId": "string",
  "carryoverNo": "string",
  "amount": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

账单流水记录列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|costId|string|false|none|账单id|账单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转|类型：0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转|
|carryoverId|string|false|none|结转单id|结转单id|
|carryoverNo|string|false|none|结转单号|结转单号|
|amount|number|false|none|记账金额|记账金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_CostFlowRelVo">CostFlowRelVo</h2>

<a id="schemacostflowrelvo"></a>
<a id="schema_CostFlowRelVo"></a>
<a id="tocScostflowrelvo"></a>
<a id="tocscostflowrelvo"></a>

```json
{
  "id": "string",
  "costId": "string",
  "flowId": "string",
  "flowNo": "string",
  "type": 0,
  "confirmStatus": 0,
  "confirmTime": "2019-08-24T14:15:22Z",
  "confirmUserId": "string",
  "confirmUserName": "string",
  "payAmount": 0,
  "acctAmount": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "projectId": "string",
  "projectName": "string",
  "entryTime": "2019-08-24T14:15:22Z",
  "payType": 0,
  "payMethod": 0,
  "orderNo": "string",
  "usedAmount": 0,
  "payerName": "string",
  "target": "string",
  "merchant": "string"
}

```

账单流水关系列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|costId|string|false|none|账单id|账单id|
|flowId|string|false|none|流水id|流水id|
|flowNo|string|false|none|流水单号|流水单号|
|type|integer(int32)|false|none|账单类型：1-收款 2-转入 3-转出 4-退款|账单类型：1-收款 2-转入 3-转出 4-退款|
|confirmStatus|integer(int32)|false|none|确认状态: 0-未确认、1-自动确认、2-手动确认|确认状态: 0-未确认、1-自动确认、2-手动确认|
|confirmTime|string(date-time)|false|none|确认时间|确认时间|
|confirmUserId|string|false|none|确认人id|确认人id|
|confirmUserName|string|false|none|确认人姓名|确认人姓名|
|payAmount|number|false|none|支付金额|支付金额|
|acctAmount|number|false|none|本次记账金额|本次记账金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|entryTime|string(date-time)|false|none|入账时间|入账时间|
|payType|integer(int32)|false|none|支付类型|支付类型|
|payMethod|integer(int32)|false|none|支付方式|支付方式|
|orderNo|string|false|none|订单号|订单号|
|usedAmount|number|false|none|已使用金额|已使用金额|
|payerName|string|false|none|支付人姓名|支付人姓名|
|target|string|false|none|支付对象|支付对象|
|merchant|string|false|none|收款商户|收款商户|

<h2 id="tocS_CostVo">CostVo</h2>

<a id="schemacostvo"></a>
<a id="schema_CostVo"></a>
<a id="tocScostvo"></a>
<a id="tocscostvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "chargeType": 0,
  "bizId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerName": "string",
  "costType": 0,
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractType": 0,
  "roomName": "string",
  "contractStatus": 0,
  "contractStatusTwo": 0
}

```

账单信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|chargeType|integer(int32)|false|none|收费类别: 0-定单, 1-合同|收费类别: 0-定单, 1-合同|
|bizId|string|false|none|业务id, 定单类别对应定单id, 合同类别对应合同id|业务id, 定单类别对应定单id, 合同类别对应合同id|
|bizNo|string|false|none|业务单号|业务单号|
|customerId|string|false|none|承租人id|承租人id|
|customerName|string|false|none|承租人名称|承租人名称|
|costType|integer(int32)|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|period|integer(int32)|false|none|账单期数|账单期数|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途名称|收款用途名称|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|status|integer(int32)|false|none|账单状态: 0-待收、1-待付、2-已收、3-已付|账单状态: 0-待收、1-待付、2-已收、3-已付|
|canPay|boolean|false|none|是否可收/付 0-否,1-是|是否可收/付 0-否,1-是|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|carryoverAmount|number|false|none|结转金额|结转金额|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|roomName|string|false|none|租赁资源|租赁资源|
|contractStatus|integer(int32)|false|none|合同一级状态|合同一级状态|
|contractStatusTwo|integer(int32)|false|none|合同二级状态|合同二级状态|

<h2 id="tocS_CostSaveRecordDTO">CostSaveRecordDTO</h2>

<a id="schemacostsaverecorddto"></a>
<a id="schema_CostSaveRecordDTO"></a>
<a id="tocScostsaverecorddto"></a>
<a id="tocscostsaverecorddto"></a>

```json
{
  "costId": "string",
  "flowRelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "costId": "string",
      "flowId": "string",
      "flowNo": "string",
      "type": 0,
      "confirmStatus": 0,
      "confirmTime": "2019-08-24T14:15:22Z",
      "confirmUserId": "string",
      "confirmUserName": "string",
      "payAmount": 0,
      "acctAmount": 0,
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|costId|string|true|none|账单id|账单id|
|flowRelList|[[CostFlowRelAddDTO](#schemacostflowreladddto)]|true|none|记账记录列表|记账记录列表|

