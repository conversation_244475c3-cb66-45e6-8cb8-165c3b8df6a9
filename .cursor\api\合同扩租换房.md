---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/合同变更api

<a id="opIdsave_7"></a>

## POST 合同变更保存

POST /contractChange/save

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeType": 0,
  "approveStatus": 0,
  "processId": "string",
  "remark": "string",
  "attachment": "string",
  "signAttachments": "string",
  "isDel": true,
  "isSubmit": true,
  "changeRooms": [
    {
      "id": "string",
      "changeId": "string",
      "type": 0,
      "isChecked": true,
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "bondPriceType": 0,
      "bondPrice": 0,
      "isDel": true
    }
  ],
  "changeCosts": [
    {
      "id": "string",
      "changeId": "string",
      "costType": 0,
      "type": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0,
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "terminateReceivable": 0,
      "refundAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "isDel": true,
      "costId": "string"
    }
  ],
  "costSnapshot": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractChangeAddDTO](#schemacontractchangeadddto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdlist_10"></a>

## POST 合同变更列表查询

POST /contractChange/list

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeType": 0,
  "approveStatus": 0,
  "processId": "string",
  "remark": "string",
  "attachment": "string",
  "costSnapshot": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractChangeQueryDTO](#schemacontractchangequerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[TableDataInfo](#schematabledatainfo)|

<a id="opIdgenerateCost"></a>

## POST 生成房间款项

POST /contractChange/generateCost

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeType": 0,
  "approveStatus": 0,
  "processId": "string",
  "remark": "string",
  "attachment": "string",
  "signAttachments": "string",
  "isDel": true,
  "isSubmit": true,
  "changeRooms": [
    {
      "id": "string",
      "changeId": "string",
      "type": 0,
      "isChecked": true,
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "bondPriceType": 0,
      "bondPrice": 0,
      "isDel": true
    }
  ],
  "changeCosts": [
    {
      "id": "string",
      "changeId": "string",
      "costType": 0,
      "type": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0,
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "terminateReceivable": 0,
      "refundAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "isDel": true,
      "costId": "string"
    }
  ],
  "costSnapshot": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractChangeAddDTO](#schemacontractchangeadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"changeCosts":[{"id":"string","changeId":"string","costType":0,"type":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","contractStartDate":"2019-08-24T14:15:22Z","rentTicketPeriod":0,"period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"terminateReceivable":0,"refundAmount":0,"isRevenue":true,"isDiscount":true,"costId":"string","createByName":"string","updateByName":"string","isDel":true}],"oldCosts":[{"id":"string","contractId":"string","costType":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"isRevenue":true,"isDiscount":true,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","createByName":"string","updateByName":"string","isDel":true,"contractStartDate":"2019-08-24T14:15:22Z","rentTicketPeriod":0}],"newCosts":[{"id":"string","contractId":"string","costType":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"isRevenue":true,"isDiscount":true,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","createByName":"string","updateByName":"string","isDel":true,"contractStartDate":"2019-08-24T14:15:22Z","rentTicketPeriod":0}],"transferCosts":[{"id":"string","contractId":"string","costType":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"isRevenue":true,"isDiscount":true,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","createByName":"string","updateByName":"string","isDel":true,"contractStartDate":"2019-08-24T14:15:22Z","rentTicketPeriod":0}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[ContractChangeCostGenerateVo](#schemacontractchangecostgeneratevo)|

<a id="opIddelete"></a>

## POST 合同变更删除

POST /contractChange/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIdapprove_3"></a>

## POST 合同变更审批回调

POST /contractChange/approve

> Body 请求参数

```json
{
  "processId": "string",
  "state": "string",
  "act": "string",
  "nodeName": "string",
  "data": "string",
  "stateCode": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[OaNotifyDto](#schemaoanotifydto)| 否 |none|

> 返回示例

> 200 Response

```
true
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|boolean|

<a id="opIddetail_8"></a>

## GET 合同变更详情查询

GET /contractChange/detail

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"contractChange":{"id":"string","projectId":"string","contractId":"string","contractUnionId":"string","changeDate":"2019-08-24T14:15:22Z","changeType":0,"approveStatus":0,"processId":"string","remark":"string","attachment":"string","signAttachments":"string","costSnapshot":"string","createByName":"string","updateByName":"string","isDel":true},"changeRooms":[{"id":"string","changeId":"string","type":0,"isChecked":true,"roomId":"string","roomName":"string","buildingName":"string","floorName":"string","parcelName":"string","stageName":"string","area":0,"standardUnitPrice":0,"bottomPrice":0,"priceUnit":0,"discount":0,"signedUnitPrice":0,"signedMonthlyPrice":0,"bondPriceType":0,"bondPrice":0,"createByName":"string","updateByName":"string","isDel":true}],"changeCosts":[{"id":"string","changeId":"string","costType":0,"type":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","contractStartDate":"2019-08-24T14:15:22Z","rentTicketPeriod":0,"period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"terminateReceivable":0,"refundAmount":0,"isRevenue":true,"isDiscount":true,"costId":"string","createByName":"string","updateByName":"string","isDel":true}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[ContractChangeDetailVo](#schemacontractchangedetailvo)|

# 数据模型

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_OaNotifyDto">OaNotifyDto</h2>

<a id="schemaoanotifydto"></a>
<a id="schema_OaNotifyDto"></a>
<a id="tocSoanotifydto"></a>
<a id="tocsoanotifydto"></a>

```json
{
  "processId": "string",
  "state": "string",
  "act": "string",
  "nodeName": "string",
  "data": "string",
  "stateCode": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|processId|string|false|none||none|
|state|string|false|none||none|
|act|string|false|none||none|
|nodeName|string|false|none||none|
|data|string|false|none||none|
|stateCode|integer(int32)|false|none||none|

<h2 id="tocS_ContractCostVo">ContractCostVo</h2>

<a id="schemacontractcostvo"></a>
<a id="schema_ContractCostVo"></a>
<a id="tocScontractcostvo"></a>
<a id="tocscontractcostvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "period": 0,
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "unitPrice": 0,
  "priceUnit": 0,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "isRevenue": true,
  "isDiscount": true,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractStartDate": "2019-08-24T14:15:22Z",
  "rentTicketPeriod": 0
}

```

合同应收列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|period|integer(int32)|false|none|账单期数|账单期数|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|area|number|false|none|商铺面积|商铺面积|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途|收款用途|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|unitPrice|number|false|none|单价|单价|
|priceUnit|integer(int32)|false|none|单价单位|单价单位|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|isRevenue|boolean|false|none|是否营收提成,0-否,1-是|是否营收提成,0-否,1-是|
|isDiscount|boolean|false|none|是否优惠,0-否,1-是|是否优惠,0-否,1-是|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractStartDate|string(date-time)|false|none|合同开始日期, 用来推理租赁月|合同开始日期, 用来推理租赁月|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|

<h2 id="tocS_ContractChangeAddDTO">ContractChangeAddDTO</h2>

<a id="schemacontractchangeadddto"></a>
<a id="schema_ContractChangeAddDTO"></a>
<a id="tocScontractchangeadddto"></a>
<a id="tocscontractchangeadddto"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeType": 0,
  "approveStatus": 0,
  "processId": "string",
  "remark": "string",
  "attachment": "string",
  "signAttachments": "string",
  "isDel": true,
  "isSubmit": true,
  "changeRooms": [
    {
      "id": "string",
      "changeId": "string",
      "type": 0,
      "isChecked": true,
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "bondPriceType": 0,
      "bondPrice": 0,
      "isDel": true
    }
  ],
  "changeCosts": [
    {
      "id": "string",
      "changeId": "string",
      "costType": 0,
      "type": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0,
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "terminateReceivable": 0,
      "refundAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "isDel": true,
      "costId": "string"
    }
  ],
  "costSnapshot": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractUnionId|string|false|none|合同联合id|合同联合id|
|changeDate|string(date-time)|false|none|变更日期|变更日期|
|changeType|integer(int32)|false|none|变更类型: 1-扩租, 2-换房|变更类型: 1-扩租, 2-换房|
|approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|processId|string|false|none|流程id|流程id|
|remark|string|false|none|备注|备注|
|attachment|string|false|none|附件|附件|
|signAttachments|string|false|none|签署附件|签署附件|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|isSubmit|boolean|false|none|是否提交,0-否,1-是|是否提交,0-否,1-是|
|changeRooms|[[ContractChangeRoomAddDTO](#schemacontractchangeroomadddto)]|false|none|变更房源列表|变更房源列表|
|changeCosts|[[ContractChangeCostAddDTO](#schemacontractchangecostadddto)]|false|none|变更费用列表|变更费用列表|
|costSnapshot|string|false|none|账单信息快照|用于记录当时的应收与实收, 包含变更前、变更后、结转账单|

<h2 id="tocS_ContractChangeCostAddDTO">ContractChangeCostAddDTO</h2>

<a id="schemacontractchangecostadddto"></a>
<a id="schema_ContractChangeCostAddDTO"></a>
<a id="tocScontractchangecostadddto"></a>
<a id="tocscontractchangecostadddto"></a>

```json
{
  "id": "string",
  "changeId": "string",
  "costType": 0,
  "type": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractStartDate": "2019-08-24T14:15:22Z",
  "rentTicketPeriod": 0,
  "period": 0,
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "unitPrice": 0,
  "priceUnit": 0,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "terminateReceivable": 0,
  "refundAmount": 0,
  "isRevenue": true,
  "isDiscount": true,
  "isDel": true,
  "costId": "string"
}

```

变更费用列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|changeId|string|false|none|合同变更id|合同变更id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|type|integer(int32)|false|none|类型,1-新增账单, 2-退款账单|类型,1-新增账单, 2-退款账单|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|contractStartDate|string(date-time)|false|none|合同开始日期, 用来推理租赁月|合同开始日期, 用来推理租赁月|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|period|integer(int32)|false|none|账单期数|账单期数|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|area|number|false|none|商铺面积|商铺面积|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途|收款用途|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|unitPrice|number|false|none|单价|单价|
|priceUnit|integer(int32)|false|none|单价单位|单价单位|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|terminateReceivable|number|false|none|截止换房日应收（元）|截止换房日应收（元）|
|refundAmount|number|false|none|预计退款金额（元）|预计退款金额（元）|
|isRevenue|boolean|false|none|是否营收提成,0-否,1-是|是否营收提成,0-否,1-是|
|isDiscount|boolean|false|none|是否优惠,0-否,1-是|是否优惠,0-否,1-是|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|costId|string|false|none|t_cost表id|t_cost表id|

<h2 id="tocS_ContractChangeRoomAddDTO">ContractChangeRoomAddDTO</h2>

<a id="schemacontractchangeroomadddto"></a>
<a id="schema_ContractChangeRoomAddDTO"></a>
<a id="tocScontractchangeroomadddto"></a>
<a id="tocscontractchangeroomadddto"></a>

```json
{
  "id": "string",
  "changeId": "string",
  "type": 0,
  "isChecked": true,
  "roomId": "string",
  "roomName": "string",
  "buildingName": "string",
  "floorName": "string",
  "parcelName": "string",
  "stageName": "string",
  "area": 0,
  "standardUnitPrice": 0,
  "bottomPrice": 0,
  "priceUnit": 0,
  "discount": 0,
  "signedUnitPrice": 0,
  "signedMonthlyPrice": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "isDel": true
}

```

变更房源列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|changeId|string|false|none|合同变更id|合同变更id|
|type|integer(int32)|false|none|类型: 1-新加, 2-去除|类型: 1-新加, 2-去除|
|isChecked|boolean|false|none|是否选中,0-否,1-是; type=2且为true代表选中要被换掉的房间|是否选中,0-否,1-是; type=2且为true代表选中要被换掉的房间|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房间名称|房间名称|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorName|string|false|none|楼层名称|楼层名称|
|parcelName|string|false|none|地块名称|地块名称|
|stageName|string|false|none|分期名称|分期名称|
|area|number|false|none|面积（㎡）|面积（㎡）|
|standardUnitPrice|number|false|none|标准租金（单价）|标准租金（单价）|
|bottomPrice|number|false|none|底价|底价|
|priceUnit|integer(int32)|false|none|单价单位, 1-元/平米/月, 2-元/月, 3-元/日|单价单位, 1-元/平米/月, 2-元/月, 3-元/日|
|discount|number|false|none|折扣|折扣|
|signedUnitPrice|number|false|none|签约单价|签约单价|
|signedMonthlyPrice|number|false|none|签约月总价（元/月）|签约月总价（元/月）|
|bondPriceType|integer(int32)|false|none|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|
|bondPrice|number|false|none|立项定价保证金金额|立项定价保证金金额|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractChangeCostGenerateVo">ContractChangeCostGenerateVo</h2>

<a id="schemacontractchangecostgeneratevo"></a>
<a id="schema_ContractChangeCostGenerateVo"></a>
<a id="tocScontractchangecostgeneratevo"></a>
<a id="tocscontractchangecostgeneratevo"></a>

```json
{
  "changeCosts": [
    {
      "id": "string",
      "changeId": "string",
      "costType": 0,
      "type": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0,
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "terminateReceivable": 0,
      "refundAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "costId": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "oldCosts": [
    {
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "newCosts": [
    {
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ],
  "transferCosts": [
    {
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0
    }
  ]
}

```

合同变更费用生成VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|changeCosts|[[ContractChangeCostVo](#schemacontractchangecostvo)]|false|none|新增账单和退款单|新增账单和退款单|
|oldCosts|[[ContractCostVo](#schemacontractcostvo)]|false|none|原合同账单|原合同账单|
|newCosts|[[ContractCostVo](#schemacontractcostvo)]|false|none|新合同账单|新合同账单|
|transferCosts|[[ContractCostVo](#schemacontractcostvo)]|false|none|结转账单|结转账单|

<h2 id="tocS_ContractChangeQueryDTO">ContractChangeQueryDTO</h2>

<a id="schemacontractchangequerydto"></a>
<a id="schema_ContractChangeQueryDTO"></a>
<a id="tocScontractchangequerydto"></a>
<a id="tocscontractchangequerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeType": 0,
  "approveStatus": 0,
  "processId": "string",
  "remark": "string",
  "attachment": "string",
  "costSnapshot": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractUnionId|string|false|none|合同联合id|合同联合id|
|changeDate|string(date-time)|false|none|变更日期|变更日期|
|changeType|integer(int32)|false|none|变更类型: 1-扩租, 2-换房|变更类型: 1-扩租, 2-换房|
|approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|processId|string|false|none|流程id|流程id|
|remark|string|false|none|备注|备注|
|attachment|string|false|none|附件|附件|
|costSnapshot|string|false|none|账单信息快照|用于记录当时的应收与实收, 包含变更前、变更后、结转账单|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractChangeCostVo">ContractChangeCostVo</h2>

<a id="schemacontractchangecostvo"></a>
<a id="schema_ContractChangeCostVo"></a>
<a id="tocScontractchangecostvo"></a>
<a id="tocscontractchangecostvo"></a>

```json
{
  "id": "string",
  "changeId": "string",
  "costType": 0,
  "type": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "contractStartDate": "2019-08-24T14:15:22Z",
  "rentTicketPeriod": 0,
  "period": 0,
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "unitPrice": 0,
  "priceUnit": 0,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "terminateReceivable": 0,
  "refundAmount": 0,
  "isRevenue": true,
  "isDiscount": true,
  "costId": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

变更应收账单列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|changeId|string|false|none|合同变更id|合同变更id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|type|integer(int32)|false|none|类型,1-新增账单, 2-退款账单|类型,1-新增账单, 2-退款账单|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|contractStartDate|string(date-time)|false|none|合同开始日期, 用来推理租赁月|合同开始日期, 用来推理租赁月|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|period|integer(int32)|false|none|账单期数|账单期数|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|area|number|false|none|商铺面积|商铺面积|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途|收款用途|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|unitPrice|number|false|none|单价|单价|
|priceUnit|integer(int32)|false|none|单价单位|单价单位|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|terminateReceivable|number|false|none|截止换房日应收（元）|截止换房日应收（元）|
|refundAmount|number|false|none|预计退款金额（元）|预计退款金额（元）|
|isRevenue|boolean|false|none|是否营收提成,0-否,1-是|是否营收提成,0-否,1-是|
|isDiscount|boolean|false|none|是否优惠,0-否,1-是|是否优惠,0-否,1-是|
|costId|string|false|none|t_cost表id|t_cost表id|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractChangeDetailVo">ContractChangeDetailVo</h2>

<a id="schemacontractchangedetailvo"></a>
<a id="schema_ContractChangeDetailVo"></a>
<a id="tocScontractchangedetailvo"></a>
<a id="tocscontractchangedetailvo"></a>

```json
{
  "contractChange": {
    "id": "string",
    "projectId": "string",
    "contractId": "string",
    "contractUnionId": "string",
    "changeDate": "2019-08-24T14:15:22Z",
    "changeType": 0,
    "approveStatus": 0,
    "processId": "string",
    "remark": "string",
    "attachment": "string",
    "signAttachments": "string",
    "costSnapshot": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "changeRooms": [
    {
      "id": "string",
      "changeId": "string",
      "type": 0,
      "isChecked": true,
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "bondPriceType": 0,
      "bondPrice": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "changeCosts": [
    {
      "id": "string",
      "changeId": "string",
      "costType": 0,
      "type": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "contractStartDate": "2019-08-24T14:15:22Z",
      "rentTicketPeriod": 0,
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "terminateReceivable": 0,
      "refundAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "costId": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

合同变更详情VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|contractChange|[ContractChangeVo](#schemacontractchangevo)|false|none||合同变更信息|
|changeRooms|[[ContractChangeRoomVo](#schemacontractchangeroomvo)]|false|none|变更房源列表|变更房源列表|
|changeCosts|[[ContractChangeCostVo](#schemacontractchangecostvo)]|false|none|变更应收账单列表|变更应收账单列表|

<h2 id="tocS_ContractChangeRoomVo">ContractChangeRoomVo</h2>

<a id="schemacontractchangeroomvo"></a>
<a id="schema_ContractChangeRoomVo"></a>
<a id="tocScontractchangeroomvo"></a>
<a id="tocscontractchangeroomvo"></a>

```json
{
  "id": "string",
  "changeId": "string",
  "type": 0,
  "isChecked": true,
  "roomId": "string",
  "roomName": "string",
  "buildingName": "string",
  "floorName": "string",
  "parcelName": "string",
  "stageName": "string",
  "area": 0,
  "standardUnitPrice": 0,
  "bottomPrice": 0,
  "priceUnit": 0,
  "discount": 0,
  "signedUnitPrice": 0,
  "signedMonthlyPrice": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

变更房源列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|changeId|string|false|none|合同变更id|合同变更id|
|type|integer(int32)|false|none|类型: 1-新加, 2-去除|类型: 1-新加, 2-去除|
|isChecked|boolean|false|none|是否选中,0-否,1-是; type=2且为true代表选中要被换掉的房间|是否选中,0-否,1-是; type=2且为true代表选中要被换掉的房间|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房间名称|房间名称|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorName|string|false|none|楼层名称|楼层名称|
|parcelName|string|false|none|地块名称|地块名称|
|stageName|string|false|none|分期名称|分期名称|
|area|number|false|none|面积（㎡）|面积（㎡）|
|standardUnitPrice|number|false|none|标准租金（单价）|标准租金（单价）|
|bottomPrice|number|false|none|底价|底价|
|priceUnit|integer(int32)|false|none|单价单位, 1-元/平米/月, 2-元/月, 3-元/日|单价单位, 1-元/平米/月, 2-元/月, 3-元/日|
|discount|number|false|none|折扣|折扣|
|signedUnitPrice|number|false|none|签约单价|签约单价|
|signedMonthlyPrice|number|false|none|签约月总价（元/月）|签约月总价（元/月）|
|bondPriceType|integer(int32)|false|none|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|
|bondPrice|number|false|none|立项定价保证金金额|立项定价保证金金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractChangeVo">ContractChangeVo</h2>

<a id="schemacontractchangevo"></a>
<a id="schema_ContractChangeVo"></a>
<a id="tocScontractchangevo"></a>
<a id="tocscontractchangevo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeType": 0,
  "approveStatus": 0,
  "processId": "string",
  "remark": "string",
  "attachment": "string",
  "signAttachments": "string",
  "costSnapshot": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同变更信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractUnionId|string|false|none|合同联合id|合同联合id|
|changeDate|string(date-time)|false|none|变更日期|变更日期|
|changeType|integer(int32)|false|none|变更类型: 1-扩租, 2-换房|变更类型: 1-扩租, 2-换房|
|approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|processId|string|false|none|流程id|流程id|
|remark|string|false|none|备注|备注|
|attachment|string|false|none|附件|附件|
|signAttachments|string|false|none|签署附件|签署附件|
|costSnapshot|string|false|none|账单信息快照|用于记录当时的应收与实收, 包含变更前、变更后、结转账单|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

