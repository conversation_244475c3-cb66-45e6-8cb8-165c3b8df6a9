/// <reference types="vite/client" />

declare module '*.vue' {
    import { DefineComponent } from 'vue';
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
    const component: DefineComponent<{}, {}, any>;
    export default component;
}
interface ImportMetaEnv {
    readonly VITE_API_BASE_URL: string;
}

// 告诉 TypeScript JSX 元素是 Vue 的 VNode
// import { VNode } from 'vue';
// declare global {
//   namespace JSX {
//     interface IntrinsicElements {
//       [elem: string]: any; // 允许所有原生 HTML 元素
//     }
//     interface Element extends VNode {} // Vue 的 JSX 元素类型
//   }
// }