<template>
    <section>
        <sectionTitle title="开票信息" />
        <div class="content">
            <a-form ref="formRef" :model="contractData.customer" :rules="isDetailMode ? {} : rules"
                :disabled="isDetailMode" layout="vertical" auto-label-width>
                <a-form-item label="名称" field="invoiceTitle">
                    <a-input v-model="contractData.customer.invoiceTitle" placeholder="请输入名称" />
                </a-form-item>
                <a-form-item label="税号" field="invoiceTaxNumber">
                    <a-input v-model="contractData.customer.invoiceTaxNumber" placeholder="请输入税号" />
                </a-form-item>
                <a-form-item label="单位地址" field="invoiceAddress">
                    <a-input v-model="contractData.customer.invoiceAddress" placeholder="请输入单位地址" />
                </a-form-item>
                <a-form-item label="电话号码" field="invoicePhone">
                    <a-input v-model="contractData.customer.invoicePhone" placeholder="请输入电话号码" />
                </a-form-item>
                <a-form-item label="开户银行" field="invoiceBankName">
                    <a-input v-model="contractData.customer.invoiceBankName" placeholder="请输入开户银行" />
                </a-form-item>
                <a-form-item label="银行账户" field="invoiceAccountNumber">
                    <a-input v-model="contractData.customer.invoiceAccountNumber" placeholder="请输入银行账户" />
                </a-form-item>
            </a-form>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { inject, watch, ref, toRef, computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { useContractStore } from '@/store/modules/contract'
import { FormInstance } from '@arco-design/web-vue';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

/**
 * @description 编辑类型
 * 1. 详情
 *    - 所有字段不可修改，且展示成文本
 */
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => editType.value === 'detail' || (editType.value === 'change' && !changeType.value.includes(1)) || editType.value === 'changeDetail' || editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo')
const formItemMargin = computed(() => isDetailMode.value ? '16px' : '16px')

// 表单验证规则
const rules = {
    // invoiceTitle: [{ required: true, message: '请输入名称' }],
    // invoiceTaxNumber: [{ required: true, message: '请输入税号' }],
    // invoiceAddress: [{ required: true, message: '请输入单位地址' }],
    // invoicePhone: [{ required: true, message: '请输入电话号码' }],
    // invoiceBankName: [{ required: true, message: '请输入开户银行' }],
    // invoiceAccountNumber: [{ required: true, message: '请输入银行账户' }]
}

const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('开票信息', errors);
            reject()
        } else {
            resolve(true)
        }
    })
}
defineExpose({
    validate
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0 0;
    }
}

.detail-text {
    padding: 4px 0;
    color: #333;
}

.arco-form-item {
    margin-bottom: v-bind(formItemMargin);
}
</style>