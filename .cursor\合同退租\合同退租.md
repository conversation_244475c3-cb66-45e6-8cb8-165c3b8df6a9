# 合同退租-接口调用说明
    ## 承租方信息
        - 接口：/terminate/detail
        - 数据：在 contract 里面取
    ## 租赁房源
         - 接口：/terminate/detail
         - 数据：roomList
         - 操作：可以多选
         - 联动：选中之后调用接口 /terminate/getBill 查询下面的 预计退款信息
    ## 基础信息        
        - 接口：/terminate/detail
        - 数据：在 data里面取
    ## 退租信息
        - 接口：/terminate/detail
        - 数据：在 data里面取
    ## 退款信息
        - 接口：/terminate/detail
        - 数据：在 data里面取
        - 操作：可以选择
        - 联动：选了 ‘退租类型’和退租日期，调用接口 /terminate/getBill 查询下面的 预计退款信息
    ## 免租期是否收费
        - 接口：/terminate/detail
        - 数据：在 contract 里面的 fees 里面取
        - 操作：都可以操作
        - 联动：选了 是否收费 的 ‘是’ 值；调用接口 /terminate/freeRent 查询 免租期租金，和下面的 预计退款信息 列表合并展示，注意字段区别。选否，重新调接口，注意增删。
    ## 预计退款信息
        - 通过上面的操作来展示
        - 操作： 罚没金额 可以输入值，其他不可操作
        - 合计：罚没金额和预计退款金额
    ## 其他扣款
        - 接口：/terminate/detail
        - 数据：在 data里面取
    ## 附件
        - 使用全局的 上传组件
        - 数据：在 data里面取
    ## 操作
        - 先做暂存和提交申请，下一步等后期再做