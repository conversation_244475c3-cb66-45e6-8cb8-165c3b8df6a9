<template>
    <div class="features-container">
        <div class="features">
            <div class="guide">
                <img src="@/assets/images/dashboard/icon-guide.png" alt="">
                操作指引
            </div>
            <div class="features-header">
                <div class="tab-item" :class="{ active: activeTab === 'quick' }" @click="activeTab = 'quick'">
                    快捷操作
                </div>
                <div class="tab-item" :class="{ active: activeTab === 'recent' }" @click="activeTab = 'recent'">
                    最近访问
                </div>
                <div class="tab-line" :style="{ left: activeTab === 'quick' ? '16px' : '85px' }"></div>
            </div>
            <div class="features-content">
                <div v-if="activeTab === 'quick'" class="features-grid">
                    <div class="feature-item" v-for="item in quickFeatures" :key="item.name"
                        @click="handleFeatureClick(item)">
                        <div class="feature-icon">
                            <img :src="item.icon" :alt="item.name" />
                        </div>
                        <div class="feature-name">{{ item.name }}</div>
                    </div>
                </div>
                <div v-if="activeTab === 'recent'" class="features-grid">
                    <div class="feature-item" v-for="item in recentFeatures" :key="item.name"
                        @click="handleFeatureClick(item)">
                        <div class="feature-icon">
                            <img :src="item.icon" :alt="item.name" />
                        </div>
                        <div class="feature-name">{{ item.name }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import feature1 from '@/assets/images/dashboard/feature-1.png'
import feature2 from '@/assets/images/dashboard/feature-2.png'
import feature3 from '@/assets/images/dashboard/feature-3.png'
import feature4 from '@/assets/images/dashboard/feature-4.png'
import feature5 from '@/assets/images/dashboard/feature-5.png'
import feature6 from '@/assets/images/dashboard/feature-6.png'
import feature7 from '@/assets/images/dashboard/feature-7.png'

const activeTab = ref('quick')

const quickFeatures = ref([
    { name: '创建房源', icon: feature1 },
    { name: '房源租控', icon: feature2 },
    { name: '新增客户', icon: feature3 },
    { name: '新增定单', icon: feature4 },
    { name: '出场办理', icon: feature5 },
    { name: '进场办理', icon: feature6 },
    { name: '新建合同', icon: feature7 },
])

const recentFeatures = ref([
    { name: '出场办理', icon: feature5 },
    { name: '进场办理', icon: feature6 },
    { name: '新建合同', icon: feature7 },
])

const handleFeatureClick = (item: any) => {
    console.log('点击功能:', item.name)
    // 这里可以添加路由跳转或其他逻辑
}
</script>

<style lang="less" scoped>
.features-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.features {
    position: absolute;
    left: 0;
    top: -70px;
    background: #fff;
    padding: 16px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    border-radius: 20px 0 0 0;

    &::after {
        content: '';
        width: 70px;
        height: 38px;
        background: url('@/assets/images/dashboard/section-bg_03.png') no-repeat;
        background-size: contain;
        position: absolute;
        top: 0;
        left: -20px;
    }

    .guide {
        position: absolute;
        right: 0;
        top: 0;
        width: 135px;
        height: 36px;
        background: linear-gradient(223.73deg, rgba(68, 197, 255, 0.2) 0%, rgba(76, 152, 255, 0.2) 96.97%);

        border-radius: 0 0 0 68px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 15px;
        color: #0071FF;
        font-weight: 500;

        img {
            width: 16px;
        }
    }

    .features-header {
        position: relative;
        display: flex;
        gap: 21px;
        margin-bottom: 18px;

        .tab-item {
            font-size: 16px;
            font-weight: 500;
            color: #000;
            cursor: pointer;
            padding-bottom: 8px;
            position: relative;
            transition: color 0.3s;

            &.active {
                color: #0071FF;
            }
        }

        .tab-line {
            position: absolute;
            bottom: 0;
            width: 32px;
            height: 3px;
            background: #1677FF;
            border-radius: 1.5px;
            transition: left 0.3s ease;
        }
    }

    .features-content {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 18px;

        // &:has(.feature-item:nth-child(3)) {
        //     grid-template-columns: repeat(3, 1fr);
        // }
    }

    .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: transform 0.2s;

        &:hover {
            transform: translateY(-2px);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: #F5F9FF;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 9px;

            img {
                width: 30px;
                height: 30px;
                object-fit: contain;
            }
        }

        .feature-name {
            font-size: 14px;
            color: #000;
            text-align: center;
            line-height: 1.4;
        }
    }
}
</style>