<template>
    <div class="project-control-settings">
        <!-- 项目选择 -->
        <div class="project-selector">
            <a-form layout="inline">
                <a-form-item label="所属项目">
                    <ProjectTreeSelect
                        v-model="selectedProjectId"
                        :min-level="4"
                        @change="handleProjectChange"
                    />
                </a-form-item>
            </a-form>
        </div>

        <!-- 合同管控 -->
        <div class="form-section">
            <SectionTitle title="合同管控" />
            <a-form
                ref="formRef"
                :model="formData"
                :rules="formRules"
                label-align="right"
                layout="horizontal"
                auto-label-width
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
            >
                
                <!-- 签订日期固化设置 -->
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item field="signDateRange" label="合同签订日期固化">
                            <a-radio-group v-model="formData.signDateRange">
                                <a-radio :value="1">不固化</a-radio>
                                <a-radio :value="2">当月固化</a-radio>
                                <a-radio :value="3">自定义</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                    <a-col v-if="formData.signDateRange === 3" :span="8">
                        <a-form-item field="days" label="自定义天数">
                            <a-input-number
                                v-model="formData.days"
                                placeholder="请输入天数"
                                :min="1"
                                :precision="0"
                                style="width: 100%"
                            >
                                <template #append>天</template>
                            </a-input-number>
                        </a-form-item>
                    </a-col>
                </a-row>
                
                <div class="reminder-text">
                    <p>不固化：不限制签约日期</p>
                    <p>当月固化：签约日期仅能选在做合同当月，不允许跨月</p>
                    <p>自定义：在进行合同签约的操作时，签订日期可以选择自定义天数前、后N天的时间，为0表示不可以修改，直接获取系统时间</p>
                </div>
                
                <!-- 单价位数、总价位数、面积保留位数 -->
                <a-row :gutter="16">
                    <a-col :span="24">
                        <a-form-item field="priceDigit" label="单价保留位">
                            <a-radio-group v-model="formData.priceDigit">
                                <a-radio :value="0">元</a-radio>
                                <a-radio :value="1">角</a-radio>
                                <a-radio :value="2">分</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="16">
                    <a-col :span="24">
                        <a-form-item field="totalDigit" label="总价保留位">
                            <a-radio-group v-model="formData.totalDigit">
                                <a-radio :value="0">元</a-radio>
                                <a-radio :value="1">角</a-radio>
                                <a-radio :value="2">分</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="16">
                    <a-col :span="24">
                        <a-form-item field="areaDigit" label="面积保留位">
                            <a-input-number
                                v-model="formData.areaDigit"
                                :min="0"
                                :precision="0"
                                style="width: 100px"
                            />
                            <span style="margin-left: 8px">位</span>
                        </a-form-item>
                    </a-col>
                </a-row>

                <!-- 成交总价进位方式 -->
                <a-row :gutter="16">
                    <a-col :span="24">
                        <a-form-item field="dealType" label="成交总价进位方式">
                            <a-radio-group v-model="formData.dealType">
                                <a-radio :value="1">按保留位四舍五入</a-radio>
                                <a-radio :value="2">舍去保留位后的小数</a-radio>
                                <a-radio :value="3">保留位后逢小数进位</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                </a-row>
                
                <div class="calculation-examples">
                    <p><strong>说明：</strong></p>
                    <p>1. 按保留位四舍五入：如，100.005 结果为100.01</p>
                    <p>2. 舍去保留位后的小数：如，100.005 结果为100.00</p>
                    <p>3. 保留位后逢小数进位：如，100.003 结果为100.01</p>
                </div>
            </a-form>
        </div>
        
        <!-- 保存按钮 -->
        <div class="form-actions">
            <a-button type="primary" @click="handleSave" :loading="saveLoading">
                保存
            </a-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import SectionTitle from '@/components/sectionTitle/index.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import { addContractRules, getContractRulesDetail, type ContractRulesAddDTO } from '@/api/contractRules'


// 表单引用
const formRef = ref()

// 保存加载状态
const saveLoading = ref(false)

// 选中的项目ID和项目信息
const selectedProjectId = ref()
const selectedProjectName = ref()

// 表单数据 - 使用API字段名
const formData = reactive({
    type: 2, // 类型（1集团，2项目）
    projectId: '',
    projectName: '',
    signDateRange: 2, // 签订日期固化(1不固化 2当月固化 3自定义) - 对应原contractRenewalType
    days: undefined, // 签订日期天数 - 对应原customDays
    priceDigit: 2, // 单价位数(0元 1角 2分) - 对应原priceDecimalPlaces
    totalDigit: 2, // 总价位数(0元 1角 2分) - 对应原contractDecimalPlaces
    areaDigit: 2, // 面积保留位(默认保留2位) - 对应原areaDecimalPlaces
    dealType: 1, // 成交总价进位方式(1四舍五入 2直接舍去 3直接进位) - 对应原calculationMethod
    isDel: false
})

// 表单验证规则
const formRules = {
    days: [
        {
            required: true,
            message: '请输入自定义天数',
            validator: (value: any, callback: any) => {
                if (formData.signDateRange === 3 && !value) {
                    callback('请输入自定义天数')
                } else {
                    callback()
                }
            }
        }
    ]
}

// 处理项目变更
const handleProjectChange = (projectId: string, selectOrg: any) => {
    selectedProjectId.value = projectId
    selectedProjectName.value = selectOrg?.name || ''

    // 更新表单数据
    formData.projectId = projectId
    formData.projectName = selectOrg?.name || ''
    console.log(selectOrg)
    if (projectId) {
        loadProjectSettings(projectId)
    }
}

// 处理保存
const handleSave = async () => {
    try {
        if (!selectedProjectId.value) {
            Message.warning('请先选择项目')
            return
        }

        const errors = await formRef.value?.validate()
        if (errors) return

        saveLoading.value = true

        // 构造合同规则数据
        const contractRulesData: ContractRulesAddDTO = {
            type: 2, // 项目类型
            projectId: formData.projectId,
            projectName: formData.projectName,
            signDateRange: formData.signDateRange,
            days: formData.days,
            priceDigit: formData.priceDigit,
            totalDigit: formData.totalDigit,
            areaDigit: formData.areaDigit,
            dealType: formData.dealType,
            isDel: false
        }

        console.log('保存合同规则:', contractRulesData)
        await addContractRules(contractRulesData)

        Message.success('合同规则保存成功')
    } catch (error) {
        console.error('保存失败:', error)
    } finally {
        saveLoading.value = false
    }
}

// 加载项目设置
const loadProjectSettings = async (projectId: string) => {
    try {
        // 调用合同规则详情接口
        const response = await getContractRulesDetail({
            type: 2, // 项目类型
            projectId: projectId
        })
        if (response.data) {
            const data = response.data
            // 更新表单数据 - 使用 ?? 操作符来处理 null/undefined，避免 0 值被覆盖
            formData.signDateRange = data.signDateRange ?? 2
            formData.days = data.days
            formData.priceDigit = data.priceDigit ?? 2
            formData.totalDigit = data.totalDigit ?? 2
            formData.areaDigit = data.areaDigit ?? 2
            formData.dealType = data.dealType ?? 1

            console.log('加载项目合同规则成功:', data)
        } else {
            console.log('未找到项目合同规则，使用默认值')
        }
    } catch (error) {
        console.error('加载项目合同规则失败:', error)
        // 保持默认值
    }
}

onMounted(() => {
    // 初始化
})
</script>

<style scoped lang="less">
.project-control-settings {
    padding: 12px;
    box-sizing: border-box;

    .project-selector {
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--color-border-2);
    }

    .form-section {
        margin-bottom: 32px;
    }

    :deep(.section-title){
        margin-bottom: 16px;
    }

    .reminder-text {
        margin: 0 0 16px 0;
        padding: 12px;
        background: var(--color-fill-1);
        border-radius: 4px;
        font-size: 12px;
        color: var(--color-text-2);

        p {
            margin: 4px 0;
        }
    }



    .calculation-examples {
        margin: 0 0 16px 0;
        padding: 12px;
        background: var(--color-fill-1);
        border-radius: 4px;
        font-size: 12px;
        color: var(--color-text-2);

        p {
            margin: 4px 0;
        }
    }

    .form-actions {
        display: flex;
        justify-content: center;
        padding-top: 16px;
        border-top: 1px solid var(--color-border-2);
        margin-top: 32px;
    }
}
</style>
