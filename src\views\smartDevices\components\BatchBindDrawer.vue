<template>
    <a-drawer
        v-model:visible="visible"
        title="批量绑定门锁"
        class="common-drawer"
        :footer="false"
        @cancel="handleCancel"
    >
        <!-- 项目信息 -->
        <div class="project-info">
            <span>项目：{{ projectInfo.projectName }}</span>
        </div>

        <!-- 设备品牌管理 -->
        <div class="brand-section">
            <SectionTitle title="设备品牌" />
            <div class="brand-cards">
                <div
                    v-for="brand in brandList"
                    :key="brand.id"
                    class="brand-card"
                >
                    <span class="brand-label">{{ brand.label }}</span>
                    <icon-close-circle class="delete-icon"
                    @click="handleDeleteAccount(brand)"/>
                </div>
                <div class="brand-card add-brand" @click="handleAddAccount">
                    +绑定账号
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧房源列表 -->
            <div class="left-panel">
                <SectionTitle title="房源">
                    <template #right>
                        <div class="header-actions">
                            <a-button type="text" @click="handleAutoMatch">自动匹配</a-button>
                            <a-button type="text" @click="handleExportRooms">导出房源</a-button>
                            <a-button type="text" @click="handleImportBinding" :loading="importLoading">导入绑定关系</a-button>
                        </div>
                    </template>
                </SectionTitle>
                
                <!-- 房源筛选 -->
                <div class="filter-section" style="margin-top: 16px;">
                    <a-row :gutter="8">
                        <a-col :span="4">
                            <a-select v-model="roomFilter.bindFlag" placeholder="绑定状态" allow-clear>
                                <a-option value="">全部</a-option>
                                <a-option :value="0">未绑定</a-option>
                                <a-option :value="1">已绑定</a-option>
                            </a-select>
                        </a-col>
                        <a-col :span="5">
                            <a-select v-model="roomFilter.parcelId" placeholder="地块" allow-clear @change="handleParcelChange">
                                <a-option v-for="item in parcelOptions" :key="item.id" :value="item.id">
                                    {{ item.parcelName }}
                                </a-option>
                            </a-select>
                        </a-col>
                        <a-col :span="5">
                            <a-select v-model="roomFilter.buildingId" placeholder="楼栋" allow-clear>
                                <a-option v-for="item in buildingOptions" :key="item.id" :value="item.id">
                                    {{ item.buildingName }}
                                </a-option>
                            </a-select>
                        </a-col>
                        <a-col :span="8">
                            <a-input v-model="roomFilter.roomName" placeholder="请输入房源名称" allow-clear />
                        </a-col>
                        <a-col :span="2">
                            <a-button type="primary" @click="handleRoomSearch">搜索</a-button>
                        </a-col>
                    </a-row>
                </div>
                <!-- 房源表格 -->
                <div class="room-table">
                    <a-table
                        :data="roomList"
                        :columns="roomColumns"
                        :pagination="roomPagination"
                        :loading="roomLoading"
                        row-key="roomId"
                        size="small"
                        :scroll="{x: '100%',y: '100%'}"
                        :scrollbar="true"
                        @page-change="handleRoomPageChange"
                        @page-size-change="handleRoomPageSizeChange"
                    >
                        <template #ttlockId="{ record, rowIndex }">
                            <a-input
                                v-model="record.ttlockId"
                                placeholder="请选择设备"
                                readonly
                                @click="handleSelectDevice(record, rowIndex)"
                                style="cursor: pointer;"
                            />
                        </template>
                    </a-table>
                </div>
            </div>

            <!-- 右侧设备信息 -->
            <div class="right-panel">
                <SectionTitle title="设备信息">
                    <template #right>
                        <div class="header-actions">
                            <a-button type="text" @click="handleSyncDevices">同步设备数据</a-button>
                            <a-button type="text" @click="handleExportDevices">导出设备</a-button>
                        </div>
                    </template>
                </SectionTitle>

                <!-- 设备筛选 -->
                <div class="filter-section" style="margin-top: 16px;">
                    <a-row :gutter="8">
                        <a-col :span="18">
                             <a-input v-model="deviceFilter.deviceName" placeholder="请输入门牌号" allow-clear />
                        </a-col>
                        <a-col :span="6">
                            <a-button type="primary" @click="handleDeviceSearch" style="margin-left: 8px;">搜索</a-button>
                        </a-col>
                    </a-row>
                </div>

                <!-- 设备表格 -->
                <div class="device-table">
                    <a-table
                        :data="deviceList"
                        :columns="deviceColumns"
                        :pagination="devicePagination"
                        :loading="deviceLoading"
                        row-key="id"
                        size="small"
                        :scroll="{x: '100%',y: '100%'}"
                        :scrollbar="true"
                        @page-change="handleDevicePageChange"
                        @page-size-change="handleDevicePageSizeChange"
                    >
                        <template #action="{ record }">
                            <a-button
                                type="text"
                                size="small"
                                @click="handleSelectDeviceFromList(record)"
                                :disabled="!currentSelectingRoom || isDeviceSelected(record)"
                            >
                                {{ isDeviceSelected(record) ? '已选择' : '选择' }}
                            </a-button>
                        </template>
                    </a-table>
                </div>
            </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="footer-actions">
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleSave">保存</a-button>
        </div>

        <!-- 添加账号弹窗 -->
        <AddAccountModal
            v-model:visible="addAccountVisible"
            :project-id="projectInfo.projectId"
            @success="handleAddAccountSuccess"
        />
    </a-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import SectionTitle from '@/components/sectionTitle/index.vue'
import AddAccountModal from './AddAccountModal.vue'
import { getRoomList, exportRoomList, getDeviceInfo, bindRoomDevice, autoBindRoomDevice, syncDeviceData, getTTLockAccountList, deleteTTLockAccount, importRoomDeviceBind, exportTTLockDeviceList, type RoomSimpleVo, type TTLockDeviceVo, type TTLockDeviceInfoQueryDTO } from '@/api/smartLock'
import { getParcelList, getBuildingSelectList, type SysParcel, type SysBuilding } from '@/api/project'
import { exportExcel, importExcelFile } from '@/utils/exportUtil'

// 直接使用 RoomSimpleVo，不需要扩展
type ExtendedRoomVo = RoomSimpleVo

// Props - 不再需要必传的项目信息
// interface Props {
//     // 可以保留一些可选的配置项
// }

// Emits
const emit = defineEmits<{
    'success': []
    'cancel': []
}>()

// 响应式数据
const visible = ref(true) // 使用 v-if 控制时，组件创建时就应该是可见的

// 项目信息 - 通过 show 方法设置
const projectInfo = ref({
    projectId: '',
    projectName: ''
})

// 设备品牌列表
const brandList = ref<Array<{id: string, label: string, username: string}>>([])
const addAccountVisible = ref(false)

// 导入相关
const importLoading = ref(false)

// 房源相关
const roomLoading = ref(false)
const roomList = ref<ExtendedRoomVo[]>([])
const roomFilter = reactive({
    bindFlag: 0, // 默认显示未绑定 (对应接口字段)
    parcelId: '', // 对应接口字段
    buildingId: '', // 对应接口字段
    roomName: ''
})

const roomPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true
})

// 设备相关
const deviceLoading = ref(false)
const deviceList = ref<TTLockDeviceVo[]>([])
const deviceFilter = reactive({
    deviceName: ''
})

const devicePagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true
})

// 当前选择的房源
const currentSelectingRoom = ref<ExtendedRoomVo | null>(null)
const currentSelectingIndex = ref(-1)

// 全局绑定关系存储（用于跨分页保持绑定关系）
const globalBindingMap = ref<Map<string, {
    ttlockDeviceId: string
    ttlockId: number
    ttlockName: string
}>>(new Map())

// 地块和楼栋选项数据
const parcelOptions = ref<SysParcel[]>([])
const buildingOptions = ref<SysBuilding[]>([])
const parcelLoading = ref(false)
const buildingLoading = ref(false)

// 房源表格列配置
const roomColumns = [
    {
        title: '房源名称',
        dataIndex: 'fullName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '设备ID',
        slotName: 'ttlockId',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '设备门牌号',
        dataIndex: 'ttlockName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    }
]

// 设备表格列配置
const deviceColumns = [
    {
        title: '操作',
        slotName: 'action',
        width: 80,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '设备门牌号',
        dataIndex: 'lockName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '设备ID',
        dataIndex: 'lockId',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    }
]



// 添加账号
const handleAddAccount = () => {
    addAccountVisible.value = true
}

const handleAddAccountSuccess = () => {
    Message.success('账号添加成功')
    // 重新加载品牌列表（会自动调用fetchDeviceList）
    fetchBrandList()
}

// 获取品牌列表
const fetchBrandList = async () => {
    if (!projectInfo.value.projectId) return

    try {
        const params = {
            pageNum: 1,
            pageSize: 100,
            projectId: projectInfo.value.projectId
        }

        const response = await getTTLockAccountList(params)
        if (response && response.data) {
            brandList.value = response.data.map((item: any) => ({
                id: item.id,
                label: item.brandType === 1 ? '通通锁' : '未知品牌',
                username: item.username
            }))
        } else {
            brandList.value = []
        }

        // 获取品牌列表后，重新获取设备列表
        await fetchDeviceList()
    } catch (error) {
        console.error('获取品牌列表失败:', error)
        brandList.value = []
    }
}

// 删除账号
const handleDeleteAccount = (brand:any) => {
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除账号"${brand.label}/${brand.username}"吗？删除后将无法恢复。`,
        onOk: async () => {
            try {
                await deleteTTLockAccount(brand.id)
                Message.success('账号删除成功')
                // 重新加载品牌列表（会自动调用fetchDeviceList）
                fetchBrandList()
            } catch (error) {
                console.error('删除账号失败:', error)
            }
        }
    })
}

// 房源相关方法
const fetchRoomList = async (clearBindings = false) => {
    if (!projectInfo.value.projectId) return

    roomLoading.value = true
    try {
        const params = {
            pageNum: roomPagination.current,
            pageSize: roomPagination.pageSize,
            projectId: projectInfo.value.projectId,
            bindFlag: roomFilter.bindFlag,
            parcelId: roomFilter.parcelId,
            buildingId: roomFilter.buildingId,
            roomName: roomFilter.roomName
        }

        const response = await getRoomList(params)
        if (response && response.rows) {
            // 直接使用接口返回的数据
            roomList.value = response.rows

            // 如果需要清空绑定关系，清空全局绑定映射
            if (clearBindings) {
                globalBindingMap.value.clear()
            } else {
                // 恢复绑定关系
                roomList.value.forEach(room => {
                    const binding = globalBindingMap.value.get(room.roomId)
                    if (binding) {
                        room.ttlockDeviceId = binding.ttlockDeviceId
                        room.ttlockId = binding.ttlockId
                        room.ttlockName = binding.ttlockName
                    }
                })
            }

            roomPagination.total = response.total
        }
    } catch (error) {
        console.error('获取房源列表失败:', error)
    } finally {
        roomLoading.value = false
    }
}

const handleRoomSearch = () => {
    roomPagination.current = 1
    fetchRoomList(true) // 搜索时清空绑定关系
}

const handleRoomPageChange = (page: number) => {
    roomPagination.current = page
    fetchRoomList(false) // 分页时不清空绑定关系
}
const handleRoomPageSizeChange = (pageSize: number) => {
    roomPagination.pageSize = pageSize
    roomPagination.current = 1
    fetchRoomList(false) // 分页时不清空绑定关系
}

// 设备相关方法
const fetchDeviceList = async () => {
    if (!projectInfo.value.projectId) return

    // 如果没有品牌账号，不获取设备列表
    if (brandList.value.length === 0) {
        deviceList.value = []
        devicePagination.total = 0
        return
    }

    deviceLoading.value = true
    try {
        const params: TTLockDeviceInfoQueryDTO = {
            pageNum: devicePagination.current,
            pageSize: devicePagination.pageSize,
            deviceName: deviceFilter.deviceName,
            accountList: brandList.value.map(brand => brand.username)
        }

        const response = await getDeviceInfo(params)
        if (response && response.rows) {
            deviceList.value = response.rows
            devicePagination.total = response.total

            // 检查并清除无效的关联关系
            clearInvalidBindings(response.rows)
        } else {
            deviceList.value = []
            devicePagination.total = 0
        }
    } catch (error) {
        console.error('获取设备列表失败:', error)
        deviceList.value = []
        devicePagination.total = 0
    } finally {
        deviceLoading.value = false
    }
}

const handleDeviceSearch = () => {
    devicePagination.current = 1
    fetchDeviceList()
}

const handleDevicePageChange = (page: number) => {
    devicePagination.current = page
    fetchDeviceList()
}
const handleDevicePageSizeChange = (pageSize: number) => {
    devicePagination.pageSize = pageSize
    devicePagination.current = 1
    fetchDeviceList()
}

// 设备选择相关
const handleSelectDevice = (record: any, rowIndex: number) => {
    currentSelectingRoom.value = record
    currentSelectingIndex.value = rowIndex
    Message.info('请在右侧设备列表中选择一个设备')
}

const handleSelectDeviceFromList = (device: TTLockDeviceVo) => {
    if (!currentSelectingRoom.value) {
        Message.warning('请先选择要绑定的房源')
        return
    }

    // 检查设备是否已被其他房源绑定（包括全局绑定映射中的数据）
    const isDeviceAlreadyBound = Array.from(globalBindingMap.value.values()).some(binding =>
        binding.ttlockDeviceId === device.id
    ) || roomList.value.some(room =>
        room.roomId !== currentSelectingRoom.value?.roomId &&
        room.ttlockDeviceId === device.id
    )

    if (isDeviceAlreadyBound) {
        Message.warning('该设备已被其他房源绑定，请选择其他设备')
        return
    }

    // 保存绑定关系到全局映射
    globalBindingMap.value.set(currentSelectingRoom.value.roomId, {
        ttlockDeviceId: device.id,
        ttlockId: device.lockId,
        ttlockName: device.lockName
    })

    // 填充设备信息到房源列表
    currentSelectingRoom.value.ttlockDeviceId = device.id
    currentSelectingRoom.value.ttlockId = device.lockId
    currentSelectingRoom.value.ttlockName = device.lockName

    // 清除选择状态
    currentSelectingRoom.value = null
    currentSelectingIndex.value = -1

    Message.success('设备选择成功')
}

// 检查设备是否已被选择
const isDeviceSelected = (device: TTLockDeviceVo) => {
    // 检查全局绑定映射中是否包含该设备（使用设备ID判断）
    return Array.from(globalBindingMap.value.values()).some(binding =>
        binding.ttlockDeviceId === device.id
    )
}

// 清除无效的关联关系
const clearInvalidBindings = (validDevices: any[]) => {
    const validDeviceIds = validDevices.map(device => device.id)

    // 清除全局绑定映射中无效的关联关系
    for (const [roomId, binding] of globalBindingMap.value.entries()) {
        if (!validDeviceIds.includes(binding.ttlockDeviceId)) {
            globalBindingMap.value.delete(roomId)
        }
    }

    // 清除当前房源列表中无效的关联关系
    roomList.value.forEach(room => {
        if (room.ttlockDeviceId && !validDeviceIds.includes(room.ttlockDeviceId)) {
            room.ttlockDeviceId = ''
            room.ttlockId = 0
            room.ttlockName = ''
        }
    })
}

// 清除设备绑定
const handleClearDevice = (record: any) => {
    // 从全局绑定映射中移除
    globalBindingMap.value.delete(record.roomId)

    // 清空房源的设备信息
    record.ttlockDeviceId = ''
    record.ttlockId = null
    record.ttlockName = ''

    Message.success('设备绑定已清除')
}

// 其他操作方法
const handleAutoMatch = async () => {
    try {
        await autoBindRoomDevice(projectInfo.value.projectId)
        Message.success('自动匹配完成')
        fetchRoomList(true) // 自动匹配后清空绑定关系，重新加载
    } catch (error) {
        console.error('自动匹配失败:', error)
    }
}

const handleSyncDevices = async () => {
    try {
        await syncDeviceData({
            pageNum: 1,
            pageSize: 10000,
            projectId: projectInfo.value.projectId,
            accountIdList: brandList.value.map(brand => brand.id)
        })
        Message.success('设备数据同步完成')
        fetchDeviceList()
    } catch (error) {
        console.error('同步设备数据失败:', error)
    }
}

const handleExportRooms = async () => {
    if (!projectInfo.value.projectId) {
        Message.warning('请先选择项目')
        return
    }

    try {
        // 构建导出查询参数，使用当前筛选条件
        const exportParams = {
            pageNum: 1,
            pageSize: 10000, // 导出时使用大页码
            projectId: projectInfo.value.projectId,
            bindFlag: roomFilter.bindFlag,
            parcelId: roomFilter.parcelId,
            buildingId: roomFilter.buildingId,
            roomName: roomFilter.roomName
        }

        await exportExcel(exportRoomList, exportParams, '房源列表')
        Message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
    }
}

const handleExportDevices = async () => {
    if (!projectInfo.value.projectId) {
        Message.warning('请先选择项目')
        return
    }

    // 如果没有品牌账号，不能导出设备
    if (brandList.value.length === 0) {
        Message.warning('请先添加设备品牌账号')
        return
    }

    try {
        // 构建导出查询参数，使用当前筛选条件
        const exportParams: TTLockDeviceInfoQueryDTO = {
            pageNum: 1,
            pageSize: 10000, // 导出时使用大页码
            deviceName: deviceFilter.deviceName,
            accountList: brandList.value.map(brand => brand.username)
        }

        await exportExcel(exportTTLockDeviceList, exportParams, '设备列表')
        Message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
    }
}

const handleImportBinding = () => {
    // 创建文件输入元素
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.xlsx,.xls'
    input.style.display = 'none'

    input.onchange = async (event: Event) => {
        const target = event.target as HTMLInputElement
        const file = target.files?.[0]

        if (!file) return

        // 检查文件类型
        const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']
        if (!allowedTypes.includes(file.type)) {
            Message.error('请选择Excel文件（.xlsx或.xls格式）')
            return
        }

        // 检查文件大小（限制10MB）
        if (file.size > 10 * 1024 * 1024) {
            Message.error('文件大小不能超过10MB')
            return
        }

        importLoading.value = true
        try {
            await importExcelFile(file, importRoomDeviceBind)
            Message.success('导入成功')

            // 导入成功后重新加载数据
            fetchRoomList(true) // 清空绑定关系，重新加载
        } catch (error) {
            console.error('导入失败:', error)
        } finally {
            importLoading.value = false
            // 清理input元素
            document.body.removeChild(input)
        }
    }

    // 添加到DOM并触发点击
    document.body.appendChild(input)
    input.click()
}

const handleSave = async () => {
    // 获取已绑定的房源数据
    const bindingData = roomList.value.filter(room => room.ttlockId && room.ttlockName)

    if (bindingData.length === 0) {
        Message.warning('请至少绑定一个设备')
        return
    }

    try {
        const data = bindingData.map(room => ({
            roomId: room.roomId,
            roomName: room.roomName,
            parcelName: room.parcelName,
            buildingName: room.buildingName,
            fullName: room.fullName,
            ttlockDeviceId: room.ttlockDeviceId,
            ttlockId: room.ttlockId,
            ttlockName: room.ttlockName
        }))

        await bindRoomDevice(data)
        emit('success')
        handleCancel()
    } catch (error) {
        console.error('绑定失败:', error)
    }
}

const handleCancel = () => {
    // 重置数据
    currentSelectingRoom.value = null
    currentSelectingIndex.value = -1
    // 触发取消事件
    emit('cancel')
}

// 加载地块数据
const loadParcelList = async (projectId: string) => {
    if (!projectId) return
    parcelLoading.value = true
    try {
        const res = await getParcelList(projectId)
        if (res && res.data) {
            parcelOptions.value = res.data
        }
    } catch (error) {
        console.error('获取地块列表失败:', error)
        parcelOptions.value = []
    } finally {
        parcelLoading.value = false
    }
}

// 加载楼栋数据
const loadBuildingList = async (parcelId: string) => {
    if (!parcelId) return
    buildingLoading.value = true
    try {
        const res = await getBuildingSelectList(parcelId)
        if (res && res.data) {
            buildingOptions.value = res.data
        }
    } catch (error) {
        console.error('获取楼栋列表失败:', error)
        buildingOptions.value = []
    } finally {
        buildingLoading.value = false
    }
}

// 处理地块变化
const handleParcelChange = (parcelId: string) => {
    // 清空楼栋选择
    roomFilter.buildingId = ''
    buildingOptions.value = []

    // 如果选择了地块，加载对应的楼栋列表
    if (parcelId) {
        loadBuildingList(parcelId)
    }
}

// show 方法 - 用于初始化组件
const show = (params: { projectId: string, projectName: string }) => {
    // 设置项目信息
    projectInfo.value = {
        projectId: params.projectId,
        projectName: params.projectName
    }

    // 初始化数据
    fetchBrandList() // 会自动调用fetchDeviceList
    loadParcelList(params.projectId) // 加载地块列表
    fetchRoomList(true) // 初始化时清空绑定关系
}

// 暴露方法给父组件
defineExpose({
    show
})


</script>

<style lang="less" scoped>
.project-info {
    margin-bottom: 16px;
    font-weight: 500;
}

.brand-section {
    margin-bottom: 24px;

    .brand-cards {
        display: flex;
        gap: 12px;
        margin-top: 16px;
        width: 100%;
        overflow: auto;
        .brand-card {
            position: relative;
            padding: 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            text-align: center;
            justify-content: space-between;

            &:hover {
                border-color: #1890ff;
            }

            &.add-brand {
                background-color: #f0f0f0;
                color: #666;
                justify-content: center;
                cursor: pointer;
            }

            .brand-label {
                flex: 1;
            }

            .delete-icon {
                color: #333333;
                cursor: pointer;
                font-size: 14px;
                position: absolute;
                right: 0;
                top: 0;
            }
        }
    }
}

.main-content {
    display: flex;
    gap: 24px;
    height: 600px;
    
    .left-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .right-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .header-actions {
        display: flex;
        gap: 8px;
    }
    
    .filter-section {
        margin-bottom: 16px;
    }
    
    .room-table,
    .device-table {
        flex: 1;
        overflow: hidden;

    }
}

.footer-actions {
    margin-top: 24px;
    text-align: right;
    
    .arco-btn + .arco-btn {
        margin-left: 12px;
    }
}
</style>
