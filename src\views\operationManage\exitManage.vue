<template>
    <div class="container">
        <div class="content">
            <!-- 顶部状态切换 -->
            <a-tabs v-model:activeKey="activeType" size="large" hide-content @change="handleChangeType"
                style="margin-bottom: 16px;">
                <a-tab-pane v-for="item in typeOptions" :key="item.value" :title="item.label" />
            </a-tabs>
            <a-card class="general-card">
                <!-- 搜索区域 -->
                <a-row>
                    <a-col :flex="1">
                        <a-form :model="formModel" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
                            label-align="right">
                            <a-row :gutter="16">
                                <a-col :span="8">
                                    <a-form-item field="projectId" label="项目">
                                        <ProjectTreeSelect v-model="formModel.projectId"
                                            @change="handleProjectChange" />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="tenantName" label="承租方">
                                        <a-input v-model="formModel.tenantName" placeholder="请输入承租方名称" allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="buildingOrHouseName" label="楼栋/房源">
                                        <a-input v-model="formModel.buildingOrHouseName" placeholder="请输入楼栋或房源名称"
                                            allow-clear />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item field="leaseStartDateRange" label="退租日期">
                                        <a-range-picker v-model="formModel.leaseStartDateRange" style="width: 100%" />
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </a-form>
                    </a-col>
                    <a-divider style="height: 84px" direction="vertical" />
                    <a-col :flex="'86px'" style="text-align: right">
                        <a-space direction="vertical" :size="18">
                            <a-button type="primary" @click="search">
                                <template #icon>
                                    <icon-search />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="reset">
                                <template #icon>
                                    <icon-refresh />
                                </template>
                                重置
                            </a-button>
                        </a-space>
                    </a-col>
                </a-row>

                <!-- 表格区域 -->
                <a-table row-key="id" :loading="loading" :pagination="pagination" :columns="columns" :data="tableData"
                    :scroll="{ x: 1 }" :bordered="{ cell: true }" @page-change="onPageChange"
                    @page-size-change="onPageSizeChange">

                    <template #contractNo="{ record }">
                        <a-tooltip :content="record.contractNo">
                            <div class="ellipsis-link" @click="handleContractDetail(record.contractId)">
                                {{ record.contractNo }}
                            </div>
                        </a-tooltip>
                    </template>

                    <template #progressStatus="{ record }">
                        <a-tag :color="getProgressStatusColor(record.progressStatus)">
                            {{ getProgressStatusText(record.progressStatus) }}
                        </a-tag>
                    </template>

                    <template #confirmStatus="{ record }">
                        <a-tag :color="getConfirmStatusColor(record.confirmStatus)">
                            {{ getConfirmStatusText(record.confirmStatus) }}
                        </a-tag>
                    </template>

                    <template #processType="{ record }">
                        <a-tag :color="getProcessTypeColor(record.processType)">
                            {{ getProcessTypeText(record.processType) }}
                        </a-tag>
                    </template>

                    <template #signType="{ record }">
                        <a-tag :color="getSignTypeColor(record.signType)">
                            {{ getSignTypeText(record.signType) }}
                        </a-tag>
                    </template>

                    <template #operations="{ record }">
                        <a-space>
                            <!-- 待办理状态 -->
                            <template v-if="activeType === 'pending'">
                                <a-button v-permission="['rent:exit:query']" type="text" size="mini" @click="handleExit(record)">
                                    办理出场
                                </a-button>
                            </template>

                            <!-- 办理中状态 -->
                            <template v-if="activeType === 'processing'">
                                <!-- 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废 -->
                                <a-button v-permission="['rent:exit:query']" type="text" size="mini" @click="handleContinueExit(record)" v-if="record.progressStatus === 5 || record.progressStatus === 10 || record.progressStatus === 15 || record.progressStatus === 20">
                                    继续办理
                                </a-button>
                                <a-button v-permission="['rent:exit:cancel']" type="text" size="mini" @click="handleCancelExit(record)" v-if="record.progressStatus === 5 || record.progressStatus === 15">
                                    作废
                                </a-button>

                                <!-- |signType|query|integer(int32)| 否 | 签字方式: 1-线上, 2-线下|签字方式: 1-线上, 2-线下| -->
                                <a-button v-permission="['rent:exit:print']" type="text" size="mini" @click="handlePrintExit(record)" v-if="record.progressStatus === 20 && record.signType === 2">
                                    打印出场单
                                </a-button>
                                <a-button v-permission="['rent:exit:uploadSignature']" type="text" size="mini" @click="handleUploadSignature(record)" v-if="record.progressStatus === 20 && record.signType === 2">
                                    <!-- <icon-upload /> -->
                                    上传签字单
                                </a-button>
                                <!-- refundStatus 123 不能编辑， 0和4 是发起退款申请， -->
                                <a-button v-permission="['rent:exit:refund:apply']" type="text" size="mini" @click="handleRefundApply(record)" v-if="record.refundStatus === 4 || record.refundStatus === 0">
                                    发起退款申请
                                    <!-- {{ record.refundStatus === 4 ? '发起退款申请' : '退款详情' }} -->
                                </a-button>
                                <!-- <a-button type="text" size="mini" @click="handleRefundDetail(record)" v-if="record.refundStatus === 1 || record.refundStatus === 2 || record.refundStatus === 3">
                                    退款详情
                                </a-button> -->
                                <a-button v-permission="['rent:exit:query']" type="text" size="mini" @click="handleViewDetail(record)" v-if="record.progressStatus === 20 || record.progressStatus === 25 || record.progressStatus === 30 || record.progressStatus === 40">
                                    详情
                                </a-button>
                                <!-- <a-button type="text" size="mini" @click="handleExitSave(record)" v-if="record.progressStatus === 20">
                                    完成签字
                                </a-button> -->
                                <!-- <a-dropdown>
                                    <a-button type="text" size="mini">
                                        更多操作
                                        <icon-down />
                                    </a-button>
                                    <template #content>
                                        <a-doption @click="handleCancelExit(record)">
                                            <icon-close />
                                            作废
                                        </a-doption>
                                        <a-doption @click="handlePrintExit(record)">
                                            <icon-printer />
                                            打印出场单
                                        </a-doption>
                                        <a-doption @click="handleUploadSignature(record)">
                                            <icon-upload />
                                            上传签字单
                                        </a-doption>
                                        <a-doption @click="handleRefundApply(record)">
                                            <icon-export />
                                            发起退款申请
                                        </a-doption>
                                        <a-doption @click="handleViewDetail(record)">
                                            <icon-eye />
                                            详情
                                        </a-doption>
                                    </template>
    </a-dropdown> -->
                            </template>

                            <!-- 已办理状态 -->
                            <template v-if="activeType === 'handled'">
                                <a-button v-permission="['rent:exit:query']" type="text" size="mini" @click="handleViewDetail(record)">
                                    详情
                                </a-button>
                                <a-button v-permission="['rent:exit:print']" type="text" size="mini" @click="handlePrintExit(record)">
                                    打印出场单
                                </a-button>
                            </template>
                        </a-space>
                    </template>
                </a-table>
            </a-card>
        </div>

        <!-- 办理出场抽屉 -->
        <a-drawer v-model:visible="exitDrawerVisible" title="办理出场" width="1200px" :footer="false"
            @cancel="handleExitCancel" class="common-drawer">
            <exit-handler v-if="exitDrawerVisible" :data="currentExitData || {}" :mode="exitMode"
                @cancel="handleExitCancel" @save="handleExitSave" @mode-change="handleModeChange" />
        </a-drawer>

        <!-- 出场办理模态框 -->
        <a-modal v-model:visible="exitProcessModalVisible" title="出场办理" :footer="false"
            @cancel="handleExitProcessCancel">
            <exit-process v-if="exitProcessModalVisible" :data="currentExitData || {}" @cancel="handleExitProcessCancel"
                @property-only="handlePropertyOnly" @property-and-settlement="handlePropertyAndSettlement" />
        </a-modal>

        <!-- 上传签字单模态框 -->
        <a-modal v-model:visible="uploadSignatureVisible" title="上传签字单" :footer="false"
            @cancel="handleUploadSignatureCancel">
            <div v-if="uploadSignatureVisible">
                <a-form :model="uploadForm" layout="vertical">
                    <a-form-item label="选择签字单文件">
                        <upload-file v-model="uploadForm.fileList" :limit="5" accept=".pdf,.jpg,.jpeg,.png"
                            :max-size="10" />
                    </a-form-item>
                </a-form>
                <div style="text-align: center;">
                    <!-- <a-space > -->
                    <a-button type="primary" @click="submitUploadSignature">确认上传</a-button>
                    <a-button @click="handleUploadSignatureCancel" style="margin-left: 16px;">取消</a-button>
                    <!-- </a-space> -->
                </div>
            </div>
        </a-modal>

        <!-- 退款申请组件 -->
        <ExitRefundApplyForm ref="exitRefundApplyFormRef" />

        <!-- 选择签字方式模态框 -->
        <a-modal v-model:visible="signTypeModalVisible" title="签字方式选择" width="520px" :footer="false"
            @cancel="handleSignTypeCancel">
            <div v-if="signTypeModalVisible" >
                <div style="margin-bottom: 16px;">
                    <div style="margin-bottom: 16px;display: flex;align-items: center;">
                        <div style="font-size: 16px; font-weight: 500; color: #333;width: 100px;">签字方式：</div>
                        <a-radio-group v-model="selectedSignType" style="width: 0;flex: 1;margin-left: 16px;display: flex;align-items: center;">
                            <div >
                                <a-radio :value="1">线上</a-radio>
                            </div>
                            <div>
                                <a-radio :value="2">线下</a-radio>
                            </div>
                        </a-radio-group>
                    </div>
                    
                    <div style="background-color: #f7f8fa; padding: 16px; border-radius: 6px; margin-bottom: 16px;">
                        <div style="font-size: 14px; color: #666; line-height: 1.5;">
                            <div style="font-weight: 500; margin-bottom: 8px;">提示：</div>
                            <div>1、线上签字：系统将短信推送出场确认单给客户线上签字</div>
                            <div>2、线下签字：可打印出场单，线下签字后再上传</div>
                        </div>
                    </div>
                    
                    <div style="text-align: center;">
                        <a-space size="medium">
                            <a-button type="primary" @click="handleConfirmSignType" :disabled="!selectedSignType">
                                确认
                            </a-button>
                            <a-button @click="handleSignTypeCancel">
                                取消
                            </a-button>
                        </a-space>
                    </div>
                </div>
            </div>
        </a-modal>

        <!-- 查看详情抽屉 -->
        <a-drawer v-model:visible="detailDrawerVisible" title="出场详情" width="1200px" :footer="false"
            @cancel="handleDetailCancel" class="common-drawer">
            <exit-handler v-if="detailDrawerVisible" :data="currentDetailData || {}" :mode="'view'"
                @cancel="handleDetailCancel" />
        </a-drawer>

        <!-- 合同详情抽屉 -->
        <ContractDetailDrawer ref="contractDetailDrawerRef" @submit="handleContractDetailUpdate" />

        <!-- 添加 WordViewer 组件 -->
        <WordViewer
            v-model="previewVisible"
            :file-url="previewFileUrl"
            :title="previewTitle"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
    getExitList,
    addExit,
    updateExit,
    deleteExit,
    saveExitSettlement,
    saveExitRoom,
    batchUpdateExitRoom,
    uploadExitSignature,
    copyExitPropertyUrl,
    cancelExit,
    printExit,
    exportExitList
} from '@/api/operationManage'
import type {
    ExitInfo,
    ExitListParams,
    ExitAddDTO,
    ExitRoomAddDTO,
    AjaxResult,
    PageResult
} from '@/types/exit'
import sectionTitle from '@/components/sectionTitle/index.vue'
import ExitHandler from './components/exitHandlerNew.vue'
import ExitProcess from './components/exitProcess.vue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import ContractDetailDrawer from '@/views/contract/contractDetailDrawer.vue'
import UploadFile from '@/components/upload/uploadFile.vue'
import ExitRefundApplyForm from './components/exitRefundApplyForm.vue'
import WordViewer from '@/components/WordViewer/index.vue'
import { getDictLabel } from '@/dict'
// 解决TypeScript类型推断问题
// defineExpose({})

// 类型定义

interface TableItem {
    id: string
    contractId: string
    contractNo: string
    contractPurpose: string
    tenantName: string
    exitType?: string  // 改为可选属性
    exitDate: string
    exitHouses: string
    houseCount: number
    progressStatus: number // 办理进度状态
    projectId?: string
    processType?: number // 办理流程: 1-先交割后结算, 2-交割并结算
    signType?: number // 签字方式: 1-线上, 2-线下
    // 新增字段
    shouldHandleDate?: string // 应办理时间
    createByName?: string // 创建人
    createTime?: string // 创建时间
    handlerName?: string // 办理人
    handleTime?: string // 办理时间
    expectedFinishTime?: string // 预计完成时间
    actualFinishTime?: string // 实际完成时间
    settlementAmount?: number // 结算金额
    confirmStatus?: number // 确认状态
    [key: string]: any
}

// 视图类型
const activeType = ref('pending')
const typeOptions = ref([
    {
        value: 'pending',
        label: '待办理'
    },
    {
        value: 'processing',
        label: '办理中'
    },
    {
        value: 'handled',
        label: '已办理'
    }
])

// 退款申请组件
const exitRefundApplyFormRef = ref<InstanceType<typeof ExitRefundApplyForm> | null>(null)
// const refundApplyFormVisible = ref(false)
// const refundApplyFormRef2     = ref(true)


// 表格数据
const loading = ref(false)
const tableData = ref<TableItem[]>([
    // {
    //     id: '1',
    //     contractId: 'C202301001',
    //     contractNo: 'HT-2023-001',
    //     contractPurpose: '商业办公',
    //     tenantName: '北京科技有限公司',
    //     exitType: '到期退租',
    //     exitDate: '2023-12-31',
    //     exitHouses: 'A1栋101室, A1栋102室',
    //     houseCount: 2
    // },
    // {
    //     id: '2',
    //     contractId: 'C202301002',
    //     contractNo: 'HT-2023-002',
    //     contractPurpose: '居住',
    //     tenantName: '张三',
    //     exitType: '提前退租',
    //     exitDate: '2023-08-15',
    //     exitHouses: 'B2栋303室',
    //     houseCount: 1
    // },
    // {
    //     id: '3',
    //     contractId: 'C202301003',
    //     contractNo: 'HT-2023-003',
    //     contractPurpose: '商业零售',
    //     tenantName: '上海商贸有限公司',
    //     exitType: '到期退租',
    //     exitDate: '2023-03-14',
    //     exitHouses: 'C1栋一层商铺, C1栋二层商铺',
    //     houseCount: 2
    // }
])


// 基础表格列
const baseColumns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 70,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 操作列
const operationColumns = [
    {
        title: '操作',
        slotName: 'operations',
        width: 240,
        fixed: 'right',
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 动态表格列定义
const columns = ref<any[]>([])

// 分页
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
    showTotal: true,
    showPageSize: true
})

// 表单
const formModel = reactive({
    projectId: undefined as string | undefined,
    tenantName: '',
    buildingOrHouseName: '',
    leaseStartDateRange: [] as any[]
})

// 办理出场抽屉
const exitDrawerVisible = ref(false)
const currentExitData = ref<TableItem | null>(null)
const exitMode = ref('property-only')

// 出场办理模态框
const exitProcessModalVisible = ref(false)

// 上传签字单相关
const uploadForm = reactive({
    fileList: ''
})

// 选择签字方式相关
const signTypeModalVisible = ref(false)
const currentSignRecord = ref<TableItem | null>(null)
const selectedSignType = ref<number | undefined>(undefined)

// 添加预览相关的响应式变量
const previewVisible = ref(false)
const previewFileUrl = ref('')
const previewTitle = ref('')

// 获取办理进度状态文本
const getProgressStatusText = (status: number) => {
    // 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废
    switch (status) {
        case 0:
            return '不可见'
        case 1:
            return '待办理'
        case 5:
            return '物业交割' 
        case 10:
            return '费用结算'
        case 15:
            return '交割并结算'
        case 20:
            return '客户签字'
        case 25:
            return '发起退款'
        case 30:
            return '已完成'
        case 40:
            return '已作废'
        default:
            return '未知'
    }
}

// 获取办理进度状态颜色
const getProgressStatusColor = (status: number) => {
    switch (status) {
        case 0:
            return 'gray'
        case 1:
            return 'blue'
        case 5:
            return 'blue'
        case 10:
            return 'green'
        case 15:
            return 'orange'
        case 20:
            return 'green'
        case 25:
            return 'red'
        case 30:
            return 'blue'
        case 40:
            return 'red'
        default:
            return 'default'
    }
}

// 获取确认状态文本
const getConfirmStatusText = (status: number) => {
    switch (status) {
        case 0:
            return '待确认'
        case 1:
            return '已确认'
        case 2:
            return '已拒绝'
        default:
            return '未知'
    }
}

// 获取确认状态颜色
const getConfirmStatusColor = (status: number) => {
    switch (status) {
        case 0:
            return 'orange'
        case 1:
            return 'green'
        case 2:
            return 'red'
        default:
            return 'default'
    }
}

// 获取办理流程文本
const getProcessTypeText = (processType: number) => {
    switch (processType) {
        case 1:
            return '先交割后结算'
        case 2:
            return '交割并结算'
        default:
            return '未知'
    }
}

// 获取办理流程颜色
const getProcessTypeColor = (processType: number) => {
    switch (processType) {
        case 1:
            return 'blue'
        case 2:
            return 'green'
        default:
            return 'default'
    }
}

// 获取签字方式文本
const getSignTypeText = (signType: number) => {
    switch (signType) {
        case 1:
            return '线上签字'
        case 2:
            return '线下签字'
        default:
            return '未设置'
    }
}

// 获取签字方式颜色
const getSignTypeColor = (signType: number) => {
    switch (signType) {
        case 1:
            return 'blue'
        case 2:
            return 'orange'
        default:
            return 'gray'
    }
}

// 生命周期
onMounted(async () => {
    // 初始化表格列
    updateTableColumns(activeType.value)

    await fetchTableData()
})

// 切换视图类型
const handleChangeType = (key: string) => {
    activeType.value = key

    // 根据状态更新表格列
    updateTableColumns(key)

    fetchTableData()
}

// 更新表格列
const updateTableColumns = (type: string) => {
    switch (type) {
        case 'pending':
            // 待办理状态：基础列 + 应办理时间 + 创建信息
            columns.value = [
                ...baseColumns,
                {
                    title: '合同编号',
                    dataIndex: 'contractNo',
                    slotName: 'contractNo',
                    width: 280,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '合同用途',
                    dataIndex: 'contractPurpose',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '承租方',
                    dataIndex: 'tenantName',
                    width: 150,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租类型',
                    dataIndex: 'terminateType',
                    slotName: 'terminateType',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租日期',
                    dataIndex: 'exitDate',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租房源',
                    dataIndex: 'exitHouses',
                    width: 200,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '房源数',
                    dataIndex: 'houseCount',
                    width: 80,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                ...operationColumns
            ]
            break

        case 'processing':
            // 办理中状态：基础列 + 办理进度 + 办理信息
            // operationColumns[0]['width']
            columns.value = [
                ...baseColumns,
                {
                    title: '办理进度',
                    dataIndex: 'progressStatus',
                    slotName: 'progressStatus',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '合同编号',
                    dataIndex: 'contractNo',
                    slotName: 'contractNo',
                    width: 280,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '合同用途',
                    dataIndex: 'contractPurpose',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '承租方',
                    dataIndex: 'tenantName',
                    width: 150,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租类型',
                    dataIndex: 'terminateType',
                    slotName: 'terminateType',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租日期',
                    dataIndex: 'exitDate',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租房源',
                    dataIndex: 'exitHouses',
                    width: 200,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '费用合计',
                    dataIndex: 'finalAmount',
                    width: 200,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '办理流程',
                    dataIndex: 'processType',
                    slotName: 'processType',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '签字方式',
                    dataIndex: 'signType',
                    slotName: 'signType',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '操作',
                    slotName: 'operations',
                    width: 330,
                    fixed: 'right',
                    align: 'center'
                }
            ]
            break

        case 'handled':
            // 已办理状态：基础列 + 完成信息 + 结算信息
            columns.value = [
                ...baseColumns,
                {
                    title: '合同编号',
                    dataIndex: 'contractNo',
                    slotName: 'contractNo',
                    width: 280,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '合同用途',
                    dataIndex: 'contractPurpose',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '承租方',
                    dataIndex: 'tenantName',
                    width: 150,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租类型',
                    dataIndex: 'terminateType',
                    slotName: 'terminateType',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租日期',
                    dataIndex: 'exitDate',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租房源',
                    dataIndex: 'exitHouses',
                    width: 200,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '费用合计',
                    dataIndex: 'finalAmount',
                    width: 200,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                ...operationColumns
            ]
            break

        case 'processing':
            // 办理中状态：基础列 + 办理进度 + 办理信息
            columns.value = [
                ...baseColumns,
                {
                    title: '合同编号',
                    dataIndex: 'contractNo',
                    slotName: 'contractNo',
                    width: 140,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '合同用途',
                    dataIndex: 'contractPurpose',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '承租方',
                    dataIndex: 'tenantName',
                    width: 150,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租类型',
                    dataIndex: 'terminateType',
                    slotName: 'terminateType',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租日期',
                    dataIndex: 'exitDate',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '退租房源',
                    dataIndex: 'exitHouses',
                    width: 200,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                {
                    title: '费用合计',
                    dataIndex: 'finalAmount',
                    width: 200,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true,
                },
                ...operationColumns
            ]
            break

        default:
            columns.value = [...baseColumns, ...operationColumns]
    }
}



// 获取表格数据
const fetchTableData = async () => {
    loading.value = true
    try {
        const params: ExitListParams = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            projectId: formModel.projectId,
            customerName: formModel.tenantName,
            buildingOrRoomName: formModel.buildingOrHouseName,
            terminateStartDate: formModel.leaseStartDateRange[0] ? formModel.leaseStartDateRange[0] : undefined,
            terminateEndDate: formModel.leaseStartDateRange[1] ? formModel.leaseStartDateRange[1] : undefined,
            status: getStatusByViewType()
        }

        const res: any = await getExitList(params)
        console.log(res)
        if (res && res.code === 200 && res.rows) {
            tableData.value = (res.rows || []).map((item: any, index: number) => {
                const tableItem: TableItem = {
                    id: item.id || '',
                    contractId: item.contractId,
                    contractNo: item.contractNo,
                    contractPurpose: getContractPurposeText(item.contractPurpose),
                    tenantName: item.customerName || '',
                    terminateType: getTerminateTypeText(item.terminateType),
                    exitDate: item.terminateDate || '',
                    exitHouses: item.terminateRoomName || '',
                    houseCount: item.terminateRoomCount || 0,
                    progressStatus: item.progressStatus || 0,
                    projectId: item.projectId,
                    processType: item.processType || 0,
                    signType: item.signType || 0,
                    // 新增字段
                    shouldHandleDate: item.shouldHandleDate || '',
                    createByName: item.createByName || '',
                    createTime: item.createTime || '',
                    handlerName: item.handlerName || '',
                    handleTime: item.handleTime || '',
                    expectedFinishTime: item.expectedFinishTime || '',
                    actualFinishTime: item.actualFinishTime || '',
                    settlementAmount: item.settlementAmount || 0,
                    confirmStatus: item.confirmStatus || 0,
                    index: (pagination.current - 1) * pagination.pageSize + index + 1
                }
                // 添加其他属性
                Object.keys(item).forEach(key => {
                    if (!(key in tableItem)) {
                        (tableItem as any)[key] = (item as any)[key]
                    }
                })
                return tableItem
            })
            pagination.total = res.total
        }
    } catch (error) {
        console.error('获取出场管理列表失败', error)
        // Message.error('获取出场管理列表失败')
    } finally {
        loading.value = false
    }
}

// 根据视图类型获取状态值
const getStatusByViewType = () => {
    switch (activeType.value) {
        case 'pending':
            return 0 // 待办理
        case 'processing':
            return 1 // 办理中（物业交割）
        case 'handled':
            return 2 // 已完成
        default:
            return 1
    }
}

// 获取退租类型文本
const getExitTypeText = (type?: number) => {
    switch (type) {
        case 1:
            return '正常退租'
        case 2:
            return '提前退租'
        case 3:
            return '违约退租'
        default:
            return '未知'
    }
}
// 获取退租类型文本
// 0 - 到期退租
// 1 - 提前退租
const getTerminateTypeText = (type?: number) => {
    switch (type) {
        case 0:
            return '到期退租'
        case 1:
            return '提前退租'
        default:
            return '未知'
    }
}

// 获取合同用途文本
const getContractPurposeText = (purpose?: number) => {
    return getDictLabel('diversification_purpose', purpose as number)
}

// 查询
const search = () => {
    pagination.current = 1
    fetchTableData()
}

// 重置
const reset = () => {
    formModel.tenantName = ''
    formModel.buildingOrHouseName = ''
    formModel.leaseStartDateRange = []
    pagination.current = 1
    fetchTableData()
}

// 项目变化处理
const handleProjectChange = (value: string) => {
    formModel.projectId = value
    // 可以在这里添加项目变化后的逻辑，比如清空其他相关字段
    search()
}

// 分页变化
const onPageChange = (page: number) => {
    pagination.current = page
    fetchTableData()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    fetchTableData()
}

// 合同详情抽屉引用
const contractDetailDrawerRef = ref()

// 查看合同详情
const handleContractDetail = (contractId: string) => {
    console.log('查看合同详情', contractId)
    contractDetailDrawerRef.value?.open({ id: contractId })
}

// 合同详情更新回调
const handleContractDetailUpdate = () => {
    // 合同详情更新后，刷新出场管理列表
    fetchTableData()
}

// 办理出场
const handleExit = (record: TableItem) => {
    currentExitData.value = record
    exitProcessModalVisible.value = true
}

// 取消出场办理
const handleExitProcessCancel = () => {
    exitProcessModalVisible.value = false
    currentExitData.value = null
}

// 先物业交割，后续结算
const handlePropertyOnly = () => {
    exitProcessModalVisible.value = false
    exitMode.value = 'property-only'
    exitDrawerVisible.value = true
    fetchTableData()
}

// 物业交割并结算
const handlePropertyAndSettlement = () => {
    exitProcessModalVisible.value = false
    exitMode.value = 'property-and-settlement'
    exitDrawerVisible.value = true
    fetchTableData()
}

// 取消办理出场
const handleExitCancel = () => {
    exitDrawerVisible.value = false
    currentExitData.value = null
}

// 保存办理出场 - 弹出签字方式选择框
const handleExitSave = (record: TableItem) => {
    // 关闭抽屉并刷新列表
    exitDrawerVisible.value = false
    fetchTableData()
    
    if (!record.isSubmit) {
        // 如果是暂存，直接关闭抽屉并刷新列表
        return
    }
    
    // 如果是提交，则弹出签字方式选择框
    currentSignRecord.value = record
    signTypeModalVisible.value = true
}

// 取消选择签字方式
const handleSignTypeCancel = () => {
    signTypeModalVisible.value = false
    currentSignRecord.value = null
    selectedSignType.value = undefined
}

// 确认签字方式
const handleConfirmSignType = async () => {
    if (!selectedSignType.value) {
        Message.warning('请选择签字方式')
        return
    }
    
    if (selectedSignType.value === 1) {
        // 线上签字
        await handleOnlineSign()
    } else if (selectedSignType.value === 2) {
        // 线下签字
        await handleOfflineSign()
    }
}

// 选择线上签字
const handleOnlineSign = async () => {
    try {
        // 调用保存接口，标记为线上签字 (1-线上)
        const data = {
            ...currentSignRecord.value,
            signType: 1
        }
        const res = await saveExitSettlement(data)
        if (res && res.code === 200) {
            Message.success('已提交，等待客户线上签字')
            signTypeModalVisible.value = false
            selectedSignType.value = undefined
            currentSignRecord.value = null
            fetchTableData()
        }
    } catch (error) {
        console.error('提交失败', error)
        // Message.error('提交失败')
    }
}

// 选择线下签字
const handleOfflineSign = async () => {
    try {
        // 调用保存接口，标记为线下签字 (2-线下)
        const data = {
            ...currentSignRecord.value,
            signType: 2
        }
        const res = await saveExitSettlement(data)
        if (res && res.code === 200) {
            Message.success('出场办理成功')
            // 弹出上传签字单的模态框
            currentUploadRecord.value = currentSignRecord.value
            uploadSignatureVisible.value = true
            signTypeModalVisible.value = false
            selectedSignType.value = undefined
            currentSignRecord.value = null
            fetchTableData()
        }
    } catch (error) {
        console.error('提交失败', error)
        // Message.error('提交失败')
    }
}

// 继续办理（办理中状态）
const handleContinueExit = (record: TableItem) => {
    currentExitData.value = record
    exitMode.value = record.processType === 1 ? 'property-only' : 'property-and-settlement'
    // 根据当前进度状态选择办理模式
    if (record.progressStatus === 5) {
        // 物业交割阶段
        // exitMode.value = 'property-only'
    } else if (record.progressStatus === 10) {
        // 费用结算阶段
        // exitMode.value = 'settlement-only'
    } else if (record.progressStatus === 15) {
        // 交割并结算阶段
        // exitMode.value = 'property-and-settlement'
    }
    exitDrawerVisible.value = true
}

// 作废出场单
const handleCancelExit = (record: TableItem) => {
    Modal.confirm({
        title: '确认作废',
        content: `确定要作废出场单 "${record.contractNo}" 吗？此操作不可撤销。`,
        okText: '确认作废',
        cancelText: '取消',
        okButtonProps: {
            // status: ''
        },
        onOk: async () => {
            try {
                const res = await cancelExit({ exitId: record.id })
                if (res && res.code === 200) {
                    Message.success('作废成功')
                    fetchTableData()
                }
            } catch (error) {
                console.error('作废失败', error)
                // Message.error('作废失败')
            }
        }
    })
}

// 打印出场单
const handlePrintExit = async (record: TableItem) => {
    try {
        const res = await printExit(record.id)
        if (res && res.code === 200) {
            // 使用 WordViewer 预览文件
            if (res.data.fileUrl) {
                previewFileUrl.value = res.data.fileUrl
                previewTitle.value = '出场单打印文件'
                previewVisible.value = true
                // Message.success('生成打印文件成功')
            }
        }
    } catch (error) {
        console.error('打印失败', error)
    }
}

// 上传签字单
const uploadSignatureVisible = ref(false)
const currentUploadRecord = ref<TableItem | null>(null)

const handleUploadSignature = (record: TableItem) => {
    currentUploadRecord.value = record
    uploadSignatureVisible.value = true
}

const handleUploadSignatureCancel = () => {
    uploadSignatureVisible.value = false
    currentUploadRecord.value = null
}

const handleUploadSignatureSubmit = async (data: any) => {
    try {
        const res = await uploadExitSignature({
            exitId: currentUploadRecord.value?.id || '',
            signatureFiles: data.signatureFiles
        })
        if (res && res.code === 200) {
            Message.success('上传签字单成功')
            uploadSignatureVisible.value = false
            fetchTableData()
        }
    } catch (error) {
        console.error('上传签字单失败', error)
        // Message.error('上传签字单失败')
    }
}

// 查看详情
const detailDrawerVisible = ref(false)
const currentDetailData = ref<TableItem | null>(null)

const handleViewDetail = (record: TableItem) => {
    currentDetailData.value = record
    detailDrawerVisible.value = true
}

const handleDetailCancel = () => {
    detailDrawerVisible.value = false
    currentDetailData.value = null
}

// 提交上传签字单
const submitUploadSignature = async () => {
    if (!uploadForm.fileList) {
        Message.warning('请先上传签字单文件')
        return
    }

    try {
        const res = await uploadExitSignature({
            exitId: currentUploadRecord.value?.id || '',
            signatureFiles: uploadForm.fileList
        })
        if (res && res.code === 200) {
            Message.success('上传签字单成功')
            uploadSignatureVisible.value = false
            // 重置表单
            uploadForm.fileList = ''
            fetchTableData()
        }
    } catch (error) {
        console.error('上传签字单失败:', error)
    }
}

// 处理模式变更
const handleModeChange = (newMode: string) => {
    exitMode.value = newMode
}

// 发起退款申请
const handleRefundApply = (record: TableItem) => {
    // 根据 refundStatus 决定是查看模式还是编辑模式
    // const mode = record.refundStatus === 0 ? 'view' : 'create';
    exitRefundApplyFormRef.value?.open({ record, mode: 'create'});
}

// 退款详情
const handleRefundDetail = (record: TableItem) => {
    // 打开退款申请表单的只读模式
    exitRefundApplyFormRef.value?.open({ 
        record, 
        mode: 'view' // 查看模式
    });
}
</script>

<style scoped>
:deep(.arco-collapse-item-header-right + .arco-collapse-item-content) {
    padding-left: 0 !important;
}

:deep(.arco-collapse-item-content) {
    margin-right: 0 !important;
}

.container {
    padding: 0 16px;

    .content {
        background-color: #fff;
        border-radius: 4px;
    }
}

:deep(.arco-table-th) {
    background-color: var(--color-fill-2);
}

:deep(.arco-card) {
    height: 100%;
}

.upload-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: border-color 0.3s;
}

.upload-btn:hover {
    border-color: #165dff;
}

:deep(.arco-dropdown-option) {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ellipsis-link {
    color: #165dff;
    cursor: pointer;
    text-decoration: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ellipsis-link:hover {
    color: #0e42d2;
    text-decoration: underline;
}
</style>