# 智能设备父子组件交互优化

## 问题描述

1. 项目切换函数只有初次加载页面才调用查询
2. 查询和重置按钮没有反应
3. 需要通过 defineExpose 和 ref 方式实现父子组件交互
4. 后续会加上智能水电，需要判断当前tab调用对应的子组件列表接口

## 解决方案

### 1. 父组件 (src/views/smartDevices/index.vue) 修改

#### 添加子组件引用和状态管理
```typescript
// 子组件引用
const waterElectricTabRef = ref()
const doorLockTabRef = ref()

// 初始化状态
const isInit = ref(false)

// 存储当前选中的项目信息
const currentProject = ref<any>({
    projectId: '',
    projectName: ''
})
```

#### 模板添加 ref 引用
```vue
<water-electric-tab v-if="activeTab === 'water-electric'"
  ref="waterElectricTabRef"
  :filter-form="filterForm"
  v-model:selected-keys="selectedKeys"
  @update:loading="loading = $event"
  @edit-device="handleEditDevice"
  @view-detail="handleViewDetail"
/>
<door-lock-tab v-if="activeTab === 'door-lock'"
  ref="doorLockTabRef"
  :filter-form="filterForm"
  v-model:selected-keys="selectedKeys"
  @update:loading="loading = $event"
  @edit-device="handleEditDevice"
  @view-detail="handleViewDetail"
/>
```

#### 核心函数实现

**调用当前激活tab的列表接口**
```typescript
const loadData = () => {
  if (activeTab.value === 'water-electric' && waterElectricTabRef.value) {
    waterElectricTabRef.value.fetchTableData()
  } else if (activeTab.value === 'door-lock' && doorLockTabRef.value) {
    doorLockTabRef.value.fetchTableData()
  }
}
```

**项目变更处理 - 参考 reduction/index.vue**
```typescript
const handleProjectChange = (value: string | number, selectedOrg: any) => {
  console.log('项目变化，检查是否有项目ID再调用列表', value, selectedOrg)

  // 存储项目信息
  currentProject.value = {
    projectId: value,
    projectName: selectedOrg?.name || ''
  }

  filterForm.projectId = value as string

  // 只有在有项目ID时才自动触发搜索
  if (value && !isInit.value) {
    isInit.value = true
    // 重置分页到第一页
    if (activeTab.value === 'water-electric' && waterElectricTabRef.value) {
      waterElectricTabRef.value.resetPagination()
    } else if (activeTab.value === 'door-lock' && doorLockTabRef.value) {
      doorLockTabRef.value.resetPagination()
    }
    loadData()
  }
}
```

**查询处理**
```typescript
const handleSearch = () => {
  console.log('查询条件:', filterForm)
  // 重置分页到第一页
  if (activeTab.value === 'water-electric' && waterElectricTabRef.value) {
    waterElectricTabRef.value.resetPagination()
  } else if (activeTab.value === 'door-lock' && doorLockTabRef.value) {
    doorLockTabRef.value.resetPagination()
  }
  loadData()
}
```

**重置处理**
```typescript
const handleReset = () => {
  filterForm.projectId = ''
  filterForm.buildingOrRoom = ''
  selectedKeys.value = []
  isInit.value = false
  currentProject.value = {
    projectId: '',
    projectName: ''
  }
  // 重置后不自动调用查询，等待用户选择项目
}
```

**Tab切换处理**
```typescript
const handleTabChange = (key: string) => {
  activeTab.value = key
  selectedKeys.value = []
  // Tab切换时，如果有项目ID则调用查询
  if (filterForm.projectId) {
    loadData()
  }
}
```

### 2. 子组件修改

#### DoorLockTab.vue 和 WaterElectricTab.vue 都需要添加

**defineExpose 暴露方法**
```typescript
// 暴露给父组件的方法
defineExpose({
  fetchTableData,
  resetPagination: () => {
    pagination.current = 1
  }
})
```

**移除自动监听**
```typescript
// 不再监听筛选条件变化，由父组件控制调用
// watch(() => props.filterForm, () => {
//   pagination.current = 1
//   fetchTableData()
// }, { deep: true })
```

**组件挂载时不自动调用**
```typescript
// 组件挂载时不自动获取数据，等待父组件调用
onMounted(() => {
  // 不自动调用 fetchTableData，由父组件控制
})
```

## 实现效果

### ✅ 解决的问题

1. **项目切换控制**: 参考 reduction/index.vue 实现，只有初次选择项目时才调用查询，避免重复调用
2. **查询和重置响应**: 通过 ref 引用子组件，正确调用子组件的数据获取方法
3. **父子组件交互**: 使用 defineExpose 暴露子组件方法，父组件通过 ref 调用
4. **多tab支持**: 根据当前激活的tab判断调用对应的子组件列表接口
5. **分页重置**: 查询和重置时正确重置分页到第一页

### 🔧 技术特点

- **状态管理**: 使用 isInit 控制初始化状态，避免重复调用
- **条件加载**: 只有在有项目ID时才调用API接口
- **组件解耦**: 子组件不再自动监听props变化，完全由父组件控制
- **扩展性**: 支持后续添加更多智能设备tab
- **用户体验**: 重置后不自动调用查询，等待用户选择项目

### 📋 调用流程

1. 用户选择项目 → handleProjectChange → 存储项目信息 → 首次调用 loadData
2. 用户点击查询 → handleSearch → 重置分页 → loadData
3. 用户点击重置 → handleReset → 清空所有状态 → 等待用户重新选择项目
4. 用户切换tab → handleTabChange → 如果有项目ID则调用 loadData
5. loadData → 根据当前tab调用对应子组件的 fetchTableData 方法

这样实现了完整的父子组件交互，解决了所有提到的问题。
