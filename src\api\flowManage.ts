import http from './index'

// 财务流水相关接口类型定义
export interface FinancialFlowAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  projectId?: string
  orderNo?: string
  payDirection?: number // 支付方向: 0-收入, 1-支出
  payType?: number // 支付类型
  payMethod?: number // 支付方式
  targetType?: number // 支付对象类型
  target?: string // 支付对象
  entryTime?: string // 入账时间
  status?: number // 状态: 0-未记账, 1-部分记账, 2-已记账
  amount?: number // 支付金额
  usedAmount?: number // 已使用金额
  payerName?: string // 支付人姓名
  payerPhone?: string // 支付人手机号
  payerAccount?: string // 支付人账号
  payRemark?: string // 支付备注
  merchant?: string // 收款商户
  payChannel?: string // 收款渠道
  sourceNo?: string // 退款流水的原单号
  isOtherIncome?: boolean // 是否是其他收入: 0-否,1-是
  otherIncomeDesc?: string // 其他收入说明
  isDel?: boolean // 0-否,1-是
}

export interface FinancialFlowQueryDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  id?: string // 主键ID
  projectId?: string // 项目id
  orderNo?: string // 订单号
  payDirection?: number // 支付方向: 0-收入, 1-支出
  payTypes?: number[] // 支付类型（线上/线下多选）
  payMethods?: number[] // 支付方式（取一房一码里的paytype多选）
  targetTypes?: number[] // 支付对象类型（多选）
  payType?: number // 支付类型
  payMethod?: number // 支付方式
  targetType?: number // 支付对象类型
  target?: string // 支付对象
  entryTimeStart?: string // 入账时间开始
  entryTimeEnd?: string // 入账时间结束
  entryTime?: string // 入账时间
  status?: number // 状态: 0-未记账, 1-部分记账, 2-已记账
  amount?: number // 支付金额
  usedAmount?: number // 已使用金额
  payerName?: string // 支付人姓名
  payerPhone?: string // 支付人手机号
  payerAccount?: string // 支付人账号
  payRemark?: string // 支付备注
  merchants?: string[] // 收款商户号（多选下拉选择）
  merchant?: string // 收款商户
  payChannel?: string // 收款渠道
  sourceNo?: string // 退款流水的原单号
  isOtherIncome?: boolean // 是否是其他收入: 0-否,1-是
  otherIncomeDesc?: string // 其他收入说明
  createBy?: string // 创建人账号
  createByName?: string // 创建人姓名
  createTime?: string // 创建时间
  updateBy?: string // 更新人账号
  updateByName?: string // 更新人姓名
  updateTime?: string // 更新时间
  isDel?: boolean // 0-否,1-是
  searchType?: number // 1-未明流水 2-其他收入
}

export interface FinancialFlowVo {
  id?: string
  projectId?: string
  projectName?: string
  orderNo?: string
  payDirection?: number
  payType?: number
  payMethod?: number
  targetType?: number
  target?: string
  entryTime?: string
  status?: number
  amount?: number
  usedAmount?: number
  payerName?: string
  payerPhone?: string
  payerAccount?: string
  payRemark?: string
  merchant?: string
  payChannel?: string
  sourceNo?: string
  isOtherIncome?: boolean
  otherIncomeDesc?: string
  refundId?: string // 退款单ID
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  isDel?: boolean
  bizId?: string // 业务id: 根据退款类型退租申请单id,定单id,流水id
  bizType?: number // 业务类型: 1-退租申请单id,2-定单id,3-流水id
}

// 记账相关类型定义
export interface CostFlowRelAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  costId?: string // 账单id
  refundId?: string // 退款单id
  flowId?: string // 流水id
  flowNo?: string // 流水单号
  type?: number // 账单类型：1-收款 2-转入 3-转出 4-退款
  confirmStatus?: number // 确认状态: 0-未确认、1-自动确认、2-手动确认
  confirmTime?: string // 确认时间
  confirmUserId?: string // 确认人id
  confirmUserName?: string // 确认人姓名
  payAmount?: number // 支付金额
  pendingAmount?: number // 账单待收金额
  acctAmount?: number // 本次记账金额
  remark?: string // 备注
  isDel?: boolean // 0-否,1-是
}

export interface FinancialFlowSaveRecordDTO {
  flowId: string // 流水id
  flowRelList: CostFlowRelAddDTO[] // 记账记录列表
}

export interface FinancialFlowRecordQueryDTO {
  params?: Record<string, any>
  pageNum: number
  pageSize: number
  bookingId?: string // 定单id
  refundId?: string // 退款id
  costId?: string // 账单id
}

export interface FinancialFlowMarkOtherIncomeDTO {
  flowId: string // 流水id
  otherIncomeDesc: string // 其他收入说明
}

// 退款相关类型定义
export interface FinancialRefundAddDTO {
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  id?: string
  projectId?: string // 项目id
  refundType?: number // 退款类型：0-退租退款、1-退定退款、2-未明流水退款
  refundNo?: string // 退款单号
  bizId?: string // 业务id: 根据退款类型退租申请单id,定单id,流水id
  refundTarget?: string // 退款对象: 根据退款类型合同号,定单预定客户+房源,流水号
  applyTime?: string // 退款申请时间
  refundAmount?: number // 退款金额
  recordAmount?: number // 已记账金额
  feeType?: string // 退款费用类型
  refundWay?: number // 退款方式: 0-原路退回、1-银行转账
  receiverName?: string // 收款方姓名
  receiverBank?: string // 收款方开户行
  receiverAccount?: string // 收款方银行账号
  refundRemark?: string // 退款申请说明
  refundTime?: string // 退款时间
  refundStatus?: number // 退款单状态:0-草稿、1-待退款、2-已退款、3-作废
  approveStatus?: number // 审批状态:0-草稿、1-审批中、2-审批通过、3-审批驳回
  approveTime?: string // 审批通过时间
  recordStatus?: number // 记账状态: 0-未记账,1-已记账
  confirmStatus?: number // 确认状态: 0-待确认, 1-已确认
  roomYardStatus?: number // 一房一码退款状态
  attachments?: string // 附件
  cancelTime?: string // 作废时间
  cancelBy?: string // 作废人
  cancelByName?: string // 作废人姓名
  cancelReason?: string // 作废原因
  isDel?: boolean // 0-否,1-是
  isSubmit?: boolean // 0-暂存,1-提交
}

export interface TableDataInfo<T = any> {
  total?: number
  rows?: T[]
  code?: number
  msg?: string
}

export interface AjaxResult<T = any> {
  error?: boolean
  success?: boolean
  warn?: boolean
  empty?: boolean
  data?: T
  code?: number
  msg?: string
  [key: string]: any
}

// 枚举定义
export enum PayDirection {
  INCOME = 0, // 收入
  EXPENSE = 1 // 支出
}

export enum FlowStatus {
  UNACCOUNTED = 0, // 未记账
  PARTIAL_ACCOUNTED = 1, // 部分记账
  ACCOUNTED = 2 // 已记账
}

export enum TargetType {
  PARK = 1, // 园区
  PARCEL = 2, // 地块
  BUILDING = 3, // 楼栋
  ROOM = 4, // 房间
  BILL_PAYMENT = 5, // 账单支付
  BANK_TRANSFER = 6 // 银行转账
}

// API 方法

/**
 * 保存记账
 * @param data 记账数据
 */
export const saveFinancialFlowRecord = (data: FinancialFlowSaveRecordDTO) => {
  return http.post<AjaxResult>('/business-rent-admin/financialFlow/saveRecord', data)
}

/**
 * 发起退款接口
 * @param data 退款数据
 */
export const createFinancialRefund = (data: FinancialRefundAddDTO) => {
  return http.post<AjaxResult>('/business-rent-admin/financialFlow/refund', data)
}

/**
 * 查询流水信息列表
 * @param data 查询参数
 */
export const getFinancialFlowRecordList = (data: FinancialFlowRecordQueryDTO) => {
  return http.post<TableDataInfo<FinancialFlowVo>>('/business-rent-admin/financialFlow/recordList', data)
}

/**
 * 标记为其他收入
 * @param data 标记数据
 */
export const markFinancialFlowAsOtherIncome = (data: FinancialFlowMarkOtherIncomeDTO) => {
  return http.post<AjaxResult>('/business-rent-admin/financialFlow/markOtherIncome', data)
}

/**
 * 查询财务流水列表
 * @param data 查询参数
 */
export const getFinancialFlowList = (data: FinancialFlowQueryDTO) => {
  return http.post<TableDataInfo<FinancialFlowVo>>('/business-rent-admin/financialFlow/list', data)
}

/**
 * 导出财务流水列表
 * @param params 查询参数
 */
export const exportFinancialFlowList = (params: FinancialFlowQueryDTO) => {
  return http.post('/business-rent-admin/financialFlow/export', params)
}

/**
 * 取消其他收入
 * @param flowId 流水ID
 */
export const cancelFinancialFlowOtherIncome = (flowId: string) => {
  return http.post<AjaxResult>('/business-rent-admin/financialFlow/cancelOtherIncome', {}, {
    params: { 'flowId': flowId }
  })
}

/**
 * 查看流水使用记录
 * @param flowId 流水id
 */
export const getFinancialFlowUsage = (flowId: string) => {
  return http.get<AjaxResult>(`/business-rent-admin/financialFlow/usage/${flowId}`)
}

/**
 * 根据合同查询未收齐账单
 * @param contractId 合同id
 */
export const getBillByContractId = (contractId: string) => {
  return http.get<AjaxResult>('/business-rent-admin/financialFlow/getBillByContractId', {
    contractId
  })
}

/**
 * 获取流水详情
 * @param flowId 流水id
 */
export const getFinancialFlowDetail = (flowId: string) => {
  return http.get<AjaxResult<FinancialFlowVo>>(`/business-rent-admin/financialFlow/detail/${flowId}`)
}

/**
 * 确认流水记账
 * @param data 确认数据
 */
export interface FlowConfirmRecordDTO {
  flowId: string // 流水ID
  isOneKeyConfirm: boolean // 是否一键确认
  flowRelId?: string // 记账记录id（非一键确认时必填）
}

export const confirmFlowRecord = (data: FlowConfirmRecordDTO) => {
  return http.post<AjaxResult>('/business-rent-admin/financialFlow/confirmRecord', data)
}

/**
 * 取消流水记账
 * @param flowRelId 记账记录ID
 */
export const cancelFlowRecord = (flowRelId: string) => {
  return http.post<AjaxResult>('/business-rent-admin/financialFlow/cancelRecord', {}, {
    params: { 'flowRelId': flowRelId }
  })
}

// 导出所有接口和类型
export default {
  saveFinancialFlowRecord,
  createFinancialRefund,
  getFinancialFlowRecordList,
  markFinancialFlowAsOtherIncome,
  getFinancialFlowList,
  exportFinancialFlowList,
  cancelFinancialFlowOtherIncome,
  getFinancialFlowUsage,
  getBillByContractId,
  getFinancialFlowDetail,
  confirmFlowRecord,
  cancelFlowRecord
} 