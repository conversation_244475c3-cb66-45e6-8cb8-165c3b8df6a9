<template>
    <a-modal 
        v-model:visible="visible" 
        title="选择项目" 
        width="600px"
        :footer="false"
        @cancel="handleCancel"
    >
        <div class="project-select-content">
            <div class="project-tree-container">
                <div class="project-tree-header">
                    <div class="project-tree-header-title">
                        <span class="common-header-tag"></span>
                        <span>项目选择</span>
                    </div>
                </div>
                <div class="project-tree-search">
                    <a-input 
                        v-model="searchValue" 
                        placeholder="请输入项目名称搜索" 
                        allow-clear
                    >
                        <template #prefix>
                            <icon-search />
                        </template>
                    </a-input>
                </div>
                <div class="project-tree-content">
                    <div class="project-tree-list">
                        <a-tree 
                            :data="filteredTreeData" 
                            :field-names="{ title: 'name', key: 'id' }" 
                            :default-expand-all="true"
                            :expand-all="!!searchKeyword"
                            :selected-keys="selectedKeys" 
                            show-line 
                            @select="handleTreeSelect" 
                        />
                        <div v-if="filteredTreeData.length === 0 && searchKeyword" class="no-data">
                            <a-empty description="未找到匹配的项目" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="project-select-footer">
            <div class="selected-info" v-if="selectedProject">
                <span class="selected-label">已选择：</span>
                <span class="selected-name">{{ selectedProject.name }}</span>
                <a-tag v-if="onlyLeaf && !isLeafNode(selectedProject)" color="orange" size="small">
                    请选择最末级项目
                </a-tag>
            </div>
            <div v-else class="selected-info"></div>
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button 
                    type="primary" 
                    @click="handleConfirm" 
                    :disabled="!canConfirm"
                >
                    确定
                </a-button>
            </a-space>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { IconSearch } from '@arco-design/web-vue/es/icon'
import { getOrgTree } from '@/api/org'

interface ProjectInfo {
    id: string
    name: string
    children?: ProjectInfo[]
    [key: string]: any
}

interface Props {
    onlyLeaf?: boolean // 是否只能选择最末级节点
    minLevel?: number // 最小层级要求，如果设置为4，则只显示有第四级数据的节点
}

const props = withDefaults(defineProps<Props>(), {
    onlyLeaf: false,
    minLevel: 0
})

const emit = defineEmits<{
    confirm: [project: ProjectInfo]
}>()

const visible = ref(false)
const selectedProject = ref<ProjectInfo | null>(null)
const selectedKeys = ref<string[]>([])
const searchValue = ref('')
const searchKeyword = ref('')
const treeData = ref<ProjectInfo[]>([])

// 搜索防抖
let searchTimer: number | null = null

// 防抖处理
const handleSearchDebounce = (value: string) => {
    if (searchTimer) {
        clearTimeout(searchTimer)
    }
    searchTimer = setTimeout(() => {
        searchKeyword.value = value
    }, 300)
}

// watch 监听搜索值变化
watch(searchValue, (newValue) => {
    handleSearchDebounce(newValue)
})

// 判断是否为叶子节点
const isLeafNode = (node: ProjectInfo): boolean => {
    return !node.children || node.children.length === 0
}

// 检查节点是否满足最小层级要求
const checkMinLevel = (node: ProjectInfo, currentLevel: number = 1): boolean => {
    if (props.minLevel <= 0) return true // 没有层级要求
    
    // 如果当前层级已经达到要求，返回true
    if (currentLevel >= props.minLevel) return true
    
    // 如果有子节点，递归检查子节点
    if (node.children && node.children.length > 0) {
        return node.children.some(child => checkMinLevel(child, currentLevel + 1))
    }
    
    // 没有子节点且当前层级不满足要求
    return false
}

// 过滤树数据，移除不满足层级要求的节点
const filterByLevel = (nodes: ProjectInfo[], currentLevel: number = 1): ProjectInfo[] => {
    if (props.minLevel <= 0) return nodes // 没有层级要求
    
    return nodes.filter(node => {
        // 检查当前节点是否满足层级要求
        if (!checkMinLevel(node, currentLevel)) {
            return false
        }
        
        return true
    }).map(node => {
        // 创建新的节点对象，避免修改原始数据
        const newNode = { ...node }
        
        // 如果有子节点，递归过滤子节点
        if (node.children && node.children.length > 0) {
            const filteredChildren = filterByLevel(node.children, currentLevel + 1)
            // 如果过滤后没有子节点了，需要重新检查是否满足层级要求
            if (filteredChildren.length === 0) {
                // 如果当前层级不满足要求，则不包含此节点
                if (currentLevel < props.minLevel) {
                    return null
                }
                newNode.children = []
            } else {
                newNode.children = filteredChildren
            }
        }
        
        return newNode
    }).filter(node => node !== null) as ProjectInfo[]
}

// 是否可以确认选择
const canConfirm = computed(() => {
    if (!selectedProject.value) return false
    if (props.onlyLeaf) {
        return isLeafNode(selectedProject.value)
    }
    return true
})

// 过滤树数据（根据搜索关键词）
const filteredTreeData = computed(() => {
    // 首先应用层级过滤
    let levelFilteredData = filterByLevel([...treeData.value])
    
    // 如果没有搜索关键词，直接返回层级过滤后的数据
    if (!searchKeyword.value.trim()) {
        return levelFilteredData
    }
    
    const keyword = searchKeyword.value.toLowerCase().trim()
    
    const filterTree = (nodes: ProjectInfo[]): ProjectInfo[] => {
        const result: ProjectInfo[] = []
        
        for (const node of nodes) {
            const matchesSearch = node.name?.toLowerCase().includes(keyword)
            let filteredChildren: ProjectInfo[] = []
            
            if (node.children && node.children.length > 0) {
                filteredChildren = filterTree(node.children)
            }
            
            // 如果当前节点匹配或有匹配的子节点，则包含此节点
            if (matchesSearch || filteredChildren.length > 0) {
                result.push({
                    ...node,
                    children: filteredChildren.length > 0 ? filteredChildren : node.children
                })
            }
        }
        
        return result
    }
    
    return filterTree(levelFilteredData)
})

// 在树数据中查找节点
const findNodeInTree = (tree: ProjectInfo[], targetId: string): ProjectInfo | null => {
    for (const node of tree) {
        if (node.id === targetId) {
            return node
        }
        if (node.children && node.children.length > 0) {
            const found = findNodeInTree(node.children, targetId)
            if (found) return found
        }
    }
    return null
}

// 处理树节点选择
const handleTreeSelect = (keys: string[]) => {
    selectedKeys.value = keys
    
    if (keys.length > 0) {
        const selectedId = keys[0]
        // 始终在原始数据中查找节点，确保能找到完整的节点信息
        const selectedNode = findNodeInTree(treeData.value, selectedId)
        
        if (selectedNode) {
            selectedProject.value = {
                ...selectedNode,
                children: selectedNode.children
            }
        }
    } else {
        selectedProject.value = null
    }
}

// 获取组织架构数据
const fetchTreeData = async () => {
    try {
        const { data } = await getOrgTree()
        treeData.value = data as ProjectInfo[]
    } catch (error) {
        console.error('获取组织架构数据失败:', error)
        treeData.value = []
    }
}

// 确认选择
const handleConfirm = () => {
    if (selectedProject.value && canConfirm.value) {
        emit('confirm', selectedProject.value)
        handleCancel()
    }
}

// 取消选择
const handleCancel = () => {
    visible.value = false
    selectedProject.value = null
    selectedKeys.value = []
    searchValue.value = ''
    searchKeyword.value = ''
    
    // 清理搜索防抖定时器
    if (searchTimer) {
        clearTimeout(searchTimer)
        searchTimer = null
    }
}

// 显示弹框
const show = () => {
    visible.value = true
    // 每次显示时重新获取数据
    fetchTreeData()
}

// 组件挂载时获取数据
onMounted(() => {
    fetchTreeData()
})

// 暴露方法
defineExpose({
    show
})
</script>

<style scoped lang="less">
.project-select-content {
    height: 500px;
    overflow: hidden;
}

.project-tree-container {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    border: 1px solid var(--color-border-2);
    padding: 12px;
    box-sizing: border-box;
    
    .project-tree-header {
        display: flex;
        justify-content: space-between;
        
        .project-tree-header-title {
            display: flex;
            align-items: center;
            
            .common-header-tag {
                width: 4px;
                height: 16px;
                background: rgb(var(--arcoblue-6));
                border-radius: 2px;
                margin-right: 8px;
            }
        }
    }
    
    .project-tree-search {
        margin-top: 12px;
    }
    
    .project-tree-content {
        margin-top: 12px;
        height: calc(100% - 80px);
        overflow: auto;
        
        .project-tree-list {
            height: 100%;
            
            .no-data {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 200px;
            }
        }
    }
}

.project-select-footer {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--color-border-2);
    padding-top: 16px;
    
    .selected-info {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .selected-label {
            color: var(--color-text-2);
            font-size: 14px;
        }
        
        .selected-name {
            color: var(--color-text-1);
            font-weight: 500;
            font-size: 14px;
        }
    }
}
</style> 