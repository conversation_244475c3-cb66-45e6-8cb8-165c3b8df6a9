<template>
    <section>
        <sectionTitle title="保证金信息"></sectionTitle>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table :data="tableData" :pagination="false" :scroll="{ x: 1000 }" :bordered="{ cell: true }">
                <template #columns>
                    <a-table-column title="租期" data-index="startDate" :width="110" align="center">
                        <template #cell="{ record }">
                            {{ dayjs(record.startDate).format('YYYY-MM-DD') }} 至 {{ dayjs(record.endDate).format('YYYY-MM-DD') }}
                        </template>
                    </a-table-column>
                    <a-table-column title="商户" data-index="customerName" :width="100" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="费项" data-index="subjectName" :width="80" align="center" :ellipsis="true"
                        :tooltip="true">
                        <template #cell="{ record }">
                           履约保证金
                        </template>
                    </a-table-column>
                    <a-table-column title="应收日期" data-index="receivableDate" :width="120" align="center">
                        <template #cell="{ record, rowIndex }">
                            <a-date-picker 
                                v-if="contractData.contractMode === 1 && !isDetailMode && editingRows.includes(rowIndex)" 
                                v-model="record.receivableDate" 
                                style="width: 100%" 
                                placeholder="请选择日期"
                                format="YYYY-MM-DD" 
                                size="mini"
                                @change="handleDateChange(record, rowIndex)"
                            />
                            <span v-else>{{ record.receivableDate }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="账单应收金额（元）" data-index="actualReceivable" :width="80" align="right">
                        <template #cell="{ record, rowIndex }">
                            <a-input-number v-model="record.actualReceivable" v-if="contractData.contractMode === 1 && !isDetailMode && editingRows.includes(rowIndex)"  :precision="2" placeholder=""
                                :formatter="formatter" :parser="parser" :min="0">
                                <template #suffix>元</template>
                            </a-input-number>
                            <span v-else>{{ formatAmount(record.actualReceivable) }}</span>
                        </template>
                    </a-table-column>
                    <!-- <a-table-column title="账单已收金额（元）" data-index="receivedAmount" :width="100" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.receivedAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单剩余应收金额（元）" data-index="remainingAmount" :width="100" align="right">
                        <template #cell="{ record }">
                            <span v-if="contractData.contractMode === 0">{{ formatAmount(record.remainingAmount) }}</span>
                            <span v-else>{{ formatAmount(record.actualReceivable - record.receivedAmount) }}</span>
                        </template>
                    </a-table-column> -->
                    <a-table-column v-if="contractData.contractMode === 1 && !isDetailMode" title="操作" fixed="right" :width="120"
                        align="center">
                        <template #cell="{ record, rowIndex }">
                            <a-button 
                                v-if="!editingRows.includes(rowIndex)"
                                type="text" 
                                size="mini" 
                                @click="handleEdit(record, rowIndex)"
                            >
                                编辑
                            </a-button>
                            <a-button 
                                v-if="editingRows.includes(rowIndex)"
                                type="text" 
                                size="mini" 
                                @click="handleSave(record, rowIndex)"
                            >
                                保存
                            </a-button>
                            <a-button 
                                v-if="editingRows.includes(rowIndex)"
                                type="text" 
                                size="mini" 
                                @click="handleCancel(record, rowIndex)"
                                style="margin-left: 8px;"
                            >
                                取消
                            </a-button>
                        </template>
                    </a-table-column>
                </template>
            </a-table>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import { ContractCostDTO } from '@/api/contract';
import dayjs from 'dayjs';
import { Message } from '@arco-design/web-vue';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const formatter = (value: string) => {
    const values = value.split('.');
    values[0] = values[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return values.join('.')
};

const parser = (value: string) => {
    return value.replace(/,/g, '')
};

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractCosts)
const contractData = computed(() => contractStore.contractData)
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || (editType.value === 'change' && !changeType.value.includes(3)))

const tableData = ref<any[]>([])
const editingRows = ref<number[]>([])
const originalData = ref<Record<number, any>>({})

// 处理日期变化
const handleDateChange = (record: any, index: number) => {
    // 这里可以添加日期变化的逻辑，如果需要的话
}

// 保存操作
const handleSave = (record: any, index: number) => {
    // 校验应收日期不能为空
    if (!record.receivableDate) {
        Message.error('请选择应收日期')
        return
    }
    
    // 更新contractStore中的数据
    contractStore.contractCosts.costs = contractStore.contractCosts.costs?.map(item => {
        if(item.costType === 1 && item.period === record.period) {
            item.receivableDate = record.receivableDate
            item.actualReceivable = record.actualReceivable
        }
        return item
    })
    
    // 重新执行定金结转逻辑
    contractStore.transferBookingAmount()
    
    // 退出编辑状态
    editingRows.value = editingRows.value.filter(i => i !== index)
    delete originalData.value[index]
    
    Message.success('保存成功')
}

// 编辑操作
const handleEdit = (record: any, index: number) => {
    // 备份原始数据
    originalData.value[index] = JSON.parse(JSON.stringify(record))
    editingRows.value.push(index)
}

// 取消编辑
const handleCancel = (record: any, index: number) => {
    // 恢复原始数据
    if (originalData.value[index]) {
        Object.assign(tableData.value[index], originalData.value[index])
        delete originalData.value[index]
    }
    editingRows.value = editingRows.value.filter(i => i !== index)
}

/**
 * 监听应收计划计算结果
 * 1. 当合同为标准合同时，需要合并costs中所有costType为保证金且period相同的数据
 *    - rentDate取保证金同一期中最早的日期(startDate)和最晚的日期(endDate)，格式为YYYY-MM-DD 至 YYYY-MM-DD
 *    - customerName取保证金同一期第一条数据的customerName，因为都相同
 *    - subjectName取保证金同一期第一条数据的subjectName，因为都相同
 *    - receivableDate取保证金同一期第一条数据的receivableDate，因为都相同
 *    - actualReceivable取保证金同一期所有数据的actualReceivable之和
 *    - receivedAmount取保证金同一期所有数据的receivedAmount之和
 *    - remainingAmount为actualReceivable - receivedAmount
 * 2. 当合同为非标准合同时，直接使用costs中的数据
 *    - rentDate 格式为 startDate 至 endDate
 *    - remainingAmount为actualReceivable - receivedAmount
 *    - 应收日期和账单应收金额可编辑，其他不可编辑
 */
watch(contractCosts, (newVal) => {
    if (newVal.costs && newVal.costs.length > 0) {
        if (contractData.value.contractMode === 0) {
            // 标准合同
            const depositCosts = newVal.costs.filter(item => item.costType === 1)
            if (depositCosts.length > 0) {
                const depositCostsGroup = depositCosts.reduce((acc, item) => {
                    if (!acc[item.period]) {
                        acc[item.period] = []
                    }
                    acc[item.period].push(item)
                    return acc
                }, {} as Record<number, ContractCostDTO[]>)
                tableData.value = Object.values(depositCostsGroup).map(item => {
                    const startDate = item[0].startDate
                    const endDate =item[item.length - 1].endDate
                    const customerName = item[0].customerName ?? ''
                    const subjectName = item[0].subjectName ?? ''
                    const receivableDate = item[0].receivableDate ? dayjs(item[0].receivableDate).format('YYYY-MM-DD') : ''
                    const actualReceivable = item.reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
                    const receivedAmount = item.reduce((sum, item) => sum + (item.receivedAmount || 0), 0)
                    const remainingAmount = actualReceivable - receivedAmount
                    const period = item[0].period
                    return {
                        startDate,
                        endDate,
                        customerName,
                        subjectName,
                        receivableDate,
                        actualReceivable,
                        receivedAmount,
                        remainingAmount,
                        period
                    }
                })
            }
        } else {
            // 非标准合同 - 参照标准合同的合并方式
            const depositCosts = newVal.costs.filter(item => item.costType === 1)
            if (depositCosts.length > 0) {
                // 按period分组合并保证金数据（参照标准合同的处理方式）
                const depositCostsGroup = depositCosts.reduce((acc, item) => {
                    if (!acc[item.period]) {
                        acc[item.period] = []
                    }
                    acc[item.period].push(item)
                    return acc
                }, {} as Record<number, ContractCostDTO[]>)
                
                tableData.value = Object.values(depositCostsGroup).map(items => {
                    const startDate = items[0].startDate
                    const endDate = items[items.length - 1].endDate
                    const customerName = items[0].customerName ?? ''
                    const subjectName = items[0].subjectName ?? ''
                    const receivableDate = items[0].receivableDate ? dayjs(items[0].receivableDate).format('YYYY-MM-DD') : ''
                    const actualReceivable = items.reduce((sum, item) => sum + (item.actualReceivable || 0), 0)
                    const receivedAmount = items.reduce((sum, item) => sum + (item.receivedAmount || 0), 0)
                    const remainingAmount = actualReceivable - receivedAmount
                    const period = items[0].period
                    return {
                        startDate,
                        endDate,
                        customerName,
                        subjectName,
                        receivableDate,
                        actualReceivable,
                        receivedAmount,
                        remainingAmount,
                        period
                    }
                })
            } else {
                tableData.value = []
            }
        }
    } else {
        tableData.value = []
    }
}, { deep: true, immediate: true })
</script>

<style lang="less" scoped></style>