---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/合同信息

<a id="opIdgetContractOptions"></a>

## GET 合同下拉列表选项

GET /contract/contractOption

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|query|string| 是 |项目id|
|customerId|query|string| 是 |客户id|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","unionId":"string","contractNo":"string","roomName":"string","status":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z"}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ContractOptionVo](#schemacontractoptionvo)]|false|none||none|
|» id|string|false|none|合同id|合同id|
|» unionId|string|false|none|统一id|统一id|
|» contractNo|string|false|none|合同号|合同号|
|» roomName|string|false|none|房源名称|房源名称|
|» status|integer(int32)|false|none|状态|状态|
|» startDate|string(date-time)|false|none|合同开始日期|合同开始日期|
|» endDate|string(date-time)|false|none|合同结束日期|合同结束日期|

# 数据模型

<h2 id="tocS_ContractOptionVo">ContractOptionVo</h2>

<a id="schemacontractoptionvo"></a>
<a id="schema_ContractOptionVo"></a>
<a id="tocScontractoptionvo"></a>
<a id="tocscontractoptionvo"></a>

```json
{
  "id": "string",
  "unionId": "string",
  "contractNo": "string",
  "roomName": "string",
  "status": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|合同id|合同id|
|unionId|string|false|none|统一id|统一id|
|contractNo|string|false|none|合同号|合同号|
|roomName|string|false|none|房源名称|房源名称|
|status|integer(int32)|false|none|状态|状态|
|startDate|string(date-time)|false|none|合同开始日期|合同开始日期|
|endDate|string(date-time)|false|none|合同结束日期|合同结束日期|

