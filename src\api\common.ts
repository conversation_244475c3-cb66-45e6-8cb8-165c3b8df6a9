import http from './index';

/**
 * AjaxResult接口定义
 */
export interface AjaxResult {
  error?: boolean;
  success?: boolean;
  warn?: boolean;
  empty?: boolean;
  [key: string]: any;
}

/**
 * 通用响应列表接口
 */
export interface RList<T = any> {
  code: number;
  msg: string;
  data: T[];
}

/**
 * 文件上传参数
 */
export interface UploadFileParams {
  file: File;
}

/**
 * 图片分析参数
 */
export interface AnalyzeImageParams {
  type: number;
  file: File;
}

/**
 * 上传文件
 * @param data 文件数据
 * @returns Promise<AjaxResult>
 */
export function uploadFile(data: FormData) {
  return http.post<AjaxResult>('/business-platform/common/upload', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 图片分析接口
 * @param data 文件数据
 * @param type 分析类型
 * @returns Promise<AjaxResult>
 */
export function analyzeImage(data: FormData, type: number) {
  return http.post<AjaxResult>(`/business-orc/common/analyzeImageByFile?type=${type}`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 通用接口请求
 * @param name 接口名称
 * @param data 请求参数
 * @returns Promise<RList>
 */
export function commonQuery<T = any>(name: string, data?: Record<string, any>) {
  return http.post<RList<T>>(`/business-system/common/query?name=${name}`, data);
} 