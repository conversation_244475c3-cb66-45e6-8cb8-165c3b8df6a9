<template>
  <div>
    <template v-for="(item, index) in options">
      <template v-if="values.includes(String(item.value))">
        <span
          v-if="(item.tagType === 'default' || item.tagType === '') && (item.tagClass === '' || item.tagClass === null)"
          :key="item.value" :index="index" :class="item.tagClass">{{ item.label + " " }}</span>
        <a-tag v-else :key="item.value + ''" :index="index" :color="getTagColor(item.tagType)" :class="item.tagClass">{{
          item.label + " " }}</a-tag>
      </template>
    </template>
    <template v-if="unmatch && showValue">
      {{ handleArray(unmatchArray) }}
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, PropType } from 'vue'

interface DictOption {
  value: string | number;
  label: string;
  tagType?: string;
  tagClass?: string;
}

// 修复 ref 的语法错误
const unmatchArray = ref<(string | number)[]>([])

const props = defineProps({
  options: {
    type: Array as PropType<DictOption[]>,
    default: () => [],
  },
  value: {
    type: [Number, String, Array] as PropType<number | string | (string | number)[]>,
    default: null,
  },
  showValue: {
    type: Boolean,
    default: true,
  },
  separator: {
    type: String,
    default: ",",
  }
})

const values = computed(() => {
  if (props.value === null || typeof props.value === 'undefined' || props.value === '') return []
  return Array.isArray(props.value)
    ? props.value.map(item => String(item))
    : String(props.value).split(props.separator)
})

const unmatch = computed(() => {
  unmatchArray.value = []
  if (props.value === null || typeof props.value === 'undefined' || props.value === '' || !Array.isArray(props.options) || props.options.length === 0) {
    return false
  }

  let hasUnmatch = false
  values.value.forEach(item => {
    if (!props.options.some(v => String(v.value) === item)) {
      unmatchArray.value.push(item)
      hasUnmatch = true
    }
  })
  return hasUnmatch
})

function handleArray(array: (string | number)[]): string {
  if (array.length === 0) return ""
  return array.reduce((pre: string, cur) => {
    return pre + " " + cur
  }, "")
}

// Arco Tag 颜色映射
function getTagColor(type?: string): string {
  const colorMap: Record<string, string> = {
    'success': 'green',
    'info': 'blue',
    'warning': 'orange',
    'danger': 'red',
    'default': ''
  }
  return colorMap[type || 'default'] || ''
}
</script>

<style scoped>
.arco-tag+.arco-tag {
  margin-left: 10px;
}
</style>