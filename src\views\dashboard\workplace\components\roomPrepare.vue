<template>
    <div class="room-prepare">
        <div class="prepare-title">
            <div class="title-decorator"></div>
            房源准备
        </div>
        <div class="prepare-content">
            <div class="prepare-cards">
                <div class="prepare-card" v-for="(item, index) in prepareItems" :key="item.label"
                    :class="`card-${index + 1}`">
                    <div class="card-background"></div>
                    <div class="card-text">
                        <div class="card-label">{{ item.label }}</div>
                        <div class="card-count">{{ item.count }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const prepareItems = ref([
    { label: '待定价', count: 3 },
    { label: '待绑定电水表', count: 6 },
    { label: '待绑定门锁', count: 6 },
    { label: '待配置宿舍户型', count: 6 },
])
</script>

<style lang="less" scoped>
.room-prepare {
    background: #fff;
    border-radius: 10px 0 0 0;
    padding: 20px 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;

    .prepare-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
        color: #000;
        position: relative;
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .title-decorator {
            width: 5px;
            height: 19px;
            background-color: #0071ff;
            border-radius: 4px 0px 4px 0px;
            margin-right: 14px;
        }
    }

    .prepare-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .prepare-cards {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 3px;
        width: 100%;
        height: 100%;
        position: relative;
    }

    .prepare-card {
        position: relative;
        border-radius: 48px 4px 48px 0px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;

        &.card-1 {
            border-radius: 0 48px 0 48px;
        }

        &.card-2 {
            border-radius: 48px 0 48px 0;
        }

        &.card-4 {
            border-radius: 0 48px 0 48px;
        }

        .card-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }

        .card-text {
            position: relative;
            z-index: 2;
            text-align: center;
            color: #3A4252;

            .card-label {
                font-size: 14px;
                line-height: 1.4;
                margin-bottom: 4px;
            }

            .card-count {
                font-size: 20px;
                line-height: 1.4;
                font-weight: 500;
            }
        }

        &.card-1 .card-background {
            // background: linear-gradient(135deg, rgba(217, 235, 255, 0) 0%, #CDE3FF 100%);
            background: linear-gradient(223.67deg, rgba(217, 235, 255, 0.0001) 4.29%, #CDE3FF 96.18%);
            background: linear-gradient(135deg, rgba(217, 235, 255, 0.0001) 4.29%, #CDE3FF 96.18%);
        }

        &.card-2 .card-background {
            // background: linear-gradient(135deg, rgba(231, 232, 255, 0) 0%, #D4C5FF 100%);
            background: linear-gradient(223.67deg, rgba(231, 232, 255, 0.0001) 4.29%, #D4C5FF 97.28%);
        }

        &.card-3 .card-background {
            // background: linear-gradient(135deg, rgba(238, 253, 255, 0) 0%, #BDE8FF 100%);
            background: linear-gradient(80deg, rgba(255, 245, 235, 0.0001) 4.9%, #FFE2C5 96.27%);
        }

        &.card-4 .card-background {
            // background: linear-gradient(135deg, rgba(255, 245, 235, 0) 0%, #FFE2C5 100%);
            background: linear-gradient(340deg, rgba(238, 253, 255, 0.0001) 4.57%, #BDE8FF 97.22%);

        }
    }
}
</style>