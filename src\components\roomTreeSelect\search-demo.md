# 房源选择组件搜索过滤演示

## 搜索过滤规则

### 1. 基本匹配规则

- **房间节点**：匹配房间名称或房源名称
- **楼层节点**：匹配楼层名称 或 有匹配的房间子节点
- **楼栋节点**：匹配楼栋名称 或 有匹配的楼层子节点

### 2. 父节点隐藏规则

如果父节点的所有子节点都不匹配搜索条件，则隐藏该父节点。

## 演示场景

### 场景1：搜索房间号

**搜索关键词**：`101`

**预期结果**：
- 显示包含"101"的房间节点
- 显示这些房间所在的楼层节点
- 显示这些楼层所在的楼栋节点
- 隐藏没有"101"房间的楼层和楼栋

**数据结构示例**：
```
A栋 (显示 - 有匹配的子节点)
├── 1层 (显示 - 有匹配的子节点)
│   ├── A栋101室 ✓ (显示 - 匹配)
│   └── A栋102室 (隐藏 - 不匹配)
├── 2层 (隐藏 - 没有匹配的子节点)
│   ├── A栋201室 (隐藏 - 不匹配)
│   └── A栋202室 (隐藏 - 不匹配)

B栋 (隐藏 - 没有匹配的子节点)
├── 1层 (隐藏 - 没有匹配的子节点)
│   ├── B栋111室 (隐藏 - 不匹配)
│   └── B栋112室 (隐藏 - 不匹配)
```

### 场景2：搜索楼栋名

**搜索关键词**：`A栋`

**预期结果**：
- 显示名称包含"A栋"的楼栋节点（自身匹配）
- 显示该楼栋下的所有楼层和房间
- 隐藏其他楼栋

### 场景3：搜索楼层名

**搜索关键词**：`1层`

**预期结果**：
- 显示名称包含"1层"的楼层节点（自身匹配）
- 显示这些楼层所在的楼栋节点
- 显示这些楼层下的所有房间
- 隐藏其他楼层

### 场景4：搜索不存在的关键词

**搜索关键词**：`不存在的关键词`

**预期结果**：
- 所有节点都被隐藏
- 下拉框显示为空
- 用户可以清空搜索条件恢复显示

## 技术实现

### 递归匹配算法

```typescript
const checkNodeMatch = (currentNode: any, searchValue: string): boolean => {
  // 叶子节点：直接检查匹配
  if (currentNode.isLeaf && currentNode.roomData) {
    return currentNode.title?.toLowerCase().includes(searchValue.toLowerCase()) ||
           currentNode.roomData.roomName?.toLowerCase().includes(searchValue.toLowerCase())
  }
  
  // 非叶子节点：检查自身匹配
  const selfMatch = currentNode.title?.toLowerCase().includes(searchValue.toLowerCase())
  
  // 检查子节点匹配
  let hasMatchingChild = false
  if (currentNode.children && currentNode.children.length > 0) {
    hasMatchingChild = currentNode.children.some((child: any) => checkNodeMatch(child, searchValue))
  }
  
  // 显示条件：自身匹配 或 有匹配的子节点
  return selfMatch || hasMatchingChild
}
```

### 性能优化

1. **大小写不敏感**：使用 `toLowerCase()` 进行匹配
2. **短路求值**：使用 `some()` 方法，找到第一个匹配就停止
3. **递归深度控制**：最多3层递归（楼栋→楼层→房间）
4. **缓存友好**：搜索不影响数据缓存

## 用户体验

### 优势

1. **精确过滤**：只显示相关的节点，减少干扰
2. **层级关联**：保持父子节点的逻辑关系
3. **实时响应**：输入即时过滤，无需等待
4. **清晰反馈**：空结果时明确显示无匹配项

### 使用建议

1. **搜索提示**：提供常用搜索关键词的提示
2. **搜索历史**：记录用户的搜索历史
3. **模糊匹配**：支持拼音首字母搜索
4. **高亮显示**：在结果中高亮匹配的关键词

## 测试用例

### 基础测试

- [ ] 搜索房间号：`101`, `201`, `301`
- [ ] 搜索楼栋名：`A栋`, `B栋`, `C栋`
- [ ] 搜索楼层名：`1层`, `2层`, `3层`
- [ ] 搜索产品类型：`住宅`, `商业`, `办公`

### 边界测试

- [ ] 空字符串搜索：显示所有节点
- [ ] 不存在的关键词：隐藏所有节点
- [ ] 特殊字符搜索：正确处理特殊字符
- [ ] 长字符串搜索：性能表现良好

### 交互测试

- [ ] 搜索后选择：能正确选择过滤后的节点
- [ ] 清空搜索：恢复显示所有节点
- [ ] 连续搜索：多次搜索性能稳定
- [ ] 搜索切换：在不同关键词间切换正常 