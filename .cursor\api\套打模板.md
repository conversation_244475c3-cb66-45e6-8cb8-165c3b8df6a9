---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心/打印模版

<a id="opIdedit"></a>

## PUT 修改打印模版

PUT /template

修改打印模版

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "templateName": "string",
  "applyLevel": 0,
  "printType": 0,
  "contractPurpose": 0,
  "effectiveDate": "2019-08-24T14:15:22Z",
  "expirationDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "linkUrl": "string",
  "remark": "string",
  "projectIdList": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PrintTemplateAddDTO](#schemaprinttemplateadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd"></a>

## POST 新增打印模版

POST /template

新增打印模版

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "templateName": "string",
  "applyLevel": 0,
  "printType": 0,
  "contractPurpose": 0,
  "effectiveDate": "2019-08-24T14:15:22Z",
  "expirationDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "linkUrl": "string",
  "remark": "string",
  "projectIdList": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PrintTemplateAddDTO](#schemaprinttemplateadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdupdateStatus"></a>

## POST 修改打印模版状态

POST /template/updateStatus

修改打印模版状态

> Body 请求参数

```json
{
  "templateId": "123456",
  "status": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PrintTemplateStatusDto](#schemaprinttemplatestatusdto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist"></a>

## POST 查询打印模版列表

POST /template/list

查询打印模版列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "templateName": "string",
  "projectId": "string",
  "applyLevel": "string",
  "applyScope": "string",
  "printType": "string",
  "contractPurpose": "string",
  "effectiveDate": "2019-08-24T14:15:22Z",
  "expirationDate": "2019-08-24T14:15:22Z",
  "status": "string",
  "linkUrl": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PrintTemplateQueryDTO](#schemaprinttemplatequerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdexport"></a>

## POST 导出询打印模版列表

POST /template/export

导出询打印模版列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|templateName|query|string| 否 | 模版名称|模版名称|
|projectId|query|string| 否 | 项目ID|项目ID|
|applyLevel|query|string| 否 | 适用层级（1集团 2项目）|适用层级（1集团 2项目）|
|applyScope|query|string| 否 | 适用范围|适用范围|
|printType|query|string| 否 | 套打类型（1合同 2协议）|套打类型（1合同 2协议）|
|contractPurpose|query|string| 否 | 合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|
|effectiveDate|query|string(date-time)| 否 | 生效日期|生效日期|
|expirationDate|query|string(date-time)| 否 | 失效日期|失效日期|
|status|query|string| 否 | 状态（0新增 1生效 2失效）|状态（0新增 1生效 2失效）|
|linkUrl|query|string| 否 | 链接地址|链接地址|
|remark|query|string| 否 | 备注|备注|
|createBy|query|string| 否 | 创建人账号|创建人账号|
|createByName|query|string| 否 | 创建人姓名|创建人姓名|
|createTime|query|string(date-time)| 否 | 创建时间|创建时间|
|updateBy|query|string| 否 | 更新人账号|更新人账号|
|updateByName|query|string| 否 | 更新人姓名|更新人姓名|
|updateTime|query|string(date-time)| 否 | 更新时间|更新时间|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdprintTempalteById"></a>

## GET printTempalteById

GET /template/printTempalteById

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|bizId|query|string| 是 ||none|
|templateId|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdgetInfo_1"></a>

## GET 获取打印模版详细信息

GET /template/detail

获取打印模版详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||打印模版ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdremove_1"></a>

## DELETE 删除打印模版

DELETE /template/delete

删除打印模版

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|query|array[string]| 是 ||打印模版ID列表|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_PrintTemplateAddDTO">PrintTemplateAddDTO</h2>

<a id="schemaprinttemplateadddto"></a>
<a id="schema_PrintTemplateAddDTO"></a>
<a id="tocSprinttemplateadddto"></a>
<a id="tocsprinttemplateadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "templateName": "string",
  "applyLevel": 0,
  "printType": 0,
  "contractPurpose": 0,
  "effectiveDate": "2019-08-24T14:15:22Z",
  "expirationDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "linkUrl": "string",
  "remark": "string",
  "projectIdList": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|templateName|string|false|none|模版名称|模版名称|
|applyLevel|integer(int32)|false|none|适用层级（1集团 2项目）|适用层级（1集团 2项目）|
|printType|integer(int32)|false|none|套打类型（1合同 2协议）|套打类型（1合同 2协议）|
|contractPurpose|integer(int32)|false|none|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|
|effectiveDate|string(date-time)|false|none|生效日期|生效日期|
|expirationDate|string(date-time)|false|none|失效日期|失效日期|
|status|integer(int32)|false|none|状态（0新增 1生效 2失效）|状态（0新增 1生效 2失效）|
|linkUrl|string|false|none|链接地址|链接地址|
|remark|string|false|none|备注|备注|
|projectIdList|[string]|false|none|项目id|项目id|
|» 项目id|string|false|none|项目id|项目id|

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_PrintTemplateStatusDto">PrintTemplateStatusDto</h2>

<a id="schemaprinttemplatestatusdto"></a>
<a id="schema_PrintTemplateStatusDto"></a>
<a id="tocSprinttemplatestatusdto"></a>
<a id="tocsprinttemplatestatusdto"></a>

```json
{
  "templateId": "123456",
  "status": 1
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|templateId|string|false|none||模版id|
|status|integer(int32)|false|none||模版状态|

<h2 id="tocS_PrintTemplateQueryDTO">PrintTemplateQueryDTO</h2>

<a id="schemaprinttemplatequerydto"></a>
<a id="schema_PrintTemplateQueryDTO"></a>
<a id="tocSprinttemplatequerydto"></a>
<a id="tocsprinttemplatequerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "templateName": "string",
  "projectId": "string",
  "applyLevel": "string",
  "applyScope": "string",
  "printType": "string",
  "contractPurpose": "string",
  "effectiveDate": "2019-08-24T14:15:22Z",
  "expirationDate": "2019-08-24T14:15:22Z",
  "status": "string",
  "linkUrl": "string",
  "remark": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|templateName|string|false|none|模版名称|模版名称|
|projectId|string|false|none|项目ID|项目ID|
|applyLevel|string|false|none|适用层级（1集团 2项目）|适用层级（1集团 2项目）|
|applyScope|string|false|none|适用范围|适用范围|
|printType|string|false|none|套打类型（1合同 2协议）|套打类型（1合同 2协议）|
|contractPurpose|string|false|none|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|合同/协议用途（1宿舍 2商业 3厂房 4办公 5车位 6综合体 7中央食堂）|
|effectiveDate|string(date-time)|false|none|生效日期|生效日期|
|expirationDate|string(date-time)|false|none|失效日期|失效日期|
|status|string|false|none|状态（0新增 1生效 2失效）|状态（0新增 1生效 2失效）|
|linkUrl|string|false|none|链接地址|链接地址|
|remark|string|false|none|备注|备注|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

