import type { RouteLocationNormalized } from 'vue-router';
import { defineStore } from 'pinia';
import {
    DEFAULT_ROUTE,
    DEFAULT_ROUTE_NAME,
    REDIRECT_ROUTE_NAME,
    DEFAULT_ROUTE_PATH,
} from '@/router/constants';
import { isString } from '@/utils/is';
import piniaPersistConfig from '../../helper/persist';
import { TabBarState, TagProps } from './types';

const formatTag = (route: RouteLocationNormalized): TagProps => {
    const { name, meta, fullPath, query } = route;
    return {
        title: (meta?.title as string) || '',
        name: String(name),
        fullPath,
        query,
        noCache: meta.noCache as boolean,
    };
};

const BAN_LIST = [REDIRECT_ROUTE_NAME];

const useTabBarStore = defineStore('tabBar', {
    state: (): TabBarState => ({
        cacheTabList: [DEFAULT_ROUTE_PATH],
        tagList: [DEFAULT_ROUTE],
    }),

    getters: {
        getTabList(): TagProps[] {
            return this.tagList;
        },
        getCacheList(): string[] {
            return this.cacheTabList;
        },
    },

    actions: {
        updateTabList(route: RouteLocationNormalized) {
            if (BAN_LIST.includes(route.name as string)) return;
            this.tagList.push(formatTag(route));
            if (!route.meta.noCache) {
                // 去重
                this.cacheTabList = [
                    ...new Set([...this.cacheTabList, route.fullPath]),
                ];
            }
        },
        deleteTag(idx: number, tag: TagProps) {
            const tagItem = this.tagList.splice(idx, 1);
            const index = this.cacheTabList.findIndex(
                (el: string) => el === tagItem[0].fullPath
            );
            if (index > -1) {
                this.cacheTabList.splice(index, 1);
            }
        },
        addCache(name: string) {
            if (isString(name) && name !== '') this.cacheTabList.push(name);
        },
        deleteCache(tag: TagProps) {
            const index = this.cacheTabList.findIndex(
                (el: any) => el.fullPath === tag.fullPath
            );
            if (index > -1) {
                this.cacheTabList.splice(index, 1);
            }
        },
        freshTabList(tags: TagProps[]) {
            this.tagList = tags;
            this.cacheTabList = [];
            // 要先判断noCache
            this.tagList
                .filter((el: any) => !el.noCache)
                .map((el: any) => el.fullPath)
                .forEach((x: any) => this.cacheTabList.push(x));
        },
        resetTabList() {
            this.tagList = [DEFAULT_ROUTE];
            this.cacheTabList = [];
            this.cacheTabList = [DEFAULT_ROUTE_PATH];
        },
    },
    persist: piniaPersistConfig('tabBar'),
});

export default useTabBarStore;
