<template>
    <a-drawer v-model:visible="visible" :title="isViewMode ? '查看记账记录' : '流水记账'" width="1200px" @cancel="handleCancel">
        <div class="flow-account-modal">
            <!-- 流水记账信息 -->
            <section-title title="流水信息" style="margin-bottom: 16px;" />
            <div class="flow-info">
                <a-table :columns="flowColumns" :data="[flowData]" :pagination="false" :bordered="true" size="small"
                    :show-header="true" :loading="flowLoading" class="flow-info-table">
                    <template #payDirection="{ record }">
                        <span>{{ record.payDirection === 0 ? '收入' : '支出' }}</span>
                    </template>
                    <template #payMethod="{ record }">
                        <span>{{ getPayMethodText(record.payMethod) }}</span>
                    </template>
                    <template #amount="{ record }">
                        <span>{{ formatAmount(record.amount) }}</span>
                    </template>
                    <template #usedAmount="{ record }">
                        <span>{{ formatAmount(record.usedAmount) }}</span>
                    </template>
                    <template #remainAmount="{ record }">
                        <span>{{ formatAmount((record.amount || 0) - (record.usedAmount || 0)) }}</span>
                    </template>
                </a-table>
            </div>

            <!-- 查看模式：记账记录信息 -->
            <section-title title="记账记录" style="margin: 24px 0 16px 0;" v-if="isViewMode" />
            <div class="view-record-info" v-if="isViewMode">
                <!-- 一键确认按钮 -->
                <div style="margin-bottom: 16px; text-align: right;">
                    <a-button type="primary" @click="handleBatchConfirm" :disabled="!hasUnconfirmedRecords" size="mini">
                        一键确认
                    </a-button>
                </div>
                <a-table :columns="viewRecordColumns" :data="viewRecordData" row-key="id" :pagination="false"
                    :scroll="{ x: 1 }">
                    <template #costType="{ record }">
                        <span>{{ getCostTypeText(record.costType) }}</span>
                    </template>
                    <template #acctAmount="{ record }">
                        <span>{{ formatAmount(record.acctAmount) }}</span>
                    </template>
                    <template #confirmStatus="{ record }">
						<!-- 0-未确认、1-自动确认、2-手动确认 -->	
						<a-tag :color="record.confirmStatus === 0 ? 'red' : record.confirmStatus === 1 ? 'blue' : 'green'">
							{{ record.confirmStatus === 0 ? '未确认' : record.confirmStatus === 1 ? '自动确认' : '手动确认' }}
						</a-tag>
                    </template>
                    <template #operation="{ record }">
                        <a-space>
                            <a-button type="text" size="mini" @click="handleConfirmRecord(record)"
                                v-if="record.confirmStatus === 0">
                                确认
                            </a-button>
                            <a-button type="text" size="mini" status="danger" @click="handleCancelAccount(record)">
                                取消记账
                            </a-button>
                        </a-space>
                    </template>
                </a-table>
            </div>

            <!-- 记账模式：账单信息 -->
            <section-title title="账单信息" style="margin: 24px 0 16px 0;" v-if="!isViewMode" />
            <div class="bill-info" v-if="!isViewMode">
                <!-- 选择合同 -->
                <div class="contract-selector" style="margin-bottom: 16px;">
                    <a-space>
                        <span>选择合同：</span>
                        <a-input v-model="selectedContractDisplay" placeholder="请选择合同" readonly style="width: 300px;" />
                        <a-button type="primary" @click="openContractSelector">选择合同</a-button>
                        <a-button v-if="selectedContract.contractId" @click="autoWriteOff">自动核销</a-button>
                    </a-space>
                </div>

                <!-- 合同信息展示 -->
                <div v-if="selectedContract.contractId" class="contract-info" style="margin-bottom: 16px;">
                    <a-descriptions :column="4" size="small" bordered>
                        <a-descriptions-item label="合同号">{{ selectedContract.contractNo }}</a-descriptions-item>
                        <a-descriptions-item label="合同周期">{{ selectedContract.contractPeriod }}</a-descriptions-item>
                        <a-descriptions-item label="租赁单元">{{ selectedContract.roomName }}</a-descriptions-item>
                        <a-descriptions-item label="合同状态">{{ getContractStatusText(selectedContract.contractStatus)
                            }}</a-descriptions-item>
                    </a-descriptions>
                </div>

                <!-- 账单列表 -->
                <div v-if="selectedContract.contractId" class="bill-list">
                    <a-table :columns="billColumns" :data="billList" row-key="id" :pagination="false" :bordered="true"
                         :loading="billLoading" :scroll="{ x: 1 }">
                        <template #totalAmount="{ record }">
                            <span>{{ formatAmount(record.totalAmount) }}</span>
                        </template>
                        <template #discountAmount="{ record }">
                            <span>{{ formatAmount(record.discountAmount) }}</span>
                        </template>
                        <template #actualReceivable="{ record }">
                            <span>{{ formatAmount(record.actualReceivable) }}</span>
                        </template>
                        <template #receivedAmount="{ record }">
                            <span>{{ formatAmount(record.receivedAmount) }}</span>
                        </template>
                        <template #operation="{ record }">
                            <a-button 
                                type="text" 
                                size="mini" 
                                @click="handleBillAccount(record)"
                                :disabled="isAccountButtonDisabled(record)"
                            >
                                {{ isAccountButtonDisabled(record) ? '已记账' : '记账' }}
                            </a-button>
                        </template>
                    </a-table>
                </div>
            </div>

            <!-- 记账模式：记账信息 -->
            <section-title title="记账信息" style="margin: 24px 0 16px 0;" v-if="!isViewMode" />
            <div class="account-info" v-if="!isViewMode">
                <a-table :columns="accountColumns" :data="accountList" row-key="id" :pagination="false" :bordered="true"
                    size="small" :scroll="{ x: 1 }">
                    <template #pendingAmount="{ record }">
                        <span>{{ formatAmount(record.pendingAmount) }}</span>
                    </template>
                    <template #acctAmount="{ record }">
                        <a-input-number
                            v-model="record.acctAmount"
                            :min="0"
                            :max="getMaxAcctAmount(record)"
                            :precision="2"
                            :step="0.01"
                            size="mini"
                            style="width: 120px;"
                            @change="handleAcctAmountChange(record)"
                        />
                    </template>
                    <template #remainingAmount="{ record }">
                        <span>{{ formatAmount(record.remainingAmount) }}</span>
                    </template>
                    <template #operation="{ record }">
                        <a-button type="text" size="mini" @click="handleRemoveAccount(record)">移除</a-button>
                    </template>
                </a-table>

                <!-- 合计 -->
                <div class="account-summary" style="margin-top: 8px; text-align: right;">
                    <span style="font-weight: bold;">合计记账金额：{{ formatAmount(totalAccountAmount) }}</span>
                </div>
            </div>

            <!-- 历史记账信息 -->
            <section-title title="历史记账信息" style="margin: 24px 0 16px 0;" />
            <div class="history-info">
                <a-table :columns="historyColumns" :data="historyList" row-key="id" :pagination="false" :bordered="true"
                    size="small" :loading="historyLoading">
                    <template #type="{ record }">
                        <span>{{ getTypeText(record.type) }}</span>
                    </template>
                    <template #acctAmount="{ record }">
                        <span>{{ formatAmount(record.acctAmount) }}</span>
                    </template>
                </a-table>
            </div>


        </div>

        <!-- 选择合同弹窗 -->
        <contract-selector ref="contractSelectorRef" @confirm="handleContractSelect" v-if="!isViewMode" />

        <!-- 底部操作按钮 -->
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">{{ isViewMode ? '关闭' : '取消' }}</a-button>
                <a-button type="primary" @click="handleSave" :loading="saving" v-if="!isViewMode">保存</a-button>
            </a-space>
        </template>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import ContractSelector from './contractSelector.vue'
import type { FinancialFlowVo } from '@/api/flowManage'
import {
    getBillByContractId,
    saveFinancialFlowRecord,
    getFinancialFlowUsage,
    getFinancialFlowDetail,
    confirmFlowRecord,
    type FinancialFlowSaveRecordDTO,
    type FlowConfirmRecordDTO
} from '@/api/flowManage'
import { cancelCostRecord } from '@/api/billManage'

const props = defineProps<{
    data: FinancialFlowVo & { viewMode?: boolean }
}>()

const emit = defineEmits(['cancel', 'save'])

// 查看模式（true: 查看记账记录，false: 记账操作）
const isViewMode = computed(() => props.data?.viewMode === true)

// 抽屉显示控制
const visible = ref(false)
const saving = ref(false)
const billLoading = ref(false)
const historyLoading = ref(false)
const flowLoading = ref(false)

// 流水数据
const flowData = ref<FinancialFlowVo>({})

// 查看模式：记账记录数据
const viewRecordData = ref<any[]>([])

// 是否有未确认的记录
const hasUnconfirmedRecords = computed(() => {
    return viewRecordData.value.some(item => item.confirmStatus !== 1)
})

// 查看模式：记账记录表格列配置
const viewRecordColumns = [
    { title: '项目名称', dataIndex: 'projectName', width: 120, align: 'center', ellipsis: true, tooltip: true },
    { title: '账单类型', dataIndex: 'costType', slotName: 'costType', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '合同号', dataIndex: 'bizNo', width: 160, align: 'center', ellipsis: true, tooltip: true },
    { title: '客户名称', dataIndex: 'customerName', width: 120, align: 'center', ellipsis: true, tooltip: true },
    { title: '记账时间', dataIndex: 'createTime', width: 160, align: 'center', ellipsis: true, tooltip: true },
    { title: '记账金额', dataIndex: 'acctAmount', slotName: 'acctAmount', width: 120, align: 'center', ellipsis: true, tooltip: true },
    { title: '记账人', dataIndex: 'createByName', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '确认状态', dataIndex: 'confirmStatus', slotName: 'confirmStatus', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '确认时间', dataIndex: 'confirmTime', width: 160, align: 'center', ellipsis: true, tooltip: true },
    { title: '确认人', dataIndex: 'confirmUserName', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '操作', slotName: 'operation', width: 190, align: 'center', fixed: 'right' }
]

// 选中的合同信息
const selectedContract = reactive({
    contractId: '',
    contractNo: '',
    contractPeriod: '',
    roomName: '',
    contractStatus: 0,
    customerName: ''
})

// 合同选择器引用
const contractSelectorRef = ref()

// 账单列表
const billList = ref<any[]>([])

// 记账列表
const accountList = ref<any[]>([])

// 历史记账列表
const historyList = ref<any[]>([])

// 选中合同的显示文本
const selectedContractDisplay = computed(() => {
    if (selectedContract.contractId) {
        return `${selectedContract.contractNo} - ${selectedContract.customerName} - ${selectedContract.roomName}`
    }
    return ''
})

// 合计记账金额
const totalAccountAmount = computed(() => {
    return accountList.value.reduce((sum, item) => sum + (item.acctAmount || 0), 0)
})

// 流水信息表格列
const flowColumns = [
    { title: '流水订单号', dataIndex: 'orderNo', width: 160, align: 'center' },
    { title: '支付方向', dataIndex: 'payDirection', slotName: 'payDirection', width: 100, align: 'center' },
    { title: '支付方式', dataIndex: 'payMethod', slotName: 'payMethod', width: 120, align: 'center' },
    { title: '对方姓名', dataIndex: 'payerName', width: 120, align: 'center' },
    { title: '入账时间', dataIndex: 'entryTime', width: 160, align: 'center' },
    { title: '支付金额', dataIndex: 'amount', slotName: 'amount', width: 120, align: 'center' },
    { title: '已使用金额', dataIndex: 'usedAmount', slotName: 'usedAmount', width: 120, align: 'center' },
    { title: '剩余金额', dataIndex: 'remainAmount', slotName: 'remainAmount', width: 120, align: 'center' }
]

// 账单列表表格列
const billColumns = [
    { title: '承租人', dataIndex: 'customerName', width: 120, align: 'center' },
    { title: '账单周期', dataIndex: 'billPeriod', width: 200, align: 'center' },
    { title: '账单类型', dataIndex: 'costType', width: 120, align: 'center' },
    { title: '应收日期', dataIndex: 'receivableDate', width: 120, align: 'center' },
    { title: '账单总额(元)', dataIndex: 'totalAmount', slotName: 'totalAmount', width: 160, align: 'center' },
    { title: '优惠金额(元)', dataIndex: 'discountAmount', slotName: 'discountAmount', width: 160, align: 'center' },
    { title: '账单应收总额(元)', dataIndex: 'actualReceivable', slotName: 'actualReceivable', width: 180, align: 'center' },
    { title: '账单已收金额(元)', dataIndex: 'receivedAmount', slotName: 'receivedAmount', width: 180, align: 'center' },
    { title: '操作', slotName: 'operation', width: 80, align: 'center', fixed: 'right' }
]

// 记账信息表格列
const accountColumns = [
    { title: '项目', dataIndex: 'projectName', width: 120, align: 'center' },
    { title: '合同号', dataIndex: 'contractNo', width: 160, align: 'center' },
    { title: '承租人', dataIndex: 'customerName', width: 120, align: 'center' },
    { title: '租赁单元', dataIndex: 'roomName', width: 150, align: 'center' },
    { title: '合同状态', dataIndex: 'contractStatus', width: 100, align: 'center' },
    { title: '账单类型', dataIndex: 'costType', width: 120, align: 'center' },
    { title: '应收日期', dataIndex: 'receivableDate', width: 120, align: 'center' },
    { title: '账单待收金额', dataIndex: 'pendingAmount', slotName: 'pendingAmount', width: 140, align: 'center' },
    { title: '本次记账金额', dataIndex: 'acctAmount', slotName: 'acctAmount', width: 140, align: 'center' },
    { title: '记账后剩余金额', dataIndex: 'remainingAmount', slotName: 'remainingAmount', width: 140, align: 'center' },
    { title: '操作', slotName: 'operation', width: 80, align: 'center', fixed: 'right' }
]

// 历史记账信息表格列
const historyColumns = [
    { title: '流水单号', dataIndex: 'flowNo', width: 160, align: 'center' },
    { title: '类型', dataIndex: 'type', slotName: 'type', width: 120, align: 'center' },
    { title: '记账金额', dataIndex: 'acctAmount', slotName: 'acctAmount', width: 120, align: 'center' },
    { title: '记账时间', dataIndex: 'createTime', width: 160, align: 'center' },
    { title: '记账人', dataIndex: 'createByName', width: 120, align: 'center' }
]

// 打开抽屉
const open = () => {
    visible.value = true
    loadFlowDetail()
    loadHistoryData()
}

// 关闭抽屉
const close = () => {
    visible.value = false
    resetData()
}

// 暴露方法给父组件调用
defineExpose({
    open,
    close
})

// 重置数据
const resetData = () => {
    Object.assign(selectedContract, {
        contractId: '',
        contractNo: '',
        contractPeriod: '',
        roomName: '',
        contractStatus: 0,
        customerName: ''
    })
    Object.assign(flowData.value, {})
    billList.value = []
    accountList.value = []
    historyList.value = []
}

// 打开合同选择器
const openContractSelector = () => {
    contractSelectorRef.value?.open()
}

// 处理合同选择
const handleContractSelect = (contract: any) => {
    Object.assign(selectedContract, contract)
    loadBillList()
}

// 加载账单列表
const loadBillList = async () => {
    if (!selectedContract.contractId) return

    try {
        billLoading.value = true
        const response = await getBillByContractId(selectedContract.contractId)
        if (response.code === 200 && response.data) {
            billList.value = Array.isArray(response.data) ? response.data : []
        }
    } catch (error) {
        console.error('获取账单列表失败:', error)
        Message.error('获取账单列表失败')
    } finally {
        billLoading.value = false
    }
}

// 加载流水详情
const loadFlowDetail = async () => {
    if (!props.data.id) return

    try {
        flowLoading.value = true
        const response = await getFinancialFlowDetail(props.data.id)
        if (response.code === 200 && response.data) {
            Object.assign(flowData.value, response.data.flowInfo)
        }
    } catch (error) {
        console.error('获取流水详情失败:', error)
        Message.error('获取流水详情失败')
    } finally {
        flowLoading.value = false
    }
}

// 加载历史记账数据
const loadHistoryData = async () => {
    if (!props.data.id) return

    try {
        historyLoading.value = true
        if (isViewMode.value) {
            // 查看模式：获取记账记录
            const response = await getFinancialFlowUsage(props.data.id)
            if (response.data && response.data.recordList) {
                viewRecordData.value = response.data.recordList.map((item: any) => ({
                    id: item.costFlowRelId || item.id,
                    projectName: item.projectName || '',
                    costType: item.costType || 2,
                    bizNo: item.bizNo || '',
                    customerName: item.customerName || '',
                    createTime: item.createTime || '',
                    acctAmount: item.acctAmount || 0,
                    createByName: item.createByName || '',
                    confirmStatus: item.confirmStatus || 0,
                    confirmTime: item.confirmTime || '',
                    confirmUserName: item.confirmUserName || ''
                }))
            }
        } else {
            // 记账模式：获取历史记账数据
            const response = await getFinancialFlowUsage(props.data.id)
            if (response.data && response.data.recordList) {
                historyList.value = response.data.recordList.map((item: any) => ({
                    id: item.costFlowRelId || item.id,
                    flowNo: item.bizNo || '',
                    type: item.type || 1,
                    acctAmount: item.acctAmount || 0,
                    createTime: item.createTime || '',
                    createByName: item.createByName || ''
                }))
            }
        }
    } catch (error) {
        console.error('获取历史记账数据失败:', error)
    } finally {
        historyLoading.value = false
    }
}

// 自动核销
const autoWriteOff = () => {
    // TODO: 实现自动核销逻辑
    Message.info('自动核销功能开发中')
}

// 处理账单记账
const handleBillAccount = (bill: any) => {
    // 检查是否已经记账
    if (isAccountButtonDisabled(bill)) {
        Message.warning('该账单已记账，请先移除后再重新记账')
        return
    }

    // 计算可记账金额
    const remainingFlowAmount = (flowData.value.amount || 0) - (flowData.value.usedAmount || 0)
    const billPendingAmount = (bill.actualReceivable || 0) - (bill.receivedAmount || 0)
    const acctAmount = Math.min(remainingFlowAmount, billPendingAmount)

    if (acctAmount <= 0) {
        Message.warning('无可记账金额')
        return
    }

    const accountRecord = {
        id: `${Date.now()}_${Math.random()}`,
        projectName: bill.projectName,
        contractNo: selectedContract.contractNo,
        customerName: bill.customerName,
        roomName: selectedContract.roomName,
        contractStatus: selectedContract.contractStatus,
        costType: bill.costType,
        receivableDate: bill.receivableDate,
        pendingAmount: billPendingAmount,
        acctAmount: acctAmount,
        remainingAmount: 0, // 计算后更新
        costId: bill.id,
        contractId: selectedContract.contractId
    }

    // 计算记账后剩余金额
    accountRecord.remainingAmount = accountRecord.pendingAmount - accountRecord.acctAmount

    accountList.value.push(accountRecord)
    Message.success('已添加到记账列表')
}

// 检查账单记账按钮是否禁用
const isAccountButtonDisabled = (bill: any) => {
    return accountList.value.some(item => item.costId === bill.id)
}

// 获取最大记账金额
const getMaxAcctAmount = (record: any) => {
    const remainingFlowAmount = (flowData.value.amount || 0) - (flowData.value.usedAmount || 0)
    const currentTotal = accountList.value
        .filter(item => item.id !== record.id)
        .reduce((sum, item) => sum + (item.acctAmount || 0), 0)
    
    return Math.min(remainingFlowAmount - currentTotal, record.pendingAmount)
}

// 处理记账金额变化
const handleAcctAmountChange = (record: any) => {
    const maxAmount = getMaxAcctAmount(record)
    if (record.acctAmount > maxAmount) {
        record.acctAmount = maxAmount
        Message.warning('记账金额不能超过可用金额')
    }
    
    // 重新计算剩余金额
    record.remainingAmount = record.pendingAmount - record.acctAmount
}

// 移除记账项
const handleRemoveAccount = (record: any) => {
    const index = accountList.value.findIndex(item => item.id === record.id)
    if (index !== -1) {
        accountList.value.splice(index, 1)
        Message.success('移除成功')
    }
}

// 获取账单类型文本
const getCostTypeText = (costType: number | undefined) => {
    const typeMap = new Map([
        [1, '保证金'],
        [2, '租金'],
        [3, '其他费用']
    ])
    return typeMap.get(costType || 0) || '未知'
}

// 获取合同状态文本
const getContractStatusText = (status: number | undefined) => {
    const statusMap = new Map([
        [10, '草稿'],
        [20, '待生效'],
        [30, '生效中'],
        [40, '失效'],
        [50, '作废']
    ])
    return statusMap.get(status || 0) || '未知'
}

// 获取支付方式文本
const getPayMethodText = (payMethod: number | undefined) => {
    const methodMap = new Map([
        [1, '微信'],
        [2, '支付宝'],
        [3, '银行卡'],
        [4, '现金']
    ])
    return methodMap.get(payMethod || 0) || ''
}

// 获取类型文本
const getTypeText = (type: number | undefined) => {
    const typeMap = new Map([
        [1, '手动记账（收款）'],
        [2, '自动记账（收款）'],
        [3, '手动记账（退款）'],
        [4, '自动记账（退款）'],
        [5, '结转（转入）'],
        [6, '结转（转出）'],
        [7, '取消记账']
    ])
    return typeMap.get(type || 0) || '未知'
}

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return '0.00'
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 一键确认所有记录
const handleBatchConfirm = async () => {
    try {
        const unconfirmedRecords = viewRecordData.value.filter(item => item.confirmStatus !== 1)
        if (unconfirmedRecords.length === 0) {
            Message.warning('没有需要确认的记录')
            return
        }

        if (!props.data.id) {
            Message.error('流水信息不完整')
            return
        }

        const confirmData: FlowConfirmRecordDTO = {
            flowId: props.data.id,
            isOneKeyConfirm: true
        }

        await confirmFlowRecord(confirmData)
        await loadHistoryData() // 重新加载数据
        Message.success('一键确认成功')
    } catch (error) {
        console.error('一键确认失败:', error)
        Message.error('一键确认失败')
    }
}

// 确认单个记录
const handleConfirmRecord = async (record: any) => {
    try {
        if (!props.data.id || !record.id) {
            Message.error('数据不完整')
            return
        }

        const confirmData: FlowConfirmRecordDTO = {
            flowId: props.data.id,
            isOneKeyConfirm: false,
            flowRelId: record.id
        }

        await confirmFlowRecord(confirmData)
        await loadHistoryData() // 重新加载数据
        Message.success('确认成功')
    } catch (error) {
        console.error('确认失败:', error)
        Message.error('确认失败')
    }
}

// 取消记账
const handleCancelAccount = async (record: any) => {
    try {
        if (!record.id) {
            Message.error('记账记录ID不存在')
            return
        }

        await cancelCostRecord(record.id)
        await loadHistoryData() // 重新加载数据
        Message.success('取消记账成功')
    } catch (error) {
        console.error('取消记账失败:', error)
        Message.error('取消记账失败')
    }
}

// 取消
const handleCancel = () => {
    close()
    emit('cancel')
}

// 保存
const handleSave = async () => {
    if (accountList.value.length === 0) {
        Message.warning('请先添加记账信息')
        return
    }

    if (saving.value) return

    saving.value = true

    try {
        const saveData: FinancialFlowSaveRecordDTO = {
            flowId: props.data.id!,
            flowRelList: accountList.value.map(item => ({
                costId: item.costId,
                flowId: props.data.id!,
                type: 1,
                confirmStatus: 1,
                acctAmount: item.acctAmount
            }))
        }

        await saveFinancialFlowRecord(saveData)
        Message.success('保存记账成功')
        close()
        emit('save')
    } catch (error: any) {
        console.error('保存记账失败:', error)
        const errorMsg = error?.response?.data?.msg || error?.message || '保存记账失败，请重试'
        Message.error(errorMsg)
    } finally {
        saving.value = false
    }
}
</script>

<style scoped lang="less">
.flow-account-modal {

    .flow-info-table,
    .bill-list,
    .account-info,
    .history-info {
        :deep(.arco-table) {
            font-size: 12px;
        }
    }

    .contract-selector {
        padding: 12px;
        background-color: #f7f8fa;
        border-radius: 4px;
    }

    .contract-info {
        padding: 12px;
        background-color: #f7f8fa;
        border-radius: 4px;
    }

    .account-summary {
        background-color: #f7f8fa;
        padding: 8px 12px;
        border-radius: 4px;
    }

    .footer {
        border-top: 1px solid #e5e6e8;
        padding-top: 16px;
    }
}
</style>