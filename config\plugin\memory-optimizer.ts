/**
 * 智能内存优化插件
 * 解决 Node.js 内存分配失败和垃圾回收问题
 */
import type { Plugin } from 'vite';
import os from 'os';

export interface MemoryOptimizerOptions {
    /** 最大内存使用量(MB) */
    maxMemory?: number;
    /** 是否启用垃圾回收优化 */
    enableGCOptimization?: boolean;
    /** 是否启用内存监控 */
    enableMemoryMonitoring?: boolean;
    /** 垃圾回收触发阈值(MB) */
    gcThreshold?: number;
}

/**
 * 获取系统内存信息和推荐设置
 */
function getMemoryRecommendations(): {
    totalMemory: number;
    availableMemory: number;
    recommendedMaxMemory: number;
    recommendedSemiSpace: number;
} {
    const totalMemory = Math.round(os.totalmem() / 1024 / 1024); // MB
    const freeMemory = Math.round(os.freemem() / 1024 / 1024); // MB
    
    // 计算推荐的最大内存使用量 (不超过系统总内存的70%)
    const recommendedMaxMemory = Math.min(
        Math.floor(totalMemory * 0.7),
        16 * 1024 // 最大16GB
    );
    
    // 推荐的半空间大小 (通常是最大内存的1/8)
    const recommendedSemiSpace = Math.max(
        Math.floor(recommendedMaxMemory / 8),
        512 // 最小512MB
    );
    
    return {
        totalMemory,
        availableMemory: freeMemory,
        recommendedMaxMemory,
        recommendedSemiSpace
    };
}

/**
 * 设置Node.js内存相关的环境变量
 */
function setupNodeMemoryOptions(options: MemoryOptimizerOptions = {}): void {
    const { recommendedMaxMemory, recommendedSemiSpace } = getMemoryRecommendations();
    
    const maxMemory = options.maxMemory || recommendedMaxMemory;
    const semiSpace = Math.min(Math.floor(maxMemory / 8), 2048); // 最大2GB半空间
    
    // 构建Node.js选项
    const nodeOptions = [
        `--max-old-space-size=${maxMemory}`,
        `--max-semi-space-size=${semiSpace}`,
        '--optimize-for-size', // 优化内存使用而非速度
        '--memory-reducer', // 启用内存缩减器
        '--gc-interval=100', // 设置垃圾回收间隔
    ];
    
    if (options.enableGCOptimization) {
        nodeOptions.push(
            '--expose-gc', // 允许手动触发GC
            '--gc-global', // 全局GC
            '--incremental-marking', // 增量标记
            '--concurrent-marking', // 并发标记
        );
    }
    
    // 设置环境变量
    process.env.NODE_OPTIONS = (process.env.NODE_OPTIONS || '') + ' ' + nodeOptions.join(' ');
    
    // 设置UV线程池大小
    const cpuCount = os.cpus().length;
    process.env.UV_THREADPOOL_SIZE = Math.min(cpuCount * 2, 128).toString();
    
    console.log(`🧠 内存优化设置:`);
    console.log(`   最大内存: ${maxMemory}MB`);
    console.log(`   半空间: ${semiSpace}MB`);
    console.log(`   线程池: ${process.env.UV_THREADPOOL_SIZE}`);
}

/**
 * 内存监控函数
 */
function startMemoryMonitoring(gcThreshold: number = 1024): void {
    let lastGCTime = Date.now();
    let forceGCCount = 0;
    
    const monitor = setInterval(() => {
        const memUsage = process.memoryUsage();
        const heapUsed = Math.round(memUsage.heapUsed / 1024 / 1024);
        const heapTotal = Math.round(memUsage.heapTotal / 1024 / 1024);
        const external = Math.round(memUsage.external / 1024 / 1024);
        
        // 如果内存使用超过阈值，触发垃圾回收
        if (heapUsed > gcThreshold && Date.now() - lastGCTime > 10000) {
            if (global.gc) {
                console.log(`🗑️  内存使用 ${heapUsed}MB，触发垃圾回收...`);
                global.gc();
                lastGCTime = Date.now();
                forceGCCount++;
                
                const newMemUsage = process.memoryUsage();
                const newHeapUsed = Math.round(newMemUsage.heapUsed / 1024 / 1024);
                console.log(`   回收后: ${newHeapUsed}MB (节省 ${heapUsed - newHeapUsed}MB)`);
            }
        }
        
        // 每30秒输出一次内存状态
        if (Date.now() % 30000 < 5000) {
            console.log(`📊 内存状态: 堆内存 ${heapUsed}/${heapTotal}MB, 外部 ${external}MB, 强制GC ${forceGCCount}次`);
        }
    }, 5000); // 每5秒检查一次
    
    // 构建完成后清理监控
    process.on('exit', () => {
        clearInterval(monitor);
        console.log(`🏁 构建完成，总共执行 ${forceGCCount} 次强制垃圾回收`);
    });
}

/**
 * 创建内存优化插件
 */
export function createMemoryOptimizerPlugin(options: MemoryOptimizerOptions = {}): Plugin {
    const {
        enableGCOptimization = true,
        enableMemoryMonitoring = true,
        gcThreshold = 2048
    } = options;
    
    return {
        name: 'memory-optimizer',
        configResolved(config) {
            // 只在构建时启用
            if (config.command === 'build') {
                console.log('🧠 启动内存优化...');
                
                // 设置内存选项
                setupNodeMemoryOptions(options);
                
                // 启用内存监控
                if (enableMemoryMonitoring) {
                    startMemoryMonitoring(gcThreshold);
                }
                
                // 设置进程信号处理
                process.on('SIGINT', () => {
                    console.log('\n🛑 收到中断信号，清理内存...');
                    if (global.gc) {
                        global.gc();
                    }
                    process.exit(0);
                });
                
                // 监听未捕获异常
                process.on('uncaughtException', (error) => {
                    if (error.message.includes('allocation failure')) {
                        console.log('❌ 内存分配失败，尝试垃圾回收...');
                        if (global.gc) {
                            global.gc();
                        }
                    }
                    throw error;
                });
            }
        },
        
        buildStart() {
            if (enableGCOptimization && global.gc) {
                // 构建开始时执行一次垃圾回收
                console.log('🧹 构建开始，执行初始垃圾回收...');
                global.gc();
            }
        },
        
        generateBundle() {
            if (enableGCOptimization && global.gc) {
                // 生成bundle时执行垃圾回收
                console.log('📦 生成Bundle，执行垃圾回收...');
                global.gc();
            }
        },
        
        writeBundle() {
            if (enableGCOptimization && global.gc) {
                // 写入文件后执行最终垃圾回收
                console.log('💾 写入完成，执行最终垃圾回收...');
                global.gc();
            }
        }
    };
}

/**
 * 获取内存优化的Node.js启动参数
 */
export function getOptimizedNodeOptions(maxMemoryMB?: number): string {
    const { recommendedMaxMemory, recommendedSemiSpace } = getMemoryRecommendations();
    
    const maxMemory = maxMemoryMB || recommendedMaxMemory;
    const semiSpace = Math.min(Math.floor(maxMemory / 8), 2048);
    
    const options = [
        `--max-old-space-size=${maxMemory}`,
        `--max-semi-space-size=${semiSpace}`,
        '--expose-gc',
        '--optimize-for-size',
        '--memory-reducer',
        '--gc-interval=100',
        '--incremental-marking',
        '--concurrent-marking'
    ];
    
    return options.join(' ');
}

export default createMemoryOptimizerPlugin; 