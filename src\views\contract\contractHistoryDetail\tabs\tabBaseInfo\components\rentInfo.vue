<template>
    <section>
        <sectionTitle title="租金信息" />
        <div class="content">
            <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">收费方式</label>
                        <div class="detail-text">
                            {{ contractData.chargeWay === 0 ? '固定租金' : contractData.chargeWay === 1 ? '递增租金' :
                                contractData.chargeWay === 2 ? '营收抽成' : '--' }}
                        </div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">应收日期</label>
                        <div class="detail-text">
                            {{ contractData.rentReceivableType === 1 ? '租期开始前付' : '租期开始后付' }}
                            {{ contractData.rentReceivableDate ? contractData.rentReceivableDate + '天' : '' }}
                        </div>
                    </div>
                </a-grid-item>
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">支付周期</label>
                        <div class="detail-text">{{ contractData.rentPayPeriod == null ? '--' :
                            contractData.rentPayPeriod + '个月一付' }}</div>
                    </div>
                </a-grid-item>
                <a-grid-item v-if="contractData.contractMode !== 1">
                    <div class="form-item">
                        <label class="form-label">账单周期</label>
                        <div class="detail-text">{{ contractData.rentTicketPeriod === 1 ? '租赁月' : '自然月' }}</div>
                    </div>
                </a-grid-item>
                <a-grid-item v-if="contractData.contractMode === 1 && contractData.chargeWay === 1">
                    <div class="form-item">
                        <label class="form-label">租金递增规则</label>
                        <div class="detail-text">{{ contractData.increaseRule || '--' }}</div>
                    </div>
                </a-grid-item>
                <template v-if="contractData.chargeWay === 1">
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">递增间隔</label>
                            <div class="detail-text">{{ contractData.increaseGap == null ? '--' :
                                contractData.increaseGap + '年' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">单价递增率</label>
                            <div class="detail-text">{{ contractData.increaseRate == null ? '--' :
                                contractData.increaseRate + '%' }}</div>
                        </div>
                    </a-grid-item>
                </template>
                <template v-if="contractData.chargeWay === 2">
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">预计月营收金额</label>
                            <div class="detail-text">{{ contractData.estimateRevenue == null ? '--' :
                                contractData.estimateRevenue + '元' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">抽成类型</label>
                            <div class="detail-text">{{ contractData.percentageType === 1 ? '固定' :
                                contractData.percentageType === 2 ? '阶梯' : '--' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item v-if="contractData.percentageType === 1">
                        <div class="form-item">
                            <label class="form-label">固定提成比例</label>
                            <div class="detail-text">{{ contractData.fixedPercentage == null ? '--' :
                                contractData.fixedPercentage + '%' }}</div>
                        </div>
                    </a-grid-item>
                    <a-grid-item v-if="contractData.chargeWay === 2 && contractData.percentageType === 2">
                        <div class="form-item">
                            <label class="form-label">阶梯类型</label>
                            <div class="detail-text">{{ contractData.revenueType === 1 ? '按月' : '按年' }}</div>
                        </div>
                    </a-grid-item>
                </template>
            </a-grid>

            <!-- 阶梯抽成表格 -->
            <template v-if="contractData.chargeWay === 2 && contractData.percentageType === 2">
                <a-table :data="ladderData" :pagination="false" style="margin-bottom: 16px;" :scroll="{ x: 1 }"
                    :bordered="{ cell: true }">
                    <template #columns>
                        <a-table-column :title="contractData.revenueType === 1 ? '月营收额' : '年营收额'" align="center">
                            <template #cell="{ record }">
                                {{ formatAmount(record.amount) }}元{{ record.order === ladderData.length ? '以上' : '及以下' }}
                            </template>
                        </a-table-column>
                        <a-table-column title="阶梯提成比例" align="center">
                            <template #cell="{ record }">
                                <span>{{ record.percentage == null ? '--' : record.percentage + '%' }}</span>
                            </template>
                        </a-table-column>
                    </template>
                </a-table>
            </template>

            <a-grid :cols="{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3, xxl: 4 }" :col-gap="16" :row-gap="0">
                <!-- 物业费相关字段 -->
                <a-grid-item>
                    <div class="form-item">
                        <label class="form-label">物业费是否另收</label>
                        <div class="detail-text">{{ contractData.isIncludePm ? '是' : '否' }}</div>
                    </div>
                </a-grid-item>
                <template v-if="contractData.isIncludePm">
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">月物业费单价</label>
                            <div class="detail-text">{{ contractData.pmUnitPrice == null ? '--' :
                                formatAmount(contractData.pmUnitPrice) + '元/㎡/月' }}</div>
                        </div>
                    </a-grid-item>
                    <!-- <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">月物业费总价</label>
                            <div class="detail-text">{{ contractData.pmMonthlyPrice == null ? '--' :
                                formatAmount(contractData.pmMonthlyPrice) + '元/月' }}</div>
                        </div>
                    </a-grid-item> -->
                    <a-grid-item>
                        <div class="form-item">
                            <label class="form-label">物业费支付周期</label>
                            <div class="detail-text">{{ contractData.pmPayPeriod === 0 ? '年' : '按租金账单' }}</div>
                        </div>
                    </a-grid-item>
                </template>
            </a-grid>
        </div>
    </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import { ContractAddDTO } from '@/api/contract';

const contractStore = useContractStore()
const contractData = computed(() => contractStore.historyVersionContractDetail as ContractAddDTO)

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 阶梯数据
interface LadderItem {
    amount: number | undefined,
    percentage: number | undefined,
    order: number
}

const ladderData = computed<LadderItem[]>(() => {
    try {
        if (contractData.value.stepPercentage) {
            return JSON.parse(contractData.value.stepPercentage) as LadderItem[]
        }
    } catch (error) {
        console.error('解析阶梯数据失败:', error)
    }
    return []
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.form-item {
    margin-bottom: 16px;

    .form-label {
        display: block;
        margin-bottom: 2px;
        color: #333;
        font-weight: bold;
    }
}
</style>