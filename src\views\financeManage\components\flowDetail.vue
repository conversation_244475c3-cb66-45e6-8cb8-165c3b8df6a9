<template>
    <div class="flow-detail">
        <!-- 账单信息 -->
        <section-title title="账单信息" style="margin-bottom: 16px;" />
        <div class="bill-info">
            <a-descriptions :column="3" size="medium" bordered>
                <a-descriptions-item label="项目名称">{{ data.projectName }}</a-descriptions-item>
                <a-descriptions-item label="合同号">{{ data.contractNo }}</a-descriptions-item>
                <a-descriptions-item label="承租方">{{ data.tenantName }}</a-descriptions-item>
            </a-descriptions>
            <a-table :data="[data]" :columns="billColumns" :pagination="false" :bordered="{ cell: true }"
                style="margin-top: 16px">
                <template #totalAmount="{ record }">
                    {{ formatAmount(record.totalAmount) }}
                </template>
                <template #discountAmount="{ record }">
                    {{ formatAmount(record.discountAmount) }}
                </template>
                <template #actualAmount="{ record }">
                    {{ formatAmount(record.actualAmount) }}
                </template>
                <template #paidAmount="{ record }">
                    {{ formatAmount(record.paidAmount) }}
                </template>
                <template #unpaidAmount="{ record }">
                    {{ formatAmount(record.unpaidAmount) }}
                </template>
            </a-table>
        </div>

        <!-- 流水信息 -->
        <section-title title="流水信息" style="margin: 24px 0 16px 0;">
            <template #right>
                <a-button type="primary" @click="handleConfirmAll" size="mini">一键确认</a-button>
            </template>
        </section-title>
        <div class="flow-info">
            <a-table :data="data.flowList" :columns="flowColumns" :pagination="false" :bordered="{ cell: true }"
                :scroll="{ x: 1 }">
                <template #payAmount="{ record }">
                    {{ formatAmount(record.payAmount) }}
                </template>
                <template #acctAmount="{ record }">
                    {{ formatAmount(record.acctAmount) }}
                </template>
                <template #confirmStatus="{ record }">
                    <span>
                        {{ getConfirmStatusText(record.confirmStatus) }}
                    </span>
                </template>
                <template #type="{ record }">
                    <span>
                        {{ getFlowTypeText(record.type) }}
                    </span>
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <a-button v-if="record.confirmStatus === 0" type="text" size="small"
                            @click="handleConfirm(record)">
                            确认
                        </a-button>
                        <a-button type="text" status="danger" size="small" @click="handleCancelAccount(record)">
                            取消记账
                        </a-button>
                    </a-space>
                </template>
            </a-table>
        </div>

        <!-- 账单记录 -->
        <section-title title="账单记录" style="margin: 24px 0 16px 0;"> </section-title>
        <div class="flow-info">
            <a-table :data="data.flowLogList" :columns="flowLogColumns" :pagination="false" :bordered="{ cell: true }"
                :scroll="{ x: 1 }">
                <template #type="{ record }">
                    <span>
                        {{ typeMap.get(record.type) }}
                    </span>
                </template>
                <template #amount="{ record }">
                    {{ formatAmount(record.amount) }}
                </template>
            </a-table>
        </div>

        <!-- 确认记账弹窗 -->
        <a-modal v-model:visible="confirmVisible" title="确认记账" width="380px" @ok="handleConfirmOk"
            @cancel="handleConfirmCancel">
            <p>是否确认当前记账操作？</p>
        </a-modal>

        <!-- 取消记账确认弹窗 -->
        <a-modal v-model:visible="cancelAccountVisible" title="提示" width="380px" @ok="handleCancelAccountConfirm"
            @cancel="handleCancelAccountCancel">
            <p><icon-exclamation-circle-fill style="color: #F56C6C;font-size: 16px;" /> 是否确认执行当前取消记账操作？</p>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import { Message } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'
import {
    confirmCostRecord,
    cancelCostRecord,
    type CostFlowRelVo,
    type CostConfirmRecordDTO,
    type CostFlowLogVo
} from '@/api/billManage'

// 定义Props类型
interface FlowDetailData {
    projectName: string
    contractNo: string
    tenantName: string
    billType: string
    billPeriod: string
    dueDate: string
    totalAmount: number
    discountAmount: number
    actualAmount: number
    paidAmount: number
    unpaidAmount: number
    flowList: CostFlowRelVo[]
    flowLogList: CostFlowLogVo[]
    costId?: string
}

const props = defineProps<{
    data: FlowDetailData
}>()

const emit = defineEmits<{
    refresh: []
}>()

// 弹窗控制
const confirmVisible = ref(false)
const cancelAccountVisible = ref(false)
const currentRecord = ref<CostFlowRelVo>({})

// 账单表格列
const billColumns = [
    {
        title: '账单类型',
        dataIndex: 'billType',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '账单周期',
        dataIndex: 'billPeriod',
        width: 200,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '应收日期',
        dataIndex: 'dueDate',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '账单总额',
        dataIndex: 'totalAmount',
        slotName: 'totalAmount',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '免租优惠',
        dataIndex: 'discountAmount',
        slotName: 'discountAmount',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '实际应收金额',
        dataIndex: 'actualAmount',
        slotName: 'actualAmount',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '实际已收金额',
        dataIndex: 'paidAmount',
        slotName: 'paidAmount',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '未收金额',
        dataIndex: 'unpaidAmount',
        slotName: 'unpaidAmount',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
]

// 流水表格列
const flowColumns = [
    {
        title: '流水类型',
        dataIndex: 'type',
        slotName: 'type',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '流水单号',
        dataIndex: 'flowNo',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '入账时间',
        dataIndex: 'entryTime',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '支付方式',
        dataIndex: 'payMethod',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '订单号',
        dataIndex: 'orderNo',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '支付金额',
        dataIndex: 'payAmount',
        slotName: 'payAmount',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '本次记账金额',
        dataIndex: 'acctAmount',
        slotName: 'acctAmount',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '确认状态',
        dataIndex: 'confirmStatus',
        slotName: 'confirmStatus',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '确认时间',
        dataIndex: 'confirmTime',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '确认人',
        dataIndex: 'confirmUserName',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '支付人',
        dataIndex: 'payerName',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 160,
        fixed: 'right',
        align: 'center'
    },
]

// 0:记账（自动）、1:记账（手动）、2:记账（结转）、3:取消记账、4:结转
const typeMap = new Map([
    [0, '记账（自动）'],
    [1, '记账（手动）'],
    [2, '记账（结转）'],
    [3, '取消记账'],
    [4, '结转']
])

// 账单记录表格列
const flowLogColumns = [
    {
        title: '账单类型',
        dataIndex: 'type',
        slotName: 'type',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '单号',
        dataIndex: 'flowNo',
        slotName: 'flowNo',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '金额',
        dataIndex: 'amount',
        slotName: 'amount',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'right'
    },
    {
        title: '操作时间',
        dataIndex: 'updateDate',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '操作人',
        dataIndex: 'updateByName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
]

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 获取确认状态文本
const getConfirmStatusText = (status: number | undefined) => {
    switch (status) {
        case 0: return '未确认'
        case 1: return '自动确认'
        case 2: return '手动确认'
        default: return ''
    }
}

// 获取流水类型文本
const getFlowTypeText = (type: number | undefined) => {
    switch (type) {
        case 1: return '收款'
        case 2: return '转入'
        case 3: return '转出'
        case 4: return '退款'
        default: return ''
    }
}

// 一键确认
const handleConfirmAll = async () => {
    if (!props.data.costId) {
        Message.error('账单ID不能为空')
        return
    }

    try {
        const params: CostConfirmRecordDTO = {
            costId: props.data.costId,
            isOneKeyConfirm: true
        }
        await confirmCostRecord(params)
        Message.success('一键确认成功')
        emit('refresh')
    } catch (error) {
        console.error('一键确认失败:', error)
    }
}

// 确认记账
const handleConfirm = (record: CostFlowRelVo) => {
    currentRecord.value = record
    confirmVisible.value = true
}

// 确认记账确定
const handleConfirmOk = async () => {
    if (!props.data.costId || !currentRecord.value.id) {
        Message.error('参数错误')
        return
    }

    try {
        const params: CostConfirmRecordDTO = {
            costId: props.data.costId,
            isOneKeyConfirm: false,
            flowRelId: currentRecord.value.id
        }
        await confirmCostRecord(params)
        Message.success('确认成功')
        confirmVisible.value = false
        emit('refresh')
    } catch (error) {
        console.error('确认记账失败:', error)
    }
}

// 确认记账取消
const handleConfirmCancel = () => {
    confirmVisible.value = false
    currentRecord.value = {}
}

// 取消记账
const handleCancelAccount = (record: CostFlowRelVo) => {
    currentRecord.value = record
    cancelAccountVisible.value = true
}

// 取消记账确认
const handleCancelAccountConfirm = async () => {
    if (!currentRecord.value.id) {
        Message.error('流水记录ID不能为空')
        return
    }

    try {
        await cancelCostRecord(currentRecord.value.id)
        Message.success('取消记账成功')
        cancelAccountVisible.value = false
        emit('refresh')
    } catch (error) {
        console.error('取消记账失败:', error)
    }
}

// 取消记账取消
const handleCancelAccountCancel = () => {
    cancelAccountVisible.value = false
    currentRecord.value = {}
}
</script>

<style scoped lang="less">
.flow-detail {
    padding: 16px
}

.bill-info {
    margin-bottom: 24px
}

.flow-info {
    margin-top: 16px
}
</style>