<template>
    <div class="container">
        <a-card class="general-card">
            <!-- 搜索区域 -->
            <a-row>
                <a-col :flex="1">
                    <a-form label-align="right" :model="formModel" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
                        auto-label-width>
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="orgCompanyName" label="商业公司名称">
                                    <a-input v-model="formModel.orgCompanyName" placeholder="请输入商业公司名称" allow-clear />
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <!-- <a-divider style="height: 84px" direction="vertical" /> -->
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space :size="18">
                        <a-button v-permission="['rent:merchant:list']" type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin: 0 0 16px 0;" />
            <!-- 表格区域 -->
            <a-table row-key="id" :scroll="{x: 1}" :loading="loading" :pagination="pagination" :columns="columns" :data="tableData"
                :bordered="{ cell: true }" @page-change="onPageChange" @page-size-change="onPageSizeChange">
                <template #index="{ rowIndex }">
                    {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
                </template>
                <template #orgCompanyName="{ record }">
                    <PermissionLink v-permission="['rent:merchant:detail']" @click="handleCompanyDetail(record.id)">
                        {{ record.orgCompanyName || '' }}
                    </PermissionLink>
                </template>
                <template #merchantNo="{ record }">
                    {{ record.merchantNo || '' }}
                </template>
                <template #bankName="{ record }">
                    {{ record.bankName || '' }}
                </template>
                <template #bankAccount="{ record }">
                    {{ record.bankAccount || '' }}
                </template>
                <template #registeredAddress="{ record }">
                    {{ record.registeredAddress || '' }}
                </template>
                <template #unifiedSocialCreditCode="{ record }">
                    {{ record.unifiedSocialCreditCode || '' }}
                </template>
                <template #legalPersonName="{ record }">
                    {{ record.legalPersonName || '' }}
                </template>
                <template #updateByName="{ record }">
                    {{ record.updateByName || '' }}
                </template>
                <template #updateTime="{ record }">
                    {{ record.updateTime || '' }}
                </template>
                <template #projectNum="{ record }">
                    <a-link @click="handleProjectSetting(record.id)">
                        {{ record.projectNum || 0 }}
                    </a-link>
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <a-button v-permission="['rent:merchant:edit']" type="text" size="mini" @click="handleEdit(record)">
                            编辑
                        </a-button>
                    </a-space>
                </template>
            </a-table>
        </a-card>

        <!-- 详情抽屉 -->
        <a-drawer v-model:visible="detailDrawerVisible" title="商业公司详情" class="common-drawer-small" :footer="false"
            @cancel="handleDetailCancel">
            <company-edit v-if="detailDrawerVisible" :id="currentCompanyId" mode="view" />
        </a-drawer>

        <!-- 项目设置抽屉 -->
        <a-drawer v-model:visible="projectDrawerVisible" title="项目关联签约公司" class="common-drawer-small" :footer="false"
            @cancel="handleProjectCancel">
            <project-setting v-if="projectDrawerVisible" :id="currentCompanyId" :company-name="currentCompanyName" @refresh="fetchMerchantList" />
        </a-drawer>

        <!-- 编辑抽屉 -->
        <a-drawer v-model:visible="editDrawerVisible" title="编辑商业公司" class="common-drawer-small" 
            ok-text="保存" cancel-text="取消" :confirm-loading="editLoading" 
            @before-ok="handleSubmit" @cancel="handleEditCancel">
            <company-edit ref="editFormRef" v-if="editDrawerVisible" :id="currentCompanyId" mode="edit"
                @save="handleEditOk" @loading="handleEditLoading" />
        </a-drawer>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconSearch, IconRefresh } from '@arco-design/web-vue/es/icon'
import companyEdit from './components/companyEdit.vue'
import projectSetting from './components/projectSetting.vue'
import { getMerchantList, getMerchantDetail, editMerchant, type MerchantQueryParams, type MerchantAddDTO } from '@/api/company'

const editFormRef = ref()

// 表单数据
const formModel = ref({
    orgCompanyName: ''
})

// 表格数据类型
interface Company extends MerchantAddDTO {
    projectNum?: number
    isDel?: boolean
}

const tableData = ref<Company[]>([])
const loading = ref(false)
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
})

// 表格列配置
const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        slotName: 'index',
        width: 80,
        align: 'center'
    },
    {
        title: '商业公司名称',
        dataIndex: 'orgCompanyName',
        slotName: 'orgCompanyName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 250
    },
    {
        title: '商户号',
        dataIndex: 'merchantNo',
        slotName: 'merchantNo',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 150
    },
    {
        title: '开户银行',
        dataIndex: 'bankName',
        slotName: 'bankName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 200
    },
    {
        title: '银行账号',
        dataIndex: 'bankAccount',
        slotName: 'bankAccount',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 180
    },
    {
        title: '注册地址',
        dataIndex: 'registeredAddress',
        slotName: 'registeredAddress',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 200
    },
    {
        title: '统一社会信用代码',
        dataIndex: 'unifiedSocialCreditCode',
        slotName: 'unifiedSocialCreditCode',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 180
    },
    {
        title: '法人名称',
        dataIndex: 'legalPersonName',
        slotName: 'legalPersonName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 120
    },
    {
        title: '最近更新人',
        dataIndex: 'updateByName',
        slotName: 'updateByName',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 120
    },
    {
        title: '更新时间',
        dataIndex: 'updateTime',
        slotName: 'updateTime',
        ellipsis: true,
        tooltip: true,
        align: 'center',
        width: 180
    },
    {
        title: '关联项目数',
        dataIndex: 'projectNum',
        slotName: 'projectNum',
        width: 120,
        ellipsis: false,
        tooltip: false,
        fixed: 'right',
        align: 'center'
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 100,
        ellipsis: false,
        tooltip: false,
        fixed: 'right',
        align: 'center'
    }
]

// 抽屉相关
const detailDrawerVisible = ref(false)
const projectDrawerVisible = ref(false)
const editDrawerVisible = ref(false)
const currentCompanyId = ref<number | string>('')
const currentCompanyName = ref('')
const editLoading = ref(false)

// 获取商业公司列表
const fetchMerchantList = async () => {
    try {
        loading.value = true
        const params: MerchantQueryParams = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            ...formModel.value
        }
        
        const response = await getMerchantList(params)
        if (response) {
            tableData.value = response.rows || []
            pagination.total = response.total || 0
        }
    } catch (error) {
        console.error('获取商业公司列表失败:', error)
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索
const search = () => {
    pagination.current = 1
    fetchMerchantList()
}

// 分页变化
const onPageChange = (current: number) => {
    pagination.current = current
    fetchMerchantList()
}

// 每页条数变化
const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    fetchMerchantList()
}

// 重置
const reset = () => {
    formModel.value = {
        orgCompanyName: ''
    }
    search()
}

// 查看详情
const handleCompanyDetail = (id: string) => {
    currentCompanyId.value = id
    detailDrawerVisible.value = true
}

// 项目设置功能
const handleProjectSetting = (id: string) => {
    const record = tableData.value.find(item => item.id === id)
    currentCompanyId.value = id
    currentCompanyName.value = record?.orgCompanyName || ''
    projectDrawerVisible.value = true
}

// 编辑
const handleEdit = (record: Company) => {
    currentCompanyId.value = record.id || ''
    currentCompanyName.value = record.orgCompanyName || ''
    editDrawerVisible.value = true
}

// 关闭详情抽屉
const handleDetailCancel = () => {
    detailDrawerVisible.value = false
    currentCompanyId.value = ''
}

// 项目设置抽屉关闭方法
const handleProjectCancel = () => {
    projectDrawerVisible.value = false
    currentCompanyId.value = ''
    currentCompanyName.value = ''
}

// 提交编辑
const handleSubmit = async () => {
    try {
        if (editFormRef.value?.handleSubmit) {
            await editFormRef.value.handleSubmit()
            // 保存成功后会通过@save事件关闭抽屉
        }
    } catch (error) {
        // 保存失败，抽屉保持打开状态
        console.error('保存失败:', error)
    }
    // 返回false阻止抽屉自动关闭
    return false
}

// 编辑成功
const handleEditOk = () => {
    editDrawerVisible.value = false
    currentCompanyId.value = ''
    // 刷新列表
    fetchMerchantList()
}

// 取消编辑
const handleEditCancel = () => {
    editDrawerVisible.value = false
    currentCompanyId.value = ''
}

// 编辑加载中
const handleEditLoading = (loading: boolean) => {
    editLoading.value = loading
}

// 页面初始化时加载数据
onMounted(() => {
    fetchMerchantList()
})
</script>

<style scoped lang="less">
:deep(.section-title) {
    margin: 16px 0;
}

.container {
    padding: 0 16px 16px 16px;
}

.general-card {
    border-radius: 4px;

    :deep(.arco-card-body) {
        padding: 16px;
    }
}
</style>