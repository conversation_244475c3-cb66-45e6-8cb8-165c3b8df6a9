<template>
	<a-drawer v-model:visible="visible" title="外部项目管理" class="common-drawer" @cancel="handleCancel">
		<div class="external-project-container">
			<!-- 顶部菜单 -->
			<a-menu :style="{ width: '200px', height: '100%' }" mode="vertical" :selected-keys="[currentTab]"
				@menu-item-click="handleMenuClick">
				<a-menu-item key="basic">基本信息</a-menu-item>
				<a-menu-item key="building">设置楼栋</a-menu-item>
				<a-menu-item key="room">设置房源</a-menu-item>
			</a-menu>

			<!-- 基本信息内容 -->
			<div v-show="currentTab === 'basic'" class="content-section">
				<div class="basic-info">
					<a-form ref="basicFormRef" :model="formData" layout="horizontal" auto-label-width>
						<a-row :gutter="16">
							<a-col :span="8">
								<a-form-item label="项目名称" field="mdmName" :required="modalType !== 'view'"
									:rules="[{ required: modalType !== 'view', message: '请输入项目名称', trigger: ['change', 'blur'] }]">
									<a-input v-model="formData.mdmName" placeholder="请输入项目名称"
										:disabled="modalType === 'view'" />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="项目编码" field="projectCode" :required="modalType !== 'view'"
									:rules="[{ required: modalType !== 'view', message: '请输入项目编码', trigger: ['change', 'blur'] }]">
									<a-input v-model="formData.projectCode" placeholder="请输入项目编码"
										:disabled="modalType === 'view'" />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="项目简称" field="name" :required="modalType !== 'view'"
									:rules="[{ required: modalType !== 'view', message: '请输入项目简称', trigger: ['change', 'blur'] }]">
									<a-input v-model="formData.name" placeholder="请输入项目简称"
										:disabled="modalType === 'view'" />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="省市区" field="areaValue" :required="modalType !== 'view'"
									:rules="[{ required: modalType !== 'view', message: '请选择省市区', trigger: ['change'] }]">
									<a-cascader v-model="formData.areaValue" :options="areaData.array"
										placeholder="请选择省市区" :style="{ width: '100%' }" allow-clear
										expand-trigger="click" @change="handleAreaChange" :field-names="{
											label: 'address',
											value: 'code',
											children: 'children'
										}" path-mode :disabled="modalType === 'view'" />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="项目定位" field="location" :required="modalType !== 'view'"
									:rules="[{ required: modalType !== 'view', message: '请输入项目定位', trigger: ['change', 'blur'] }]">
									<a-input v-model="formData.location" placeholder="请输入项目定位"
										:disabled="modalType === 'view'">
										<template #suffix>
											<icon-location v-if="modalType !== 'view'" style="cursor:pointer"
												@click="handleLocationClick" />
										</template>
									</a-input>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="项目地址" field="address">
									<a-input v-model="formData.address" :disabled="modalType === 'view'" />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="项目资产分类" field="assetType" :required="modalType !== 'view'">
									<a-select v-model="formData.assetType" :disabled="modalType === 'view'">
										<a-option v-for="item in assetTypeOptions" :key="item.value"
											:value="item.value">
											{{ item.label }}
										</a-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="产权单位" field="propertyUnit">
									<a-input v-model="formData.propertyUnit" :disabled="modalType === 'view'" />
								</a-form-item>
							</a-col>
						</a-row>
					</a-form>

					<!-- 分期信息 -->
					<div class="phase-info">
						<SectionTitle title="分期信息">
							<template #right>
								<a-button type="primary" v-if="modalType !== 'view'" class="add-phase-btn"
									@click="addPhase" size="mini">
									<icon-plus />
								</a-button>
							</template>
						</SectionTitle>
						<div class="phase-buttons">
							<div v-for="(phase, index) in phases" :key="index" class="phase-item">
								<a-input v-model="phase.name" placeholder="请输入分期名称" class="phase-input"
									:disabled="modalType === 'view'" />
								<icon-close
									v-if="index !== 0 && modalType !== 'view' && !isPhaseLinkedToParcel(phase.id)"
									class="delete-icon" @click="removePhase(index)" />
							</div>
						</div>
					</div>

					<SectionTitle title="地块信息">
						<template #right>
							<!-- <div>地块信息</div> -->
							<a-button type="primary" @click="handleAddLand" size="mini"
								v-if="modalType !== 'view'">添加地块</a-button>
						</template>
					</SectionTitle>
					<!-- <div v-if="modalType !== 'view'" class="button-group">
						<a-button type="primary" @click="handleAddLand" size="mini">添加地块</a-button>
					</div> -->
					<a-table :columns="landColumns" :data="landData" :pagination="false" :bordered="{ cell: true }">
						<!-- 地块名称列（必填） -->
						<template #blockName="{ record, index }">
							<a-form-item :name="[`landData`, index, 'blockName']" :rules="[
								{ required: modalType !== 'view', message: '请输入地块名称', trigger: ['change', 'blur'] },
								{ max: 30, message: '地块名称不能超过30个字符', trigger: ['change', 'blur'] }
							]" :required="modalType !== 'view'" class="common-table-form-item">
								<a-input v-model="record.blockName" placeholder="请输入地块名称"
									:disabled="modalType === 'view'" :max-length="30" size="mini" />
							</a-form-item>
						</template>

						<!-- 用地性质列（必填） -->
						<template #usage="{ record, index }">
							<a-form-item :name="[`landData`, index, 'usage']"
								:rules="[{ required: modalType !== 'view', message: '请选择用地性质', trigger: ['change'] }]"
								:required="modalType !== 'view'" class="common-table-form-item">
								<a-select v-model="record.usage" :disabled="modalType === 'view'" size="mini">
									<a-option value="工业用地">工业用地</a-option>
									<a-option value="商业用地">商业用地</a-option>
								</a-select>
							</a-form-item>
						</template>

						<!-- 地块地址列（必填） -->
						<template #address="{ record, index }">
							<a-form-item :name="[`landData`, index, 'address']"
								:rules="[{ required: modalType !== 'view', message: '请输入地块地址', trigger: ['change', 'blur'] }]"
								:required="modalType !== 'view'" class="common-table-form-item">
								<a-input v-model="record.address" placeholder="请输入地块地址" size="mini"
									:disabled="modalType === 'view'" />
							</a-form-item>
						</template>

						<!-- 所属分期列（必填） -->
						<template #phase="{ record, index }">
							<a-form-item :name="[`landData`, index, 'phase']"
								:rules="[{ required: modalType !== 'view', message: '请选择所属分期', trigger: ['change'] }]"
								:required="modalType !== 'view'" class="common-table-form-item">
								<a-select v-model="record.phase" :disabled="modalType === 'view'" size="mini">
									<a-option v-for="phase in phases" :key="phase.name" :value="phase.id">
										{{ phase.name }}
									</a-option>
								</a-select>
							</a-form-item>
						</template>

						<!-- 操作列（无需修改） -->
						<template #operations="{ record }">
							<a-space>
								<a-button
									v-if="modalType !== 'view' && !isParcelLinkedToBuilding(record.id || record.mdmParcelId)"
									type="text" status="danger" @click="handleDeleteLand(record)"
									size="mini">删除</a-button>
							</a-space>
						</template>
					</a-table>
				</div>
			</div>

			<!-- 楼栋设置内容 -->
			<div v-show="currentTab === 'building'" class="content-section">
				<div class="building-settings">
					<SectionTitle title="楼栋信息">
						<template #right>
							<a-button type="primary" v-if="modalType !== 'view'" class="add-phase-btn"
								@click="handleAddBuilding" size="mini">
								<!-- <icon-plus /> -->
								添加楼栋
							</a-button>
						</template>
					</SectionTitle>
					<div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
						<a-select v-model="selectedBlock" style="width: 220px" placeholder="请选择地块"
							@change="handleBuildingTabParcelChange" allow-clear>
							<a-option v-for="item in parcelOptions" :key="item.value" :value="item.value">{{ item.label
							}}</a-option>
						</a-select>
						<a-input v-model="searchBuilding" placeholder="请输入楼栋名称搜索" style="width: 220px">
							<template #suffix>
								<icon-search style="cursor:pointer" @click="handleSearchBuilding" />
							</template>
						</a-input>
						<!-- <a-button v-if="modalType !== 'view'" type="primary" style="margin-left:auto"
							@click="handleAddBuilding">添加楼栋</a-button> -->
					</div>
					<a-table :columns="buildingColumns" :data="filteredBuildingData" :pagination="buildingPagination"
						:bordered="{ cell: true }" @page-change="handleBuildingPageChange"
						@page-size-change="handleBuildingPageSizeChange">
						<template #operations="{ record }">
							<a-space>
								<!-- <a-button v-if="modalType !== 'view'" type="text" @click="handleEditBuilding(record)">编辑</a-button> -->
								<a-button
									v-if="modalType !== 'view' && !isBuildingLinkedToRoom(record.id || record.buildingId)"
									type="text" status="danger" @click="handleDeleteBuilding(record)"
									size="mini">删除</a-button>
							</a-space>
						</template>
					</a-table>
				</div>
			</div>

			<!-- 房源设置内容 -->
			<div v-show="currentTab === 'room'" class="content-section">
				<div class="room-settings">
					<SectionTitle title="房源信息" />
					<div class="room-info">
						<a-select v-model="selectedBlock" style="width: 220px" placeholder="请选择地块"
							@change="handleRoomTabParcelChange" allow-clear>
							<a-option v-for="item in parcelOptions" :key="item.value" :value="item.value">{{ item.label
							}}</a-option>
						</a-select>
						<a-input v-model="searchRoom" placeholder="请输入房源名称搜索" style="width: 220px">
							<template #suffix>
								<icon-search style="cursor:pointer" @click="handleSearchRoom" />
							</template>
						</a-input>
						<div v-if="modalType !== 'view'" class="button-group button-group2">
							<a-button type="primary" @click="openAddParkingDialog">添加房源</a-button>
							<a-button @click="handleDownloadTemplate">下载模板</a-button>
							<a-button @click="handleImportRoom">导入房源</a-button>
							<a-button @click="handleBatchDeleteParking"
								:disabled="!hasValidSelectedRooms">批量删除</a-button>
						</div>
					</div>

					<a-table :columns="roomColumns" :data="parkingData" :pagination="roomPagination"
						:bordered="{ cell: true }" @page-change="handleRoomPageChange"
						@page-size-change="handleRoomPageSizeChange" row-key="index"
						:row-selection="modalType === 'view' ? undefined : rowSelection"
						v-model:selectedKeys="selectedParkingKeys"
						@selection-change="handleParkingSelectionChange" :scroll="{ x: 'max-content' }">
						<template #areaType="{ record }">
							{{ getAreaTypeText(record.areaType) }}
						</template>
						<template #operations="{ record }">
							<a-space>
								<a-button v-if="record.source !== '主数据' && modalType !== 'view'" type="text" size="mini"
									@click="openEditParkingDialog(record)">编辑</a-button>
								<a-button v-if="record.source !== '主数据' && modalType !== 'view'" type="text" size="mini"
									status="danger" @click="handleDeleteParking(record)">删除</a-button>
							</a-space>
						</template>
					</a-table>
				</div>
			</div>
		</div>

		<template #footer>
			<a-space>
				<a-button @click="handleCancel">{{ modalType === 'view' ? '关闭' : '取消' }}</a-button>
				<a-button v-if="modalType !== 'view'" type="primary" @click="handleSave">保存</a-button>
			</a-space>
		</template>

		<MapDialog :visibleDialog="mapDialogVisible" :mapCenter="{ lng: 120.6994, lat: 27.9949 }"
			@confirmDialog="handleMapDialogConfirm" @closeDialog="handleMapDialogClose" />
	</a-drawer>
	<!-- 车位弹窗 -->
	<a-modal v-model:visible="parkingDialogVisible" :title=dialogTitle :width="'800px'"
		@cancel="handleParkingDialogCancel">
		<div class="external-project-container">
			<a-form ref="parkingDialogFormRef" :model="parkingDialogForm" layout="horizontal"
				:label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" :style="{ width: '100%' }"
				auto-label-width>
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="房源名称" field="name" required :rules="[
							{ required: true, message: '请输入房源名称' },
							{ max: 30, message: '房源名称不能超过30个字符' }
						]">
							<a-input v-model="parkingDialogForm.name" :max-length="30" />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="地块" field="block" required :rules="[{ required: true, message: '请选择地块' }]">
							<a-select v-model="parkingDialogForm.block" @change="handleParcelChange">
								<a-option v-for="item in parcelOptions" :key="item.value" :value="item.value">{{
									item.label
								}}</a-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="楼栋" field="building" required
							:rules="[{ required: true, message: '请选择楼栋' }]">
							<a-select v-model="parkingDialogForm.building" @change="handleBuildingChange">
								<a-option v-for="item in buildingOptions" :key="item.value" :value="item.value">{{
									item.label
								}}</a-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="楼层" field="floor" required :rules="[{ required: true, message: '请选择楼层' }]">
							<a-select v-model="parkingDialogForm.floor">
								<a-option v-for="item in floorOptions" :key="item.value" :value="item.value">{{
									item.label
								}}</a-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="产品类型" field="productType" required
							:rules="[{ required: true, message: '请选择产品类型' }]">
							<a-select v-model="parkingDialogForm.productType">
								<a-option v-for="item in productTypeOptions" :key="item.value" :value="item.value">
									{{ item.label }}
								</a-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="面积类型" field="areaType" required
							:rules="[{ required: true, message: '请选择面积类型' }]">
							<a-select v-model="parkingDialogForm.areaType">
								<a-option value="1">实测</a-option>
								<a-option value="2">预测</a-option>
								<a-option value="3">设计</a-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="建筑面积(㎡)" field="buildingArea" required
							:rules="[{ required: true, message: '请输入建筑面积' }]">
							<a-input-number v-model="parkingDialogForm.buildingArea">
								<template #append>㎡</template>
							</a-input-number>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="套内面积(㎡)" field="innerArea" required
							:rules="[{ required: true, message: '请输入套内面积' }]">
							<a-input-number v-model="parkingDialogForm.innerArea">
								<template #append>㎡</template>
							</a-input-number>
						</a-form-item>
					</a-col>
				</a-row>
			</a-form>
		</div>

		<template #footer>
			<a-space>
				<a-button @click="handleParkingDialogCancel">取消</a-button>
				<a-button type="primary" @click="handleParkingDialogOk" :loading="parkingSaveLoading">保存</a-button>
			</a-space>
		</template>
	</a-modal>
	<!-- 楼栋弹窗 -->
	<a-modal v-model:visible="buildingDialogVisible" :title="buildingDialogTitle" :width="'680px'"
		@cancel="handleBuildingDialogCancel">
		<div class="external-project-container">
			<a-form ref="buildingDialogFormRef" :model="buildingDialogForm" layout="horizontal"
				:label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }" :style="{ width: '100%' }">
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="楼栋名称" field="buildingName" required :rules="[
							{ required: true, message: '请输入楼栋名称' },
							{ max: 30, message: '楼栋名称不能超过30个字符' }
						]">
							<a-input v-model="buildingDialogForm.buildingName" placeholder="请输入楼栋名称" :max-length="30" />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="所属地块" field="block" required
							:rules="[{ required: true, message: '请选择地块' }]">
							<a-select v-model="buildingDialogForm.block" placeholder="请选择地块">
								<a-option v-for="item in parcelOptions" :key="item.value" :value="item.value">{{
									item.label }}</a-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="地上楼层数" field="upFloorNums" required
							:rules="[{ required: true, message: '请输入地上楼层数' }]">
							<a-input-number v-model="buildingDialogForm.upFloorNums" :min="0" placeholder="请输入地上楼层数"
								style="width: 100%" />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="地下楼层数" field="underFloorNums" required
							:rules="[{ required: true, message: '请输入地下楼层数' }]">
							<a-input-number v-model="buildingDialogForm.underFloorNums" :min="0" placeholder="请输入地下楼层数"
								style="width: 100%" />
						</a-form-item>
					</a-col>
				</a-row>
			</a-form>
		</div>

		<template #footer>
			<a-space>
				<a-button @click="handleBuildingDialogCancel">取消</a-button>
				<a-button type="primary" @click="handleBuildingDialogOk" :loading="saveLoading">保存</a-button>
			</a-space>
		</template>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import SectionTitle from '@/components/sectionTitle/index.vue';
import MapDialog from '@/components/MapDialog/mapDialog.vue';
import {
	deleteBuilding,
	deleteRoom,
	getParcelList,
	getBuildingDropdownList,
	getFloorDropdownList,
	addProject,
	saveBuilding,
	getProjectDetail,
	getBuildingList,
	getRoomList,
	saveRoom
} from '@/api/asset/projectManage';
// 导入房源模板下载和导入接口
import { downloadRoomTemplate, importRoomTemplate } from '@/api/project';
// 引入区域数据JSON
import areaList from '@/types/area.json';
// 引入字典工具函数
import { useDictSync } from '@/utils/dict';
// 引入导出工具函数
import { exportExcel, importExcelFile } from '@/utils/exportUtil';

// 面积类型转换函数
const getAreaTypeText = (areaType: string | number): string => {
	const areaTypeMap: Record<string, string> = {
		'1': '实测',
		'2': '预测',
		'3': '设计'
	};
	return areaTypeMap[String(areaType)] || String(areaType) || '';
};

// 声明BMap全局类型
declare global {
	interface Window {
		BMap: any;
	}
}

// 为省市区数据结构定义类型
interface AreaItem {
	code: string;
	address: string;
	children: AreaItem[];
}

// 使用导入的JSON数据
const areaData = {
	array: areaList.areaList as AreaItem[]
};

const visible = ref(false);
const currentTab = ref('basic');
const selectedBlock = ref('');
const deleteLoading = ref(false);

// 地图弹窗控制
const mapDialogVisible = ref(false);
const handleLocationClick = () => {
	mapDialogVisible.value = true;
};
const handleMapDialogConfirm = (point: { lng: number; lat: number; address: string }) => {
	formData.location = point.address;
	formData.lng = point.lng.toString();
	formData.lat = point.lat.toString();
	mapDialogVisible.value = false;
};
const handleMapDialogClose = () => {
	mapDialogVisible.value = false;
};

// 反地理编码方法
const reverseGeocode = (lng: string, lat: string) => {
	if (!lng || !lat) return;

	// 创建百度地图Geocoder实例
	const loadBMap = () => {
		// 检查BMap是否已加载
		if (typeof window.BMap !== 'undefined' && typeof window.BMap.Geocoder === 'function') {
			try {
				const geocoder = new window.BMap.Geocoder();
				const point = new window.BMap.Point(Number(lng), Number(lat));

				geocoder.getLocation(point, (result: any) => {
					if (result && result.address) {
						// 将获取到的地址更新到表单
						formData.location = result.address;
					}
				});
			} catch (error) {
				console.error('反地理编码失败:', error);
			}
		} else {
			// 如果BMap未加载，最多尝试5次，每次间隔500ms
			let retryCount = 0;
			const maxRetries = 5;

			const tryLoad = () => {
				if (retryCount >= maxRetries) {
					console.error('无法加载百度地图API');
					return;
				}

				retryCount++;
				setTimeout(() => {
					if (typeof window.BMap !== 'undefined' && typeof window.BMap.Geocoder === 'function') {
						try {
							const geocoder = new window.BMap.Geocoder();
							const point = new window.BMap.Point(Number(lng), Number(lat));

							geocoder.getLocation(point, (result: any) => {
								if (result && result.address) {
									formData.location = result.address;
								}
							});
						} catch (error) {
							console.error('反地理编码失败:', error);
						}
					} else {
						tryLoad();
					}
				}, 500);
			};

			tryLoad();
		}
	};

	loadBMap();
};

const handleMenuClick = (key: string) => {
	currentTab.value = key;

	// 如果切换到楼栋标签，加载楼栋数据
	if (key === 'building' && formData.projectId) {
		loadBuildingData(formData.projectId);
	}

	// 如果切换到房源标签，清空选中状态并加载房源数据
	if (key === 'room') {
		selectedParkingKeys.value = [];
		if (formData.projectId) {
			loadRoomData();
		}
	}
};

// 表单数据
const formData = reactive({
	mdmName: '',
	projectCode: '',
	area: '',
	areaValue: [] as string[],
	location: '',
	address: '',
	assetType: '',
	propertyUnit: '',
	phase: '',
	lng: '120.6994',
	lat: '27.9949',
	projectId: '',
	name: '',
	// 省市区字段
	provinceCode: '',
	provinceName: '',
	cityCode: '',
	cityName: '',
	countryCode: '',
	countryName: ''
});

// 获取省市区名称的工具函数
const getAreaName = (code: string) => {
	// 递归查找区域名称
	const findName = (items: AreaItem[], targetCode: string): string => {
		for (const item of items) {
			if (item.code === targetCode) {
				return item.address;
			}
			if (item.children && item.children.length > 0) {
				const found = findName(item.children, targetCode);
				if (found) return found;
			}
		}
		return '';
	};

	return findName(areaData.array, code);
};

// 处理省市区选择变化
const handleAreaChange = (value: string[], selectedOptions?: AreaItem[]) => {
	console.log('handleAreaChange 被调用:', { value, selectedOptions });

	// 先将值同步到formData (这步可能是多余的，因为v-model已经绑定了formData.areaValue)
	formData.areaValue = value;

	if (value && value.length > 0) {
		// 如果没有传递selectedOptions，根据value值查找对应的选项
		let options = selectedOptions;
		if (!options || options.length === 0) {
			console.log('selectedOptions为空，根据value查找选项');
			console.log('要查找的value:', value);
			console.log('区域数据总数:', areaData.array.length);
			options = [];

			if (value.length >= 1) {
				console.log('查找省份，code:', value[0]);
				const province = areaData.array.find(item => item.code === value[0]);
				console.log('找到的省份:', province);
				if (province) {
					options.push(province);

					if (value.length >= 2) {
						console.log('查找城市，code:', value[1]);
						const city = province.children?.find(item => item.code === value[1]);
						console.log('找到的城市:', city);
						if (city) {
							options.push(city);

							if (value.length >= 3) {
								console.log('查找区县，code:', value[2]);
								const country = city.children?.find(item => item.code === value[2]);
								console.log('找到的区县:', country);
								if (country) {
									options.push(country);
								}
							}
						}
					}
				} else {
					// 如果找不到省份，打印前几个省份的code用于调试
					console.log('未找到省份，前5个省份的code:', areaData.array.slice(0, 5).map(item => ({ code: item.code, address: item.address })));
				}
			}
			console.log('查找到的选项:', options);
		}

		if (options && options.length > 0) {
			// 清空原有值
			formData.provinceCode = '';
			formData.provinceName = '';
			formData.cityCode = '';
			formData.cityName = '';
			formData.countryCode = '';
			formData.countryName = '';

			// 根据选中项设置值
			// 省
			if (options.length >= 1) {
				formData.provinceCode = options[0].code;
				formData.provinceName = options[0].address;
			}

			// 市
			if (options.length >= 2) {
				formData.cityCode = options[1].code;
				formData.cityName = options[1].address;
			}

			// 区县
			if (options.length >= 3) {
				formData.countryCode = options[2].code;
				formData.countryName = options[2].address;
			}

			// 更新展示字段
			formData.area = options.map(opt => opt.address).join('/');

			console.log('省市区字段设置完成:', {
				provinceCode: formData.provinceCode,
				provinceName: formData.provinceName,
				cityCode: formData.cityCode,
				cityName: formData.cityName,
				countryCode: formData.countryCode,
				countryName: formData.countryName,
				area: formData.area
			});
		} else {
			console.log('未找到对应的选项');
		}
	} else {
		// 清空省市区字段
		formData.provinceCode = '';
		formData.provinceName = '';
		formData.cityCode = '';
		formData.cityName = '';
		formData.countryCode = '';
		formData.countryName = '';
		formData.area = '';

		console.log('省市区字段已清空');
	}

	// 不管有没有选值，都手动触发一次验证，确保验证状态更新
	if (basicFormRef.value) {
		// 延迟执行以确保值已更新
		setTimeout(() => {
			basicFormRef.value.validate(['areaValue']);
		}, 10);
	}
};

// 地块表格列定义
const landColumns = [
	{ title: '序号', dataIndex: 'index', width: 70, align: 'center' },
	{
		title: '地块名称',
		dataIndex: 'blockName',
		slotName: 'blockName',
		width: 180,
		align: 'center'
	},
	{
		title: '用地性质',
		dataIndex: 'usage',
		slotName: 'usage',
		width: 180,
		align: 'center'
	},
	{
		title: '地块地址（合同签约地址）',
		dataIndex: 'address',
		slotName: 'address',
		// width: 200,
		align: 'center'
	},
	{
		title: '所属分期',
		dataIndex: 'phase',
		slotName: 'phase',
		width: 180,
		align: 'center'
	},
	{ title: '操作', slotName: 'operations', width: 70, fixed: 'right', align: 'center' }
];

// 地块数据类型
interface LandRecord {
	index: number;
	blockName: string;
	usage: string;
	address: string;
	phase: string;
	id?: string;
	mdmParcelId?: string;
	stageId?: string;
	mdmNatureName?: string;
	mdmAddress?: string;
	totalSelfArea?: string;
}

// 地块数据
const landData = ref<LandRecord[]>([]);

// 楼栋表格列定义
const buildingColumns = [
	{ title: '序号', dataIndex: 'index', width: 70, align: 'center' },
	{ title: '楼栋名称', dataIndex: 'buildingName', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '所属地块', dataIndex: 'block', slotName: 'block', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '地上楼层数', dataIndex: 'aboveFloors', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '地下楼层数', dataIndex: 'undergroundFloors', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '操作', slotName: 'operations', width: 70, fixed: 'right', align: 'center' }
];
// 楼栋数据类型
interface BuildingRecord {
	index: number;
	buildingName: string;
	block: string;
	aboveFloors: number;
	undergroundFloors: number;
	source?: string;
	[key: string]: any;
}
// 楼栋数据
const buildingData = ref<BuildingRecord[]>([]);

// 房源表格列定义
const roomColumns = [
	{ title: '序号', dataIndex: 'index', width: 70, align: 'center' },
	{ title: '房源名称', dataIndex: 'roomName', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '所属地块', dataIndex: 'block', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '楼栋', dataIndex: 'building', width: 180, align: 'center', ellipsis: true, tooltip: true },
	{ title: '楼层', dataIndex: 'floor', width: 100, align: 'center', ellipsis: true, tooltip: true },
	{ title: '面积类型', dataIndex: 'areaType', slotName: 'areaType', width: 100, align: 'center', ellipsis: true, tooltip: true },
	{ title: '建筑面积(㎡)', dataIndex: 'buildingArea', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '套内面积(㎡)', dataIndex: 'innerArea', width: 120, align: 'center', ellipsis: true, tooltip: true },
	{ title: '产品类型', dataIndex: 'productTypeName', width: 100, align: 'center', ellipsis: true, tooltip: true },
	{ title: '操作', slotName: 'operations', width: 70, fixed: 'right', align: 'center' }
];

// 房源数据类型
interface RoomRecord {
	index: string;
	name: string;
	roomName?: string; // 添加roomName字段以匹配表格列的dataIndex
	block: string;
	blockId?: string;
	building: string;
	buildingId?: string;
	floor: string;
	floorId?: string;
	areaType: string;
	buildingArea: number;
	innerArea: number;
	productType: string;
	saleable: string;
	shared: string;
	selfHeldType: string;
	source: string;
	selfHeldExpireDate: string;
	roomId: string;
	id?: string;
}

// 房源数据
const parkingData = ref<RoomRecord[]>([]);

// 处理方法
const handleCancel = () => {
	visible.value = false;
	// 清除所有数据
	resetAllData();
};

const handleSave = async () => {
	try {
		if (currentTab.value === 'basic') {
			// 保存基本信息
			await saveProjectBasic();
		} else if (currentTab.value === 'building') {
			// 楼栋信息无需单独保存，楼栋数据通过新增/编辑楼栋弹窗实时保存
			Message.success('楼栋信息已保存');
		} else if (currentTab.value === 'room') {
			// 保存房源信息
			await handleSaveRoom();
		}
	} catch (error) {
		console.error('保存失败:', error);
		// Message.error('保存失败');
	}
};

const handleAddBuilding = async () => {
	// 确保地块选项已加载
	if (parcelOptions.value.length === 0 && formData.projectId) {
		await loadParcelOptions(formData.projectId);
	}

	// 重置表单数据
	buildingDialogForm.value.buildingName = '';
	buildingDialogForm.value.block = selectedBlock.value || ''; // 如果有选中地块，自动带出
	buildingDialogForm.value.upFloorNums = 1;
	buildingDialogForm.value.underFloorNums = 0;
	buildingDialogForm.value.buildingId = '';

	// 设置弹窗为添加模式
	buildingDialogMode.value = 'add';
	buildingDialogTitle.value = '新增楼栋';
	buildingDialogVisible.value = true;
};

const handleEditBuilding = async (record: any) => {
	// 确保地块选项已加载
	if (parcelOptions.value.length === 0 && formData.projectId) {
		await loadParcelOptions(formData.projectId);
	}

	// 加载表单数据
	buildingDialogForm.value.buildingName = record.buildingName;
	buildingDialogForm.value.block = record.blockId || record.block; // 使用blockId而不是block名称
	buildingDialogForm.value.upFloorNums = record.aboveFloors;
	buildingDialogForm.value.underFloorNums = record.undergroundFloors;
	buildingDialogForm.value.buildingId = record.id || '';

	// 设置编辑索引
	editingBuildingIndex = record.index.toString();

	// 设置弹窗为编辑模式
	buildingDialogMode.value = 'edit';
	buildingDialogTitle.value = '编辑楼栋';
	buildingDialogVisible.value = true;
};

const handleDeleteBuilding = (record: any) => {
	if (isBuildingLinkedToRoom(record.id || record.buildingId)) {
		Message.warning('该楼栋已被房源关联，无法删除');
		return;
	}
	// 原有删除逻辑
	Modal.confirm({
		title: '确认删除',
		content: `确定要删除楼栋"${record.buildingName}"吗？`,
		onOk: async () => {
			try {
				deleteLoading.value = true;
				await deleteBuilding(record.buildingId);
				deleteLoading.value = false;
				Message.success('删除楼栋成功');
				buildingData.value = buildingData.value.filter(item => item !== record);
				buildingData.value.forEach((item, idx) => item.index = idx + 1);
			} catch (error) {
				console.error('删除楼栋失败:', error);
				deleteLoading.value = false;
			}
		}
	});
};

const handleDeleteParking = (record: any) => {
	Modal.confirm({
		title: '确认删除',
		content: `确定要删除房源"${record.name}"吗？`,
		onOk: async () => {
			try {
				deleteLoading.value = true;
				await deleteRoom(record.id);
				deleteLoading.value = false;

				Message.success('删除房源成功');
				// 暂时使用本地方式重新渲染列表

				parkingData.value = parkingData.value.filter(item => item !== record);
			} catch (error) {
				console.error('删除房源失败:', error);
				// Message.error('删除房源失败');
				deleteLoading.value = false;
			}
		}
	});
};

const modalType = ref('add');
// 暴露方法给父组件
defineExpose({
	show: async (projectId?: string, type?: string) => {
		visible.value = true;
		modalType.value = type || 'add';
		if (projectId) {
			// 编辑模式，加载项目详情
			formData.projectId = projectId;
			try {
				const response = await getProjectDetail(projectId);
				if (response && response.data) {
					const projectData = response.data;
					// 设置表单数据
					formData.mdmName = projectData.project?.mdmName || '';
					formData.projectCode = projectData.project?.code || '';
					formData.name = projectData.project?.name || '';
					formData.area = projectData.project?.provinceName && projectData.project?.cityName && projectData.project?.countryName
						? `${projectData.project.provinceName}/${projectData.project.cityName}/${projectData.project.countryName}`
						: '';
					formData.location = projectData.project?.location || ''; // 使用正确的location字段
					// 触发必填校验
					// if (!!formData.location) {
					setTimeout(() => {
						if (basicFormRef.value) {
							basicFormRef.value?.validate(['location'])
						}
					}, 333)
					// }
					formData.address = projectData.project?.projectAddress || ''; // 使用projectAddress作为地址字段
					formData.lng = projectData.project?.longitude || '120.6994';
					formData.lat = projectData.project?.latitude || '27.9949';
					formData.assetType = projectData.project?.assetType.toString() || '';
					formData.propertyUnit = projectData.project?.propertyUnit || '';

					// 设置省市区
					formData.provinceCode = projectData.project?.provinceCode || '';
					formData.provinceName = projectData.project?.provinceName || '';
					formData.cityCode = projectData.project?.cityCode || '';
					formData.cityName = projectData.project?.cityName || '';
					formData.countryCode = projectData.project?.countryCode || '';
					formData.countryName = projectData.project?.countryName || '';

					console.log('从API获取的原始省市区数据:', {
						provinceCode: projectData.project?.provinceCode,
						provinceName: projectData.project?.provinceName,
						cityCode: projectData.project?.cityCode,
						cityName: projectData.project?.cityName,
						countryCode: projectData.project?.countryCode,
						countryName: projectData.project?.countryName
					});

					// 如果有省市区信息，设置cascader的值
					if (formData.provinceCode && formData.cityCode && formData.countryCode) {
						formData.areaValue = [formData.provinceCode, formData.cityCode, formData.countryCode];
						console.log('设置areaValue:', formData.areaValue);

						// 手动调用handleAreaChange，函数内部会自动查找对应的选项
						console.log('手动调用handleAreaChange');
						handleAreaChange(formData.areaValue);
					} else {
						console.log('省市区信息不完整，无法设置cascader值');

						// 如果API返回的省市区字段为空，但是有area字段，尝试从area字段解析
						if (formData.area && formData.area.includes('/')) {
							console.log('尝试从area字段解析省市区:', formData.area);
							const areaParts = formData.area.split('/');
							if (areaParts.length === 3) {
								// 根据名称查找对应的code
								const provinceName = areaParts[0];
								const cityName = areaParts[1];
								const countryName = areaParts[2];

								const province = areaData.array.find(item => item.address === provinceName);
								if (province) {
									const city = province.children?.find(item => item.address === cityName);
									if (city) {
										const country = city.children?.find(item => item.address === countryName);
										if (country) {
											console.log('从area字段成功解析省市区');
											formData.areaValue = [province.code, city.code, country.code];
											handleAreaChange(formData.areaValue);
										}
									}
								}
							}
						}
					}

					console.log('编辑模式下最终的省市区数据:', {
						provinceCode: formData.provinceCode,
						provinceName: formData.provinceName,
						cityCode: formData.cityCode,
						cityName: formData.cityName,
						countryCode: formData.countryCode,
						countryName: formData.countryName,
						areaValue: formData.areaValue
					});

					// 保存原始数据，用于跟踪删除的项目
					originalParcels.value = projectData.parcels ? [...projectData.parcels] : [];
					originalStages.value = projectData.stages ? [...projectData.stages] : [];

					// 设置分期数据
					if (projectData.stages && projectData.stages.length > 0) {
						phases.value = projectData.stages.map((stage: any) => ({
							id: stage.id,
							mdmStageId: stage.mdmStageId,
							name: stage.stageName,
							isNew: false // 从API加载的数据不是新增项
						}));
					} else {
						// 如果没有分期数据，保持默认值

					}

					// 设置地块数据
					if (projectData.parcels && projectData.parcels.length > 0) {
						landData.value = projectData.parcels.map((parcel: any, index: number) => ({
							index: index + 1,
							id: parcel.id,
							mdmParcelId: parcel.mdmParcelId,
							stageId: parcel.stageId,
							blockName: parcel.parcelName,
							usage: parcel.landUsage === 1 ? '工业用地' : parcel.landUsage === 2 ? '商业用地' : '',
							address: parcel.address || '',
							mdmAddress: parcel.mdmAddress,
							mdmNatureName: parcel.mdmNatureName,
							totalSelfArea: parcel.totalSelfArea,
							phase: parcel.stageId || ''
						}));
					} else {
						// 如果没有地块数据，清空数组
						landData.value = [];
					}
					// 如果已有经纬度但没有地址，尝试反地理编码
					if (formData.lng && formData.lat) {
						reverseGeocode(formData.lng, formData.lat);
					}
					// 加载地块选项
					await loadParcelOptions(projectId);

					// 根据当前tab加载相应数据
					if (currentTab.value === 'building') {
						loadBuildingData(projectId);
					} else if (currentTab.value === 'room') {
						loadRoomData();
					}
				}
			} catch (error) {
				console.error('获取项目详情失败:', error);
				// Message.error('获取项目详情失败');
			}
		} else {
			// 新增模式，重置表单
			resetForm();
		}
	}
});

// 重置表单
const resetForm = () => {
	formData.projectId = '';
	formData.mdmName = '';
	formData.projectCode = '';
	formData.address = '';
	formData.location = '';
	formData.lng = '120.6994';
	formData.lat = '27.9949';
	formData.propertyUnit = '';

	// 重置省市区
	formData.provinceCode = '';
	formData.provinceName = '';
	formData.cityCode = '';
	formData.cityName = '';
	formData.countryCode = '';
	formData.countryName = '';
	formData.area = '';
	formData.areaValue = [];

	phases.value = [];
	landData.value = [];
	buildingData.value = [];
	parkingData.value = [];

	// 重置原始数据
	originalParcels.value = [];
	originalStages.value = [];

	// 重置选择和搜索状态
	selectedBlock.value = '';
	searchBuilding.value = '';
	searchRoom.value = '';
};

// 重置所有数据（关闭弹窗时调用）
const resetAllData = () => {
	// 重置基本表单
	resetForm();

	// 重置标签状态
	currentTab.value = 'basic';

	// 重置分页状态
	roomPagination.current = 1;
	roomPagination.total = 0;
	buildingPagination.current = 1;
	buildingPagination.total = 0;

	// 重置选择状态
	selectedParkingKeys.value = [];

	// 重置下拉选项
	parcelOptions.value = [];
	buildingOptions.value = [];
	floorOptions.value = [];

	// 重置加载状态
	parcelOptionsLoading.value = false;

	// 重置弹窗状态
	parkingDialogVisible.value = false;
	buildingDialogVisible.value = false;

	// 重置表单引用状态
	if (basicFormRef.value) {
		basicFormRef.value.resetFields();
	}

	// 重置其他表单字段
	formData.name = '';
	formData.assetType = '';
};

// 根据parcelId查找对应的parcel数据，获取正确的parcelName
const getParcelNameById = (parcelId: string | null | undefined, options: any[]): string => {
	if (!parcelId || !options || !options.length) {
		console.log('无效参数:', { parcelId, optionsLength: options?.length });
		return '';
	}

	console.log('尝试查找地块:', { parcelId, options });

	// 首先尝试直接匹配value与parcelId
	const matchedByValue = options.find(opt => opt.value === parcelId);
	if (matchedByValue) {
		console.log('通过value匹配到地块:', matchedByValue);
		return matchedByValue.label;
	}

	// 尝试匹配id与parcelId (适用于原始API数据格式)
	const matchedById = options.find(opt => opt.id === parcelId);
	if (matchedById) {
		console.log('通过id匹配到地块:', matchedById);
		return matchedById.parcelName || matchedById.label || '';
	}

	// 最后尝试匹配地块名称与parcelId (以防parcelId存储的是名称)
	const matchedByName = options.find(opt =>
		(opt.parcelName && opt.parcelName === parcelId) ||
		(opt.label && opt.label === parcelId)
	);
	if (matchedByName) {
		console.log('通过名称匹配到地块:', matchedByName);
		return matchedByName.parcelName || matchedByName.label || '';
	}

	console.log('未找到匹配的地块:', parcelId);
	return '';
};

// 加载楼栋数据
const loadBuildingData = async (projectId: string) => {
	if (!projectId) return;

	try {
		// 确保先加载地块选项
		if (parcelOptions.value.length === 0) {
			await loadParcelOptions(projectId);
		}

		const params = {
			projectId: projectId,
			pageNum: buildingPagination.current,
			pageSize: buildingPagination.pageSize,
			parcelId: selectedBlock.value || '',
			buildingOrRoomName: searchBuilding.value || ''
		};

		const res = await getBuildingList(params);
		console.log('楼栋列表响应:', res);

		if (res && Array.isArray(res.rows)) {
			buildingPagination.total = res.total || 0;

			// 创建一个地块ID到名称的映射，优先使用API直接返回的parcelName
			const parcelIdToNameMap: Record<string, string> = {};

			// 收集API返回的地块数据
			res.rows.forEach((item: any) => {
				if (item.parcelId && item.parcelName) {
					parcelIdToNameMap[item.parcelId] = item.parcelName;
				}
			});

			console.log('API返回的地块映射:', parcelIdToNameMap);

			// 设置楼栋数据
			buildingData.value = res.rows.map((item: any, index: number) => {
				// 尝试多种方式获取地块名称
				let parcelName = item.parcelName || '';

				if (!parcelName) {
					parcelName = getParcelNameById(item.parcelId, parcelOptions.value);
				}

				if (!parcelName && item.parcelId && parcelIdToNameMap[item.parcelId]) {
					parcelName = parcelIdToNameMap[item.parcelId];
				}

				if (parcelName) {
					console.log(`已匹配到地块: parcelId=${item.parcelId}, parcelName=${parcelName}`);
				} else if (item.parcelId) {
					console.log(`未找到地块: parcelId=${item.parcelId}, API数据:`, item);

					// 如果还是找不到，尝试把parcelId作为名称
					parcelName = item.parcelId;
				}

				return {
					index: ((buildingPagination.current - 1) * buildingPagination.pageSize + index + 1),
					id: item.id || '',
					buildingId: item.id || item.buildingId || '',
					buildingName: item.buildingName || '',
					block: parcelName, // 使用查找到的parcelName
					blockId: item.parcelId || '',
					aboveFloors: item.upFloorNums || 0,
					undergroundFloors: item.underFloorNums || 0
				};
			});
		} else {
			console.log('楼栋列表为空');
			buildingData.value = [];
			buildingPagination.total = 0;
		}
	} catch (error) {
		console.error('获取楼栋列表失败:', error);
		// Message.error('获取楼栋列表失败');
		buildingData.value = [];
	}
};

const dialogTitle = computed(() => parkingDialogMode.value === 'add' ? '新增房源' : '编辑房源');

// 加载地块选项
const loadParcelOptions = async (projectId?: string): Promise<void> => {
	if (!projectId && !formData.projectId) return;

	// 防止重复加载
	if (parcelOptionsLoading.value) return;

	parcelOptionsLoading.value = true;

	try {
		const res = await getParcelList(projectId || formData.projectId);
		console.log('地块列表响应:', res);

		if (res && Array.isArray(res.data) && res.data.length > 0) {
			// API返回了有效数据，使用API数据
			const apiOptions = res.data.map(item => ({
				value: item.id,
				label: item.parcelName
			}));

			// 去重处理：基于value去重
			const uniqueOptions = apiOptions.filter((option, index, self) =>
				index === self.findIndex(o => o.value === option.value)
			);

			parcelOptions.value = uniqueOptions;
			console.log('使用API地块数据:', uniqueOptions);
		} else {
			// API未返回有效数据，使用landData作为数据源
			const landOptions = landData.value
				.filter(item => item.blockName) // 过滤掉空名称的地块
				.map(item => ({
					value: item.id || item.blockName, // 优先使用ID，如果没有则使用名称
					label: item.blockName
				}));

			// 去重处理：基于value去重
			const uniqueOptions = landOptions.filter((option, index, self) =>
				index === self.findIndex(o => o.value === option.value)
			);

			parcelOptions.value = uniqueOptions;
			console.log('使用本地地块数据:', uniqueOptions);
		}
	} catch (error) {
		console.error('获取地块列表失败:', error);
		// Message.error('获取地块列表失败');

		// 发生错误时，使用landData作为数据源
		const landOptions = landData.value
			.filter(item => item.blockName) // 过滤掉空名称的地块
			.map(item => ({
				value: item.id || item.blockName, // 优先使用ID，如果没有则使用名称
				label: item.blockName
			}));

		// 去重处理：基于value去重
		const uniqueOptions = landOptions.filter((option, index, self) =>
			index === self.findIndex(o => o.value === option.value)
		);

		parcelOptions.value = uniqueOptions;
		console.log('错误时使用本地地块数据:', uniqueOptions);
	} finally {
		parcelOptionsLoading.value = false;
	}
};

// 加载楼栋选项
const loadBuildingOptions = async (parcelId: string): Promise<void> => {
	if (!parcelId) return Promise.resolve();

	try {
		const res = await getBuildingDropdownList(parcelId);
		if (res.code === 200 && res.data) {
			buildingOptions.value = res.data.map((item: any) => ({
				value: item.id,
				label: item.buildingName
			}));
		}
	} catch (error) {
		console.error('获取楼栋选项失败:', error);
		// Message.error('获取楼栋选项失败');
	}
};

// 加载楼层选项
const loadFloorOptions = async (buildingId: string): Promise<void> => {
	if (!buildingId) return Promise.resolve();

	try {
		const res = await getFloorDropdownList(buildingId);
		if (res.code === 200 && res.data) {
			floorOptions.value = res.data.map((item: any) => ({
				value: item.id,
				label: item.floorName
			}));
		}
	} catch (error) {
		console.error('获取楼层选项失败:', error);
		// Message.error('获取楼层选项失败');
	}
};

// 加载产品类型字典选项
const loadProductTypeOptions = async (): Promise<void> => {
	try {
		const dictData = await useDictSync('product_type');
		if (dictData.product_type && Array.isArray(dictData.product_type)) {
			productTypeOptions.value = dictData.product_type.map((item: any) => ({
				value: item.value,
				label: item.label
			}));
		}
	} catch (error) {
		console.error('获取产品类型字典失败:', error);
		// 如果字典获取失败，使用默认选项
		productTypeOptions.value = [
			// { value: '车位', label: '车位' }
		];
	}
};

// 加载资产类型字典选项
const loadAssetTypeOptions = async (): Promise<void> => {
	try {
		const dictData = await useDictSync('asset_type');
		if (dictData.asset_type && Array.isArray(dictData.asset_type)) {
			assetTypeOptions.value = dictData.asset_type.map((item: any) => ({
				value: item.value,
				label: item.label
			}));

			// 如果当前没有设置资产类型，设置默认值为外部项目对应的值
			if (!formData.assetType && assetTypeOptions.value.length > 0) {
				// 查找外部项目对应的值，如果没找到则使用第一个选项
				const externalProjectOption = assetTypeOptions.value.find(item =>
					item.label.includes('外部') || item.value === '4'
				);
				formData.assetType = externalProjectOption ? externalProjectOption.value : assetTypeOptions.value[0].value;
			}
		}
	} catch (error) {
		console.error('获取资产类型字典失败:', error);
		// 如果字典获取失败，使用默认选项
		assetTypeOptions.value = [
			{ value: '4', label: '外部项目' }
		];
		if (!formData.assetType) {
			formData.assetType = '4';
		}
	}
};

// 处理地块选择变化
const handleParcelChange = (value: string) => {
	parkingDialogForm.building = '';
	parkingDialogForm.floor = '';
	if (value) {
		loadBuildingOptions(value);
	} else {
		buildingOptions.value = [];
		floorOptions.value = [];
	}
};

// 处理楼栋选择变化
const handleBuildingChange = (value: string) => {
	parkingDialogForm.floor = '';
	if (value) {
		loadFloorOptions(value);
	} else {
		floorOptions.value = [];
	}
};

// 车位弹窗相关
const parkingDialogVisible = ref(false);
const parkingDialogMode = ref<'add' | 'edit'>('add');
const parkingDialogFormRef = ref<any>(null);
const parkingSaveLoading = ref(false);
const parkingDialogForm = reactive({
	index: '',
	name: '',
	block: '',
	building: '',
	floor: '',
	areaType: '1',
	buildingArea: undefined,
	innerArea: undefined,
	productType: '',
	saleable: '否',
	shared: '否',
	selfHeldType: '',
	source: '手动添加',
	selfHeldExpireDate: '',
	roomId: '',
	id: ''
});
let editingParkingIndex = '';

// 楼栋选项
const buildingOptions = ref<{ value: string; label: string }[]>([]);
// 楼层选项
const floorOptions = ref<{ value: string; label: string }[]>([]);

// 产品类型字典选项
const productTypeOptions = ref<{ value: string; label: string }[]>([]);

// 资产类型字典选项
const assetTypeOptions = ref<{ value: string; label: string }[]>([]);

const openAddParkingDialog = async () => {
	parkingDialogMode.value = 'add';
	Object.assign(parkingDialogForm, {
		index: '', name: '', block: '', building: '', floor: '', areaType: '1', buildingArea: '', innerArea: '', productType: '', saleable: '否', shared: '否', selfHeldType: '', source: '手动添加', roomId: '', id: ''
	});

	// 加载地块选项和产品类型字典
	await Promise.all([
		loadParcelOptions(),
		loadProductTypeOptions()
	]);

	// 如果有产品类型选项，设置默认值
	if (productTypeOptions.value.length > 0) {
		parkingDialogForm.productType = productTypeOptions.value[0].value;
	}

	parkingDialogVisible.value = true;
};

const handleParkingDialogOk = async () => {
	try {
		parkingSaveLoading.value = true;

		// 表单验证
		if (parkingDialogFormRef.value) {
			try {
				const errors = await parkingDialogFormRef.value.validate();
				if (errors) {
					parkingSaveLoading.value = false;
					return;
				}
			} catch (validationError) {
				Message.error('请完善必填信息');
				parkingSaveLoading.value = false;
				return;
			}
		}

		// 获取地块名称（从选项中查找地块ID对应的名称）
		const selectedParcelOption = parcelOptions.value.find(opt => opt.value === parkingDialogForm.block);
		const parcelName = selectedParcelOption ? selectedParcelOption.label : parkingDialogForm.block;

		// 获取楼栋名称
		const selectedBuildingOption = buildingOptions.value.find(opt => opt.value === parkingDialogForm.building);
		const buildingName = selectedBuildingOption ? selectedBuildingOption.label : parkingDialogForm.building;

		// 获取楼层名称
		const selectedFloorOption = floorOptions.value.find(opt => opt.value === parkingDialogForm.floor);
		const floorName = selectedFloorOption ? selectedFloorOption.label : parkingDialogForm.floor;

		// 构建房源数据对象
		const roomData = {
			projectId: formData.projectId,
			id: parkingDialogForm.id || '',     // 房源ID，用于更新
			roomId: parkingDialogForm.roomId || '',
			roomName: parkingDialogForm.name,
			parcelId: parkingDialogForm.block,
			parcelName: parcelName,
			buildingId: parkingDialogForm.building,
			buildingName: buildingName,
			floorId: parkingDialogForm.floor,
			floorName: floorName,
			areaType: parkingDialogForm.areaType,
			buildArea: Number(parkingDialogForm.buildingArea),
			innerArea: Number(parkingDialogForm.innerArea),
			productType: parkingDialogForm.productType,
			isSale: parkingDialogForm.saleable === '是' ? 1 : 0,
			isCompanySelf: parkingDialogForm.shared === '是' ? 1 : 0,
			propertyType: parkingDialogForm.selfHeldType === '自持' ? '2' : '1',
			selfHoldingTime: parkingDialogForm.selfHeldExpireDate || null
		};

		console.log('保存房源数据:', roomData);

		try {
			// 调用保存房源API
			await saveRoom(roomData);

			// 更新本地数据
			if (parkingDialogMode.value === 'add') {
				const nextIndex = (parkingData.value.length + 1).toString();
				parkingData.value.push({
					...parkingDialogForm,
					index: nextIndex,
					id: roomData.id, // 可能服务器会返回新ID，这里暂时使用前端ID
					block: parcelName, // 使用地块名称
					blockId: parkingDialogForm.block, // 保存地块ID
					building: buildingName, // 使用楼栋名称
					buildingId: parkingDialogForm.building, // 保存楼栋ID
					floor: floorName, // 使用楼层名称
					floorId: parkingDialogForm.floor, // 保存楼层ID
					buildingArea: Number(parkingDialogForm.buildingArea),
					innerArea: Number(parkingDialogForm.innerArea)
				});
			} else if (parkingDialogMode.value === 'edit') {
				const idx = parkingData.value.findIndex(item => item.index === editingParkingIndex);
				if (idx !== -1) {
					parkingData.value[idx] = {
						...parkingDialogForm,
						index: editingParkingIndex,
						block: parcelName, // 使用地块名称
						blockId: parkingDialogForm.block, // 保存地块ID
						building: buildingName, // 使用楼栋名称
						buildingId: parkingDialogForm.building, // 保存楼栋ID
						floor: floorName, // 使用楼层名称
						floorId: parkingDialogForm.floor, // 保存楼层ID
						buildingArea: Number(parkingDialogForm.buildingArea),
						innerArea: Number(parkingDialogForm.innerArea)
					};
				}
			}

			// 成功提示
			Message.success(`${parkingDialogMode.value === 'add' ? '添加' : '编辑'}房源成功`);

			// 关闭弹窗
			parkingDialogVisible.value = false;

			// 刷新房源列表
			loadRoomData();
		} catch (apiError) {
			console.error(`${parkingDialogMode.value === 'add' ? '添加' : '编辑'}房源API调用失败:`, apiError);
			// Message.error(`${parkingDialogMode.value === 'add' ? '添加' : '编辑'}房源失败，请重试`);
		}
	} catch (error) {
		console.error(`${parkingDialogMode.value === 'add' ? '添加' : '编辑'}房源失败:`, error);
		// Message.error(`${parkingDialogMode.value === 'add' ? '添加' : '编辑'}房源失败`);
	} finally {
		parkingSaveLoading.value = false;
	}
};

const handleParkingDialogCancel = () => {
	parkingDialogVisible.value = false;
};

const openEditParkingDialog = async (record: any) => {
	parkingDialogMode.value = 'edit';

	// 复制记录到表单，但需要正确映射地块、楼栋、楼层的ID值
	Object.assign(parkingDialogForm, {
		...record,
		// 确保使用ID值而不是显示名称
		block: record.blockId || record.block || '',
		building: record.buildingId || record.building || '',
		floor: record.floorId || record.floor || '',
		areaType: record.areaType + ''
	});
	editingParkingIndex = record.index;

	// 加载地块选项和产品类型字典
	await Promise.all([
		loadParcelOptions(),
		loadProductTypeOptions()
	]);
	// 地块选项加载完成后，再加载楼栋和楼层选项
	loadDependentOptions(record);

	parkingDialogVisible.value = true;
};

// 加载依赖的楼栋和楼层选项
const loadDependentOptions = (record: any) => {
	// 如果有地块值，加载对应的楼栋选项
	const parcelId = record.blockId || record.block;
	if (parcelId) {
		loadBuildingOptions(parcelId).then(() => {
			// 楼栋选项加载完成后，再加载楼层选项
			const buildingId = record.buildingId || record.building;
			if (buildingId) {
				loadFloorOptions(buildingId);
			}
		});
	}
};

// 分期数据类型
interface PhaseRecord {
	name: string;
	id?: string;
	mdmStageId?: string;
	projectId?: string;
	isNew?: boolean; // 标记是否为新增项
}

// 分期数据
const phases = ref<PhaseRecord[]>([]);

// 添加分期
const addPhase = () => {
	// 为新增的分期生成临时ID，用于前端标识，保存时会被处理
	const tempId = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
	phases.value.push({
		name: '',
		id: tempId,
		isNew: true // 标记为新增项
	});
};

// 检查分期是否被地块关联
const isPhaseLinkedToParcel = (phaseId: string | undefined): boolean => {
	if (!phaseId) return false;
	return landData.value.some(parcel => parcel.phase === phaseId);
};

// 检查地块是否被楼栋关联
const isParcelLinkedToBuilding = (parcelId: string | undefined): boolean => {
	if (!parcelId) return false;
	return buildingData.value.some(building => building.blockId === parcelId);
};

// 检查楼栋是否被房源关联
const isBuildingLinkedToRoom = (buildingId: string | undefined): boolean => {
	if (!buildingId) return false;
	return parkingData.value.some(room => room.buildingId === buildingId);
};

// 删除分期
const removePhase = (index: number) => {
	const phase = phases.value[index];
	if (isPhaseLinkedToParcel(phase.id)) {
		Message.warning('该分期已被地块关联，无法删除');
		return;
	}
	phases.value.splice(index, 1);
};

// 修改formData中的phase计算属性
const updatePhase = computed(() => {
	formData.phase = phases.value.map((p: PhaseRecord) => p.name).join(',');
});

watch(phases, () => {
	updatePhase.value;
}, { deep: true });

// 楼栋弹窗相关
const buildingDialogVisible = ref(false);
const buildingDialogTitle = ref('新增楼栋');
const buildingDialogMode = ref<'add' | 'edit'>('add');
const buildingDialogFormRef = ref<any>(null);
const buildingDialogForm = ref({
	buildingName: '',
	block: '',
	upFloorNums: 1,
	underFloorNums: 0,
	buildingId: ''
});
let editingBuildingIndex = '';
const saveLoading = ref(false);

const handleBuildingDialogOk = async () => {
	try {
		// 表单验证
		if (!buildingDialogFormRef.value) return;

		const errors = await buildingDialogFormRef.value.validate();
		if (errors) return;

		saveLoading.value = true;

		// 构建保存数据对象
		const saveData = {
			projectId: formData.projectId,
			buildingName: buildingDialogForm.value.buildingName,
			buildingId: buildingDialogForm.value.buildingId || '',
			parcelId: buildingDialogForm.value.block,
			upFloorNums: buildingDialogForm.value.upFloorNums,
			underFloorNums: buildingDialogForm.value.underFloorNums
		};

		// 调用保存楼栋接口
		await saveBuilding(saveData);

		// 成功提示
		Message.success(`${buildingDialogMode.value === 'add' ? '添加' : '编辑'}楼栋成功`);

		// 重新加载楼栋数据
		if (formData.projectId) {
			await loadBuildingData(formData.projectId);
		}

		// 关闭弹窗
		buildingDialogVisible.value = false;
		saveLoading.value = false;
	} catch (error) {
		console.error('楼栋保存失败:', error);
		saveLoading.value = false;
	}
};

const handleBuildingDialogCancel = () => {
	buildingDialogVisible.value = false;
};

// 地块选项
const parcelOptions = ref<{ value: string; label: string }[]>([]);
// 地块选项加载状态，防止重复加载
const parcelOptionsLoading = ref(false);

// 定义emit
const emit = defineEmits(['refresh']);

const handleAddLand = () => {
	const nextIndex = landData.value.length + 1;
	landData.value.push({
		index: nextIndex,
		blockName: '',
		usage: '',
		address: '',
		phase: ''
	});
};

const handleDeleteLand = (record: LandRecord) => {
	if (isParcelLinkedToBuilding(record.id || record.mdmParcelId)) {
		Message.warning('该地块已被楼栋关联，无法删除');
		return;
	}
	landData.value = landData.value.filter(item => item !== record);
	landData.value.forEach((item, idx) => item.index = idx + 1);
};

// Add form reference
const basicFormRef = ref<any>(null);

onMounted(async () => {
	// 加载字典数据
	await Promise.all([
		loadProductTypeOptions(),
		loadAssetTypeOptions()
	]);

	// 如果有项目ID，加载地块数据
	if (formData.projectId) {
		loadParcelOptions(formData.projectId);
	}

	// 如果已有经纬度但没有地址，尝试反地理编码
	if (formData.lng && formData.lat) {
		reverseGeocode(formData.lng, formData.lat);
	}

	// 输出区域数据信息
	console.log('区域数据加载成功', areaData.array.length);
});

// 楼栋搜索
const searchBuilding = ref('');

// 房源搜索
const searchRoom = ref('');

// 楼栋分页配置
const buildingPagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showTotal: true,
	showPageSize: true,
	pageSizeOptions: [10, 20, 50, 100]
});

// 房源分页配置
const roomPagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showTotal: true,
	showPageSize: true,
	pageSizeOptions: [10, 20, 50, 100]
});

// 车位表格多选
const selectedParkingKeys = ref<string[]>([]);
const rowSelection = reactive({
	type: 'checkbox',
	showCheckedAll: true,
	onlyCurrent: false
});

// 计算当前页是否有可删除的选中项
const hasValidSelectedRooms = computed(() => {
	if (selectedParkingKeys.value.length === 0) {
		return false;
	}
	
	// 检查选中的房源是否在当前页面数据中，且可以删除
	const currentPageDeletableRooms = parkingData.value.filter(room =>
		selectedParkingKeys.value.includes(room.index) && room.source !== '主数据'
	);
	
	return currentPageDeletableRooms.length > 0;
});

// 楼栋过滤
const filteredBuildingData = computed(() => {
	let data = buildingData.value;
	if (selectedBlock.value) {
		data = data.filter(item => item.blockId === selectedBlock.value);
	}

	// 如果有搜索内容，进行过滤
	if (searchBuilding.value) {
		const keyword = searchBuilding.value.toLowerCase();
		data = data.filter(item =>
			item.buildingName.toLowerCase().includes(keyword)
		);
	}

	return data;
});

// 处理楼栋分页变化
const handleBuildingPageChange = (current: number) => {
	buildingPagination.current = current;
};

// 楼栋每页条数变化
const handleBuildingPageSizeChange = (pageSize: number) => {
	buildingPagination.pageSize = pageSize;
	buildingPagination.current = 1;
};

// 处理房源分页变化
const handleRoomPageChange = (current: number) => {
	roomPagination.current = current;
	// 清空选中状态
	selectedParkingKeys.value = [];
};

// 房源每页条数变化
const handleRoomPageSizeChange = (pageSize: number) => {
	roomPagination.pageSize = pageSize;
	roomPagination.current = 1;
	// 清空选中状态
	selectedParkingKeys.value = [];
};

// 处理楼栋信息页面的地块选择变化
const handleBuildingTabParcelChange = (value: string) => {
	// 重置分页并执行过滤
	buildingPagination.current = 1;
	selectedBlock.value = value;

	// 使用API进行查询
	if (formData.projectId) {
		loadBuildingData(formData.projectId);
	}
};

// 处理房源信息页面的地块选择变化
const handleRoomTabParcelChange = (value: string) => {
	// 重置分页并加载房源数据
	roomPagination.current = 1;
	selectedBlock.value = value;
	// 清空选中状态
	selectedParkingKeys.value = [];

	console.log('选择房源页面地块:', value);

	// 加载房源数据
	loadRoomData();
};

// 处理楼栋搜索
const handleSearchBuilding = () => {
	buildingPagination.current = 1;
	// 执行搜索查询
	if (formData.projectId) {
		loadBuildingData(formData.projectId);
	}
};

// 处理房源搜索
const handleSearchRoom = () => {
	roomPagination.current = 1;
	// 清空选中状态
	selectedParkingKeys.value = [];
	// 加载筛选后房源数据
	loadRoomData();
};

// 处理车位多选变化
const handleParkingSelectionChange = (rowKeys: string[]) => {
	selectedParkingKeys.value = rowKeys;
};

// 处理批量删除车位
const handleBatchDeleteParking = () => {
	if (selectedParkingKeys.value.length === 0) {
		Message.warning('请先选择要删除的房源');
		return;
	}

	// 过滤出可删除的房源（只有系统新增的房源可以删除）
	const deletableRooms = parkingData.value.filter(room =>
		selectedParkingKeys.value.includes(room.index) && room.source !== '主数据'
	);

	if (deletableRooms.length === 0) {
		Message.warning('选中的房源中没有可删除的项目（主数据房源不可删除）');
		return;
	}

	if (deletableRooms.length !== selectedParkingKeys.value.length) {
		Message.warning(`选中的 ${selectedParkingKeys.value.length} 个房源中，只有 ${deletableRooms.length} 个可以删除（主数据房源不可删除）`);
		return;
	}

	Modal.confirm({
		title: '确认删除',
		content: `确定要删除选中的 ${deletableRooms.length} 个房源吗？`,
		onOk: async () => {
			try {
				deleteLoading.value = true;
				// 获取可删除房源的ID
				const deletableIds = deletableRooms.map(room => room.id).filter(id => id);
				console.log('可删除的房源IDs:', deletableIds);

				if (deletableIds.length > 0) {
					await deleteRoom(deletableIds.join(','));
				}
				deleteLoading.value = false;

				Message.success('批量删除房源成功');
				// 清空选中项
				selectedParkingKeys.value = [];
				// 重新加载房源数据
				loadRoomData();
			} catch (error) {
				console.error('批量删除房源失败:', error);
				// Message.error('批量删除房源失败');
				deleteLoading.value = false;
			}
		}
	});
};

// 处理下载模板
const handleDownloadTemplate = () => {
	if (!formData.projectId) {
		Message.warning('请先保存基本信息');
		return;
	}

	try {
		// 使用exportExcel方法下载模板
		exportExcel(downloadRoomTemplate, formData.projectId, '房源导入模板')
			.then(() => {
				Message.success('模板下载成功');
			})
	} catch (error) {
		console.error('下载模板失败:', error);
	}
};

// 处理导入房源
const handleImportRoom = () => {
	if (!formData.projectId) {
		Message.warning('请先保存基本信息');
		return;
	}

	// 创建文件选择器
	const input = document.createElement('input');
	input.type = 'file';
	input.accept = '.xlsx,.xls';
	input.style.display = 'none';

	// 监听文件选择事件
	input.onchange = (event) => {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			const file = target.files[0];

			// 检查文件类型
			if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
				Message.error('请上传Excel文件(.xlsx或.xls)');
				return;
			}

			// 显示加载状态
			const loadingInstance = Message.loading({
				content: '正在导入车位数据...',
				duration: 0
			});

			// 使用importExcelFile方法导入文件
			importExcelFile(file, importRoomTemplate)
				.then((res: any) => {
					if (res && res.code === 200) {
						Message.success('房源导入成功');
						// 刷新房源列表
						loadRoomData();
					}
				})
				.catch((error: any) => {
					console.error('房源导入失败:', error);
				})
				.finally(() => {
					// 关闭加载状态
					loadingInstance.close();
					// 移除input元素
					document.body.removeChild(input);
				});
		}
	};

	// 添加到DOM并触发点击
	document.body.appendChild(input);
	input.click();
};


// 房源加载
const loadRoomData = async () => {
	if (!formData.projectId) return;

	try {
		// 清空选中状态，确保重新加载数据后状态正确
		selectedParkingKeys.value = [];
		
		// 确保先加载地块选项
		if (parcelOptions.value.length === 0) {
			await loadParcelOptions(formData.projectId);
		}

		const params = {
			projectId: formData.projectId,
			pageNum: roomPagination.current,
			pageSize: roomPagination.pageSize,
			parcelId: selectedBlock.value || undefined,
			buildingOrRoomName: searchRoom.value || undefined
		};

		// 调用获取房源列表API
		const res = await getRoomList(params);
		console.log('房源列表响应:', res);

		if (res && res.rows && Array.isArray(res.rows)) {
			roomPagination.total = res.total || 0;

			// 创建一个地块ID到名称的映射，优先使用API直接返回的parcelName
			const parcelIdToNameMap: Record<string, string> = {};

			// 收集API返回的地块数据
			res.rows.forEach((item: any) => {
				if (item.parcelId && item.parcelName) {
					parcelIdToNameMap[item.parcelId] = item.parcelName;
				}
			});

			console.log('房源API返回的地块映射:', parcelIdToNameMap);

			// 设置房源数据
			parkingData.value = res.rows.map((room: any, index: number) => {
				// 尝试多种方式获取地块名称
				let parcelName = room.parcelName || '';

				if (!parcelName) {
					parcelName = getParcelNameById(room.parcelId, parcelOptions.value);
				}

				if (!parcelName && room.parcelId && parcelIdToNameMap[room.parcelId]) {
					parcelName = parcelIdToNameMap[room.parcelId];
				}

				if (parcelName) {
					console.log(`已匹配到房源地块: parcelId=${room.parcelId}, parcelName=${parcelName}`);
				} else if (room.parcelId) {
					console.log(`未找到房源地块: parcelId=${room.parcelId}, API数据:`, room);

					// 如果还是找不到，尝试把parcelId作为名称
					parcelName = room.parcelId;
				}

				//productTypeOptions
				if (!!room.productType) {
					room.productTypeName = productTypeOptions.value.find(item => item.value === room.productType)?.label || '';
				}


				return {
					index: ((roomPagination.current - 1) * roomPagination.pageSize + index + 1).toString(),
					id: room.id || '',
					roomId: room.roomId || '',
					name: room.roomName || '',
					roomName: room.roomName || '', // 添加roomName字段以匹配表格列的dataIndex
					block: parcelName, // 使用地块名称
					blockId: room.parcelId || '', // 保存地块ID
					building: room.buildingName || '',
					buildingId: room.buildingId || '',
					floor: room.floorName || '',
					floorId: room.floorId || '',
					areaType: room.areaType,
					buildingArea: room.buildArea || 0,
					innerArea: room.innerArea || 0,
					productType: room.productType || '',
					productTypeName: room.productTypeName || '',
					saleable: room.isSale === 1 ? '是' : '否',
					shared: room.isCompanySelf === 1 ? '是' : '否',
					selfHeldType: room.propertyType === '2' ? '自持' : '非自持',
					selfHeldExpireDate: room.selfHoldingTime || '',
					source: room.mdmRoomId ? '主数据' : '手动添加'
				};
			});
		} else {
			console.log('房源列表为空');
			parkingData.value = [];
			roomPagination.total = 0;
		}
	} catch (error) {
		console.error('获取房源列表失败:', error);
		// Message.error('获取房源列表失败');
		parkingData.value = [];
	}
};

// 处理房源保存
const handleSaveRoom = async () => {
	if (!formData.projectId) {
		Message.warning('请先保存基本信息');
		return;
	}

	saveLoading.value = true;
	try {
		// 验证是否有房源数据
		if (parkingData.value.length === 0) {
			Message.warning('请添加房源信息');
			return;
		}

		// 循环调用保存房源接口
		for (const room of parkingData.value) {
			// 处理自持到期日期，有些房源可能没有这个属性
			const selfHoldingTime = (room as any).selfHeldExpireDate || null;

			const saveData = {
				projectId: formData.projectId,
				id: room.id || '',             // 房源ID，用于更新
				roomId: room.roomId || '',     // 房源编码
				roomName: room.name,
				parcelId: room.blockId,        // 地块ID
				parcelName: room.block,        // 地块名称
				buildingId: room.buildingId,   // 楼栋ID
				buildingName: room.building,   // 楼栋名称
				floorId: room.floorId,         // 楼层ID
				floorName: room.floor,         // 楼层名称
				areaType: room.areaType,
				buildArea: room.buildingArea,
				innerArea: room.innerArea,
				productType: room.productType,
				isSale: room.saleable === '是' ? 1 : 0,
				isCompanySelf: room.shared === '是' ? 1 : 0,
				propertyType: room.selfHeldType === '自持' ? '2' : '1',
				selfHoldingTime
			};

			console.log('保存房源数据:', saveData);

			// 逐个保存房源信息
			await saveRoom(saveData);
		}

		console.log('房源保存成功');

		// 显示保存成功消息
		Message.success('房源保存成功');
	} catch (error) {
		console.error('房源保存失败:', error);
		// Message.error('房源保存失败');
	} finally {
		saveLoading.value = false;
	}
};

// 保存基本信息
const saveProjectBasic = async () => {
	saveLoading.value = true;
	try {
		// 使用表单验证
		if (basicFormRef.value) {
			try {
				// 验证所有字段
				const errors = await basicFormRef.value.validate();

				// 如果有错误，不继续执行
				if (errors) {
					saveLoading.value = false;
					return;
				}
			} catch (validationError) {
				// 验证失败处理
				console.error('表单验证失败:', validationError);
				Message.error('请完善必填信息');
				saveLoading.value = false;
				return;
			}
		}

		// 验证地块信息
		if (landData.value.length === 0) {
			Message.error('请至少添加一个地块');
			saveLoading.value = false;
			return;
		}

		// 验证地块必填字段
		const invalidLand = landData.value.find(land => !land.blockName || !land.usage || !land.address || !land.phase);
		if (invalidLand) {
			Message.error('请完善地块信息的必填项');
			saveLoading.value = false;
			return;
		}

		// 调试：打印省市区字段的值
		console.log('保存前的省市区字段值:', {
			provinceCode: formData.provinceCode,
			provinceName: formData.provinceName,
			cityCode: formData.cityCode,
			cityName: formData.cityName,
			countryCode: formData.countryCode,
			countryName: formData.countryName,
			areaValue: formData.areaValue
		});

		// 计算删除的分期ID列表
		const deleteStageIdList: string[] = [];
		if (originalStages.value.length > 0) {
			const currentStageIds = phases.value.map(phase => phase.id).filter(id => id);
			originalStages.value.forEach(originalStage => {
				if (originalStage.id && !currentStageIds.includes(originalStage.id)) {
					deleteStageIdList.push(originalStage.id);
				}
			});
		}

		// 计算删除的地块ID列表
		const deleteParcelIdList: string[] = [];
		if (originalParcels.value.length > 0) {
			const currentParcelIds = landData.value.map(parcel => parcel.id).filter(id => id);
			originalParcels.value.forEach(originalParcel => {
				if (originalParcel.id && !currentParcelIds.includes(originalParcel.id)) {
					deleteParcelIdList.push(originalParcel.id);
				}
			});
		}

		// 构建项目数据结构
		const projectData = {
			// 基本信息
			id: formData.projectId, // 编辑时使用现有ID
			mdmName: formData.mdmName,
			code: formData.projectCode,
			name: formData.name,
			type: "2", // 项目类型 (1内部 2外部)
			assetType: formData.assetType, // 确保资产分类字段被保存

			// 位置信息
			projectAddress: formData.address, // 使用projectAddress作为地址字段
			location: formData.location, // 项目定位保存到location字段
			longitude: formData.lng,
			latitude: formData.lat,

			// 省市区信息
			provinceCode: formData.provinceCode,
			provinceName: formData.provinceName,
			cityCode: formData.cityCode,
			cityName: formData.cityName,
			countryCode: formData.countryCode,
			countryName: formData.countryName,

			// 产权信息
			propertyUnit: formData.propertyUnit,

			// 分期信息和地块信息使用新格式
			stageList: phases.value.map((phase: PhaseRecord) => {
				const stageData: any = {
					mdmStageId: phase.mdmStageId || '',
					projectId: formData.projectId || '',
					stageName: phase.name // 新增和编辑都需要传递stageName
				};

				// 区分新增和编辑：新增的分期不传ID，编辑的分期传原ID
				if (!phase.isNew && phase.id) {
					stageData.id = phase.id;
					console.log(`编辑分期: ${phase.name}, ID: ${phase.id}`);
				} else {
					console.log(`新增分期: ${phase.name}, 不传ID`);
				}

				return stageData;
			}),

			parcelList: landData.value.map((parcel: LandRecord) => {
				// 查找对应的分期信息
				const selectedPhase = phases.value.find(phase =>
					phase.id === parcel.phase || phase.name === parcel.phase
				);

				const parcelData: any = {
					mdmParcelId: parcel.mdmParcelId || '',
					projectId: formData.projectId || '',
					parcelName: parcel.blockName,
					mdmNatureName: parcel.mdmNatureName || '',
					landUsage: parcel.usage === '工业用地' ? 1 : 2, // 1工业用地 2商业用地
					mdmAddress: parcel.mdmAddress || '',
					address: parcel.address,
					totalSelfArea: parcel.totalSelfArea || ''
				};

				// 处理地块ID：新增地块不传ID，编辑地块传原ID
				if (parcel.id && !parcel.id.startsWith('temp_')) {
					parcelData.id = parcel.id;
					console.log(`编辑地块: ${parcel.blockName}, ID: ${parcel.id}`);
				} else {
					console.log(`新增地块: ${parcel.blockName}, 不传ID`);
				}

				// 处理分期关联：如果选择了分期，需要传递分期信息
				if (selectedPhase) {
					if (!selectedPhase.isNew && selectedPhase.id) {
						// 编辑的分期，传递分期ID
						parcelData.stageId = selectedPhase.id;
					}
					// 无论新增还是编辑，都传递分期名称
					parcelData.stageName = selectedPhase.name;
				}

				console.log(`地块 ${parcel.blockName}: 分期=${selectedPhase?.name}, stageId=${parcelData.stageId || '空'}, stageName=${parcelData.stageName || '空'}`);

				return parcelData;
			}),

			// 删除的ID列表
			deleteStageIdList: deleteStageIdList.length > 0 ? deleteStageIdList : undefined,
			deleteParcelIdList: deleteParcelIdList.length > 0 ? deleteParcelIdList : undefined
		};

		// 调试：打印完整的项目数据
		console.log('发送到API的项目数据:', projectData);
		console.log('删除的分期ID列表:', deleteStageIdList);
		console.log('删除的地块ID列表:', deleteParcelIdList);

		// 调用保存接口
		const res = await addProject(projectData);

		if (res.code === 200) {
			Message.success('保存成功');

			// 保存成功后，获取返回的项目ID
			// API返回的data可能是字符串ID或者包含id字段的对象
			let projectId = '';
			if (typeof res.data === 'string') {
				// 如果返回的是字符串ID
				projectId = res.data;
			} else if (res.data && res.data.id) {
				// 如果返回的是包含id字段的对象
				projectId = res.data.id;
			}

			if (projectId) {
				formData.projectId = projectId;

				// 如果是新增模式，现在切换为编辑模式
				if (modalType.value === 'add') {
					modalType.value = 'edit';
				}

				// 只有在原本是新增模式下才切换到楼栋标签
				if (currentTab.value === 'basic') {
					currentTab.value = 'building';
				}
			}

			// 无论新增还是编辑，保存成功后都要重新加载地块数据
			if (formData.projectId) {
				await loadParcelOptions(formData.projectId);
				// 加载房源数据
				await loadRoomData();
			}

			// 关闭对话框并通知父组件刷新列表
			// visible.value = false;

			// 触发刷新列表事件
			emit('refresh');
		} else {
			// Message.error(res.msg || '保存失败');
		}
	} catch (error) {
		console.error('保存项目失败:', error);
		// Message.error('保存失败，请稍后重试');
	} finally {
		saveLoading.value = false;
	}
};

// 原始数据，用于跟踪删除的项目
const originalParcels = ref<any[]>([]);
const originalStages = ref<any[]>([]);
</script>

<style scoped lang="less">
.external-project-container {
	display: flex;
	height: 100%;

	.content-section {
		flex: 1;
		padding: 0 16px;
		overflow-y: auto;
	}

	.section-title {
		// font-size: 16px;
		// font-weight: 500;
		margin: 16px 0;
		// color: var(--color-text-1);
	}

	.button-group {
		margin-bottom: 16px;
		text-align: right;

		.arco-btn {
			margin-right: 8px;
		}
	}

	.button-group2 {
		margin-bottom: 0px;
	}

	.phase-buttons {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 8px;
		margin-bottom: 16px;

		.phase-item {
			width: calc(25% - 8px);
			display: flex;
			align-items: center;
			position: relative;


			.phase-input {
				width: 100%;
				overflow: hidden;
				background-color: var(--color-fill-1);
				border-color: var(--color-fill-2);
			}

			.delete-icon {
				margin-left: 8px;
				font-size: 14px;
				color: var(--color-text-3);
				cursor: pointer;
				position: absolute;
				right: 5px;

				&:hover {
					color: rgb(var(--danger-6));
				}
			}
		}

		.add-phase-btn {
			background-color: var(--color-fill-2);
			border-color: var(--color-fill-2);
		}
	}

	.room-info {
		display: flex;
		align-items: center;
		margin-bottom: 16px;
		gap: 16px;
	}

	:deep(.arco-form) {
		.arco-form-item {
			margin-bottom: 16px;
		}

		.arco-form-item-label {
			white-space: nowrap;
		}
	}
}
</style>