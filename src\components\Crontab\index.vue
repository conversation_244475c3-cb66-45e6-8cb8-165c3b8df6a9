<template>
    <div>
        <a-tabs type="card">
            <a-tab-pane key="second" title="秒" v-if="shouldHide('second')">
                <CrontabSecond @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj"
                    ref="cronsecond" />
            </a-tab-pane>

            <a-tab-pane key="min" title="分钟" v-if="shouldHide('min')">
                <CrontabMin @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj" ref="cronmin" />
            </a-tab-pane>

            <a-tab-pane key="hour" title="小时" v-if="shouldHide('hour')">
                <CrontabHour @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj" ref="cronhour" />
            </a-tab-pane>

            <a-tab-pane key="day" title="日" v-if="shouldHide('day')">
                <CrontabDay @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj" ref="cronday" />
            </a-tab-pane>

            <a-tab-pane key="month" title="月" v-if="shouldHide('month')">
                <CrontabMonth @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj"
                    ref="cronmonth" />
            </a-tab-pane>

            <a-tab-pane key="week" title="周" v-if="shouldHide('week')">
                <CrontabWeek @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj" ref="cronweek" />
            </a-tab-pane>

            <a-tab-pane key="year" title="年" v-if="shouldHide('year')">
                <CrontabYear @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj" ref="cronyear" />
            </a-tab-pane>
        </a-tabs>

        <div class="popup-main">
            <div class="popup-result">
                <p class="title">时间表达式</p>
                <table>
                    <thead>
                        <tr>
                            <th v-for="item of tabTitles" :key="item">{{ item }}</th>
                            <th>Cron 表达式</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <span v-if="crontabValueObj.second.length < 10">{{ crontabValueObj.second }}</span>
                                <a-tooltip v-else :content="crontabValueObj.second" placement="top"><span>{{
                                    crontabValueObj.second }}</span></a-tooltip>
                            </td>
                            <td>
                                <span v-if="crontabValueObj.min.length < 10">{{ crontabValueObj.min }}</span>
                                <a-tooltip v-else :content="crontabValueObj.min" placement="top"><span>{{
                                    crontabValueObj.min }}</span></a-tooltip>
                            </td>
                            <td>
                                <span v-if="crontabValueObj.hour.length < 10">{{ crontabValueObj.hour }}</span>
                                <a-tooltip v-else :content="crontabValueObj.hour" placement="top"><span>{{
                                    crontabValueObj.hour }}</span></a-tooltip>
                            </td>
                            <td>
                                <span v-if="crontabValueObj.day.length < 10">{{ crontabValueObj.day }}</span>
                                <a-tooltip v-else :content="crontabValueObj.day" placement="top"><span>{{
                                    crontabValueObj.day }}</span></a-tooltip>
                            </td>
                            <td>
                                <span v-if="crontabValueObj.month.length < 10">{{ crontabValueObj.month }}</span>
                                <a-tooltip v-else :content="crontabValueObj.month" placement="top"><span>{{
                                    crontabValueObj.month }}</span></a-tooltip>
                            </td>
                            <td>
                                <span v-if="crontabValueObj.week.length < 10">{{ crontabValueObj.week }}</span>
                                <a-tooltip v-else :content="crontabValueObj.week" placement="top"><span>{{
                                    crontabValueObj.week }}</span></a-tooltip>
                            </td>
                            <td>
                                <span v-if="crontabValueObj.year.length < 10">{{ crontabValueObj.year }}</span>
                                <a-tooltip v-else :content="crontabValueObj.year" placement="top"><span>{{
                                    crontabValueObj.year }}</span></a-tooltip>
                            </td>
                            <td class="result">
                                <span v-if="crontabValueString.length < 90">{{ crontabValueString }}</span>
                                <a-tooltip v-else :content="crontabValueString" placement="top"><span>{{
                                    crontabValueString
                                        }}</span></a-tooltip>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <CrontabResult :ex="crontabValueString" />

            <div class="pop_btn">
                <a-space>
                    <a-button type="primary" @click="submitFill">确定</a-button>
                    <a-button status="warning" @click="clearCron">重置</a-button>
                    <a-button @click="hidePopup">取消</a-button>
                </a-space>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import CrontabSecond from "./second.vue";
import CrontabMin from "./min.vue";
import CrontabHour from "./hour.vue";
import CrontabDay from "./day.vue";
import CrontabMonth from "./month.vue";
import CrontabWeek from "./week.vue";
import CrontabYear from "./year.vue";
import CrontabResult from "./result.vue";

interface CrontabValue {
    second: string;
    min: string;
    hour: string;
    day: string;
    month: string;
    week: string;
    year: string;
}

interface Props {
    hideComponent?: string[];
    expression?: string;
}

const emit = defineEmits<{
    (e: 'hide'): void;
    (e: 'fill', value: string): void;
}>();

const props = withDefaults(defineProps<Props>(), {
    hideComponent: () => [],
    expression: ''
});

const tabTitles = ref(["秒", "分钟", "小时", "日", "月", "周", "年"]);
const tabActive = ref(0);
const hideComponent = ref<string[]>([]);
const expressionLocal = ref('');
const crontabValueObj = ref<CrontabValue>({
    second: "*",
    min: "*",
    hour: "*",
    day: "*",
    month: "*",
    week: "?",
    year: "",
});

const crontabValueString = computed(() => {
    const obj = crontabValueObj.value;
    return `${obj.second} ${obj.min} ${obj.hour} ${obj.day} ${obj.month} ${obj.week}${obj.year ? ' ' + obj.year : ''}`;
});

watch(expressionLocal, (val) => {
    resolveExp();
});

function shouldHide(key: string): boolean {
    return !(hideComponent.value && hideComponent.value.includes(key));
}

function resolveExp(): void {
    if (expressionLocal.value) {
        const arr = expressionLocal.value.split(/\s+/);
        if (arr.length >= 6) {
            const obj: CrontabValue = {
                second: arr[0],
                min: arr[1],
                hour: arr[2],
                day: arr[3],
                month: arr[4],
                week: arr[5],
                year: arr[6] || ""
            };
            crontabValueObj.value = { ...obj };
        }
    } else {
        clearCron();
    }
}

function updateCrontabValue(name: keyof CrontabValue, value: string): void {
    crontabValueObj.value[name] = value;
}

function checkNumber(value: number, minLimit: number, maxLimit: number): number {
    value = Math.floor(value);
    return Math.min(Math.max(value, minLimit), maxLimit);
}

function hidePopup(): void {
    emit("hide");
}

function submitFill(): void {
    emit("fill", crontabValueString.value);
    hidePopup();
}

function clearCron(): void {
    crontabValueObj.value = {
        second: "*",
        min: "*",
        hour: "*",
        day: "*",
        month: "*",
        week: "?",
        year: "",
    };
}

onMounted(() => {
    expressionLocal.value = props.expression;
    hideComponent.value = props.hideComponent || [];
});
</script>

<style lang="less" scoped>
.pop_btn {
    text-align: center;
    margin-top: 16px;
}

.popup-main {
    position: relative;
    margin: 10px auto;
    border-radius: 5px;
    font-size: 12px;
    overflow: hidden;
}

.popup-result {
    box-sizing: border-box;
    line-height: 24px;
    margin: 25px auto;
    padding: 15px 10px 10px;
    border: 1px solid var(--color-border);
    position: relative;

    .title {
        position: absolute;
        top: -28px;
        left: 50%;
        width: 140px;
        font-size: 14px;
        margin-left: -70px;
        text-align: center;
        line-height: 30px;
        background: var(--color-bg-1);
    }

    table {
        text-align: center;
        width: 100%;
        margin: 0 auto;

        td:not(.result) {
            width: 3.5rem;
            min-width: 3.5rem;
            max-width: 3.5rem;
        }

        span {
            display: block;
            width: 100%;
            font-family: arial;
            line-height: 30px;
            height: 30px;
            white-space: nowrap;
            overflow: hidden;
            border: 1px solid var(--color-border);
        }
    }
}

.popup-result-scroll {
    font-size: 12px;
    line-height: 24px;
    height: 10em;
    overflow-y: auto;
}
</style>