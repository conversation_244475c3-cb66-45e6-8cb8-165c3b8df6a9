<template>
	<a-drawer :visible="visible" title="导入营收数据" :width="600" @cancel="handleCancel" @ok="handleOk"
		:confirm-loading="loading">
		<div class="import-content">
			<!-- 项目选择 -->
			<a-form ref="formRef" :model="formData" :rules="rules" :label-col-props="{ span: 4 }"
				:wrapper-col-props="{ span: 20 }" layout="horizontal">
				<a-form-item field="projectId" label="项目" required>
					<ProjectTreeSelect v-model="formData.projectId" @change="handleProjectChange" placeholder="请选择项目" />
				</a-form-item>
			</a-form>

			<!-- 模板下载 -->
			<a-alert type="info" style="margin-bottom: 16px;" show-icon>
				<template #title>
					导入说明
				</template>
				<div>
					<p>1. 请先选择项目，然后下载对应的导入模板</p>
					<p>2. 按照模板格式填写营收数据</p>
					<p>3. 上传填写完成的Excel文件进行批量导入</p>
					<p>4. 支持的文件格式：.xlsx、.xls</p>
				</div>
				<template #action>
					<a-button type="outline" size="small" @click="handleDownloadTemplate"
						:disabled="!formData.projectId">
						<template #icon>
							<icon-download />
						</template>
						下载模板
					</a-button>
				</template>
			</a-alert>

			<!-- 文件上传 -->
			<div class="upload-section">
				<h4>选择导入文件</h4>
				<a-upload ref="uploadRef" :file-list="fileList" :show-file-list="true" :auto-upload="false" :limit="1"
					@change="handleFileChange" @remove="handleFileRemove" accept=".xlsx,.xls" drag>
					<template #upload-button>
						<div class="upload-area">
							<div class="upload-icon">
								<icon-upload />
							</div>
							<div class="upload-text">
								<div>点击或拖拽文件到此处上传</div>
								<div class="upload-tip">支持 .xlsx、.xls 格式</div>
							</div>
						</div>
					</template>
				</a-upload>
			</div>

			<!-- 导入结果 -->
			<div v-if="importResult" class="import-result">
				<a-alert :type="importResult.success ? 'success' : 'warning'" show-icon>
					<template #title>
						导入结果
					</template>
					<div>
						<p>总计：{{ importResult.total }} 条</p>
						<p>成功：{{ importResult.successCount }} 条</p>
						<p v-if="importResult.failCount > 0">失败：{{ importResult.failCount }} 条</p>
						<div v-if="importResult.errors && importResult.errors.length > 0">
							<p>错误详情：</p>
							<ul>
								<li v-for="(error, index) in importResult.errors" :key="index">
									{{ error }}
								</li>
							</ul>
						</div>
					</div>
				</a-alert>
			</div>
		</div>
	</a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
	importRevenueByTemplate,
	downloadRevenueTemplate
} from '@/api/revenue'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import { exportExcel } from '@/utils/exportUtil'

// 定义emit
const emit = defineEmits<{
	success: []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const fileList = ref<any[]>([])
const importResult = ref<any>(null)

// 表单引用和数据
const formRef = ref<any>()
const uploadRef = ref<any>()
const formData = reactive({
	projectId: ''
})

// 表单验证规则
const rules = {
	projectId: [{ required: true, message: '请选择项目' }]
}

// 方法定义
const open = (defaultData?: any) => {
	visible.value = true
	resetForm()

	// 如果有默认数据，填充默认值
	if (defaultData) {
		Object.assign(formData, defaultData)
	}
}

const resetForm = () => {
	formData.projectId = ''
	fileList.value = []
	importResult.value = null
	formRef.value?.resetFields()
}

const handleCancel = () => {
	visible.value = false
	resetForm()
}

const handleOk = async () => {
	// try {
		// const valid = await formRef.value?.validate()
		if (!!currentFileData.value) {
			// if (fileList.value.length === 0) {
			// 	Message.warning('请选择要导入的文件')
			// 	return
			// }

			loading.value = true

			// const file = fileList.value[0].file || fileList.value[0]
			const response = await importRevenueByTemplate(currentFileData.value.file)

			// 处理导入结果
			if (response.code === 200) {
				importResult.value = {
					success: response.data.failCount === 0,
					total: response.data.successCount+response.data.failCount || 0,
					successCount: response.data.successCount || 0,
					failCount: response.data.failCount || 0,
					errors: response.data.errorMessages || []
				}

				if (response.data.failCount === 0) {
					Message.success('导入成功')
					setTimeout(() => {
						emit('success')
					}, 1000)
				} else {
					// Message.warning('导入完成，但存在部分失败记录')
				}
			}
		}else {

		}
	// } catch (error: any) {
	// 	Message.error(error.message || '导入失败')
	// 	importResult.value = {
	// 		success: false,
	// 		total: 0,
	// 		successCount: 0,
	// 		failCount: 0,
	// 		errors: [error.message || '导入失败']
	// 	}
	// } finally {
	// 	loading.value = false
	// }
}

const handleProjectChange = () => {
	// 项目变更时清空文件和结果
	fileList.value = []
	importResult.value = null
}

const handleDownloadTemplate = async () => {
	if (!formData.projectId) {
		Message.warning('请先选择项目')
		return
	}

	try {
		// let res = await downloadRevenueTemplate(formData.projectId)
		// console.log('res', res)
		// 使用 exportExcel
		exportExcel(downloadRevenueTemplate, formData.projectId, `商户营收管理_${new Date().getTime()}`)
		// Message.success('模板下载成功')
	} catch (error) {
		// Message.error('模板下载失败')
	}
}
let currentFileData = ref<any>(null)
const handleFileChange = (fileList: any[], fileItem: any) => {
	currentFileData.value = fileItem
	// 限制只能上传一个文件
	if (fileList.length > 1) {
		fileList.splice(0, fileList.length - 1)
	}

	// 清空之前的导入结果
	importResult.value = null

	// 验证文件类型
	if (fileItem.file) {
		const fileName = fileItem.file.name
		const fileExtension = fileName.substring(fileName.lastIndexOf('.'))
		if (!['.xlsx', '.xls'].includes(fileExtension.toLowerCase())) {
			Message.error('请上传 Excel 文件（.xlsx 或 .xls 格式）')
			return false
		}
	}
}

const handleFileRemove = () => {
	importResult.value = null
}

// 暴露方法给父组件
defineExpose({
	open
})
</script>

<style scoped>
.import-content {
	max-height: 600px;
	overflow-y: auto;
}

.upload-section {
	margin: 24px 0;
}

.upload-section h4 {
	margin-bottom: 12px;
	color: var(--color-text-1);
	font-weight: 500;
}

.upload-area {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
	text-align: center;
}

.upload-icon {
	font-size: 48px;
	color: var(--color-text-3);
	margin-bottom: 16px;
}

.upload-text {
	color: var(--color-text-2);
}

.upload-tip {
	font-size: 12px;
	color: var(--color-text-3);
	margin-top: 4px;
}

.import-result {
	margin-top: 24px;
}

.import-result ul {
	margin: 8px 0 0 16px;
	padding: 0;
}

.import-result li {
	margin-bottom: 4px;
	color: var(--color-text-2);
	font-size: 12px;
}

:deep(.arco-upload-drag) {
	border: 2px dashed var(--color-border-2);
	border-radius: 6px;
	background-color: var(--color-fill-1);
	transition: all 0.3s;
}

:deep(.arco-upload-drag:hover) {
	border-color: var(--color-primary-light-4);
	background-color: var(--color-primary-light-1);
}

:deep(.arco-upload-drag.arco-upload-drag-over) {
	border-color: var(--color-primary-6);
	background-color: var(--color-primary-light-1);
}
:deep(.arco-upload-progress) {
	display: none !important;
}
</style>