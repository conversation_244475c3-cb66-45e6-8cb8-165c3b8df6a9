import http from '../index';

// 操作日志接口返回值类型定义
export interface OperaLogInfo {
  createBy: string | null;      // 创建者
  createByName: string | null;  // 创建者名称
  createTime: string | null;    // 创建时间
  updateBy: string | null;      // 更新者
  updateByName: string | null;  // 更新者名称
  updateTime: string | null;    // 更新时间
  businessType: number;         // 业务类型
  businessTypes: null;          // 业务类型（数组）
  costTime: number;            // 消耗时间
  deptName: string | null;     // 部门名称
  errorMsg: string | null;     // 错误消息
  jsonResult: string | null;    // 返回参数
  method: string;              // 操作方法
  operIp: string;              // 操作IP
  operName: string;            // 操作人员
  operParam: string;           // 请求参数
  operTime: string;            // 操作时间
  operUrl: string;             // 请求地址
  operatorType: number;        // 操作者类型
  requestMethod: string;       // 请求方式
  status: number;              // 操作状态（0正常 1异常）
  title: string;               // 操作模块
}

export interface ListTypeResponse {
  total: number;           // 总记录数
  rows: OperaLogInfo[];        // 操作日志列表数据
  code: number;           // 响应状态码
  msg: string;            // 响应消息
}

export interface Response {
  code: number;           // 响应状态码
  msg: string;            // 响应消息
}

// 查询操作日志列表
export const listOperlog = (query?: any) => {
  return http.get('/business-platform/operlog/list', query);
}

// 删除操作日志
export const delOperlog = (operId: string | number) => {
  return http.delete(`/business-platform/operlog/${operId}`);
}

// 清空操作日志
export const cleanOperlog = () => {
  return http.delete('/business-platform/operlog/clean');
}

// 导出操作日志
export const exportOperaLog = (data: any) => {
  return http.download('/business-platform/operlog/export', data);
}