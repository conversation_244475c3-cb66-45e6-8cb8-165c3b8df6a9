<template>
    <div class="common-page page-layout-column">
        <!-- 左侧组织树 -->
        <div class="page-layout-column-left">
            <project-org-tree @change="handleOrgChange" />
        </div>

        <!-- 右侧内容区 -->
        <div v-permission="['rent:project:statistics']" class="page-layout-column-right">
            <!-- 顶部统计卡片 -->
            <div class="statistics-cards">
                <div class="area-card">
                    <img class="area-icon" src="@/assets/images/property/building.png" alt="">
                    <span class="area-name">{{ currentOrgName || '全部' }}</span>
                </div>
                <div class="stat-card-list">
                    <div class="stat-card stat-card-1">
                        <div class="label">项目数</div>
                        <div class="value">{{ statistics.projectCount }}<span class="unit">个</span></div>
                    </div>
                    <div class="stat-card stat-card-2">
                        <div class="label">资产面积</div>
                        <div class="value">{{ statistics.receivedArea }}<span class="unit">万㎡</span></div>
                    </div>
                    <div class="stat-card stat-card-3">
                        <div class="label">房源总面积</div>
                        <div class="value">{{ statistics.totalRoomArea }}<span class="unit">万㎡</span></div>
                    </div>
                    <div class="stat-card stat-card-4">
                        <div class="label">房源数</div>
                        <div class="value">{{ statistics.roomCount }}<span class="unit">个</span></div>
                    </div>
                    <div class="stat-card stat-card-5">
                        <div class="label">待交付房源</div>
                        <div class="value">{{ statistics.pendingDeliveryCount }}<span class="unit">个</span></div>
                    </div>
                </div>
            </div>
            <a-divider />
            <!-- 搜索区域 -->
            <div class="search-wrapper">
                <div class="search-area">
                    <div class="search-item">
                        <span class="label">项目名称</span>
                        <a-input v-model="queryParams.projectName" placeholder="请输入" allow-clear />
                        <a-button v-permission="['rent:project:list']" class="search-btn" type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                    </div>
                    <div class="association">
                        <a-button v-permission="['rent:project:bind']" class="association-btn" type="primary" @click="handleBatchLink">
                            批量关联片区
                        </a-button>
                    </div>
                </div>
            </div>

            <!-- 表格区域 -->
            <div class="table-wrapper">
                <a-table row-key="id" :loading="loading" :pagination="pagination" :columns="columns" :data="tableData"
                    :bordered="{ cell: true }" v-model:selectedKeys="selectedKeys"
                    :row-selection="{ type: 'checkbox', showCheckedAll: true }" @page-change="onPageChange"
                    :scroll="{x: 1}" @page-size-change="onPageSizeChange">
                    <template #index="{ rowIndex }">
                        {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
                    </template>
                    <template #operations="{ record }">
                        <a-space>
                            <a-button v-permission="['rent:project:edit']" type="text" size="mini" @click="handleEdit(record)">
                                完善信息
                            </a-button>
                            <a-button v-permission="['rent:project:houseType']" type="text" size="mini" @click="handleConfig(record)">
                                宿舍户型配置
                            </a-button>
                        </a-space>
                    </template>
                </a-table>
            </div>

            <!-- 批量关联片区弹框 -->
            <a-modal v-model:visible="batchLinkVisible" title="批量关联片区" width="400px"
                @cancel="handleBatchLinkCancel">
                <div class="batch-link-content">
                    <div class="form-item">
                        <span class="label">所属片区</span>
                        <a-select v-model="selectedAreaId" placeholder="请选择" style="width: 300px">
                            <a-option v-for="area in areaOptions" :key="area.id" :value="area.id">
                                {{ area.name }}
                            </a-option>
                        </a-select>
                    </div>
                </div>
                <template #footer>
                    <div class="modal-footer">
                        <a-button @click="handleBatchLinkCancel">取消</a-button>
                        <a-button type="primary" :loading="batchLinkLoading" @click="handleBatchLinkConfirm">确定</a-button>
                    </div>
                </template>
            </a-modal>

            <project-detail ref="projectDetailRef" :project-id="currentProjectId" @refresh="handleProjectRefresh" />
            <room-type-config-drawer v-if="roomTypeConfigVisible" ref="roomTypeConfigRef" :project-id="currentProjectId" @cancel="handleRoomTypeConfigCancel" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import projectOrgTree from '@/components/projectOrgTree/index.vue'
import { Message } from '@arco-design/web-vue'
import projectDetail from './components/projectDetail.vue'
import roomTypeConfigDrawer from './components/RoomTypeConfigDrawer.vue'
import { 
    getProjectList, 
    bindProjectArea,
    getProjectStatistics,
    type ProjectQueryParams,
    type SysProjectVo,
    type SysProjectBindAreaDTO,
    type SysProjectStatisticsVo
} from '@/api/project'
import { getOrgTree } from '@/api/org'

// 组织树节点接口定义
interface OrgTreeNode {
    id?: string
    code?: string
    fullName?: string
    name?: string
    orgType?: number
    level?: number
    parentId?: string
    children?: OrgTreeNode[]
}

// 当前选中的组织信息
const currentOrgId = ref<string>('')
const currentOrgName = ref<string>('')


// 查询参数
const queryParams = ref<ProjectQueryParams>({
    projectName: '',
    parentId: '',
    pageNum: 1,
    pageSize: 10
})

// 统计数据
const statistics = ref<SysProjectStatisticsVo>({
    projectCount: 0,
    receivedArea: 0,
    totalRoomArea: 0,
    roomCount: 0,
    pendingDeliveryCount: 0
})

// 表格数据
const tableData = ref<SysProjectVo[]>([])
const loading = ref(false)
const selectedKeys = ref<string[]>([])

const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
})

// 表格列配置
const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        slotName: 'index',
        width: 80,
        align: 'center'
    },
    {
        title: '项目名称',
        dataIndex: 'mdmName',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '项目简称',
        dataIndex: 'name',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '所属片区',
        dataIndex: 'parentName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '项目资产分类',
        dataIndex: 'mdmTypeName',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    {
        title: '项目地址',
        dataIndex: 'projectAddress',
        width: 220,
        ellipsis: true,
        tooltip: true,
        align: 'center'        
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 200,
        fixed: 'right',
        ellipsis: false,
        tooltip: false,
        align: 'center'
    }
]

// 片区选项（从组织树中获取level=3的节点）
const areaOptions = ref<Array<{ id: string, name: string }>>([])

// 处理分页变化
const onPageChange = (current: number) => {
    pagination.current = current
    queryParams.value.pageNum = current
    loadProjectList()
}

const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    queryParams.value.pageSize = pageSize
    queryParams.value.pageNum = 1
    loadProjectList()
}

// 处理组织树选择
const handleOrgChange = (node: any) => {
    console.log('Selected organization:', node)
    currentOrgId.value = node.id
    currentOrgName.value = node.name
    
    // 查询该集团/区域/片区下的项目
    queryParams.value.parentId = node.id
    queryParams.value.id = ''
    
    // 重置分页
    pagination.current = 1
    queryParams.value.pageNum = 1
    
    search()
}

// 加载项目列表
const loadProjectList = async () => {
    try {
        loading.value = true
        const response = await getProjectList(queryParams.value)
        
        if (response && response.rows) {
            tableData.value = response.rows || []
            pagination.total = response.total || 0
        }
    } catch (error) {
        console.error('加载项目列表失败:', error)
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 加载统计数据
const loadStatistics = async () => {
    try {
        const id = currentOrgId.value || 'all'
        const response = await getProjectStatistics(id)
        
        if (response && response.data) {
            statistics.value = response.data
        }
    } catch (error) {
        console.error('加载统计数据失败:', error)
        // 使用默认统计数据
        statistics.value = {
            projectCount: 0,
            receivedArea: 0,
            totalRoomArea: 0,
            roomCount: 0,
            pendingDeliveryCount: 0
        }
    }
}



// 搜索
const search = () => {
    pagination.current = 1
    queryParams.value.pageNum = 1
    loadProjectList()
    loadStatistics()
}

// 重置
const reset = () => {
    queryParams.value.projectName = ''
    search()
}

// 项目详情和配置相关
const projectDetailRef = ref()
const roomTypeConfigRef = ref()
const currentProjectId = ref<string>('')

const handleEdit = (record: SysProjectVo) => {
    currentProjectId.value = record.id || ''
    projectDetailRef.value?.show()
}
const roomTypeConfigVisible = ref(false)
const handleConfig = (record: SysProjectVo) => {
    currentProjectId.value = record.id || ''
    roomTypeConfigVisible.value = true
    nextTick(() => {
        roomTypeConfigRef.value?.show()
    })
}
const handleRoomTypeConfigCancel = () => {
    roomTypeConfigVisible.value = false
}
// 处理项目详情刷新事件
const handleProjectRefresh = () => {
    loadProjectList()
    loadStatistics()
}

// 批量关联片区相关
const batchLinkVisible = ref(false)
const selectedAreaId = ref('')
const batchLinkLoading = ref(false)

const handleBatchLink = () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请选择要关联的项目')
        return
    }
    batchLinkVisible.value = true
}

const handleBatchLinkCancel = () => {
    batchLinkVisible.value = false
    selectedAreaId.value = ''
}

const handleBatchLinkConfirm = async () => {
    if (!selectedAreaId.value) {
        Message.warning('请选择所属片区')
        return
    }
    
    try {
        batchLinkLoading.value = true
        const params: SysProjectBindAreaDTO = {
            projectIds: selectedKeys.value,
            areaId: selectedAreaId.value
        }
        
        await bindProjectArea(params)
        Message.success('关联成功')
        batchLinkVisible.value = false
        selectedAreaId.value = ''
        selectedKeys.value = []
        
        // 重新加载列表
        loadProjectList()
    } catch (error) {
        console.error('批量关联失败:', error)
    } finally {
        batchLinkLoading.value = false
    }
}

// 初始化片区选项（从组织树API获取level=3的节点）
const initAreaOptions = async () => {
    try {
        const response = await getOrgTree()
        if (response && response.data) {
            const areas: Array<{ id: string, name: string }> = []
            
            // 递归遍历组织树，找到level=3的节点（片区）
            const findAreas = (nodes: OrgTreeNode[]) => {
                nodes.forEach(node => {
                    if (node.level === 3 && node.id && node.name) {
                        areas.push({
                            id: node.id,
                            name: node.name
                        })
                    }
                    if (node.children && node.children.length > 0) {
                        findAreas(node.children)
                    }
                })
            }
            
            findAreas(response.data)
            areaOptions.value = areas
        }
    } catch (error) {
        console.error('获取片区选项失败:', error)
    }
}

onMounted(() => {
    loadProjectList()
    loadStatistics()
    initAreaOptions()
})
</script>

<style scoped lang="less">
.page-layout-column {
    width: 100%;
    display: flex;
    align-items: flex-start;
    padding: 0 16px;

    .page-layout-column-left {
        width: 200px;
        min-width: 200px;
        height: 100%;
        background: #ffffff;
        border-radius: 4px 4px 0px 0px;
        box-shadow: -1px 0px 0px 0px #e8e8e8 inset;
    }

    .page-layout-column-right {
        flex: 1;
        height: 100%;
        background: #ffffff;
        border-radius: 4px;
        padding: 16px 0;
        width: calc(100% - 200px);
        display: flex;
        flex-direction: column;
    }
}

.statistics-cards {
    display: flex;
    align-items: center;
    background-color: #fff;
    height: 100px;
    justify-content: space-between;

    .area-card {
        display: flex;
        align-items: center;
        background: linear-gradient(180deg, #6974a0, #617597 100%);
        border-radius: 0px 25px 25px 0;
        min-width: 161px;
        min-height: 50px;
        margin-right: 43px;
        padding: 0 12px;
        position: relative;
        z-index: 1;

        .area-icon {
            width: 25px;
            height: 25px;
            margin-right: 9px;
        }

        .area-name {
            font-size: 16px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: Medium;
            text-align: left;
            color: #ffffff;
        }
    }

    .area-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: -8px;
        width: 168px;
        height: 50px;
        opacity: 0.2;
        background: linear-gradient(180deg, #6974a0, #617597 100%);
        border-radius: 0px 25px 25px 0;
        z-index: -1;
    }

    .area-card::after {
        content: '';
        position: absolute;
        top: 0;
        right: -16px;
        width: 176px;
        height: 50px;
        opacity: 0.2;
        background: linear-gradient(180deg, #6974a0, #617597 100%);
        border-radius: 0px 25px 25px 0;
        z-index: -2;
    }

    .stat-card-list {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        flex: 1;

        .stat-card {
            flex: 1;
            height: 60px;
            background: linear-gradient(180deg, #34c7ff, #7ad4ff 96%);
            border-radius: 4px;
            margin-right: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            &.stat-card-1 {
                background: linear-gradient(180deg, #34c7ff, #7ad4ff 96%);
            }

            &.stat-card-2 {
                background: linear-gradient(180deg, #47d8b1, #66dac4 97%);
            }

            &.stat-card-3 {
                background: linear-gradient(180deg, #349bff, #7abdff 95%);
            }

            &.stat-card-4 {
                background: linear-gradient(180deg, #6d89ff, #90a2ff 97%);
            }

            &.stat-card-5 {
                background: linear-gradient(180deg, #ff9a61, #ef9a70 97%);
            }

            .label {
                font-size: 14px;
                color: #ffffff;
            }

            .value {
                font-size: 22px;
                font-weight: 600;
                color: #ffffff;

                .unit {
                    font-size: 14px;
                    font-weight: normal;
                    margin-left: 4px;
                }
            }
        }
    }

}

.search-wrapper {
    margin: 0 0 16px 0;
    padding: 0 16px;

    .search-area {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #ffffff;

        .search-item {
            display: flex;
            align-items: center;

            .label {
                color: rgb(var(--gray-8));
                margin-right: 9px;
            }

            :deep(.arco-input-wrapper) {
                width: 240px;
                margin-right: 40px;
            }

            .search-btn {
                width: 76px;
                height: 32px;
                border-radius: 4px;
            }
        }

        .association-btn {
            width: 109px;
            height: 32px;
            border-radius: 4px;
        }
    }
}

.table-wrapper {
    width: 100%;
    background: #ffffff;
    padding: 0 16px 16px 16px;
    flex: 1;
    overflow: auto;
}

:deep(.arco-table) {
    .arco-table-th {
        background-color: var(--color-fill-2);
        border-bottom: 1px solid var(--color-border);

        .arco-table-th-item-title {
            font-weight: 600;
        }
    }

    .arco-table-td {
        border-bottom: 1px solid var(--color-border);
    }

    .arco-table-tr:hover {
        .arco-table-td {
            background-color: var(--color-fill-2);
        }
    }
}

:deep(.arco-pagination) {
    margin-top: 16px;
    justify-content: flex-end;
}

.batch-link-content {
    padding: 16px 0;

    .form-item {
        display: flex;
        align-items: center;

        .label {
            width: 70px;
            text-align: right;
            margin-right: 8px;
            color: var(--color-text-2);
        }
    }
}

:deep(.arco-modal) {
    .arco-modal-header {
        border-bottom: none;
        padding: 16px;
    }

    .arco-modal-footer {
        border-top: none;
        padding: 16px;
    }
}

.modal-footer {
    display: flex;
    justify-content: center;
    gap: 8px;

    .arco-btn {
        padding: 0 16px;
    }
}
</style>