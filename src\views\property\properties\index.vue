<template>
    <div class="container">
        <div class="property-management">
            <!-- 左侧项目选择 -->
            <div class="left-panel" ref="leftPanelRef" :style="{ height: leftPanelHeight }">
                <a-card class="project-selector">
                    <property-project-selector @update:selection="handleProjectSelectionChange" />
                </a-card>
            </div>

            <!-- 右侧内容区域 -->
            <div class="right-panel" ref="rightPanelRef">
                <a-card class="general-card">
                    <!-- Tab切换 -->
                    <a-tabs v-model:active-key="activeTab" @change="handleTabChange">
                        <a-tab-pane key="active" title="生效中" />
                        <a-tab-pane key="draft" title="草稿" />
                        <a-tab-pane key="pending" title="待生效" />
                        <a-tab-pane key="all" title="全部" />
                        <a-tab-pane key="records" title="拆分合并记录" />
                    </a-tabs>

                    <!-- 统一筛选栏 -->
                    <div class="filter-bar">
                        <a-row>
                            <a-col :flex="1">
                                <a-form label-align="right" :model="activeTab === 'records' ? recordsFilterForm : filterForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>
                                    <a-row :gutter="16">
                                        <!-- 房源名称 - 所有tab都有 -->
                                        <a-col :span="8">
                                            <a-form-item field="roomName" label="房源名称">
                                                <a-input v-if="activeTab === 'records'" v-model="recordsFilterForm.roomName" placeholder="请输入房源名称" allow-clear />
                                                <a-input v-else v-model="filterForm.roomName" placeholder="请输入房源名称" allow-clear />
                                            </a-form-item>
                                        </a-col>
                                        
                                        <!-- 物业类型 - 生效中、草稿、待生效、全部 -->
                                        <a-col :span="8" v-if="['active', 'draft', 'pending', 'all'].includes(activeTab)">
                                            <a-form-item field="propertyType" label="物业类型">
                                                <a-tree-select
                                                    v-model="filterForm.propertyType"
                                                    :data="propertyTypeOptions"
                                                    placeholder="请选择物业类型"
                                                    allow-clear
                                                    :field-names="{
                                                        key: 'dictValue',
                                                        title: 'dictLabel',
                                                        children: 'childList'
                                                    }"
                                                />
                                            </a-form-item>
                                        </a-col>
                                        
                                        <!-- 交付状态 - 生效中、草稿、待生效、全部 -->
                                        <a-col :span="8" v-if="['active', 'draft', 'pending', 'all'].includes(activeTab)">
                                            <a-form-item field="paymentStatus" label="交付状态">
                                                <a-select v-model="filterForm.paymentStatus" placeholder="请选择交付状态" allow-clear>
                                                    <a-option :value="1">未交付</a-option>
                                                    <a-option :value="2">已交付</a-option>
                                                </a-select>
                                            </a-form-item>
                                        </a-col>
                                        
                                        <!-- 是否定价 - 生效中、待生效 -->
                                        <a-col :span="8" v-if="['active', 'pending'].includes(activeTab)">
                                            <a-form-item field="priceFlag" label="是否定价">
                                                <a-select v-model="filterForm.priceFlag" placeholder="请选择是否定价" allow-clear>
                                                    <a-option :value="1">是</a-option>
                                                    <a-option :value="0">否</a-option>
                                                </a-select>
                                            </a-form-item>
                                        </a-col>
                                        
                                        <!-- 房源状态 - 全部 -->
                                        <a-col :span="8" v-if="activeTab === 'all'">
                                            <a-form-item field="status" label="房源状态">
                                                <a-select v-model="filterForm.status" placeholder="请选择房源状态" allow-clear>
                                                    <a-option :value="RoomStatus.DRAFT">草稿</a-option>
                                                    <a-option :value="RoomStatus.PENDING">待生效</a-option>
                                                    <a-option :value="RoomStatus.ACTIVE">生效中</a-option>
                                                    <a-option :value="RoomStatus.EXPIRED">已失效</a-option>
                                                </a-select>
                                            </a-form-item>
                                        </a-col>
                                        
                                        <!-- 调整类型 - 拆分合并记录 -->
                                        <a-col :span="8" v-if="activeTab === 'records'">
                                            <a-form-item field="adjustmentType" label="调整类型">
                                                <a-select v-model="recordsFilterForm.adjustmentType" placeholder="请选择调整类型" allow-clear>
                                                    <a-option :value="AdjustmentType.SPLIT">拆分</a-option>
                                                    <a-option :value="AdjustmentType.MERGE">合并</a-option>
                                                </a-select>
                                            </a-form-item>
                                        </a-col>
                                        
                                        <!-- 审批状态 - 拆分合并记录 -->
                                        <a-col :span="8" v-if="activeTab === 'records'">
                                            <a-form-item field="approvalStatus" label="审批状态">
                                                <a-select v-model="recordsFilterForm.approvalStatus" placeholder="请选择审批状态" allow-clear>
                                                    <a-option :value="ApprovalStatus.PENDING_SUBMIT">待提交</a-option>
                                                    <a-option :value="ApprovalStatus.APPROVING">审批中</a-option>
                                                    <a-option :value="ApprovalStatus.APPROVED">已通过</a-option>
                                                    <a-option :value="ApprovalStatus.REJECTED">已驳回</a-option>
                                                </a-select>
                                            </a-form-item>
                                        </a-col>
                                        
                                        <!-- 创建日期 - 拆分合并记录 -->
                                        <a-col :span="8" class="w-full" v-if="activeTab === 'records'">
                                            <a-form-item field="createTime" label="创建日期">
                                                <a-range-picker v-model="recordsFilterForm.createTime" @change="handleDateChange" />
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                </a-form>
                            </a-col>
                            <a-divider style="height: 84px" v-if="activeTab !== 'draft'" direction="vertical" />
                            <a-col :flex="'86px'" :class="activeTab === 'draft' ? 'draft-filter-bar' : ''" style="text-align: right">
                                <a-space :direction="activeTab === 'draft' ? 'horizontal' : 'vertical'" :size="18">
                                    <a-button v-permission="['rent:room:list']" type="primary" @click="handleSearch">
                                        <template #icon><icon-search /></template>
                                        查询
                                    </a-button>
                                    <a-button @click="handleReset">
                                        <template #icon><icon-refresh /></template>
                                        重置
                                    </a-button>
                                </a-space>
                            </a-col>
                        </a-row>
                    </div>
                    <a-divider style="margin: 0px 0px 16px;" />
                    
                    <!-- 统一操作按钮栏 -->
                    <div class="action-bar" v-if="activeTab !== 'records'">
                        <a-space>
                            <!-- 生效中tab的按钮 -->
                            <template v-if="activeTab === 'active'">
                                <a-button type="primary" @click="handleAssetStructure">
                                    <template #icon><icon-apps /></template>
                                    资产构成
                                </a-button>
                                <a-dropdown>
                                    <a-button type="primary">
                                        <template #icon><icon-plus /></template>
                                        新增房源
                                    </a-button>
                                    <template #content>
                                        <a-doption v-permission="['rent:room:add']" @click="handleAddManually">手动新增</a-doption>
                                        <a-doption v-permission="['rent:room:introduce']" @click="handleImportAsset">引入已接收资产</a-doption>
                                    </template>
                                </a-dropdown>
                                <a-button v-permission="['rent:room:template:download']" @click="handleDownloadTemplate">
                                    <template #icon><icon-download /></template>
                                    下载模板
                                </a-button>
                                <a-button v-permission="['rent:room:template:import']" @click="handleImport">
                                    <template #icon><icon-upload /></template>
                                    导入房源
                                </a-button>
                                <a-button v-permission="['rent:room:batchUpdate']" @click="handleBatchUpdate">
                                    <template #icon><icon-edit /></template>
                                    批量更新房源
                                </a-button>
                                <a-button v-permission="['rent:room:merge:add']" @click="handleMerge">
                                    <template #icon><icon-link /></template>
                                    合并房源
                                </a-button>
                                <a-button @click="handlePricing">
                                    <template #icon><icon-tag /></template>
                                    发起立项定价
                                </a-button>
                            </template>
                            
                            <!-- 草稿tab的按钮 -->
                            <template v-if="activeTab === 'draft'">
                                <a-dropdown>
                                    <a-button type="primary">
                                        <template #icon><icon-plus /></template>
                                        新增房源
                                    </a-button>
                                    <template #content>
                                        <a-doption v-permission="['rent:room:add']" @click="handleAddManually">手动新增</a-doption>
                                        <a-doption v-permission="['rent:room:introduce']" @click="handleImportAsset">引入已接收资产</a-doption>
                                    </template>
                                </a-dropdown>
                                <a-button v-permission="['rent:room:template:download']" @click="handleDownloadTemplate">
                                    <template #icon><icon-download /></template>
                                    下载模板
                                </a-button>
                                <a-button v-permission="['rent:room:template:import']" @click="handleImport">
                                    <template #icon><icon-upload /></template>
                                    导入房源
                                </a-button>
                                <a-button v-permission="['rent:room:batchUpdate']" @click="handleBatchUpdate">
                                    <template #icon><icon-edit /></template>
                                    批量更新房源
                                </a-button>
                                <a-button v-permission="['rent:room:effect']" @click="() => handleActivate()">
                                    <template #icon><icon-check /></template>
                                    生效房源
                                </a-button>
                            </template>
                            
                            <!-- 待生效tab的按钮 -->
                            <template v-if="activeTab === 'pending'">
                                <a-button v-permission="['rent:room:quickEffect']" type="primary" @click="() => handleActivateNow()">
                                    <template #icon><icon-check /></template>
                                    立即生效房源
                                </a-button>
                                <a-button v-permission="['rent:room:effectCancel']" @click="() => handleVoid()">
                                    <template #icon><icon-close /></template>
                                    作废房源
                                </a-button>
                                <a-button v-permission="['rent:room:project:add']" @click="handlePricing">
                                    <template #icon><icon-tag /></template>
                                    发起立项定价
                                </a-button>
                            </template>
                            
                            <!-- 全部tab的按钮 -->
                            <template v-if="activeTab === 'all'">
                                <a-button v-permission="['rent:room:export']" type="primary" @click="handleExport">
                                    <template #icon><icon-download /></template>
                                    导出房源
                                </a-button>
                            </template>
                        </a-space>
                    </div>
                    
                    <!-- 内容区域 -->
                    <div class="content-area">
                        <active-properties 
                            v-if="activeTab === 'active'"
                            ref="activePropertiesRef"
                            :selection="currentSelection" 
                            :filter-form="filterForm"
                            :loading="loading"
                            @update:loading="loading = $event"
                            @selection-change="handleSelectionChange"
                            @view-detail="handleViewDetail"
                            @edit-room="handleEditRoom"
                            @split-room="handleSplitRoom"
                            @merge-room="handleMergeRoom"
                            @invalidate-room="handleInvalidateRoom"
                            @update-room-data-cache="handleUpdateRoomDataCache"
                        />
                        
                        <draft-properties 
                            v-if="activeTab === 'draft'"
                            :selection="currentSelection" 
                            :filter-form="filterForm"
                            :loading="loading"
                            @update:loading="loading = $event"
                            @selection-change="handleSelectionChange"
                            @view-detail="handleViewDetail"
                            @edit-room="handleEditRoom"
                            @delete-room="handleDeleteRoom"
                            @effect-room="handleEffectRoom"
                        />
                        
                        <pending-properties 
                            v-if="activeTab === 'pending'"
                            :selection="currentSelection" 
                            :filter-form="filterForm"
                            :loading="loading"
                            @update:loading="loading = $event"
                            @selection-change="handleSelectionChange"
                            @view-detail="handleViewDetail"
                            @edit-room="handleEditRoom"
                            @immediate-effect-room="handleImmediateEffectRoom"
                            @pending-invalidate-room="handlePendingInvalidateRoom"
                        />
                        
                        <all-properties 
                            v-if="activeTab === 'all'"
                            :selection="currentSelection" 
                            :filter-form="filterForm"
                            :loading="loading"
                            @update:loading="loading = $event"
                            @selection-change="handleSelectionChange"
                            @view-detail="handleViewDetail"
                            @edit-room="handleEditRoom"
                        />
                        
                        <property-records 
                            v-if="activeTab === 'records'"
                            :selection="currentSelection" 
                            :filter-form="recordsFilterForm"
                            :loading="recordsLoading"
                            @update:loading="recordsLoading = $event"
                            @open-split-drawer="handleOpenSplitDrawer"
                            @open-merge-drawer="handleOpenMergeDrawer"
                        />
                    </div>
                </a-card>
            </div>
        </div>

        <!-- 弹框组件 -->
        <add-property-drawer 
            v-if="showAddPropertyDrawer" 
            ref="addPropertyDrawerRef" 
            @confirm="handleAddPropertyConfirm" 
            @cancel="handleAddPropertyCancel"
        />
        <batch-update-modal v-if="showBatchUpdateModal" ref="batchUpdateModalRef" @confirm="handleBatchUpdateConfirm" @cancel="handleBatchUpdateCancel"/>
        <select-building-modal v-if="showSelectBuildingModal" ref="selectBuildingModalRef" @next="handleSelectBuildingNext" @cancel="handleSelectBuildingCancel" />
        <select-rooms-modal v-if="showSelectRoomsModal" ref="selectRoomsModalRef" @prev="handleSelectRoomsPrev" @next="handleSelectRoomsNext" @cancel="handleSelectRoomsCancel" />
        <config-property-modal v-if="showConfigPropertyModal" ref="configPropertyModalRef" @confirm="handleConfigPropertySave" @cancel="handleConfigPropertyCancel" />
        <split-property-drawer v-if="showSplitPropertyDrawer" ref="splitPropertyDrawerRef" @confirm="handleSplitDrawerConfirm" @cancel="handleSplitDrawerCancel" />
        <merge-property-drawer v-if="showMergePropertyDrawer" ref="mergePropertyDrawerRef" @confirm="handleMergeConfirm" />
        <project-pricing-drawer v-if="showProjectPricingDrawer" ref="projectPricingDrawerRef" @success="handleProjectPricingSuccess" @cancel="handleProjectPricingCancel" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { 
    IconSearch, 
    IconRefresh, 
    IconDownload, 
    IconUpload, 
    IconEdit, 
    IconTag, 
    IconClose, 
    IconPlus, 
    IconBranch, 
    IconSwap,
    IconCheck,
    IconDelete,
    IconApps,
    IconLink
} from '@arco-design/web-vue/es/icon'
import PropertyProjectSelector from '@/components/propertyProjectSelector/index.vue'
import ActiveProperties from './components/ActiveProperties.vue'
import DraftProperties from './components/DraftProperties.vue'
import PendingProperties from './components/PendingProperties.vue'
import AllProperties from './components/AllProperties.vue'
import PropertyRecords from './components/PropertyRecords.vue'
import AddPropertyDrawer from './components/addPropertyDrawer.vue'
import BatchUpdateModal from './components/BatchUpdateModal.vue'
import MergePropertyDrawer from './components/MergePropertyDrawer.vue'
import SelectBuildingModal from './components/SelectBuildingModal.vue'
import SelectRoomsModal from './components/SelectRoomsModal.vue'
import ConfigPropertyModal from './components/ConfigPropertyModal.vue'
import SplitPropertyDrawer from './components/SplitPropertyDrawer.vue'
import ProjectPricingDrawer from '@/views/projectPricing/components/ProjectPricingDrawer.vue'
import { useDictSync } from '@/utils/dict'
import { 
    getRoomList,
    exportRoomList,
    downloadRoomTemplate,
    importRooms,
    effectRooms,
    deleteRooms,
    invalidateRooms,
    submitProjectPricingApplication,
    deleteRoomById,
    pendingCancelRooms,
    effectCancelRooms,
    quickEffectRooms,
    getSplitDetail,
    downloadRoomTemplateForLease,
    importRoomsFromTemplate,
    type RoomQueryDTO,
    type MergeSplitRecordQueryDTO,
    type RoomVo,
    RoomStatus,
    AdjustmentType,
    ApprovalStatus,
    RoomType
} from '@/api/room'
import { exportExcel, importExcelFile } from '@/utils/exportUtil'

// 当前选中的Tab
const activeTab = ref('active')


//控制弹框显示
const showAddPropertyDrawer = ref(false)
const showBatchUpdateModal = ref(false)
const showMergePropertyDrawer = ref(false)
const showSelectBuildingModal = ref(false)
const showSelectRoomsModal = ref(false)
const showConfigPropertyModal = ref(false)
const showSplitPropertyDrawer = ref(false)
const showProjectPricingDrawer = ref(false)

// 字典数据
interface DictData {
    dictCode: string;
    dictLabel: string;
    dictValue: string;
    dictSort: number;
    parentCode?: string;
    childList?: DictData[];
    [key: string]: any;
}

const propertyTypeOptions = ref<DictData[]>([])

// 当前选择
const currentSelection = ref({
    projectId: '',
    projectName: '',
    blockId: '',
    blockName: '',
    buildingId: '',
    buildingName: ''
})

// 房源筛选表单
const filterForm = reactive({
    projectId: '',
    parcelId: '',
    buildingId: '',
    roomName: '',
    propertyType: '',
    paymentStatus: undefined as number | undefined,
    priceFlag: undefined as number | undefined,
    status: undefined as number | undefined,
    propertyStatus: '',
    type: RoomType.NORMAL, // 默认为普通房源
    pageNum: 1,
    pageSize: 10
} as RoomQueryDTO & { propertyStatus?: string })

// 拆分合并记录筛选表单
const recordsFilterForm = reactive({
    projectId: '',
    parcelId: '',
    buildingId: '',
    roomName: '',
    adjustmentType: undefined as number | undefined,
    approvalStatus: undefined as number | undefined,
    createTime: [] as string[],
    createTimeStart: '',
    createTimeEnd: '',
    pageNum: 1,
    pageSize: 10
})

// 当前筛选表单 - 根据tab动态切换
const currentFilterForm = computed(() => {
    if (activeTab.value === 'records') {
        return recordsFilterForm
    }
    return filterForm
})

// 加载状态
const loading = ref(false)
const recordsLoading = ref(false)

// 选中的房源
const selectedKeys = ref<string[]>([])

// 组件引用
const addPropertyDrawerRef = ref()
const batchUpdateModalRef = ref()
const mergePropertyDrawerRef = ref()
const activePropertiesRef = ref()
const selectBuildingModalRef = ref()
const selectRoomsModalRef = ref()
const configPropertyModalRef = ref()
const splitPropertyDrawerRef = ref()
const projectPricingDrawerRef = ref()

// 高度动态计算相关
const leftPanelRef = ref()
const rightPanelRef = ref()
const leftPanelHeight = ref('auto')


// 防抖定时器
let searchTimer: number | null = null

// 添加一个引用来访问ActiveProperties组件的数据
let roomDataCache: RoomVo[] = []

// 根据ID查找房源记录
const findRoomById = (id: string): RoomVo | undefined => {
    // 从数据缓存中查找
    if (roomDataCache && roomDataCache.length > 0) {
        return roomDataCache.find(room => room.id === id)
    }
    return undefined
}

// 计算左侧面板高度
const calculateLeftPanelHeight = () => {
    nextTick(() => {
        if (rightPanelRef.value) {
            const rightPanelHeight = rightPanelRef.value.offsetHeight
            leftPanelHeight.value = `${rightPanelHeight}px`
        }
    })
}

// ResizeObserver 实例
let resizeObserver: ResizeObserver | null = null

// 监听右侧面板高度变化
const observeRightPanelHeight = () => {
    if (rightPanelRef.value && typeof ResizeObserver !== 'undefined') {
        resizeObserver = new ResizeObserver(() => {
            calculateLeftPanelHeight()
        })
        resizeObserver.observe(rightPanelRef.value)
    }
}

// 获取字典数据
const getPropertyTypeDicts = async () => {
    try {
        const dictData = await useDictSync('property_type')
        if (dictData.property_type) {
            // 处理树形结构数据
            const dictList = dictData.property_type as DictData[]
            // 组织成树形结构
            const treeData = dictList.filter(item => !item.parentCode)
            treeData.forEach(parent => {
                parent.childList = dictList.filter(child => child.parentCode === parent.dictCode)
            })
            propertyTypeOptions.value = treeData
        }
    } catch (error) {
        console.error('获取物业类型字典数据失败:', error)
    }
}

// 组件挂载时初始化数据
onMounted(() => {
    getPropertyTypeDicts()
    
    // 初始化高度监听
    nextTick(() => {
        observeRightPanelHeight()
        // 初始化时计算一次高度
        calculateLeftPanelHeight()
    })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
    // 清理 ResizeObserver
    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }
})

// 处理删除房源
const handleDeleteRoom = (id: string, name: string) => {
    if (!id) return
    
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除房源"${name || id}"吗？删除后无法恢复。`,
        onOk: async () => {
            try {
                // 使用正确的单个房源删除接口
                await deleteRoomById({ id })
                Message.success('删除成功')
                handleSearch()
            } catch (error) {
                console.error('删除房源失败:', error)
            }
        }
    })
}

// 处理生效房源
const handleEffectRoom = (id: string, name: string) => {
    if (!id) return
    
    Modal.confirm({
        title: '确认生效',
        content: `确定要生效房源"${name || id}"吗？`,
        onOk: async () => {
            try {
                await effectRooms([id])
                Message.success('生效成功')
                handleSearch()
            } catch (error) {
                console.error('生效房源失败:', error)
            }
        }
    })
}

// 处理作废房源
const handleInvalidateRoom = (id: string, name: string) => {
    if (!id) return
    
    Modal.confirm({
        title: '确认作废',
        content: `确定要作废房源"${name || id}"吗？`,
        onOk: async () => {
            try {
                // 生效中状态使用effect/cancel接口
                await effectCancelRooms([id])
                Message.success('作废成功')
                handleSearch()
            } catch (error) {
                console.error('作废房源失败:', error)
            }
        }
    })
}

// 处理待生效房源立即生效
const handleImmediateEffectRoom = (id: string, name: string) => {
    if (!id) return
    
    Modal.confirm({
        title: '确认立即生效',
        content: `确定要立即生效房源"${name || id}"吗？`,
        onOk: async () => {
            try {
                // 使用quick/effect接口处理待生效房源的立即生效
                await quickEffectRooms([id])
                Message.success('立即生效成功')
                handleSearch()
            } catch (error) {
                console.error('立即生效房源失败:', error)
            }
        }
    })
}

// 处理待生效房源作废
const handlePendingInvalidateRoom = (id: string, name: string) => {
    if (!id) return
    
    Modal.confirm({
        title: '确认作废',
        content: `确定要作废待生效房源"${name || id}"吗？`,
        onOk: async () => {
            try {
                // 待生效状态使用pending/cancel接口
                await pendingCancelRooms([id])
                Message.success('作废成功')
                handleSearch()
            } catch (error) {
                console.error('作废房源失败:', error)
            }
        }
    })
}

// 处理拆分房源
const handleSplitRoom = async (id: string) => {
    if (!id) return
    
    // 打开拆分抽屉，只传递房源ID，由抽屉组件内部根据ID获取详情
    showSplitPropertyDrawer.value = true
    nextTick(() => {
        // 只传递房源ID
        splitPropertyDrawerRef.value?.show({ id },false,currentSelection.value)
    })
}

// 处理合并房源（单个房源合并，从表格操作栏触发）
const handleMergeRoom = (room: RoomVo) => {
    if (!room || !room.id) return
    
    // 打开合并抽屉，传递完整的房源对象和当前选择的项目信息
    showMergePropertyDrawer.value = true
    nextTick(() => {
        mergePropertyDrawerRef.value?.show([room], false, currentSelection.value)
    })
}

// 处理合并房源 (表格上方的合并按钮，批量合并)
const handleMerge = () => {
    if (selectedKeys.value.length < 2) {
        Message.warning('请至少选择两个房源进行合并')
        return
    }
    
    // 获取所有选中房源的ID
    const roomIds = selectedKeys.value.filter(id => !!id)
    
    if (roomIds.length > 0) {
        // 从缓存中获取完整的房源对象数据
        const selectedRooms = roomIds.map(id => findRoomById(id)).filter(room => !!room) as RoomVo[]
        
        if (selectedRooms.length === 0) {
            Message.warning('找不到所选房源的详细信息')
            return
        }
        
        // 验证所选房源是否在同一地块、楼栋、楼层
        const firstRoom = selectedRooms[0]
        const isSameParcel = selectedRooms.every(room => room.parcelId === firstRoom.parcelId)
        const isSameBuilding = selectedRooms.every(room => room.buildingId === firstRoom.buildingId)
        const isSameFloor = selectedRooms.every(room => room.floorId === firstRoom.floorId)
        
        if (!isSameParcel) {
            Message.error('合并的房源必须在同一地块内')
            return
        }
        
        if (!isSameBuilding) {
            Message.error('合并的房源必须在同一楼栋内')
            return
        }
        
        if (!isSameFloor) {
            Message.error('合并的房源必须在同一楼层内')
            return
        }
        
        // 打开合并抽屉，传递完整的房源对象
        showMergePropertyDrawer.value = true
        nextTick(() => {
            mergePropertyDrawerRef.value?.show(selectedRooms, false, currentSelection.value)
        })
    } else {
        Message.warning('请选择有效的房源进行合并')
    }
}

// 更新房源数据缓存
const handleUpdateRoomDataCache = (data: RoomVo[]) => {
    if (data && Array.isArray(data)) {
        updateRoomDataCache(data)
    }
}

// 项目选择变化
const handleProjectSelectionChange = (selection: { 
    projectId: string; 
    projectName: string;
    blockId: string; 
    blockName: string;
    buildingId: string;
    buildingName: string;
}) => {
    currentSelection.value = selection
    filterForm.projectId = selection.projectId
    filterForm.parcelId = selection.blockId
    filterForm.buildingId = selection.buildingId
    recordsFilterForm.projectId = selection.projectId
    recordsFilterForm.parcelId = selection.blockId
    recordsFilterForm.buildingId = selection.buildingId
    // 使用防抖避免重复调用
    if (searchTimer) {
        clearTimeout(searchTimer)
    }
    searchTimer = setTimeout(() => {
        handleSearch()
    }, 300)
}

// 处理表格选择变化
const handleSelectionChange = (keys: string[]) => {
    selectedKeys.value = keys
}

// 处理Tab切换
const handleTabChange = (key: string) => {
    activeTab.value = key
    // 重置loading状态
    loading.value = false
    recordsLoading.value = false
    // 重置选中状态
    selectedKeys.value = []
    
    // 静默重置筛选条件（不触发监听器）
    if (key === 'records') {
        Object.keys(recordsFilterForm).forEach(fieldKey => {
            if (fieldKey !== 'projectId' && fieldKey !== 'parcelId' && fieldKey !== 'buildingId' && fieldKey !== 'pageNum' && fieldKey !== 'pageSize') {
                if (fieldKey === 'createTime') {
                    recordsFilterForm.createTime = []
                } else if (typeof recordsFilterForm[fieldKey as keyof typeof recordsFilterForm] === 'string') {
                    (recordsFilterForm as any)[fieldKey] = ''
                } else {
                    (recordsFilterForm as any)[fieldKey] = undefined
                }
            }
        })
    } else {
        Object.keys(filterForm).forEach(fieldKey => {
            if (fieldKey !== 'projectId' && fieldKey !== 'parcelId' && fieldKey !== 'buildingId' && fieldKey !== 'pageNum' && fieldKey !== 'pageSize') {
                if (typeof filterForm[fieldKey as keyof typeof filterForm] === 'string') {
                    (filterForm as any)[fieldKey] = ''
                } else {
                    (filterForm as any)[fieldKey] = undefined
                }
            }
        })
    }
    
    // 使用防抖避免快速切换时的重复调用
    if (searchTimer) {
        clearTimeout(searchTimer)
    }
    searchTimer = setTimeout(() => {
        handleSearch()
    }, 200)
}

// 搜索
const handleSearch = () => {
    console.log('handleSearch 被调用，当前tab:', activeTab.value)
    // 重置loading状态
    if (activeTab.value === 'records') {
        recordsLoading.value = false
        const eventName = 'refresh-property-records-data'
        console.log('发送事件:', eventName)
        const event = new CustomEvent(eventName)
        document.dispatchEvent(event)
    } else {
        loading.value = false
        // 使用tab-specific的事件名称，避免所有组件同时响应
        const eventName = `refresh-property-${activeTab.value}-data`
        console.log('发送事件:', eventName)
        const event = new CustomEvent(eventName)
        document.dispatchEvent(event)
    }
    
    // 数据加载完成后重新计算高度
    setTimeout(() => {
        calculateLeftPanelHeight()
    }, 500)
}

// 重置
const handleReset = () => {
    if (activeTab.value === 'records') {
        Object.keys(recordsFilterForm).forEach(key => {
            if (key !== 'projectId' && key !== 'pageNum' && key !== 'pageSize') {
                if (key === 'createTime') {
                    recordsFilterForm.createTime = []
                } else if (typeof recordsFilterForm[key as keyof typeof recordsFilterForm] === 'string') {
                    (recordsFilterForm as any)[key] = ''
                } else {
                    (recordsFilterForm as any)[key] = undefined
                }
            }
        })
    } else {
        Object.keys(filterForm).forEach(fieldKey => {
            if (fieldKey !== 'projectId' && fieldKey !== 'parcelId' && fieldKey !== 'buildingId' && fieldKey !== 'pageNum' && fieldKey !== 'pageSize') {
                if (typeof filterForm[fieldKey as keyof typeof filterForm] === 'string') {
                    (filterForm as any)[fieldKey] = ''
                } else {
                    (filterForm as any)[fieldKey] = undefined
                }
            }
        })
    }
    // 重置后不立即调用搜索，避免重复调用
    if (searchTimer) {
        clearTimeout(searchTimer)
    }
    searchTimer = setTimeout(() => {
        handleSearch()
    }, 200)
}

// 处理日期变化
const handleDateChange = (dateStrings: string[]) => {
    recordsFilterForm.createTimeStart = dateStrings[0] || ''
    recordsFilterForm.createTimeEnd = dateStrings[1] || ''
}

// 导出
const handleExport = async () => {
    try {
        const queryParams: RoomQueryDTO = {
            ...filterForm,
            status: getStatusByTab(),
            type: RoomType.NORMAL // 默认为普通房源
        }
        await exportExcel(exportRoomList, queryParams, '房源列表')
        Message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
    }
}

// 下载模板
const handleDownloadTemplate = async () => {
    if (!filterForm.projectId) {
		Message.warning('请先保存基本信息');
		return;
	}
	try {
		// 使用exportExcel方法下载模板
		exportExcel(downloadRoomTemplateForLease, filterForm.projectId, '房源导入模板')
			.then(() => {
				Message.success('模板下载成功');
			})
	} catch (error) {
		console.error('下载模板失败:', error);
	}
}

// 导入
const handleImport = () => {
    if (!filterForm.projectId) {
		Message.warning('请先保存基本信息');
		return;
	}

	// 创建文件选择器
	const input = document.createElement('input');
	input.type = 'file';
	input.accept = '.xlsx,.xls';
	input.style.display = 'none';

	// 监听文件选择事件
	input.onchange = (event) => {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			const file = target.files[0];
			
			// 检查文件类型
			if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
				Message.error('请上传Excel文件(.xlsx或.xls)');
				return;
			}

			// 显示加载状态
			const loadingInstance = Message.loading({
				content: '正在导入房源数据...',
				duration: 0
			});
			
			// 使用importExcelFile方法导入文件
			importExcelFile(file, importRoomsFromTemplate)
				.then((res: any) => {
					if (res && res.code === 200) {
						Message.success('房源导入成功');
						// 刷新房源列表
                        handleSearch()
					}
				})
				.catch((error: any) => {
					console.error('房源导入失败:', error);
				})
				.finally(() => {
					// 关闭加载状态
					loadingInstance.close();
					// 移除input元素
					document.body.removeChild(input);
				});
		}
	};
	
	// 添加到DOM并触发点击
	document.body.appendChild(input);
	input.click();
}

// 批量修改
const handleBatchUpdate = () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请先选择要修改的房源')
        return
    }
    showBatchUpdateModal.value = true
    nextTick(() => {    
        batchUpdateModalRef.value?.show(selectedKeys.value, filterForm.projectId)
    })
}

// 发起立项定价
const handlePricing = async () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请先选择要定价的房源')
        return
    }

    // 从缓存中获取选中房间的完整数据
    const selectedRooms = selectedKeys.value.map(id => findRoomById(id)).filter(room => !!room) as RoomVo[]

    if (selectedRooms.length === 0) {
        Message.warning('未找到选中房源的详细信息')
        return
    }

    // 将RoomVo数据转换为ProjectPricingDrawer期望的格式
    const mappedRooms = selectedRooms.map(room => ({
        ...room,
        id: room.id,
        name: room.roomName,
        roomName: room.roomName,
        buildingId: room.buildingId,
        buildingName: room.buildingName,
        floorId: room.floorId,
        floorName: room.floorName,
        plotId: room.parcelId || '',
        plotName: room.parcelName || '',
        propertyType: room.propertyType || '', // 物业类型字典值
        propertyTypeName: (room as any).propertyTypeName || room.propertyType || '', // 物业类型中文名称
        roomUsage: room.propertyType || '', // 用途字段，应该是字典值
        layoutName: (room as any).houseTypeName || room.houseType || '',
        roomType: (room as any).houseTypeName || room.houseType || '', // 户型字段
        rentArea: room.rentArea || 0
    }))

    // 构造传递给ProjectPricingDrawer的数据
    const projectPricingData = {
        selectedRooms: mappedRooms,
        selectedRoomIds: selectedKeys.value,
        projectId: filterForm.projectId,
        projectName: currentSelection.value.projectName,
        buildingIds: [...new Set(selectedRooms.map(room => room.buildingId).filter(id => !!id))]
    }

    // 打开立项定价抽屉
    showProjectPricingDrawer.value = true
    nextTick(() => {
        projectPricingDrawerRef.value?.show('add', projectPricingData)
    })
}

// 作废房源
const handleVoid = async () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请先选择要作废的房源')
        return
    }
    
    Modal.confirm({
        title: '确认作废',
        content: `确定要作废选中的 ${selectedKeys.value.length} 个房源吗？`,
        onOk: async () => {
            try {
                // 根据当前tab使用对应的作废接口
                if (activeTab.value === 'pending') {
                    // 待生效状态使用pending/cancel接口
                    await pendingCancelRooms(selectedKeys.value)
                } else if (activeTab.value === 'active') {
                    // 生效中状态使用effect/cancel接口
                    await effectCancelRooms(selectedKeys.value)
                } else {
                    Message.error('当前状态不支持作废操作')
                    return
                }
                Message.success('作废成功')
                // 清空选中状态，避免影响刷新后的列表
                selectedKeys.value = []
                handleSearch()
            } catch (error) {
                console.error('作废房源失败:', error)
            }
        }
    })
}

// 立即生效房源
const handleActivateNow = async () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请先选择要立即生效的房源')
        return
    }
    
    Modal.confirm({
        title: '确认立即生效',
        content: `确定要立即生效选中的 ${selectedKeys.value.length} 个房源吗？`,
        onOk: async () => {
            try {
                // 使用quick/effect接口处理待生效房源的立即生效
                await quickEffectRooms(selectedKeys.value)
                Message.success('立即生效成功')
                // 清空选中状态，避免影响刷新后的列表
                selectedKeys.value = []
                handleSearch()
            } catch (error) {
                console.error('立即生效房源失败:', error)
            }
        }
    })
}

// 生效房源
const handleActivate = async () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请先选择要生效的房源')
        return
    }
    
    Modal.confirm({
        title: '确认生效',
        content: `确定要生效选中的 ${selectedKeys.value.length} 个房源吗？`,
        onOk: async () => {
            try {
                // 使用effect接口将草稿状态房源转为生效中状态
                await effectRooms(selectedKeys.value)
                Message.success('生效成功')
                // 清空选中状态，避免影响刷新后的列表
                selectedKeys.value = []
                handleSearch()
            } catch (error) {
                console.error('生效房源失败:', error)
            }
        }
    })
}

// 删除房源
const handleDelete = async () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请先选择要删除的房源')
        return
    }
    
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${selectedKeys.value.length} 个房源吗？删除后无法恢复。`,
        onOk: async () => {
            try {
                await deleteRooms({ ids: selectedKeys.value })
                Message.success('删除成功')
                // 清空选中状态，避免影响刷新后的列表
                selectedKeys.value = []
                handleSearch()
            } catch (error) {
                console.error('删除房源失败:', error)
            }
        }
    })
}

// 资产构成
const handleAssetStructure = () => {
    if (activeTab.value === 'active') {
        activePropertiesRef.value?.showAssetAnalysis()
    } else {
        Message.warning('资产构成分析仅在生效中tab可用')
    }
}

// 查看房源详情
const handleViewDetail = (id: string) => {
    console.log('查看房源详情:', id)
    if (!id) {
        Message.error('房源ID不存在')
        return
    }
    showAddPropertyDrawer.value = true
    nextTick(() => {
        addPropertyDrawerRef.value?.show('view', id, currentSelection.value)
    })
}

// 编辑房源
const handleEditRoom = (id: string) => {
    console.log('编辑房源:', id)
    if (!id) {
        Message.error('房源ID不存在')
        return
    }
    showAddPropertyDrawer.value = true
    nextTick(() => {
        addPropertyDrawerRef.value?.show('edit', id, currentSelection.value)
    })
}

// 手动新增
const handleAddManually = () => {
    console.log('新增房源, currentSelection:', currentSelection.value)
    showAddPropertyDrawer.value = true
    nextTick(() => {
        addPropertyDrawerRef.value?.show('add', undefined, currentSelection.value)
    })
}

// 引入资产
const handleImportAsset = () => {
    // 引入资产的流程：选择楼栋 -> 选择房源 -> 配置属性
    showSelectBuildingModal.value = true
    nextTick(() => {
        selectBuildingModalRef.value?.show({ projectId: currentSelection.value.projectId })
    })
}

// 选择楼栋相关处理函数
const handleSelectBuildingNext = (buildingData: any) => {
    showSelectRoomsModal.value = true
    nextTick(() => {
        selectRoomsModalRef.value?.show(buildingData)
    })
}

const handleSelectBuildingCancel = () => {
    // 取消选择楼栋
}

// 选择房源相关处理函数
const handleSelectRoomsPrev = (prevData: any) => {
    console.log('从房源选择返回到楼栋选择，传递数据:', prevData)
    
    // 确保数据存在且格式正确
    if (prevData && typeof prevData === 'object') {
        // 传递上一步的数据，包括已选择的楼栋等信息
        showSelectBuildingModal.value = true
        nextTick(() => {
            selectBuildingModalRef.value?.show(prevData)
        })
    } else {
        console.warn('未从房源选择页面获取到有效数据，使用空对象初始化楼栋选择页面')
        showSelectBuildingModal.value = true
        nextTick(() => {
            selectBuildingModalRef.value?.show({ projectId: currentSelection.value.projectId })
        })
    }
}

const handleSelectRoomsNext = (roomsData: any) => {
    showConfigPropertyModal.value = true
    nextTick(() => {
        configPropertyModalRef.value?.show(roomsData)
    })
}

const handleSelectRoomsCancel = () => {
    // 取消选择房源
}

// 配置属性相关处理函数
const handleConfigPropertySave = (configData: any) => {
    Message.success('引入资产成功')
    handleSearch()
}

const handleConfigPropertyCancel = () => {
    // 取消配置属性
}

// 处理拆分组件的取消事件
const handleSplitDrawerCancel = () => {
    showSplitPropertyDrawer.value = false
}

// 处理拆分组件的确认事件
const handleSplitDrawerConfirm = () => {
    showSplitPropertyDrawer.value = false
    // 拆分成功后刷新列表
    handleSearch()
}

// 处理合并组件的确认事件
const handleMergeConfirm = () => {
    showMergePropertyDrawer.value = false
    // 合并成功后刷新列表
    handleSearch()
}

// 批量更新确认处理函数
const handleBatchUpdateConfirm = () => {
    showBatchUpdateModal.value = false
    handleSearch()
}

// 批量更新取消处理函数
const handleBatchUpdateCancel = () => {
    showBatchUpdateModal.value = false
}

// 根据tab获取状态
const getStatusByTab = () => {
    switch (activeTab.value) {
        case 'active':
            return RoomStatus.ACTIVE
        case 'draft':
            return RoomStatus.DRAFT
        case 'pending':
            return RoomStatus.PENDING
        default:
            return undefined
    }
}

// 更新房源数据缓存
const updateRoomDataCache = (data: RoomVo[]) => {
    roomDataCache = [...data]
}

// 处理查看房源详情
const handleViewPropertyDetail = (id: string, mode: string = 'view') => {
    showAddPropertyDrawer.value = true
    nextTick(() => {
        addPropertyDrawerRef.value?.show( mode, id, currentSelection.value)
    })
}

// 处理编辑房源
const handleEditProperty = (id: string, mode: string = 'edit') => {
    showAddPropertyDrawer.value = true
    nextTick(() => {
        addPropertyDrawerRef.value?.show( mode, id, currentSelection.value)
    })
}

// 处理房源新增/编辑确认事件
const handleAddPropertyConfirm = (data: any) => {
    showAddPropertyDrawer.value = false
    // 刷新列表
    handleSearch()
}

// 处理房源新增/编辑取消事件
const handleAddPropertyCancel = () => {
    showAddPropertyDrawer.value = false
}

// 处理拆分组件的取消事件
const handleOpenSplitDrawer = (data: { splitId: string, readonly: boolean }) => {
    showSplitPropertyDrawer.value = true
    // 延迟执行，确保抽屉组件已渲染
    setTimeout(() => {
        splitPropertyDrawerRef.value?.show({ splitId: data.splitId }, data.readonly, currentSelection.value)
    }, 100)
}

// 处理开启合并抽屉（从记录页点击查看合并记录）
const handleOpenMergeDrawer = (data: { mergeId: string, readonly: boolean }) => {
    showMergePropertyDrawer.value = true
    // 延迟执行，确保抽屉组件已渲染
    setTimeout(() => {
        mergePropertyDrawerRef.value?.show({ mergeId: data.mergeId }, data.readonly, currentSelection.value)
    }, 100)
}

// 立项定价相关处理函数
const handleProjectPricingSuccess = () => {
    showProjectPricingDrawer.value = false
    Message.success('立项定价申请提交成功')
    handleSearch()
}

const handleProjectPricingCancel = () => {
    showProjectPricingDrawer.value = false
}

</script>

<style scoped lang="less">
.container {
    padding: 0 16px;

}

.property-management {
    display: flex;
    gap: 16px;


    .left-panel {
        width: 200px;
        flex-shrink: 0;

        :deep(.arco-card-body){
            height: 100%;
        }
        .project-selector {
            height: 100%;
        }
    }

    .right-panel {
        flex: 1;
        min-width: 0;

        .general-card {
            height: 100%;
        }
    }
}

.filter-bar {
    background-color: var(--color-bg-2);
    border-radius: 4px;
}

.action-bar {
    .arco-space {
        flex-wrap: wrap;
        .arco-btn {
            margin-bottom: 16px;
        }
    }
}
.draft-filter-bar {
    margin-left: 16px;
}
:deep(.w-full) {
    @media (max-width: 1599px) {
        flex: 0 0 330px;
    }
}
</style>