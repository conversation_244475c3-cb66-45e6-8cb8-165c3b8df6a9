<template>
    <a-drawer :visible="drawerVisible" :footer="true" :title="drawerTitle" unmount-on-close @cancel="handleCancel"
        class="common-drawer">
        <div class="refund-form-container">
            <!-- 申请单标题 -->
            <!-- <div class="form-header">
                <div class="form-title">退款申请单</div>
            </div> -->

            <!-- 退款申请信息 -->
            <div class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>退款申请信息</span>
                </div>
                <a-form :model="formData" :rules="rules" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }"
                    label-align="right" ref="formRef">
                    <a-row :gutter="16">
                        <!-- <a-col :span="8">
                            <a-form-item field="projectId" label="项目" required>
                                <ProjectTreeSelect v-model="formData.projectId" />
                            </a-form-item>
                        </a-col> -->
                        <a-col :span="8">
                            <a-form-item field="refundType" label="退款类型" required>
                                <a-select v-model="formData.refundType" placeholder="请选择退款类型">
                                    <a-option :value="0">退租退款</a-option>
                                    <a-option :value="1">退定退款</a-option>
                                    <a-option :value="2">未明流水退款</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="applyTime" label="退款申请日期" required>
                                <a-date-picker v-model="formData.applyTime" style="width: 100%" show-time />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="feeType" label="退款费用类型">
                                <a-select v-model="formData.feeType" placeholder="请选择退款费用类型">
                                    <a-option value="1">保证金</a-option>
                                    <a-option value="2">租金</a-option>
                                    <a-option value="3">定金</a-option>
                                    <a-option value="4">未明流水</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="refundAmount" label="退款金额" required>
                                <a-input-number v-model="formData.refundAmount" placeholder="请输入退款金额"
                                    style="width: 100%" :precision="2" />
                                <template #append>元</template>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="refundWay" label="退款方式" required>
                                <a-select v-model="formData.refundWay" placeholder="请选择退款方式">
                                    <a-option :value="0">原路退回</a-option>
                                    <a-option :value="1">银行转账</a-option>
                                </a-select>
                                <div class="tip-text">默认原路退回，如果有特殊要求需要按付款，请在退款申请中说明</div>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverName" label="收款方名称" required>
                                <a-input v-model="formData.receiverName" placeholder="请输入收款方名称" allow-clear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverAccount" label="收款方账号" required>
                                <a-input v-model="formData.receiverAccount" placeholder="请输入收款方账号及开户行" allow-clear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverBank" label="补充银行信息">
                                <a-input v-model="formData.receiverBank" placeholder="请输入补充银行信息" allow-clear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item field="refundRemark" label="退款申请说明" :label-col-props="{ span: 3 }"
                                :wrapper-col-props="{ span: 21 }">
                                <a-textarea v-model="formData.refundRemark" placeholder="请输入退款申请说明" allow-clear
                                    :max-length="200" show-word-limit />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>

            <!-- 退租信息 -->
            <div class="form-section" v-if="formData.refundType !== 2">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>退租信息</span>
                    <a-button type="text" @click="handleViewContractDetail">查看详情</a-button>
                </div>
                <div class="base-info">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">承租方：</span>
                            <span class="info-value">{{ contractInfo.tenantName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退租类型：</span>
                            <span class="info-value">{{ getRefundTypeName(contractInfo.refundType) }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退租日期：</span>
                            <span class="info-value">{{ contractInfo.refundDate }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">合同用途：</span>
                            <span class="info-value">{{ contractInfo.contractPurpose }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ contractInfo.contractNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">合同终止日期：</span>
                            <span class="info-value">{{ contractInfo.endDate }}</span>
                        </div>
                    </div>
                </div>

                <!-- 退租费用明细表格 -->
                <div class="refund-detail">
                    <a-table :data="refundDetailData" :columns="refundDetailColumns" :pagination="false"
                        :bordered="{ cell: true }">
                        <template #feeExplain="{ record }">
                            <span>{{ record.feeExplain }}</span>
                        </template>
                    </a-table>
                    <div class="total-sum">
                        <span>减免金额：<span class="money">{{ formatMoney(discountAmount) }}</span></span>
                        <span>最终费用金额：<span class="money">{{ formatMoney(finalAmount) }}</span></span>
                    </div>
                    <div class="discount-reason">
                        <span>减免原因：</span>
                        <span>{{ discountReason }}</span>
                    </div>
                </div>
            </div>

            <!-- 流水信息 -->
            <div class="form-section" v-if="formData.refundType === 2">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>流水信息</span>
                </div>
                <div class="base-info">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">流水号：</span>
                            <span class="info-value">{{ flowInfo.flowNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付时间：</span>
                            <span class="info-value">{{ flowInfo.payTime }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付金额：</span>
                            <span class="info-value">{{ formatMoney(flowInfo.amount) }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">支付方式：</span>
                            <span class="info-value">{{ flowInfo.payMethod }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付账号：</span>
                            <span class="info-value">{{ flowInfo.payAccount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">银行支行：</span>
                            <span class="info-value">{{ flowInfo.bankBranch }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 手续办理情况 -->
            <div class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>手续办理情况</span>
                </div>
                <div class="procedure-status">
                    <div class="procedure-item">
                        <span class="procedure-name">营业执照：</span>
                        <span class="procedure-value">{{ procedureStatus.businessLicense ? '已办理' : '未办理' }}</span>
                    </div>
                    <div class="procedure-item">
                        <span class="procedure-name">税务登记证：</span>
                        <span class="procedure-value">{{ procedureStatus.taxRegistration ? '已办理' : '未办理' }}</span>
                    </div>
                </div>
            </div>

            <!-- 附件上传 -->
            <div class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>附件：</span>
                </div>
                <div class="attachment-upload">
                    <upload-file v-model="formData.attachments" :limit="0" />
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <template #footer>
            <div class="drawer-footer">
                <a-space>
                    <a-button @click="handleCancel">取消</a-button>
                    <a-button type="primary" status="success" @click="handleSave">暂存</a-button>
                    <a-button type="primary" @click="handleSubmit">发起审批</a-button>
                </a-space>
            </div>
        </template>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import type { TableColumnData } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue';
import dayjs from 'dayjs';
import UploadFile from '@/components/upload/uploadFile.vue';
import { getFinancialRefundDetail, saveFinancialRefund, type FinancialRefundDetailVo, type FinancialRefundAddDTO } from '@/api/refundManage';

// 抽屉控制
const drawerVisible = ref(false);
const drawerTitle = ref('退款申请单');

// 表单数据
const formData = reactive<Omit<FinancialRefundAddDTO, 'isSubmit'> & { isSubmit: number }>({
    refundType: 2, // 默认未明流水退款
    applyTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    feeType: '',
    refundAmount: 0,
    refundWay: 0, // 默认原路退回
    receiverName: '',
    receiverAccount: '', // 包含开户行信息的完整账号
    receiverBank: '', // 可选的额外银行信息
    refundRemark: '',
    attachments: '',
    isSubmit: 0,
    projectId: ''
});

// 合同信息
const contractInfo = reactive({
    tenantName: '',
    refundType: '',
    refundDate: '',
    contractPurpose: '',
    contractNo: '',
    endDate: ''
});

// 退款明细数据接口
interface RefundDetailItem {
    paymentType: '收' | '支';
    feeItem: string;
    amount: number;
    feePeriod: string;
    feeExplain: string;
}

// 流水信息接口
interface FlowInfo {
    flowNo: string;
    payTime: string;
    amount: number;
    payMethod: string;
    payAccount: string;
    bankBranch: string;
}

// 退款费用明细表格列定义
const refundDetailColumns = ref<TableColumnData[]>([
    {
        title: '收支类型',
        dataIndex: 'paymentType',
        width: 120,
        align: 'center'
    },
    {
        title: '费用科目',
        dataIndex: 'feeItem',
        width: 120,
        align: 'center'
    },
    {
        title: '金额（元）',
        dataIndex: 'amount',
        width: 120,
        align: 'center'
    },
    {
        title: '费用周期',
        dataIndex: 'feePeriod',
        width: 200,
        align: 'center'
    },
    {
        title: '费用说明',
        dataIndex: 'feeExplain',
        slotName: 'feeExplain',
        align: 'center'
    }
]);

// 退款费用明细数据
const refundDetailData = ref<RefundDetailItem[]>([]);

// 计算减免金额和最终金额
const discountAmount = ref(0);
const finalAmount = computed(() => {
    const totalExpense = refundDetailData.value
        .filter(item => item.paymentType === '支')
        .reduce((sum, item) => sum + item.amount, 0);

    const totalIncome = refundDetailData.value
        .filter(item => item.paymentType === '收')
        .reduce((sum, item) => sum + item.amount, 0);

    return totalExpense - totalIncome - discountAmount.value;
});

// 减免原因
const discountReason = ref('');

// 手续办理情况
const procedureStatus = reactive({
    businessLicense: false,
    taxRegistration: false
});

// 流水信息
const flowInfo = reactive<FlowInfo>({
    flowNo: '',
    payTime: '',
    amount: 0,
    payMethod: '',
    payAccount: '',
    bankBranch: ''
});

// 退款类型名称映射
const getRefundTypeName = (type: string) => {
    const typeMap: Record<string, string> = {
        '1': '提前退租',
        '2': '正常退租',
        '3': '违约退租'
    };
    return typeMap[type] || '';
};

// 金额格式化
const formatMoney = (amount: number) => {
    return amount.toFixed(2);
};

const formRef = ref<FormInstance>();

// 表单校验规则
const rules = {
    refundType: [
        { required: true, message: '请选择退款类型' }
    ],
    refundAmount: [
        { required: true, message: '请输入退款金额' },
        { type: 'number', min: 0, message: '退款金额必须大于0' }
    ],
    receiverName: [
        { required: true, message: '请输入收款方名称' }
    ],
    receiverAccount: [
        { required: true, message: '请输入收款方账号' }
    ],
    projectId: [
        { required: true, message: '请选择项目' }
    ]
};

// 加载退款详情
const loadRefundDetail = async (bizId: string, refundType?: number) => {
    try {
        const response = await getFinancialRefundDetail(undefined, refundType, bizId);
        if (response.data) {
            const detailData = response.data;
            
            // 填充表单数据
            if (detailData.refund) {
                const refund = detailData.refund;
                formData.refundType = refund.refundType || 0;
                formData.applyTime = refund.applyTime || dayjs().format('YYYY-MM-DD HH:mm:ss');
                formData.feeType = refund.feeType || '';
                formData.refundAmount = refund.refundAmount || 0;
                formData.refundWay = refund.refundWay || 0;
                formData.receiverName = refund.receiverName || '';
                formData.receiverAccount = refund.receiverAccount || '';
                formData.receiverBank = refund.receiverBank || '';
                formData.refundRemark = refund.refundRemark || '';
                formData.attachments = refund.attachments || '';
                formData.projectId = refund.projectId || '';
            }

            // 根据退款类型填充相关信息
            if (detailData.refund?.refundType === 0 && detailData.terminate) {
                // 退租退款 - 填充合同信息
                const terminate = detailData.terminate;
                contractInfo.refundType = terminate.terminateType?.toString() || '1';
                contractInfo.refundDate = terminate.terminateDate || '';
                contractInfo.contractPurpose = '商铺'; // 根据实际数据调整
                contractInfo.tenantName = detailData.refund.refundTarget || '';
                contractInfo.contractNo = terminate.contractId || '';
                contractInfo.endDate = '';
            } else if (detailData.refund?.refundType === 1 && detailData.booking) {
                // 退定退款 - 填充订单信息
                const booking = detailData.booking;
                contractInfo.tenantName = booking.customerName || '';
                contractInfo.contractNo = booking.bookingNo || '';
            }
            
            console.log('已加载退款详情:', detailData);
        }
    } catch (error) {
        console.error('加载退款详情失败:', error);
        Message.error('加载退款详情失败');
    }
};

// 查看合同详情
const handleViewContractDetail = () => {
    console.log('查看合同详情', contractInfo.contractNo);
    // TODO: 实现查看合同详情功能
};

// 取消
const handleCancel = () => {
    drawerVisible.value = false;
};

// 保存为草稿
const handleSave = async () => {
    const validResult = await formRef.value?.validate();
    if (!validResult) {
        formData.isSubmit = 0;
        await saveFinancialRefund({
            ...formData,
            isSubmit: 0
        });
        Message.success('保存成功');
        drawerVisible.value = false;
    }
};

// 提交审批
const handleSubmit = async () => {
    const validResult = await formRef.value?.validate();
    if (!validResult) {
        formData.isSubmit = 1;
        await saveFinancialRefund({
            ...formData,
            isSubmit: 1
        });
        Message.success('提交成功');
        drawerVisible.value = false;
    }
};

// 暴露方法给父组件
defineExpose({
    async open(options?: { refundType?: number; bizId?: string; flowData?: any }) {
        // 重置表单数据
        Object.assign(formData, {
            refundType: options?.refundType ?? 0,
            applyTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            feeType: '',
            refundAmount: 0,
            refundWay: 0,
            receiverName: '',
            receiverAccount: '',
            receiverBank: '',
            refundRemark: '',
            attachments: '',
            isSubmit: 0,
            projectId: ''
        });

        // 如果传入了 bizId，则先加载退款详情
        if (options?.bizId) {
            const response = await getFinancialRefundDetail(undefined, options?.refundType, options?.bizId);
            if (response.data) {
                const detailData = response.data;
                
                // 填充表单数据
                if (detailData.refund) {
                    const refund = detailData.refund;
                    formData.refundType = refund.refundType ?? options?.refundType ?? 0;
                    formData.applyTime = refund.applyTime || dayjs().format('YYYY-MM-DD HH:mm:ss');
                    formData.feeType = refund.feeType || '';
                    formData.refundAmount = refund.refundAmount || 0;
                    formData.refundWay = refund.refundWay ?? 0;
                    formData.receiverName = refund.receiverName || '';
                    formData.receiverAccount = refund.receiverAccount || '';
                    formData.receiverBank = refund.receiverBank || '';
                    formData.refundRemark = refund.refundRemark || '';
                    formData.attachments = refund.attachments || '';
                    formData.projectId = refund.projectId || '';
                    formData.isSubmit = 0; // 重新打开时始终设为未提交状态
                }

                // 根据退款类型填充相关信息
                if (detailData.refund?.refundType === 0 && detailData.terminate) {
                    // 退租退款 - 填充合同信息
                    const terminate = detailData.terminate;
                    contractInfo.refundType = terminate.terminateType?.toString() || '1';
                    contractInfo.refundDate = terminate.terminateDate || '';
                    contractInfo.contractPurpose = '商铺';
                    contractInfo.tenantName = detailData.refund.refundTarget || '';
                    contractInfo.contractNo = terminate.contractId || '';
                    contractInfo.endDate = '';
                } else if (detailData.refund?.refundType === 1 && detailData.booking) {
                    // 退定退款 - 填充订单信息
                    const booking = detailData.booking;
                    contractInfo.tenantName = booking.customerName || '';
                    contractInfo.contractNo = booking.bookingNo || '';
                }
            }
        }

        // 如果传入了流水数据,则使用流水数据初始化或覆盖表单
        if (options?.flowData) {
            formData.projectId = options.flowData.projectId || formData.projectId;
            formData.refundAmount = options.flowData.amount - (options.flowData.usedAmount || 0);
            formData.receiverName = options.flowData.payerName || formData.receiverName;
            formData.receiverAccount = options.flowData.payerAccount || formData.receiverAccount;

            // 初始化流水信息
            flowInfo.flowNo = options.flowData.flowNo || '';
            flowInfo.payTime = options.flowData.payTime || '';
            flowInfo.amount = options.flowData.amount || 0;
            flowInfo.payMethod = options.flowData.payMethod || '';
            flowInfo.payAccount = options.flowData.payerAccount || '';
            flowInfo.bankBranch = options.flowData.bankBranch || '';
        } else {
            // 重置流水信息
            Object.assign(flowInfo, {
                flowNo: '',
                payTime: '',
                amount: 0,
                payMethod: '',
                payAccount: '',
                bankBranch: ''
            });
        }
        
        drawerVisible.value = true;
    }
});
</script>

<style scoped lang="less">
.refund-form-container {
    padding: 0 16px;

    .form-header {
        text-align: center;
        margin-bottom: 24px;

        .form-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }
    }

    .form-section {
        margin-bottom: 24px;

        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .section-marker {
                width: 4px;
                height: 16px;
                background-color: #1890ff;
                margin-right: 8px;
                border-radius: 2px;
            }

            span {
                font-size: 16px;
                font-weight: 500;
                flex: 1;
            }
        }

        .base-info {
            background-color: #f9f9f9;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 16px;

            .info-row {
                display: flex;
                margin-bottom: 12px;

                &:last-child {
                    margin-bottom: 0;
                }

                .info-item {
                    flex: 1;
                    display: flex;

                    .info-label {
                        color: #646a73;
                        margin-right: 8px;
                        min-width: 90px;
                    }

                    .info-value {
                        color: #1d2129;
                        flex: 1;
                    }
                }
            }
        }

        .refund-detail {
            margin-top: 16px;

            .total-sum {
                display: flex;
                justify-content: flex-end;
                margin-top: 16px;
                gap: 24px;

                .money {
                    font-weight: 600;
                    color: #f56c6c;
                }
            }

            .discount-reason {
                display: flex;
                justify-content: flex-end;
                margin-top: 12px;
                color: #646a73;
            }
        }

        .procedure-status {
            display: flex;
            gap: 24px;
            background-color: #f9f9f9;
            padding: 16px;
            border-radius: 4px;

            .procedure-item {
                display: flex;
                align-items: center;

                .procedure-name {
                    color: #646a73;
                    margin-right: 8px;
                }

                .procedure-value {
                    color: #1d2129;
                    font-weight: 500;
                }
            }
        }
    }
}

.drawer-footer {
    display: flex;
    justify-content: flex-end;
}
</style>