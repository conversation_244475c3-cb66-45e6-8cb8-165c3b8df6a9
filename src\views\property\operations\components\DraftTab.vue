<template>
  <div class="draft-tab">
    <!-- 表格 -->
    <a-table
      :columns="columns"
      :data="tableData"
      :pagination="pagination"
      :bordered="{ cell: true }"
      :scroll="{ x: 1 }"
      :stripe="true"
      :loading="loading"
      :row-selection="rowSelection"
      row-key="id"
      v-model:selectedKeys="localSelectedKeys"
      @page-change="onPageChange"
      @page-size-change="onPageSizeChange"
    >
      <template #roomName="{ record }">
        <PermissionLink v-permission="['rent:room:query']" @click="handleViewDetails(record)">{{ record.roomName }}</PermissionLink>
      </template>
      <template #operations="{ record }">
        <a-space>
          <a-button v-permission="['rent:room:edit']" type="text" size="mini" @click="handleEdit(record)">编辑</a-button>
          <a-button v-permission="['rent:room:remove']" type="text" size="mini"status="danger" @click="handleDelete(record)">删除</a-button>
          <a-button v-permission="['rent:room:effect']" type="text" size="mini" @click="handleActivate(record)">生效</a-button>
        </a-space>
      </template>
    </a-table>
  </div>
  <add-operation-drawer ref="addDrawerRef" />
</template>

<script lang="ts" setup>
import { ref, reactive, watch, PropType, onMounted } from 'vue';
import { Modal } from '@arco-design/web-vue';
import {
  IconSearch,
  IconRefresh,
  IconPlus,
  IconDownload,
  IconUpload,
  IconCheck,
  IconEdit,
  IconDelete
} from '@arco-design/web-vue/es/icon';
import type { TableColumnData, Pagination } from '@arco-design/web-vue';
import { Message } from '@arco-design/web-vue';
import addOperationDrawer from './addOperationDrawer.vue'
import { getRoomList, effectRooms , RoomStatus , deleteRoomById } from '@/api/room';

interface FilterForm {
  projectId?: string;
  parcelOrBuildingName: string;
  roomName: string;
  propertyType?: string;
  priceFlag?: number;
  status?: number;
  type: number;
  pageNum: number;
  pageSize: number;
}

interface RoomVo {
  id: string;
  name: string;
  propertyType: string;
  parcelName: string;
  buildingName: string;
  floorName: string;
  rentArea: number;
  rentPrice: number;
  rentUnit: string;
  roomCode: string;
  [key: string]: any;
}

// 定义props
const props = defineProps({
  filterForm: {
    type: Object as PropType<FilterForm>,
    required: true
  },
  selectedKeys: {
    type: Array as PropType<string[]>,
    default: () => []
  }
});

// 定义emit事件
const emit = defineEmits<{
  'update:loading': [value: boolean]
  'update:selectedKeys': [keys: string[]]
  'view-detail': [id: string]
  'edit-room': [id: string]
  'update-room-data-cache': [data: any[]]
}>()
// 内部选中的行keys
const localSelectedKeys = ref<string[]>([]);

// 监听selectedKeys的变化
watch(() => props.selectedKeys, (newVal) => {
  // 检查是否与当前值相同，如果相同则不更新
  if (JSON.stringify(localSelectedKeys.value) !== JSON.stringify(newVal)) {
    localSelectedKeys.value = [...newVal];
  }
}, { deep: true });

// 监听本地selectedKeys的变化，同步到父组件
watch(() => localSelectedKeys.value, (newVal) => {
  // 检查是否与props中的值相同，如果相同则不触发更新
  if (JSON.stringify(props.selectedKeys) !== JSON.stringify(newVal)) {
    emit('update:selectedKeys', newVal);
  }
}, { deep: true });

// 加载状态
const loading = ref(false);

// 更新loading状态
const setLoading = (value: boolean) => {
  loading.value = value;
  emit('update:loading', value);
};

// 表格列定义
const columns: TableColumnData[] = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
  { title: '多经点位名称', dataIndex: 'roomName', slotName: 'roomName', width: 200, ellipsis: true, tooltip: true, align: 'center' },
  { title: '多经用途', dataIndex: 'propertyTypeName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '所属地块', dataIndex: 'parcelName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼栋', dataIndex: 'buildingName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '楼层', dataIndex: 'floorName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '计租面积 (㎡)', dataIndex: 'rentArea', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '可招租日期', dataIndex: 'rentalStartDate', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '对外出租起始日期', dataIndex: 'externalRentStartDate', width: 150, ellipsis: true, tooltip: true, align: 'center' },
  { title: '对应房源', dataIndex: 'djBindName', width: 120, ellipsis: true, tooltip: true, align: 'center' },
  { title: '操作', slotName: 'operations', width: 210, fixed: 'right', align: 'center' },
];

// 表格数据
const tableData = ref<RoomVo[]>([]);

// 分页配置
const pagination = reactive<Pagination>({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
});

// 表格选择配置
const rowSelection = {
  type: 'checkbox',
  showCheckedAll: true,
};

// 加载数据
const loadData = async () => {
  try {
    if (!props.filterForm.projectId) {
      console.log('没有项目ID，跳过接口调用')
      return
    }
    setLoading(true);
    const response = await getRoomList({
      ...props.filterForm,
      status: RoomStatus.DRAFT, // 只查询草稿房源
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    });
    
    if (response.rows) {
      tableData.value = response.rows.map((item: RoomVo, index: number) => ({
        ...item,
        index: (pagination.current - 1) * pagination.pageSize + index + 1
      }));
      pagination.total = response.total;

      // 发出数据更新事件，供父组件缓存
      emit('update-room-data-cache', tableData.value);
    }
  } catch (error) {
    console.error('加载多经点位列表失败:', error);
  } finally {
    setLoading(false);
  }
};


// 页码变化
const onPageChange = (current: number) => {
  pagination.current = current;
  loadData();
};

// 每页条数变化
const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1; // 重置页码
  loadData();
};

// 查看详情
const handleViewDetails = (record: RoomVo) => {
  console.log('查看详情:', record);
  if (record.id) {
    emit('view-detail', record.id)
  } else {
    Message.error('房源ID不存在')
  }
};

// 编辑
const handleEdit = (record: RoomVo) => {
  console.log('编辑房源:', record)
  if (record.id) {
    emit('edit-room', record.id)
  } else {
    Message.error('房源ID不存在')
  }
}

// 删除
const handleDelete = (record: RoomVo) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除 ${record.roomName} 吗？`,
    onOk:async  () => {
      try {
          // 使用正确的单个房源删除接口
        await deleteRoomById({ id: record.id })
          Message.success('删除成功')
          loadData();
      } catch (error) {
          console.error('删除房源失败:', error)
      }
    }
  });
};

// 生效
const handleActivate = async (record: RoomVo) => {
  Modal.confirm({
    title: '确认生效',
    content: `确定要生效 ${record.roomName} 吗？`,
    onOk: async () => {
      try {
        await effectRooms([record.id]);
        Message.success('生效成功');
        loadData();
      } catch (error) {
        console.error('生效失败:', error);
        Message.error('生效失败');
      }
    }
  });
};


// 监听父组件的刷新事件
const handleRefresh = () => {
  console.log('收到刷新事件')
  loadData();
}

// 移除对selection的监听，避免重复调用接口
// 改为只通过全局事件触发刷新

// 监听特定的刷新事件
onMounted(() => {
  document.addEventListener('refresh-operation-draft-data', handleRefresh)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('refresh-operation-draft-data', handleRefresh)
})
</script>

<style lang="less" scoped>
.draft-tab {
  width: 100%;
}
</style> 