# 智能门锁API重构说明

## 重构内容

### 1. 文件结构调整
- **删除**: `src/api/smartDevices` 文件夹及其所有文件
  - `src/api/smartDevices/doorLock.ts`
  - `src/api/smartDevices/index.ts`
  - `src/api/smartDevices/types.ts`
  - `src/api/smartDevices/waterElectric.ts`

- **新增**: `src/api/smartLock.ts` 单独文件

### 2. API接口实现

根据 `.cursor/api/智能门锁.md` 文档，实现了以下API接口：

#### 房源管理
- `getRoomList(params)` - 查询房源信息列表 (POST /ttlock/room/list)
- `exportRoomList(params)` - 导出房源信息列表 (POST /ttlock/room/export)

#### 设备管理
- `getTTLockDeviceList(params)` - 查询通通锁设备列表 (GET /ttlock/device/list)
- `getTTLockDetail(deviceId)` - 智能门锁详情 (GET /ttlock/device/detail)
- `getDeviceInfo(params)` - 获取设备信息 (POST /ttlock/device/info)
- `exportTTLockDeviceList(params)` - 导出通通锁设备列表 (POST /ttlock/device/export)
- `syncDeviceData(params)` - 同步设备数据 (POST /ttlock/device/sync)

#### 设备绑定
- `bindRoomDevice(data)` - 保存关联关系 (POST /ttlock/device/room/bind)
- `unbindRoomDevice(roomIds)` - 解绑房间与设备 (POST /ttlock/device/room/bind/delete)
- `autoBindRoomDevice(projectId)` - 自动匹配绑定关系 (POST /ttlock/device/room/auto/bind)

#### 密码管理
- `getLockOperateLog(params)` - 智能门锁密码记录 (POST /ttlock/log/detail)
- `generateTempPassword(deviceId)` - 获取临时密码 (POST /ttlock/generate/password)

#### 账号管理
- `getTTLockAccountList(params)` - 查询通通锁账号列表 (GET /ttlock/account/list)
- `addTTLockAccount(data)` - 新增通通锁账号 (POST /ttlock/account/save)
- `deleteTTLockAccount(id)` - 删除通通锁账号 (DELETE /ttlock/account/delete)

### 3. 类型定义

在同一文件中定义了所有相关的TypeScript类型：

#### 基础类型
- `BasePageParams` - 基础分页参数
- `AjaxResult<T>` - 通用响应结果
- `TableDataInfo<T>` - 分页响应结果

#### 业务类型
- `RoomSimpleQueryDTO` - 房源查询参数
- `RoomSimpleVo` - 房源信息
- `TTLockDeviceQueryParams` - 智能门锁设备查询参数
- `TTLockDeviceVo` - 智能门锁设备信息
- `TTLockOperateLogQueryDTO` - 操作日志查询参数
- `TTLockOperateLogVo` - 操作日志信息
- `TTLockAccountQueryDTO` - 通通锁账号查询参数
- `TTLockAccountAddDTO` - 通通锁账号新增参数
- `TTLockDeviceInfoQueryDTO` - 设备信息查询参数
- `TTLockSyncDeviceDTO` - 同步设备数据参数

### 4. 技术规范

- 使用 `/business-rent-admin` 作为API前缀
- 遵循项目的API调用规范
- 所有接口都有完整的TypeScript类型定义
- 包含详细的JSDoc注释
- 导出功能使用 `responseType: 'blob'` 处理文件下载

### 5. 优势

1. **简化结构**: 将智能门锁相关功能整合到单个文件中，便于维护
2. **类型安全**: 完整的TypeScript类型定义确保类型安全
3. **文档完整**: 每个API函数都有详细的注释说明
4. **规范统一**: 遵循项目现有的API调用规范
5. **易于扩展**: 单文件结构便于后续功能扩展

### 6. 组件联调更新

#### DoorLockTab.vue 组件更新
- **API集成**: 将组件从使用模拟数据改为调用真实的 `getTTLockDeviceList` API
- **表格列数据驱动**: 参考 `src/views/reduction/index.vue` 的实现，将表格列配置改为数据驱动方式
- **类型安全**: 使用 `TTLockDeviceVo` 类型确保数据类型安全
- **条件加载**: 只有在有项目ID时才调用API接口

#### 表格列配置
```typescript
const columns = [
    {
        title: '序号',
        slotName: 'index',
        width: 80,
        align: 'center'
    },
    {
        title: '房号',
        dataIndex: 'roomName',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
    },
    // ... 其他列配置
    {
        title: '操作',
        slotName: 'action',
        width: 120,
        align: 'center',
        fixed: 'right'
    }
]
```

#### API调用逻辑
```typescript
const fetchTableData = async () => {
  // 如果没有项目ID，不调用接口
  if (!props.filterForm.projectId) {
    tableData.value = []
    pagination.total = 0
    return
  }

  const params = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    projectId: props.filterForm.projectId,
    roomOrBuildingName: props.filterForm.buildingOrRoom || undefined
  }

  const response = await getTTLockDeviceList(params)

  if (response.rows) {
    tableData.value = response.rows
    pagination.total = response.total
  }
}
```

### 7. 使用示例

```typescript
import {
    getRoomList,
    getTTLockDeviceList,
    generateTempPassword
} from '@/api/smartLock'

// 查询房源列表
const roomList = await getRoomList({
    pageNum: 1,
    pageSize: 10,
    projectId: 'xxx'
})

// 查询设备列表
const deviceList = await getTTLockDeviceList({
    pageNum: 1,
    pageSize: 10,
    projectId: 'xxx'
})

// 生成临时密码
const result = await generateTempPassword('deviceId123')
```

### 8. 改动总结

#### 完成的工作
1. ✅ 删除了 `src/api/smartDevices` 文件夹
2. ✅ 创建了 `src/api/smartLock.ts` 智能门锁API文件
3. ✅ 实现了所有智能门锁相关的API接口
4. ✅ 更新了 `DoorLockTab.vue` 组件以使用真实API
5. ✅ 将表格列改为数据驱动方式
6. ✅ 添加了完整的TypeScript类型定义
7. ✅ 实现了条件加载逻辑（只有项目ID时才调用API）

#### 技术改进
- **数据驱动**: 表格列配置使用数据驱动，便于维护和扩展
- **类型安全**: 完整的TypeScript类型系统
- **性能优化**: 条件加载避免不必要的API调用
- **用户体验**: 统一的表格样式和交互体验
- **代码规范**: 遵循项目现有的代码规范和最佳实践
