<template>
  <div class="container">
    <a-card class="general-card" :title="'操作日志'">
      <a-row v-if="showSearch">
        <a-col :flex="1">
          <a-form :model="queryParams" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
            label-align="right">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="operIp" label="操作地址">
                  <a-input v-model="queryParams.operIp" placeholder="请输入操作地址" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="title" label="系统模块">
                  <a-input v-model="queryParams.title" placeholder="请输入系统模块" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="operName" label="操作人员">
                  <a-input v-model="queryParams.operName" placeholder="请输入操作人员" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="businessType" label="操作类型">
                  <a-select v-model="queryParams.businessType" placeholder="操作类型" allow-clear>
                    <a-option v-for="dict in sys_oper_type" :key="dict.value" :value="dict.value">
                      {{ dict.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" label="状态">
                  <a-select v-model="queryParams.status" placeholder="操作状态" allow-clear>
                    <a-option v-for="dict in sys_common_status" :key="dict.value" :value="dict.value">
                      {{ dict.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="操作时间">
                  <a-range-picker v-model="dateRange" style="width: 100%" value-format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleQuery">
              <template #icon><icon-search /></template>
              搜索
            </a-button>
            <a-button @click="resetQuery">
              <template #icon><icon-refresh /></template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="16">
          <a-space>
            <a-button type="primary" status="danger" :disabled="!selectedKeys.length" @click="handleDelete">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
            <a-button type="primary" status="danger" @click="handleClean">
              <template #icon><icon-delete /></template>
              清空
            </a-button>
            <a-button type="primary" status="warning" @click="handleExport">
              <template #icon><icon-download /></template>
              导出
            </a-button>
          </a-space>
        </a-col>
        <a-col :span="8" style="text-align: right">
          <a-button type="text" @click="showSearch = !showSearch">
            <template #icon><icon-filter /></template>
            筛选
          </a-button>
        </a-col>
      </a-row>

      <a-table :row-selection="rowSelection" row-key="operId" :loading="loading" :data="list" :pagination="pagination"
        :scroll="{x: 1}" @page-change="onPageChange" @page-size-change="onPageSizeChange" @selection-change="handleSelectionChange">
        <template #columns>
          <a-table-column type="selection" :width="50" />
          <a-table-column title="日志编号" data-index="operId" align="center" :width="100" />
          <a-table-column title="系统模块" data-index="title" align="center" :ellipsis="true" :width="110" />
          <a-table-column title="操作类型" align="center" :width="110">
            <template #cell="{ record }">
              <dict-tag :options="sys_oper_type" :value="record.businessType" />
            </template>
          </a-table-column>
          <a-table-column title="请求方式" data-index="requestMethod" align="center" :width="110" />
          <a-table-column title="操作人员" data-index="operName" align="center" :ellipsis="true" :width="110" />
          <a-table-column title="操作地址" data-index="operIp" align="center" :ellipsis="true" :width="110" />
          <a-table-column title="操作状态" align="center" :width="110">
            <template #cell="{ record }">
              <dict-tag :options="sys_common_status" :value="record.status" />
            </template>
          </a-table-column>
          <a-table-column title="操作日期" data-index="operTime" align="center" :width="110">
            <template #cell="{ record }">
              {{ parseTime(record.operTime) }}
            </template>
          </a-table-column>
          <a-table-column title="消耗时间" data-index="costTime" align="center" :width="110">
            <template #cell="{ record }">
              {{ record.costTime }}毫秒
            </template>
          </a-table-column>
          <a-table-column title="操作" align="center" fixed="right" :width="110">
            <template #cell="{ record }">
              <a-button type="text" size="small" @click="handleView(record)">
                <template #icon><icon-eye /></template>
                详细
              </a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>

      <a-modal v-model:visible="open" title="操作日志详细" width="1000px" @cancel="open = false">
        <a-form :model="form" label-align="right" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 10 }">
          <a-row>
            <a-col :span="12">
              <a-form-item label="操作模块">{{ form.title }} / {{ typeFormat(form) }}</a-form-item>
              <a-form-item label="登录信息">{{ form.operName }} / {{ form.operIp }}</a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="请求地址">{{ form.operUrl }}</a-form-item>
              <a-form-item label="请求方式">{{ form.requestMethod }}</a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="操作方法">{{ form.method }}</a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="请求参数">{{ form.operParam }}</a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="返回参数">{{ form.jsonResult }}</a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="操作状态">
                <a-tag :color="form.status === 0 ? 'green' : 'red'">
                  {{ form.status === 0 ? '正常' : '失败' }}
                </a-tag>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="消耗时间">{{ form.costTime }}毫秒</a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="操作时间">{{ parseTime(form.operTime || '') }}</a-form-item>
            </a-col>
            <a-col :span="24" v-if="form.status === 1">
              <a-form-item label="异常信息">{{ form.errorMsg }}</a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <template #footer>
          <a-button @click="open = false">关闭</a-button>
        </template>
      </a-modal>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import DictTag from '@/components/DictTag/index.vue';
import { parseTime } from '@/utils/index';
import useLoading from '@/hooks/loading';
import { DictType } from '@/api/system/dict';
import { useDict } from '@/utils/dict'
import { exportOperaLog, listOperlog, delOperlog, cleanOperlog, OperaLogInfo } from '@/api/system/operalog'
import { exportExcel } from '@/utils/exportUtil';
import axios from 'axios';

interface OperLogForm {
  title?: string;
  operName?: string;
  operIp?: string;
  operUrl?: string;
  requestMethod?: string;
  method?: string;
  operParam?: string;
  jsonResult?: string;
  status?: number;
  errorMsg?: string;
  operTime?: string;
  costTime?: number;
  businessType?: string;
}

interface QueryParams {
  pageNum: number;
  pageSize: number;
  operIp?: string;
  title?: string;
  operName?: string;
  businessType?: string;
  status?: string;
  orderByColumn?: string;
  isAsc?: string;
}

const { sys_oper_type, sys_common_status } = useDict('sys_oper_type', 'sys_common_status')
// 状态管理
const { loading, setLoading } = useLoading();
const open = ref(false);
const selectedKeys = ref<string[]>([]);
const multiple = ref(true);
const showSearch = ref(true);
const list = ref<OperaLogInfo[]>([]);
const dateRange = ref([]);
const form = ref<OperLogForm>({});

const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showPageSize: true
});

// 查询参数
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  operIp: '',
  title: '',
  operName: '',
  businessType: '',
  status: ''
});

// 获取操作日志列表
const getList = async () => {
  try {
    setLoading(true);
    let params = {}
    if (dateRange.value[0] && dateRange.value[1]) {
      params = { ...queryParams, ...{ beginTime: dateRange.value[0], endTime: dateRange.value[1] } }
    } else {
      params = { ...queryParams }
    }
    const response = await listOperlog(params);
    list.value = response.rows as any[];
    pagination.total = response.total;
  } catch (error) {
    console.error('获取操作日志列表失败', error);
    Message.error('获取操作日志列表失败');
  } finally {
    setLoading(false);
  }
};

// 操作日志类型字典翻译
const typeFormat = (row: OperLogForm): string => {
  const dict = sys_oper_type.value.find((item: { value: string; label: string }) => item.value === row.businessType?.toString());
  return dict ? dict.label : '';
};

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置按钮操作
const resetQuery = () => {
  dateRange.value = []
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    operIp: undefined,
    title: undefined,
    operName: undefined,
    businessType: undefined,
    status: undefined
  });
  // 重置分页参数
  pagination.current = 1;
  pagination.pageSize = 10;
  getList();
};

// 多选框选中数据
const handleSelectionChange = (rowKeys: string[]) => {
  selectedKeys.value = rowKeys
};;

// 详细按钮操作
const handleView = (row: OperLogForm) => {
  open.value = true;
  form.value = row;
};

// 删除按钮操作
const handleDelete = async (row?: any) => {
  if (!selectedKeys.value.length) return;
  try {
    const handleConfirm = async () => {
      try {
        const res = await delOperlog(selectedKeys.value.join(','));
        if (res.code === 200) {
          Message.success('删除成功')
          getList()
        } else {
          Message.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败', error)
        Message.error('删除失败')
      }
    }
    await Modal.confirm({
      title: '确认删除',
      content: `是否确认删除日志编号为"${selectedKeys.value.join(',')}"的数据项？`,
      onOk: handleConfirm
    });
  } catch (error) {
    console.error('删除失败', error);
  }
};

// 清空按钮操作
const handleClean = async () => {
  try {
    const handleConfirm = async () => {
      try {
        const res = await cleanOperlog();
        if (res.code === 200) {
          Message.success('清空成功');
          getList()
        } else {
          Message.error(res.msg || '清空失败')
        }
      } catch (error) {
        console.error('清空失败', error)
        Message.error('清空失败')
      }
    }
    await Modal.confirm({
      title: '确认清空',
      content: '是否确认清空所有操作日志数据项？',
      onOk: handleConfirm
    });
  } catch (error) {
    console.error('清空失败', error);
  }
};

// 导出按钮操作
const handleExport = () => {
  let params = {}
  if (dateRange.value[0] && dateRange.value[1]) {
    params = { ...queryParams, ...{ beginTime: dateRange.value[0], endTime: dateRange.value[1] } }
  } else {
    params = { ...queryParams }
  }
  exportExcel(exportOperaLog, params, `operlog_${new Date().getTime()}`)

};
// script部分
const onPageChange = (current: number) => {
  queryParams.pageNum = current;
  pagination.current = current;
  getList();
};
// 每页条数改变
const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;  // 切换每页条数时重置为第一页
  queryParams.pageSize = pageSize;
  queryParams.pageNum = 1;
  getList();
};
// 初始化
getList();
</script>

<style scoped>
.container {
  padding: 0 16px 16px 16px;
}
</style>
