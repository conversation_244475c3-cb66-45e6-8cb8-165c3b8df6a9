/**
 * Windows 系统专用的 Vite 性能优化配置
 * 针对 Windows 文件系统和内存管理特点进行优化
 */
import type { UserConfig } from 'vite';
import os from 'os';

export interface WindowsPerformanceOptions {
    /** 是否启用缓存 */
    enableCache?: boolean;
    /** 是否启用多线程构建 */
    enableMultiThread?: boolean;
    /** 最大内存使用量(MB) */
    maxMemory?: number;
    /** 是否启用文件系统缓存 */
    enableFsCache?: boolean;
    /** 是否启用Windows特定优化 */
    enableWindowsOptimizations?: boolean;
}

export default function configWindowsPerformancePlugin(options: WindowsPerformanceOptions = {}): Partial<UserConfig> {
    const {
        enableCache = true,
        enableMultiThread = true,
        maxMemory = 12288,
        enableFsCache = true,
        enableWindowsOptimizations = true,
    } = options;

    // 获取 CPU 核心数，Windows通常能更好地利用所有核心
    const cpuCount = os.cpus().length;
    const workerCount = Math.min(cpuCount, 8); // Windows限制最大8个并行进程

    // Windows专用路径优化
    const isWindows = process.platform === 'win32';
    
    return {
        // Windows特定的解析优化
        resolve: {
            // Windows下使用原生路径解析
            preserveSymlinks: false,
        },

        // 优化构建配置
        build: {
            // Windows下的Rollup优化
            rollupOptions: {
                // 设置并行数，Windows下不宜过高
                maxParallelFileOps: workerCount,
                // Windows文件系统优化
                output: {
                    // 更激进的代码分割策略
                    manualChunks: (id) => {
                        // 将node_modules中的包按大小分组
                        if (id.includes('node_modules')) {
                            if (id.includes('@arco-design') || id.includes('arco')) {
                                return 'arco-ui';
                            }
                            if (id.includes('echarts')) {
                                return 'charts';
                            }
                            if (id.includes('vue-router') || id.includes('pinia')) {
                                return 'vue-core';
                            }
                            if (id.includes('lodash') || id.includes('dayjs') || id.includes('axios')) {
                                return 'utils';
                            }
                            if (id.includes('vue') && !id.includes('vue-router')) {
                                return 'vue-core';
                            }
                            // 其他第三方库
                            return 'vendor';
                        }
                    },
                    // 优化文件名长度，避免Windows路径限制
                    entryFileNames: 'js/[name]-[hash:6].js',
                    chunkFileNames: 'js/[name]-[hash:6].js',
                    assetFileNames: 'assets/[name]-[hash:6].[ext]',
                },
                // Windows下的外部依赖优化
                external: [],
            },
            // 关闭源码映射以提高构建速度
            sourcemap: false,
            // 增加chunk警告限制
            chunkSizeWarningLimit: 2000,
            // 关闭压缩大小报告
            reportCompressedSize: false,
            // Windows下的目标设置
            target: 'es2020',
            // 启用构建缓存
            ...(enableWindowsOptimizations && {
                // Windows特定构建优化
                minify: 'terser',
                terserOptions: {
                    compress: {
                        // Windows下更激进的压缩
                        drop_console: true,
                        drop_debugger: true,
                        pure_funcs: ['console.log', 'console.info'],
                    },
                    mangle: {
                        // 保持较短的变量名
                        toplevel: true,
                    },
                },
            }),
        },

        // 依赖预构建优化
        optimizeDeps: {
            // Windows下强制预构建更多依赖
            force: false,
            include: [
                'vue',
                'vue-router',
                'pinia',
                'pinia-plugin-persistedstate',
                '@arco-design/web-vue',
                '@arco-design/web-vue/es/icon',
                'echarts/core',
                'echarts/charts/LineChart',
                'echarts/charts/BarChart',
                'echarts/charts/PieChart',
                'echarts/components/TitleComponent',
                'echarts/components/TooltipComponent',
                'echarts/components/LegendComponent',
                'vue-echarts',
                'dayjs',
                'dayjs/locale/zh-cn',
                'lodash',
                'lodash-es',
                'axios',
                'nprogress',
                'mitt',
                'sortablejs',
                'query-string',
            ],
            exclude: [
                // 'vue-demi',
                '@iconify/iconify',
            ],
            // Windows下的esbuild优化
            esbuildOptions: {
                // 增加可用内存
                incremental: false,
                // Windows下的目标优化
                target: 'es2020',
                // 优化打包速度
                treeShaking: true,
            },
        },

        // Windows专用缓存设置
        cacheDir: enableFsCache ? (isWindows ? 'node_modules\\.vite-win' : 'node_modules/.vite') : undefined,

        // 服务器配置优化
        server: {
            // Windows文件系统监听优化
            watch: {
                // 使用轮询模式，解决Windows文件监听问题
                usePolling: isWindows,
                interval: isWindows ? 1000 : undefined,
                // 忽略node_modules减少监听负担
                ignored: ['**/node_modules/**', '**/.git/**'],
            },
            fs: {
                // Windows下严格模式可能导致问题
                strict: false,
                // 允许访问工作区外的文件
                allow: ['..'],
            },
        },

        // esbuild优化
        esbuild: {
            // Windows下的目标环境
            target: 'es2020',
            // 保持函数名用于调试
            keepNames: false,
            // 优化压缩
            minifyIdentifiers: true,
            minifySyntax: true,
            minifyWhitespace: true,
            // Windows下的源码映射
            sourcemap: false,
        },

        // 定义环境变量
        define: {
            // Windows特定的环境变量
            __WINDOWS_BUILD__: isWindows,
            __DEV__: false,
            __PROD__: true,
        },

        // worker配置
        worker: {
            // Windows下的worker优化
            format: 'es',
            rollupOptions: {
                output: {
                    entryFileNames: 'workers/[name]-[hash:6].js',
                },
            },
        },
    };
} 