<template>
    <section>
        <sectionTitle title="优惠条款" />
        <div class="content">
            <a-space direction="vertical" fill>
                <a-row justify="end" v-if="!isDetailMode">
                    <a-space>
                        <a-button type="primary" @click="handleAdd">添加优惠条款</a-button>
                        <a-button type="primary" @click="handleFirstMonth">免首月</a-button>
                        <a-button type="primary" @click="handleLastMonth">免尾月</a-button>
                    </a-space>
                </a-row>
                <a-table v-if="contractData.fees?.length" :data="contractData.fees" :scroll="{ x: 1 }" :bordered="{ cell: true }" :pagination="false">
                    <template #columns>
                        <a-table-column title="条款类型" :width="140" align="center">
                            <template #cell="{ record }">
                                <template v-if="isDetailMode">
                                    <div class="detail-text">{{ getDiscountTypeLabel(record.freeType) }}</div>
                                </template>
                                <template v-else>
                                    <a-select v-model="record.freeType" :disabled="editType === 'change' && changeType.includes(3) && isStarted(record.startDate, record.endDate)">
                                        <!-- 当合同用途为宿舍时，只显示合同免租选项 -->
                                        <template v-if="contractData.contractPurpose === 10">
                                            <a-option :value="2">合同免租</a-option>
                                        </template>
                                        <!-- 其他合同用途显示所有选项 -->
                                        <template v-else>
                                            <a-option :value="0">装修免租</a-option>
                                            <a-option :value="1">经营免租</a-option>
                                            <a-option :value="2">合同免租</a-option>
                                        </template>
                                    </a-select>
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column title="开始日期" :width="180" align="center">
                            <template #cell="{ record }">
                                <template v-if="isDetailMode">
                                    <div class="detail-text">{{ record.startDate || '无' }}</div>
                                </template>
                                <template v-else>
                                    <a-date-picker v-model="record.startDate" format="YYYY-MM-DD" :disabled="editType === 'change' && changeType.includes(3) && isStarted(record.startDate, record.endDate)"
                                        @change="(date: string) => handleStartDateChange(record, date)" :disabled-date="disabledDate" />
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column title="免租天数" align="center" :width="180">
                            <template #cell="{ record }">
                                <template v-if="isDetailMode">
                                    <div class="detail-text">{{ (record.freeRentMonth || 0) + '个月 ' + (record.freeRentDay
                                        || 0) + '天' }}</div>
                                </template>
                                <template v-else>
                                    <a-space>
                                        <a-input-number v-model="record.freeRentMonth"  :disabled="editType === 'change' && changeType.includes(3) && isStarted(record.startDate, record.endDate)"
                                            @change="(value: number) => handleFreeMonthsChange(record, value)" :min="0"
                                            :max="99">
                                            <template #suffix>个月</template>
                                        </a-input-number>
                                        <a-input-number v-model="record.freeRentDay" :disabled="editType === 'change' && changeType.includes(3) && isStarted(record.startDate, record.endDate)"
                                            @change="(value: number) => handleFreeDaysChange(record, value)" :min="0"
                                            :max="31">
                                            <template #suffix>天</template>
                                        </a-input-number>
                                    </a-space>
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column title="结束日期" :width="180" align="center">
                            <template #cell="{ record }">
                                <template v-if="isDetailMode">
                                    <div class="detail-text">{{ record.endDate || '无' }}</div>
                                </template>
                                <template v-else>
                                    <a-date-picker v-model="record.endDate" format="YYYY-MM-DD" :disabled="editType === 'change' && changeType.includes(3) && isStarted(record.startDate, record.endDate)"
                                        @change="(date: string) => handleEndDateChange(record, date)" :disabled-date="disabledDate"/>
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column title="备注" :width="150" align="center" ellipsis tooltip>
                            <template #cell="{ record }">
                                <template v-if="isDetailMode">
                                    <div class="detail-text">{{ record.remark }}</div>
                                </template>
                                <template v-else>
                                    <a-input v-model="record.remark" maxlength="50" :disabled="editType === 'change' && changeType.includes(3) && isStarted(record.startDate, record.endDate)" />
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column v-if="!isDetailMode" title="操作" fixed="right" :width="80" align="center">
                            <template #cell="{ record }">
                                <a-button type="text" status="danger" @click="handleDelete(record)" :disabled="editType === 'change' && changeType.includes(3) && isStarted(record.startDate, record.endDate)">
                                    删除
                                </a-button>
                            </template>
                        </a-table-column>
                    </template>
                </a-table>
            </a-space>
        </div>
    </section>
</template>

<script lang="ts" setup>
/**
 * @description: 优惠条款
 * - 可以通过"添加优惠条款"按钮手动添加条款
 * - 可以通过"免首月"/"免尾月"按钮快速添加对应的优惠条款
 * - 可以通过以下方式设置免租期：
 * - 设置开始日期和结束日期，自动计算免租时长
 * - 设置开始日期和免租时长，自动计算结束日期
 * - 可以通过删除按钮移除不需要的优惠条款
 */

import { ref, toRef, computed, onMounted, watch } from 'vue'
import dayjs from 'dayjs'
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract/index'
import { ContractFeeDTO } from '@/api/contract'
import { Message } from '@arco-design/web-vue'

const contractStore = useContractStore()
const contractData = toRef(contractStore, 'contractData')

/**
 * @description 编辑类型
 * 1. 续签
 *    - 清空优惠条款
 */
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || (editType.value === 'change' && !changeType.value.includes(3)) || editType.value === 'updateBank' || editType.value === 'updateContact' || editType.value === 'updateSignMethod' || editType.value === 'updateRentNo')

// 判断优惠条款是否已经开始，如果已经开始，则不能修改
const isStarted = (startDate: string, endDate?: string) => {
    if (!startDate) return false
    
    // 在函数开头统一转换所有日期对象
    const startDateObj = dayjs(startDate)
    const endDateObj = endDate ? dayjs(endDate) : null
    const today = dayjs()
    const changeDate = contractData.value.changeDate ? dayjs(contractData.value.changeDate) : null
    
    // 如果传入的开始日期在今天之前，不能修改
    if (startDateObj.isBefore(today, 'day')) {
        return true
    }
    
    // 如果有变更执行日期和结束日期，检查变更执行日期是否在优惠条款期间内
    if (changeDate && endDateObj) {
        // 如果变更执行日期在优惠条款期间内（大于等于开始日期且小于等于结束日期），不能修改
        if ((changeDate.isAfter(startDateObj, 'day') || changeDate.isSame(startDateObj, 'day')) && 
            (changeDate.isBefore(endDateObj, 'day') || changeDate.isSame(endDateObj, 'day'))) {
            return true
        }
    }
    
    // 特殊处理：如果开始日期是今天且等于变更执行日期，可以修改
    if (startDateObj.isSame(today, 'day') && changeDate && startDateObj.isSame(changeDate, 'day')) {
        return false
    }
    
    // 如果传入的开始日期是今天，不能修改（因为开始时间不能选择今天以及之前的日期）
    if (startDateObj.isSame(today, 'day')) {
        return true
    }
    
    return false
}

// 添加优惠条款
const handleAdd = () => {
    contractData.value.fees.push({
        freeType: contractData.value.contractPurpose === 10 ? 2 : 0, // 宿舍合同默认为合同免租(2)，其他为装修免租(0)
        startDate: '',
        freeRentMonth: undefined,
        freeRentDay: undefined,
        endDate: '',
        remark: ''
    })
}

// 计算两个日期之间的月数和天数
const calculateDuration = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return { months: 0, days: 0 }

    const start = dayjs(startDate)
    const end = dayjs(endDate)
    const months = end.diff(start, 'month')
    const remainingDays = end.diff(start.add(months, 'month'), 'day')

    return {
        months,
        days: remainingDays
    }
}

// 根据开始日期和时长计算结束日期
const calculateEndDate = (startDate: string, months: number, days: number) => {
    if (!startDate) return ''
    // 修正：先加上月数和天数，然后减去1天，确保期间计算正确
    return dayjs(startDate).add(months, 'month').add(days, 'day').subtract(1, 'day').format('YYYY-MM-DD')
}

// 开始日期变化
const handleStartDateChange = (record: ContractFeeDTO, date: string) => {
    if (date && record.endDate) {
        const duration = calculateDuration(date, record.endDate)
        record.freeRentMonth = duration.months
        record.freeRentDay = duration.days
    } else if (date && !!record.freeRentMonth && !!record.freeRentDay) {
        record.endDate = calculateEndDate(date, record.freeRentMonth || 0, record.freeRentDay || 0)
    }

    // 校验免租期区间
    validateFreeRentPeriod(record)
}

// 结束日期变化
const handleEndDateChange = (record: ContractFeeDTO, date: string) => {
    if (date && record.startDate) {
        const duration = calculateDuration(record.startDate, date)
        record.freeRentMonth = duration.months
        record.freeRentDay = duration.days
    }

    // 校验免租期区间
    validateFreeRentPeriod(record)
}

// 免租月数变化
const handleFreeMonthsChange = (record: ContractFeeDTO, value: number | null) => {
    // 当value为null时，默认设置为0
    const months = value ?? 0
    record.freeRentMonth = months
    
    // 当免租月数有值但免租天数没有值时，默认给免租天数补0
    if (months > 0 && (record.freeRentDay === null || record.freeRentDay === undefined)) {
        record.freeRentDay = 0
    }

    if (record.startDate) {
        const freeRentDay = record.freeRentDay || 0
        record.endDate = calculateEndDate(record.startDate, months, freeRentDay)

        // 校验免租期区间
        validateFreeRentPeriod(record)
    }
}

// 免租天数变化
const handleFreeDaysChange = (record: ContractFeeDTO, value: number | null) => {
    // 当value为null时，默认设置为0
    const days = value ?? 0
    record.freeRentDay = days

    if (record.startDate) {
        const freeRentMonth = record.freeRentMonth || 0
        record.endDate = calculateEndDate(record.startDate, freeRentMonth, days)

        // 校验免租期区间
        validateFreeRentPeriod(record)
    }
}

// 校验免租期区间
const validateFreeRentPeriod = (currentRecord: ContractFeeDTO) => {
    if (!currentRecord.startDate || !currentRecord.endDate) {
        return
    }

    const currentStart = dayjs(currentRecord.startDate)
    const currentEnd = dayjs(currentRecord.endDate)
    const contractStart = dayjs(contractData.value.startDate)
    const contractEnd = dayjs(contractData.value.endDate)

    // 校验是否超出合同租期
    if (currentStart.isBefore(contractStart) || currentEnd.isAfter(contractEnd)) {
        Message.error('免租期不能超出合同租期范围')
        // 清空错误数据
        currentRecord.startDate = ''
        currentRecord.endDate = ''
        currentRecord.freeRentMonth = undefined
        currentRecord.freeRentDay = undefined
        return
    }

    // 校验是否与其他免租期重叠
    const otherRecords = contractData.value.fees.filter(item => item !== currentRecord)

    for (const otherRecord of otherRecords) {
        if (!otherRecord.startDate || !otherRecord.endDate) {
            continue
        }

        const otherStart = dayjs(otherRecord.startDate)
        const otherEnd = dayjs(otherRecord.endDate)

        // 检查区间是否重叠
        // 两个区间重叠的条件：当前区间开始时间 < 其他区间结束时间 && 当前区间结束时间 > 其他区间开始时间
        if ((currentStart.isBefore(otherEnd) || currentStart.isSame(otherEnd)) &&
            (currentEnd.isAfter(otherStart) || currentEnd.isSame(otherStart))) {
            Message.error('免租期区间不能重叠')
            // 清空错误数据
            currentRecord.startDate = ''
            currentRecord.endDate = ''
            currentRecord.freeRentMonth = undefined
            currentRecord.freeRentDay = undefined
            return
        }
    }
}

// 免首月
const handleFirstMonth = () => {
    if (!contractData.value.startDate) {
        Message.warning('请先设置合同开始日期')
        return
    }
    
    let startDate = contractData.value.startDate
    
    contractData.value.fees.push({
        freeType: contractData.value.contractPurpose === 10 ? 2 : 0, // 宿舍合同默认为合同免租(2)，其他为装修免租(0)
        startDate: startDate,
        freeRentMonth: 1,
        freeRentDay: 0,
        endDate: dayjs(startDate).add(1, 'month').subtract(1, 'day').format('YYYY-MM-DD'),
        remark: ''
    })
}

// 免尾月
const handleLastMonth = () => {
    if (!contractData.value.endDate) {
        Message.warning('请先设置合同结束日期')
        return
    }
    let startDate = ''
    if(contractData){
        if(contractData.value.rentTicketPeriod===1){
            startDate = dayjs(contractData.value.endDate).subtract(1, 'month').add(1, 'day').format('YYYY-MM-DD')
        }else{
            // 尾月免租，自然月，应该是该自然月的1号
            startDate = dayjs(contractData.value.endDate).startOf('month').format('YYYY-MM-DD')
        }
        
        // 如果是费用条款变更，且计算出的开始日期在明天之前，则使用明天作为开始日期
        if (editType.value === 'change' && changeType.value.includes(3)) {
            const tomorrow = dayjs().add(1, 'day').format('YYYY-MM-DD')
            if (dayjs(startDate).isBefore(dayjs(tomorrow))) {
                startDate = tomorrow
            }
        }
        
        contractData.value.fees.push({
            freeType: contractData.value.contractPurpose === 10 ? 2 : 0, // 宿舍合同默认为合同免租(2)，其他为装修免租(0)
            startDate: startDate,
            freeRentMonth: 1,
            freeRentDay: 0,
            endDate: contractData.value.endDate,
            remark: ''
        })
    }
}

// 删除优惠条款
const handleDelete = (record: ContractFeeDTO) => {
    const index = contractData.value.fees.findIndex(item => item.id === record.id)
    if (index !== -1) {
        contractData.value.fees.splice(index, 1)
    }
}

// 验证所有优惠条款必填字段
const validateTerms = () => {
    if (!contractData.value.fees.length) return true

    // 首先检查合同日期是否设置
    if (!contractData.value.startDate || !contractData.value.endDate) {
        Message.error('请先设置合同开始日期和结束日期')
        return false
    }

    console.log(contractData.value.fees);
    for (const fee of contractData.value.fees) {
        // 当免租月数有值但免租天数没有值时，默认给免租天数补0
        if ((fee.freeRentMonth !== null && fee.freeRentMonth !== undefined && fee.freeRentMonth > 0) && 
            (fee.freeRentDay === null || fee.freeRentDay === undefined)) {
            fee.freeRentDay = 0
        }
        
        if (
            fee.freeType === null ||
            fee.freeType === undefined ||
            !fee.startDate ||
            !fee.endDate
        ) {
            return false
        }

        // 再次校验所有免租期区间
        const feeStart = dayjs(fee.startDate)
        const feeEnd = dayjs(fee.endDate)
        const contractStart = dayjs(contractData.value.startDate)
        const contractEnd = dayjs(contractData.value.endDate)

        // 检查是否超出合同租期
        if (feeStart.isBefore(contractStart) || feeEnd.isAfter(contractEnd)) {
            Message.error('存在免租期超出合同租期范围')
            return false
        }

        // 检查是否有重叠
        const otherFees = contractData.value.fees.filter(item => item !== fee)
        for (const otherFee of otherFees) {
            if (!otherFee.startDate || !otherFee.endDate) continue

            const otherStart = dayjs(otherFee.startDate)
            const otherEnd = dayjs(otherFee.endDate)

            if ((feeStart.isBefore(otherEnd) || feeStart.isSame(otherEnd)) &&
                (feeEnd.isAfter(otherStart) || feeEnd.isSame(otherStart))) {
                Message.error('存在免租期区间重叠')
                return false
            }
        }
    }
    return true
}

const validate = async () => {
    return new Promise((resolve, reject) => {
        if (validateTerms()) {
            resolve(true)
        } else {
            reject()
        }
    })
}

defineExpose({
    validate
})

// 设置合同变更-费用及价格变更的时候 今天及今天之前的时间不可选
const disabledDate = (date: Date) => {
    // 只有在合同变更且包含费用条款变更时，才禁用今天及今天之前的日期
    if (editType.value === 'change' && changeType.value.includes(3)) {
        return dayjs(date).isBefore(dayjs(), 'day') || dayjs(date).isSame(dayjs(), 'day')
    }
    return false
}

// 获取优惠条款类型标签（兼容历史数据）
const getDiscountTypeLabel = (freeType: number) => {
    // 当合同用途为宿舍时，历史数据中的装修免租和经营免租都显示为合同免租
    if (contractData.value.contractPurpose === 10) {
        return '合同免租'
    }

    // 其他合同用途按原逻辑显示
    switch (freeType) {
        case 0:
            return '装修免租'
        case 1:
            return '经营免租'
        case 2:
            return '合同免租'
        default:
            return '合同免租' // 默认显示合同免租
    }
}

// 数据初始化时的历史数据兼容处理
const handleHistoryDataCompatibility = () => {
    if (contractData.value.contractPurpose === 10 && contractData.value.fees?.length) {
        contractData.value.fees.forEach(fee => {
            // 将历史数据中的装修免租和经营免租统一转换为合同免租
            if (fee.freeType === 0 || fee.freeType === 1) {
                fee.freeType = 2 // 转换为合同免租
            }
        })
    }
}

// 在组件挂载时执行历史数据兼容处理
onMounted(() => {
    handleHistoryDataCompatibility()
})

// 监听合同用途变化，处理数据兼容性
watch(() => contractData.value.contractPurpose, (newPurpose) => {
    if (newPurpose === 10) {
        handleHistoryDataCompatibility()
    }
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

+.detail-text {
    padding: 4px 0;
    color: #333;
}
</style>