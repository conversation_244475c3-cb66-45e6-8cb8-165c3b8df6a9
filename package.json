{"name": "wy", "description": "wy", "version": "1.0.0", "private": true, "author": "zx", "license": "MIT", "scripts": {"dev20": "source ~/.nvm/nvm.sh && nvm use v20.9.0 && vite --config ./config/vite.config.dev.ts", "dev": "vite --config ./config/vite.config.dev.ts", "build:dev": "node --max-old-space-size=8192 node_modules/vite/bin/vite.js build --config ./config/vite.config.prod.ts", "build:uat": "node --max-old-space-size=8192 node_modules/vite/bin/vite.js build --mode uat --config ./config/vite.config.uat.ts", "build:prod": "node --max-old-space-size=8192 node_modules/vite/bin/vite.js build --mode production --config ./config/vite.config.prod.ts", "build:notsc": "node --max-old-space-size=8192 node_modules/vite/bin/vite.js build --config ./config/vite.config.prod.ts", "build20": "source ~/.nvm/nvm.sh && nvm use v20.9.0 && node --max-old-space-size=8192 node_modules/vite/bin/vite.js build --mode development --config ./config/vite.config.prod.ts", "build": "node --max-old-space-size=8192 node_modules/vite/bin/vite.js build --mode development --config ./config/vite.config.prod.ts", "build:fast": "node --max-old-space-size=8192 --max-semi-space-size=1024 node_modules/vite/bin/vite.js build --config ./config/vite.config.fast.ts", "build:uat:fast": "node --max-old-space-size=8192 --max-semi-space-size=1024 node_modules/vite/bin/vite.js build --mode uat --config ./config/vite.config.uat.ts", "build:turbo": "node --max-old-space-size=12288 --max-semi-space-size=2048 --optimize-for-size node_modules/vite/bin/vite.js build --config ./config/vite.config.fast.ts", "build:win": "node --max-old-space-size=8192 --expose-gc --max-semi-space-size=1024 --optimize-for-size --memory-reducer --gc-interval=100 --incremental-marking --concurrent-marking node_modules/vite/bin/vite.js build --config ./config/vite.config.win.ts", "build:win:fast": "node --max-old-space-size=12288 --expose-gc --max-semi-space-size=1536 --optimize-for-size --memory-reducer --gc-interval=100 --incremental-marking --concurrent-marking node_modules/vite/bin/vite.js build --config ./config/vite.config.win.ts", "build:win:turbo": "node --max-old-space-size=16384 --expose-gc --max-semi-space-size=2048 --optimize-for-size --memory-reducer --gc-interval=100 --incremental-marking --concurrent-marking node_modules/vite/bin/vite.js build --config ./config/vite.config.win.ts", "upload": "node upload.server.js", "upload:uat": "node upload.server.uat.js", "upload:prod": "node upload.server.prod.js", "buildAndupload:prod": "npm run build:prod && npm run upload:prod", "buildAndupload:uat": "npm run build:uat && npm run upload:uat", "buildAndupload20": "npm run build20 && npm run upload", "buildAndupload": "npm run build && npm run upload", "buildAndupload:fast": "npm run build:fast && npm run upload", "buildAndupload:uat:fast": "npm run build:uat:fast && npm run upload:uat", "benchmark": "node scripts/benchmark.js", "setup:win": "node scripts/setup-windows.js", "diagnose:memory": "node scripts/memory-diagnostics.js", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-staged": "npx lint-staged", "prepare": "husky install"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@arco-design/theme-line": "^0.0.3", "@arco-design/web-vue": "^2.57.0", "@vueuse/core": "^9.3.0", "axios": "^1.9.0", "dayjs": "^1.11.5", "docx-preview": "0.3.5", "echarts": "^5.4.0", "express": "^5.1.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.23", "pinia-plugin-persistedstate": "^3.2.1", "qrcode-generator": "^1.4.4", "query-string": "^8.0.3", "sortablejs": "^1.15.0", "ssh2-sftp-client": "^12.0.0", "vue": "^3.2.40", "vue-baidu-map-3x": "^1.0.40", "vue-echarts": "^6.2.3", "vue-i18n": "^9.2.2", "vue-router": "^4.0.14", "ws": "^8.14.0"}, "devDependencies": {"@arco-plugins/vite-vue": "^1.4.5", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@openapitools/openapi-generator-cli": "^2.20.0", "@types/lodash": "^4.14.186", "@types/mockjs": "^1.0.7", "@types/node": "24.0.10", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.0", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "^5.40.0", "@typescript-eslint/parser": "^5.40.0", "@vitejs/plugin-vue": "^3.1.2", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/babel-plugin-jsx": "^1.1.1", "consola": "^2.15.3", "cross-env": "^7.0.3", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.33.0", "husky": "^8.0.1", "less": "^4.1.3", "lint-staged": "^13.0.3", "mockjs": "^1.1.0", "openapi-typescript-codegen": "^0.29.0", "postcss-html": "^1.5.0", "prettier": "^2.8.8", "rollup": "^3.9.1", "rollup-plugin-visualizer": "^5.8.2", "stylelint": "^14.13.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "terser": "^5.43.1", "typescript": "^4.8.4", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^0.24.1", "uuid": "11.1.0", "vite": "^3.2.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^3.6.0", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^1.0.14"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}}