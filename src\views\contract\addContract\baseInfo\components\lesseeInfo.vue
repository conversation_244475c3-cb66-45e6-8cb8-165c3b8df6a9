<template>
    <section>
        <sectionTitle title="承租人信息"></sectionTitle>
        <div class="content">
            <a-form ref="formRef" :model="contractData.customer" :rules="isDetailMode ? {} : rules" auto-label-width
                :disabled="(editType === 'change' && !changeType.includes(1)) || isDetailMode" layout="vertical">
                <a-form-item label="承租类型" field="customerType">
                    <a-radio-group :disabled="disabledFields.includes('customerType')"
                        v-model="contractData.customer.customerType" @change="handleCustomerTypeChange">
                        <a-radio :value="1">个人</a-radio>
                        <a-radio :value="2">企业</a-radio>
                    </a-radio-group>
                </a-form-item>
                <template v-if="contractData.customer.customerType === 2">
                    <a-form-item label="承租人" field="customerName">
                        <a-select :disabled="disabledFields.includes('customerName')"
                            v-model="contractData.customer.customerName" :loading="searchLoading" placeholder="请选择承租人"
                            allow-search @search="(value: string) => handleFieldSearch(value, 'customerName')"
                            @change="(value: string) => handleFieldChange(value, 'customerName')"
                            :filter-option="false">
                            <a-option v-for="item of fieldOptions.customerName" :key="item.id"
                                :value="getOriginalCustomerName(item)" :label="getOriginalCustomerName(item)">{{
                                    formatCustomerDisplayName(item) }}</a-option>
                        </a-select>
                        <a-button v-if="!disabledFields.includes('customerName')" type="primary"
                            style="margin-left: 8px;" @click="handleAddCustomer">新增</a-button>
                    </a-form-item>
                    <a-form-item label="统一社会信用代码" field="creditCode">
                        <a-select :disabled="disabledFields.includes('creditCode')"
                            v-model="contractData.customer.creditCode" placeholder="请输入统一社会信用代码"
                            :loading="searchLoading" allow-search
                            @search="(value: string) => handleFieldSearch(value, 'creditCode')"
                            @change="(value: string) => handleFieldChange(value, 'creditCode')" :filter-option="false">
                            <a-option v-for="item of fieldOptions.creditCode" :key="item.id" :value="item.customerName">
                                {{ item.customerName }}
                            </a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="委托代理人" field="contactName">
                        <a-select :disabled="disabledFields.includes('contactName')"
                            v-model="contractData.customer.contactName" placeholder="请输入委托代理人" allow-create
                            @change="changeContact">
                            <a-option v-for="item of contactList" :key="item.id" :value="item.name">{{ item.name
                            }}</a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="委托代理人手机号" field="contactPhone">
                        <a-select :disabled="disabledFields.includes('contactPhone')" maxlength="11"
                            v-model="contractData.customer.contactPhone" placeholder="请输入委托代理人手机号" allow-create
                            @change="changeContactPhone">
                            <a-option v-for="item of contactList" :key="item.id" :value="item.phone">{{ item.phone
                            }}</a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="委托代理人身份证" field="contactIdNumber">
                        <a-select :disabled="disabledFields.includes('contactIdNumber')"
                            v-model="contractData.customer.contactIdNumber" placeholder="请输入委托代理人身份证" allow-create
                            @change="changeContactIdNumber">
                            <a-option v-for="item of contactList" :key="item.id" :value="item.idNumber">{{
                                item.idNumber
                            }}</a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="法人" field="legalName">
                        <a-input readonly :disabled="disabledFields.includes('legalName')"
                            v-model="contractData.customer.legalName" placeholder="请输入法人" />
                    </a-form-item>
                    <a-form-item label="法人手机号">
                        <a-input :disabled="disabledFields.includes('phone')" v-model="contractData.customer.phone"
                            placeholder="请输入法人手机号" maxlength="11" />
                    </a-form-item>
                    <a-form-item label="法人身份证">
                        <a-input :disabled="disabledFields.includes('idNumber')"
                            v-model="contractData.customer.idNumber" placeholder="请输入法人身份证号码" />
                    </a-form-item>
                    <a-form-item label="付款银行名称" field="paymentBank">
                        <a-select :disabled="disabledFields.includes('paymentBank')"
                            v-model="contractData.customer.paymentBank" placeholder="请选择付款银行名称" allow-search
                            allow-create>
                            <a-option v-for="item of bankAccountList" :key="item.id" :value="item.bankName">{{
                                item.bankName
                            }}</a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="付款银行账号" field="paymentAccount">
                        <a-select :disabled="disabledFields.includes('paymentAccount')"
                            v-model="contractData.customer.paymentAccount" placeholder="请选择付款银行账号" allow-search
                            allow-create>
                            <a-option v-for="item of bankAccountList" :key="item.id" :value="item.accountNumber">{{
                                item.accountNumber
                            }}</a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="地址" field="address">
                        <a-input :disabled="disabledFields.includes('address')" v-model="contractData.customer.address"
                            placeholder="请输入" />
                    </a-form-item>
                </template>
                <template v-else>
                    <a-form-item label="承租人" field="customerName">
                        <a-select :disabled="disabledFields.includes('customerName')"
                            v-model="contractData.customer.customerName" :loading="searchLoading" placeholder="请选择承租人"
                            allow-search @search="(value: string) => handleFieldSearch(value, 'customerName')"
                            @change="(value: string) => handleFieldChange(value, 'customerName')"
                            :filter-option="false">
                            <a-option v-for="item of fieldOptions.customerName" :key="item.id"
                                :value="getOriginalCustomerName(item)" :label="getOriginalCustomerName(item)">{{
                                    formatCustomerDisplayName(item) }}</a-option>
                        </a-select>
                        <a-button type="primary" style="margin-left: 8px;" @click="handleAddCustomer">新增</a-button>
                    </a-form-item>
                    <a-form-item label="手机号" field="phone">
                        <a-select :disabled="disabledFields.includes('phone')" v-model="contractData.customer.phone"
                            placeholder="请输入手机号" :loading="searchLoading" maxlength="11" allow-create
                            @change="(value: string) => handleFieldChange(value, 'phone')" :filter-option="false">
                            <a-option v-for="item of fieldOptions.phone" :key="item.id" :value="item.customerName">
                                {{ item.customerName }}
                            </a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="证件类型" field="idType">
                        <a-select :disabled="disabledFields.includes('idType')" v-model="contractData.customer.idType"
                            placeholder="请选择证件类型">
                            <a-option v-for="item in idTypeOptions" :key="item.value" :value="item.value">
                                {{ item.label }}
                            </a-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="证件号码" field="idNumber">
                        <a-input :disabled="disabledFields.includes('idNumber')"
                            v-model="contractData.customer.idNumber" placeholder="请输入证件号码" />
                    </a-form-item>
                    <a-form-item label="通讯地址" field="address">
                        <a-input :disabled="disabledFields.includes('address')" v-model="contractData.customer.address"
                            placeholder="请输入通讯地址" />
                    </a-form-item>
                </template>
            </a-form>
        </div>

        <!-- 新增/编辑客户抽屉 -->
        <customer-drawer ref="customerDrawerRef" />
    </section>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import sectionTitle from '@/components/sectionTitle/index.vue';
import { useContractStore } from '@/store/modules/contract/index'
import customerApi, { type CustomerQueryDTO, type CustomerVo, type CustomerSimpleVo } from '@/api/customer'
import { FormInstance, Message } from '@arco-design/web-vue';
import router from '@/router';
import CustomerDrawer from '@/views/customer/components/CustomerDrawer.vue'

// 注入响应式合同数据
const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

/**
 * @description 编辑类型
 * 1. 续签
 *    - 承租类型、承租人、统一社会信用代码、法人、法人手机号、法人身份证不可修改
 * 2. 详情
 *    - 所有字段不可修改，且展示成文本
 * 3. 更新付款账号
 *    - 除了付款账号，别的所有字段不可修改，且展示成文本
 */
const editType = computed(() => contractStore.editType)
const changeType = computed(() => contractStore.changeType) // 变更类型
const disabledFields = computed(() => {
    if (editType.value === 'renew') {
        return ['customerType', 'customerName', 'creditCode', 'legalName', 'phone', 'idNumber', 'idType']
    }
    if (editType.value === 'updateBank') {
        return ['customerType', 'customerName', 'contactName', 'contactPhone', 'contactIdNumber', 'creditCode', 'legalName', 'phone', 'idNumber', 'idType', 'paymentBank', 'address']
    }
    if (editType.value === 'updateContact') {
        return ['customerType', 'customerName', 'creditCode', 'legalName', 'phone', 'idNumber', 'idType', 'paymentBank', 'address', 'paymentAccount']
    }
    if (editType.value === 'updateRentNo') {
        return ['customerType', 'customerName', 'idType', 'idNumber', 'address']
    }
    return []
})

// 判断是否为详情模式，展示文本
const isDetailMode = computed(() => editType.value === 'detail' || editType.value === 'changeDetail' || editType.value ==='updateSignMethod')
const formItemMargin = computed(() => isDetailMode.value ? '16px' : '16px')

/**
 * 1. 承租类型为个人时，承租人、手机号、证件类型、证件号码为必填
 * 2. 承租类型为企业时，统一社会信用代码、委托代理人、委托代理人手机号、委托代理人身份证、法人、法人手机号、法人身份证、付款银行账号为必填
 */
const rules = {
    customerType: [{ required: true, message: '请选择承租类型' }],
    customerName: [{ required: true, message: '请选择承租人' }],
    creditCode: [{ required: true, message: '请输入统一社会信用代码' }],
    contactName: [{ required: true, message: '请选择委托代理人' }],
    contactPhone: [{ required: true, message: '请输入委托代理人手机号' }],
    contactIdNumber: [{ required: true, message: '请输入委托代理人身份证号' }],
    legalName: [{ required: true, message: '请选择法人' }],
    // paymentAccount: [{ required: true, message: '请选择付款银行账号' }],
    // paymentBank: [{ required: true, message: '请选择付款银行名称' }],
    phone: [{ required: true, message: '请输入手机号' }],
    idType: [{ required: true, message: '请选择证件类型' }],
    idNumber: [{ required: true, message: '请输入证件号码' }],
}

const handleCustomerTypeChange = (value: number) => {
    fieldOptions.value = {
        customerName: [],
        creditCode: [],
        phone: []
    };
    if (value === 2) {
        // 企业类型
        // 重置个人字段值
        contractData.value.customer.customerName = ''
        contractData.value.customer.phone = ''
        contractData.value.customer.idType = ''
        contractData.value.customer.idNumber = ''
        contractData.value.customer.address = ''
    } else if (value === 1) {
        // 个人类型
        // 重置企业字段值
        contractData.value.customer.customerName = ''
        contractData.value.customer.creditCode = ''
        contractData.value.customer.contactName = ''
        contractData.value.customer.contactPhone = ''
        contractData.value.customer.contactIdNumber = ''
        contractData.value.customer.legalName = ''
        contractData.value.customer.phone = ''
        contractData.value.customer.idType = '1'
        contractData.value.customer.idNumber = ''
        contractData.value.customer.paymentBank = ''
        contractData.value.customer.paymentAccount = ''
        contractData.value.customer.address = ''
        contractData.value.customer.invoiceTitle = ''
        contractData.value.customer.invoiceTaxNumber = ''
        contractData.value.customer.invoiceAddress = '';
        contractData.value.customer.invoicePhone = '';
        contractData.value.customer.invoiceAccountNumber = '';
        contractData.value.customer.invoiceBankName = '';
    }
}

// 通用搜索相关变量
const fieldOptions = ref<Record<string, CustomerSimpleVo[]>>({
    customerName: [],
    creditCode: [],
    phone: []
});
const searchLoading = ref(false);

/**
 * 通用字段搜索方法
 * @param value 搜索关键词
 * @param field 搜索字段名称
 */
const handleFieldSearch = async (value: string, field: string) => {
    if (!value) {
        fieldOptions.value[field] = [];
        return;
    }

    searchLoading.value = true;

    try {
        // 设置查询参数
        const searchParams: CustomerQueryDTO = {
            pageNum: 1,
            pageSize: 50,
            projectId: contractData.value.projectId,
            customerType: contractData.value.customer.customerType,
        };
        if (field === 'customerName') {
            searchParams.customerName = value
        } else if (field === 'phone') {
            searchParams.phone = value
        } else if (field === 'creditCode') {
            searchParams.creditCode = value
        }

        // 调用客户列表查询接口-合同
        const { data } = await customerApi.getCustomerListForContract(searchParams);

        if (data) {
            // 如果是搜索承租人，需要获取每个客户的详细信息来获取手机号
            if (field === 'customerName') {
                const customersWithPhone = await Promise.all(
                    data.map(async (customer) => {
                        try {
                            const { data: customerDetail } = await customerApi.getCustomerDetail(customer.id!);
                            return {
                                ...customer,
                                // 保持原始customerName不变，只添加contactPhone字段
                                originalCustomerName: customer.customerName, // 保存原始名称
                                contactPhone: customerDetail?.contactPhone || ''
                            };
                        } catch (error) {
                            console.error(`获取客户${customer.id}详情失败:`, error);
                            return {
                                ...customer,
                                originalCustomerName: customer.customerName, // 保存原始名称
                                contactPhone: ''
                            };
                        }
                    })
                );
                fieldOptions.value[field] = customersWithPhone;
            } else {
                fieldOptions.value[field] = data;
            }
        } else {
            fieldOptions.value[field] = [];
            Message.error(`获取${field}列表失败`);
        }
    } catch (error) {
        console.error(`获取${field}列表失败:`, error);
        Message.error(`获取${field}列表失败`);
        fieldOptions.value[field] = [];
    } finally {
        searchLoading.value = false;
    }
};

/**
 * 通用字段值变更处理
 * @param value 选择的值
 * @param field 字段名称
 */
const contactList = ref<any[]>([])
const bankAccountList = ref<any[]>([])
const handleFieldChange = async (value: string, field: string) => {
    // 查找选中的客户数据
    // 注意：value是纯客户名称，需要通过customerName字段匹配
    const selectedCustomer = fieldOptions.value[field].find(item => {
        // 确保使用原始的customerName进行匹配
        return getOriginalCustomerName(item) === value;
    });

    if (!selectedCustomer || !selectedCustomer.id) return;

    try {
        // 通过客户详情接口获取完整客户信息
        const { data: customerDetail } = await customerApi.getCustomerDetail(selectedCustomer.id);

        if (!customerDetail) {
            Message.error('获取客户详情失败');
            return;
        }

        // 设置客户ID
        contractData.value.customer.customerId = customerDetail.id;

        // 根据客户类型填充不同的字段
        if (contractData.value.customer.customerType === 2) {
            // 企业类型
            contractData.value.customer.customerName = customerDetail.customerName || '';
            contractData.value.customer.creditCode = customerDetail.creditCode || '';

            // 委托代理人信息（取首选委托代理人或第一个委托代理人）

            contactList.value = customerDetail.contactList || []
            const preferredContact = customerDetail.contactList?.find(contact => contact.isPreferred) || customerDetail.contactList?.[0];
            if (preferredContact) {
                contractData.value.customer.contactName = preferredContact.name || '';
                contractData.value.customer.contactPhone = preferredContact.phone || '';
                contractData.value.customer.contactIdNumber = preferredContact.idNumber || '';
            }

            // 法人信息
            contractData.value.customer.legalName = customerDetail.legalName || '';
            contractData.value.customer.phone = customerDetail.contactPhone || '';
            contractData.value.customer.idNumber = customerDetail.idNumber || '';

            // 付款银行账号（取第一个银行账号）
            bankAccountList.value = customerDetail.bankAccountList || []
            const firstBankAccount = customerDetail.bankAccountList?.[0];
            if (firstBankAccount) {
                contractData.value.customer.paymentAccount = firstBankAccount.accountNumber || '';
                contractData.value.customer.paymentBank = firstBankAccount.bankName || '';
            }

            // 地址
            contractData.value.customer.address = customerDetail.contactAddress || '';

            // 开票信息（取第一个开票信息）
            const firstInvoice = customerDetail.invoiceList?.[0];
            if (firstInvoice) {
                contractData.value.customer.invoiceTitle = firstInvoice.title || customerDetail.customerName || '';
                contractData.value.customer.invoiceTaxNumber = firstInvoice.taxNumber || customerDetail.creditCode || '';
                // 单位地址
                contractData.value.customer.invoiceAddress = firstInvoice.address || '';
                // 单位电话
                contractData.value.customer.invoicePhone = firstInvoice.phone || '';
                // 单位银行账号
                contractData.value.customer.invoiceAccountNumber = firstInvoice.accountNumber || '';
                // 单位开户行
                contractData.value.customer.invoiceBankName = firstInvoice.bankName || '';
            } else {
                contractData.value.customer.invoiceTitle = customerDetail.customerName || '';
                contractData.value.customer.invoiceTaxNumber = customerDetail.creditCode || '';
                contractData.value.customer.invoiceAddress = '';
                contractData.value.customer.invoicePhone = '';
                contractData.value.customer.invoiceAccountNumber = '';
                contractData.value.customer.invoiceBankName = '';
            }
        } else {
            // 个人类型
            contractData.value.customer.customerName = customerDetail.customerName || '';
            contractData.value.customer.phone = customerDetail.contactPhone || '';
            contractData.value.customer.idType = customerDetail.idType || '';
            contractData.value.customer.idNumber = customerDetail.idNumber || '';
            contractData.value.customer.address = customerDetail.contactAddress || '';
        }

        // 担保人信息（取第一个担保人）
        const firstGuarantor = customerDetail.guarantorList?.[0];
        if (firstGuarantor) {
            contractData.value.customer.guarantorName = firstGuarantor.name || '';
            contractData.value.customer.guarantorPhone = firstGuarantor.phone || '';
            contractData.value.customer.guarantorIdType = firstGuarantor.idType?.toString() || '';
            contractData.value.customer.guarantorIdNumber = firstGuarantor.idNumber || '';
            contractData.value.customer.guarantorAddress = firstGuarantor.address || '';
        } else {
            contractData.value.customer.guarantorName = '';
            contractData.value.customer.guarantorPhone = '';
            contractData.value.customer.guarantorIdType = '';
            contractData.value.customer.guarantorIdNumber = '';
            contractData.value.customer.guarantorAddress = '';
        }

        // 触发表单验证
        formRef.value?.validate();
    } catch (error) {
        console.error('获取客户详情失败:', error);
        Message.error('获取客户详情失败');
    }
};

const changeContact = (val: string) => {
    const contactCustomer = contactList.value.find(item => item.name === val)
    if (contactCustomer) {
        contractData.value.customer.contactName = contactCustomer.name || '';
        contractData.value.customer.contactPhone = contactCustomer.phone || '';
        contractData.value.customer.contactIdNumber = contactCustomer.idNumber || '';
    }

}

const changeContactPhone = (val: string) => {
    const contactCustomer = contactList.value.find(item => item.phone === val)
    if (contactCustomer) {
        contractData.value.customer.contactName = contactCustomer.name || '';
        contractData.value.customer.contactPhone = contactCustomer.phone || '';
        contractData.value.customer.contactIdNumber = contactCustomer.idNumber || '';
    }
}

const changeContactIdNumber = (val: string) => {
    const contactCustomer = contactList.value.find(item => item.idNumber === val)
    if (contactCustomer) {
        contractData.value.customer.contactName = contactCustomer.name || '';
        contractData.value.customer.contactPhone = contactCustomer.phone || '';
        contractData.value.customer.contactIdNumber = contactCustomer.idNumber || '';
    }
}

// 证件类型选项
const idTypeOptions = [
    { label: '身份证', value: '1' },
    { label: '护照', value: '2' },
    { label: '军官证', value: '3' },
    { label: '其他', value: '4' },
    // ... 其他证件类型
]


const formRef = ref<FormInstance>()
const validate = async () => {
    return new Promise(async (resolve, reject) => {
        const errors = await formRef.value.validate()
        if (errors) {
            console.log('承租人信息', errors);
            reject()
        } else {
            resolve(true)
        }
    })
}

/**
 * 获取原始客户名称（用于option的value，确保选择后只显示客户名称）
 * @param customer 客户信息
 * @returns 原始客户名称
 */
const getOriginalCustomerName = (customer: any) => {
    return customer.originalCustomerName || customer.customerName || '';
}

/**
 * 格式化客户显示名称，在客户名称后面加上手机号后四位
 * @param customer 客户信息
 * @returns 格式化后的显示名称
 */
const formatCustomerDisplayName = (customer: any) => {
    if (!customer.customerName) return '';

    // 如果有手机号，显示手机号后四位
    if (customer.contactPhone && customer.contactPhone.length >= 4) {
        const lastFourDigits = customer.contactPhone.slice(-4);
        return `${customer.customerName}（${lastFourDigits}）`;
    }

    // 如果没有手机号，只显示客户名称
    return customer.customerName;
}

const customerDrawerRef = ref()
const handleAddCustomer = () => {
    console.log(customerDrawerRef.value);
    let data = {
        id: contractData.value.projectId,
        name: contractData.value.projectName,
    }
    customerDrawerRef.value?.show('add', data)
}

defineExpose({
    validate
})
</script>

<style lang="less" scoped>
section {
    .content {
        padding: 16px 0;
    }
}

.detail-text {
    padding: 4px 0;
    color: #333;
}

.arco-form-item {
    margin-bottom: v-bind(formItemMargin);
}
</style>