<template>
    <a-modal
        v-model:visible="visible"
        title="增加授权账号"
        width="400px"
        @ok="handleSubmit"
        @cancel="handleCancel"
    >
        <a-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            layout="vertical"
            auto-label-width
        >
            <a-form-item field="brandType" label="品牌" required>
                <a-select v-model="formData.brandType" placeholder="请选择品牌">
                    <a-option :value="1">通通锁</a-option>
                </a-select>
            </a-form-item>
            
            <a-form-item field="username" label="登录账号" required>
                <a-input 
                    v-model="formData.username" 
                    placeholder="请输入登录账号"
                    allow-clear
                />
            </a-form-item>
            
            <a-form-item field="password" label="账号密码" required>
                <a-input-password 
                    v-model="formData.password" 
                    placeholder="请输入账号密码"
                    allow-clear
                />
            </a-form-item>
        </a-form>
        
        <template #footer>
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleSubmit" :loading="loading">确认</a-button>
        </template>
    </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { addTTLockAccount, type TTLockAccountAddDTO } from '@/api/smartLock'

// Props
interface Props {
    visible: boolean
    projectId?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
    'update:visible': [value: boolean]
    'success': []
}>()

// 响应式数据
const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
    brandType: 1, // 默认通通锁
    username: '',
    password: '',
    projectId: ''
})

// 表单验证规则
const rules = {
    brandType: [
        { required: true, message: '请选择品牌' }
    ],
    username: [
        { required: true, message: '请输入登录账号' },
        { minLength: 2, message: '账号长度不能少于2位' }
    ],
    password: [
        { required: true, message: '请输入账号密码' },
        { minLength: 6, message: '密码长度不能少于6位' }
    ]
}



// 提交表单
const handleSubmit = async () => {
    try {
        const errors = await formRef.value.validate()
        if (errors) return
        
        loading.value = true
        
        const submitData: TTLockAccountAddDTO = {
            projectId: formData.projectId,
            brandType: formData.brandType,
            username: formData.username,
            password: formData.password
        }
        
        await addTTLockAccount(submitData)
        emit('success')
        handleCancel()
        
    } catch (error) {
        console.error('添加账号失败:', error)
    } finally {
        loading.value = false
    }
}

// 取消
const handleCancel = () => {
    visible.value = false
    resetForm()
}

// 重置表单
const resetForm = () => {
    formData.brandType = 1
    formData.username = ''
    formData.password = ''
    formRef.value?.resetFields()
}

// 注意：由于去掉了品牌选择功能，这里直接使用默认的通通锁品牌类型
// 如果将来需要支持多品牌，可以在表单中添加品牌选择器

// 获取项目ID - 从父组件的父组件传递
const getProjectId = () => {
    // 这里需要从全局状态或者其他方式获取项目ID
    // 暂时返回空字符串，实际使用时需要根据具体情况调整
    return props.projectId || ''
}

// 监听项目ID变化
watch(() => props.projectId, (newProjectId) => {
    if (newProjectId) {
        formData.projectId = newProjectId
    } else {
        formData.projectId = getProjectId()
    }
}, { immediate: true })

// 监听弹窗显示状态
watch(visible, (newVisible) => {
    if (!newVisible) {
        resetForm()
    }
})
</script>

<style lang="less" scoped>
.arco-form-item {
    margin-bottom: 20px;
}

.arco-form-item-label {
    font-weight: 500;
}
</style>
