type TargetContext = '_self' | '_parent' | '_blank' | '_top';

export const openWindow = (
    url: string,
    opts?: { target?: TargetContext; [key: string]: any }
) => {
    const { target = '_blank', ...others } = opts || {};
    window.open(
        url,
        target,
        Object.entries(others)
            .reduce((preValue: string[], curValue) => {
                const [key, value] = curValue;
                return [...preValue, `${key}=${value}`];
            }, [])
            .join(',')
    );
};

export const regexUrl = new RegExp(
    '^(?!mailto:)(?:(?:http|https|ftp)://)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$',
    'i'
);

/**
 * 构造树型结构数据
 * @param {*} list 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export const listToTree = (
    list: any,
    id = 'id',
    parentId = 'parentId',
    children = 'children'
) => {
    if (!list || !Array.isArray(list) || list.length === 0) {
        return []
    }
    
    let info = list.reduce((map: any, node: any) => {
        map[node[id]] = node;
        node[children] = [];
        return map;
    }, {});
    
    return list.filter((node: any) => {
        // 如果有父级ID且父级存在于数据中，则添加到父级的children中
        if (node[parentId] && info[node[parentId]]) {
            info[node[parentId]].children.push(node);
            return false; // 不作为根节点
        }
        // 如果没有父级ID（parentId为0、null、undefined）或找不到父级，则作为根节点
        return !node[parentId] || !info[node[parentId]];
    });
};

/**
 * 表格时间格式化
 */
export function formatDate(cellValue: string | number | null): string {
    if (cellValue == null || cellValue == '') return '';
    var date = new Date(cellValue);
    var year = date.getFullYear();
    var month =
        date.getMonth() + 1 < 10
            ? '0' + (date.getMonth() + 1)
            : date.getMonth() + 1;
    var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    var minutes =
        date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    var seconds =
        date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    return (
        year +
        '-' +
        month +
        '-' +
        day +
        ' ' +
        hours +
        ':' +
        minutes +
        ':' +
        seconds
    );
}

// 日期格式化
export function parseTime(
    time: string | number | Date | undefined,
    pattern?: string
) {
    if (arguments.length === 0 || !time) {
        return null;
    }
    const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
    let date;
    if (typeof time === 'object') {
        date = time;
    } else {
        if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
            time = parseInt(time);
        } else if (typeof time === 'string') {
            time = time
                .replace(new RegExp(/-/gm), '/')
                .replace('T', ' ')
                .replace(new RegExp(/\.[\d]{3}/gm), '');
        }
        if (typeof time === 'number' && time.toString().length === 10) {
            time = time * 1000;
        }
        date = new Date(time);
    }
    const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay(),
    };
    const time_str = format.replace(
        /{(y|m|d|h|i|s|a)+}/g,
        (result: string, key: string): string => {
            let value = formatObj[key as keyof typeof formatObj];
            // Note: getDay() returns 0 on Sunday
            if (key === 'a') {
                return ['日', '一', '二', '三', '四', '五', '六'][value];
            }
            if (result.length > 0 && value < 10) {
                value = Number(`0${value}`);
            }
            return String(value || 0);
        }
    );
    return time_str;
}

// 回显数据字典
export function selectDictLabel(data: any[], value: string | undefined) {
    if (value === undefined) {
        return '';
    }
    var actions = [];
    Object.keys(data).some((key: any) => {
        if (data[key].value == '' + value) {
            actions.push(data[key].label);
            return true;
        }
    });
    if (actions.length === 0) {
        actions.push(value);
    }
    return actions.join('');
}

// 添加日期范围
export function addDateRange(params: any, dateRange: any[], propName?: string | undefined) {
    let search = params;
    search.params =
        typeof search.params === 'object' &&
        search.params !== null &&
        !Array.isArray(search.params)
            ? search.params
            : {};
    dateRange = Array.isArray(dateRange) ? dateRange : [];
    if (typeof propName === 'undefined') {
        search.params['beginTime'] = dateRange[0];
        search.params['endTime'] = dateRange[1];
    } else {
        search.params['begin' + propName] = dateRange[0];
        search.params['end' + propName] = dateRange[1];
    }
    return search;
}

export default null;
