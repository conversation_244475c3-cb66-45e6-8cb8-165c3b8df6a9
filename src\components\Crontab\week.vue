<template>
    <a-form :model="{}" :label-col-props="{ span: 1 }">
        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="1"> </a-radio>
                <span class="radio-span">周，允许的通配符[, - * ? / L #]</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="2"> </a-radio>
                <span class="radio-span">不指定</span>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="3"> </a-radio>
                <span class="radio-span">周期从</span>
                <a-select v-model:model-value="cycle01" allow-clear style="width: 8rem">
                    <a-option v-for="item in weekList" :key="item.value" :value="item.key" :label="item.value"
                        :disabled="item.key === 7" />
                </a-select>
                <span class="radio-span">-</span>
                <a-select v-model:model-value="cycle02" allow-clear style="width: 8rem">
                    <a-option v-for="item in weekList" :key="item.value" :value="item.key" :label="item.value"
                        :disabled="item.key <= cycle01" />
                </a-select>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="4"> </a-radio>
                <span class="radio-span">第</span>
                <a-input-number v-model:model-value="average01" :min="1" :max="4" />
                <span class="radio-span">周的</span>
                <a-select v-model:model-value="average02" allow-clear style="width: 8rem">
                    <a-option v-for="item in weekList" :key="item.value" :value="item.key" :label="item.value" />
                </a-select>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="5"> </a-radio>
                <span class="radio-span">本月最后一个</span>
                <a-select v-model:model-value="weekday" allow-clear style="width: 8rem">
                    <a-option v-for="item in weekList" :key="item.value" :value="item.key" :label="item.value" />
                </a-select>
            </a-space>
        </a-form-item>

        <a-form-item>
            <a-space>
                <a-radio v-model:model-value="radioValue" :value="6"> </a-radio>
                <span class="radio-span">指定</span>
                <a-select v-model:model-value="checkboxList" placeholder="可多选" multiple :max-tag-count="10" allow-clear
                    style="width: 17.8rem">
                    <a-option v-for="item in weekList" :key="item.value" :value="item.key" :label="item.value" />
                </a-select>
            </a-space>
        </a-form-item>
    </a-form>
</template>

<script lang="ts" setup>
interface WeekOption {
    label: string;
    value: number;
}

import { ref, computed, watch } from 'vue';

interface WeekItem {
    key: number;
    value: string;
}

interface CronProps {
    second: string;
    min: string;
    hour: string;
    day: string;
    month: string;
    week: string;
    year: string;
}

interface Props {
    cron: CronProps;
    check: (value: number, min: number, max: number) => number;
}

const emit = defineEmits(['update'])

const props = withDefaults(defineProps<Props>(), {
    cron: () => ({
        second: "*",
        min: "*",
        hour: "*",
        day: "*",
        month: "*",
        week: "?",
        year: "",
    }),
    check: () => (value: number) => value,
});

const radioValue = ref(2);
const cycle01 = ref(2);
const cycle02 = ref(3);
const average01 = ref(1);
const average02 = ref(2);
const weekday = ref(2);
const checkboxList = ref<number[]>([]);
const checkCopy = ref<number[]>([2]);
const weekList = ref<WeekItem[]>([
    { key: 1, value: '星期日' },
    { key: 2, value: '星期一' },
    { key: 3, value: '星期二' },
    { key: 4, value: '星期三' },
    { key: 5, value: '星期四' },
    { key: 6, value: '星期五' },
    { key: 7, value: '星期六' }
]);

const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, 1, 6);
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, 7);
    return `${cycle01.value}-${cycle02.value}`;
});

const averageTotal = computed(() => {
    average01.value = props.check(average01.value, 1, 4);
    average02.value = props.check(average02.value, 1, 7);
    return `${average02.value}#${average01.value}`;
});

const weekdayTotal = computed(() => {
    weekday.value = props.check(weekday.value, 1, 7);
    return `${weekday.value}L`;
});

const checkboxString = computed(() => {
    return checkboxList.value.join(',');
});

watch(() => props.cron.week, value => changeRadioValue(value));
watch([radioValue, cycleTotal, averageTotal, weekdayTotal, checkboxString], () => onRadioChange());

function changeRadioValue(value: string): void {
    if (value === "*") {
        radioValue.value = 1;
    } else if (value === "?") {
        radioValue.value = 2;
    } else if (value.indexOf("-") > -1) {
        const indexArr = value.split('-');
        cycle01.value = Number(indexArr[0]);
        cycle02.value = Number(indexArr[1]);
        radioValue.value = 3;
    } else if (value.indexOf("#") > -1) {
        const indexArr = value.split('#');
        average01.value = Number(indexArr[1]);
        average02.value = Number(indexArr[0]);
        radioValue.value = 4;
    } else if (value.indexOf("L") > -1) {
        const indexArr = value.split("L");
        weekday.value = Number(indexArr[0]);
        radioValue.value = 5;
    } else {
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))];
        radioValue.value = 6;
    }
}

function onRadioChange(): void {
    if (radioValue.value === 2 && props.cron.day === '?') {
        emit('update', 'day', '*', 'week');
    }
    if (radioValue.value !== 2 && props.cron.day !== '?') {
        emit('update', 'day', '?', 'week');
    }
    switch (radioValue.value) {
        case 1:
            emit('update', 'week', '*', 'week');
            break;
        case 2:
            emit('update', 'week', '?', 'week');
            break;
        case 3:
            emit('update', 'week', cycleTotal.value, 'week');
            break;
        case 4:
            emit('update', 'week', averageTotal.value, 'week');
            break;
        case 5:
            emit('update', 'week', weekdayTotal.value, 'week');
            break;
        case 6:
            if (checkboxList.value.length === 0) {
                checkboxList.value.push(checkCopy.value[0]);
            } else {
                checkCopy.value = checkboxList.value;
            }
            emit('update', 'week', checkboxString.value, 'week');
            break;
    }
}
</script>

<style lang="less" scoped>
.arco-input-number {
    margin: 0 0.2rem;
}

.arco-select {
    margin: 0 0.2rem;
}

.radio-span {
    flex-shrink: 0;
}
</style>