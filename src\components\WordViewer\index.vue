<template>
    <a-modal
        :visible="modelValue"
        :title="title"
        :mask-closable="true"
        :footer="false"
        :width="1200"
        :body-style="{ padding: '16px' }"
        @cancel="handleCancel"
    >
        <div class="word-viewer-container">
            <div v-if="loading" class="loading-container">
                <a-spin tip="文档加载中..." />
            </div>
            <div v-show="!loading" ref="viewerRef" class="viewer-content"></div>
            
            <!-- 工具栏 -->
            <div class="toolbar">
                <a-space>
                    <a-button type="primary" @click="handleDownload">
                        <template #icon>
                            <icon-download />
                        </template>
                        下载文档
                    </a-button>
                    <a-button @click="handlePrint">
                        <template #icon>
                            <icon-printer />
                        </template>
                        打印文档
                    </a-button>
                </a-space>
            </div>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconDownload, IconPrinter } from '@arco-design/web-vue/es/icon'
import { renderAsync } from 'docx-preview'

const props = defineProps<{
    modelValue: boolean
    fileUrl: string
    title?: string
}>()

const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void
    (e: 'cancel'): void
}>()

const loading = ref(true)
const viewerRef = ref<HTMLElement>()

// 处理文件 URL
const getProxyUrl = (url: string) => {
    try {
        // 先对URL中的#号进行编码
        const encodedUrl = url.replace(/#/g, '%23')
        
        const urlObj = new URL(encodedUrl)
        if (urlObj.hostname === '************') {
            // 提取路径中的 /statics 部分
            const path = urlObj.pathname
            if (path.startsWith('/statics')) {
                return path // 返回相对路径，将通过代理访问
            }
        }
    } catch (e) {
        console.warn('URL 解析失败:', e)
    }
    // 如果不是目标服务器或解析失败，也需要对#号进行编码
    return url.replace(/#/g, '%23')
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
    if (newVal && props.fileUrl) {
        loadDocument()
    }
})

// 监听fileUrl变化
watch(() => props.fileUrl, (newVal) => {
    if (props.modelValue && newVal) {
        loadDocument()
    }
})

// 加载文档
const loadDocument = async () => {
    if (!viewerRef.value || !props.fileUrl) return
    
    loading.value = true
    try {
        // 清空容器
        if (viewerRef.value) {
            viewerRef.value.innerHTML = ''
        }
        
        // 获取文档内容
        const proxyUrl = getProxyUrl(props.fileUrl)
        const response = await fetch(proxyUrl)
        const blob = await response.blob()
        
        // 渲染文档
        await renderAsync(blob, viewerRef.value, viewerRef.value, {
            className: 'docx-viewer',
            inWrapper: true,
            useBase64URL: true,
        })
        
        loading.value = false
    } catch (error) {
        console.error('文档加载失败:', error)
        Message.error('文档加载失败')
        loading.value = false
    }
}

// 下载文档
const handleDownload = () => {
    if (!props.fileUrl) {
        Message.warning('文档地址不存在')
        return
    }
    
    // 使用代理 URL 下载
    const proxyUrl = getProxyUrl(props.fileUrl)
    const link = document.createElement('a')
    link.href = proxyUrl
    link.download = props.title || '文档下载.docx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
}

// 打印文档
const handlePrint = () => {
    if (!viewerRef.value) return
    
    const printContent = viewerRef.value.innerHTML
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
        Message.warning('请允许打开新窗口')
        return
    }
    
    // 注入打印样式和内容
    const printHtml = `
        <!DOCTYPE html>
        <html>
            <head>
                <title>打印预览</title>
                <style>
                    body { margin: 0; padding: 20px; }
                    .docx-viewer { max-width: 100%; margin: 0 auto; }
                </style>
            </head>
            <body>
                ${printContent}
                <script>
                    window.onload = function() {
                        window.print();
                        window.close();
                    }
                <\/script>
            </body>
        </html>
    `
    printWindow.document.write(printHtml)
    printWindow.document.close()
}

// 关闭弹窗
const handleCancel = () => {
    emit('update:modelValue', false)
    emit('cancel')
}

// 组件卸载时清理
onMounted(() => {
    if (props.modelValue && props.fileUrl) {
        loadDocument()
    }
})

defineExpose({
    reload: loadDocument
})
</script>

<style scoped lang="less">
.word-viewer-container {
    position: relative;
    height: calc(100vh - 200px);
    min-height: 500px;
    
    .loading-container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    
    .viewer-content {
        height: calc(100% - 50px); // 减去工具栏高度
        overflow: auto;
        //padding: 20px;
        
        :deep(.docx-viewer) {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            // padding: 40px;
        }
    }
    
    .toolbar {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 50px;
        padding: 8px 16px;
        background: #fff;
        border-top: 1px solid var(--color-neutral-3);
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
:deep(.arco-modal-body) {
    // padding: 16px !important;
}
:deep(.docx-viewer) {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0px !important;
    margin: 0px !important;
}
:deep(.docx-viewer-wrapper) {
    padding: 0px !important;
}
</style> 