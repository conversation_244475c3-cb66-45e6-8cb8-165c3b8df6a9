<template>
    <div class="container">
        <a-card class="general-card" :title="'用户管理'">
            <a-row>
                <a-col :flex="1">
                    <a-form :model="formModel" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }"
                        label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item field="nickName" label="人员名称">
                                    <a-input v-model="formModel.nickName" placeholder="请输入人员名称" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="userName" label="人员账号">
                                    <a-input v-model="formModel.userName" placeholder="请输入人员账号" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="phonenumber" label="手机号">
                                    <a-input v-model="formModel.phonenumber" placeholder="请输入手机号" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="menuRoles" label="功能角色">
                                    <a-select v-model="formModel.menuRoles" :options="functionRoleOptions"
                                        placeholder="请选择功能角色" multiple />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="dataRoles" label="数据角色">
                                    <a-select v-model="formModel.dataRoles" :options="dataRoleOptions"
                                        placeholder="请选择数据角色" multiple />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item field="status" label="账号状态">
                                    <a-select v-model="formModel.status" :options="statusOptions"
                                        placeholder="请选择账号状态" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-col>
                <a-divider style="height: 84px" direction="vertical" />
                <a-col :flex="'86px'" style="text-align: right">
                    <a-space direction="vertical" :size="18">
                        <a-button type="primary" @click="search">
                            <template #icon>
                                <icon-search />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            重置
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-divider style="margin-top: 0" />
            <a-row style="margin-bottom: 16px">
                <a-col :span="24" style="text-align: right;">
                    <a-space>
                        <a-button v-permission="['system:user:add']" type="primary" @click="handleAddUser">
                            <template #icon>
                                <icon-plus />
                            </template>添加用户
                        </a-button>
                    </a-space>
                </a-col>
            </a-row>
            <a-table row-key="id" :loading="loading" :pagination="pagination" column-resizable
                :bordered="{ cell: true }" :columns="(cloneColumns as TableColumnData[])" :data="renderData"
                :size="size" @page-change="onPageChange">
                <template #index="{ rowIndex }">
                    {{ rowIndex + 1 + (pagination.pageNum - 1) * pagination.pageSize }}
                </template>
                <template #status="{ record }">
                    <span v-if="record.status === '1'" class="circle circle-red"></span>
                    <span v-else class="circle pass"></span>
                    {{ record.status === '0' ? '启用' : '禁用' }}
                </template>
                <template #operations="{ record }">
                    <a-button v-permission="['system:user:enable']" type="text" size="small" v-if="record.status === '1'"
                        @click="handleEnableUser(record)">启用</a-button>
                    <a-button v-permission="['system:user:disable']" type="text" size="small" v-if="record.status === '0'"
                        @click="handleDisableUser(record)">禁用</a-button>
                    <a-button v-permission="['system:user:auth']" type="text" size="small" v-if="record.status === '0'"
                        @click="handleAuthUser(record)">授权</a-button>
                </template>
            </a-table>
        </a-card>
        <!-- 2功能（菜单）  3 是数据 -->
        <!-- 添加用户 -->
        <a-drawer :width="1200" :visible="addUserVisible" :title="addUserTitle" :mask-closable="true"
            :ok-text="currentStep === 1 ? '下一步' : '提交'" :key="drawerKey" @ok="handleAddUserOk" @cancel="handleAddUserCancel">
            <div class="add-user-container">
                <div class="add-user-header-container">
                    <div class="add-user-header" v-if="addUserType === 'add'">
                        <a-steps :current="currentStep" changeable @change="handleStepChange">
                            <a-step :description="'已选择用户：' + ehrUserIdListWithNameString">添加用户</a-step>
                            <a-step>角色授权</a-step>
                        </a-steps>
                    </div>
                    <div class="add-user-header" v-if="addUserType === 'auth'">
                        <a-table :data="authUserList" :bordered="{ cell: true }" :pagination="false"
                            style="width: 100%;" :columns="addUserColumns">
                        </a-table>
                    </div>
                </div>
                <div class="add-user-content" v-if="currentStep === 1">
                    <div class="add-user-content-left">
                        <ehr-org-tree @change="handleEhrOrgTreeChange" />
                    </div>
                    <div class="add-user-content-right">
                        <ehr-user-table @change="handleEhrUserTableChange" />
                    </div>
                </div>
                <div class="add-user-content" v-if="currentStep === 2">
                    <div class="add-user-content-left-wide">
                        <function-role-auth @change="handleFunctionRoleAuthChange" :data="functionRoleIdList" v-if="addUserVisible"/>
                    </div>
                    <div class="add-user-content-right">
                        <data-role-auth @change="handleDataRoleAuthChange" :data="dataRoleIdList" v-if="addUserVisible"/>
                    </div>
                </div>
            </div>
        </a-drawer>
    </div>
</template>
<script lang="ts" setup>
import { computed, ref, reactive, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { getUserList, setUserAuthRole, changeUserStatus, getUserAuthRoleByUsername } from '@/api/user'
import useLoading from '@/hooks/loading'
import { queryPolicyList, PolicyRecord, PolicyParams } from '@/api/list'
import { Pagination } from '@/types/global'
import { getRoleList } from '@/api/role'

import ehrOrgTree from '@/components/ehrOrgTree/ehrOrgTree.vue'
import ehrUserTable from '@/components/ehrUserTable/ehrUserTable.vue'
import functionRoleAuth from '@/components/functionRoleAuth/functionRoleAuth.vue'
import dataRoleAuth from '@/components/dataRoleAuth/dataRoleAuth.vue'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
import cloneDeep from 'lodash/cloneDeep';
import Sortable from 'sortablejs';
import { Message } from '@arco-design/web-vue';
// import { getOrgTree } from '@/api/org';
type SizeProps = 'mini' | 'small' | 'medium' | 'large';
type Column = TableColumnData & { checked?: true };

const drawerKey = ref(0)

const generateFormModel = () => {
    return {
        nickName: '',
        userName: '',
        phonenumber: '',
        menuRoles: [],
        dataRoles: [],
        status: '',
    }
}
const { loading, setLoading } = useLoading(true);
const { t } = useI18n();
const renderData = ref<PolicyRecord[]>([]);
const formModel = ref(generateFormModel());
const cloneColumns = ref<Column[]>([]);
const showColumns = ref<Column[]>([]);

const size = ref<SizeProps>('medium');

const basePagination: Pagination = {
    pageNum: 1,
    pageSize: 20,
    showPageSize: true,
};
const pagination = reactive({
    ...basePagination,
});
const addUserVisible = ref(false)
const addUserType = ref('add')
const currentStep = ref(1)
const addUserTitle = ref('添加用户')
const addUserForm = ref({
    nickName: '',
    userName: '',
    phonenumber: '',
})

const columns = computed<TableColumnData[]>(() => [
    {
        title: t('searchTable.columns.index'),
        dataIndex: 'index',
        slotName: 'index',
    },
    {
        title: '人员名称',
        dataIndex: 'nickName',
    },
    {
        title: 'OA账号',
        dataIndex: 'userName',
    },
    {
        title: '手机号',
        dataIndex: 'phonenumber',
    },
    {
        title: '所属部门',
        dataIndex: 'postname',
    },
    {
        title: '功能角色',
        dataIndex: 'menuPermissions',
    },
    {
        title: '数据角色',
        dataIndex: 'dataPermissions',
    },
    {
        title: '账号状态',
        dataIndex: 'status',
        slotName: 'status',
    },
    {
        title: '操作',
        slotName: 'operations',
    },
])
const addUserColumns = ref<any[]>([
    {
        title: '人员名称',
        dataIndex: 'nickName',
    },
    {
        title: 'OA账号',
        dataIndex: 'userName',
    },
    {
        title: '手机号',
        dataIndex: 'phonenumber',
    },
    {
        title: '所属部门',
        dataIndex: 'postname',
    },
    {
        title: '状态',
        dataIndex: 'status',
        render: (text: string) => {
            return text === '1' ? '禁用' : '启用'
        }
    },
])
const statusOptions = computed<SelectOptionData[]>(() => [
    {
        label: '启用',
        value: '0',
    },
    {
        label: '禁用',
        value: '1',
    },
])
const functionRoleOptions = ref<SelectOptionData[]>([])
const dataRoleOptions = ref<SelectOptionData[]>([])
const fetchFunctionRoleList = async () => {
    const { data } = await getRoleList({ type: 2 })
    console.log(data)
    functionRoleOptions.value = data.map((item: any) => ({
        label: item.roleName,
        value: item.roleId,
    }))
}
const fetchDataRoleList = async () => {
    const { data } = await getRoleList({ type: 3 })
    console.log(data)
    dataRoleOptions.value = data.map((item: any) => ({
        label: item.roleName,
        value: item.roleId,
    }))
}
fetchFunctionRoleList()
fetchDataRoleList()
/**
 * {
  "userId": 0,
  "status": "string"
}*/
const handleEnableUser = async (record: any) => {
    console.log(record)
    await changeUserStatus({
        userId: record.userId,
        status: '0'
    })
    Message.success('操作成功')
    fetchData()
}
const handleDisableUser = async (record: any) => {
    console.log(record)
    await changeUserStatus({
        userId: record.userId,
        status: '1'
    })
    Message.success('操作成功')
    fetchData()
}

const userIdList = ref<any[]>([])
const authUserList = ref<any[]>([])
const handleAuthUser = async (record: any) => {
    drawerKey.value++
    addUserTitle.value = '用户授权'
    console.log(record)
    const { data } = await getUserAuthRoleByUsername(record.userId)
    console.log(data)
    currentStep.value = 2
    addUserVisible.value = true
    addUserType.value = 'auth'
    authUserList.value = [record]
    console.log(authUserList.value)
    functionRoleIdList.value = []
    dataRoleIdList.value = []
    userIdList.value = [data.userId]

    if (data.roles) {
        data.roles.forEach((item: any) => {
            if (item.type === 2) {
                functionRoleIdList.value.push(item.roleId)
            } else {
                dataRoleIdList.value.push(item.roleId)
            }
        })
    }
    // functionRoleIdList.value = [record.roleId]
    // dataRoleIdList.value = [record.roleId]
    // ehrUserIdList.value = [record.userId]
    // ehrUserIdListWithName.value = [record]
    // ehrUserIdListWithNameString.value = record.nickName
}
const handleAddUser = () => {
    drawerKey.value++
    addUserTitle.value = '添加用户'
    addUserVisible.value = true
    currentStep.value = 1
    ehrUserIdList.value = []
    ehrUserIdListWithName.value = []
    ehrUserIdListWithNameString.value = ''
    functionRoleIdList.value = []
    dataRoleIdList.value = []
    addUserType.value = 'add'
}
const handleStepChange = (val: number) => {
    console.log(val)
    currentStep.value = val
}
const addUserRules = {
    nickName: [{ required: true, message: '请输入用户名' }],
}
const handleAddUserOk = async () => {
    console.log(addUserForm.value)
    if (currentStep.value === 1) {
        currentStep.value = 2
    } else {
        console.log('提交')
        if (ehrUserIdList.value.length <= 0 && addUserType.value === 'add') {
            Message.error('请选择用户')
            return
        }
        if (functionRoleIdList.value.length <= 0) {
            Message.error('请选择功能角色')
            return
        }
        if (dataRoleIdList.value.length <= 0) {
            Message.error('请选择数据角色')
            return
        }
        await setUserAuthRole({
            userIdList: userIdList.value,
            ehrUserIdList: ehrUserIdList.value,
            roleIdList: functionRoleIdList.value.concat(dataRoleIdList.value)
        })
        Message.success('操作成功')
        addUserVisible.value = false
        fetchData()
    }
}
const handleAddUserCancel = () => {
    addUserVisible.value = false
}

const ehrOrgIdList = ref<any[]>([])
const handleEhrOrgTreeChange = (keys: any[]) => {
    console.log(keys)
    if (keys.length > 0) {
        ehrOrgIdList.value = keys
    }
}
const ehrUserIdList = ref<any[]>([])
const ehrUserIdListWithName = ref<any[]>([])
const ehrUserIdListWithNameString = ref<string>('')
const handleEhrUserTableChange = (selectedRows: any[], selectedRowsWithName: any[]) => {
    console.log(selectedRows)
    console.log(selectedRowsWithName)
    ehrUserIdList.value = selectedRows
    ehrUserIdListWithName.value = selectedRowsWithName
    if (selectedRowsWithName.length > 0) {
        ehrUserIdListWithNameString.value = selectedRowsWithName.map((item: any) => item.name).join(',')
        // currentStep.value = 2
    } else {
        ehrUserIdListWithNameString.value = ''
    }
}
const dataRoleIdList = ref<any[]>([])
const handleDataRoleAuthChange = (value: any) => {
    console.log(value)
    dataRoleIdList.value = value
}
const functionRoleIdList = ref<any[]>([])
const handleFunctionRoleAuthChange = (value: any) => {
    console.log(value)
    functionRoleIdList.value = value
}
//ehrUserIdList
//roleIdList
//userIdList
const fetchData = async (
    params: PolicyParams = { current: 1, pageSize: 20 }
) => {
    setLoading(true);
    try {
        const res: any = await getUserList(params)
        // console.log(rows)
        renderData.value = res.rows
        // pagination.total = res.total
        // pagination.current = params.current;
        // pagination.total = data.total;
    } catch (err) {
        // you can report use errorHandler or other
    } finally {
        setLoading(false);
    }
};

const search = () => {
    fetchData({
        ...basePagination,
        ...formModel.value,
    } as unknown as PolicyParams);
};
const onPageChange = (current: number) => {
    fetchData({ ...basePagination, current });
};

fetchData();
const reset = () => {
    formModel.value = generateFormModel();
};



const exchangeArray = <T extends Array<any>>(
    array: T,
    beforeIdx: number,
    newIdx: number,
    isDeep = false
): T => {
    const newArray = isDeep ? cloneDeep(array) : array;
    if (beforeIdx > -1 && newIdx > -1) {
        // 先替换后面的，然后拿到替换的结果替换前面的
        newArray.splice(
            beforeIdx,
            1,
            newArray.splice(newIdx, 1, newArray[beforeIdx]).pop()
        );
    }
    return newArray;
};

const popupVisibleChange = (val: boolean) => {
    if (val) {
        nextTick(() => {
            const el = document.getElementById(
                'tableSetting'
            ) as HTMLElement;
            const sortable = new Sortable(el, {
                onEnd(e: any) {
                    const { oldIndex, newIndex } = e;
                    exchangeArray(cloneColumns.value, oldIndex, newIndex);
                    exchangeArray(showColumns.value, oldIndex, newIndex);
                },
            });
        });
    }
};

watch(
    () => columns.value,
    (val) => {
        cloneColumns.value = cloneDeep(val);
        cloneColumns.value.forEach((item, index) => {
            item.checked = true;
        });
        showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
);
</script>

<script lang="ts">
export default { name: 'postRouter' };
</script>

<style scoped lang="less">
.container {
    padding: 0 12px;
}

.add-user-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.add-user-header-container {
    padding: 12px;
    background: rgb(var(--gray-1));
    border-radius: 4px;
    margin-bottom: 12px;

    .add-user-header {
        display: flex;
        align-items: center;
        justify-content: center;

        .arco-steps {
            width: 50%;
        }
    }
}

.add-user-content {
    display: flex;
    flex: 1;
    height: 100%;
    
    .add-user-content-left {
        width: 280px;
    }
    
    .add-user-content-left-wide {
        width: 600px;
    }
    
    .add-user-content-right {
        flex: 1;
        height: 100%;
        margin-left: 12px;
    }
}

:deep(.arco-table-th) {
    &:last-child {
        .arco-table-th-item-title {
            margin-left: 16px;
        }
    }
}

:deep(.arco-steps-item-description) {
    max-width: 180px;
    height: 22px !important;
    line-height: 22px !important;
    overflow: hidden;
    // 省略号
    text-overflow: ellipsis;
    white-space: nowrap;
}

.action-icon {
    margin-left: 12px;
    cursor: pointer;
}

.active {
    color: #0960bd;
    background-color: #e3f4fc;
}

.setting {
    display: flex;
    align-items: center;
    width: 200px;

    .title {
        margin-left: 12px;
        cursor: pointer;
    }
}
</style>
