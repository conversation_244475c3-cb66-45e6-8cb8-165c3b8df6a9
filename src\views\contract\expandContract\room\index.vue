<template>
    <div class="expand-property">
        <section>
            <sectionTitle title="新房源账单" :large="true" />
        </section>
        <section class="margin-top-16">
            <sectionTitle title="保证金信息列表"></sectionTitle>
            <div class="table-content">
                <a-table :data="depositTableData" :columns="depositColumns" :bordered="{ cell: true }"
                    :pagination="{ showJumper: true }" :scroll="{ x: 1200 }">
                    <template #actualReceivable="{ record }">
                        {{ formatAmount(record.actualReceivable) }}
                    </template>
                </a-table>
            </div>
        </section>
        <section class="margin-top-16">
            <sectionTitle title="租金信息列表"></sectionTitle>
            <div class="table-content">
                <a-table :data="rentTableData" :columns="rentColumns" :bordered="{ cell: true }"
                    :pagination="{ showJumper: true }" :scroll="{ x: 1200 }">
                    <template #actualReceivable="{ record }">
                        {{ formatAmount(record.actualReceivable) }}
                    </template>
                    <template #freeRent="{ record }">
                        {{ formatAmount(record.freeRent) }}
                    </template>
                    <template #receivedAmount="{ record }">
                        {{ formatAmount(record.receivedAmount) }}
                    </template>
                </a-table>
            </div>
        </section>
    </div>

</template>
<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue';
import { ref, computed, watch } from 'vue';
import { useContractStore } from '@/store/modules/contract'
import { ContractRoomDTO } from '@/api/contract';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 定义props接收数据
interface Props {
    newCosts?: any[];
    oldCosts?: any[];
    changeCosts?: any[];
    isViewMode?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    newCosts: () => [],
    oldCosts: () => [],
    changeCosts: () => [],
    isViewMode: false
});

const formData = ref({
    roomName: ''
});
const tableData = ref<ContractRoomDTO[]>([]);
const columns = ref([
    {
        title: '房源名称',
        dataIndex: 'roomName',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'roomName'
    },
    {
        title: '租赁面积（㎡）',
        dataIndex: 'area',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'area'
    },
    {
        title: '标准租金',
        dataIndex: 'standardUnitPrice',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'standardUnitPrice'
    },
    {
        title: '折扣',
        dataIndex: 'discount',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'discount'
    },
    {
        title: '签约单价',
        dataIndex: 'signedUnitPrice',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'signedUnitPrice'
    },
    {
        title: '签约月总价（元/月）',
        dataIndex: 'signedMonthlyPrice',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'signedMonthlyPrice'
    },
    {
        title: '操作',
        dataIndex: 'operations',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'operations',
        fixed: 'right',
    },
]);
const depositTableData = ref<any[]>([]);
const depositColumns = ref([
    {
        title: '租期',
        dataIndex: 'startDate',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'startDate'
    },
    {
        title: '租户',
        dataIndex: 'customerName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'customerName'
    },
    {
        title: '款项',
        dataIndex: 'subjectName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'subjectName'
    },
    {
        title: '应收日期',
        dataIndex: 'receivableDate',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'receivableDate'
    },
    {
        title: '账单应收金额（元）',
        dataIndex: 'actualReceivable',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'actualReceivable'
    },
]);
const rentTableData = ref<any[]>([]);
const rentColumns = ref([
    {
        title: '租期',
        dataIndex: 'startDate',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'startDate'
    },
    {
        title: '租户',
        dataIndex: 'customerName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'customerName'
    },
    {
        title: '款项',
        dataIndex: 'subjectName',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'subjectName'
    },
    {
        title: '应收日期',
        dataIndex: 'receivableDate',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'receivableDate'
    },
    {
        title: '账单总额（元）',
        dataIndex: 'totalAmount',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'totalAmount'
    },
    {
        title: '免租优惠（元）',
        dataIndex: 'freeRent',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'freeRent'
    },
    {
        title: '账单应收金额（元）',
        dataIndex: 'actualReceivable',
        width: 150,
        align: 'right',
        ellipsis: true,
        tooltip: true,
        slotName: 'actualReceivable'
    },
]);
const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)
const editType = computed(() => contractStore.editType)

const discount = ref()

// 处理账单数据，根据period合并
const processBillData = () => {
    console.log('扩租room组件接收到的数据:', {
        newCosts: props.newCosts,
        oldCosts: props.oldCosts,
        changeCosts: props.changeCosts,
        isViewMode: props.isViewMode
    });
    
    // 新房源账单使用changeCosts数据（包含新房源账单和退款账单）
    const changeCostsData = props.changeCosts || [];
    console.log('原始changeCosts数据:', changeCostsData);
    
    // 按period分组合并账单
    const groupedByPeriod = new Map();
    
    changeCostsData.forEach((cost: any) => {
        console.log('处理单个changeCost数据:', {
            period: cost.period,
            costType: cost.costType,
            type: cost.type, // type=1新房源，type=2退款
            actualReceivable: cost.actualReceivable,
            totalAmount: cost.totalAmount,
            discountAmount: cost.discountAmount,
            subjectName: cost.subjectName
        });
        
        const period = cost.period;
        if (!groupedByPeriod.has(period)) {
            groupedByPeriod.set(period, {
                period: period,
                startDate: cost.startDate,
                endDate: cost.endDate,
                customerName: cost.customerName,
                depositCosts: [],
                rentCosts: []
            });
        }
        
        const group = groupedByPeriod.get(period);
        
        // 根据costType分类
        if (cost.costType === 1) { // 保证金
            group.depositCosts.push(cost);
        } else if (cost.costType === 2) { // 租金
            group.rentCosts.push(cost);
        }
    });
    
    // 转换为表格数据
    const depositData: any[] = [];
    const rentData: any[] = [];
    
    groupedByPeriod.forEach((group: any, period) => {
        // 处理保证金数据 - 按period合并
        if (group.depositCosts.length > 0) {
            // 合并同一period的保证金数据
            const depositCosts = group.depositCosts;
            const startDate = depositCosts[0].startDate;
            const endDate = depositCosts[depositCosts.length - 1].endDate;
            const customerName = depositCosts[0].customerName;
            const subjectName = depositCosts[0].subjectName || '保证金';
            const receivableDate = depositCosts[0].receivableDate;
            
            // 合并金额
            const actualReceivable = depositCosts.reduce((sum: number, cost: any) => sum + (cost.actualReceivable || 0), 0);
            const receivedAmount = depositCosts.reduce((sum: number, cost: any) => sum + (cost.receivedAmount || 0), 0);
            const remainingReceivable = actualReceivable - receivedAmount;
            
            console.log('保证金数据处理（按period合并）:', {
                period,
                depositCosts,
                startDate,
                endDate,
                customerName,
                subjectName,
                receivableDate,
                actualReceivable,
                receivedAmount,
                remainingReceivable
            });
            
            depositData.push({
                startDate: `${startDate} 至 ${endDate}`,
                customerName,
                subjectName,
                receivableDate,
                actualReceivable,
                receivedAmount,
                remainingReceivable,
                period
            });
        }
        
        // 处理租金数据 - 按period合并
        if (group.rentCosts.length > 0) {
            // 合并同一period的租金数据
            const rentCosts = group.rentCosts;
            const startDate = rentCosts[0].startDate;
            const endDate = rentCosts[rentCosts.length - 1].endDate;
            const customerName = rentCosts[0].customerName;
            const subjectName = rentCosts[0].subjectName || '租金';
            const receivableDate = rentCosts[0].receivableDate;
            
            // 合并金额
            const totalAmount = rentCosts.reduce((sum: number, cost: any) => sum + (cost.totalAmount || 0), 0);
            const actualReceivable = rentCosts.reduce((sum: number, cost: any) => sum + (cost.actualReceivable || 0), 0);
            const discountAmount = rentCosts.reduce((sum: number, cost: any) => sum + (cost.discountAmount || 0), 0);
            const receivedAmount = rentCosts.reduce((sum: number, cost: any) => sum + (cost.receivedAmount || 0), 0);
            
            console.log('租金数据处理（按period合并）:', {
                period,
                rentCosts,
                startDate,
                endDate,
                customerName,
                subjectName,
                receivableDate,
                totalAmount,
                actualReceivable,
                discountAmount,
                receivedAmount
            });
            
            rentData.push({
                startDate: `${startDate} 至 ${endDate}`,
                customerName,
                subjectName,
                receivableDate,
                totalAmount,
                actualReceivable,
                freeRent: discountAmount, // 使用discountAmount作为免租优惠
                receivedAmount,
                period
            });
        }
    });
    
    // 更新表格数据
    depositTableData.value = depositData;
    rentTableData.value = rentData;
};

// 监听props变化，重新处理数据
watch(() => [props.newCosts, props.oldCosts, props.changeCosts], () => {
    processBillData();
}, { deep: true, immediate: true });

</script>
<style lang="less" scoped>
.table-content {
    margin-top: 16px;
}

.margin-top-16 {
    margin-top: 16px;
}
</style>