/**
 * 套打模板相关类型定义
 */

// 套打模板查询参数（保持string类型用于查询）
export interface TemplateQueryParams {
  templateName?: string;
  applyLevel?: string;
  applyScope?: string;
  printType?: string;
  contractPurpose?: string;
  status?: string;
  pageNum: number;
  pageSize: number;
}

// 套打模板数据（基于最新接口文档更新）
export interface TemplateData {
  id: string;
  templateName: string;
  // 【变更】删除了 projectId 字段，改为使用 projectIdList
  // projectId: string;
  applyLevel: number; // 【变更】类型从 string 改为 number
  // 【修正】适用范围改为项目选择，删除 applyScope 字段
  // applyScope: string;
  printType: number; // 【变更】类型从 string 改为 number
  contractPurpose: number; // 【变更】类型从 string 改为 number
  effectiveDate: string;
  expirationDate: string;
  status: number; // 【变更】类型从 string 改为 number
  linkUrl: string;
  remark: string;
  projectIdList?: string[]; // 【修正】项目ID列表，用于项目级的适用项目选择
  createBy: string;
  createByName: string;
  createTime: string;
  updateBy: string;
  updateByName: string;
  updateTime: string;
}

// 套打模板状态修改参数（基于最新接口文档更新）
export interface TemplateStatusDTO {
  templateId: string;
  status: number; // 【变更】类型从 string 改为 number
}

/**
 * 套打字典相关类型定义
 */

// 套打字典查询参数
export interface DictQueryParams {
  name?: string;
  type?: number;
  code?: string;
  pageNum: number;
  pageSize: number;
}

// 套打字典数据
export interface DictData {
  id: string;
  parentId: string;
  name: string;
  type: number;
  code: string;
  dbField: string;
  sqlText: string;
  remark: string;
  attachments?: Array<{
    fileId: string;
    name: string;
    url: string;
  }>;
  createBy: string;
  createByName: string;
  createTime: string;
  updateBy: string;
  updateByName: string;
  updateTime: string;
}

/**
 * 分页数据通用接口
 */
export interface PaginationData<T> {
  total: number;
  rows: T[];
  code: number;
  msg: string;
}

/**
 * 通用响应接口
 */
export interface ApiResponse<T> {
  data: T;
  code: number;
  msg: string;
  success?: boolean;
} 