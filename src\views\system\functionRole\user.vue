<template>
    <!-- 数据角色用户列表 -->
    <div class="data-role-user-list">
        <a-row>
            <a-col :flex="1">
                <a-form :model="formModel" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }"
                    label-align="right">
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item field="nickName" label="人员名称">
                                <a-input v-model="formModel.nickName" placeholder="请输入人员名称" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="phonenumber" label="手机号">
                                <a-input v-model="formModel.phonenumber" placeholder="请输入手机号" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="status" label="账号状态">
                                <a-select v-model="formModel.status" :options="statusOptions"
                                    placeholder="请选择账号状态" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </a-col>
            <a-divider style="height: 42px" direction="vertical" />
            <a-col :flex="'86px'" style="text-align: right">
                <a-space direction="vertical" :size="18">
                    <a-button type="primary" @click="search">
                        <template #icon>
                            <icon-search />
                        </template>
                        查询
                    </a-button>
                    <!-- <a-button @click="reset">
                                            <template #icon>
                                                <icon-refresh />
                                            </template>
                                            {{ $t('searchTable.form.reset') }}
                                        </a-button> -->
                </a-space>
            </a-col>
        </a-row>
        <a-divider style="margin-top: 0" />
        <a-row style="margin: 16px 0">
            <a-col :span="24" style="text-align: right;">
                <a-space>
                    <a-button v-permission="['system:menu:user:add']" type="primary" @click="handleAddUser">
                        <template #icon>
                            <icon-plus />
                        </template>
                        添加用户
                    </a-button>
                </a-space>
            </a-col>
        </a-row>
        <a-table row-key="id" :loading="loading" :pagination="pagination" column-resizable :bordered="{ cell: true }"
            :columns="(cloneColumns as TableColumnData[])" :data="renderData" :size="size" @page-change="onPageChange">
            <template #index="{ rowIndex }">
                {{
                    rowIndex +
                    1 +
                    (pagination.pageNum - 1) * pagination.pageSize
                }}
            </template>
            <template #status="{ record }">
                <span v-if="record.status === '1'" class="circle circle-red"></span>
                <span v-else class="circle pass"></span>
                {{ record.status === '0' ? '启用' : '禁用' }}
            </template>
            <template #operations="{ record }">
                <a-button v-permission="['system:menu:user:remove']" type="text" size="small" @click="handleRemoveUser(record)">移除</a-button>
            </template>
        </a-table>
        <!-- 添加用户 -->
        <a-drawer :width="1200" :visible="addUserVisible" :title="addUserTitle" :mask-closable="true" :ok-text="'提交'" @ok="handleAddUserOk" @cancel="handleAddUserCancel">
            <div class="add-user-content" >
                <div class="add-user-content-left">
                    <ehr-org-tree @change="handleEhrOrgTreeChange" />
                </div>
                <div class="add-user-content-right">
                    <ehr-user-table @change="handleEhrUserTableChange" />
                </div>
            </div>
            <!-- <a-layout style="height: 100%">
                <a-layout >
                    <a-layout-sider :resize-directions="['right']" :width="280"> -->
                        <!-- <ehr-org-tree @change="handleEhrOrgTreeChange" /> -->
                    <!-- </a-layout-sider>
                    <a-layout-content> -->
                        <!-- <ehr-user-table @change="handleEhrUserTableChange" /> -->
                    <!-- </a-layout-content>
                </a-layout>
            </a-layout> -->
        </a-drawer>
    </div>
</template>
<script setup lang="ts">
import { computed, ref, reactive, watch, nextTick } from 'vue'
import { defineProps } from 'vue'
import dataRolesList from '@/components/dataRolesList/index.vue'
import ehrOrgTree from '@/components/ehrOrgTree/ehrOrgTree.vue'
import ehrUserTable from '@/components/ehrUserTable/ehrUserTable.vue'
import { getRoleUserList, bindRoleUser, cancelRoleUser } from '@/api/role'
import { useI18n } from 'vue-i18n';
import useLoading from '@/hooks/loading';
import { queryPolicyList, PolicyRecord, PolicyParams } from '@/api/list';
import { Pagination } from '@/types/global';
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
import cloneDeep from 'lodash/cloneDeep';
import Sortable from 'sortablejs';
import { Message, Modal } from '@arco-design/web-vue';

const props = defineProps({
    roleId: {
        type: String,
        required: true,
    },
})

type SizeProps = 'mini' | 'small' | 'medium' | 'large';
type Column = TableColumnData & { checked?: true };
const generateFormModel = () => {
    return {
        nickName: '',
        phonenumber: '',
        status: '',
    };
};
const otherParams = {
    roleId: props.roleId,
    type: 2
}
const { loading, setLoading } = useLoading(false);
const { t } = useI18n();
const renderData = ref<PolicyRecord[]>([]);
const formModel = ref(generateFormModel());
const cloneColumns = ref<Column[]>([]);
const showColumns = ref<Column[]>([]);

const size = ref<SizeProps>('medium');

const basePagination: Pagination = {
    pageNum: 1,
    pageSize: 20,
    showPageSize: true,
};
const pagination = reactive({
    ...basePagination,
})
const columns = computed<TableColumnData[]>(() => [
    {
        title: '序号',
        dataIndex: 'index',
        slotName: 'index',
    },
    {
        title: '人员名称',
        dataIndex: 'nickName',
    },
    {
        title: 'OA账号',
        dataIndex: 'userName',
    },
    {
        title: '手机号',
        dataIndex: 'phonenumber',
    },
    {
        title: '所属部门',
        dataIndex: 'postName',
    },
    {
        title: '账号状态',
        dataIndex: 'status',
        slotName: 'status',
    },
    {
        title: '操作',
        dataIndex: 'operations',
        slotName: 'operations',
    },
]);

const statusOptions = computed<SelectOptionData[]>(() => [
    {
        label: t('searchTable.form.status.online'),
        value: 'online',
    },
    {
        label: t('searchTable.form.status.offline'),
        value: 'offline',
    },
]);

const addUserVisible = ref(false)
const addUserTitle = ref('添加用户')


const ehrUserIdList = ref<PolicyRecord[]>([])
const handleAddUser = () => {
    addUserVisible.value = true
}

// {
//   "roleId": 0,
//   "ehrUserIdList": [
//     "string"
//   ],
//   "userIdList": [
//     0
//   ]
// }

const handleAddUserOk = async () => {
    console.log('handleAddUserOk')
    await bindRoleUser({
        roleId: props.roleId,
        ehrUserIdList: ehrUserIdList.value
    })
    addUserVisible.value = false

    Message.success('添加成功')
    fetchData()
}

const handleAddUserCancel = () => {
    console.log('handleAddUserCancel')
    addUserVisible.value = false
}

const handleEhrOrgTreeChange = (value: string) => {
    console.log('handleEhrOrgTreeChange', value)
    // console.log(keys)
    // if (keys.length > 0) {
    //     ehrOrgIdList.value = keys
    // }
}

const handleEhrUserTableChange = (selectedRows: PolicyRecord[]) => {
    console.log('handleEhrUserTableChange', selectedRows)
    ehrUserIdList.value = selectedRows
}

/**
 * {
  "roleId": 0,
  "userIdList": [
    0
  ]
}*/ 
const handleRemoveUser = async (record: any) => {
    console.log('handleRemoveUser', record)
    // confirm
    Modal.confirm({
        title: '提示',
        content: '确定移除该用户吗？',
        onOk: async () => {
            await cancelRoleUser({
                roleId: props.roleId,
                userIdList: [record.userId]
            })
            Message.success('移除成功')
            fetchData()
        }
    })
}
const fetchData = async (
    params: PolicyParams = { pageNum: 1, pageSize: 20 }
) => {
    setLoading(true)
    try {
        otherParams.roleId = props.roleId
        const { rows } = await getRoleUserList({ ...params, ...otherParams })
        renderData.value = rows
        pagination.pageNum = params.pageNum;
        // pagination.total = data
    } catch (err) {
        // you can report use errorHandler or other
    } finally {
        setLoading(false)
    }
};

const search = () => {
    fetchData({
        ...basePagination,
        ...formModel.value,
    } as unknown as PolicyParams);
};
const onPageChange = (current: number) => {
    fetchData({ ...basePagination, current })
};

// fetchData();
const reset = () => {
    formModel.value = generateFormModel()
}

const handleSelectDensity = (
    val: string | number | Record<string, any> | undefined,
    e: Event
) => {
    size.value = val as SizeProps;
}

const handleChange = (
    checked: boolean | (string | boolean | number)[],
    column: Column,
    index: number
) => {
    if (!checked) {
        cloneColumns.value = showColumns.value.filter(
            (item) => item.dataIndex !== column.dataIndex
        );
    } else {
        cloneColumns.value.splice(index, 0, column);
    }
};

const exchangeArray = <T extends Array<any>>(
    array: T,
    beforeIdx: number,
    newIdx: number,
    isDeep = false
): T => {
    const newArray = isDeep ? cloneDeep(array) : array;
    if (beforeIdx > -1 && newIdx > -1) {
        // 先替换后面的，然后拿到替换的结果替换前面的
        newArray.splice(
            beforeIdx,
            1,
            newArray.splice(newIdx, 1, newArray[beforeIdx]).pop()
        );
    }
    return newArray;
};

const popupVisibleChange = (val: boolean) => {
    if (val) {
        nextTick(() => {
            const el = document.getElementById(
                'tableSetting'
            ) as HTMLElement;
            const sortable = new Sortable(el, {
                onEnd(e: any) {
                    const { oldIndex, newIndex } = e;
                    exchangeArray(cloneColumns.value, oldIndex, newIndex);
                    exchangeArray(showColumns.value, oldIndex, newIndex);
                },
            });
        });
    }
};

watch(
    () => columns.value,
    (val) => {
        cloneColumns.value = cloneDeep(val);
        cloneColumns.value.forEach((item, index) => {
            item.checked = true;
        });
        showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
)

defineExpose({
    fetchData
})

</script>

<script lang="ts">
export default { name: 'dataRoleUserComponent' }
</script>

<style scoped lang="less">
.add-user-content {
    display: flex;
    height: 100%;
    .add-user-content-left {
        width: 280px;
    }
    .add-user-content-right {
        flex: 1;
        height: 100%;
        margin-left: 12px;
    }
}
.page-layout-column {
    width: 100%;
    // height: 100%;
    // box-sizing: border-box;
    // padding: 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .page-layout-column-left {
        width: 280px;
        height: 100%;
    }

    .page-layout-column-right {
        flex: 1;
        height: 100%;
        margin-left: 12px;
    }
}

.data-role-setting-item-group {
    padding: 12px;
    border-radius: 4px;
    background-color: rgb(var(--gray-1));

    .data-role-setting-item {
        display: flex;
        align-items: center;
        // justify-content: space-between;
        margin-right: 12px;

        .data-role-setting-item-title {
            font-size: 14px;
            font-weight: 600;
            color: rgb(var(--gray-9));
            margin-right: 12px;
        }

        .data-role-setting-item-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
}

.data-role-user-list {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 12px;
    // border-radius: 4px;
    // background-color: rgb(var(--gray-1));
}

:deep(.arco-table-th) {
    &:last-child {
        .arco-table-th-item-title {
            margin-left: 16px;
        }
    }
}

.action-icon {
    margin-left: 12px;
    cursor: pointer;
}

.active {
    color: #0960bd;
    background-color: #e3f4fc;
}

.setting {
    display: flex;
    align-items: center;
    width: 200px;

    .title {
        margin-left: 12px;
        cursor: pointer;
    }
}
</style>
